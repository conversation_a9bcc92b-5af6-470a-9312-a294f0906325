{"ast": null, "code": "import _regenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _objectSpread from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.find.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { mapGetters } from 'vuex';\nimport ChatBox from '@/components/ChatBox.vue';\nimport DiceRoller from '@/components/DiceRoller.vue';\nimport CombatLog from '@/components/combat/CombatLog.vue';\nimport BattlefieldGrid from '@/components/combat/BattlefieldGrid.vue';\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue';\nimport KeeperCombatPanel from '@/components/combat/KeeperCombatPanel.vue';\nimport ForcedCombatMode from '@/components/combat/ForcedCombatMode.vue';\nimport { storageMixin } from '@/mixins/storageMixin';\nexport default {\n  name: 'GameRoomFixed',\n  mixins: [storageMixin],\n  components: {\n    ChatBox: ChatBox,\n    DiceRoller: DiceRoller,\n    CombatLog: CombatLog,\n    BattlefieldGrid: BattlefieldGrid,\n    InitiativeTracker: InitiativeTracker,\n    KeeperCombatPanel: KeeperCombatPanel,\n    ForcedCombatMode: ForcedCombatMode\n  },\n  props: {\n    roomId: {\n      type: String,\n      required: true\n    }\n  },\n  data: function data() {\n    return {\n      // 房间数据\n      roomData: {\n        name: '神秘的古宅',\n        status: 'active',\n        creator: {\n          username: 'KP_Master'\n        },\n        maxPlayers: 6\n      },\n      // 战斗状态\n      combatActive: false,\n      combatData: null,\n      combatLogs: [],\n      // 玩家数据\n      players: [{\n        id: 1,\n        username: 'KP_Master',\n        characterName: 'KP',\n        isKP: true,\n        status: 'online'\n      }, {\n        id: 2,\n        username: 'Player1',\n        characterName: '侦探约翰',\n        isKP: false,\n        status: 'online'\n      }, {\n        id: 3,\n        username: 'Player2',\n        characterName: '记者玛丽',\n        isKP: false,\n        status: 'online'\n      }],\n      // 聊天数据\n      messages: [{\n        id: 1,\n        username: 'KP_Master',\n        content: '欢迎来到游戏房间！',\n        timestamp: new Date()\n      }, {\n        id: 2,\n        username: 'Player1',\n        content: '准备好开始冒险了！',\n        timestamp: new Date()\n      }],\n      // 组件状态\n      showDiceRoller: false,\n      currentPlayerCharacter: null,\n      isFullscreen: false\n    };\n  },\n  computed: _objectSpread(_objectSpread({}, mapGetters(['currentUser'])), {}, {\n    isKeeper: function isKeeper() {\n      var _this$currentUser;\n      return ((_this$currentUser = this.currentUser) === null || _this$currentUser === void 0 ? void 0 : _this$currentUser.isKP) || false;\n    },\n    currentPlayers: function currentPlayers() {\n      return this.players.length;\n    }\n  }),\n  methods: {\n    getStatusText: function getStatusText() {\n      var statusMap = {\n        'active': '进行中',\n        'waiting': '等待中',\n        'paused': '已暂停',\n        'ended': '已结束'\n      };\n      return statusMap[this.roomData.status] || '未知';\n    },\n    getPlayerStatusText: function getPlayerStatusText(status) {\n      var statusMap = {\n        'online': '在线',\n        'away': '离开',\n        'offline': '离线'\n      };\n      return statusMap[status] || '未知';\n    },\n    startCombat: function startCombat() {\n      var _this = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              try {\n                _this.combatActive = true;\n                _this.combatData = {\n                  currentRound: 1,\n                  currentTurn: 0,\n                  initiativeOrder: _this.generateInitiativeOrder(),\n                  participants: _this.generateCombatParticipants()\n                };\n                _this.addCombatLog('system', '战斗开始！');\n\n                // 这里可以添加WebSocket通知其他玩家\n                // await this.notifyPlayersOfCombatStart()\n              } catch (error) {\n                console.error('开始战斗失败:', error);\n                _this.$store.dispatch('showNotification', {\n                  type: 'error',\n                  message: '开始战斗失败，请重试'\n                });\n              }\n            case 1:\n              return _context.a(2);\n          }\n        }, _callee);\n      }))();\n    },\n    endCombat: function endCombat() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              try {\n                _this2.combatActive = false;\n                _this2.addCombatLog('system', '战斗结束！');\n\n                // 重置战斗数据\n                _this2.combatData = null;\n\n                // 这里可以添加WebSocket通知其他玩家\n                // await this.notifyPlayersOfCombatEnd()\n              } catch (error) {\n                console.error('结束战斗失败:', error);\n                _this2.$store.dispatch('showNotification', {\n                  type: 'error',\n                  message: '结束战斗失败，请重试'\n                });\n              }\n            case 1:\n              return _context2.a(2);\n          }\n        }, _callee2);\n      }))();\n    },\n    generateInitiativeOrder: function generateInitiativeOrder() {\n      // 生成先攻顺序的示例数据\n      return [{\n        id: 1,\n        name: '侦探约翰',\n        initiative: 65,\n        isPlayer: true\n      }, {\n        id: 2,\n        name: '记者玛丽',\n        initiative: 45,\n        isPlayer: true\n      }, {\n        id: 3,\n        name: '邪教徒',\n        initiative: 40,\n        isPlayer: false\n      }].sort(function (a, b) {\n        return b.initiative - a.initiative;\n      });\n    },\n    generateCombatParticipants: function generateCombatParticipants() {\n      // 生成战斗参与者的示例数据\n      return [{\n        id: 1,\n        name: '侦探约翰',\n        isPlayer: true,\n        position: {\n          x: 2,\n          y: 2\n        },\n        hp: 12,\n        maxHp: 12,\n        mp: 10,\n        maxMp: 10\n      }, {\n        id: 2,\n        name: '记者玛丽',\n        isPlayer: true,\n        position: {\n          x: 3,\n          y: 2\n        },\n        hp: 10,\n        maxHp: 10,\n        mp: 12,\n        maxMp: 12\n      }, {\n        id: 3,\n        name: '邪教徒',\n        isPlayer: false,\n        position: {\n          x: 8,\n          y: 8\n        },\n        hp: 8,\n        maxHp: 8,\n        mp: 0,\n        maxMp: 0\n      }];\n    },\n    addCombatLog: function addCombatLog(type, message) {\n      var _this$combatData;\n      var details = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n      this.combatLogs.push({\n        id: Date.now() + Math.random(),\n        type: type,\n        message: message,\n        details: details,\n        timestamp: new Date(),\n        round: ((_this$combatData = this.combatData) === null || _this$combatData === void 0 ? void 0 : _this$combatData.currentRound) || 1\n      });\n    },\n    handleNextTurn: function handleNextTurn() {\n      if (this.combatData) {\n        var maxTurn = this.combatData.initiativeOrder.length - 1;\n        if (this.combatData.currentTurn >= maxTurn) {\n          this.combatData.currentTurn = 0;\n          this.combatData.currentRound++;\n          this.addCombatLog('system', \"\\u7B2C\".concat(this.combatData.currentRound, \"\\u8F6E\\u5F00\\u59CB\"));\n        } else {\n          this.combatData.currentTurn++;\n        }\n        var currentActor = this.combatData.initiativeOrder[this.combatData.currentTurn];\n        this.addCombatLog('turn', \"\\u8F6E\\u5230 \".concat(currentActor.name, \" \\u884C\\u52A8\"));\n      }\n    },\n    handlePreviousTurn: function handlePreviousTurn() {\n      if (this.combatData) {\n        if (this.combatData.currentTurn <= 0) {\n          if (this.combatData.currentRound > 1) {\n            this.combatData.currentRound--;\n            this.combatData.currentTurn = this.combatData.initiativeOrder.length - 1;\n          }\n        } else {\n          this.combatData.currentTurn--;\n        }\n      }\n    },\n    handleCharacterMove: function handleCharacterMove(characterId, newPosition) {\n      var _this$combatData2;\n      var participant = (_this$combatData2 = this.combatData) === null || _this$combatData2 === void 0 || (_this$combatData2 = _this$combatData2.participants) === null || _this$combatData2 === void 0 ? void 0 : _this$combatData2.find(function (p) {\n        return p.id === characterId;\n      });\n      if (participant) {\n        participant.position = newPosition;\n        this.addCombatLog('move', \"\".concat(participant.name, \" \\u79FB\\u52A8\\u5230 (\").concat(newPosition.x, \", \").concat(newPosition.y, \")\"));\n      }\n    },\n    handleMonsterMove: function handleMonsterMove(monsterId, newPosition) {\n      var _this$combatData3;\n      var monster = (_this$combatData3 = this.combatData) === null || _this$combatData3 === void 0 || (_this$combatData3 = _this$combatData3.participants) === null || _this$combatData3 === void 0 ? void 0 : _this$combatData3.find(function (p) {\n        return p.id === monsterId && !p.isPlayer;\n      });\n      if (monster) {\n        monster.position = newPosition;\n        this.addCombatLog('move', \"\".concat(monster.name, \" \\u79FB\\u52A8\\u5230 (\").concat(newPosition.x, \", \").concat(newPosition.y, \")\"));\n      }\n    },\n    handleAttack: function handleAttack(attackData) {\n      this.addCombatLog('attack', \"\".concat(attackData.attacker, \" \\u653B\\u51FB \").concat(attackData.target), attackData);\n    },\n    handlePlayerAction: function handlePlayerAction(action) {\n      this.addCombatLog('action', \"\\u73A9\\u5BB6\\u6267\\u884C: \".concat(action.type), action);\n    },\n    updateCombatData: function updateCombatData(newData) {\n      this.combatData = _objectSpread(_objectSpread({}, this.combatData), newData);\n    },\n    addMonster: function addMonster(monsterData) {\n      var _this$combatData4;\n      if ((_this$combatData4 = this.combatData) !== null && _this$combatData4 !== void 0 && _this$combatData4.participants) {\n        this.combatData.participants.push(monsterData);\n        this.addCombatLog('system', \"\".concat(monsterData.name, \" \\u52A0\\u5165\\u6218\\u6597\"));\n      }\n    },\n    updateMonster: function updateMonster(monsterId, updates) {\n      var _this$combatData5;\n      var monster = (_this$combatData5 = this.combatData) === null || _this$combatData5 === void 0 || (_this$combatData5 = _this$combatData5.participants) === null || _this$combatData5 === void 0 ? void 0 : _this$combatData5.find(function (p) {\n        return p.id === monsterId && !p.isPlayer;\n      });\n      if (monster) {\n        Object.assign(monster, updates);\n        this.addCombatLog('system', \"\".concat(monster.name, \" \\u72B6\\u6001\\u66F4\\u65B0\"));\n      }\n    },\n    sendMessage: function sendMessage(content) {\n      var _this$currentUser2;\n      var message = {\n        id: Date.now(),\n        username: ((_this$currentUser2 = this.currentUser) === null || _this$currentUser2 === void 0 ? void 0 : _this$currentUser2.username) || '匿名',\n        content: content,\n        timestamp: new Date()\n      };\n      this.messages.push(message);\n\n      // 这里可以添加WebSocket发送消息\n      // await this.sendMessageToRoom(message)\n    },\n    handleDiceResult: function handleDiceResult(result) {\n      var _this$currentUser3;\n      var message = \"\\uD83C\\uDFB2 \".concat(((_this$currentUser3 = this.currentUser) === null || _this$currentUser3 === void 0 ? void 0 : _this$currentUser3.username) || '匿名', \" \\u6295\\u63B7\\u4E86 \").concat(result.dice, \": \").concat(result.results.join(', '), \" (\\u603B\\u8BA1: \").concat(result.total, \")\");\n      this.sendMessage(message);\n    },\n    clearMessages: function clearMessages() {\n      if (confirm('确定要清空聊天记录吗？')) {\n        this.messages = [];\n      }\n    },\n    clearCombatLogs: function clearCombatLogs() {\n      if (confirm('确定要清空战斗日志吗？')) {\n        this.combatLogs = [];\n      }\n    },\n    loadTestMap: function loadTestMap() {\n      this.$store.dispatch('showNotification', {\n        type: 'info',\n        message: '测试地图加载功能暂未实现'\n      });\n    },\n    toggleFullscreen: function toggleFullscreen() {\n      this.isFullscreen = !this.isFullscreen;\n      if (this.isFullscreen) {\n        var _document$documentEle, _document$documentEle2;\n        (_document$documentEle = (_document$documentEle2 = document.documentElement).requestFullscreen) === null || _document$documentEle === void 0 || _document$documentEle.call(_document$documentEle2);\n      } else {\n        var _document$exitFullscr, _document;\n        (_document$exitFullscr = (_document = document).exitFullscreen) === null || _document$exitFullscr === void 0 || _document$exitFullscr.call(_document);\n      }\n    },\n    leaveRoom: function leaveRoom() {\n      if (confirm('确定要离开房间吗？')) {\n        this.$router.push('/');\n      }\n    },\n    loadRoomData: function loadRoomData() {\n      // 加载房间数据的方法\n      // 这里可以添加从API获取房间数据的逻辑\n      console.log('加载房间数据:', this.roomId);\n    }\n  },\n  mounted: function mounted() {\n    // 初始化房间数据\n    this.loadRoomData();\n\n    // 设置WebSocket连接\n    // this.setupWebSocket()\n  },\n  beforeUnmount: function beforeUnmount() {\n    // 清理WebSocket连接\n    // this.cleanupWebSocket()\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "ChatBox", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CombatLog", "BattlefieldGrid", "InitiativeTracker", "KeeperCombatPanel", "ForcedCombatMode", "storageMixin", "name", "mixins", "components", "props", "roomId", "type", "String", "required", "data", "roomData", "status", "creator", "username", "maxPlayers", "combatActive", "combatData", "combatLogs", "players", "id", "<PERSON><PERSON><PERSON>", "isKP", "messages", "content", "timestamp", "Date", "showDiceRoller", "currentPlayerCharacter", "isFullscreen", "computed", "_objectSpread", "<PERSON><PERSON><PERSON><PERSON>", "_this$currentUser", "currentUser", "currentPlayers", "length", "methods", "getStatusText", "statusMap", "getPlayerStatusText", "startCombat", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "currentRound", "currentTurn", "initiativeOrder", "generateInitiativeOrder", "participants", "generateCombatParticipants", "addCombatLog", "error", "console", "$store", "dispatch", "message", "a", "endCombat", "_this2", "_callee2", "_context2", "initiative", "isPlayer", "sort", "b", "position", "x", "y", "hp", "maxHp", "mp", "maxMp", "_this$combatData", "details", "arguments", "undefined", "push", "now", "Math", "random", "round", "handleNextTurn", "maxTurn", "concat", "current<PERSON><PERSON>", "handlePreviousTurn", "handleCharacterMove", "characterId", "newPosition", "_this$combatData2", "participant", "find", "p", "handleMonsterMove", "monsterId", "_this$combatData3", "monster", "handleAttack", "attackData", "attacker", "target", "handlePlayerAction", "action", "updateCombatData", "newData", "addMonster", "monsterData", "_this$combatData4", "updateMonster", "updates", "_this$combatData5", "Object", "assign", "sendMessage", "_this$currentUser2", "handleDiceResult", "result", "_this$currentUser3", "dice", "results", "join", "total", "clearMessages", "confirm", "clearCombatLogs", "loadTestMap", "toggleFullscreen", "_document$documentEle", "_document$documentEle2", "document", "documentElement", "requestFullscreen", "call", "_document$exitFullscr", "_document", "exitFullscreen", "leaveRoom", "$router", "loadRoomData", "log", "mounted", "beforeUnmount"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomFixed.vue"], "sourcesContent": ["<template>\r\n  <div class=\"game-room\" :class=\"{ 'combat-mode': combatActive }\">\r\n    <!-- 房间头部信息 -->\r\n    <div class=\"room-header\">\r\n      <div class=\"room-info\">\r\n        <div class=\"room-title\">\r\n          <i class=\"fas fa-door-open\"></i>\r\n          <h1>{{ roomData.name || '游戏房间' }}</h1>\r\n          <div class=\"room-status\" :class=\"roomData.status\">\r\n            <span class=\"status-dot\"></span>\r\n            <span>{{ getStatusText() }}</span>\r\n          </div>\r\n          <!-- 战斗状态指示器 -->\r\n          <div v-if=\"combatActive\" class=\"combat-indicator\">\r\n            <i class=\"fas fa-sword\"></i>\r\n            <span>战斗进行中</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"room-meta\">\r\n          <span class=\"room-creator\">\r\n            <i class=\"fas fa-crown\"></i>\r\n            KP: {{ roomData.creator?.username || '未知' }}\r\n          </span>\r\n          <span class=\"room-players\">\r\n            <i class=\"fas fa-users\"></i>\r\n            玩家: {{ currentPlayers }}/{{ roomData.maxPlayers || 6 }}\r\n          </span>\r\n          <span v-if=\"combatActive\" class=\"combat-round\">\r\n            <i class=\"fas fa-clock\"></i>\r\n            第{{ combatData?.currentRound || 1 }}轮\r\n          </span>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"room-controls\">\r\n        <!-- KP战斗控制按钮 -->\r\n        <button \r\n          v-if=\"isKeeper && !combatActive\" \r\n          @click=\"startCombat\" \r\n          class=\"control-btn combat-btn\"\r\n          title=\"开始战斗\"\r\n        >\r\n          <i class=\"fas fa-sword\"></i>\r\n        </button>\r\n        <button \r\n          v-if=\"isKeeper && combatActive\" \r\n          @click=\"endCombat\" \r\n          class=\"control-btn combat-btn active\"\r\n          title=\"结束战斗\"\r\n        >\r\n          <i class=\"fas fa-stop\"></i>\r\n        </button>\r\n        <button @click=\"toggleFullscreen\" class=\"control-btn\" title=\"全屏模式\">\r\n          <i class=\"fas fa-expand\"></i>\r\n        </button>\r\n        <button @click=\"leaveRoom\" class=\"control-btn leave-btn\" title=\"离开房间\">\r\n          <i class=\"fas fa-sign-out-alt\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 游戏主体布局 -->\r\n    <div class=\"game-layout\" :class=\"{ 'combat-layout': combatActive }\">\r\n      <!-- 左侧面板 -->\r\n      <div class=\"left-panel\">\r\n        <div class=\"panel-header\">\r\n          <h3>{{ combatActive ? '战斗状态' : '角色信息' }}</h3>\r\n        </div>\r\n        \r\n        <div class=\"panel-content\">\r\n          <!-- 非战斗时：角色列表 -->\r\n          <div v-if=\"!combatActive\" class=\"character-section\">\r\n            <div class=\"character-list\">\r\n              <div v-for=\"player in players\" :key=\"player.id\" class=\"character-card\">\r\n                <div class=\"character-avatar\">\r\n                  <i class=\"fas fa-user-circle\"></i>\r\n                </div>\r\n                <div class=\"character-info\">\r\n                  <div class=\"character-name\">{{ player.characterName || player.username }}</div>\r\n                  <div class=\"character-role\" :class=\"{ 'kp-role': player.isKP }\">\r\n                    {{ player.isKP ? 'KP' : 'PL' }}\r\n                  </div>\r\n                  <div class=\"character-status\">\r\n                    <span class=\"status-indicator\" :class=\"player.status || 'online'\"></span>\r\n                    {{ getPlayerStatusText(player.status) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 战斗时：先攻追踪器 -->\r\n          <div v-if=\"combatActive\" class=\"combat-section\">\r\n            <InitiativeTracker\r\n              :initiative-order=\"combatData?.initiativeOrder || []\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              :current-turn=\"combatData?.currentTurn || 0\"\r\n              @next-turn=\"handleNextTurn\"\r\n              @previous-turn=\"handlePreviousTurn\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 中央面板 -->\r\n      <div class=\"center-panel\">\r\n        <!-- 非战斗时：地图场景 -->\r\n        <div v-if=\"!combatActive\" class=\"scene-content\">\r\n          <div class=\"map-placeholder\">\r\n            <i class=\"fas fa-map\"></i>\r\n            <h3>地图系统</h3>\r\n            <p>地图和战斗场景将在这里显示</p>\r\n            <button @click=\"loadTestMap\" class=\"load-map-btn\">\r\n              <i class=\"fas fa-upload\"></i>\r\n              加载测试地图\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 战斗时：2D战场 -->\r\n        <div v-if=\"combatActive\" class=\"combat-scene\">\r\n          <BattlefieldGrid\r\n            :characters=\"combatData?.participants?.filter(p => p.isPlayer) || []\"\r\n            :monsters=\"combatData?.participants?.filter(p => !p.isPlayer) || []\"\r\n            :current-round=\"combatData?.currentRound || 1\"\r\n            :is-keeper=\"isKeeper\"\r\n            @character-move=\"handleCharacterMove\"\r\n            @monster-move=\"handleMonsterMove\"\r\n            @attack=\"handleAttack\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧面板 -->\r\n      <div class=\"right-panel\">\r\n        <div class=\"panel-header\">\r\n          <h3>{{ combatActive ? '战斗日志' : '聊天' }}</h3>\r\n          <div class=\"panel-controls\">\r\n            <button @click=\"clearMessages\" class=\"clear-btn\" title=\"清空\">\r\n              <i class=\"fas fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"panel-content\">\r\n          <!-- 非战斗时：聊天系统 -->\r\n          <div v-if=\"!combatActive\" class=\"chat-section\">\r\n            <ChatBox\r\n              :messages=\"messages\"\r\n              :current-user=\"currentUser\"\r\n              @send-message=\"sendMessage\"\r\n            />\r\n          </div>\r\n          \r\n          <!-- 战斗时：战斗日志 -->\r\n          <div v-if=\"combatActive\" class=\"combat-log-section\">\r\n            <CombatLog \r\n              :combat-logs=\"combatLogs\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              @clear-logs=\"clearCombatLogs\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 浮动组件 -->\r\n    <DiceRoller \r\n      v-if=\"showDiceRoller\" \r\n      @close=\"showDiceRoller = false\"\r\n      @roll-result=\"handleDiceResult\"\r\n    />\r\n    \r\n    <!-- 战斗模式组件 -->\r\n    <ForcedCombatMode\r\n      v-if=\"combatActive && !isKeeper\"\r\n      :combat-data=\"combatData\"\r\n      :character=\"currentPlayerCharacter\"\r\n      @action=\"handlePlayerAction\"\r\n    />\r\n    \r\n    <KeeperCombatPanel\r\n      v-if=\"combatActive && isKeeper\"\r\n      :combat-data=\"combatData\"\r\n      :players=\"players\"\r\n      @update-combat=\"updateCombatData\"\r\n      @add-monster=\"addMonster\"\r\n      @update-monster=\"updateMonster\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport ChatBox from '@/components/ChatBox.vue'\r\nimport DiceRoller from '@/components/DiceRoller.vue'\r\nimport CombatLog from '@/components/combat/CombatLog.vue'\r\nimport BattlefieldGrid from '@/components/combat/BattlefieldGrid.vue'\r\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue'\r\nimport KeeperCombatPanel from '@/components/combat/KeeperCombatPanel.vue'\r\nimport ForcedCombatMode from '@/components/combat/ForcedCombatMode.vue'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'GameRoomFixed',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    ChatBox,\r\n    DiceRoller,\r\n    CombatLog,\r\n    BattlefieldGrid,\r\n    InitiativeTracker,\r\n    KeeperCombatPanel,\r\n    ForcedCombatMode\r\n  },\r\n  props: {\r\n    roomId: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      // 房间数据\r\n      roomData: {\r\n        name: '神秘的古宅',\r\n        status: 'active',\r\n        creator: {\r\n          username: 'KP_Master'\r\n        },\r\n        maxPlayers: 6\r\n      },\r\n      \r\n      // 战斗状态\r\n      combatActive: false,\r\n      combatData: null,\r\n      combatLogs: [],\r\n      \r\n      // 玩家数据\r\n      players: [\r\n        {\r\n          id: 1,\r\n          username: 'KP_Master',\r\n          characterName: 'KP',\r\n          isKP: true,\r\n          status: 'online'\r\n        },\r\n        {\r\n          id: 2,\r\n          username: 'Player1',\r\n          characterName: '侦探约翰',\r\n          isKP: false,\r\n          status: 'online'\r\n        },\r\n        {\r\n          id: 3,\r\n          username: 'Player2',\r\n          characterName: '记者玛丽',\r\n          isKP: false,\r\n          status: 'online'\r\n        }\r\n      ],\r\n      \r\n      // 聊天数据\r\n      messages: [\r\n        {\r\n          id: 1,\r\n          username: 'KP_Master',\r\n          content: '欢迎来到游戏房间！',\r\n          timestamp: new Date()\r\n        },\r\n        {\r\n          id: 2,\r\n          username: 'Player1',\r\n          content: '准备好开始冒险了！',\r\n          timestamp: new Date()\r\n        }\r\n      ],\r\n      \r\n      // 组件状态\r\n      showDiceRoller: false,\r\n      currentPlayerCharacter: null,\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    ...mapGetters(['currentUser']),\r\n    \r\n    isKeeper() {\r\n      return this.currentUser?.isKP || false\r\n    },\r\n    \r\n    currentPlayers() {\r\n      return this.players.length\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    getStatusText() {\r\n      const statusMap = {\r\n        'active': '进行中',\r\n        'waiting': '等待中',\r\n        'paused': '已暂停',\r\n        'ended': '已结束'\r\n      }\r\n      return statusMap[this.roomData.status] || '未知'\r\n    },\r\n    \r\n    getPlayerStatusText(status) {\r\n      const statusMap = {\r\n        'online': '在线',\r\n        'away': '离开',\r\n        'offline': '离线'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n    \r\n    async startCombat() {\r\n      try {\r\n        this.combatActive = true\r\n        this.combatData = {\r\n          currentRound: 1,\r\n          currentTurn: 0,\r\n          initiativeOrder: this.generateInitiativeOrder(),\r\n          participants: this.generateCombatParticipants()\r\n        }\r\n        \r\n        this.addCombatLog('system', '战斗开始！')\r\n        \r\n        // 这里可以添加WebSocket通知其他玩家\r\n        // await this.notifyPlayersOfCombatStart()\r\n        \r\n      } catch (error) {\r\n        console.error('开始战斗失败:', error)\r\n        this.$store.dispatch('showNotification', {\r\n          type: 'error',\r\n          message: '开始战斗失败，请重试'\r\n        })\r\n      }\r\n    },\r\n    \r\n    async endCombat() {\r\n      try {\r\n        this.combatActive = false\r\n        this.addCombatLog('system', '战斗结束！')\r\n        \r\n        // 重置战斗数据\r\n        this.combatData = null\r\n        \r\n        // 这里可以添加WebSocket通知其他玩家\r\n        // await this.notifyPlayersOfCombatEnd()\r\n        \r\n      } catch (error) {\r\n        console.error('结束战斗失败:', error)\r\n        this.$store.dispatch('showNotification', {\r\n          type: 'error',\r\n          message: '结束战斗失败，请重试'\r\n        })\r\n      }\r\n    },\r\n    \r\n    generateInitiativeOrder() {\r\n      // 生成先攻顺序的示例数据\r\n      return [\r\n        { id: 1, name: '侦探约翰', initiative: 65, isPlayer: true },\r\n        { id: 2, name: '记者玛丽', initiative: 45, isPlayer: true },\r\n        { id: 3, name: '邪教徒', initiative: 40, isPlayer: false }\r\n      ].sort((a, b) => b.initiative - a.initiative)\r\n    },\r\n    \r\n    generateCombatParticipants() {\r\n      // 生成战斗参与者的示例数据\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '侦探约翰',\r\n          isPlayer: true,\r\n          position: { x: 2, y: 2 },\r\n          hp: 12,\r\n          maxHp: 12,\r\n          mp: 10,\r\n          maxMp: 10\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '记者玛丽',\r\n          isPlayer: true,\r\n          position: { x: 3, y: 2 },\r\n          hp: 10,\r\n          maxHp: 10,\r\n          mp: 12,\r\n          maxMp: 12\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '邪教徒',\r\n          isPlayer: false,\r\n          position: { x: 8, y: 8 },\r\n          hp: 8,\r\n          maxHp: 8,\r\n          mp: 0,\r\n          maxMp: 0\r\n        }\r\n      ]\r\n    },\r\n    \r\n    addCombatLog(type, message, details = null) {\r\n      this.combatLogs.push({\r\n        id: Date.now() + Math.random(),\r\n        type,\r\n        message,\r\n        details,\r\n        timestamp: new Date(),\r\n        round: this.combatData?.currentRound || 1\r\n      })\r\n    },\r\n    \r\n    handleNextTurn() {\r\n      if (this.combatData) {\r\n        const maxTurn = this.combatData.initiativeOrder.length - 1\r\n        if (this.combatData.currentTurn >= maxTurn) {\r\n          this.combatData.currentTurn = 0\r\n          this.combatData.currentRound++\r\n          this.addCombatLog('system', `第${this.combatData.currentRound}轮开始`)\r\n        } else {\r\n          this.combatData.currentTurn++\r\n        }\r\n        \r\n        const currentActor = this.combatData.initiativeOrder[this.combatData.currentTurn]\r\n        this.addCombatLog('turn', `轮到 ${currentActor.name} 行动`)\r\n      }\r\n    },\r\n    \r\n    handlePreviousTurn() {\r\n      if (this.combatData) {\r\n        if (this.combatData.currentTurn <= 0) {\r\n          if (this.combatData.currentRound > 1) {\r\n            this.combatData.currentRound--\r\n            this.combatData.currentTurn = this.combatData.initiativeOrder.length - 1\r\n          }\r\n        } else {\r\n          this.combatData.currentTurn--\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleCharacterMove(characterId, newPosition) {\r\n      const participant = this.combatData?.participants?.find(p => p.id === characterId)\r\n      if (participant) {\r\n        participant.position = newPosition\r\n        this.addCombatLog('move', `${participant.name} 移动到 (${newPosition.x}, ${newPosition.y})`)\r\n      }\r\n    },\r\n    \r\n    handleMonsterMove(monsterId, newPosition) {\r\n      const monster = this.combatData?.participants?.find(p => p.id === monsterId && !p.isPlayer)\r\n      if (monster) {\r\n        monster.position = newPosition\r\n        this.addCombatLog('move', `${monster.name} 移动到 (${newPosition.x}, ${newPosition.y})`)\r\n      }\r\n    },\r\n    \r\n    handleAttack(attackData) {\r\n      this.addCombatLog('attack', `${attackData.attacker} 攻击 ${attackData.target}`, attackData)\r\n    },\r\n    \r\n    handlePlayerAction(action) {\r\n      this.addCombatLog('action', `玩家执行: ${action.type}`, action)\r\n    },\r\n    \r\n    updateCombatData(newData) {\r\n      this.combatData = { ...this.combatData, ...newData }\r\n    },\r\n    \r\n    addMonster(monsterData) {\r\n      if (this.combatData?.participants) {\r\n        this.combatData.participants.push(monsterData)\r\n        this.addCombatLog('system', `${monsterData.name} 加入战斗`)\r\n      }\r\n    },\r\n    \r\n    updateMonster(monsterId, updates) {\r\n      const monster = this.combatData?.participants?.find(p => p.id === monsterId && !p.isPlayer)\r\n      if (monster) {\r\n        Object.assign(monster, updates)\r\n        this.addCombatLog('system', `${monster.name} 状态更新`)\r\n      }\r\n    },\r\n    \r\n    sendMessage(content) {\r\n      const message = {\r\n        id: Date.now(),\r\n        username: this.currentUser?.username || '匿名',\r\n        content,\r\n        timestamp: new Date()\r\n      }\r\n      this.messages.push(message)\r\n      \r\n      // 这里可以添加WebSocket发送消息\r\n      // await this.sendMessageToRoom(message)\r\n    },\r\n    \r\n    handleDiceResult(result) {\r\n      const message = `🎲 ${this.currentUser?.username || '匿名'} 投掷了 ${result.dice}: ${result.results.join(', ')} (总计: ${result.total})`\r\n      this.sendMessage(message)\r\n    },\r\n    \r\n    clearMessages() {\r\n      if (confirm('确定要清空聊天记录吗？')) {\r\n        this.messages = []\r\n      }\r\n    },\r\n    \r\n    clearCombatLogs() {\r\n      if (confirm('确定要清空战斗日志吗？')) {\r\n        this.combatLogs = []\r\n      }\r\n    },\r\n    \r\n    loadTestMap() {\r\n      this.$store.dispatch('showNotification', {\r\n        type: 'info',\r\n        message: '测试地图加载功能暂未实现'\r\n      })\r\n    },\r\n    \r\n    toggleFullscreen() {\r\n      this.isFullscreen = !this.isFullscreen\r\n      if (this.isFullscreen) {\r\n        document.documentElement.requestFullscreen?.()\r\n      } else {\r\n        document.exitFullscreen?.()\r\n      }\r\n    },\r\n    \r\n    leaveRoom() {\r\n      if (confirm('确定要离开房间吗？')) {\r\n        this.$router.push('/')\r\n      }\r\n    },\r\n\r\n    loadRoomData() {\r\n      // 加载房间数据的方法\r\n      // 这里可以添加从API获取房间数据的逻辑\r\n      console.log('加载房间数据:', this.roomId)\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 初始化房间数据\r\n    this.loadRoomData()\r\n    \r\n    // 设置WebSocket连接\r\n    // this.setupWebSocket()\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    // 清理WebSocket连接\r\n    // this.cleanupWebSocket()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 游戏房间基础样式 */\r\n.game-room {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode {\r\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\r\n}\r\n\r\n/* ===== 房间头部 ===== */\r\n.room-header {\r\n  background: linear-gradient(135deg, #22c55e 0%, #16a085 100%);\r\n  color: white;\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode .room-header {\r\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\r\n}\r\n\r\n.room-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.room-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.room-title i {\r\n  font-size: 24px;\r\n}\r\n\r\n.room-title h1 {\r\n  margin: 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.room-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.status-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background: #10b981;\r\n}\r\n\r\n.room-status.waiting .status-dot {\r\n  background: #f59e0b;\r\n}\r\n\r\n.room-status.paused .status-dot {\r\n  background: #6b7280;\r\n}\r\n\r\n.combat-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  animation: combat-pulse 2s infinite;\r\n}\r\n\r\n@keyframes combat-pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  gap: 16px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.room-meta span {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.room-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.control-btn {\r\n  padding: 10px 16px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  /* 布局属性 */\r\n  display: flex;\r\n  align-items: center;\r\n  /* gap: 6px; */\r\n  font-size: 14px;\r\n}\r\n\r\n.control-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.combat-btn.active {\r\n  background: rgba(239, 68, 68, 0.8);\r\n  animation: combat-btn-pulse 1.5s infinite;\r\n}\r\n\r\n@keyframes combat-btn-pulse {\r\n  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }\r\n  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0); }\r\n}\r\n\r\n.leave-btn:hover {\r\n  background: rgba(239, 68, 68, 0.8);\r\n}\r\n\r\n/* ===== 游戏布局 ===== */\r\n.game-layout {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-layout.combat-layout {\r\n  background: rgba(17, 24, 39, 0.1);\r\n}\r\n\r\n/* 左右面板基础样式 */\r\n.left-panel {\r\n  width: 320px;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.right-panel {\r\n  width: 320px;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode .left-panel,\r\n.game-room.combat-mode .right-panel {\r\n  background: rgba(31, 41, 55, 0.95);\r\n  border-color: #4b5563;\r\n  color: #f3f4f6;\r\n}\r\n\r\n.center-panel {\r\n  flex: 1;\r\n  background: white;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode .center-panel {\r\n  background: rgba(17, 24, 39, 0.9);\r\n}\r\n\r\n.panel-header {\r\n  background: #22c55e;\r\n  color: white;\r\n  padding: 16px 20px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.game-room.combat-mode .panel-header {\r\n  background: #dc2626;\r\n}\r\n\r\n.panel-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.clear-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: none;\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clear-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.panel-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* ===== 角色列表 ===== */\r\n.character-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.character-card {\r\n  background: #f8fafc;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  border: 1px solid #e2e8f0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.character-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.game-room.combat-mode .character-card {\r\n  background: rgba(55, 65, 81, 0.8);\r\n  border-color: #6b7280;\r\n}\r\n\r\n.character-avatar {\r\n  font-size: 32px;\r\n  color: #6b7280;\r\n}\r\n\r\n.character-info {\r\n  flex: 1;\r\n}\r\n\r\n.character-name {\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.character-role {\r\n  font-size: 12px;\r\n  color: #6b7280;\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  background: #e5e7eb;\r\n  display: inline-block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.character-role.kp-role {\r\n  background: #fef3c7;\r\n  color: #92400e;\r\n}\r\n\r\n.character-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 12px;\r\n}\r\n\r\n.status-indicator {\r\n  width: 6px;\r\n  height: 6px;\r\n  border-radius: 50%;\r\n  background: #10b981;\r\n}\r\n\r\n.status-indicator.away {\r\n  background: #f59e0b;\r\n}\r\n\r\n.status-indicator.offline {\r\n  background: #6b7280;\r\n}\r\n\r\n/* ===== 地图占位符 ===== */\r\n.map-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  color: #6b7280;\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n.map-placeholder i {\r\n  font-size: 64px;\r\n  margin-bottom: 20px;\r\n  opacity: 0.5;\r\n}\r\n\r\n.map-placeholder h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 24px;\r\n}\r\n\r\n.map-placeholder p {\r\n  margin: 0 0 24px 0;\r\n  font-size: 16px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.load-map-btn {\r\n  background: #22c55e;\r\n  color: white;\r\n  border: none;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.load-map-btn:hover {\r\n  background: #16a085;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* ===== 战斗场景 ===== */\r\n.combat-scene {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: #1f2937;\r\n  border-radius: 8px;\r\n  margin: 8px;\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n@media (max-width: 1200px) {\r\n  .left-panel,\r\n  .right-panel {\r\n    width: 280px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .game-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .left-panel,\r\n  .right-panel {\r\n    width: 100%;\r\n    height: 200px;\r\n  }\r\n  \r\n  .room-header {\r\n    padding: 12px 16px;\r\n  }\r\n  \r\n  .room-title h1 {\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .room-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .room-controls {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* ===== 动画效果 ===== */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.slide-enter-active,\r\n.slide-leave-active {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.slide-enter-from {\r\n  transform: translateX(-100%);\r\n}\r\n\r\n.slide-leave-to {\r\n  transform: translateX(100%);\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;AAiMA,SAASA,UAAS,QAAS,MAAK;AAChC,OAAOC,OAAM,MAAO,0BAAyB;AAC7C,OAAOC,UAAS,MAAO,6BAA4B;AACnD,OAAOC,SAAQ,MAAO,mCAAkC;AACxD,OAAOC,eAAc,MAAO,yCAAwC;AACpE,OAAOC,iBAAgB,MAAO,2CAA0C;AACxE,OAAOC,iBAAgB,MAAO,2CAA0C;AACxE,OAAOC,gBAAe,MAAO,0CAAyC;AACtE,SAASC,YAAW,QAAS,uBAAsB;AAEnD,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,MAAM,EAAE,CAACF,YAAY,CAAC;EACtBG,UAAU,EAAE;IACVV,OAAO,EAAPA,OAAO;IACPC,UAAU,EAAVA,UAAU;IACVC,SAAS,EAATA,SAAS;IACTC,eAAe,EAAfA,eAAe;IACfC,iBAAiB,EAAjBA,iBAAiB;IACjBC,iBAAiB,EAAjBA,iBAAiB;IACjBC,gBAAe,EAAfA;EACF,CAAC;EACDK,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC;EAEDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,QAAQ,EAAE;QACRT,IAAI,EAAE,OAAO;QACbU,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE;UACPC,QAAQ,EAAE;QACZ,CAAC;QACDC,UAAU,EAAE;MACd,CAAC;MAED;MACAC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,EAAE;MAEd;MACAC,OAAO,EAAE,CACP;QACEC,EAAE,EAAE,CAAC;QACLN,QAAQ,EAAE,WAAW;QACrBO,aAAa,EAAE,IAAI;QACnBC,IAAI,EAAE,IAAI;QACVV,MAAM,EAAE;MACV,CAAC,EACD;QACEQ,EAAE,EAAE,CAAC;QACLN,QAAQ,EAAE,SAAS;QACnBO,aAAa,EAAE,MAAM;QACrBC,IAAI,EAAE,KAAK;QACXV,MAAM,EAAE;MACV,CAAC,EACD;QACEQ,EAAE,EAAE,CAAC;QACLN,QAAQ,EAAE,SAAS;QACnBO,aAAa,EAAE,MAAM;QACrBC,IAAI,EAAE,KAAK;QACXV,MAAM,EAAE;MACV,EACD;MAED;MACAW,QAAQ,EAAE,CACR;QACEH,EAAE,EAAE,CAAC;QACLN,QAAQ,EAAE,WAAW;QACrBU,OAAO,EAAE,WAAW;QACpBC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,CAAC,EACD;QACEN,EAAE,EAAE,CAAC;QACLN,QAAQ,EAAE,SAAS;QACnBU,OAAO,EAAE,WAAW;QACpBC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB,EACD;MAED;MACAC,cAAc,EAAE,KAAK;MACrBC,sBAAsB,EAAE,IAAI;MAC5BC,YAAY,EAAE;IAChB;EACF,CAAC;EAEDC,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHtC,UAAU,CAAC,CAAC,aAAa,CAAC,CAAC;IAE9BuC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MAAA,IAAAC,iBAAA;MACT,OAAO,EAAAA,iBAAA,OAAI,CAACC,WAAW,cAAAD,iBAAA,uBAAhBA,iBAAA,CAAkBX,IAAG,KAAK,KAAI;IACvC,CAAC;IAEDa,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,OAAO,IAAI,CAAChB,OAAO,CAACiB,MAAK;IAC3B;EAAA,EACD;EAEDC,OAAO,EAAE;IACPC,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAMC,SAAQ,GAAI;QAChB,QAAQ,EAAE,KAAK;QACf,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE;MACX;MACA,OAAOA,SAAS,CAAC,IAAI,CAAC5B,QAAQ,CAACC,MAAM,KAAK,IAAG;IAC/C,CAAC;IAED4B,mBAAmB,WAAnBA,mBAAmBA,CAAC5B,MAAM,EAAE;MAC1B,IAAM2B,SAAQ,GAAI;QAChB,QAAQ,EAAE,IAAI;QACd,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE;MACb;MACA,OAAOA,SAAS,CAAC3B,MAAM,KAAK,IAAG;IACjC,CAAC;IAEK6B,WAAW,WAAXA,WAAWA,CAAA,EAAG;MAAA,IAAAC,KAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAClB,IAAI;gBACFP,KAAI,CAAC1B,YAAW,GAAI,IAAG;gBACvB0B,KAAI,CAACzB,UAAS,GAAI;kBAChBiC,YAAY,EAAE,CAAC;kBACfC,WAAW,EAAE,CAAC;kBACdC,eAAe,EAAEV,KAAI,CAACW,uBAAuB,CAAC,CAAC;kBAC/CC,YAAY,EAAEZ,KAAI,CAACa,0BAA0B,CAAC;gBAChD;gBAEAb,KAAI,CAACc,YAAY,CAAC,QAAQ,EAAE,OAAO;;gBAEnC;gBACA;cAEF,EAAE,OAAOC,KAAK,EAAE;gBACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;gBAC9Bf,KAAI,CAACiB,MAAM,CAACC,QAAQ,CAAC,kBAAkB,EAAE;kBACvCrD,IAAI,EAAE,OAAO;kBACbsD,OAAO,EAAE;gBACX,CAAC;cACH;YAAA;cAAA,OAAAb,QAAA,CAAAc,CAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IACF,CAAC;IAEKiB,SAAS,WAATA,SAASA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAArB,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAoB,SAAA;QAAA,OAAArB,YAAA,GAAAG,CAAA,WAAAmB,SAAA;UAAA,kBAAAA,SAAA,CAAAjB,CAAA;YAAA;cAChB,IAAI;gBACFe,MAAI,CAAChD,YAAW,GAAI,KAAI;gBACxBgD,MAAI,CAACR,YAAY,CAAC,QAAQ,EAAE,OAAO;;gBAEnC;gBACAQ,MAAI,CAAC/C,UAAS,GAAI,IAAG;;gBAErB;gBACA;cAEF,EAAE,OAAOwC,KAAK,EAAE;gBACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;gBAC9BO,MAAI,CAACL,MAAM,CAACC,QAAQ,CAAC,kBAAkB,EAAE;kBACvCrD,IAAI,EAAE,OAAO;kBACbsD,OAAO,EAAE;gBACX,CAAC;cACH;YAAA;cAAA,OAAAK,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACF,CAAC;IAEDZ,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;MACxB;MACA,OAAO,CACL;QAAEjC,EAAE,EAAE,CAAC;QAAElB,IAAI,EAAE,MAAM;QAAEiE,UAAU,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAK,CAAC,EACvD;QAAEhD,EAAE,EAAE,CAAC;QAAElB,IAAI,EAAE,MAAM;QAAEiE,UAAU,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAK,CAAC,EACvD;QAAEhD,EAAE,EAAE,CAAC;QAAElB,IAAI,EAAE,KAAK;QAAEiE,UAAU,EAAE,EAAE;QAAEC,QAAQ,EAAE;MAAM,EACvD,CAACC,IAAI,CAAC,UAACP,CAAC,EAAEQ,CAAC;QAAA,OAAKA,CAAC,CAACH,UAAS,GAAIL,CAAC,CAACK,UAAU;MAAA;IAC9C,CAAC;IAEDZ,0BAA0B,WAA1BA,0BAA0BA,CAAA,EAAG;MAC3B;MACA,OAAO,CACL;QACEnC,EAAE,EAAE,CAAC;QACLlB,IAAI,EAAE,MAAM;QACZkE,QAAQ,EAAE,IAAI;QACdG,QAAQ,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAC;QACxBC,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE,EAAE;QACTC,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE;MACT,CAAC,EACD;QACEzD,EAAE,EAAE,CAAC;QACLlB,IAAI,EAAE,MAAM;QACZkE,QAAQ,EAAE,IAAI;QACdG,QAAQ,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAC;QACxBC,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE,EAAE;QACTC,EAAE,EAAE,EAAE;QACNC,KAAK,EAAE;MACT,CAAC,EACD;QACEzD,EAAE,EAAE,CAAC;QACLlB,IAAI,EAAE,KAAK;QACXkE,QAAQ,EAAE,KAAK;QACfG,QAAQ,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAC;QACxBC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE,CAAC;QACRC,EAAE,EAAE,CAAC;QACLC,KAAK,EAAE;MACT,EACF;IACF,CAAC;IAEDrB,YAAY,WAAZA,YAAYA,CAACjD,IAAI,EAAEsD,OAAO,EAAkB;MAAA,IAAAiB,gBAAA;MAAA,IAAhBC,OAAM,GAAAC,SAAA,CAAA5C,MAAA,QAAA4C,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAI,IAAI;MACxC,IAAI,CAAC9D,UAAU,CAACgE,IAAI,CAAC;QACnB9D,EAAE,EAAEM,IAAI,CAACyD,GAAG,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC;QAC9B9E,IAAI,EAAJA,IAAI;QACJsD,OAAO,EAAPA,OAAO;QACPkB,OAAO,EAAPA,OAAO;QACPtD,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrB4D,KAAK,EAAE,EAAAR,gBAAA,OAAI,CAAC7D,UAAU,cAAA6D,gBAAA,uBAAfA,gBAAA,CAAiB5B,YAAW,KAAK;MAC1C,CAAC;IACH,CAAC;IAEDqC,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAI,IAAI,CAACtE,UAAU,EAAE;QACnB,IAAMuE,OAAM,GAAI,IAAI,CAACvE,UAAU,CAACmC,eAAe,CAAChB,MAAK,GAAI;QACzD,IAAI,IAAI,CAACnB,UAAU,CAACkC,WAAU,IAAKqC,OAAO,EAAE;UAC1C,IAAI,CAACvE,UAAU,CAACkC,WAAU,GAAI;UAC9B,IAAI,CAAClC,UAAU,CAACiC,YAAY,EAAC;UAC7B,IAAI,CAACM,YAAY,CAAC,QAAQ,WAAAiC,MAAA,CAAM,IAAI,CAACxE,UAAU,CAACiC,YAAY,uBAAK;QACnE,OAAO;UACL,IAAI,CAACjC,UAAU,CAACkC,WAAW,EAAC;QAC9B;QAEA,IAAMuC,YAAW,GAAI,IAAI,CAACzE,UAAU,CAACmC,eAAe,CAAC,IAAI,CAACnC,UAAU,CAACkC,WAAW;QAChF,IAAI,CAACK,YAAY,CAAC,MAAM,kBAAAiC,MAAA,CAAQC,YAAY,CAACxF,IAAI,kBAAK;MACxD;IACF,CAAC;IAEDyF,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAAC1E,UAAU,EAAE;QACnB,IAAI,IAAI,CAACA,UAAU,CAACkC,WAAU,IAAK,CAAC,EAAE;UACpC,IAAI,IAAI,CAAClC,UAAU,CAACiC,YAAW,GAAI,CAAC,EAAE;YACpC,IAAI,CAACjC,UAAU,CAACiC,YAAY,EAAC;YAC7B,IAAI,CAACjC,UAAU,CAACkC,WAAU,GAAI,IAAI,CAAClC,UAAU,CAACmC,eAAe,CAAChB,MAAK,GAAI;UACzE;QACF,OAAO;UACL,IAAI,CAACnB,UAAU,CAACkC,WAAW,EAAC;QAC9B;MACF;IACF,CAAC;IAEDyC,mBAAmB,WAAnBA,mBAAmBA,CAACC,WAAW,EAAEC,WAAW,EAAE;MAAA,IAAAC,iBAAA;MAC5C,IAAMC,WAAU,IAAAD,iBAAA,GAAI,IAAI,CAAC9E,UAAU,cAAA8E,iBAAA,gBAAAA,iBAAA,GAAfA,iBAAA,CAAiBzC,YAAY,cAAAyC,iBAAA,uBAA7BA,iBAAA,CAA+BE,IAAI,CAAC,UAAAC,CAAA;QAAA,OAAKA,CAAC,CAAC9E,EAAC,KAAMyE,WAAW;MAAA;MACjF,IAAIG,WAAW,EAAE;QACfA,WAAW,CAACzB,QAAO,GAAIuB,WAAU;QACjC,IAAI,CAACtC,YAAY,CAAC,MAAM,KAAAiC,MAAA,CAAKO,WAAW,CAAC9F,IAAI,2BAAAuF,MAAA,CAASK,WAAW,CAACtB,CAAC,QAAAiB,MAAA,CAAKK,WAAW,CAACrB,CAAC,MAAG;MAC1F;IACF,CAAC;IAED0B,iBAAiB,WAAjBA,iBAAiBA,CAACC,SAAS,EAAEN,WAAW,EAAE;MAAA,IAAAO,iBAAA;MACxC,IAAMC,OAAM,IAAAD,iBAAA,GAAI,IAAI,CAACpF,UAAU,cAAAoF,iBAAA,gBAAAA,iBAAA,GAAfA,iBAAA,CAAiB/C,YAAY,cAAA+C,iBAAA,uBAA7BA,iBAAA,CAA+BJ,IAAI,CAAC,UAAAC,CAAA;QAAA,OAAKA,CAAC,CAAC9E,EAAC,KAAMgF,SAAQ,IAAK,CAACF,CAAC,CAAC9B,QAAQ;MAAA;MAC1F,IAAIkC,OAAO,EAAE;QACXA,OAAO,CAAC/B,QAAO,GAAIuB,WAAU;QAC7B,IAAI,CAACtC,YAAY,CAAC,MAAM,KAAAiC,MAAA,CAAKa,OAAO,CAACpG,IAAI,2BAAAuF,MAAA,CAASK,WAAW,CAACtB,CAAC,QAAAiB,MAAA,CAAKK,WAAW,CAACrB,CAAC,MAAG;MACtF;IACF,CAAC;IAED8B,YAAY,WAAZA,YAAYA,CAACC,UAAU,EAAE;MACvB,IAAI,CAAChD,YAAY,CAAC,QAAQ,KAAAiC,MAAA,CAAKe,UAAU,CAACC,QAAQ,oBAAAhB,MAAA,CAAOe,UAAU,CAACE,MAAM,GAAIF,UAAU;IAC1F,CAAC;IAEDG,kBAAkB,WAAlBA,kBAAkBA,CAACC,MAAM,EAAE;MACzB,IAAI,CAACpD,YAAY,CAAC,QAAQ,+BAAAiC,MAAA,CAAWmB,MAAM,CAACrG,IAAI,GAAIqG,MAAM;IAC5D,CAAC;IAEDC,gBAAgB,WAAhBA,gBAAgBA,CAACC,OAAO,EAAE;MACxB,IAAI,CAAC7F,UAAS,GAAAc,aAAA,CAAAA,aAAA,KAAS,IAAI,CAACd,UAAU,GAAK6F,OAAM,CAAE;IACrD,CAAC;IAEDC,UAAU,WAAVA,UAAUA,CAACC,WAAW,EAAE;MAAA,IAAAC,iBAAA;MACtB,KAAAA,iBAAA,GAAI,IAAI,CAAChG,UAAU,cAAAgG,iBAAA,eAAfA,iBAAA,CAAiB3D,YAAY,EAAE;QACjC,IAAI,CAACrC,UAAU,CAACqC,YAAY,CAAC4B,IAAI,CAAC8B,WAAW;QAC7C,IAAI,CAACxD,YAAY,CAAC,QAAQ,KAAAiC,MAAA,CAAKuB,WAAW,CAAC9G,IAAI,8BAAO;MACxD;IACF,CAAC;IAEDgH,aAAa,WAAbA,aAAaA,CAACd,SAAS,EAAEe,OAAO,EAAE;MAAA,IAAAC,iBAAA;MAChC,IAAMd,OAAM,IAAAc,iBAAA,GAAI,IAAI,CAACnG,UAAU,cAAAmG,iBAAA,gBAAAA,iBAAA,GAAfA,iBAAA,CAAiB9D,YAAY,cAAA8D,iBAAA,uBAA7BA,iBAAA,CAA+BnB,IAAI,CAAC,UAAAC,CAAA;QAAA,OAAKA,CAAC,CAAC9E,EAAC,KAAMgF,SAAQ,IAAK,CAACF,CAAC,CAAC9B,QAAQ;MAAA;MAC1F,IAAIkC,OAAO,EAAE;QACXe,MAAM,CAACC,MAAM,CAAChB,OAAO,EAAEa,OAAO;QAC9B,IAAI,CAAC3D,YAAY,CAAC,QAAQ,KAAAiC,MAAA,CAAKa,OAAO,CAACpG,IAAI,8BAAO;MACpD;IACF,CAAC;IAEDqH,WAAW,WAAXA,WAAWA,CAAC/F,OAAO,EAAE;MAAA,IAAAgG,kBAAA;MACnB,IAAM3D,OAAM,GAAI;QACdzC,EAAE,EAAEM,IAAI,CAACyD,GAAG,CAAC,CAAC;QACdrE,QAAQ,EAAE,EAAA0G,kBAAA,OAAI,CAACtF,WAAW,cAAAsF,kBAAA,uBAAhBA,kBAAA,CAAkB1G,QAAO,KAAK,IAAI;QAC5CU,OAAO,EAAPA,OAAO;QACPC,SAAS,EAAE,IAAIC,IAAI,CAAC;MACtB;MACA,IAAI,CAACH,QAAQ,CAAC2D,IAAI,CAACrB,OAAO;;MAE1B;MACA;IACF,CAAC;IAED4D,gBAAgB,WAAhBA,gBAAgBA,CAACC,MAAM,EAAE;MAAA,IAAAC,kBAAA;MACvB,IAAM9D,OAAM,mBAAA4B,MAAA,CAAU,EAAAkC,kBAAA,OAAI,CAACzF,WAAW,cAAAyF,kBAAA,uBAAhBA,kBAAA,CAAkB7G,QAAO,KAAK,IAAI,0BAAA2E,MAAA,CAAQiC,MAAM,CAACE,IAAI,QAAAnC,MAAA,CAAKiC,MAAM,CAACG,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,sBAAArC,MAAA,CAASiC,MAAM,CAACK,KAAK,MAAE;MAChI,IAAI,CAACR,WAAW,CAAC1D,OAAO;IAC1B,CAAC;IAEDmE,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAIC,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI,CAAC1G,QAAO,GAAI,EAAC;MACnB;IACF,CAAC;IAED2G,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAID,OAAO,CAAC,aAAa,CAAC,EAAE;QAC1B,IAAI,CAAC/G,UAAS,GAAI,EAAC;MACrB;IACF,CAAC;IAEDiH,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACxE,MAAM,CAACC,QAAQ,CAAC,kBAAkB,EAAE;QACvCrD,IAAI,EAAE,MAAM;QACZsD,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IAEDuE,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACvG,YAAW,GAAI,CAAC,IAAI,CAACA,YAAW;MACrC,IAAI,IAAI,CAACA,YAAY,EAAE;QAAA,IAAAwG,qBAAA,EAAAC,sBAAA;QACrB,CAAAD,qBAAA,IAAAC,sBAAA,GAAAC,QAAQ,CAACC,eAAe,EAACC,iBAAiB,cAAAJ,qBAAA,eAA1CA,qBAAA,CAAAK,IAAA,CAAAJ,sBAA6C;MAC/C,OAAO;QAAA,IAAAK,qBAAA,EAAAC,SAAA;QACL,CAAAD,qBAAA,IAAAC,SAAA,GAAAL,QAAQ,EAACM,cAAc,cAAAF,qBAAA,eAAvBA,qBAAA,CAAAD,IAAA,CAAAE,SAA0B;MAC5B;IACF,CAAC;IAEDE,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAIb,OAAO,CAAC,WAAW,CAAC,EAAE;QACxB,IAAI,CAACc,OAAO,CAAC7D,IAAI,CAAC,GAAG;MACvB;IACF,CAAC;IAED8D,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb;MACA;MACAtF,OAAO,CAACuF,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC3I,MAAM;IACpC;EACF,CAAC;EAED4I,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAACF,YAAY,CAAC;;IAElB;IACA;EACF,CAAC;EAEDG,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd;IACA;EAAA;AAEJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}