{"ast": null, "code": "import _regenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _objectSpread from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { mapGetters } from 'vuex';\nimport { storageMixin } from '@/mixins/storageMixin';\nexport default {\n  name: 'Home',\n  mixins: [storageMixin],\n  data: function data() {\n    return {\n      roomFilter: 'all',\n      viewMode: 'grid',\n      loading: false,\n      // 副标题列表\n      subtitles: ['准备开始新的冒险了吗？', '黑暗中有什么在等待着你...', '真相往往比想象更加恐怖', '在未知的世界中寻找答案', '每一次投骰都可能改变命运', '克苏鲁的呼唤正在响起...']\n    };\n  },\n  computed: _objectSpread(_objectSpread({}, mapGetters('auth', ['currentUser', 'isAuthenticated'])), {}, {\n    filteredRooms: function filteredRooms() {\n      var _this = this;\n      var rooms = this.$store.getters['rooms/rooms'] || [];\n      switch (this.roomFilter) {\n        case 'public':\n          return rooms.filter(function (room) {\n            return room.is_public;\n          });\n        case 'private':\n          return rooms.filter(function (room) {\n            return !room.is_public;\n          });\n        case 'my':\n          return rooms.filter(function (room) {\n            var _this$currentUser;\n            return room.creator_id === ((_this$currentUser = _this.currentUser) === null || _this$currentUser === void 0 ? void 0 : _this$currentUser.id);\n          });\n        case 'available':\n          return rooms.filter(function (room) {\n            return room.current_players < room.max_players && room.status === 'waiting';\n          });\n        default:\n          return rooms;\n      }\n    },\n    myCharacters: function myCharacters() {\n      var _this$$store$getters$;\n      return ((_this$$store$getters$ = this.$store.getters.userCharacters) === null || _this$$store$getters$ === void 0 ? void 0 : _this$$store$getters$.length) || 0;\n    },\n    availableRoomsCount: function availableRoomsCount() {\n      return this.$store.getters['rooms/availableRooms'].length;\n    },\n    activeRoomsCount: function activeRoomsCount() {\n      var rooms = this.$store.getters['rooms/rooms'] || [];\n      return rooms.filter(function (room) {\n        return room.status === 'active';\n      }).length;\n    }\n  }),\n  methods: {\n    // 问候语相关\n    getGreeting: function getGreeting() {\n      var hour = new Date().getHours();\n      if (hour < 6) return '深夜好';\n      if (hour < 12) return '早上好';\n      if (hour < 18) return '下午好';\n      return '晚上好';\n    },\n    getRandomSubtitle: function getRandomSubtitle() {\n      return this.subtitles[Math.floor(Math.random() * this.subtitles.length)];\n    },\n    // 导航方法\n    openCharacterManager: function openCharacterManager() {\n      if (!this.isAuthenticated) {\n        this.$router.push('/login');\n        return;\n      }\n      this.$router.push('/characters');\n    },\n    // 房间操作\n    createRoom: function createRoom() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              if (_this2.isAuthenticated) {\n                _context.n = 1;\n                break;\n              }\n              _this2.$router.push('/login');\n              return _context.a(2);\n            case 1:\n              _this2.$router.push('/create-room');\n            case 2:\n              return _context.a(2);\n          }\n        }, _callee);\n      }))();\n    },\n    joinRandomRoom: function joinRandomRoom() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var availableRooms, randomRoom;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              if (_this3.isAuthenticated) {\n                _context2.n = 1;\n                break;\n              }\n              _this3.$router.push('/login');\n              return _context2.a(2);\n            case 1:\n              availableRooms = _this3.filteredRooms.filter(function (room) {\n                return room.current_players < room.max_players && room.status === 'waiting';\n              });\n              if (availableRooms.length > 0) {\n                randomRoom = availableRooms[Math.floor(Math.random() * availableRooms.length)];\n                _this3.joinRoom(randomRoom);\n              } else {\n                _this3.$message.info('暂无可加入的房间');\n              }\n            case 2:\n              return _context2.a(2);\n          }\n        }, _callee2);\n      }))();\n    },\n    joinRoom: function joinRoom(room) {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              if (_this4.isAuthenticated) {\n                _context3.n = 1;\n                break;\n              }\n              _this4.$router.push('/login');\n              return _context3.a(2);\n            case 1:\n              if (!(room.current_players >= room.max_players)) {\n                _context3.n = 2;\n                break;\n              }\n              _this4.$message.warning('房间已满');\n              return _context3.a(2);\n            case 2:\n              try {\n                // 这里可以添加加入房间的逻辑，比如检查权限等\n                // await this.$store.dispatch('joinRoom', room.id)\n                _this4.$router.push(\"/room/\".concat(room.id));\n              } catch (error) {\n                console.error('加入房间失败:', error);\n                _this4.$message.error('加入房间失败');\n              }\n            case 3:\n              return _context3.a(2);\n          }\n        }, _callee3);\n      }))();\n    },\n    refreshRooms: function refreshRooms() {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        var _t;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              _context4.p = 0;\n              _this5.loading = true;\n              _context4.n = 1;\n              return _this5.$store.dispatch('rooms/loadRooms');\n            case 1:\n              _this5.$message.success('房间列表已刷新');\n              _context4.n = 3;\n              break;\n            case 2:\n              _context4.p = 2;\n              _t = _context4.v;\n              console.error('刷新房间列表失败:', _t);\n              _this5.$message.error('刷新失败');\n            case 3:\n              _context4.p = 3;\n              _this5.loading = false;\n              return _context4.f(3);\n            case 4:\n              return _context4.a(2);\n          }\n        }, _callee4, null, [[0, 2, 3, 4]]);\n      }))();\n    },\n    // 状态相关\n    getStatusIcon: function getStatusIcon(status) {\n      var iconMap = {\n        active: 'fas fa-play',\n        waiting: 'fas fa-clock',\n        finished: 'fas fa-check',\n        paused: 'fas fa-pause'\n      };\n      return iconMap[status] || 'fas fa-question';\n    },\n    getStatusText: function getStatusText(status) {\n      var textMap = {\n        active: '进行中',\n        waiting: '等待中',\n        finished: '已结束',\n        paused: '已暂停'\n      };\n      return textMap[status] || '未知';\n    },\n    // 空状态\n    getEmptyStateTitle: function getEmptyStateTitle() {\n      switch (this.roomFilter) {\n        case 'public':\n          return '暂无公开房间';\n        case 'private':\n          return '暂无私人房间';\n        case 'my':\n          return '你还没有创建房间';\n        case 'available':\n          return '暂无可加入的房间';\n        default:\n          return '暂无房间';\n      }\n    },\n    getEmptyStateDesc: function getEmptyStateDesc() {\n      switch (this.roomFilter) {\n        case 'public':\n          return '当前没有公开的游戏房间';\n        case 'private':\n          return '当前没有私人游戏房间';\n        case 'my':\n          return '创建一个新房间开始你的冒险吧！';\n        case 'available':\n          return '所有房间都已满员或正在进行中';\n        default:\n          return '创建一个新房间开始你的冒险吧！';\n      }\n    },\n    // 工具方法\n    formatTime: function formatTime(time) {\n      if (!time) return '未知时间';\n      var now = new Date();\n      var timeDate = new Date(time);\n      var diffMs = now - timeDate;\n      var diffMins = Math.floor(diffMs / 60000);\n      var diffHours = Math.floor(diffMs / 3600000);\n      var diffDays = Math.floor(diffMs / 86400000);\n      if (diffMins < 1) return '刚刚';\n      if (diffMins < 60) return \"\".concat(diffMins, \"\\u5206\\u949F\\u524D\");\n      if (diffHours < 24) return \"\".concat(diffHours, \"\\u5C0F\\u65F6\\u524D\");\n      if (diffDays < 7) return \"\".concat(diffDays, \"\\u5929\\u524D\");\n      return timeDate.toLocaleDateString('zh-CN');\n    }\n  },\n  mounted: function mounted() {\n    // 加载房间列表\n    this.refreshRooms();\n\n    // 加载用户数据\n    if (this.isAuthenticated) {\n      this.$store.dispatch('loadUserCharacters');\n    }\n\n    // 从本地存储恢复视图模式\n    var savedViewMode = this.safeGetItem('home-view-mode');\n    if (savedViewMode) {\n      this.viewMode = savedViewMode;\n    }\n  },\n  watch: {\n    viewMode: function viewMode(newMode) {\n      this.safeSetItem('home-view-mode', newMode);\n    },\n    isAuthenticated: function isAuthenticated(newVal) {\n      if (newVal) {\n        this.$store.dispatch('loadUserCharacters');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "storageMixin", "name", "mixins", "data", "roomFilter", "viewMode", "loading", "subtitles", "computed", "_objectSpread", "filteredRooms", "_this", "rooms", "$store", "getters", "filter", "room", "is_public", "_this$currentUser", "creator_id", "currentUser", "id", "current_players", "max_players", "status", "myCharacters", "_this$$store$getters$", "userCharacters", "length", "availableRoomsCount", "activeRoomsCount", "methods", "getGreeting", "hour", "Date", "getHours", "getRandomSubtitle", "Math", "floor", "random", "openCharacterManager", "isAuthenticated", "$router", "push", "createRoom", "_this2", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "a", "joinRandomRoom", "_this3", "_callee2", "availableRooms", "randomRoom", "_context2", "joinRoom", "$message", "info", "_this4", "_callee3", "_context3", "warning", "concat", "error", "console", "refreshRooms", "_this5", "_callee4", "_t", "_context4", "p", "dispatch", "success", "v", "f", "getStatusIcon", "iconMap", "active", "waiting", "finished", "paused", "getStatusText", "textMap", "getEmptyStateTitle", "getEmptyStateDesc", "formatTime", "time", "now", "timeDate", "diffMs", "diffMins", "diffHours", "diffDays", "toLocaleDateString", "mounted", "savedViewMode", "safeGetItem", "watch", "newMode", "safeSetItem", "newVal"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-page\">\r\n    <!-- 简化的欢迎区域 -->\r\n    <section class=\"welcome-section\">\r\n      <div class=\"welcome-content\">\r\n        <div class=\"welcome-text\">\r\n          <h1 class=\"welcome-title\">\r\n            <span v-if=\"isAuthenticated\">{{ getGreeting() }}，{{ currentUser?.username || '探索者' }}</span>\r\n            <span v-else>欢迎来到COC跑团</span>\r\n          </h1>\r\n          <p class=\"welcome-subtitle\">{{ getRandomSubtitle() }}</p>\r\n        </div>\r\n        \r\n        <!-- 未登录时显示登录按钮 -->\r\n        <div class=\"welcome-actions\" v-if=\"!isAuthenticated\">\r\n          <router-link to=\"/login\" class=\"welcome-btn primary\">\r\n            <i class=\"fas fa-sign-in-alt\"></i>\r\n            <span>立即登录</span>\r\n          </router-link>\r\n          <router-link to=\"/register\" class=\"welcome-btn secondary\">\r\n            <i class=\"fas fa-user-plus\"></i>\r\n            <span>注册账号</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <main class=\"main-content\">\r\n      <!-- 快速操作区域 -->\r\n      <section class=\"quick-actions-section\" v-if=\"isAuthenticated\">\r\n        <div class=\"quick-actions-grid\">\r\n          <button class=\"action-btn primary\" @click=\"createRoom\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>创建房间</span>\r\n          </button>\r\n          \r\n          <button class=\"action-btn\" @click=\"joinRandomRoom\" :disabled=\"availableRoomsCount === 0\">\r\n            <i class=\"fas fa-random\"></i>\r\n            <span>随机加入</span>\r\n          </button>\r\n          \r\n          <button class=\"action-btn\" @click=\"openCharacterManager\">\r\n            <i class=\"fas fa-user-friends\"></i>\r\n            <span>角色管理</span>\r\n          </button>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 房间列表区域 -->\r\n      <section class=\"rooms-section\">\r\n        <div class=\"section-header\">\r\n          <div class=\"header-left\">\r\n            <h2 class=\"section-title\">\r\n              <i class=\"fas fa-door-open\"></i>\r\n              <span>房间大厅</span>\r\n            </h2>\r\n            <p class=\"section-subtitle\">发现正在进行的游戏</p>\r\n          </div>\r\n          \r\n          <div class=\"header-controls\">\r\n            <div class=\"filter-group\">\r\n              <label class=\"filter-label\">筛选：</label>\r\n              <select v-model=\"roomFilter\" class=\"filter-select\">\r\n                <option value=\"all\">全部房间</option>\r\n                <option value=\"public\">公开房间</option>\r\n                <option value=\"private\">私人房间</option>\r\n                <option value=\"my\" v-if=\"isAuthenticated\">我的房间</option>\r\n                <option value=\"available\">可加入</option>\r\n              </select>\r\n            </div>\r\n            \r\n            <div class=\"view-controls\">\r\n              <button \r\n                @click=\"viewMode = 'grid'\" \r\n                class=\"view-btn\" \r\n                :class=\"{ 'active': viewMode === 'grid' }\"\r\n                title=\"网格视图\"\r\n              >\r\n                <i class=\"fas fa-th\"></i>\r\n              </button>\r\n              <button \r\n                @click=\"viewMode = 'list'\" \r\n                class=\"view-btn\" \r\n                :class=\"{ 'active': viewMode === 'list' }\"\r\n                title=\"列表视图\"\r\n              >\r\n                <i class=\"fas fa-list\"></i>\r\n              </button>\r\n            </div>\r\n            \r\n            <button @click=\"refreshRooms\" class=\"refresh-btn\" :disabled=\"loading\" title=\"刷新房间列表\">\r\n              <i class=\"fas fa-sync-alt\" :class=\"{ 'fa-spin': loading }\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 房间统计 -->\r\n        <div class=\"rooms-stats\" v-if=\"filteredRooms.length > 0\">\r\n          <div class=\"stats-item\">\r\n            <span class=\"stats-number\">{{ filteredRooms.length }}</span>\r\n            <span class=\"stats-label\">个房间</span>\r\n          </div>\r\n          <div class=\"stats-item\">\r\n            <span class=\"stats-number\">{{ availableRoomsCount }}</span>\r\n            <span class=\"stats-label\">可加入</span>\r\n          </div>\r\n          <div class=\"stats-item\">\r\n            <span class=\"stats-number\">{{ activeRoomsCount }}</span>\r\n            <span class=\"stats-label\">进行中</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 房间列表 -->\r\n        <div class=\"rooms-container\">\r\n          <!-- 网格视图 -->\r\n          <div v-if=\"viewMode === 'grid'\" class=\"room-grid\">\r\n            <div \r\n              v-for=\"room in filteredRooms\" \r\n              :key=\"room.id\" \r\n              class=\"room-card\" \r\n              @click=\"joinRoom(room)\"\r\n              :class=\"{ 'full': room.current_players >= room.max_players }\"\r\n            >\r\n              <div class=\"room-header\">\r\n                <div class=\"room-info\">\r\n                  <h3 class=\"room-title\">{{ room.name }}</h3>\r\n                  <div class=\"room-meta\">\r\n                    <span class=\"room-creator\">by {{ room.creator_name || '未知' }}</span>\r\n                    <span class=\"room-time\">{{ formatTime(room.created_at) }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"room-status-badge\" :class=\"room.status\">\r\n                  <i :class=\"getStatusIcon(room.status)\"></i>\r\n                  <span>{{ getStatusText(room.status) }}</span>\r\n                </div>\r\n              </div>\r\n              \r\n              <p class=\"room-description\">{{ room.description || '暂无描述' }}</p>\r\n              \r\n              <div class=\"room-tags\" v-if=\"room.tags && room.tags.length > 0\">\r\n                <span v-for=\"tag in room.tags.slice(0, 3)\" :key=\"tag\" class=\"room-tag\">{{ tag }}</span>\r\n              </div>\r\n              \r\n              <div class=\"room-footer\">\r\n                <div class=\"room-players\">\r\n                  <div class=\"players-info\">\r\n                    <i class=\"fas fa-users\"></i>\r\n                    <span>{{ room.current_players }}/{{ room.max_players }}</span>\r\n                  </div>\r\n                  <div class=\"players-bar\">\r\n                    <div \r\n                      class=\"players-fill\" \r\n                      :style=\"{ width: (room.current_players / room.max_players * 100) + '%' }\"\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"room-actions\">\r\n                  <button \r\n                    class=\"join-btn\" \r\n                    :disabled=\"room.current_players >= room.max_players || !isAuthenticated\"\r\n                    @click.stop=\"joinRoom(room)\"\r\n                  >\r\n                    {{ room.current_players >= room.max_players ? '已满' : '加入' }}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 列表视图 -->\r\n          <div v-else class=\"room-list\">\r\n            <div \r\n              v-for=\"room in filteredRooms\" \r\n              :key=\"room.id\" \r\n              class=\"room-list-item\" \r\n              @click=\"joinRoom(room)\"\r\n              :class=\"{ 'full': room.current_players >= room.max_players }\"\r\n            >\r\n              <div class=\"room-list-info\">\r\n                <div class=\"room-list-header\">\r\n                  <h3 class=\"room-list-title\">{{ room.name }}</h3>\r\n                  <div class=\"room-list-status\" :class=\"room.status\">\r\n                    <i :class=\"getStatusIcon(room.status)\"></i>\r\n                    <span>{{ getStatusText(room.status) }}</span>\r\n                  </div>\r\n                </div>\r\n                <p class=\"room-list-description\">{{ room.description || '暂无描述' }}</p>\r\n                <div class=\"room-list-meta\">\r\n                  <span class=\"meta-item\">\r\n                    <i class=\"fas fa-user\"></i>\r\n                    <span>{{ room.creator_name || '未知' }}</span>\r\n                  </span>\r\n                  <span class=\"meta-item\">\r\n                    <i class=\"fas fa-clock\"></i>\r\n                    <span>{{ formatTime(room.created_at) }}</span>\r\n                  </span>\r\n                  <span class=\"meta-item\">\r\n                    <i class=\"fas fa-users\"></i>\r\n                    <span>{{ room.current_players }}/{{ room.max_players }}</span>\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div class=\"room-list-actions\">\r\n                <button \r\n                  class=\"join-btn\" \r\n                  :disabled=\"room.current_players >= room.max_players || !isAuthenticated\"\r\n                  @click.stop=\"joinRoom(room)\"\r\n                >\r\n                  {{ room.current_players >= room.max_players ? '已满' : '加入' }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 空状态 -->\r\n          <div v-if=\"filteredRooms.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">\r\n              <i class=\"fas fa-door-open\"></i>\r\n            </div>\r\n            <h3 class=\"empty-title\">{{ getEmptyStateTitle() }}</h3>\r\n            <p class=\"empty-desc\">{{ getEmptyStateDesc() }}</p>\r\n            <div class=\"empty-actions\">\r\n              <button @click=\"createRoom\" class=\"empty-action-btn primary\" v-if=\"isAuthenticated\">\r\n                <i class=\"fas fa-plus\"></i>\r\n                <span>创建房间</span>\r\n              </button>\r\n              <button @click=\"refreshRooms\" class=\"empty-action-btn secondary\">\r\n                <i class=\"fas fa-sync-alt\"></i>\r\n                <span>刷新列表</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n\r\n    </main>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'Home',\r\n  mixins: [storageMixin],\r\n  data() {\r\n    return {\r\n      roomFilter: 'all',\r\n      viewMode: 'grid',\r\n      loading: false,\r\n      \r\n      // 副标题列表\r\n      subtitles: [\r\n        '准备开始新的冒险了吗？',\r\n        '黑暗中有什么在等待着你...',\r\n        '真相往往比想象更加恐怖',\r\n        '在未知的世界中寻找答案',\r\n        '每一次投骰都可能改变命运',\r\n        '克苏鲁的呼唤正在响起...'\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('auth', ['currentUser', 'isAuthenticated']),\r\n    \r\n    filteredRooms() {\r\n      const rooms = this.$store.getters['rooms/rooms'] || []\r\n      switch (this.roomFilter) {\r\n        case 'public':\r\n          return rooms.filter(room => room.is_public)\r\n        case 'private':\r\n          return rooms.filter(room => !room.is_public)\r\n        case 'my':\r\n          return rooms.filter(room => room.creator_id === this.currentUser?.id)\r\n        case 'available':\r\n          return rooms.filter(room => \r\n            room.current_players < room.max_players && room.status === 'waiting'\r\n          )\r\n        default:\r\n          return rooms\r\n      }\r\n    },\r\n    \r\n    myCharacters() {\r\n      return this.$store.getters.userCharacters?.length || 0\r\n    },\r\n    \r\n    availableRoomsCount() {\r\n      return this.$store.getters['rooms/availableRooms'].length\r\n    },\r\n    \r\n    activeRoomsCount() {\r\n      const rooms = this.$store.getters['rooms/rooms'] || []\r\n      return rooms.filter(room => room.status === 'active').length\r\n    }\r\n  },\r\n  methods: {\r\n    // 问候语相关\r\n    getGreeting() {\r\n      const hour = new Date().getHours()\r\n      if (hour < 6) return '深夜好'\r\n      if (hour < 12) return '早上好'\r\n      if (hour < 18) return '下午好'\r\n      return '晚上好'\r\n    },\r\n    \r\n    getRandomSubtitle() {\r\n      return this.subtitles[Math.floor(Math.random() * this.subtitles.length)]\r\n    },\r\n    \r\n    // 导航方法\r\n    openCharacterManager() {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      this.$router.push('/characters')\r\n    },\r\n    \r\n\r\n    \r\n    // 房间操作\r\n    async createRoom() {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      this.$router.push('/create-room')\r\n    },\r\n    \r\n    async joinRandomRoom() {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      \r\n      const availableRooms = this.filteredRooms.filter(room => \r\n        room.current_players < room.max_players && room.status === 'waiting'\r\n      )\r\n      \r\n      if (availableRooms.length > 0) {\r\n        const randomRoom = availableRooms[Math.floor(Math.random() * availableRooms.length)]\r\n        this.joinRoom(randomRoom)\r\n      } else {\r\n        this.$message.info('暂无可加入的房间')\r\n      }\r\n    },\r\n    \r\n    async joinRoom(room) {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      \r\n      if (room.current_players >= room.max_players) {\r\n        this.$message.warning('房间已满')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        // 这里可以添加加入房间的逻辑，比如检查权限等\r\n        // await this.$store.dispatch('joinRoom', room.id)\r\n        this.$router.push(`/room/${room.id}`)\r\n      } catch (error) {\r\n        console.error('加入房间失败:', error)\r\n        this.$message.error('加入房间失败')\r\n      }\r\n    },\r\n    \r\n    async refreshRooms() {\r\n      try {\r\n        this.loading = true\r\n        await this.$store.dispatch('rooms/loadRooms')\r\n        this.$message.success('房间列表已刷新')\r\n      } catch (error) {\r\n        console.error('刷新房间列表失败:', error)\r\n        this.$message.error('刷新失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 状态相关\r\n    getStatusIcon(status) {\r\n      const iconMap = {\r\n        active: 'fas fa-play',\r\n        waiting: 'fas fa-clock',\r\n        finished: 'fas fa-check',\r\n        paused: 'fas fa-pause'\r\n      }\r\n      return iconMap[status] || 'fas fa-question'\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const textMap = {\r\n        active: '进行中',\r\n        waiting: '等待中',\r\n        finished: '已结束',\r\n        paused: '已暂停'\r\n      }\r\n      return textMap[status] || '未知'\r\n    },\r\n    \r\n\r\n    \r\n    // 空状态\r\n    getEmptyStateTitle() {\r\n      switch (this.roomFilter) {\r\n        case 'public':\r\n          return '暂无公开房间'\r\n        case 'private':\r\n          return '暂无私人房间'\r\n        case 'my':\r\n          return '你还没有创建房间'\r\n        case 'available':\r\n          return '暂无可加入的房间'\r\n        default:\r\n          return '暂无房间'\r\n      }\r\n    },\r\n    \r\n    getEmptyStateDesc() {\r\n      switch (this.roomFilter) {\r\n        case 'public':\r\n          return '当前没有公开的游戏房间'\r\n        case 'private':\r\n          return '当前没有私人游戏房间'\r\n        case 'my':\r\n          return '创建一个新房间开始你的冒险吧！'\r\n        case 'available':\r\n          return '所有房间都已满员或正在进行中'\r\n        default:\r\n          return '创建一个新房间开始你的冒险吧！'\r\n      }\r\n    },\r\n    \r\n    // 工具方法\r\n    formatTime(time) {\r\n      if (!time) return '未知时间'\r\n      \r\n      const now = new Date()\r\n      const timeDate = new Date(time)\r\n      const diffMs = now - timeDate\r\n      const diffMins = Math.floor(diffMs / 60000)\r\n      const diffHours = Math.floor(diffMs / 3600000)\r\n      const diffDays = Math.floor(diffMs / 86400000)\r\n      \r\n      if (diffMins < 1) return '刚刚'\r\n      if (diffMins < 60) return `${diffMins}分钟前`\r\n      if (diffHours < 24) return `${diffHours}小时前`\r\n      if (diffDays < 7) return `${diffDays}天前`\r\n      \r\n      return timeDate.toLocaleDateString('zh-CN')\r\n    },\r\n    \r\n\r\n  },\r\n  \r\n  mounted() {\r\n    // 加载房间列表\r\n    this.refreshRooms()\r\n    \r\n    // 加载用户数据\r\n    if (this.isAuthenticated) {\r\n      this.$store.dispatch('loadUserCharacters')\r\n    }\r\n    \r\n    // 从本地存储恢复视图模式\r\n    const savedViewMode = this.safeGetItem('home-view-mode')\r\n    if (savedViewMode) {\r\n      this.viewMode = savedViewMode\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    viewMode(newMode) {\r\n      this.safeSetItem('home-view-mode', newMode)\r\n    },\r\n    \r\n    isAuthenticated(newVal) {\r\n      if (newVal) {\r\n        this.$store.dispatch('loadUserCharacters')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script><style\r\n scoped>\r\n/* ===== 页面基础样式 ===== */\r\n.home-page {\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  padding-bottom: var(--spacing-8);\r\n}\r\n\r\n/* ===== 简化的欢迎区域 ===== */\r\n.welcome-section {\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 50%, var(--success-400) 100%);\r\n  color: var(--text-inverse);\r\n  padding: var(--spacing-6) 0;\r\n  margin-bottom: var(--spacing-6);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.welcome-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>') repeat;\r\n  opacity: 0.3;\r\n}\r\n\r\n.welcome-content {\r\n  position: relative;\r\n  z-index: 1;\r\n  text-align: center;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--spacing-4);\r\n}\r\n\r\n.welcome-text {\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: var(--font-size-3xl);\r\n  font-weight: var(--font-weight-bold);\r\n  margin-bottom: var(--spacing-2);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: var(--font-size-lg);\r\n  opacity: 0.9;\r\n  margin: 0;\r\n  font-style: italic;\r\n}\r\n\r\n.welcome-actions {\r\n  display: flex;\r\n  gap: var(--spacing-3);\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.welcome-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-5);\r\n  border-radius: var(--radius-xl);\r\n  text-decoration: none;\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  border: 2px solid transparent;\r\n  min-width: 140px;\r\n  justify-content: center;\r\n}\r\n\r\n.welcome-btn.primary {\r\n  background: var(--bg-primary);\r\n  color: var(--success-600);\r\n  border-color: var(--bg-primary);\r\n}\r\n\r\n.welcome-btn.primary:hover {\r\n  background: var(--success-50);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-lg);\r\n  text-decoration: none;\r\n  color: var(--success-700);\r\n}\r\n\r\n.welcome-btn.secondary {\r\n  background: transparent;\r\n  color: var(--text-inverse);\r\n  border-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.welcome-btn.secondary:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n  text-decoration: none;\r\n  color: var(--text-inverse);\r\n}\r\n\r\n/* ===== 主要内容区域 ===== */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--spacing-4);\r\n}\r\n\r\n/* ===== 快速操作区域 ===== */\r\n.quick-actions-section {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.quick-actions-grid {\r\n  display: flex;\r\n  gap: var(--spacing-3);\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-4);\r\n  background: var(--success-50);\r\n  color: var(--success-700);\r\n  border: 2px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  cursor: pointer;\r\n  min-width: 140px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn:hover:not(:disabled) {\r\n  background: var(--success-100);\r\n  border-color: var(--success-300);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.action-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: var(--success-600);\r\n  color: var(--text-inverse);\r\n  border-color: var(--success-600);\r\n}\r\n\r\n.action-btn.primary:hover:not(:disabled) {\r\n  background: var(--success-700);\r\n  border-color: var(--success-700);\r\n  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.5);\r\n}\r\n\r\n.welcome-btn.secondary:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n  transform: translateY(-2px);\r\n  text-decoration: none;\r\n  color: var(--text-inverse);\r\n}\r\n\r\n/* ===== 主要内容区域 ===== */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--spacing-4);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-8);\r\n}\r\n\r\n/* ===== 房间列表区域 ===== */\r\n.rooms-section {\r\n  background: var(--bg-elevated);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--spacing-6);\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--success-200);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-6);\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-2);\r\n}\r\n\r\n.section-title i {\r\n  color: var(--success-500);\r\n}\r\n\r\n.section-subtitle {\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-base);\r\n  margin: 0;\r\n}\r\n\r\n\r\n\r\n.header-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.filter-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.filter-select {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  transition: all var(--transition-fast);\r\n  min-width: 120px;\r\n}\r\n\r\n.filter-select {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  transition: all var(--transition-fast);\r\n  min-width: 120px;\r\n}\r\n\r\n.filter-select:focus {\r\n  outline: none;\r\n  border-color: var(--success-400);\r\n  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.view-controls {\r\n  display: flex;\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-md);\r\n  overflow: hidden;\r\n}\r\n\r\n.view-btn {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: none;\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  border-right: 1px solid var(--success-200);\r\n}\r\n\r\n.view-btn:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.view-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n}\r\n\r\n.view-btn.active {\r\n  background: var(--success-500);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.refresh-btn {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  min-width: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-400);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.refresh-btn .fa-spin {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* ===== 房间统计 ===== */\r\n.rooms-stats {\r\n  display: flex;\r\n  gap: var(--spacing-6);\r\n  margin-bottom: var(--spacing-4);\r\n  padding: var(--spacing-4);\r\n  background: var(--success-50);\r\n  border-radius: var(--radius-lg);\r\n  border: 1px solid var(--success-200);\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.stats-number {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--success-700);\r\n}\r\n\r\n.stats-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--success-600);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n/* ===== 房间网格视图 ===== */\r\n.room-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.room-card {\r\n  background: var(--bg-primary);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--spacing-5);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  box-shadow: var(--shadow-sm);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.05), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.room-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n  border-color: var(--success-400);\r\n}\r\n\r\n.room-card:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.room-card.full {\r\n  opacity: 0.7;\r\n}\r\n\r\n.room-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-3);\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.room-title {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-1);\r\n  line-height: 1.3;\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.room-creator {\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.room-status-badge {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  white-space: nowrap;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.room-status-badge.active {\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n}\r\n\r\n.room-status-badge.waiting {\r\n  background: var(--warning-100);\r\n  color: var(--warning-700);\r\n}\r\n\r\n.room-status-badge.finished {\r\n  background: var(--gray-100);\r\n  color: var(--gray-700);\r\n}\r\n\r\n.room-status-badge.paused {\r\n  background: var(--error-100);\r\n  color: var(--error-700);\r\n}\r\n\r\n.room-description {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin-bottom: var(--spacing-3);\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-tags {\r\n  display: flex;\r\n  gap: var(--spacing-1);\r\n  margin-bottom: var(--spacing-3);\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.room-tag {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n  border-radius: var(--radius-md);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.room-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-players {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.players-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--spacing-1);\r\n}\r\n\r\n.players-bar {\r\n  height: 4px;\r\n  background: var(--gray-200);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n}\r\n\r\n.players-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, var(--success-400), var(--success-500));\r\n  border-radius: var(--radius-full);\r\n  transition: width var(--transition-fast);\r\n}\r\n\r\n.room-actions {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.join-btn {\r\n  padding: var(--spacing-2) var(--spacing-4);\r\n  border: 1px solid var(--success-400);\r\n  border-radius: var(--radius-md);\r\n  background: var(--success-500);\r\n  color: var(--text-inverse);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.join-btn:hover:not(:disabled) {\r\n  background: var(--success-600);\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.join-btn:disabled {\r\n  background: var(--gray-400);\r\n  border-color: var(--gray-400);\r\n  cursor: not-allowed;\r\n  opacity: 0.6;\r\n}\r\n\r\n/* ===== 房间列表视图 ===== */\r\n.room-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-list-item {\r\n  background: var(--bg-primary);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--spacing-4);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.room-list-item:hover {\r\n  border-color: var(--success-400);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.room-list-item.full {\r\n  opacity: 0.7;\r\n}\r\n\r\n.room-list-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.room-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-2);\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-list-title {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin: 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.room-list-status {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  white-space: nowrap;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.room-list-status.active {\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n}\r\n\r\n.room-list-status.waiting {\r\n  background: var(--warning-100);\r\n  color: var(--warning-700);\r\n}\r\n\r\n.room-list-description {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin-bottom: var(--spacing-2);\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-list-meta {\r\n  display: flex;\r\n  gap: var(--spacing-4);\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.room-list-actions {\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* ===== 空状态 ===== */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: var(--spacing-8) var(--spacing-4);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.empty-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: var(--success-100);\r\n  border-radius: var(--radius-full);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--font-size-3xl);\r\n  margin: 0 auto var(--spacing-4);\r\n  color: var(--success-400);\r\n}\r\n\r\n.empty-title {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.empty-desc {\r\n  font-size: var(--font-size-base);\r\n  margin-bottom: var(--spacing-6);\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.empty-actions {\r\n  display: flex;\r\n  gap: var(--spacing-3);\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.empty-action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-5);\r\n  border-radius: var(--radius-lg);\r\n  font-weight: var(--font-weight-medium);\r\n  transition: all var(--transition-fast);\r\n  cursor: pointer;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.empty-action-btn.primary {\r\n  background: var(--success-500);\r\n  color: var(--text-inverse);\r\n  border-color: var(--success-500);\r\n}\r\n\r\n.empty-action-btn.primary:hover {\r\n  background: var(--success-600);\r\n  border-color: var(--success-600);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.empty-action-btn.secondary {\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  border-color: var(--success-200);\r\n}\r\n\r\n.empty-action-btn.secondary:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-400);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n\r\n@media (max-width: 768px) {\r\n  .welcome-title {\r\n    font-size: var(--font-size-2xl);\r\n  }\r\n  \r\n  .welcome-subtitle {\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .quick-actions-grid {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .action-btn {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n  \r\n  .header-controls {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .rooms-stats {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .welcome-actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .welcome-btn {\r\n    width: 100%;\r\n    max-width: 250px;\r\n  }\r\n  \r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-card {\r\n    padding: var(--spacing-4);\r\n  }\r\n  \r\n  .room-list-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-list-actions {\r\n    align-self: flex-end;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n\r\n<style scoped>\r\n/* ===== 首页基础样式 ===== */\r\n.home-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--success-50) 100%);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.home-page::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"home-pattern\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"><circle cx=\"30\" cy=\"30\" r=\"3\" fill=\"%2322c55e\" opacity=\"0.03\"/><circle cx=\"15\" cy=\"15\" r=\"1.5\" fill=\"%2322c55e\" opacity=\"0.02\"/><circle cx=\"45\" cy=\"45\" r=\"1.5\" fill=\"%2322c55e\" opacity=\"0.02\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23home-pattern)\"/></svg>') repeat;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n\r\n.home-page > * {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n/* ===== 英雄区域 ===== */\r\n.hero-section {\r\n  min-height: 60vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.hero-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"hero-pattern\" width=\"80\" height=\"80\" patternUnits=\"userSpaceOnUse\"><circle cx=\"40\" cy=\"40\" r=\"4\" fill=\"%23ffffff\" opacity=\"0.05\"/><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"%23ffffff\" opacity=\"0.03\"/><circle cx=\"60\" cy=\"60\" r=\"2\" fill=\"%23ffffff\" opacity=\"0.03\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23hero-pattern)\"/></svg>') repeat;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n\r\n.hero-content {\r\n  text-align: center;\r\n  max-width: 800px;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* ===== 英雄区域 ===== */\r\n.hero-section {\r\n  min-height: 60vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n\r\n\r\n.hero-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: var(--spacing-8) var(--spacing-6);\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n\r\n.hero-text {\r\n  margin-bottom: var(--spacing-8);\r\n}\r\n\r\n.hero-title {\r\n  font-size: var(--font-size-4xl);\r\n  font-weight: var(--font-weight-bold);\r\n  margin: 0 0 var(--spacing-4) 0;\r\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n  animation: hero-title-appear 1s ease-out;\r\n}\r\n\r\n@keyframes hero-title-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.hero-subtitle {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-medium);\r\n  margin: 0 0 var(--spacing-6) 0;\r\n  opacity: 0.9;\r\n  animation: hero-subtitle-appear 1s ease-out 0.3s both;\r\n}\r\n\r\n@keyframes hero-subtitle-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 0.9;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.hero-stats {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--spacing-8);\r\n  margin-top: var(--spacing-6);\r\n  animation: hero-stats-appear 1s ease-out 0.6s both;\r\n}\r\n\r\n@keyframes hero-stats-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-4);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: var(--radius-xl);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.stat-item:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.stat-item i {\r\n  font-size: var(--font-size-2xl);\r\n  color: var(--success-200);\r\n}\r\n\r\n.stat-value {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.stat-label {\r\n  font-size: var(--font-size-sm);\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.hero-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--spacing-4);\r\n  animation: hero-actions-appear 1s ease-out 0.9s both;\r\n}\r\n\r\n@keyframes hero-actions-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.hero-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  padding: var(--spacing-4) var(--spacing-6);\r\n  border: none;\r\n  border-radius: var(--radius-xl);\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-bold);\r\n  text-decoration: none;\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.hero-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.hero-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.hero-btn.primary {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: var(--text-inverse);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.hero-btn.primary:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.hero-btn.secondary {\r\n  background: transparent;\r\n  color: var(--text-inverse);\r\n  border: 2px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.hero-btn.secondary:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.7);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* ===== 主要内容区域 ===== */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: var(--spacing-8) var(--spacing-6);\r\n}\r\n\r\n/* ===== 区域头部通用样式 ===== */\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: var(--spacing-8);\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--spacing-3);\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-3) 0;\r\n}\r\n\r\n.section-title i {\r\n  color: var(--success-600);\r\n  font-size: var(--font-size-xl);\r\n}\r\n\r\n.section-desc {\r\n  font-size: var(--font-size-base);\r\n  color: var(--text-secondary);\r\n  margin: 0;\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.section-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: var(--spacing-4);\r\n}\r\n\r\n/* ===== 快速操作区域 ===== */\r\n.quick-actions-section {\r\n  margin-bottom: var(--spacing-12);\r\n}\r\n\r\n.quick-actions-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--spacing-6);\r\n  margin-top: var(--spacing-6);\r\n}\r\n\r\n.action-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-4);\r\n  padding: var(--spacing-6);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.95) 100%);\r\n  border: 2px solid var(--success-200);\r\n  border-radius: var(--radius-2xl);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  text-align: left;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.action-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 50%, var(--success-400) 100%);\r\n  transform: scaleX(0);\r\n  transition: transform var(--transition-fast);\r\n}\r\n\r\n.action-card:hover::before {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.action-card:hover {\r\n  border-color: var(--success-300);\r\n  box-shadow: var(--shadow-xl);\r\n  transform: translateY(-6px);\r\n}\r\n\r\n.action-card:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.action-card.primary {\r\n  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);\r\n  border-color: var(--success-300);\r\n}\r\n\r\n.action-card.primary:hover {\r\n  background: linear-gradient(135deg, var(--success-200) 0%, var(--success-300) 100%);\r\n  border-color: var(--success-400);\r\n}\r\n\r\n.action-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);\r\n  border-radius: var(--radius-xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--font-size-xl);\r\n  color: var(--text-inverse);\r\n  flex-shrink: 0;\r\n  box-shadow: var(--shadow-md);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-icon::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);\r\n  transform: translateX(-100%);\r\n  transition: transform 0.6s ease;\r\n}\r\n\r\n.action-card:hover .action-icon::before {\r\n  transform: translateX(100%);\r\n}\r\n\r\n.action-content {\r\n  flex: 1;\r\n}\r\n\r\n.action-title {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-2) 0;\r\n}\r\n\r\n.action-desc {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  margin: 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* ===== 房间过滤器 ===== */\r\n.room-filters {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.9) 100%);\r\n  padding: var(--spacing-2);\r\n  border-radius: var(--radius-xl);\r\n  box-shadow: var(--shadow-sm);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.filter-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-4);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.filter-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.filter-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.filter-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.filter-btn.active {\r\n  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);\r\n  color: var(--text-inverse);\r\n  border-color: var(--success-500);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.filter-count {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: var(--text-inverse);\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-bold);\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.filter-btn:not(.active) .filter-count {\r\n  background: var(--success-200);\r\n  color: var(--success-700);\r\n}\r\n\r\n.refresh-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--font-size-base);\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n  transform: scale(1.1);\r\n  box-shadow: var(--shadow-sm);\r\n}/* =\r\n==== 房间网格 ===== */\r\n.rooms-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: var(--spacing-6);\r\n  margin-top: var(--spacing-6);\r\n}\r\n\r\n.room-card {\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.95) 100%);\r\n  border: 2px solid var(--success-200);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--spacing-6);\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-md);\r\n  animation: room-card-appear 0.5s ease-out;\r\n}\r\n\r\n@keyframes room-card-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px) scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n.room-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 50%, var(--success-400) 100%);\r\n  transform: scaleX(0);\r\n  transition: transform var(--transition-fast);\r\n}\r\n\r\n.room-card:hover {\r\n  border-color: var(--success-300);\r\n  box-shadow: var(--shadow-xl);\r\n  transform: translateY(-6px);\r\n}\r\n\r\n.room-card:hover::before {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.room-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.room-info {\r\n  flex: 1;\r\n}\r\n\r\n.room-name {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-2) 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.room-creator,\r\n.room-players {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.room-creator i {\r\n  color: var(--warning-500);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.room-players i {\r\n  color: var(--success-500);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.room-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-bold);\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.room-status.active {\r\n  background: rgba(34, 197, 94, 0.1);\r\n  color: var(--success-700);\r\n  border: 1px solid var(--success-300);\r\n}\r\n\r\n.room-status.waiting {\r\n  background: rgba(251, 191, 36, 0.1);\r\n  color: var(--warning-700);\r\n  border: 1px solid var(--warning-300);\r\n}\r\n\r\n.room-status.recruiting {\r\n  background: rgba(59, 130, 246, 0.1);\r\n  color: var(--info-700);\r\n  border: 1px solid var(--info-300);\r\n}\r\n\r\n.room-status.private {\r\n  background: rgba(107, 114, 128, 0.1);\r\n  color: var(--gray-700);\r\n  border: 1px solid var(--gray-300);\r\n}\r\n\r\n.status-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: var(--radius-full);\r\n  animation: status-pulse 2s ease-in-out infinite;\r\n}\r\n\r\n.room-status.active .status-dot {\r\n  background: var(--success-500);\r\n}\r\n\r\n.room-status.waiting .status-dot {\r\n  background: var(--warning-500);\r\n}\r\n\r\n.room-status.recruiting .status-dot {\r\n  background: var(--info-500);\r\n}\r\n\r\n.room-status.private .status-dot {\r\n  background: var(--gray-500);\r\n}\r\n\r\n@keyframes status-pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n.room-content {\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.room-description {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  line-height: 1.6;\r\n  margin: 0 0 var(--spacing-3) 0;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.room-tag {\r\n  padding: var(--spacing-1) var(--spacing-3);\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  border: 1px solid var(--success-200);\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.room-tag:hover {\r\n  background: var(--success-200);\r\n  border-color: var(--success-300);\r\n  transform: scale(1.05);\r\n}\r\n\r\n.room-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: var(--spacing-4);\r\n  border-top: 1px solid var(--success-200);\r\n}\r\n\r\n.room-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.room-time i {\r\n  color: var(--success-400);\r\n}\r\n\r\n.room-actions {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.join-btn,\r\n.view-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-2) var(--spacing-4);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-semibold);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.join-btn::before,\r\n.view-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.join-btn:hover::before,\r\n.view-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.join-btn {\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.join-btn:hover:not(:disabled) {\r\n  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.join-btn:disabled {\r\n  background: var(--disabled-bg);\r\n  color: var(--disabled-text);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.view-btn {\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--success-200);\r\n}\r\n\r\n.view-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n/* ===== 空状态 ===== */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: var(--spacing-12) var(--spacing-6);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.9) 100%);\r\n  border: 2px dashed var(--success-300);\r\n  border-radius: var(--radius-2xl);\r\n  margin-top: var(--spacing-6);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.empty-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);\r\n  border-radius: var(--radius-full);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto var(--spacing-4);\r\n  font-size: var(--font-size-2xl);\r\n  color: var(--success-600);\r\n  animation: empty-icon-float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes empty-icon-float {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n\r\n.empty-title {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-3) 0;\r\n}\r\n\r\n.empty-desc {\r\n  font-size: var(--font-size-base);\r\n  color: var(--text-secondary);\r\n  margin: 0 0 var(--spacing-6) 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.empty-action-btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  padding: var(--spacing-4) var(--spacing-6);\r\n  border: none;\r\n  border-radius: var(--radius-xl);\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n  font-size: var(--font-size-base);\r\n  font-weight: var(--font-weight-bold);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.empty-action-btn:hover {\r\n  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);\r\n  transform: translateY(-3px);\r\n  box-shadow: var(--shadow-lg);\r\n}/* ====\r\n= 响应式设计 ===== */\r\n@media (max-width: 1024px) {\r\n  .hero-content {\r\n    padding: var(--spacing-6) var(--spacing-4);\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: var(--font-size-3xl);\r\n  }\r\n  \r\n  .hero-subtitle {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n  \r\n  .hero-stats {\r\n    gap: var(--spacing-6);\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-6) var(--spacing-4);\r\n  }\r\n  \r\n  .quick-actions-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: var(--spacing-4);\r\n  }\r\n  \r\n  .rooms-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: var(--spacing-4);\r\n  }\r\n  \r\n  .section-controls {\r\n    flex-direction: column;\r\n    gap: var(--spacing-4);\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-filters {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .hero-section {\r\n    min-height: 50vh;\r\n  }\r\n  \r\n  .hero-content {\r\n    padding: var(--spacing-4) var(--spacing-3);\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: var(--font-size-2xl);\r\n  }\r\n  \r\n  .hero-subtitle {\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .hero-stats {\r\n    flex-direction: column;\r\n    gap: var(--spacing-4);\r\n    align-items: center;\r\n  }\r\n  \r\n  .stat-item {\r\n    flex-direction: row;\r\n    padding: var(--spacing-3);\r\n    width: 100%;\r\n    max-width: 200px;\r\n  }\r\n  \r\n  .hero-actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .hero-btn {\r\n    width: 100%;\r\n    max-width: 250px;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-4) var(--spacing-3);\r\n  }\r\n  \r\n  .section-title {\r\n    font-size: var(--font-size-xl);\r\n  }\r\n  \r\n  .quick-actions-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .action-card {\r\n    padding: var(--spacing-4);\r\n  }\r\n  \r\n  .action-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: var(--font-size-lg);\r\n  }\r\n  \r\n  .rooms-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .room-card {\r\n    padding: var(--spacing-4);\r\n  }\r\n  \r\n  .room-header {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .room-footer {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .room-actions {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .join-btn,\r\n  .view-btn {\r\n    flex: 1;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .hero-content {\r\n    padding: var(--spacing-3) var(--spacing-2);\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: var(--font-size-xl);\r\n  }\r\n  \r\n  .hero-subtitle {\r\n    font-size: var(--font-size-sm);\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-3) var(--spacing-2);\r\n  }\r\n  \r\n  .section-header {\r\n    margin-bottom: var(--spacing-6);\r\n  }\r\n  \r\n  .section-title {\r\n    font-size: var(--font-size-lg);\r\n    flex-direction: column;\r\n    gap: var(--spacing-2);\r\n  }\r\n  \r\n  .action-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    padding: var(--spacing-3);\r\n  }\r\n  \r\n  .action-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .room-card {\r\n    padding: var(--spacing-3);\r\n  }\r\n  \r\n  .room-name {\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .room-meta {\r\n    gap: var(--spacing-2);\r\n  }\r\n  \r\n  .room-creator,\r\n  .room-players {\r\n    font-size: var(--font-size-xs);\r\n  }\r\n  \r\n  .room-tags {\r\n    gap: var(--spacing-1);\r\n  }\r\n  \r\n  .room-tag {\r\n    font-size: var(--font-size-xs);\r\n    padding: var(--spacing-1) var(--spacing-2);\r\n  }\r\n  \r\n  .filter-btn {\r\n    padding: var(--spacing-2) var(--spacing-3);\r\n    font-size: var(--font-size-xs);\r\n  }\r\n  \r\n  .filter-btn span:not(.filter-count) {\r\n    display: none;\r\n  }\r\n  \r\n  .empty-state {\r\n    padding: var(--spacing-8) var(--spacing-4);\r\n  }\r\n  \r\n  .empty-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: var(--font-size-xl);\r\n  }\r\n  \r\n  .empty-title {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n  \r\n  .empty-desc {\r\n    font-size: var(--font-size-sm);\r\n  }\r\n}\r\n\r\n/* ===== 可访问性增强 ===== */\r\n.home-page button:focus,\r\n.home-page a:focus {\r\n  outline: 2px solid var(--success-500);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* ===== 高对比度模式 ===== */\r\n@media (prefers-contrast: high) {\r\n  .hero-section {\r\n    border-bottom: 3px solid var(--success-400);\r\n  }\r\n  \r\n  .action-card,\r\n  .room-card {\r\n    border-width: 3px;\r\n  }\r\n  \r\n  .hero-btn,\r\n  .join-btn,\r\n  .view-btn,\r\n  .filter-btn {\r\n    border-width: 2px;\r\n  }\r\n  \r\n  .empty-state {\r\n    border-width: 3px;\r\n  }\r\n}\r\n\r\n/* ===== 减少动画模式 ===== */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .home-page *,\r\n  .home-page *::before,\r\n  .home-page *::after {\r\n    animation: none !important;\r\n    transition: none !important;\r\n  }\r\n  \r\n  .action-card:hover,\r\n  .room-card:hover,\r\n  .hero-btn:hover,\r\n  .join-btn:hover,\r\n  .view-btn:hover {\r\n    transform: none !important;\r\n  }\r\n}\r\n\r\n/* ===== 打印样式 ===== */\r\n@media print {\r\n  .home-page {\r\n    background: white !important;\r\n  }\r\n  \r\n  .hero-section {\r\n    background: var(--gray-100) !important;\r\n    color: var(--text-primary) !important;\r\n    page-break-after: avoid;\r\n  }\r\n  \r\n  .hero-actions,\r\n  .room-actions,\r\n  .section-controls {\r\n    display: none !important;\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-4) !important;\r\n  }\r\n  \r\n  .action-card,\r\n  .room-card {\r\n    page-break-inside: avoid;\r\n    margin-bottom: var(--spacing-4);\r\n    box-shadow: none !important;\r\n    border: 1px solid var(--gray-300) !important;\r\n  }\r\n  \r\n  .quick-actions-grid,\r\n  .rooms-grid {\r\n    grid-template-columns: 1fr !important;\r\n    gap: var(--spacing-3) !important;\r\n  }\r\n}\r\n\r\n/* ===== 自定义滚动条 ===== */\r\n.home-page::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.home-page::-webkit-scrollbar-track {\r\n  background: var(--success-100);\r\n  border-radius: var(--radius-full);\r\n}\r\n\r\n.home-page::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(180deg, var(--success-400) 0%, var(--success-500) 100%);\r\n  border-radius: var(--radius-full);\r\n  border: 1px solid var(--success-300);\r\n}\r\n\r\n.home-page::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(180deg, var(--success-500) 0%, var(--success-600) 100%);\r\n}\r\n\r\n/* ===== 加载状态 ===== */\r\n.loading-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--spacing-8);\r\n  color: var(--success-600);\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 4px solid var(--success-200);\r\n  border-top: 4px solid var(--success-600);\r\n  border-radius: var(--radius-full);\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;AAoPA,SAASA,UAAS,QAAS,MAAK;AAChC,SAASC,YAAW,QAAS,uBAAsB;AAEnD,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,CAACF,YAAY,CAAC;EACtBG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,KAAK;MAEd;MACAC,SAAS,EAAE,CACT,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,aAAa,EACb,cAAc,EACd,eAAc;IAElB;EACF,CAAC;EACDC,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHV,UAAU,CAAC,MAAM,EAAE,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;IAEzDW,aAAa,WAAbA,aAAaA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACd,IAAMC,KAAI,GAAI,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,aAAa,KAAK,EAAC;MACrD,QAAQ,IAAI,CAACV,UAAU;QACrB,KAAK,QAAQ;UACX,OAAOQ,KAAK,CAACG,MAAM,CAAC,UAAAC,IAAG;YAAA,OAAKA,IAAI,CAACC,SAAS;UAAA;QAC5C,KAAK,SAAS;UACZ,OAAOL,KAAK,CAACG,MAAM,CAAC,UAAAC,IAAG;YAAA,OAAK,CAACA,IAAI,CAACC,SAAS;UAAA;QAC7C,KAAK,IAAI;UACP,OAAOL,KAAK,CAACG,MAAM,CAAC,UAAAC,IAAG;YAAA,IAAAE,iBAAA;YAAA,OAAKF,IAAI,CAACG,UAAS,OAAAD,iBAAA,GAAMP,KAAI,CAACS,WAAW,cAAAF,iBAAA,uBAAhBA,iBAAA,CAAkBG,EAAE;UAAA;QACtE,KAAK,WAAW;UACd,OAAOT,KAAK,CAACG,MAAM,CAAC,UAAAC,IAAG;YAAA,OACrBA,IAAI,CAACM,eAAc,GAAIN,IAAI,CAACO,WAAU,IAAKP,IAAI,CAACQ,MAAK,KAAM,SAAQ;UAAA,CACrE;QACF;UACE,OAAOZ,KAAI;MACf;IACF,CAAC;IAEDa,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MACb,OAAO,EAAAA,qBAAA,OAAI,CAACb,MAAM,CAACC,OAAO,CAACa,cAAc,cAAAD,qBAAA,uBAAlCA,qBAAA,CAAoCE,MAAK,KAAK;IACvD,CAAC;IAEDC,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,OAAO,IAAI,CAAChB,MAAM,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAACc,MAAK;IAC1D,CAAC;IAEDE,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAMlB,KAAI,GAAI,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,aAAa,KAAK,EAAC;MACrD,OAAOF,KAAK,CAACG,MAAM,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACQ,MAAK,KAAM,QAAQ;MAAA,EAAC,CAACI,MAAK;IAC7D;EAAA,EACD;EACDG,OAAO,EAAE;IACP;IACAC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAMC,IAAG,GAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC;MACjC,IAAIF,IAAG,GAAI,CAAC,EAAE,OAAO,KAAI;MACzB,IAAIA,IAAG,GAAI,EAAE,EAAE,OAAO,KAAI;MAC1B,IAAIA,IAAG,GAAI,EAAE,EAAE,OAAO,KAAI;MAC1B,OAAO,KAAI;IACb,CAAC;IAEDG,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAC7B,SAAS,CAAC8B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,IAAI,CAAChC,SAAS,CAACqB,MAAM,CAAC;IACzE,CAAC;IAED;IACAY,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAAC,IAAI,CAACC,eAAe,EAAE;QACzB,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC,QAAQ;QAC1B;MACF;MACA,IAAI,CAACD,OAAO,CAACC,IAAI,CAAC,aAAa;IACjC,CAAC;IAID;IACMC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;QAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAA,IACZP,MAAI,CAACJ,eAAe;gBAAAU,QAAA,CAAAC,CAAA;gBAAA;cAAA;cACvBP,MAAI,CAACH,OAAO,CAACC,IAAI,CAAC,QAAQ;cAAA,OAAAQ,QAAA,CAAAE,CAAA;YAAA;cAG5BR,MAAI,CAACH,OAAO,CAACC,IAAI,CAAC,cAAc;YAAA;cAAA,OAAAQ,QAAA,CAAAE,CAAA;UAAA;QAAA,GAAAJ,OAAA;MAAA;IAClC,CAAC;IAEKK,cAAc,WAAdA,cAAcA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAAT,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAQ,SAAA;QAAA,IAAAC,cAAA,EAAAC,UAAA;QAAA,OAAAX,YAAA,GAAAG,CAAA,WAAAS,SAAA;UAAA,kBAAAA,SAAA,CAAAP,CAAA;YAAA;cAAA,IAChBG,MAAI,CAACd,eAAe;gBAAAkB,SAAA,CAAAP,CAAA;gBAAA;cAAA;cACvBG,MAAI,CAACb,OAAO,CAACC,IAAI,CAAC,QAAQ;cAAA,OAAAgB,SAAA,CAAAN,CAAA;YAAA;cAItBI,cAAa,GAAIF,MAAI,CAAC7C,aAAa,CAACK,MAAM,CAAC,UAAAC,IAAG;gBAAA,OAClDA,IAAI,CAACM,eAAc,GAAIN,IAAI,CAACO,WAAU,IAAKP,IAAI,CAACQ,MAAK,KAAM,SAAQ;cAAA,CACrE;cAEA,IAAIiC,cAAc,CAAC7B,MAAK,GAAI,CAAC,EAAE;gBACvB8B,UAAS,GAAID,cAAc,CAACpB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIkB,cAAc,CAAC7B,MAAM,CAAC;gBACnF2B,MAAI,CAACK,QAAQ,CAACF,UAAU;cAC1B,OAAO;gBACLH,MAAI,CAACM,QAAQ,CAACC,IAAI,CAAC,UAAU;cAC/B;YAAA;cAAA,OAAAH,SAAA,CAAAN,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACF,CAAC;IAEKI,QAAQ,WAARA,QAAQA,CAAC5C,IAAI,EAAE;MAAA,IAAA+C,MAAA;MAAA,OAAAjB,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAgB,SAAA;QAAA,OAAAjB,YAAA,GAAAG,CAAA,WAAAe,SAAA;UAAA,kBAAAA,SAAA,CAAAb,CAAA;YAAA;cAAA,IACdW,MAAI,CAACtB,eAAe;gBAAAwB,SAAA,CAAAb,CAAA;gBAAA;cAAA;cACvBW,MAAI,CAACrB,OAAO,CAACC,IAAI,CAAC,QAAQ;cAAA,OAAAsB,SAAA,CAAAZ,CAAA;YAAA;cAAA,MAIxBrC,IAAI,CAACM,eAAc,IAAKN,IAAI,CAACO,WAAW;gBAAA0C,SAAA,CAAAb,CAAA;gBAAA;cAAA;cAC1CW,MAAI,CAACF,QAAQ,CAACK,OAAO,CAAC,MAAM;cAAA,OAAAD,SAAA,CAAAZ,CAAA;YAAA;cAI9B,IAAI;gBACF;gBACA;gBACAU,MAAI,CAACrB,OAAO,CAACC,IAAI,UAAAwB,MAAA,CAAUnD,IAAI,CAACK,EAAE,CAAE;cACtC,EAAE,OAAO+C,KAAK,EAAE;gBACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;gBAC9BL,MAAI,CAACF,QAAQ,CAACO,KAAK,CAAC,QAAQ;cAC9B;YAAA;cAAA,OAAAH,SAAA,CAAAZ,CAAA;UAAA;QAAA,GAAAW,QAAA;MAAA;IACF,CAAC;IAEKM,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAAzB,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAwB,SAAA;QAAA,IAAAC,EAAA;QAAA,OAAA1B,YAAA,GAAAG,CAAA,WAAAwB,SAAA;UAAA,kBAAAA,SAAA,CAAAtB,CAAA;YAAA;cAAAsB,SAAA,CAAAC,CAAA;cAEjBJ,MAAI,CAACjE,OAAM,GAAI,IAAG;cAAAoE,SAAA,CAAAtB,CAAA;cAAA,OACZmB,MAAI,CAAC1D,MAAM,CAAC+D,QAAQ,CAAC,iBAAiB;YAAA;cAC5CL,MAAI,CAACV,QAAQ,CAACgB,OAAO,CAAC,SAAS;cAAAH,SAAA,CAAAtB,CAAA;cAAA;YAAA;cAAAsB,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAI,CAAA;cAE/BT,OAAO,CAACD,KAAK,CAAC,WAAW,EAAAK,EAAO;cAChCF,MAAI,CAACV,QAAQ,CAACO,KAAK,CAAC,MAAM;YAAA;cAAAM,SAAA,CAAAC,CAAA;cAE1BJ,MAAI,CAACjE,OAAM,GAAI,KAAI;cAAA,OAAAoE,SAAA,CAAAK,CAAA;YAAA;cAAA,OAAAL,SAAA,CAAArB,CAAA;UAAA;QAAA,GAAAmB,QAAA;MAAA;IAEvB,CAAC;IAED;IACAQ,aAAa,WAAbA,aAAaA,CAACxD,MAAM,EAAE;MACpB,IAAMyD,OAAM,GAAI;QACdC,MAAM,EAAE,aAAa;QACrBC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE,cAAc;QACxBC,MAAM,EAAE;MACV;MACA,OAAOJ,OAAO,CAACzD,MAAM,KAAK,iBAAgB;IAC5C,CAAC;IAED8D,aAAa,WAAbA,aAAaA,CAAC9D,MAAM,EAAE;MACpB,IAAM+D,OAAM,GAAI;QACdL,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,KAAK;QACdC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE;MACV;MACA,OAAOE,OAAO,CAAC/D,MAAM,KAAK,IAAG;IAC/B,CAAC;IAID;IACAgE,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,QAAQ,IAAI,CAACpF,UAAU;QACrB,KAAK,QAAQ;UACX,OAAO,QAAO;QAChB,KAAK,SAAS;UACZ,OAAO,QAAO;QAChB,KAAK,IAAI;UACP,OAAO,UAAS;QAClB,KAAK,WAAW;UACd,OAAO,UAAS;QAClB;UACE,OAAO,MAAK;MAChB;IACF,CAAC;IAEDqF,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,QAAQ,IAAI,CAACrF,UAAU;QACrB,KAAK,QAAQ;UACX,OAAO,aAAY;QACrB,KAAK,SAAS;UACZ,OAAO,YAAW;QACpB,KAAK,IAAI;UACP,OAAO,iBAAgB;QACzB,KAAK,WAAW;UACd,OAAO,gBAAe;QACxB;UACE,OAAO,iBAAgB;MAC3B;IACF,CAAC;IAED;IACAsF,UAAU,WAAVA,UAAUA,CAACC,IAAI,EAAE;MACf,IAAI,CAACA,IAAI,EAAE,OAAO,MAAK;MAEvB,IAAMC,GAAE,GAAI,IAAI1D,IAAI,CAAC;MACrB,IAAM2D,QAAO,GAAI,IAAI3D,IAAI,CAACyD,IAAI;MAC9B,IAAMG,MAAK,GAAIF,GAAE,GAAIC,QAAO;MAC5B,IAAME,QAAO,GAAI1D,IAAI,CAACC,KAAK,CAACwD,MAAK,GAAI,KAAK;MAC1C,IAAME,SAAQ,GAAI3D,IAAI,CAACC,KAAK,CAACwD,MAAK,GAAI,OAAO;MAC7C,IAAMG,QAAO,GAAI5D,IAAI,CAACC,KAAK,CAACwD,MAAK,GAAI,QAAQ;MAE7C,IAAIC,QAAO,GAAI,CAAC,EAAE,OAAO,IAAG;MAC5B,IAAIA,QAAO,GAAI,EAAE,EAAE,UAAA5B,MAAA,CAAU4B,QAAQ;MACrC,IAAIC,SAAQ,GAAI,EAAE,EAAE,UAAA7B,MAAA,CAAU6B,SAAS;MACvC,IAAIC,QAAO,GAAI,CAAC,EAAE,UAAA9B,MAAA,CAAU8B,QAAQ;MAEpC,OAAOJ,QAAQ,CAACK,kBAAkB,CAAC,OAAO;IAC5C;EAGF,CAAC;EAEDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAAC7B,YAAY,CAAC;;IAElB;IACA,IAAI,IAAI,CAAC7B,eAAe,EAAE;MACxB,IAAI,CAAC5B,MAAM,CAAC+D,QAAQ,CAAC,oBAAoB;IAC3C;;IAEA;IACA,IAAMwB,aAAY,GAAI,IAAI,CAACC,WAAW,CAAC,gBAAgB;IACvD,IAAID,aAAa,EAAE;MACjB,IAAI,CAAC/F,QAAO,GAAI+F,aAAY;IAC9B;EACF,CAAC;EAEDE,KAAK,EAAE;IACLjG,QAAQ,WAARA,QAAQA,CAACkG,OAAO,EAAE;MAChB,IAAI,CAACC,WAAW,CAAC,gBAAgB,EAAED,OAAO;IAC5C,CAAC;IAED9D,eAAe,WAAfA,eAAeA,CAACgE,MAAM,EAAE;MACtB,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC5F,MAAM,CAAC+D,QAAQ,CAAC,oBAAoB;MAC3C;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}