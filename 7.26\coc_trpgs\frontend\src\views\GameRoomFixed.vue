<template>
  <div class="game-room" :class="{ 'combat-mode': combatActive }">
    <!-- 房间头部信息 -->
    <div class="room-header">
      <div class="room-info">
        <div class="room-title">
          <i class="fas fa-door-open"></i>
          <h1>{{ roomData.name || '游戏房间' }}</h1>
          <div class="room-status" :class="roomData.status">
            <span class="status-dot"></span>
            <span>{{ getStatusText() }}</span>
          </div>
          <!-- 战斗状态指示器 -->
          <div v-if="combatActive" class="combat-indicator">
            <i class="fas fa-sword"></i>
            <span>战斗进行中</span>
          </div>
        </div>
        <div class="room-meta">
          <span class="room-creator">
            <i class="fas fa-crown"></i>
            KP: {{ roomData.creator?.username || '未知' }}
          </span>
          <span class="room-players">
            <i class="fas fa-users"></i>
            玩家: {{ currentPlayers }}/{{ roomData.maxPlayers || 6 }}
          </span>
          <span v-if="combatActive" class="combat-round">
            <i class="fas fa-clock"></i>
            第{{ combatData?.currentRound || 1 }}轮
          </span>
        </div>
      </div>
      
      <div class="room-controls">
        <!-- KP战斗控制按钮 -->
        <button 
          v-if="isKeeper && !combatActive" 
          @click="startCombat" 
          class="control-btn combat-btn"
          title="开始战斗"
        >
          <i class="fas fa-sword"></i>
        </button>
        <button 
          v-if="isKeeper && combatActive" 
          @click="endCombat" 
          class="control-btn combat-btn active"
          title="结束战斗"
        >
          <i class="fas fa-stop"></i>
        </button>
        <button @click="toggleFullscreen" class="control-btn" title="全屏模式">
          <i class="fas fa-expand"></i>
        </button>
        <button @click="leaveRoom" class="control-btn leave-btn" title="离开房间">
          <i class="fas fa-sign-out-alt"></i>
        </button>
      </div>
    </div>

    <!-- 游戏主体布局 -->
    <div class="game-layout" :class="{ 'combat-layout': combatActive }">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3>{{ combatActive ? '战斗状态' : '角色信息' }}</h3>
        </div>
        
        <div class="panel-content">
          <!-- 非战斗时：角色列表 -->
          <div v-if="!combatActive" class="character-section">
            <div class="character-list">
              <div v-for="player in players" :key="player.id" class="character-card">
                <div class="character-avatar">
                  <i class="fas fa-user-circle"></i>
                </div>
                <div class="character-info">
                  <div class="character-name">{{ player.characterName || player.username }}</div>
                  <div class="character-role" :class="{ 'kp-role': player.isKP }">
                    {{ player.isKP ? 'KP' : 'PL' }}
                  </div>
                  <div class="character-status">
                    <span class="status-indicator" :class="player.status || 'online'"></span>
                    {{ getPlayerStatusText(player.status) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 战斗时：先攻追踪器 -->
          <div v-if="combatActive" class="combat-section">
            <InitiativeTracker
              :initiative-order="combatData?.initiativeOrder || []"
              :current-round="combatData?.currentRound || 1"
              :current-turn="combatData?.currentTurn || 0"
              @next-turn="handleNextTurn"
              @previous-turn="handlePreviousTurn"
            />
          </div>
        </div>
      </div>

      <!-- 中央面板 -->
      <div class="center-panel">
        <!-- 非战斗时：地图场景 -->
        <div v-if="!combatActive" class="scene-content">
          <div class="map-placeholder">
            <i class="fas fa-map"></i>
            <h3>地图系统</h3>
            <p>地图和战斗场景将在这里显示</p>
            <button @click="loadTestMap" class="load-map-btn">
              <i class="fas fa-upload"></i>
              加载测试地图
            </button>
          </div>
        </div>
        
        <!-- 战斗时：2D战场 -->
        <div v-if="combatActive" class="combat-scene">
          <BattlefieldGrid
            :characters="combatData?.participants?.filter(p => p.isPlayer) || []"
            :monsters="combatData?.participants?.filter(p => !p.isPlayer) || []"
            :current-round="combatData?.currentRound || 1"
            :is-keeper="isKeeper"
            @character-move="handleCharacterMove"
            @monster-move="handleMonsterMove"
            @attack="handleAttack"
          />
        </div>
      </div>

      <!-- 右侧面板 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3>{{ combatActive ? '战斗日志' : '聊天' }}</h3>
          <div class="panel-controls">
            <button @click="clearMessages" class="clear-btn" title="清空">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        
        <div class="panel-content">
          <!-- 非战斗时：聊天系统 -->
          <div v-if="!combatActive" class="chat-section">
            <ChatBox
              :messages="messages"
              :current-user="currentUser"
              @send-message="sendMessage"
            />
          </div>
          
          <!-- 战斗时：战斗日志 -->
          <div v-if="combatActive" class="combat-log-section">
            <CombatLog 
              :combat-logs="combatLogs"
              :current-round="combatData?.currentRound || 1"
              @clear-logs="clearCombatLogs"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 浮动组件 -->
    <DiceRoller 
      v-if="showDiceRoller" 
      @close="showDiceRoller = false"
      @roll-result="handleDiceResult"
    />
    
    <!-- 战斗模式组件 -->
    <ForcedCombatMode
      v-if="combatActive && !isKeeper"
      :combat-data="combatData"
      :character="currentPlayerCharacter"
      @action="handlePlayerAction"
    />
    
    <KeeperCombatPanel
      v-if="combatActive && isKeeper"
      :combat-data="combatData"
      :players="players"
      @update-combat="updateCombatData"
      @add-monster="addMonster"
      @update-monster="updateMonster"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChatBox from '@/components/ChatBox.vue'
import DiceRoller from '@/components/DiceRoller.vue'
import CombatLog from '@/components/combat/CombatLog.vue'
import BattlefieldGrid from '@/components/combat/BattlefieldGrid.vue'
import InitiativeTracker from '@/components/combat/InitiativeTracker.vue'
import KeeperCombatPanel from '@/components/combat/KeeperCombatPanel.vue'
import ForcedCombatMode from '@/components/combat/ForcedCombatMode.vue'
import { storageMixin } from '@/mixins/storageMixin'

export default {
  name: 'GameRoomFixed',
  mixins: [storageMixin],
  components: {
    ChatBox,
    DiceRoller,
    CombatLog,
    BattlefieldGrid,
    InitiativeTracker,
    KeeperCombatPanel,
    ForcedCombatMode
  },
  props: {
    roomId: {
      type: String,
      required: true
    }
  },
  
  data() {
    return {
      // 房间数据
      roomData: {
        name: '神秘的古宅',
        status: 'active',
        creator: {
          username: 'KP_Master'
        },
        maxPlayers: 6
      },
      
      // 战斗状态
      combatActive: false,
      combatData: null,
      combatLogs: [],
      
      // 玩家数据
      players: [
        {
          id: 1,
          username: 'KP_Master',
          characterName: 'KP',
          isKP: true,
          status: 'online'
        },
        {
          id: 2,
          username: 'Player1',
          characterName: '侦探约翰',
          isKP: false,
          status: 'online'
        },
        {
          id: 3,
          username: 'Player2',
          characterName: '记者玛丽',
          isKP: false,
          status: 'online'
        }
      ],
      
      // 聊天数据
      messages: [
        {
          id: 1,
          username: 'KP_Master',
          content: '欢迎来到游戏房间！',
          timestamp: new Date()
        },
        {
          id: 2,
          username: 'Player1',
          content: '准备好开始冒险了！',
          timestamp: new Date()
        }
      ],
      
      // 组件状态
      showDiceRoller: false,
      currentPlayerCharacter: null,
      isFullscreen: false
    }
  },
  
  computed: {
    ...mapGetters(['currentUser']),
    
    isKeeper() {
      return this.currentUser?.isKP || false
    },
    
    currentPlayers() {
      return this.players.length
    }
  },
  
  methods: {
    getStatusText() {
      const statusMap = {
        'active': '进行中',
        'waiting': '等待中',
        'paused': '已暂停',
        'ended': '已结束'
      }
      return statusMap[this.roomData.status] || '未知'
    },
    
    getPlayerStatusText(status) {
      const statusMap = {
        'online': '在线',
        'away': '离开',
        'offline': '离线'
      }
      return statusMap[status] || '未知'
    },
    
    async startCombat() {
      try {
        this.combatActive = true
        this.combatData = {
          currentRound: 1,
          currentTurn: 0,
          initiativeOrder: this.generateInitiativeOrder(),
          participants: this.generateCombatParticipants()
        }
        
        this.addCombatLog('system', '战斗开始！')
        
        // 这里可以添加WebSocket通知其他玩家
        // await this.notifyPlayersOfCombatStart()
        
      } catch (error) {
        console.error('开始战斗失败:', error)
        this.$store.dispatch('showNotification', {
          type: 'error',
          message: '开始战斗失败，请重试'
        })
      }
    },
    
    async endCombat() {
      try {
        this.combatActive = false
        this.addCombatLog('system', '战斗结束！')
        
        // 重置战斗数据
        this.combatData = null
        
        // 这里可以添加WebSocket通知其他玩家
        // await this.notifyPlayersOfCombatEnd()
        
      } catch (error) {
        console.error('结束战斗失败:', error)
        this.$store.dispatch('showNotification', {
          type: 'error',
          message: '结束战斗失败，请重试'
        })
      }
    },
    
    generateInitiativeOrder() {
      // 生成先攻顺序的示例数据
      return [
        { id: 1, name: '侦探约翰', initiative: 65, isPlayer: true },
        { id: 2, name: '记者玛丽', initiative: 45, isPlayer: true },
        { id: 3, name: '邪教徒', initiative: 40, isPlayer: false }
      ].sort((a, b) => b.initiative - a.initiative)
    },
    
    generateCombatParticipants() {
      // 生成战斗参与者的示例数据
      return [
        {
          id: 1,
          name: '侦探约翰',
          isPlayer: true,
          position: { x: 2, y: 2 },
          hp: 12,
          maxHp: 12,
          mp: 10,
          maxMp: 10
        },
        {
          id: 2,
          name: '记者玛丽',
          isPlayer: true,
          position: { x: 3, y: 2 },
          hp: 10,
          maxHp: 10,
          mp: 12,
          maxMp: 12
        },
        {
          id: 3,
          name: '邪教徒',
          isPlayer: false,
          position: { x: 8, y: 8 },
          hp: 8,
          maxHp: 8,
          mp: 0,
          maxMp: 0
        }
      ]
    },
    
    addCombatLog(type, message, details = null) {
      this.combatLogs.push({
        id: Date.now() + Math.random(),
        type,
        message,
        details,
        timestamp: new Date(),
        round: this.combatData?.currentRound || 1
      })
    },
    
    handleNextTurn() {
      if (this.combatData) {
        const maxTurn = this.combatData.initiativeOrder.length - 1
        if (this.combatData.currentTurn >= maxTurn) {
          this.combatData.currentTurn = 0
          this.combatData.currentRound++
          this.addCombatLog('system', `第${this.combatData.currentRound}轮开始`)
        } else {
          this.combatData.currentTurn++
        }
        
        const currentActor = this.combatData.initiativeOrder[this.combatData.currentTurn]
        this.addCombatLog('turn', `轮到 ${currentActor.name} 行动`)
      }
    },
    
    handlePreviousTurn() {
      if (this.combatData) {
        if (this.combatData.currentTurn <= 0) {
          if (this.combatData.currentRound > 1) {
            this.combatData.currentRound--
            this.combatData.currentTurn = this.combatData.initiativeOrder.length - 1
          }
        } else {
          this.combatData.currentTurn--
        }
      }
    },
    
    handleCharacterMove(characterId, newPosition) {
      const participant = this.combatData?.participants?.find(p => p.id === characterId)
      if (participant) {
        participant.position = newPosition
        this.addCombatLog('move', `${participant.name} 移动到 (${newPosition.x}, ${newPosition.y})`)
      }
    },
    
    handleMonsterMove(monsterId, newPosition) {
      const monster = this.combatData?.participants?.find(p => p.id === monsterId && !p.isPlayer)
      if (monster) {
        monster.position = newPosition
        this.addCombatLog('move', `${monster.name} 移动到 (${newPosition.x}, ${newPosition.y})`)
      }
    },
    
    handleAttack(attackData) {
      this.addCombatLog('attack', `${attackData.attacker} 攻击 ${attackData.target}`, attackData)
    },
    
    handlePlayerAction(action) {
      this.addCombatLog('action', `玩家执行: ${action.type}`, action)
    },
    
    updateCombatData(newData) {
      this.combatData = { ...this.combatData, ...newData }
    },
    
    addMonster(monsterData) {
      if (this.combatData?.participants) {
        this.combatData.participants.push(monsterData)
        this.addCombatLog('system', `${monsterData.name} 加入战斗`)
      }
    },
    
    updateMonster(monsterId, updates) {
      const monster = this.combatData?.participants?.find(p => p.id === monsterId && !p.isPlayer)
      if (monster) {
        Object.assign(monster, updates)
        this.addCombatLog('system', `${monster.name} 状态更新`)
      }
    },
    
    sendMessage(content) {
      const message = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名',
        content,
        timestamp: new Date()
      }
      this.messages.push(message)
      
      // 这里可以添加WebSocket发送消息
      // await this.sendMessageToRoom(message)
    },
    
    handleDiceResult(result) {
      const message = `🎲 ${this.currentUser?.username || '匿名'} 投掷了 ${result.dice}: ${result.results.join(', ')} (总计: ${result.total})`
      this.sendMessage(message)
    },
    
    clearMessages() {
      if (confirm('确定要清空聊天记录吗？')) {
        this.messages = []
      }
    },
    
    clearCombatLogs() {
      if (confirm('确定要清空战斗日志吗？')) {
        this.combatLogs = []
      }
    },
    
    loadTestMap() {
      this.$store.dispatch('showNotification', {
        type: 'info',
        message: '测试地图加载功能暂未实现'
      })
    },
    
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      if (this.isFullscreen) {
        document.documentElement.requestFullscreen?.()
      } else {
        document.exitFullscreen?.()
      }
    },
    
    leaveRoom() {
      if (confirm('确定要离开房间吗？')) {
        this.$router.push('/')
      }
    },

    loadRoomData() {
      // 加载房间数据的方法
      // 这里可以添加从API获取房间数据的逻辑
      console.log('加载房间数据:', this.roomId)
    }
  },
  
  mounted() {
    // 初始化房间数据
    this.loadRoomData()
    
    // 设置WebSocket连接
    // this.setupWebSocket()
  },
  
  beforeUnmount() {
    // 清理WebSocket连接
    // this.cleanupWebSocket()
  }
}
</script>

<style scoped lang="css">
/* ===== 游戏房间基础样式 ===== */
.game-room {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  transition: all 0.3s ease;
}

.game-room.combat-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* ===== 房间头部 ===== */
.room-header {
  background: linear-gradient(135deg, #22c55e 0%, #16a085 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.game-room.combat-mode .room-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.room-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.room-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.room-title i {
  font-size: 24px;
}

.room-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.room-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  opacity: 0.9;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
}

.room-status.waiting .status-dot {
  background: #f59e0b;
}

.room-status.paused .status-dot {
  background: #6b7280;
}

.combat-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  animation: combat-pulse 2s infinite;
}

@keyframes combat-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.room-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  opacity: 0.9;
}

.room-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.room-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  /* 布局属性 */
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.combat-btn.active {
  background: rgba(239, 68, 68, 0.8);
  animation: combat-btn-pulse 1.5s infinite;
}

@keyframes combat-btn-pulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0); }
}

.leave-btn:hover {
  background: rgba(239, 68, 68, 0.8);
}

/* ===== 游戏布局 ===== */
.game-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
  transition: all 0.3s ease;
}

.game-layout.combat-layout {
  background: rgba(17, 24, 39, 0.1);
}

/* 左右面板基础样式 */
.left-panel {
  width: 320px;
  background: white;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.right-panel {
  width: 320px;
  background: white;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.game-room.combat-mode .left-panel,
.game-room.combat-mode .right-panel {
  background: rgba(31, 41, 55, 0.95);
  border-color: #4b5563;
  color: #f3f4f6;
}

.center-panel {
  flex: 1;
  background: white;
  position: relative;
  transition: all 0.3s ease;
}

.game-room.combat-mode .center-panel {
  background: rgba(17, 24, 39, 0.9);
}

.panel-header {
  background: #22c55e;
  color: white;
  padding: 16px 20px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.game-room.combat-mode .panel-header {
  background: #dc2626;
}

.panel-controls {
  display: flex;
  gap: 8px;
}

.clear-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* ===== 角色列表 ===== */
.character-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.character-card {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.character-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.game-room.combat-mode .character-card {
  background: rgba(55, 65, 81, 0.8);
  border-color: #6b7280;
}

.character-avatar {
  font-size: 32px;
  color: #6b7280;
}

.character-info {
  flex: 1;
}

.character-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.character-role {
  font-size: 12px;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 12px;
  background: #e5e7eb;
  display: inline-block;
  margin-bottom: 4px;
}

.character-role.kp-role {
  background: #fef3c7;
  color: #92400e;
}

.character-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #10b981;
}

.status-indicator.away {
  background: #f59e0b;
}

.status-indicator.offline {
  background: #6b7280;
}

/* ===== 地图占位符 ===== */
.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  text-align: center;
  padding: 40px;
}

.map-placeholder i {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.map-placeholder h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
}

.map-placeholder p {
  margin: 0 0 24px 0;
  font-size: 16px;
  opacity: 0.8;
}

.load-map-btn {
  background: #22c55e;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.load-map-btn:hover {
  background: #16a085;
  transform: translateY(-1px);
}

/* ===== 战斗场景 ===== */
.combat-scene {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1f2937;
  border-radius: 8px;
  margin: 8px;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .game-layout {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .room-header {
    padding: 12px 16px;
  }
  
  .room-title h1 {
    font-size: 20px;
  }
  
  .room-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .room-controls {
    flex-direction: column;
  }
}

/* ===== 动画效果 ===== */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
</style>