/**
 * COC 7版怪物和NPC管理系统测试
 * 验证怪物AI、战斗逻辑和特殊能力
 */

import MonsterSystem from './monsterSystem.js'

console.log('=== COC 7版怪物和NPC管理系统测试 ===\n')

// 测试1: 怪物创建和基础属性
console.log('=== 测试1: 怪物创建和基础属性 ===')

const civilian = MonsterSystem.createMonster('civilian')
console.log('创建平民NPC:')
console.log(`  名称: ${civilian.name}`)
console.log(`  类型: ${civilian.type}`)
console.log(`  生命值: ${civilian.currentHP}/${civilian.hitPoints}`)
console.log(`  力量: ${civilian.attributes.strength}`)
console.log(`  敏捷: ${civilian.attributes.dexterity}`)
console.log(`  AI类型: ${civilian.aiType}`)
console.log(`  士气: ${civilian.morale}`)

const deepOne = MonsterSystem.createMonster('deep_one')
console.log(`\n创建深潜者:`)
console.log(`  名称: ${deepOne.name}`)
console.log(`  类型: ${deepOne.type}`)
console.log(`  生命值: ${deepOne.currentHP}/${deepOne.hitPoints}`)
console.log(`  力量: ${deepOne.attributes.strength}`)
console.log(`  护甲: ${deepOne.armor}`)
console.log(`  特殊能力: ${deepOne.specialAbilities.map(a => a.name).join(', ')}`)
console.log(`  理智损失: ${deepOne.sanityLoss.first}`)

// 测试2: 怪物分类查询
console.log('\n\n=== 测试2: 怪物分类查询 ===')

const humans = MonsterSystem.getMonstersByCategory('humans')
console.log(`人类NPC (${humans.length}种):`)
humans.forEach(monster => {
  console.log(`  - ${monster.name}: ${monster.aiType}, 士气${monster.morale}`)
})

const mythos = MonsterSystem.getMonstersByCategory('mythos')
console.log(`\n神话生物 (${mythos.length}种):`)
mythos.forEach(monster => {
  console.log(`  - ${monster.name}: ${monster.hitPoints}HP, 护甲${monster.armor}`)
})

// 测试3: AI决策系统
console.log('\n\n=== 测试3: AI决策系统 ===')

// 创建测试场景
const police = MonsterSystem.createMonster('police_officer')
police.position = { x: 0, y: 0 }

const gangster = MonsterSystem.createMonster('gangster')
gangster.position = { x: 5, y: 0 }

const targets = [gangster]
const battlefield = { width: 20, height: 20 }

console.log('警察面对歹徒的AI决策:')
const policeDecision = MonsterSystem.makeAIDecision(police, targets, battlefield)
console.log(`  行动: ${policeDecision.action}`)
console.log(`  目标: ${policeDecision.target?.name || '无'}`)
console.log(`  优先级: ${policeDecision.priority}`)
console.log(`  描述: ${policeDecision.description}`)

console.log(`\n歹徒面对警察的AI决策:`)
const gangsterTargets = [police]
const gangsterDecision = MonsterSystem.makeAIDecision(gangster, gangsterTargets, battlefield)
console.log(`  行动: ${gangsterDecision.action}`)
console.log(`  目标: ${gangsterDecision.target?.name || '无'}`)
console.log(`  攻击方式: ${gangsterDecision.attack?.name || '无'}`)
console.log(`  描述: ${gangsterDecision.description}`)

// 测试4: 战斗执行
console.log('\n\n=== 测试4: 战斗执行 ===')

console.log('执行歹徒的攻击行动:')
const attackResult = MonsterSystem.executeMonsterAction(gangster, gangsterDecision, gangsterTargets)
console.log(`  成功: ${attackResult.success}`)
console.log(`  攻击者: ${attackResult.attacker}`)
console.log(`  目标: ${attackResult.target}`)
console.log(`  投掷: ${attackResult.roll} vs ${attackResult.skillValue}`)
console.log(`  成功等级: ${attackResult.successLevel}`)
if (attackResult.success) {
  console.log(`  伤害: ${attackResult.damage}`)
  console.log(`  目标剩余HP: ${attackResult.targetHP}`)
}
console.log(`  描述: ${attackResult.description}`)

// 测试5: 不同AI类型的行为
console.log('\n\n=== 测试5: 不同AI类型的行为 ===')

const testMonsters = [
  { monster: MonsterSystem.createMonster('civilian'), name: '平民' },
  { monster: MonsterSystem.createMonster('police_officer'), name: '警察' },
  { monster: MonsterSystem.createMonster('wolf'), name: '狼' },
  { monster: MonsterSystem.createMonster('deep_one'), name: '深潜者' }
]

testMonsters.forEach(({ monster, name }) => {
  monster.position = { x: Math.random() * 10, y: Math.random() * 10 }
  const aiType = MonsterSystem.aiTypes[monster.aiType]
  console.log(`${name} (${monster.aiType}):`)
  console.log(`  攻击性: ${aiType.aggressiveness}`)
  console.log(`  逃跑阈值: ${aiType.fleeThreshold}`)
  
  // 模拟受伤状态下的决策
  monster.currentHP = Math.floor(monster.hitPoints * 0.3) // 重伤状态
  const decision = MonsterSystem.makeAIDecision(monster, targets, battlefield)
  console.log(`  重伤时行动: ${decision.action}`)
  console.log(`  描述: ${decision.description}`)
  console.log()
})

// 测试6: 特殊能力系统
console.log('=== 测试6: 特殊能力系统 ===')

const shoggoth = MonsterSystem.createMonster('shoggoth')
console.log(`修格斯的特殊能力:`)
shoggoth.specialAbilities.forEach(ability => {
  console.log(`  - ${ability.name}: ${ability.description}`)
})

// 测试再生能力
shoggoth.currentHP = shoggoth.hitPoints - 5 // 受伤状态
console.log(`\n修格斯受伤状态: ${shoggoth.currentHP}/${shoggoth.hitPoints}`)

const regenResult = MonsterSystem.applySpecialAbility(shoggoth, '再生')
if (regenResult) {
  console.log(`再生效果: ${regenResult.description}`)
  console.log(`恢复后生命值: ${shoggoth.currentHP}/${shoggoth.hitPoints}`)
}

// 测试恐怖形象
const investigator = {
  name: '调查员',
  currentSAN: 65,
  hasSeenBefore: false
}

const terrorResult = MonsterSystem.applySpecialAbility(deepOne, '恐怖形象', { target: investigator })
if (terrorResult) {
  console.log(`\n恐怖形象效果: ${terrorResult.description}`)
  console.log(`理智损失: ${terrorResult.sanityLoss}`)
}

// 测试7: 威胁等级计算
console.log('\n\n=== 测试7: 威胁等级计算 ===')

const testTargets = [
  { name: '平民', strength: 50, dexterity: 50, constitution: 50, skills: { fighting_brawl: 25 } },
  { name: '警察', strength: 65, dexterity: 60, constitution: 60, skills: { fighting_brawl: 55, firearms_handgun: 60 } },
  { name: '老兵', strength: 70, dexterity: 65, constitution: 70, skills: { fighting_brawl: 70, firearms_rifle: 75 }, weapons: ['rifle', 'knife'] }
]

testTargets.forEach(target => {
  const threatLevel = MonsterSystem.calculateThreatLevel(target)
  console.log(`${target.name}的威胁等级: ${Math.round(threatLevel)}/100`)
})

// 测试8: 距离计算和移动
console.log('\n\n=== 测试8: 距离计算和移动 ===')

const zombie = MonsterSystem.createMonster('zombie')
zombie.position = { x: 0, y: 0 }

const survivor = {
  name: '幸存者',
  position: { x: 10, y: 10 },
  currentHP: 12
}

const distance = MonsterSystem.calculateDistance(zombie.position, survivor.position)
console.log(`僵尸到幸存者的距离: ${Math.round(distance)}米`)

// 执行移动
const moveDecision = {
  action: 'move',
  target: survivor
}

console.log(`僵尸移动前位置: (${zombie.position.x}, ${zombie.position.y})`)
const moveResult = MonsterSystem.executeMonsterAction(zombie, moveDecision, [survivor])
console.log(`移动结果: ${moveResult.description}`)
console.log(`僵尸移动后位置: (${Math.round(zombie.position.x)}, ${Math.round(zombie.position.y)})`)

const newDistance = MonsterSystem.calculateDistance(zombie.position, survivor.position)
console.log(`移动后距离: ${Math.round(newDistance)}米`)

// 测试9: 怪物状态检查
console.log('\n\n=== 测试9: 怪物状态检查 ===')

const testSubjects = [
  { monster: MonsterSystem.createMonster('civilian'), damage: 0 },
  { monster: MonsterSystem.createMonster('police_officer'), damage: 5 },
  { monster: MonsterSystem.createMonster('deep_one'), damage: 10 },
  { monster: MonsterSystem.createMonster('shoggoth'), damage: 25 }
]

testSubjects.forEach(({ monster, damage }) => {
  monster.currentHP = Math.max(0, monster.currentHP - damage)
  const status = MonsterSystem.checkMonsterStatus(monster)
  
  console.log(`${monster.name}:`)
  console.log(`  生命值: ${monster.currentHP}/${monster.hitPoints}`)
  console.log(`  状态: ${status.status}`)
  console.log(`  生命百分比: ${Math.round(status.healthPercentage * 100)}%`)
  console.log(`  存活: ${status.isAlive}`)
  console.log(`  可行动: ${status.canAct}`)
  console.log()
})

// 测试10: 复杂战斗场景
console.log('=== 测试10: 复杂战斗场景 ===')

// 创建一个小队 vs 怪物的场景
const squad = [
  MonsterSystem.createMonster('police_officer'),
  MonsterSystem.createMonster('police_officer')
]

const monsters = [
  MonsterSystem.createMonster('deep_one'),
  MonsterSystem.createMonster('zombie')
]

// 设置位置
squad[0].position = { x: 0, y: 0 }
squad[1].position = { x: 2, y: 0 }
monsters[0].position = { x: 8, y: 0 }
monsters[1].position = { x: 10, y: 2 }

console.log('战斗场景: 2名警察 vs 1个深潜者 + 1个僵尸')
console.log('\n初始位置:')
console.log(`  警察1: (${squad[0].position.x}, ${squad[0].position.y})`)
console.log(`  警察2: (${squad[1].position.x}, ${squad[1].position.y})`)
console.log(`  深潜者: (${monsters[0].position.x}, ${monsters[0].position.y})`)
console.log(`  僵尸: (${monsters[1].position.x}, ${monsters[1].position.y})`)

// 模拟一轮战斗
console.log('\n第1轮战斗:')

// 深潜者的行动
const deepOneDecision = MonsterSystem.makeAIDecision(monsters[0], squad, battlefield)
console.log(`深潜者决策: ${deepOneDecision.description}`)

const deepOneAction = MonsterSystem.executeMonsterAction(monsters[0], deepOneDecision, squad)
console.log(`深潜者行动: ${deepOneAction.description}`)

// 僵尸的行动
const zombieDecision = MonsterSystem.makeAIDecision(monsters[1], squad, battlefield)
console.log(`僵尸决策: ${zombieDecision.description}`)

const zombieAction = MonsterSystem.executeMonsterAction(monsters[1], zombieDecision, squad)
console.log(`僵尸行动: ${zombieAction.description}`)

// 测试11: AI类型统计
console.log('\n\n=== 测试11: AI类型统计 ===')

console.log('可用的AI类型:')
Object.entries(MonsterSystem.aiTypes).forEach(([key, aiType]) => {
  console.log(`${aiType.name} (${key}):`)
  console.log(`  描述: ${aiType.description}`)
  console.log(`  攻击性: ${aiType.aggressiveness}`)
  console.log(`  逃跑阈值: ${aiType.fleeThreshold}`)
  console.log()
})

// 测试12: 怪物数据库完整性
console.log('=== 测试12: 怪物数据库完整性 ===')

let totalMonsters = 0
Object.entries(MonsterSystem.monsterDatabase).forEach(([category, monsters]) => {
  const count = Object.keys(monsters).length
  totalMonsters += count
  console.log(`${category}: ${count} 种怪物`)
  
  Object.values(monsters).forEach(monster => {
    console.log(`  - ${monster.name}: ${monster.type}, ${monster.hitPoints}HP`)
  })
  console.log()
})

console.log(`总计: ${totalMonsters} 种怪物/NPC`)

console.log('\n=== 怪物和NPC管理系统测试完成 ===')
console.log('所有怪物AI、战斗逻辑和特殊能力已验证！')