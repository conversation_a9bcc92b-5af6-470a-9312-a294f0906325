{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport { createRouter, createWebHistory } from 'vue-router';\nimport Home from '@/views/Home.vue';\nimport Login from '@/views/Login.vue';\nimport Register from '@/views/Register.vue';\nimport CharacterManager from '@/views/CharacterManager.vue';\nimport Room from '@/views/Room.vue';\nimport CharacterCreator from '@/views/ListenerCardCreator.vue';\nimport CharacterGrowth from '@/views/CharacterGrowth.vue';\nimport CreateRoom from '@/views/CreateRoom.vue';\nimport store from '@/store';\nvar routes = [{\n  path: '/',\n  name: 'Home',\n  component: Home\n}, {\n  path: '/login',\n  name: 'Login',\n  component: Login,\n  meta: {\n    guestOnly: true\n  }\n}, {\n  path: '/register',\n  name: 'Register',\n  component: Register,\n  meta: {\n    guestOnly: true\n  }\n}, {\n  path: '/characters',\n  name: 'CharacterManager',\n  component: CharacterManager,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/create-character',\n  name: 'CharacterCreator',\n  component: CharacterCreator,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/create-listener-card',\n  redirect: '/create-character'\n}, {\n  path: '/character-growth',\n  name: 'CharacterGrowth',\n  component: CharacterGrowth,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/create-room',\n  name: 'CreateRoom',\n  component: CreateRoom,\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/room/:id',\n  name: 'Room',\n  component: function component() {\n    return import('@/views/GameRoomCombatIntegrated.vue');\n  },\n  meta: {\n    requiresAuth: true\n  },\n  props: function props(route) {\n    return {\n      roomId: route.params.id\n    };\n  }\n}, {\n  path: '/profile',\n  name: 'Profile',\n  component: function component() {\n    return import('@/views/Profile.vue');\n  },\n  meta: {\n    requiresAuth: true\n  }\n}, {\n  path: '/settings',\n  name: 'Settings',\n  component: function component() {\n    return import('@/views/Settings.vue');\n  },\n  meta: {\n    requiresAuth: true\n  }\n}];\nvar router = createRouter({\n  history: createWebHistory(process.env.BASE_URL),\n  routes: routes\n});\nrouter.beforeEach(function (to, from, next) {\n  var isAuthenticated = store.getters.isAuthenticated;\n  if (to.meta.requiresAuth && !isAuthenticated) {\n    next('/login');\n  } else if (to.meta.guestOnly && isAuthenticated) {\n    next('/');\n  } else {\n    next();\n  }\n});\nexport default router;", "map": {"version": 3, "names": ["createRouter", "createWebHistory", "Home", "<PERSON><PERSON>", "Register", "<PERSON><PERSON><PERSON><PERSON>", "Room", "CharacterCreator", "<PERSON><PERSON><PERSON><PERSON>", "CreateRoom", "store", "routes", "path", "name", "component", "meta", "guestOn<PERSON>", "requiresAuth", "redirect", "props", "route", "roomId", "params", "id", "router", "history", "process", "env", "BASE_URL", "beforeEach", "to", "from", "next", "isAuthenticated", "getters"], "sources": ["C:/Users/<USER>/Desktop/最新的 - 副本/7.26/coc_trpgs/frontend/src/router/index.js"], "sourcesContent": ["import { createRouter, createWebHistory } from 'vue-router'\r\nimport Home from '@/views/Home.vue'\r\nimport Login from '@/views/Login.vue'\r\nimport Register from '@/views/Register.vue'\r\nimport CharacterManager from '@/views/CharacterManager.vue'\r\nimport Room from '@/views/Room.vue'\r\nimport CharacterCreator from '@/views/ListenerCardCreator.vue'\r\nimport CharacterGrowth from '@/views/CharacterGrowth.vue'\r\nimport CreateRoom from '@/views/CreateRoom.vue'\r\nimport store from '@/store'\r\n\r\n\r\n\r\n\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'Home',\r\n    component: Home\r\n  },\r\n  {\r\n    path: '/login',\r\n    name: 'Login',\r\n    component: Login,\r\n    meta: { guestOnly: true }\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'Register',\r\n    component: Register,\r\n    meta: { guestOnly: true }\r\n  },\r\n  {\r\n    path: '/characters',\r\n    name: 'CharacterManager',\r\n    component: CharacterManager,\r\n    meta: { requiresAuth: true }\r\n  },\r\n  {\r\n    path: '/create-character',\r\n    name: 'CharacterCreator',\r\n    component: CharacterCreator,\r\n    meta: { requiresAuth: true }\r\n  },\r\n  {\r\n    path: '/create-listener-card',\r\n    redirect: '/create-character'\r\n  },\r\n  {\r\n    path: '/character-growth',\r\n    name: 'CharacterGrowth',\r\n    component: CharacterGrowth,\r\n    meta: { requiresAuth: true }\r\n  },\r\n\r\n  {\r\n    path: '/create-room',\r\n    name: 'CreateRoom',\r\n    component: CreateRoom,\r\n    meta: { requiresAuth: true }\r\n  },\r\n  {\r\n    path: '/room/:id',\r\n    name: 'Room',\r\n    component: () => import('@/views/GameRoomCombatIntegrated.vue'),\r\n    meta: { requiresAuth: true },\r\n    props: route => ({ roomId: route.params.id })\r\n  },\r\n\r\n  {\r\n    path: '/profile',\r\n    name: 'Profile',\r\n    component: () => import('@/views/Profile.vue'),\r\n    meta: { requiresAuth: true }\r\n  },\r\n  {\r\n    path: '/settings',\r\n    name: 'Settings',\r\n    component: () => import('@/views/Settings.vue'),\r\n    meta: { requiresAuth: true }\r\n  },\r\n\r\n]\r\n\r\nconst router = createRouter({\r\n  history: createWebHistory(process.env.BASE_URL),\r\n  routes\r\n})\r\n\r\nrouter.beforeEach((to, from, next) => {\r\n  const isAuthenticated = store.getters.isAuthenticated\r\n  \r\n\r\n  if (to.meta.requiresAuth && !isAuthenticated) {\r\n    next('/login')\r\n  } else if (to.meta.guestOnly && isAuthenticated) {\r\n    next('/')\r\n  } else {\r\n    next()\r\n  }\r\n})\r\n\r\nexport default router "], "mappings": ";;;AAAA,SAASA,YAAY,EAAEC,gBAAgB,QAAQ,YAAY;AAC3D,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,KAAK,MAAM,SAAS;AAK3B,IAAMC,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEZ;AACb,CAAC,EACD;EACEU,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEX,KAAK;EAChBY,IAAI,EAAE;IAAEC,SAAS,EAAE;EAAK;AAC1B,CAAC,EACD;EACEJ,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEV,QAAQ;EACnBW,IAAI,EAAE;IAAEC,SAAS,EAAE;EAAK;AAC1B,CAAC,EACD;EACEJ,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAET,gBAAgB;EAC3BU,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,kBAAkB;EACxBC,SAAS,EAAEP,gBAAgB;EAC3BQ,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,uBAAuB;EAC7BM,QAAQ,EAAE;AACZ,CAAC,EACD;EACEN,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAEN,eAAe;EAC1BO,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK;AAC7B,CAAC,EAED;EACEL,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEL,UAAU;EACrBM,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAQ,MAAM,CAAC,sCAAsC,CAAC;EAAA;EAC/DC,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK,CAAC;EAC5BE,KAAK,EAAE,SAAPA,KAAKA,CAAEC,KAAK;IAAA,OAAK;MAAEC,MAAM,EAAED,KAAK,CAACE,MAAM,CAACC;IAAG,CAAC;EAAA;AAC9C,CAAC,EAED;EACEX,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAQ,MAAM,CAAC,qBAAqB,CAAC;EAAA;EAC9CC,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK;AAC7B,CAAC,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAE,SAAXA,SAASA,CAAA;IAAA,OAAQ,MAAM,CAAC,sBAAsB,CAAC;EAAA;EAC/CC,IAAI,EAAE;IAAEE,YAAY,EAAE;EAAK;AAC7B,CAAC,CAEF;AAED,IAAMO,MAAM,GAAGxB,YAAY,CAAC;EAC1ByB,OAAO,EAAExB,gBAAgB,CAACyB,OAAO,CAACC,GAAG,CAACC,QAAQ,CAAC;EAC/CjB,MAAM,EAANA;AACF,CAAC,CAAC;AAEFa,MAAM,CAACK,UAAU,CAAC,UAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAK;EACpC,IAAMC,eAAe,GAAGvB,KAAK,CAACwB,OAAO,CAACD,eAAe;EAGrD,IAAIH,EAAE,CAACf,IAAI,CAACE,YAAY,IAAI,CAACgB,eAAe,EAAE;IAC5CD,IAAI,CAAC,QAAQ,CAAC;EAChB,CAAC,MAAM,IAAIF,EAAE,CAACf,IAAI,CAACC,SAAS,IAAIiB,eAAe,EAAE;IAC/CD,IAAI,CAAC,GAAG,CAAC;EACX,CAAC,MAAM;IACLA,IAAI,CAAC,CAAC;EACR;AACF,CAAC,CAAC;AAEF,eAAeR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}