/**
 * 批量修复剩余组件中的localStorage使用
 * 这个脚本会为需要的组件添加storageMixin并替换localStorage调用
 */

const fs = require('fs');
const path = require('path');

// 需要修复的组件列表
const componentsToFix = [
  'src/components/PrivateChatManager.vue',
  'src/components/PrivateChat.vue',
  'src/components/MapSystem.vue',
  'src/components/GroupChat.vue',
  'src/components/GameSaveManager.vue'
];

// 安全存储访问的替换规则
const replacements = [
  {
    from: /localStorage\.getItem\(/g,
    to: 'this.safeGetItem('
  },
  {
    from: /localStorage\.setItem\(/g,
    to: 'this.safeSetItem('
  },
  {
    from: /localStorage\.removeItem\(/g,
    to: 'this.safeRemoveItem('
  },
  {
    from: /localStorage\.key\(/g,
    to: 'this.safeGetKey('
  },
  {
    from: /localStorage\.length/g,
    to: 'this.safeGetLength()'
  }
];

// 为组件添加storageMixin的函数
function addStorageMixin(content) {
  // 检查是否已经有storageMixin
  if (content.includes('storageMixin')) {
    return content;
  }

  // 查找import语句的位置
  const importMatch = content.match(/import.*from.*['"][^'"]*['"];?\s*\n/g);
  if (importMatch) {
    const lastImport = importMatch[importMatch.length - 1];
    const importIndex = content.lastIndexOf(lastImport) + lastImport.length;
    
    // 添加storageMixin import
    const mixinImport = "import { storageMixin } from '@/mixins/storageMixin';\n";
    content = content.slice(0, importIndex) + mixinImport + content.slice(importIndex);
  }

  // 查找export default并添加mixins
  const exportMatch = content.match(/export default \{[\s\S]*?name: ['"][^'"]*['"],/);
  if (exportMatch) {
    const nameMatch = exportMatch[0];
    const nameIndex = content.indexOf(nameMatch) + nameMatch.length;
    
    // 检查是否已经有mixins
    if (!content.includes('mixins:')) {
      const mixinsLine = '\n  mixins: [storageMixin],';
      content = content.slice(0, nameIndex) + mixinsLine + content.slice(nameIndex);
    }
  }

  return content;
}

// 添加安全存储访问方法
function addSafeStorageMethods(content) {
  // 检查是否已经有这些方法
  if (content.includes('safeGetKey') || content.includes('safeGetLength')) {
    return content;
  }

  // 查找methods部分
  const methodsMatch = content.match(/methods:\s*\{/);
  if (methodsMatch) {
    const methodsIndex = content.indexOf(methodsMatch[0]) + methodsMatch[0].length;
    
    const additionalMethods = `
    // 额外的安全存储访问方法
    safeGetKey(index) {
      try {
        if (window.storageManager && window.storageManager.key) {
          return window.storageManager.key(index);
        }
        return localStorage.key(index);
      } catch (error) {
        console.warn(\`[Storage] 无法获取存储键 \${index}:\`, error.message);
        return null;
      }
    },
    
    safeGetLength() {
      try {
        if (window.storageManager && window.storageManager.length !== undefined) {
          return window.storageManager.length;
        }
        return localStorage.length;
      } catch (error) {
        console.warn('[Storage] 无法获取存储长度:', error.message);
        return 0;
      }
    },`;
    
    content = content.slice(0, methodsIndex) + additionalMethods + content.slice(methodsIndex);
  }

  return content;
}

// 处理单个文件
function fixFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  
  // 应用替换规则
  replacements.forEach(rule => {
    content = content.replace(rule.from, rule.to);
  });
  
  // 添加storageMixin
  content = addStorageMixin(content);
  
  // 添加额外的安全存储方法（如果需要）
  if (content.includes('localStorage.key') || content.includes('localStorage.length')) {
    content = addSafeStorageMethods(content);
  }
  
  // 写回文件
  fs.writeFileSync(fullPath, content, 'utf8');
  console.log(`已修复: ${filePath}`);
}

// 批量处理文件
console.log('开始批量修复组件...');
componentsToFix.forEach(fixFile);
console.log('批量修复完成！');