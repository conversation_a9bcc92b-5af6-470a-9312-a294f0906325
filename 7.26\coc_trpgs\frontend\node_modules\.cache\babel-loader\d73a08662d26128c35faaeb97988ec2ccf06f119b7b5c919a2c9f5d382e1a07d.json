{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/web.timers.js\";\nexport default {\n  name: 'CombatAnimation',\n  props: {\n    animation: {\n      type: Object,\n      required: true\n    },\n    gridSize: {\n      type: Number,\n      \"default\": 40\n    },\n    zoom: {\n      type: Number,\n      \"default\": 1\n    }\n  },\n  data: function data() {\n    return {\n      startTime: Date.now()\n    };\n  },\n  computed: {\n    duration: function duration() {\n      return this.animation.duration || 1000;\n    },\n    fromX: function fromX() {\n      var _this$animation$from;\n      return (((_this$animation$from = this.animation.from) === null || _this$animation$from === void 0 ? void 0 : _this$animation$from.x) || 0) * this.gridSize * this.zoom;\n    },\n    fromY: function fromY() {\n      var _this$animation$from2;\n      return (((_this$animation$from2 = this.animation.from) === null || _this$animation$from2 === void 0 ? void 0 : _this$animation$from2.y) || 0) * this.gridSize * this.zoom;\n    },\n    toX: function toX() {\n      var _this$animation$to;\n      return (((_this$animation$to = this.animation.to) === null || _this$animation$to === void 0 ? void 0 : _this$animation$to.x) || 0) * this.gridSize * this.zoom;\n    },\n    toY: function toY() {\n      var _this$animation$to2;\n      return (((_this$animation$to2 = this.animation.to) === null || _this$animation$to2 === void 0 ? void 0 : _this$animation$to2.y) || 0) * this.gridSize * this.zoom;\n    }\n  },\n  mounted: function mounted() {\n    var _this = this;\n    // 动画完成后触发回调\n    setTimeout(function () {\n      _this.$emit('complete', _this.animation.id);\n    }, this.duration);\n  },\n  methods: {\n    /**\r\n     * 获取近战攻击路径\r\n     */\n    getMeleeAttackPath: function getMeleeAttackPath() {\n      var midX = (this.fromX + this.toX) / 2;\n      var midY = (this.fromY + this.toY) / 2;\n\n      // 创建弧形攻击轨迹\n      return \"M \".concat(this.fromX, \" \").concat(this.fromY, \" Q \").concat(midX + 20, \" \").concat(midY - 20, \" \").concat(this.toX, \" \").concat(this.toY);\n    },\n    /**\r\n     * 获取弹道路径\r\n     */\n    getProjectilePath: function getProjectilePath() {\n      var midX = (this.fromX + this.toX) / 2;\n      var midY = (this.fromY + this.toY) / 2 - 30; // 抛物线效果\n\n      return \"M \".concat(this.fromX, \" \").concat(this.fromY, \" Q \").concat(midX, \" \").concat(midY, \" \").concat(this.toX, \" \").concat(this.toY);\n    },\n    /**\r\n     * 获取法术路径\r\n     */\n    getSpellPath: function getSpellPath() {\n      // 法术通常是直线飞行\n      return \"M \".concat(this.fromX, \" \").concat(this.fromY, \" L \").concat(this.toX, \" \").concat(this.toY);\n    },\n    /**\r\n     * 获取粒子路径\r\n     */\n    getParticlePath: function getParticlePath(index) {\n      var angle = index * Math.PI * 2 / 12;\n      var offsetX = Math.cos(angle) * 10;\n      var offsetY = Math.sin(angle) * 10;\n      return \"M \".concat(this.fromX + offsetX, \" \").concat(this.fromY + offsetY, \" L \").concat(this.toX + offsetX, \" \").concat(this.toY + offsetY);\n    },\n    /**\r\n     * 获取移动路径\r\n     */\n    getMovementPath: function getMovementPath() {\n      return \"M \".concat(this.fromX, \" \").concat(this.fromY, \" L \").concat(this.toX, \" \").concat(this.toY);\n    },\n    /**\r\n     * 获取路径长度\r\n     */\n    getPathLength: function getPathLength() {\n      var dx = this.toX - this.fromX;\n      var dy = this.toY - this.fromY;\n      return Math.sqrt(dx * dx + dy * dy);\n    },\n    /**\r\n     * 获取弹道颜色\r\n     */\n    getProjectileColor: function getProjectileColor() {\n      var weapon = this.animation.weapon;\n      if (!weapon) return '#ffaa00';\n      var colorMap = {\n        'arrow': '#8d6e63',\n        'bullet': '#607d8b',\n        'bolt': '#795548',\n        'stone': '#9e9e9e',\n        'dart': '#546e7a'\n      };\n      return colorMap[weapon.projectileType] || '#ffaa00';\n    },\n    /**\r\n     * 获取法术颜色\r\n     */\n    getSpellColor: function getSpellColor() {\n      var spell = this.animation.spell;\n      if (!spell) return '#9c27b0';\n      var colorMap = {\n        'fire': '#f44336',\n        'ice': '#2196f3',\n        'lightning': '#ffeb3b',\n        'acid': '#4caf50',\n        'necrotic': '#9c27b0',\n        'radiant': '#fff176',\n        'force': '#e91e63',\n        'psychic': '#673ab7'\n      };\n      return colorMap[spell.damageType] || '#9c27b0';\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "animation", "type", "Object", "required", "gridSize", "Number", "zoom", "data", "startTime", "Date", "now", "computed", "duration", "fromX", "_this$animation$from", "from", "x", "fromY", "_this$animation$from2", "y", "toX", "_this$animation$to", "to", "toY", "_this$animation$to2", "mounted", "_this", "setTimeout", "$emit", "id", "methods", "getMeleeAttackPath", "midX", "midY", "concat", "getProjectilePath", "getSpellPath", "getParticlePath", "index", "angle", "Math", "PI", "offsetX", "cos", "offsetY", "sin", "getMovementPath", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dx", "dy", "sqrt", "getProjectileColor", "weapon", "colorMap", "projectileType", "getSpellColor", "spell", "damageType"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatAnimation.vue"], "sourcesContent": ["<template>\r\n  <!-- 战斗动画效果组件 -->\r\n  <g class=\"combat-animation\" :class=\"animation.type\">\r\n    <!-- 近战攻击动画 -->\r\n    <g v-if=\"animation.type === 'melee_attack'\">\r\n      <!-- 攻击轨迹 -->\r\n      <path \r\n        :d=\"getMeleeAttackPath()\"\r\n        fill=\"none\"\r\n        stroke=\"#ff4444\"\r\n        stroke-width=\"4\"\r\n        stroke-linecap=\"round\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"stroke-dasharray\" \r\n          :values=\"`0,${getPathLength()};${getPathLength()},0`\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </path>\r\n      \r\n      <!-- 冲击波效果 -->\r\n      <circle \r\n        :cx=\"toX\"\r\n        :cy=\"toY\"\r\n        r=\"0\"\r\n        fill=\"none\"\r\n        stroke=\"#ff6666\"\r\n        stroke-width=\"2\"\r\n        opacity=\"0.8\"\r\n      >\r\n        <animate \r\n          attributeName=\"r\" \r\n          values=\"0;30;0\"\r\n          :dur=\"duration * 0.3 + 'ms'\"\r\n          :begin=\"duration * 0.7 + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0.8;0\"\r\n          :dur=\"duration * 0.3 + 'ms'\"\r\n          :begin=\"duration * 0.7 + 'ms'\"\r\n        />\r\n      </circle>\r\n    </g>\r\n    \r\n    <!-- 远程攻击动画 -->\r\n    <g v-if=\"animation.type === 'ranged_attack'\">\r\n      <!-- 弹道轨迹 -->\r\n      <circle \r\n        :cx=\"fromX\"\r\n        :cy=\"fromY\"\r\n        r=\"3\"\r\n        :fill=\"getProjectileColor()\"\r\n        opacity=\"0\"\r\n      >\r\n        <animateMotion \r\n          :dur=\"duration + 'ms'\"\r\n          :path=\"getProjectilePath()\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </circle>\r\n      \r\n      <!-- 轨迹线 -->\r\n      <line \r\n        :x1=\"fromX\"\r\n        :y1=\"fromY\"\r\n        :x2=\"toX\"\r\n        :y2=\"toY\"\r\n        stroke=\"#ffaa00\"\r\n        stroke-width=\"2\"\r\n        stroke-dasharray=\"5,5\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;0.6;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </line>\r\n      \r\n      <!-- 命中效果 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <circle \r\n          r=\"0\"\r\n          fill=\"none\"\r\n          stroke=\"#ffaa00\"\r\n          stroke-width=\"3\"\r\n          opacity=\"0\"\r\n        >\r\n          <animate \r\n            attributeName=\"r\" \r\n            values=\"0;25\"\r\n            :dur=\"duration * 0.2 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;1;0\"\r\n            :dur=\"duration * 0.2 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n        </circle>\r\n        \r\n        <!-- 火花效果 -->\r\n        <g v-for=\"i in 8\" :key=\"i\">\r\n          <line \r\n            x1=\"0\" y1=\"0\"\r\n            :x2=\"Math.cos(i * Math.PI / 4) * 15\"\r\n            :y2=\"Math.sin(i * Math.PI / 4) * 15\"\r\n            stroke=\"#ffdd00\"\r\n            stroke-width=\"2\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate \r\n              attributeName=\"opacity\" \r\n              values=\"0;1;0\"\r\n              :dur=\"duration * 0.3 + 'ms'\"\r\n              :begin=\"duration * 0.8 + 'ms'\"\r\n            />\r\n          </line>\r\n        </g>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 法术攻击动画 -->\r\n    <g v-if=\"animation.type === 'spell_attack'\">\r\n      <!-- 魔法光球 -->\r\n      <circle \r\n        :cx=\"fromX\"\r\n        :cy=\"fromY\"\r\n        r=\"8\"\r\n        :fill=\"getSpellColor()\"\r\n        opacity=\"0\"\r\n      >\r\n        <animateMotion \r\n          :dur=\"duration + 'ms'\"\r\n          :path=\"getSpellPath()\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0.5\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"r\" \r\n          values=\"8;12;8\"\r\n          :dur=\"500 + 'ms'\"\r\n          repeatCount=\"indefinite\"\r\n        />\r\n      </circle>\r\n      \r\n      <!-- 魔法粒子效果 -->\r\n      <g v-for=\"i in 12\" :key=\"i\">\r\n        <circle \r\n          :cx=\"fromX + Math.cos(i * Math.PI / 6) * 20\"\r\n          :cy=\"fromY + Math.sin(i * Math.PI / 6) * 20\"\r\n          r=\"2\"\r\n          :fill=\"getSpellColor()\"\r\n          opacity=\"0\"\r\n        >\r\n          <animateMotion \r\n            :dur=\"duration + 'ms'\"\r\n            :path=\"getParticlePath(i)\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;0.8;0\"\r\n            :dur=\"duration + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n      \r\n      <!-- 爆炸效果 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <circle \r\n          r=\"0\"\r\n          :fill=\"getSpellColor()\"\r\n          opacity=\"0\"\r\n        >\r\n          <animate \r\n            attributeName=\"r\" \r\n            values=\"0;40;60\"\r\n            :dur=\"duration * 0.4 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;0.8;0\"\r\n            :dur=\"duration * 0.4 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 移动动画 -->\r\n    <g v-if=\"animation.type === 'movement'\">\r\n      <!-- 移动轨迹 -->\r\n      <path \r\n        :d=\"getMovementPath()\"\r\n        fill=\"none\"\r\n        stroke=\"#4ecdc4\"\r\n        stroke-width=\"3\"\r\n        stroke-dasharray=\"8,4\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;0.8;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"stroke-dashoffset\" \r\n          values=\"0;-100\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </path>\r\n      \r\n      <!-- 移动粒子 -->\r\n      <g v-for=\"i in 6\" :key=\"i\">\r\n        <circle \r\n          :cx=\"fromX\"\r\n          :cy=\"fromY\"\r\n          r=\"3\"\r\n          fill=\"#4ecdc4\"\r\n          opacity=\"0\"\r\n        >\r\n          <animateMotion \r\n            :dur=\"duration + i * 100 + 'ms'\"\r\n            :path=\"getMovementPath()\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;0.6;0\"\r\n            :dur=\"duration + i * 100 + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 治疗动画 -->\r\n    <g v-if=\"animation.type === 'healing'\">\r\n      <!-- 治疗光环 -->\r\n      <circle \r\n        :cx=\"toX\"\r\n        :cy=\"toY\"\r\n        r=\"0\"\r\n        fill=\"none\"\r\n        stroke=\"#4caf50\"\r\n        stroke-width=\"3\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"r\" \r\n          values=\"0;50;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;0.8;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </circle>\r\n      \r\n      <!-- 治疗粒子 -->\r\n      <g v-for=\"i in 16\" :key=\"i\">\r\n        <circle \r\n          :cx=\"toX + Math.cos(i * Math.PI / 8) * 60\"\r\n          :cy=\"toY + Math.sin(i * Math.PI / 8) * 60\"\r\n          r=\"4\"\r\n          fill=\"#4caf50\"\r\n          opacity=\"0\"\r\n        >\r\n          <animateMotion \r\n            :dur=\"duration + 'ms'\"\r\n            :path=\"`M 0,0 L ${-Math.cos(i * Math.PI / 8) * 60},${-Math.sin(i * Math.PI / 8) * 60}`\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;1;0\"\r\n            :dur=\"duration + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n      \r\n      <!-- 十字光效 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <line x1=\"-20\" y1=\"0\" x2=\"20\" y2=\"0\" stroke=\"#66bb6a\" stroke-width=\"4\" opacity=\"0\">\r\n          <animate attributeName=\"opacity\" values=\"0;1;0\" :dur=\"duration * 0.5 + 'ms'\" :begin=\"duration * 0.3 + 'ms'\"/>\r\n        </line>\r\n        <line x1=\"0\" y1=\"-20\" x2=\"0\" y2=\"20\" stroke=\"#66bb6a\" stroke-width=\"4\" opacity=\"0\">\r\n          <animate attributeName=\"opacity\" values=\"0;1;0\" :dur=\"duration * 0.5 + 'ms'\" :begin=\"duration * 0.3 + 'ms'\"/>\r\n        </line>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 状态效果动画 -->\r\n    <g v-if=\"animation.type === 'status_effect'\">\r\n      <!-- 毒素效果 -->\r\n      <g v-if=\"animation.effect === 'poison'\">\r\n        <circle \r\n          :cx=\"toX\"\r\n          :cy=\"toY\"\r\n          r=\"25\"\r\n          fill=\"none\"\r\n          stroke=\"#8bc34a\"\r\n          stroke-width=\"2\"\r\n          opacity=\"0\"\r\n        >\r\n          <animate attributeName=\"opacity\" values=\"0;0.6;0\" :dur=\"duration + 'ms'\"/>\r\n          <animate attributeName=\"r\" values=\"25;35;25\" :dur=\"1000 + 'ms'\" repeatCount=\"indefinite\"/>\r\n        </circle>\r\n        \r\n        <!-- 毒气泡 -->\r\n        <g v-for=\"i in 8\" :key=\"i\">\r\n          <circle \r\n            :cx=\"toX + Math.cos(i * Math.PI / 4) * 20\"\r\n            :cy=\"toY + Math.sin(i * Math.PI / 4) * 20\"\r\n            r=\"3\"\r\n            fill=\"#689f38\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate attributeName=\"opacity\" values=\"0;0.8;0\" :dur=\"duration + i * 200 + 'ms'\"/>\r\n            <animate attributeName=\"cy\" :values=\"`${toY + Math.sin(i * Math.PI / 4) * 20};${toY + Math.sin(i * Math.PI / 4) * 20 - 30}`\" :dur=\"duration + i * 200 + 'ms'\"/>\r\n          </circle>\r\n        </g>\r\n      </g>\r\n      \r\n      <!-- 燃烧效果 -->\r\n      <g v-if=\"animation.effect === 'burning'\">\r\n        <g v-for=\"i in 12\" :key=\"i\">\r\n          <path \r\n            :d=\"`M ${toX + Math.cos(i * Math.PI / 6) * 15} ${toY + Math.sin(i * Math.PI / 6) * 15} Q ${toX + Math.cos(i * Math.PI / 6) * 10} ${toY + Math.sin(i * Math.PI / 6) * 10 - 20} ${toX + Math.cos(i * Math.PI / 6) * 5} ${toY + Math.sin(i * Math.PI / 6) * 5 - 30}`\"\r\n            fill=\"none\"\r\n            stroke=\"#ff5722\"\r\n            stroke-width=\"2\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate attributeName=\"opacity\" values=\"0;1;0\" :dur=\"duration + i * 100 + 'ms'\"/>\r\n          </path>\r\n        </g>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 死亡动画 -->\r\n    <g v-if=\"animation.type === 'death'\">\r\n      <!-- 灵魂上升 -->\r\n      <circle \r\n        :cx=\"toX\"\r\n        :cy=\"toY\"\r\n        r=\"8\"\r\n        fill=\"#e0e0e0\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate attributeName=\"cy\" :values=\"`${toY};${toY - 100}`\" :dur=\"duration + 'ms'\"/>\r\n        <animate attributeName=\"opacity\" values=\"0;0.8;0\" :dur=\"duration + 'ms'\"/>\r\n        <animate attributeName=\"r\" values=\"8;12;8\" :dur=\"1000 + 'ms'\" repeatCount=\"indefinite\"/>\r\n      </circle>\r\n      \r\n      <!-- 光芒效果 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <g v-for=\"i in 8\" :key=\"i\">\r\n          <line \r\n            x1=\"0\" y1=\"0\"\r\n            :x2=\"Math.cos(i * Math.PI / 4) * 40\"\r\n            :y2=\"Math.sin(i * Math.PI / 4) * 40\"\r\n            stroke=\"#ffffff\"\r\n            stroke-width=\"2\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate attributeName=\"opacity\" values=\"0;0.8;0\" :dur=\"duration * 0.5 + 'ms'\"/>\r\n          </line>\r\n        </g>\r\n      </g>\r\n    </g>\r\n  </g>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CombatAnimation',\r\n  props: {\r\n    animation: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    },\r\n    zoom: {\r\n      type: Number,\r\n      default: 1\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      startTime: Date.now()\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    duration() {\r\n      return this.animation.duration || 1000\r\n    },\r\n    \r\n    fromX() {\r\n      return (this.animation.from?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    fromY() {\r\n      return (this.animation.from?.y || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    toX() {\r\n      return (this.animation.to?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    toY() {\r\n      return (this.animation.to?.y || 0) * this.gridSize * this.zoom\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 动画完成后触发回调\r\n    setTimeout(() => {\r\n      this.$emit('complete', this.animation.id)\r\n    }, this.duration)\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 获取近战攻击路径\r\n     */\r\n    getMeleeAttackPath() {\r\n      const midX = (this.fromX + this.toX) / 2\r\n      const midY = (this.fromY + this.toY) / 2\r\n      \r\n      // 创建弧形攻击轨迹\r\n      return `M ${this.fromX} ${this.fromY} Q ${midX + 20} ${midY - 20} ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取弹道路径\r\n     */\r\n    getProjectilePath() {\r\n      const midX = (this.fromX + this.toX) / 2\r\n      const midY = (this.fromY + this.toY) / 2 - 30 // 抛物线效果\r\n      \r\n      return `M ${this.fromX} ${this.fromY} Q ${midX} ${midY} ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取法术路径\r\n     */\r\n    getSpellPath() {\r\n      // 法术通常是直线飞行\r\n      return `M ${this.fromX} ${this.fromY} L ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取粒子路径\r\n     */\r\n    getParticlePath(index) {\r\n      const angle = (index * Math.PI * 2) / 12\r\n      const offsetX = Math.cos(angle) * 10\r\n      const offsetY = Math.sin(angle) * 10\r\n      \r\n      return `M ${this.fromX + offsetX} ${this.fromY + offsetY} L ${this.toX + offsetX} ${this.toY + offsetY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取移动路径\r\n     */\r\n    getMovementPath() {\r\n      return `M ${this.fromX} ${this.fromY} L ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取路径长度\r\n     */\r\n    getPathLength() {\r\n      const dx = this.toX - this.fromX\r\n      const dy = this.toY - this.fromY\r\n      return Math.sqrt(dx * dx + dy * dy)\r\n    },\r\n    \r\n    /**\r\n     * 获取弹道颜色\r\n     */\r\n    getProjectileColor() {\r\n      const weapon = this.animation.weapon\r\n      if (!weapon) return '#ffaa00'\r\n      \r\n      const colorMap = {\r\n        'arrow': '#8d6e63',\r\n        'bullet': '#607d8b',\r\n        'bolt': '#795548',\r\n        'stone': '#9e9e9e',\r\n        'dart': '#546e7a'\r\n      }\r\n      \r\n      return colorMap[weapon.projectileType] || '#ffaa00'\r\n    },\r\n    \r\n    /**\r\n     * 获取法术颜色\r\n     */\r\n    getSpellColor() {\r\n      const spell = this.animation.spell\r\n      if (!spell) return '#9c27b0'\r\n      \r\n      const colorMap = {\r\n        'fire': '#f44336',\r\n        'ice': '#2196f3',\r\n        'lightning': '#ffeb3b',\r\n        'acid': '#4caf50',\r\n        'necrotic': '#9c27b0',\r\n        'radiant': '#fff176',\r\n        'force': '#e91e63',\r\n        'psychic': '#673ab7'\r\n      }\r\n      \r\n      return colorMap[spell.damageType] || '#9c27b0'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.combat-animation {\r\n  pointer-events: none;\r\n}\r\n\r\n/* 近战攻击动画样式 */\r\n.combat-animation.melee_attack path {\r\n  filter: drop-shadow(0 0 4px rgba(255, 68, 68, 0.6));\r\n}\r\n\r\n/* 远程攻击动画样式 */\r\n.combat-animation.ranged_attack circle {\r\n  filter: drop-shadow(0 0 3px rgba(255, 170, 0, 0.8));\r\n}\r\n\r\n/* 法术攻击动画样式 */\r\n.combat-animation.spell_attack circle {\r\n  filter: drop-shadow(0 0 6px rgba(156, 39, 176, 0.8));\r\n}\r\n\r\n/* 移动动画样式 */\r\n.combat-animation.movement path {\r\n  filter: drop-shadow(0 0 2px rgba(78, 205, 196, 0.6));\r\n}\r\n\r\n/* 治疗动画样式 */\r\n.combat-animation.healing circle {\r\n  filter: drop-shadow(0 0 4px rgba(76, 175, 80, 0.8));\r\n}\r\n\r\n/* 状态效果动画样式 */\r\n.combat-animation.status_effect circle {\r\n  filter: drop-shadow(0 0 3px rgba(139, 195, 74, 0.6));\r\n}\r\n\r\n/* 死亡动画样式 */\r\n.combat-animation.death circle {\r\n  filter: drop-shadow(0 0 8px rgba(224, 224, 224, 0.9));\r\n}\r\n</style>"], "mappings": ";;;;AAqYA,eAAe;EACbA,IAAI,EAAE,iBAAiB;EACvBC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;MACRH,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDC,IAAI,EAAE;MACJL,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX;EACF,CAAC;EAEDE,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACZ,SAAS,CAACY,QAAO,IAAK,IAAG;IACvC,CAAC;IAEDC,KAAK,WAALA,KAAKA,CAAA,EAAG;MAAA,IAAAC,oBAAA;MACN,OAAO,CAAC,EAAAA,oBAAA,OAAI,CAACd,SAAS,CAACe,IAAI,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,CAAA,KAAK,CAAC,IAAI,IAAI,CAACZ,QAAO,GAAI,IAAI,CAACE,IAAG;IACjE,CAAC;IAEDW,KAAK,WAALA,KAAKA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MACN,OAAO,CAAC,EAAAA,qBAAA,OAAI,CAAClB,SAAS,CAACe,IAAI,cAAAG,qBAAA,uBAAnBA,qBAAA,CAAqBC,CAAA,KAAK,CAAC,IAAI,IAAI,CAACf,QAAO,GAAI,IAAI,CAACE,IAAG;IACjE,CAAC;IAEDc,GAAG,WAAHA,GAAGA,CAAA,EAAG;MAAA,IAAAC,kBAAA;MACJ,OAAO,CAAC,EAAAA,kBAAA,OAAI,CAACrB,SAAS,CAACsB,EAAE,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBL,CAAA,KAAK,CAAC,IAAI,IAAI,CAACZ,QAAO,GAAI,IAAI,CAACE,IAAG;IAC/D,CAAC;IAEDiB,GAAG,WAAHA,GAAGA,CAAA,EAAG;MAAA,IAAAC,mBAAA;MACJ,OAAO,CAAC,EAAAA,mBAAA,OAAI,CAACxB,SAAS,CAACsB,EAAE,cAAAE,mBAAA,uBAAjBA,mBAAA,CAAmBL,CAAA,KAAK,CAAC,IAAI,IAAI,CAACf,QAAO,GAAI,IAAI,CAACE,IAAG;IAC/D;EACF,CAAC;EAEDmB,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IACR;IACAC,UAAU,CAAC,YAAM;MACfD,KAAI,CAACE,KAAK,CAAC,UAAU,EAAEF,KAAI,CAAC1B,SAAS,CAAC6B,EAAE;IAC1C,CAAC,EAAE,IAAI,CAACjB,QAAQ;EAClB,CAAC;EAEDkB,OAAO,EAAE;IACP;;;IAGAC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAMC,IAAG,GAAI,CAAC,IAAI,CAACnB,KAAI,GAAI,IAAI,CAACO,GAAG,IAAI;MACvC,IAAMa,IAAG,GAAI,CAAC,IAAI,CAAChB,KAAI,GAAI,IAAI,CAACM,GAAG,IAAI;;MAEvC;MACA,YAAAW,MAAA,CAAY,IAAI,CAACrB,KAAK,OAAAqB,MAAA,CAAI,IAAI,CAACjB,KAAK,SAAAiB,MAAA,CAAMF,IAAG,GAAI,EAAE,OAAAE,MAAA,CAAID,IAAG,GAAI,EAAE,OAAAC,MAAA,CAAI,IAAI,CAACd,GAAG,OAAAc,MAAA,CAAI,IAAI,CAACX,GAAG;IAC1F,CAAC;IAED;;;IAGAY,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAMH,IAAG,GAAI,CAAC,IAAI,CAACnB,KAAI,GAAI,IAAI,CAACO,GAAG,IAAI;MACvC,IAAMa,IAAG,GAAI,CAAC,IAAI,CAAChB,KAAI,GAAI,IAAI,CAACM,GAAG,IAAI,IAAI,EAAC,EAAE;;MAE9C,YAAAW,MAAA,CAAY,IAAI,CAACrB,KAAK,OAAAqB,MAAA,CAAI,IAAI,CAACjB,KAAK,SAAAiB,MAAA,CAAMF,IAAI,OAAAE,MAAA,CAAID,IAAI,OAAAC,MAAA,CAAI,IAAI,CAACd,GAAG,OAAAc,MAAA,CAAI,IAAI,CAACX,GAAG;IAChF,CAAC;IAED;;;IAGAa,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb;MACA,YAAAF,MAAA,CAAY,IAAI,CAACrB,KAAK,OAAAqB,MAAA,CAAI,IAAI,CAACjB,KAAK,SAAAiB,MAAA,CAAM,IAAI,CAACd,GAAG,OAAAc,MAAA,CAAI,IAAI,CAACX,GAAG;IAChE,CAAC;IAED;;;IAGAc,eAAe,WAAfA,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAMC,KAAI,GAAKD,KAAI,GAAIE,IAAI,CAACC,EAAC,GAAI,CAAC,GAAI,EAAC;MACvC,IAAMC,OAAM,GAAIF,IAAI,CAACG,GAAG,CAACJ,KAAK,IAAI,EAAC;MACnC,IAAMK,OAAM,GAAIJ,IAAI,CAACK,GAAG,CAACN,KAAK,IAAI,EAAC;MAEnC,YAAAL,MAAA,CAAY,IAAI,CAACrB,KAAI,GAAI6B,OAAO,OAAAR,MAAA,CAAI,IAAI,CAACjB,KAAI,GAAI2B,OAAO,SAAAV,MAAA,CAAM,IAAI,CAACd,GAAE,GAAIsB,OAAO,OAAAR,MAAA,CAAI,IAAI,CAACX,GAAE,GAAIqB,OAAO;IACxG,CAAC;IAED;;;IAGAE,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,YAAAZ,MAAA,CAAY,IAAI,CAACrB,KAAK,OAAAqB,MAAA,CAAI,IAAI,CAACjB,KAAK,SAAAiB,MAAA,CAAM,IAAI,CAACd,GAAG,OAAAc,MAAA,CAAI,IAAI,CAACX,GAAG;IAChE,CAAC;IAED;;;IAGAwB,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAMC,EAAC,GAAI,IAAI,CAAC5B,GAAE,GAAI,IAAI,CAACP,KAAI;MAC/B,IAAMoC,EAAC,GAAI,IAAI,CAAC1B,GAAE,GAAI,IAAI,CAACN,KAAI;MAC/B,OAAOuB,IAAI,CAACU,IAAI,CAACF,EAAC,GAAIA,EAAC,GAAIC,EAAC,GAAIA,EAAE;IACpC,CAAC;IAED;;;IAGAE,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAMC,MAAK,GAAI,IAAI,CAACpD,SAAS,CAACoD,MAAK;MACnC,IAAI,CAACA,MAAM,EAAE,OAAO,SAAQ;MAE5B,IAAMC,QAAO,GAAI;QACf,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,OAAO,EAAE,SAAS;QAClB,MAAM,EAAE;MACV;MAEA,OAAOA,QAAQ,CAACD,MAAM,CAACE,cAAc,KAAK,SAAQ;IACpD,CAAC;IAED;;;IAGAC,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAMC,KAAI,GAAI,IAAI,CAACxD,SAAS,CAACwD,KAAI;MACjC,IAAI,CAACA,KAAK,EAAE,OAAO,SAAQ;MAE3B,IAAMH,QAAO,GAAI;QACf,MAAM,EAAE,SAAS;QACjB,KAAK,EAAE,SAAS;QAChB,WAAW,EAAE,SAAS;QACtB,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,SAAS;QACrB,SAAS,EAAE,SAAS;QACpB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE;MACb;MAEA,OAAOA,QAAQ,CAACG,KAAK,CAACC,UAAU,KAAK,SAAQ;IAC/C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}