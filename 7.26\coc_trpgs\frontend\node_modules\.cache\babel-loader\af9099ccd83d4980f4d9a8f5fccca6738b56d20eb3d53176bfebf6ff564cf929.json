{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  key: 0,\n  \"class\": \"forced-combat-overlay\"\n};\nvar _hoisted_2 = {\n  \"class\": \"combat-container\"\n};\nvar _hoisted_3 = {\n  \"class\": \"combat-status-bar\"\n};\nvar _hoisted_4 = {\n  \"class\": \"status-left\"\n};\nvar _hoisted_5 = {\n  \"class\": \"round-info\"\n};\nvar _hoisted_6 = {\n  \"class\": \"round-number\"\n};\nvar _hoisted_7 = {\n  \"class\": \"turn-info\"\n};\nvar _hoisted_8 = {\n  \"class\": \"status-right\"\n};\nvar _hoisted_9 = {\n  \"class\": \"combat-main-area\"\n};\nvar _hoisted_10 = {\n  \"class\": \"battlefield-container\"\n};\nvar _hoisted_11 = {\n  \"class\": \"info-panel\"\n};\nvar _hoisted_12 = {\n  key: 0,\n  \"class\": \"current-character-panel\"\n};\nvar _hoisted_13 = {\n  \"class\": \"panel-header\"\n};\nvar _hoisted_14 = {\n  \"class\": \"character-stats\"\n};\nvar _hoisted_15 = {\n  \"class\": \"stat-bar\"\n};\nvar _hoisted_16 = {\n  \"class\": \"progress-bar health\"\n};\nvar _hoisted_17 = {\n  \"class\": \"progress-text\"\n};\nvar _hoisted_18 = {\n  key: 0,\n  \"class\": \"stat-bar\"\n};\nvar _hoisted_19 = {\n  \"class\": \"progress-bar sanity\"\n};\nvar _hoisted_20 = {\n  \"class\": \"progress-text\"\n};\nvar _hoisted_21 = {\n  key: 1,\n  \"class\": \"status-effects\"\n};\nvar _hoisted_22 = {\n  \"class\": \"effects-list\"\n};\nvar _hoisted_23 = {\n  \"class\": \"initiative-panel\"\n};\nvar _hoisted_24 = {\n  \"class\": \"initiative-list\"\n};\nvar _hoisted_25 = [\"onClick\"];\nvar _hoisted_26 = {\n  \"class\": \"initiative-number\"\n};\nvar _hoisted_27 = {\n  \"class\": \"participant-info\"\n};\nvar _hoisted_28 = {\n  \"class\": \"participant-name\"\n};\nvar _hoisted_29 = {\n  \"class\": \"participant-health\"\n};\nvar _hoisted_30 = {\n  key: 0,\n  \"class\": \"turn-indicator\"\n};\nvar _hoisted_31 = {\n  \"class\": \"combat-action-bar\"\n};\nvar _hoisted_32 = {\n  key: 0,\n  \"class\": \"player-actions\"\n};\nvar _hoisted_33 = {\n  \"class\": \"action-section\"\n};\nvar _hoisted_34 = {\n  \"class\": \"action-buttons\"\n};\nvar _hoisted_35 = {\n  key: 0,\n  \"class\": \"action-confirm\"\n};\nvar _hoisted_36 = {\n  \"class\": \"waiting-indicator\"\n};\nvar _hoisted_37 = {\n  \"class\": \"waiting-content\"\n};\nvar _hoisted_38 = {\n  \"class\": \"waiting-text\"\n};\nvar _hoisted_39 = {\n  key: 2,\n  \"class\": \"keeper-controls\"\n};\nvar _hoisted_40 = {\n  \"class\": \"control-section\"\n};\nvar _hoisted_41 = {\n  \"class\": \"keeper-buttons\"\n};\nvar _hoisted_42 = {\n  key: 0,\n  \"class\": \"combat-result-modal\"\n};\nvar _hoisted_43 = {\n  \"class\": \"result-content\"\n};\nvar _hoisted_44 = {\n  \"class\": \"result-header\"\n};\nvar _hoisted_45 = {\n  \"class\": \"result-body\"\n};\nvar _hoisted_46 = {\n  key: 0,\n  \"class\": \"result-details\"\n};\nvar _hoisted_47 = {\n  \"class\": \"result-actions\"\n};\nvar _hoisted_48 = {\n  key: 1,\n  \"class\": \"reconnect-overlay\"\n};\nvar _hoisted_49 = {\n  \"class\": \"reconnect-content\"\n};\nvar _hoisted_50 = {\n  \"class\": \"reconnect-progress\"\n};\nvar _hoisted_51 = {\n  \"class\": \"progress-bar\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$data$participants, _$data$participants2, _$data$selectedPartic, _$data$selectedPartic2, _$options$currentPart, _$options$currentPart2;\n  var _component_BattlefieldGrid = _resolveComponent(\"BattlefieldGrid\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 强制战斗模式全屏覆盖 \"), $props.isActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 背景遮罩 \"), _cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n    \"class\": \"combat-backdrop\"\n  }, null, -1 /* CACHED */)), _createCommentVNode(\" 战斗界面容器 \"), _createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 顶部状态栏 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", {\n    \"class\": \"combat-indicator\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }), _createElementVNode(\"span\", {\n    \"class\": \"combat-text\"\n  }, \"战斗模式\"), _createElementVNode(\"div\", {\n    \"class\": \"pulse-ring\"\n  })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, \"第 \" + _toDisplayString($data.currentRound) + \" 轮\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_7, _toDisplayString($options.currentTurnInfo), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"connection-status\", $data.connectionStatus])\n  }, [_cache[12] || (_cache[12] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-wifi\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($options.connectionText), 1 /* TEXT */)], 2 /* CLASS */)])]), _createCommentVNode(\" 主战斗区域 \"), _createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" 战场视图 \"), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_BattlefieldGrid, {\n    characters: ((_$data$participants = $data.participants) === null || _$data$participants === void 0 ? void 0 : _$data$participants.filter(function (p) {\n      return p.isPlayer;\n    })) || [],\n    monsters: ((_$data$participants2 = $data.participants) === null || _$data$participants2 === void 0 ? void 0 : _$data$participants2.filter(function (p) {\n      return !p.isPlayer;\n    })) || [],\n    \"selected-character\": (_$data$selectedPartic = $data.selectedParticipant) !== null && _$data$selectedPartic !== void 0 && _$data$selectedPartic.isPlayer ? $data.selectedParticipant : null,\n    \"selected-monster\": !((_$data$selectedPartic2 = $data.selectedParticipant) !== null && _$data$selectedPartic2 !== void 0 && _$data$selectedPartic2.isPlayer) ? $data.selectedParticipant : null,\n    \"current-turn\": $data.currentTurn,\n    \"target-participant\": $data.targetParticipant,\n    \"battlefield-size\": $data.battlefieldSize,\n    onParticipantClicked: $options.handleParticipantClick,\n    onPositionClicked: $options.handlePositionClick,\n    onParticipantMoved: $options.handleParticipantMove\n  }, null, 8 /* PROPS */, [\"characters\", \"monsters\", \"selected-character\", \"selected-monster\", \"current-turn\", \"target-participant\", \"battlefield-size\", \"onParticipantClicked\", \"onPositionClicked\", \"onParticipantMoved\"])]), _createCommentVNode(\" 右侧信息面板 \"), _createElementVNode(\"div\", _hoisted_11, [_createCommentVNode(\" 当前角色信息 \"), $options.currentParticipant ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"h3\", null, _toDisplayString($options.currentParticipant.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"character-type\", {\n      player: $options.currentParticipant.isPlayer\n    }])\n  }, _toDisplayString($options.currentParticipant.isPlayer ? '玩家' : 'NPC'), 3 /* TEXT, CLASS */)]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[13] || (_cache[13] = _createElementVNode(\"label\", null, \"生命值\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", {\n    \"class\": \"progress-fill\",\n    style: _normalizeStyle({\n      width: \"\".concat($options.getHealthPercentage($options.currentParticipant), \"%\")\n    })\n  }, null, 4 /* STYLE */), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($options.currentParticipant.currentHP) + \"/\" + _toDisplayString($options.currentParticipant.maxHP), 1 /* TEXT */)])]), $options.currentParticipant.currentSAN ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_cache[14] || (_cache[14] = _createElementVNode(\"label\", null, \"理智值\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", {\n    \"class\": \"progress-fill\",\n    style: _normalizeStyle({\n      width: \"\".concat($options.getSanityPercentage($options.currentParticipant), \"%\")\n    })\n  }, null, 4 /* STYLE */), _createElementVNode(\"span\", _hoisted_20, _toDisplayString($options.currentParticipant.currentSAN) + \"/\" + _toDisplayString($options.currentParticipant.maxSAN), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true), (_$options$currentPart = $options.currentParticipant.conditions) !== null && _$options$currentPart !== void 0 && _$options$currentPart.length ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_cache[15] || (_cache[15] = _createElementVNode(\"label\", null, \"状态效果\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.currentParticipant.conditions, function (effect) {\n    return _openBlock(), _createElementBlock(\"span\", {\n      key: effect,\n      \"class\": _normalizeClass([\"effect-badge\", effect])\n    }, _toDisplayString($options.getStatusEffectName(effect)), 3 /* TEXT, CLASS */);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 先攻顺序 \"), _createElementVNode(\"div\", _hoisted_23, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    \"class\": \"panel-header\"\n  }, [_createElementVNode(\"h3\", null, \"先攻顺序\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.participants, function (participant, index) {\n    var _$data$selectedPartic3;\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: participant.id,\n      \"class\": _normalizeClass([\"initiative-item\", {\n        current: index === $data.currentTurn,\n        player: participant.isPlayer,\n        selected: ((_$data$selectedPartic3 = $data.selectedParticipant) === null || _$data$selectedPartic3 === void 0 ? void 0 : _$data$selectedPartic3.id) === participant.id\n      }]),\n      onClick: function onClick($event) {\n        return $options.selectParticipant(participant);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_26, _toDisplayString(participant.initiative), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"span\", _hoisted_28, _toDisplayString(participant.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_29, _toDisplayString(participant.currentHP) + \"/\" + _toDisplayString(participant.maxHP), 1 /* TEXT */)]), index === $data.currentTurn ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, _toConsumableArray(_cache[16] || (_cache[16] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-play\"\n    }, null, -1 /* CACHED */)])))) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_25);\n  }), 128 /* KEYED_FRAGMENT */))])])])]), _createCommentVNode(\" 底部操作栏 \"), _createElementVNode(\"div\", _hoisted_31, [_createCommentVNode(\" 玩家操作区 (仅当前回合玩家可见) \"), $options.isPlayerTurn && !$props.isKeeper ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_cache[23] || (_cache[23] = _createElementVNode(\"h4\", null, \"选择行动\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $options.selectAction('attack');\n    }),\n    \"class\": _normalizeClass([\"action-btn attack\", {\n      active: $data.selectedAction === 'attack'\n    }])\n  }, _cache[18] || (_cache[18] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"攻击\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $options.selectAction('defend');\n    }),\n    \"class\": _normalizeClass([\"action-btn defend\", {\n      active: $data.selectedAction === 'defend'\n    }])\n  }, _cache[19] || (_cache[19] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-shield\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"防御\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $options.selectAction('move');\n    }),\n    \"class\": _normalizeClass([\"action-btn move\", {\n      active: $data.selectedAction === 'move'\n    }])\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-running\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"移动\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $options.selectAction('item');\n    }),\n    \"class\": _normalizeClass([\"action-btn item\", {\n      active: $data.selectedAction === 'item'\n    }])\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-backpack\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"道具\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function ($event) {\n      return $options.selectAction('maneuver');\n    }),\n    \"class\": _normalizeClass([\"action-btn maneuver\", {\n      active: $data.selectedAction === 'maneuver'\n    }])\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-fist-raised\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"战技\", -1 /* CACHED */)]), 2 /* CLASS */)])]), _createCommentVNode(\" 确认操作 \"), $data.selectedAction ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = function () {\n      return $options.confirmAction && $options.confirmAction.apply($options, arguments);\n    }),\n    \"class\": \"btn-confirm\"\n  }, _cache[24] || (_cache[24] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-check\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 确认行动 \")])), _createElementVNode(\"button\", {\n    onClick: _cache[6] || (_cache[6] = function () {\n      return $options.cancelAction && $options.cancelAction.apply($options, arguments);\n    }),\n    \"class\": \"btn-cancel\"\n  }, _cache[25] || (_cache[25] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-times\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 取消 \")]))])) : _createCommentVNode(\"v-if\", true)])) : !$props.isKeeper ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 等待提示 (非当前回合玩家) \"), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n    \"class\": \"spinner\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_38, [_cache[26] || (_cache[26] = _createElementVNode(\"h4\", null, \"等待其他玩家行动\", -1 /* CACHED */)), _createElementVNode(\"p\", null, \"当前回合: \" + _toDisplayString(((_$options$currentPart2 = $options.currentParticipant) === null || _$options$currentPart2 === void 0 ? void 0 : _$options$currentPart2.name) || '未知'), 1 /* TEXT */)])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" KP控制区 \"), $props.isKeeper ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_cache[31] || (_cache[31] = _createElementVNode(\"h4\", null, \"KP控制\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"button\", {\n    onClick: _cache[7] || (_cache[7] = function () {\n      return $options.nextTurn && $options.nextTurn.apply($options, arguments);\n    }),\n    \"class\": \"keeper-btn\"\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-forward\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 下一回合 \")])), _createElementVNode(\"button\", {\n    onClick: _cache[8] || (_cache[8] = function () {\n      return $options.pauseCombat && $options.pauseCombat.apply($options, arguments);\n    }),\n    \"class\": \"keeper-btn\"\n  }, _cache[29] || (_cache[29] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-pause\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 暂停战斗 \")])), _createElementVNode(\"button\", {\n    onClick: _cache[9] || (_cache[9] = function () {\n      return $options.endCombat && $options.endCombat.apply($options, arguments);\n    }),\n    \"class\": \"keeper-btn danger\"\n  }, _cache[30] || (_cache[30] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-stop\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 结束战斗 \")]))])])])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 战斗结果弹窗 \"), $data.showResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"h2\", null, _toDisplayString($data.resultData.title), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"p\", null, _toDisplayString($data.resultData.message), 1 /* TEXT */), $data.resultData.details ? (_openBlock(), _createElementBlock(\"div\", _hoisted_46, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.resultData.details, function (detail) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: detail.label\n    }, [_createElementVNode(\"strong\", null, _toDisplayString(detail.label) + \":\", 1 /* TEXT */), _createTextVNode(\" \" + _toDisplayString(detail.value), 1 /* TEXT */)]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = function () {\n      return $options.closeResult && $options.closeResult.apply($options, arguments);\n    }),\n    \"class\": \"btn-primary\"\n  }, \"确定\")])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 断线重连提示 \"), $data.showReconnect ? (_openBlock(), _createElementBlock(\"div\", _hoisted_48, [_createElementVNode(\"div\", _hoisted_49, [_cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n    \"class\": \"reconnect-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-wifi\"\n  })], -1 /* CACHED */)), _cache[33] || (_cache[33] = _createElementVNode(\"h3\", null, \"连接中断\", -1 /* CACHED */)), _cache[34] || (_cache[34] = _createElementVNode(\"p\", null, \"正在尝试重新连接到战斗服务器...\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_50, [_createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"div\", {\n    \"class\": \"progress-fill\",\n    style: _normalizeStyle({\n      width: \"\".concat($data.reconnectProgress, \"%\")\n    })\n  }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", null, _toDisplayString($data.reconnectAttempts) + \"/\" + _toDisplayString($data.maxReconnectAttempts), 1 /* TEXT */)])])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_createCommentVNode", "$props", "isActive", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_toDisplayString", "$data", "currentRound", "_hoisted_7", "$options", "currentTurnInfo", "_hoisted_8", "_normalizeClass", "connectionStatus", "connectionText", "_hoisted_9", "_hoisted_10", "_createVNode", "_component_BattlefieldGrid", "characters", "_$data$participants", "participants", "filter", "p", "isPlayer", "monsters", "_$data$participants2", "_$data$selectedPartic", "selectedParticipant", "_$data$selectedPartic2", "currentTurn", "targetParticipant", "battlefieldSize", "onParticipantClicked", "handleParticipantClick", "onPositionClicked", "handlePositionClick", "onParticipantMoved", "handleParticipantMove", "_hoisted_11", "currentParticipant", "_hoisted_12", "_hoisted_13", "name", "player", "_hoisted_14", "_hoisted_15", "_hoisted_16", "style", "_normalizeStyle", "width", "concat", "getHealthPercentage", "_hoisted_17", "currentHP", "maxHP", "currentSAN", "_hoisted_18", "_hoisted_19", "getSanityPercentage", "_hoisted_20", "maxSAN", "conditions", "_$options$currentPart", "length", "_hoisted_21", "_hoisted_22", "_Fragment", "_renderList", "effect", "key", "getStatusEffectName", "_hoisted_23", "_hoisted_24", "participant", "index", "_$data$selectedPartic3", "id", "onClick", "$event", "selectParticipant", "_hoisted_26", "initiative", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_toConsumableArray", "_cache", "_hoisted_31", "isPlayerTurn", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_32", "_hoisted_33", "_hoisted_34", "selectAction", "active", "selectedAction", "_hoisted_35", "confirmAction", "apply", "arguments", "cancelAction", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_$options$currentPart2", "_hoisted_39", "_hoisted_40", "_hoisted_41", "nextTurn", "pauseCombat", "endCombat", "showResult", "_hoisted_42", "_hoisted_43", "_hoisted_44", "resultData", "title", "_hoisted_45", "message", "details", "_hoisted_46", "detail", "label", "value", "_hoisted_47", "closeResult", "showReconnect", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "reconnectProgress", "reconnectAttempts", "maxReconnectAttempts"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\ForcedCombatMode.vue"], "sourcesContent": ["<template>\r\n  <!-- 强制战斗模式全屏覆盖 -->\r\n  <div class=\"forced-combat-overlay\" v-if=\"isActive\">\r\n    <!-- 背景遮罩 -->\r\n    <div class=\"combat-backdrop\"></div>\r\n    \r\n    <!-- 战斗界面容器 -->\r\n    <div class=\"combat-container\">\r\n      <!-- 顶部状态栏 -->\r\n      <div class=\"combat-status-bar\">\r\n        <div class=\"status-left\">\r\n          <div class=\"combat-indicator\">\r\n            <i class=\"fas fa-sword\"></i>\r\n            <span class=\"combat-text\">战斗模式</span>\r\n            <div class=\"pulse-ring\"></div>\r\n          </div>\r\n          <div class=\"round-info\">\r\n            <span class=\"round-number\">第 {{ currentRound }} 轮</span>\r\n            <span class=\"turn-info\">{{ currentTurnInfo }}</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"status-right\">\r\n          <div class=\"connection-status\" :class=\"connectionStatus\">\r\n            <i class=\"fas fa-wifi\"></i>\r\n            <span>{{ connectionText }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主战斗区域 -->\r\n      <div class=\"combat-main-area\">\r\n        <!-- 战场视图 -->\r\n        <div class=\"battlefield-container\">\r\n          <BattlefieldGrid\r\n            :characters=\"participants?.filter(p => p.isPlayer) || []\"\r\n            :monsters=\"participants?.filter(p => !p.isPlayer) || []\"\r\n            :selected-character=\"selectedParticipant?.isPlayer ? selectedParticipant : null\"\r\n            :selected-monster=\"!selectedParticipant?.isPlayer ? selectedParticipant : null\"\r\n            :current-turn=\"currentTurn\"\r\n            :target-participant=\"targetParticipant\"\r\n            :battlefield-size=\"battlefieldSize\"\r\n            @participant-clicked=\"handleParticipantClick\"\r\n            @position-clicked=\"handlePositionClick\"\r\n            @participant-moved=\"handleParticipantMove\"\r\n          />\r\n        </div>\r\n\r\n        <!-- 右侧信息面板 -->\r\n        <div class=\"info-panel\">\r\n          <!-- 当前角色信息 -->\r\n          <div class=\"current-character-panel\" v-if=\"currentParticipant\">\r\n            <div class=\"panel-header\">\r\n              <h3>{{ currentParticipant.name }}</h3>\r\n              <div class=\"character-type\" :class=\"{ player: currentParticipant.isPlayer }\">\r\n                {{ currentParticipant.isPlayer ? '玩家' : 'NPC' }}\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"character-stats\">\r\n              <div class=\"stat-bar\">\r\n                <label>生命值</label>\r\n                <div class=\"progress-bar health\">\r\n                  <div \r\n                    class=\"progress-fill\" \r\n                    :style=\"{ width: `${getHealthPercentage(currentParticipant)}%` }\"\r\n                  ></div>\r\n                  <span class=\"progress-text\">\r\n                    {{ currentParticipant.currentHP }}/{{ currentParticipant.maxHP }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div class=\"stat-bar\" v-if=\"currentParticipant.currentSAN\">\r\n                <label>理智值</label>\r\n                <div class=\"progress-bar sanity\">\r\n                  <div \r\n                    class=\"progress-fill\" \r\n                    :style=\"{ width: `${getSanityPercentage(currentParticipant)}%` }\"\r\n                  ></div>\r\n                  <span class=\"progress-text\">\r\n                    {{ currentParticipant.currentSAN }}/{{ currentParticipant.maxSAN }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div class=\"status-effects\" v-if=\"currentParticipant.conditions?.length\">\r\n                <label>状态效果</label>\r\n                <div class=\"effects-list\">\r\n                  <span \r\n                    v-for=\"effect in currentParticipant.conditions\" \r\n                    :key=\"effect\"\r\n                    class=\"effect-badge\"\r\n                    :class=\"effect\"\r\n                  >\r\n                    {{ getStatusEffectName(effect) }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 先攻顺序 -->\r\n          <div class=\"initiative-panel\">\r\n            <div class=\"panel-header\">\r\n              <h3>先攻顺序</h3>\r\n            </div>\r\n            <div class=\"initiative-list\">\r\n              <div \r\n                v-for=\"(participant, index) in participants\" \r\n                :key=\"participant.id\"\r\n                class=\"initiative-item\"\r\n                :class=\"{ \r\n                  current: index === currentTurn,\r\n                  player: participant.isPlayer,\r\n                  selected: selectedParticipant?.id === participant.id\r\n                }\"\r\n                @click=\"selectParticipant(participant)\"\r\n              >\r\n                <div class=\"initiative-number\">{{ participant.initiative }}</div>\r\n                <div class=\"participant-info\">\r\n                  <span class=\"participant-name\">{{ participant.name }}</span>\r\n                  <div class=\"participant-health\">\r\n                    {{ participant.currentHP }}/{{ participant.maxHP }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"turn-indicator\" v-if=\"index === currentTurn\">\r\n                  <i class=\"fas fa-play\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 底部操作栏 -->\r\n      <div class=\"combat-action-bar\">\r\n        <!-- 玩家操作区 (仅当前回合玩家可见) -->\r\n        <div class=\"player-actions\" v-if=\"isPlayerTurn && !isKeeper\">\r\n          <div class=\"action-section\">\r\n            <h4>选择行动</h4>\r\n            <div class=\"action-buttons\">\r\n              <button \r\n                @click=\"selectAction('attack')\" \r\n                class=\"action-btn attack\"\r\n                :class=\"{ active: selectedAction === 'attack' }\"\r\n              >\r\n                <i class=\"fas fa-sword\"></i>\r\n                <span>攻击</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('defend')\" \r\n                class=\"action-btn defend\"\r\n                :class=\"{ active: selectedAction === 'defend' }\"\r\n              >\r\n                <i class=\"fas fa-shield\"></i>\r\n                <span>防御</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('move')\" \r\n                class=\"action-btn move\"\r\n                :class=\"{ active: selectedAction === 'move' }\"\r\n              >\r\n                <i class=\"fas fa-running\"></i>\r\n                <span>移动</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('item')\" \r\n                class=\"action-btn item\"\r\n                :class=\"{ active: selectedAction === 'item' }\"\r\n              >\r\n                <i class=\"fas fa-backpack\"></i>\r\n                <span>道具</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('maneuver')\" \r\n                class=\"action-btn maneuver\"\r\n                :class=\"{ active: selectedAction === 'maneuver' }\"\r\n              >\r\n                <i class=\"fas fa-fist-raised\"></i>\r\n                <span>战技</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 确认操作 -->\r\n          <div class=\"action-confirm\" v-if=\"selectedAction\">\r\n            <button @click=\"confirmAction\" class=\"btn-confirm\">\r\n              <i class=\"fas fa-check\"></i>\r\n              确认行动\r\n            </button>\r\n            <button @click=\"cancelAction\" class=\"btn-cancel\">\r\n              <i class=\"fas fa-times\"></i>\r\n              取消\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 等待提示 (非当前回合玩家) -->\r\n        <div class=\"waiting-indicator\" v-else-if=\"!isKeeper\">\r\n          <div class=\"waiting-content\">\r\n            <div class=\"spinner\"></div>\r\n            <div class=\"waiting-text\">\r\n              <h4>等待其他玩家行动</h4>\r\n              <p>当前回合: {{ currentParticipant?.name || '未知' }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- KP控制区 -->\r\n        <div class=\"keeper-controls\" v-if=\"isKeeper\">\r\n          <div class=\"control-section\">\r\n            <h4>KP控制</h4>\r\n            <div class=\"keeper-buttons\">\r\n              <button @click=\"nextTurn\" class=\"keeper-btn\">\r\n                <i class=\"fas fa-forward\"></i>\r\n                下一回合\r\n              </button>\r\n              <button @click=\"pauseCombat\" class=\"keeper-btn\">\r\n                <i class=\"fas fa-pause\"></i>\r\n                暂停战斗\r\n              </button>\r\n              <button @click=\"endCombat\" class=\"keeper-btn danger\">\r\n                <i class=\"fas fa-stop\"></i>\r\n                结束战斗\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战斗结果弹窗 -->\r\n    <div class=\"combat-result-modal\" v-if=\"showResult\">\r\n      <div class=\"result-content\">\r\n        <div class=\"result-header\">\r\n          <h2>{{ resultData.title }}</h2>\r\n        </div>\r\n        <div class=\"result-body\">\r\n          <p>{{ resultData.message }}</p>\r\n          <div class=\"result-details\" v-if=\"resultData.details\">\r\n            <div v-for=\"detail in resultData.details\" :key=\"detail.label\">\r\n              <strong>{{ detail.label }}:</strong> {{ detail.value }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"result-actions\">\r\n          <button @click=\"closeResult\" class=\"btn-primary\">确定</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 断线重连提示 -->\r\n    <div class=\"reconnect-overlay\" v-if=\"showReconnect\">\r\n      <div class=\"reconnect-content\">\r\n        <div class=\"reconnect-icon\">\r\n          <i class=\"fas fa-wifi\"></i>\r\n        </div>\r\n        <h3>连接中断</h3>\r\n        <p>正在尝试重新连接到战斗服务器...</p>\r\n        <div class=\"reconnect-progress\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: `${reconnectProgress}%` }\"></div>\r\n          </div>\r\n          <span>{{ reconnectAttempts }}/{{ maxReconnectAttempts }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BattlefieldGrid from './BattlefieldGrid.vue'\r\n\r\nexport default {\r\n  name: 'ForcedCombatMode',\r\n  components: {\r\n    BattlefieldGrid\r\n  },\r\n  props: {\r\n    isActive: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isKeeper: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    combatData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 战斗状态\r\n      participants: [],\r\n      currentTurn: 0,\r\n      currentRound: 1,\r\n      selectedParticipant: null,\r\n      targetParticipant: null,\r\n      \r\n      // 玩家操作\r\n      selectedAction: null,\r\n      actionData: null,\r\n      \r\n      // 界面状态\r\n      showResult: false,\r\n      resultData: {},\r\n      showReconnect: false,\r\n      reconnectProgress: 0,\r\n      reconnectAttempts: 0,\r\n      maxReconnectAttempts: 5,\r\n      \r\n      // 连接状态\r\n      connectionStatus: 'connected',\r\n      \r\n      // 战场配置\r\n      battlefieldSize: { width: 20, height: 15 }\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    currentParticipant() {\r\n      return this.participants[this.currentTurn] || null\r\n    },\r\n    \r\n    isPlayerTurn() {\r\n      return this.currentParticipant?.isPlayer && \r\n             this.currentParticipant?.playerId === this.$store.state.auth.user?.id\r\n    },\r\n    \r\n    currentTurnInfo() {\r\n      if (!this.currentParticipant) return '等待开始'\r\n      return `${this.currentParticipant.name} 的回合`\r\n    },\r\n    \r\n    connectionText() {\r\n      const texts = {\r\n        connected: '已连接',\r\n        connecting: '连接中',\r\n        disconnected: '已断开',\r\n        error: '连接错误'\r\n      }\r\n      return texts[this.connectionStatus] || '未知状态'\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 参与者操作\r\n    selectParticipant(participant) {\r\n      this.selectedParticipant = participant\r\n      this.$emit('participant-selected', participant)\r\n    },\r\n    \r\n    handleParticipantClick(participant) {\r\n      if (this.selectedAction === 'attack') {\r\n        this.targetParticipant = participant\r\n      } else {\r\n        this.selectParticipant(participant)\r\n      }\r\n    },\r\n    \r\n    handlePositionClick(position) {\r\n      if (this.selectedAction === 'move') {\r\n        this.actionData = { targetPosition: position }\r\n      }\r\n    },\r\n    \r\n    handleParticipantMove(participant, newPosition) {\r\n      this.$emit('participant-moved', participant, newPosition)\r\n    },\r\n    \r\n    // 行动选择\r\n    selectAction(actionType) {\r\n      this.selectedAction = actionType\r\n      this.actionData = null\r\n      this.targetParticipant = null\r\n      \r\n      // 根据行动类型设置界面状态\r\n      switch (actionType) {\r\n        case 'attack':\r\n          this.showMessage('请选择攻击目标')\r\n          break\r\n        case 'move':\r\n          this.showMessage('请选择移动位置')\r\n          break\r\n        case 'defend':\r\n          this.actionData = { type: 'full_defense' }\r\n          break\r\n        case 'item':\r\n          this.showItemSelection()\r\n          break\r\n        case 'maneuver':\r\n          this.showManeuverSelection()\r\n          break\r\n      }\r\n    },\r\n    \r\n    confirmAction() {\r\n      if (!this.selectedAction) return\r\n      \r\n      const actionData = {\r\n        type: this.selectedAction,\r\n        participant: this.currentParticipant,\r\n        target: this.targetParticipant,\r\n        data: this.actionData\r\n      }\r\n      \r\n      this.$emit('action-confirmed', actionData)\r\n      this.resetActionState()\r\n    },\r\n    \r\n    cancelAction() {\r\n      this.resetActionState()\r\n    },\r\n    \r\n    resetActionState() {\r\n      this.selectedAction = null\r\n      this.actionData = null\r\n      this.targetParticipant = null\r\n    },\r\n    \r\n    // KP控制\r\n    nextTurn() {\r\n      this.$emit('next-turn')\r\n    },\r\n    \r\n    pauseCombat() {\r\n      this.$emit('pause-combat')\r\n    },\r\n    \r\n    endCombat() {\r\n      this.$emit('end-combat')\r\n    },\r\n    \r\n    // 界面辅助\r\n    getHealthPercentage(participant) {\r\n      if (!participant.maxHP) return 0\r\n      return Math.max(0, (participant.currentHP / participant.maxHP) * 100)\r\n    },\r\n    \r\n    getSanityPercentage(participant) {\r\n      if (!participant.maxSAN) return 0\r\n      return Math.max(0, (participant.currentSAN / participant.maxSAN) * 100)\r\n    },\r\n    \r\n    getStatusEffectName(effect) {\r\n      const names = {\r\n        bleeding: '流血',\r\n        poisoned: '中毒',\r\n        stunned: '眩晕',\r\n        frightened: '恐惧',\r\n        blessed: '祝福',\r\n        cursed: '诅咒',\r\n        prone: '倒地',\r\n        grappled: '被擒抱',\r\n        unconscious: '昏迷'\r\n      }\r\n      return names[effect] || effect\r\n    },\r\n    \r\n    showMessage(message) {\r\n      // 显示临时消息\r\n      this.$emit('show-message', message)\r\n    },\r\n    \r\n    showItemSelection() {\r\n      // 显示道具选择界面\r\n      this.$emit('show-item-selection')\r\n    },\r\n    \r\n    showManeuverSelection() {\r\n      // 显示战技选择界面\r\n      this.$emit('show-maneuver-selection')\r\n    },\r\n    \r\n    // 结果处理\r\n    showCombatResult(resultData) {\r\n      this.resultData = resultData\r\n      this.showResult = true\r\n    },\r\n    \r\n    closeResult() {\r\n      this.showResult = false\r\n      this.resultData = {}\r\n    },\r\n    \r\n    // 连接管理\r\n    handleConnectionLost() {\r\n      this.connectionStatus = 'disconnected'\r\n      this.showReconnect = true\r\n      this.startReconnectAttempts()\r\n    },\r\n    \r\n    startReconnectAttempts() {\r\n      this.reconnectAttempts = 0\r\n      this.reconnectProgress = 0\r\n      \r\n      const attemptReconnect = () => {\r\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n          this.handleReconnectFailed()\r\n          return\r\n        }\r\n        \r\n        this.reconnectAttempts++\r\n        this.reconnectProgress = (this.reconnectAttempts / this.maxReconnectAttempts) * 100\r\n        \r\n        // 尝试重连\r\n        this.$emit('reconnect-attempt')\r\n        \r\n        setTimeout(() => {\r\n          if (this.connectionStatus !== 'connected') {\r\n            attemptReconnect()\r\n          } else {\r\n            this.handleReconnectSuccess()\r\n          }\r\n        }, 2000)\r\n      }\r\n      \r\n      attemptReconnect()\r\n    },\r\n    \r\n    handleReconnectSuccess() {\r\n      this.showReconnect = false\r\n      this.connectionStatus = 'connected'\r\n      this.showMessage('重新连接成功')\r\n    },\r\n    \r\n    handleReconnectFailed() {\r\n      this.showReconnect = false\r\n      this.showCombatResult({\r\n        title: '连接失败',\r\n        message: '无法重新连接到战斗服务器，战斗将被强制结束。',\r\n        details: [\r\n          { label: '错误代码', value: 'CONNECTION_TIMEOUT' },\r\n          { label: '尝试次数', value: this.maxReconnectAttempts }\r\n        ]\r\n      })\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    combatData: {\r\n      handler(newData) {\r\n        if (newData) {\r\n          this.participants = newData.participants || []\r\n          this.currentTurn = newData.currentTurn || 0\r\n          this.currentRound = newData.currentRound || 1\r\n          this.battlefieldSize = newData.battlefieldSize || { width: 20, height: 15 }\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    },\r\n    \r\n    isActive(newVal) {\r\n      if (newVal) {\r\n        // 进入强制战斗模式\r\n        document.body.style.overflow = 'hidden'\r\n        this.$emit('combat-mode-entered')\r\n      } else {\r\n        // 退出强制战斗模式\r\n        document.body.style.overflow = ''\r\n        this.$emit('combat-mode-exited')\r\n      }\r\n    }\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    // 清理样式\r\n    document.body.style.overflow = ''\r\n  }\r\n}\r\n</script>"], "mappings": ";;;;;;;;;EAEO,SAAM;;;EAKJ,SAAM;AAAkB;;EAEtB,SAAM;AAAmB;;EACvB,SAAM;AAAa;;EAMjB,SAAM;AAAY;;EACf,SAAM;AAAc;;EACpB,SAAM;AAAW;;EAItB,SAAM;AAAc;;EAStB,SAAM;AAAkB;;EAEtB,SAAM;AAAuB;;EAgB7B,SAAM;AAAY;;;EAEhB,SAAM;;;EACJ,SAAM;AAAc;;EAOpB,SAAM;AAAiB;;EACrB,SAAM;AAAU;;EAEd,SAAM;AAAqB;;EAKxB,SAAM;AAAe;;;EAM1B,SAAM;;;EAEJ,SAAM;AAAqB;;EAKxB,SAAM;AAAe;;;EAM1B,SAAM;;;EAEJ,SAAM;AAAc;;EAe1B,SAAM;AAAkB;;EAItB,SAAM;AAAiB;;;EAYnB,SAAM;AAAmB;;EACzB,SAAM;AAAkB;;EACrB,SAAM;AAAkB;;EACzB,SAAM;AAAoB;;;EAI5B,SAAM;;;EAUhB,SAAM;AAAmB;;;EAEvB,SAAM;;;EACJ,SAAM;AAAgB;;EAEpB,SAAM;AAAgB;;;EA6CxB,SAAM;;;EAaR,SAAM;AAAmB;;EACvB,SAAM;AAAiB;;EAErB,SAAM;AAAc;;;EAQxB,SAAM;;;EACJ,SAAM;AAAiB;;EAErB,SAAM;AAAgB;;;EAoB9B,SAAM;;;EACJ,SAAM;AAAgB;;EACpB,SAAM;AAAe;;EAGrB,SAAM;AAAa;;;EAEjB,SAAM;;;EAMR,SAAM;AAAgB;;;EAO1B,SAAM;;;EACJ,SAAM;AAAmB;;EAMvB,SAAM;AAAoB;;EACxB,SAAM;AAAc;;;;6DApQjCA,mBAAA,gBAAmB,EACsBC,MAAA,CAAAC,QAAQ,I,cAAjDC,mBAAA,CA0QM,OA1QNC,UA0QM,GAzQJJ,mBAAA,UAAa,E,4BACbK,mBAAA,CAAmC;IAA9B,SAAM;EAAiB,4BAE5BL,mBAAA,YAAe,EACfK,mBAAA,CA+NM,OA/NNC,UA+NM,GA9NJN,mBAAA,WAAc,EACdK,mBAAA,CAmBM,OAnBNE,UAmBM,GAlBJF,mBAAA,CAUM,OAVNG,UAUM,G,4BATJH,mBAAA,CAIM;IAJD,SAAM;EAAkB,IAC3BA,mBAAA,CAA4B;IAAzB,SAAM;EAAc,IACvBA,mBAAA,CAAqC;IAA/B,SAAM;EAAa,GAAC,MAAI,GAC9BA,mBAAA,CAA8B;IAAzB,SAAM;EAAY,G,qBAEzBA,mBAAA,CAGM,OAHNI,UAGM,GAFJJ,mBAAA,CAAwD,QAAxDK,UAAwD,EAA7B,IAAE,GAAAC,gBAAA,CAAGC,KAAA,CAAAC,YAAY,IAAG,IAAE,iBACjDR,mBAAA,CAAoD,QAApDS,UAAoD,EAAAH,gBAAA,CAAzBI,QAAA,CAAAC,eAAe,iB,KAI9CX,mBAAA,CAKM,OALNY,UAKM,GAJJZ,mBAAA,CAGM;IAHD,SAAKa,eAAA,EAAC,mBAAmB,EAASN,KAAA,CAAAO,gBAAgB;kCACrDd,mBAAA,CAA2B;IAAxB,SAAM;EAAa,4BACtBA,mBAAA,CAAiC,cAAAM,gBAAA,CAAxBI,QAAA,CAAAK,cAAc,iB,sBAK7BpB,mBAAA,WAAc,EACdK,mBAAA,CAsGM,OAtGNgB,UAsGM,GArGJrB,mBAAA,UAAa,EACbK,mBAAA,CAaM,OAbNiB,WAaM,GAZJC,YAAA,CAWEC,0BAAA;IAVCC,UAAU,EAAE,EAAAC,mBAAA,GAAAd,KAAA,CAAAe,YAAY,cAAAD,mBAAA,uBAAZA,mBAAA,CAAcE,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,QAAQ;IAAA;IAChDC,QAAQ,EAAE,EAAAC,oBAAA,GAAApB,KAAA,CAAAe,YAAY,cAAAK,oBAAA,uBAAZA,oBAAA,CAAcJ,MAAM,CAAC,UAAAC,CAAC;MAAA,QAAKA,CAAC,CAACC,QAAQ;IAAA;IAC/C,oBAAkB,EAAE,CAAAG,qBAAA,GAAArB,KAAA,CAAAsB,mBAAmB,cAAAD,qBAAA,eAAnBA,qBAAA,CAAqBH,QAAQ,GAAGlB,KAAA,CAAAsB,mBAAmB;IACvE,kBAAgB,KAAAC,sBAAA,GAAGvB,KAAA,CAAAsB,mBAAmB,cAAAC,sBAAA,eAAnBA,sBAAA,CAAqBL,QAAQ,IAAGlB,KAAA,CAAAsB,mBAAmB;IACtE,cAAY,EAAEtB,KAAA,CAAAwB,WAAW;IACzB,oBAAkB,EAAExB,KAAA,CAAAyB,iBAAiB;IACrC,kBAAgB,EAAEzB,KAAA,CAAA0B,eAAe;IACjCC,oBAAmB,EAAExB,QAAA,CAAAyB,sBAAsB;IAC3CC,iBAAgB,EAAE1B,QAAA,CAAA2B,mBAAmB;IACrCC,kBAAiB,EAAE5B,QAAA,CAAA6B;gOAIxB5C,mBAAA,YAAe,EACfK,mBAAA,CAmFM,OAnFNwC,WAmFM,GAlFJ7C,mBAAA,YAAe,EAC4Be,QAAA,CAAA+B,kBAAkB,I,cAA7D3C,mBAAA,CAiDM,OAjDN4C,WAiDM,GAhDJ1C,mBAAA,CAKM,OALN2C,WAKM,GAJJ3C,mBAAA,CAAsC,YAAAM,gBAAA,CAA/BI,QAAA,CAAA+B,kBAAkB,CAACG,IAAI,kBAC9B5C,mBAAA,CAEM;IAFD,SAAKa,eAAA,EAAC,gBAAgB;MAAAgC,MAAA,EAAmBnC,QAAA,CAAA+B,kBAAkB,CAAChB;IAAQ;sBACpEf,QAAA,CAAA+B,kBAAkB,CAAChB,QAAQ,uC,GAIlCzB,mBAAA,CAwCM,OAxCN8C,WAwCM,GAvCJ9C,mBAAA,CAWM,OAXN+C,WAWM,G,4BAVJ/C,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAQM,OARNgD,WAQM,GAPJhD,mBAAA,CAGO;IAFL,SAAM,eAAe;IACpBiD,KAAK,EAAAC,eAAA;MAAAC,KAAA,KAAAC,MAAA,CAAc1C,QAAA,CAAA2C,mBAAmB,CAAC3C,QAAA,CAAA+B,kBAAkB;IAAA;2BAE5DzC,mBAAA,CAEO,QAFPsD,WAEO,EAAAhD,gBAAA,CADFI,QAAA,CAAA+B,kBAAkB,CAACc,SAAS,IAAG,GAAC,GAAAjD,gBAAA,CAAGI,QAAA,CAAA+B,kBAAkB,CAACe,KAAK,iB,KAKxC9C,QAAA,CAAA+B,kBAAkB,CAACgB,UAAU,I,cAAzD3D,mBAAA,CAWM,OAXN4D,WAWM,G,4BAVJ1D,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAQM,OARN2D,WAQM,GAPJ3D,mBAAA,CAGO;IAFL,SAAM,eAAe;IACpBiD,KAAK,EAAAC,eAAA;MAAAC,KAAA,KAAAC,MAAA,CAAc1C,QAAA,CAAAkD,mBAAmB,CAAClD,QAAA,CAAA+B,kBAAkB;IAAA;2BAE5DzC,mBAAA,CAEO,QAFP6D,WAEO,EAAAvD,gBAAA,CADFI,QAAA,CAAA+B,kBAAkB,CAACgB,UAAU,IAAG,GAAC,GAAAnD,gBAAA,CAAGI,QAAA,CAAA+B,kBAAkB,CAACqB,MAAM,iB,mEAKpCpD,QAAA,CAAA+B,kBAAkB,CAACsB,UAAU,cAAAC,qBAAA,eAA7BA,qBAAA,CAA+BC,MAAM,I,cAAvEnE,mBAAA,CAYM,OAZNoE,WAYM,G,4BAXJlE,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CASM,OATNmE,WASM,I,kBARJrE,mBAAA,CAOOsE,SAAA,QAAAC,WAAA,CANY3D,QAAA,CAAA+B,kBAAkB,CAACsB,UAAU,YAAvCO,MAAM;yBADfxE,mBAAA,CAOO;MALJyE,GAAG,EAAED,MAAM;MACZ,SAAKzD,eAAA,EAAC,cAAc,EACZyD,MAAM;wBAEX5D,QAAA,CAAA8D,mBAAmB,CAACF,MAAM;oHAOvC3E,mBAAA,UAAa,EACbK,mBAAA,CA4BM,OA5BNyE,WA4BM,G,4BA3BJzE,mBAAA,CAEM;IAFD,SAAM;EAAc,IACvBA,mBAAA,CAAa,YAAT,MAAI,E,qBAEVA,mBAAA,CAuBM,OAvBN0E,WAuBM,I,kBAtBJ5E,mBAAA,CAqBMsE,SAAA,QAAAC,WAAA,CApB2B9D,KAAA,CAAAe,YAAY,YAAnCqD,WAAW,EAAEC,KAAK;IAAA,IAAAC,sBAAA;yBAD5B/E,mBAAA,CAqBM;MAnBHyE,GAAG,EAAEI,WAAW,CAACG,EAAE;MACpB,SAAKjE,eAAA,EAAC,iBAAiB;iBACgB+D,KAAK,KAAKrE,KAAA,CAAAwB,WAAW;gBAA6B4C,WAAW,CAAClD,QAAQ;kBAA+B,EAAAoD,sBAAA,GAAAtE,KAAA,CAAAsB,mBAAmB,cAAAgD,sBAAA,uBAAnBA,sBAAA,CAAqBC,EAAE,MAAKH,WAAW,CAACG;;MAKnLC,OAAK,WAALA,OAAKA,CAAAC,MAAA;QAAA,OAAEtE,QAAA,CAAAuE,iBAAiB,CAACN,WAAW;MAAA;QAErC3E,mBAAA,CAAiE,OAAjEkF,WAAiE,EAAA5E,gBAAA,CAA/BqE,WAAW,CAACQ,UAAU,kBACxDnF,mBAAA,CAKM,OALNoF,WAKM,GAJJpF,mBAAA,CAA4D,QAA5DqF,WAA4D,EAAA/E,gBAAA,CAA1BqE,WAAW,CAAC/B,IAAI,kBAClD5C,mBAAA,CAEM,OAFNsF,WAEM,EAAAhF,gBAAA,CADDqE,WAAW,CAACpB,SAAS,IAAG,GAAC,GAAAjD,gBAAA,CAAGqE,WAAW,CAACnB,KAAK,iB,GAGlBoB,KAAK,KAAKrE,KAAA,CAAAwB,WAAW,I,cAAvDjC,mBAAA,CAEM,OAFNyF,WAEM,EAAAC,kBAAA,CAAAC,MAAA,SAAAA,MAAA,QADJzF,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B;0CAQlCL,mBAAA,WAAc,EACdK,mBAAA,CA6FM,OA7FN0F,WA6FM,GA5FJ/F,mBAAA,uBAA0B,EACQe,QAAA,CAAAiF,YAAY,KAAK/F,MAAA,CAAAgG,QAAQ,I,cAA3D9F,mBAAA,CA0DM,OA1DN+F,WA0DM,GAzDJ7F,mBAAA,CA4CM,OA5CN8F,WA4CM,G,4BA3CJ9F,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAyCM,OAzCN+F,WAyCM,GAxCJ/F,mBAAA,CAOS;IANN+E,OAAK,EAAAU,MAAA,QAAAA,MAAA,gBAAAT,MAAA;MAAA,OAAEtE,QAAA,CAAAsF,YAAY;IAAA;IACpB,SAAKnF,eAAA,EAAC,mBAAmB;MAAAoF,MAAA,EACP1F,KAAA,CAAA2F,cAAc;IAAA;kCAEhClG,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAAe,cAAT,IAAE,mB,mBAEVA,mBAAA,CAOS;IANN+E,OAAK,EAAAU,MAAA,QAAAA,MAAA,gBAAAT,MAAA;MAAA,OAAEtE,QAAA,CAAAsF,YAAY;IAAA;IACpB,SAAKnF,eAAA,EAAC,mBAAmB;MAAAoF,MAAA,EACP1F,KAAA,CAAA2F,cAAc;IAAA;kCAEhClG,mBAAA,CAA6B;IAA1B,SAAM;EAAe,2BACxBA,mBAAA,CAAe,cAAT,IAAE,mB,mBAEVA,mBAAA,CAOS;IANN+E,OAAK,EAAAU,MAAA,QAAAA,MAAA,gBAAAT,MAAA;MAAA,OAAEtE,QAAA,CAAAsF,YAAY;IAAA;IACpB,SAAKnF,eAAA,EAAC,iBAAiB;MAAAoF,MAAA,EACL1F,KAAA,CAAA2F,cAAc;IAAA;kCAEhClG,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2BACzBA,mBAAA,CAAe,cAAT,IAAE,mB,mBAEVA,mBAAA,CAOS;IANN+E,OAAK,EAAAU,MAAA,QAAAA,MAAA,gBAAAT,MAAA;MAAA,OAAEtE,QAAA,CAAAsF,YAAY;IAAA;IACpB,SAAKnF,eAAA,EAAC,iBAAiB;MAAAoF,MAAA,EACL1F,KAAA,CAAA2F,cAAc;IAAA;kCAEhClG,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAAe,cAAT,IAAE,mB,mBAEVA,mBAAA,CAOS;IANN+E,OAAK,EAAAU,MAAA,QAAAA,MAAA,gBAAAT,MAAA;MAAA,OAAEtE,QAAA,CAAAsF,YAAY;IAAA;IACpB,SAAKnF,eAAA,EAAC,qBAAqB;MAAAoF,MAAA,EACT1F,KAAA,CAAA2F,cAAc;IAAA;kCAEhClG,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,2BAC7BA,mBAAA,CAAe,cAAT,IAAE,mB,uBAKdL,mBAAA,UAAa,EACqBY,KAAA,CAAA2F,cAAc,I,cAAhDpG,mBAAA,CASM,OATNqG,WASM,GARJnG,mBAAA,CAGS;IAHA+E,OAAK,EAAAU,MAAA,QAAAA,MAAA;MAAA,OAAE/E,QAAA,CAAA0F,aAAA,IAAA1F,QAAA,CAAA0F,aAAA,CAAAC,KAAA,CAAA3F,QAAA,EAAA4F,SAAA,CAAa;IAAA;IAAE,SAAM;kCACnCtG,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2B,iBAAK,QAE9B,E,IACAA,mBAAA,CAGS;IAHA+E,OAAK,EAAAU,MAAA,QAAAA,MAAA;MAAA,OAAE/E,QAAA,CAAA6F,YAAA,IAAA7F,QAAA,CAAA6F,YAAA,CAAAF,KAAA,CAAA3F,QAAA,EAAA4F,SAAA,CAAY;IAAA;IAAE,SAAM;kCAClCtG,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2B,iBAAK,MAE9B,E,gDAKuCJ,MAAA,CAAAgG,QAAQ,I,cAAnD9F,mBAAA,CAQMsE,SAAA;IAAAG,GAAA;EAAA,IATN5E,mBAAA,oBAAuB,EACvBK,mBAAA,CAQM,OARNwG,WAQM,GAPJxG,mBAAA,CAMM,OANNyG,WAMM,G,4BALJzG,mBAAA,CAA2B;IAAtB,SAAM;EAAS,4BACpBA,mBAAA,CAGM,OAHN0G,WAGM,G,4BAFJ1G,mBAAA,CAAiB,YAAb,UAAQ,qBACZA,mBAAA,CAAmD,WAAhD,QAAM,GAAAM,gBAAA,CAAG,EAAAqG,sBAAA,GAAAjG,QAAA,CAAA+B,kBAAkB,cAAAkE,sBAAA,uBAAlBA,sBAAA,CAAoB/D,IAAI,0B,6FAK1CjD,mBAAA,WAAc,EACqBC,MAAA,CAAAgG,QAAQ,I,cAA3C9F,mBAAA,CAkBM,OAlBN8G,WAkBM,GAjBJ5G,mBAAA,CAgBM,OAhBN6G,WAgBM,G,4BAfJ7G,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAaM,OAbN8G,WAaM,GAZJ9G,mBAAA,CAGS;IAHA+E,OAAK,EAAAU,MAAA,QAAAA,MAAA;MAAA,OAAE/E,QAAA,CAAAqG,QAAA,IAAArG,QAAA,CAAAqG,QAAA,CAAAV,KAAA,CAAA3F,QAAA,EAAA4F,SAAA,CAAQ;IAAA;IAAE,SAAM;kCAC9BtG,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2B,iBAAK,QAEhC,E,IACAA,mBAAA,CAGS;IAHA+E,OAAK,EAAAU,MAAA,QAAAA,MAAA;MAAA,OAAE/E,QAAA,CAAAsG,WAAA,IAAAtG,QAAA,CAAAsG,WAAA,CAAAX,KAAA,CAAA3F,QAAA,EAAA4F,SAAA,CAAW;IAAA;IAAE,SAAM;kCACjCtG,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2B,iBAAK,QAE9B,E,IACAA,mBAAA,CAGS;IAHA+E,OAAK,EAAAU,MAAA,QAAAA,MAAA;MAAA,OAAE/E,QAAA,CAAAuG,SAAA,IAAAvG,QAAA,CAAAuG,SAAA,CAAAZ,KAAA,CAAA3F,QAAA,EAAA4F,SAAA,CAAS;IAAA;IAAE,SAAM;kCAC/BtG,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2B,iBAAK,QAE7B,E,mDAOVL,mBAAA,YAAe,EACwBY,KAAA,CAAA2G,UAAU,I,cAAjDpH,mBAAA,CAiBM,OAjBNqH,WAiBM,GAhBJnH,mBAAA,CAeM,OAfNoH,WAeM,GAdJpH,mBAAA,CAEM,OAFNqH,WAEM,GADJrH,mBAAA,CAA+B,YAAAM,gBAAA,CAAxBC,KAAA,CAAA+G,UAAU,CAACC,KAAK,iB,GAEzBvH,mBAAA,CAOM,OAPNwH,WAOM,GANJxH,mBAAA,CAA+B,WAAAM,gBAAA,CAAzBC,KAAA,CAAA+G,UAAU,CAACG,OAAO,kBACUlH,KAAA,CAAA+G,UAAU,CAACI,OAAO,I,cAApD5H,mBAAA,CAIM,OAJN6H,WAIM,I,kBAHJ7H,mBAAA,CAEMsE,SAAA,QAAAC,WAAA,CAFgB9D,KAAA,CAAA+G,UAAU,CAACI,OAAO,YAA5BE,MAAM;yBAAlB9H,mBAAA,CAEM;MAFqCyE,GAAG,EAAEqD,MAAM,CAACC;QACrD7H,mBAAA,CAAoC,gBAAAM,gBAAA,CAAzBsH,MAAM,CAACC,KAAK,IAAG,GAAC,iB,iBAAS,GAAC,GAAAvH,gBAAA,CAAGsH,MAAM,CAACE,KAAK,iB;2EAI1D9H,mBAAA,CAEM,OAFN+H,WAEM,GADJ/H,mBAAA,CAA4D;IAAnD+E,OAAK,EAAAU,MAAA,SAAAA,MAAA;MAAA,OAAE/E,QAAA,CAAAsH,WAAA,IAAAtH,QAAA,CAAAsH,WAAA,CAAA3B,KAAA,CAAA3F,QAAA,EAAA4F,SAAA,CAAW;IAAA;IAAE,SAAM;KAAc,IAAE,E,4CAKzD3G,mBAAA,YAAe,EACsBY,KAAA,CAAA0H,aAAa,I,cAAlDnI,mBAAA,CAcM,OAdNoI,WAcM,GAbJlI,mBAAA,CAYM,OAZNmI,WAYM,G,4BAXJnI,mBAAA,CAEM;IAFD,SAAM;EAAgB,IACzBA,mBAAA,CAA2B;IAAxB,SAAM;EAAa,G,iDAExBA,mBAAA,CAAa,YAAT,MAAI,qB,4BACRA,mBAAA,CAAwB,WAArB,mBAAiB,qBACpBA,mBAAA,CAKM,OALNoI,WAKM,GAJJpI,mBAAA,CAEM,OAFNqI,WAEM,GADJrI,mBAAA,CAA6E;IAAxE,SAAM,eAAe;IAAEiD,KAAK,EAAAC,eAAA;MAAAC,KAAA,KAAAC,MAAA,CAAc7C,KAAA,CAAA+H,iBAAiB;IAAA;6BAElEtI,mBAAA,CAA+D,cAAAM,gBAAA,CAAtDC,KAAA,CAAAgI,iBAAiB,IAAG,GAAC,GAAAjI,gBAAA,CAAGC,KAAA,CAAAiI,oBAAoB,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}