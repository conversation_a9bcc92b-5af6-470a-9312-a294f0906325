-- COC 7版战斗系统数据库表结构设计
-- 支持完整的战斗数据存储、同步和恢复

-- ===== 战斗会话表 =====
CREATE TABLE combat_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    room_id INT NOT NULL,
    session_name VARCHAR(255) NOT NULL,
    status ENUM('preparing', 'active', 'paused', 'completed', 'aborted') DEFAULT 'preparing',
    
    -- 战场配置
    battlefield_config JSON NOT NULL COMMENT '战场配置(大小、环境、光照等)',
    
    -- 参与者信息
    participants JSON NOT NULL COMMENT '参与者列表(玩家角色和NPC/怪物)',
    
    -- 战斗状态
    current_round INT DEFAULT 1,
    current_turn INT DEFAULT 0,
    initiative_order JSON COMMENT '先攻顺序',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP NULL,
    ended_at TIMESTAMP NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 创建者
    created_by INT NOT NULL COMMENT 'KP的用户ID',
    
    -- 索引
    INDEX idx_room_id (room_id),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='战斗会话主表，记录每次战斗的基本信息';

-- ===== 战斗行动记录表 =====
CREATE TABLE combat_actions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    round_number INT NOT NULL,
    turn_number INT NOT NULL,
    
    -- 行动者信息
    actor_type ENUM('player', 'npc', 'monster') NOT NULL,
    actor_id VARCHAR(100) NOT NULL COMMENT '角色ID或怪物实例ID',
    actor_name VARCHAR(255) NOT NULL,
    
    -- 行动类型和详情
    action_type ENUM(
        'attack', 'defend', 'move', 'use_item', 'cast_spell', 
        'maneuver', 'reload', 'aim', 'wait', 'delay', 'flee'
    ) NOT NULL,
    action_subtype VARCHAR(100) COMMENT '具体行动子类型(如攻击方式)',
    
    -- 目标信息
    target_type ENUM('player', 'npc', 'monster', 'position', 'area') COMMENT '目标类型',
    target_id VARCHAR(100) COMMENT '目标ID',
    target_name VARCHAR(255) COMMENT '目标名称',
    
    -- 行动数据
    action_data JSON NOT NULL COMMENT '完整的行动数据(武器、技能、结果等)',
    
    -- 骰子结果
    dice_rolls JSON COMMENT '所有相关的骰子投掷结果',
    
    -- 行动结果
    success_level ENUM('critical', 'extreme', 'hard', 'regular', 'failure', 'fumble'),
    damage_dealt INT DEFAULT 0,
    damage_taken INT DEFAULT 0,
    
    -- 状态变化
    status_effects JSON COMMENT '造成或移除的状态效果',
    attribute_changes JSON COMMENT '属性变化(生命值、理智值等)',
    
    -- 位置信息
    position_before JSON COMMENT '行动前位置',
    position_after JSON COMMENT '行动后位置',
    
    -- 时间戳
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_session_id (session_id),
    INDEX idx_round_turn (session_id, round_number, turn_number),
    INDEX idx_actor (actor_type, actor_id),
    INDEX idx_action_type (action_type),
    INDEX idx_executed_at (executed_at),
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='战斗行动记录表，记录每个行动的详细信息';

-- ===== 角色战斗状态快照表 =====
CREATE TABLE character_combat_snapshots (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    character_id INT NOT NULL,
    snapshot_type ENUM('initial', 'round_start', 'round_end', 'action_before', 'action_after', 'final') NOT NULL,
    round_number INT NOT NULL,
    turn_number INT DEFAULT 0,
    
    -- 基础属性快照
    attributes JSON NOT NULL COMMENT '所有属性值',
    skills JSON NOT NULL COMMENT '所有技能值',
    
    -- 当前状态
    current_hp INT NOT NULL,
    current_mp INT NOT NULL,
    current_san INT NOT NULL,
    current_luck INT NOT NULL,
    
    -- 装备状态
    equipped_weapons JSON COMMENT '装备的武器',
    equipped_armor JSON COMMENT '装备的护甲',
    inventory JSON COMMENT '背包物品',
    
    -- 战斗状态
    conditions JSON COMMENT '当前状态效果',
    temporary_modifiers JSON COMMENT '临时修正值',
    position JSON COMMENT '战场位置',
    facing INT DEFAULT 0 COMMENT '面向角度',
    
    -- 行动状态
    has_acted BOOLEAN DEFAULT FALSE,
    action_points INT DEFAULT 1,
    initiative_value INT COMMENT '先攻值',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_session_character (session_id, character_id),
    INDEX idx_snapshot_type (snapshot_type),
    INDEX idx_round_turn (session_id, round_number, turn_number),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES combat_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='角色战斗状态快照表，记录角色在战斗中的状态变化';

-- ===== 怪物战斗实例表 =====
CREATE TABLE monster_combat_instances (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    monster_template_id VARCHAR(100) NOT NULL COMMENT '怪物模板ID',
    instance_name VARCHAR(255) NOT NULL,
    
    -- 基础属性
    attributes JSON NOT NULL COMMENT '怪物属性',
    skills JSON NOT NULL COMMENT '怪物技能',
    
    -- 当前状态
    current_hp INT NOT NULL,
    current_mp INT NOT NULL,
    current_san INT DEFAULT 0,
    
    -- 战斗数据
    attacks JSON NOT NULL COMMENT '攻击方式',
    special_abilities JSON COMMENT '特殊能力',
    resistances JSON COMMENT '抗性',
    weaknesses JSON COMMENT '弱点',
    
    -- AI配置
    ai_type VARCHAR(50) NOT NULL,
    ai_config JSON COMMENT 'AI配置参数',
    morale INT DEFAULT 50,
    
    -- 战斗状态
    conditions JSON COMMENT '当前状态效果',
    position JSON COMMENT '战场位置',
    facing INT DEFAULT 0,
    has_acted BOOLEAN DEFAULT FALSE,
    initiative_value INT,
    
    -- 状态
    status ENUM('active', 'unconscious', 'dead', 'fled') DEFAULT 'active',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_session_id (session_id),
    INDEX idx_template_id (monster_template_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='怪物战斗实例表，记录战斗中的怪物状态';

-- ===== 战斗事件日志表 =====
CREATE TABLE combat_event_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    event_type ENUM(
        'session_start', 'session_end', 'session_pause', 'session_resume',
        'round_start', 'round_end', 'turn_start', 'turn_end',
        'action_executed', 'damage_dealt', 'status_applied', 'status_removed',
        'character_unconscious', 'character_dead', 'character_fled',
        'monster_spawned', 'monster_defeated', 'monster_fled',
        'special_event', 'error_occurred'
    ) NOT NULL,
    
    -- 事件详情
    event_data JSON NOT NULL COMMENT '事件的详细数据',
    event_description TEXT COMMENT '事件描述',
    
    -- 相关实体
    related_character_id INT COMMENT '相关角色ID',
    related_monster_id INT COMMENT '相关怪物ID',
    related_action_id INT COMMENT '相关行动ID',
    
    -- 时间戳
    occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_session_id (session_id),
    INDEX idx_event_type (event_type),
    INDEX idx_occurred_at (occurred_at),
    INDEX idx_related_character (related_character_id),
    INDEX idx_related_monster (related_monster_id),
    INDEX idx_related_action (related_action_id),
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES combat_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (related_character_id) REFERENCES characters(id) ON DELETE SET NULL,
    FOREIGN KEY (related_action_id) REFERENCES combat_actions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='战斗事件日志表，记录战斗中的所有重要事件';

-- ===== 战斗统计表 =====
CREATE TABLE combat_statistics (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    character_id INT COMMENT '角色ID(NULL表示整体统计)',
    monster_instance_id INT COMMENT '怪物实例ID',
    
    -- 行动统计
    total_actions INT DEFAULT 0,
    successful_actions INT DEFAULT 0,
    failed_actions INT DEFAULT 0,
    critical_successes INT DEFAULT 0,
    fumbles INT DEFAULT 0,
    
    -- 攻击统计
    attacks_made INT DEFAULT 0,
    attacks_hit INT DEFAULT 0,
    attacks_missed INT DEFAULT 0,
    total_damage_dealt INT DEFAULT 0,
    total_damage_taken INT DEFAULT 0,
    
    -- 防御统计
    dodges_attempted INT DEFAULT 0,
    dodges_successful INT DEFAULT 0,
    blocks_attempted INT DEFAULT 0,
    blocks_successful INT DEFAULT 0,
    parries_attempted INT DEFAULT 0,
    parries_successful INT DEFAULT 0,
    
    -- 其他统计
    items_used INT DEFAULT 0,
    spells_cast INT DEFAULT 0,
    distance_moved DECIMAL(10,2) DEFAULT 0,
    rounds_survived INT DEFAULT 0,
    
    -- 最终状态
    final_hp INT,
    final_mp INT,
    final_san INT,
    survival_status ENUM('survived', 'unconscious', 'dead', 'fled'),
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_session_id (session_id),
    INDEX idx_character_id (character_id),
    INDEX idx_monster_instance_id (monster_instance_id),
    INDEX idx_survival_status (survival_status),
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES combat_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (character_id) REFERENCES characters(id) ON DELETE CASCADE,
    FOREIGN KEY (monster_instance_id) REFERENCES monster_combat_instances(id) ON DELETE CASCADE,
    
    -- 唯一约束
    UNIQUE KEY unique_session_character (session_id, character_id),
    UNIQUE KEY unique_session_monster (session_id, monster_instance_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='战斗统计表，记录战斗中的各种统计数据';

-- ===== 战斗地图数据表 =====
CREATE TABLE combat_maps (
    id INT PRIMARY KEY AUTO_INCREMENT,
    session_id INT NOT NULL,
    map_name VARCHAR(255) NOT NULL,
    
    -- 地图配置
    width INT NOT NULL DEFAULT 20,
    height INT NOT NULL DEFAULT 15,
    grid_size DECIMAL(4,2) DEFAULT 1.5 COMMENT '每格代表的米数',
    
    -- 地图数据
    terrain_data JSON COMMENT '地形数据',
    obstacles JSON COMMENT '障碍物数据',
    special_areas JSON COMMENT '特殊区域(如陷阱、法阵等)',
    
    -- 环境设置
    lighting ENUM('bright', 'normal', 'dim', 'dark', 'pitch_black') DEFAULT 'normal',
    weather VARCHAR(100) COMMENT '天气条件',
    temperature INT COMMENT '温度(摄氏度)',
    
    -- 背景图片
    background_image VARCHAR(500) COMMENT '背景图片URL',
    grid_overlay BOOLEAN DEFAULT TRUE COMMENT '是否显示网格',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_session_id (session_id),
    INDEX idx_map_name (map_name),
    
    -- 外键约束
    FOREIGN KEY (session_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
COMMENT='战斗地图数据表，存储战场地图信息';

-- ===== 创建视图：当前战斗状态 =====
CREATE VIEW current_combat_status AS
SELECT 
    cs.id as session_id,
    cs.room_id,
    cs.session_name,
    cs.status,
    cs.current_round,
    cs.current_turn,
    cs.battlefield_config,
    cs.participants,
    cs.initiative_order,
    cs.created_by,
    cs.started_at,
    COUNT(DISTINCT ca.id) as total_actions,
    COUNT(DISTINCT ccs.character_id) as active_characters,
    COUNT(DISTINCT mci.id) as active_monsters
FROM combat_sessions cs
LEFT JOIN combat_actions ca ON cs.id = ca.session_id
LEFT JOIN character_combat_snapshots ccs ON cs.id = ccs.session_id 
    AND ccs.snapshot_type = 'round_start' 
    AND ccs.round_number = cs.current_round
LEFT JOIN monster_combat_instances mci ON cs.id = mci.session_id 
    AND mci.status = 'active'
WHERE cs.status IN ('preparing', 'active', 'paused')
GROUP BY cs.id;

-- ===== 创建视图：战斗参与者状态 =====
CREATE VIEW combat_participant_status AS
SELECT 
    cs.id as session_id,
    'character' as participant_type,
    c.id as participant_id,
    c.name as participant_name,
    ccs.current_hp,
    ccs.current_mp,
    ccs.current_san,
    ccs.position,
    ccs.has_acted,
    ccs.conditions,
    'active' as status
FROM combat_sessions cs
JOIN character_combat_snapshots ccs ON cs.id = ccs.session_id
JOIN characters c ON ccs.character_id = c.id
WHERE ccs.snapshot_type = 'round_start' 
    AND ccs.round_number = cs.current_round
    AND cs.status = 'active'

UNION ALL

SELECT 
    cs.id as session_id,
    'monster' as participant_type,
    mci.id as participant_id,
    mci.instance_name as participant_name,
    mci.current_hp,
    mci.current_mp,
    mci.current_san,
    mci.position,
    mci.has_acted,
    mci.conditions,
    mci.status
FROM combat_sessions cs
JOIN monster_combat_instances mci ON cs.id = mci.session_id
WHERE cs.status = 'active'
    AND mci.status IN ('active', 'unconscious');

-- ===== 创建存储过程：开始新战斗 =====
DELIMITER //
CREATE PROCEDURE StartNewCombat(
    IN p_room_id INT,
    IN p_session_name VARCHAR(255),
    IN p_created_by INT,
    IN p_battlefield_config JSON,
    IN p_participants JSON
)
BEGIN
    DECLARE v_session_id INT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 创建战斗会话
    INSERT INTO combat_sessions (
        room_id, session_name, created_by, 
        battlefield_config, participants, status
    ) VALUES (
        p_room_id, p_session_name, p_created_by,
        p_battlefield_config, p_participants, 'preparing'
    );
    
    SET v_session_id = LAST_INSERT_ID();
    
    -- 记录开始事件
    INSERT INTO combat_event_logs (
        session_id, event_type, event_data, event_description
    ) VALUES (
        v_session_id, 'session_start', 
        JSON_OBJECT('session_id', v_session_id, 'participants', p_participants),
        CONCAT('战斗会话 "', p_session_name, '" 已创建')
    );
    
    COMMIT;
    SELECT v_session_id as session_id;
END //
DELIMITER ;

-- ===== 创建存储过程：记录战斗行动 =====
DELIMITER //
CREATE PROCEDURE RecordCombatAction(
    IN p_session_id INT,
    IN p_round_number INT,
    IN p_turn_number INT,
    IN p_actor_type ENUM('player', 'npc', 'monster'),
    IN p_actor_id VARCHAR(100),
    IN p_actor_name VARCHAR(255),
    IN p_action_type VARCHAR(50),
    IN p_action_data JSON,
    IN p_dice_rolls JSON,
    IN p_success_level VARCHAR(20),
    IN p_damage_dealt INT,
    IN p_status_effects JSON
)
BEGIN
    DECLARE v_action_id INT;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 记录行动
    INSERT INTO combat_actions (
        session_id, round_number, turn_number,
        actor_type, actor_id, actor_name,
        action_type, action_data, dice_rolls,
        success_level, damage_dealt, status_effects
    ) VALUES (
        p_session_id, p_round_number, p_turn_number,
        p_actor_type, p_actor_id, p_actor_name,
        p_action_type, p_action_data, p_dice_rolls,
        p_success_level, p_damage_dealt, p_status_effects
    );
    
    SET v_action_id = LAST_INSERT_ID();
    
    -- 记录行动事件
    INSERT INTO combat_event_logs (
        session_id, event_type, event_data, 
        event_description, related_action_id
    ) VALUES (
        p_session_id, 'action_executed', p_action_data,
        CONCAT(p_actor_name, ' 执行了 ', p_action_type, ' 行动'),
        v_action_id
    );
    
    COMMIT;
    SELECT v_action_id as action_id;
END //
DELIMITER ;

-- ===== 创建触发器：自动更新统计 =====
DELIMITER //
CREATE TRIGGER update_combat_statistics 
AFTER INSERT ON combat_actions
FOR EACH ROW
BEGIN
    -- 更新角色统计
    IF NEW.actor_type = 'player' THEN
        INSERT INTO combat_statistics (
            session_id, character_id, total_actions,
            successful_actions, failed_actions,
            attacks_made, total_damage_dealt
        ) VALUES (
            NEW.session_id, CAST(NEW.actor_id AS UNSIGNED), 1,
            CASE WHEN NEW.success_level IN ('critical', 'extreme', 'hard', 'regular') THEN 1 ELSE 0 END,
            CASE WHEN NEW.success_level IN ('failure', 'fumble') THEN 1 ELSE 0 END,
            CASE WHEN NEW.action_type = 'attack' THEN 1 ELSE 0 END,
            COALESCE(NEW.damage_dealt, 0)
        ) ON DUPLICATE KEY UPDATE
            total_actions = total_actions + 1,
            successful_actions = successful_actions + 
                CASE WHEN NEW.success_level IN ('critical', 'extreme', 'hard', 'regular') THEN 1 ELSE 0 END,
            failed_actions = failed_actions + 
                CASE WHEN NEW.success_level IN ('failure', 'fumble') THEN 1 ELSE 0 END,
            attacks_made = attacks_made + 
                CASE WHEN NEW.action_type = 'attack' THEN 1 ELSE 0 END,
            total_damage_dealt = total_damage_dealt + COALESCE(NEW.damage_dealt, 0);
    END IF;
END //
DELIMITER ;

-- ===== 创建索引优化查询性能 =====
-- 复合索引用于快速查询当前回合的行动
CREATE INDEX idx_session_round_turn ON combat_actions (session_id, round_number, turn_number);

-- 复合索引用于快速查询角色的最新状态
CREATE INDEX idx_session_character_round ON character_combat_snapshots (session_id, character_id, round_number DESC);

-- 复合索引用于快速查询事件日志
CREATE INDEX idx_session_event_time ON combat_event_logs (session_id, event_type, occurred_at DESC);

-- ===== 数据库配置优化 =====
-- 设置InnoDB缓冲池大小(根据服务器内存调整)
-- SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB

-- 设置查询缓存
-- SET GLOBAL query_cache_size = 268435456; -- 256MB
-- SET GLOBAL query_cache_type = ON;

-- 设置连接数限制
-- SET GLOBAL max_connections = 200;

-- ===== 备份和恢复策略 =====
-- 建议每日备份战斗数据
-- mysqldump --single-transaction --routines --triggers coc_trpgs combat_sessions combat_actions character_combat_snapshots monster_combat_instances combat_event_logs combat_statistics combat_maps > combat_backup_$(date +%Y%m%d).sql

-- ===== 数据清理策略 =====
-- 定期清理旧的战斗数据(保留最近30天)
-- DELETE FROM combat_sessions WHERE status = 'completed' AND ended_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

COMMENT ON DATABASE = 'COC 7版战斗系统数据库，支持完整的战斗数据存储、实时同步和历史回放功能';