<template>
  <div class="keeper-combat-panel" v-if="isKeeper">
    <!-- 战斗控制头部 -->
    <div class="combat-header">
      <div class="header-title">
        <i class="fas fa-shield-alt"></i>
        <h3>KP战斗控制面板</h3>
        <span class="combat-status" :class="combatStatus">
          {{ combatStatusText }}
        </span>
      </div>
      <div class="header-actions">
        <button 
          @click="toggleCombatMode" 
          class="btn-primary"
          :disabled="!canToggleCombat"
        >
          {{ combatActive ? '结束战斗' : '开始战斗' }}
        </button>
      </div>
    </div>

    <!-- 战斗设置面板 -->
    <div class="combat-setup" v-if="!combatActive">
      <div class="setup-section">
        <h4>战场设置</h4>
        <div class="battlefield-config">
          <div class="config-row">
            <label>战场大小:</label>
            <select v-model="battlefieldConfig.size">
              <option value="small">小型 (15x10)</option>
              <option value="medium">中型 (20x15)</option>
              <option value="large">大型 (30x20)</option>
              <option value="huge">巨型 (40x30)</option>
            </select>
          </div>
          <div class="config-row">
            <label>环境:</label>
            <select v-model="battlefieldConfig.environment">
              <option value="indoor">室内</option>
              <option value="outdoor">室外</option>
              <option value="underground">地下</option>
              <option value="supernatural">超自然</option>
            </select>
          </div>
          <div class="config-row">
            <label>光照:</label>
            <select v-model="battlefieldConfig.lighting">
              <option value="bright">明亮</option>
              <option value="normal">正常</option>
              <option value="dim">昏暗</option>
              <option value="dark">黑暗</option>
              <option value="pitch_black">完全黑暗</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { combatSystem } from '@/utils/combatSystem.js'
import CombatRules from '@/utils/combatRules.js'

export default {
  name: 'KeeperCombatPanel',
  props: {
    isKeeper: {
      type: Boolean,
      required: true
    },
    roomData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      combatActive: false,
      currentRound: 1,
      currentTurn: 0,
      
      // 战场配置
      battlefieldConfig: {
        size: 'medium',
        environment: 'indoor',
        lighting: 'normal',
        weather: 'clear',
        terrain: 'normal'
      },
      
      // 参与者管理
      selectedParticipants: [],
      selectedNPCs: [],
      initiativeOrder: [],
      
      // 弹窗状态
      showMonsterLibrary: false,
      showNPCCreator: false,
      showCombatLogModal: false,
      
      // 怪物库
      selectedCategory: 'common',
      monsterCategories: [
        { id: 'common', name: '常见生物' },
        { id: 'criminal', name: '罪犯' },
        { id: 'cultist', name: '邪教徒' },
        { id: 'animal', name: '动物' },
        { id: 'supernatural', name: '超自然' },
        { id: 'mythos', name: '神话生物' }
      ],
      
      // NPC创建
      newNPC: {
        name: '',
        type: 'human',
        hitPoints: 10,
        armor: 0,
        attributes: {
          strength: 50,
          dexterity: 50,
          constitution: 50,
          size: 50
        }
      },
      
      // 战斗日志
      combatLog: []
    }
  },
  
  computed: {
    combatStatus() {
      if (!this.combatActive) return 'inactive'
      return 'active'
    },
    
    combatStatusText() {
      if (!this.combatActive) return '战斗未开始'
      return `第${this.currentRound}轮 - ${this.currentParticipant?.name || '等待'}的回合`
    },
    
    canToggleCombat() {
      if (this.combatActive) return true
      return this.selectedParticipants.length > 0 || this.selectedNPCs.length > 0
    },
    
    playerCharacters() {
      // 从房间数据获取玩家角色
      return this.roomData.players?.map(player => ({
        id: player.id,
        name: player.character?.name || player.username,
        avatar: player.character?.avatar,
        currentHP: player.character?.currentHP || player.character?.hitPoints || 10,
        maxHP: player.character?.hitPoints || 10,
        isPlayer: true,
        ...player.character
      })) || []
    },
    
    currentParticipant() {
      return this.initiativeOrder[this.currentTurn] || null
    },
    
    filteredMonsters() {
      return this.getMonstersByCategory(this.selectedCategory)
    }
  },
  
  methods: {
    // 战斗控制
    toggleCombatMode() {
      if (this.combatActive) {
        this.endCombat()
      } else {
        this.startCombat()
      }
    },
    
    startCombat() {
      // 收集所有参与者
      const participants = [
        ...this.getSelectedPlayerCharacters(),
        ...this.selectedNPCs
      ]
      
      if (participants.length === 0) {
        this.$emit('show-notification', {
          type: 'warning',
          message: '请至少选择一个参与者'
        })
        return
      }
      
      // 开始战斗
      const combat = combatSystem.startCombat(participants, this.battlefieldConfig)
      this.combatActive = true
      this.initiativeOrder = combatSystem.participants
      this.currentRound = 1
      this.currentTurn = 0
      
      this.addCombatLog('combat_start', `战斗开始！参与者：${participants.length}人`)
      
      // 通知所有玩家战斗开始
      this.$emit('combat-started', {
        combatId: combat.id,
        participants: this.initiativeOrder,
        battlefield: this.battlefieldConfig
      })
    },
    
    endCombat() {
      combatSystem.endCombat('keeper_ended')
      this.combatActive = false
      this.initiativeOrder = []
      this.currentRound = 1
      this.currentTurn = 0
      
      this.addCombatLog('combat_end', '战斗结束')
      
      // 通知所有玩家战斗结束
      this.$emit('combat-ended')
    },
    
    // 回合管理
    nextTurn() {
      const nextParticipant = combatSystem.nextTurn()
      this.currentTurn = combatSystem.currentTurn
      this.currentRound = combatSystem.roundNumber
      
      if (nextParticipant) {
        this.addCombatLog('turn_change', `${nextParticipant.name} 的回合`)
        this.$emit('turn-changed', {
          participant: nextParticipant,
          round: this.currentRound,
          turn: this.currentTurn
        })
      }
    },
    
    previousTurn() {
      // 实现上一回合逻辑
      if (this.currentTurn > 0) {
        this.currentTurn--
      } else if (this.currentRound > 1) {
        this.currentRound--
        this.currentTurn = this.initiativeOrder.length - 1
      }
      
      const participant = this.currentParticipant
      if (participant) {
        this.addCombatLog('turn_change', `回到 ${participant.name} 的回合`)
      }
    },
    
    endRound() {
      combatSystem.startNewRound()
      this.currentRound = combatSystem.roundNumber
      this.currentTurn = 0
      
      this.addCombatLog('round_end', `第${this.currentRound}轮开始`)
      this.$emit('round-ended', this.currentRound)
    },
    
    // 参与者管理
    toggleParticipant(characterId) {
      const index = this.selectedParticipants.indexOf(characterId)
      if (index > -1) {
        this.selectedParticipants.splice(index, 1)
      } else {
        this.selectedParticipants.push(characterId)
      }
    },
    
    getSelectedPlayerCharacters() {
      return this.playerCharacters.filter(char => 
        this.selectedParticipants.includes(char.id)
      )
    },
    
    delayParticipant(participantId) {
      const success = combatSystem.delayAction(participantId)
      if (success) {
        this.initiativeOrder = combatSystem.participants
        const participant = combatSystem.getParticipant(participantId)
        this.addCombatLog('delay_action', `${participant.name} 延迟行动`)
      }
    },
    
    // 怪物和NPC管理
    getMonstersByCategory(category) {
      // 这里应该从怪物数据库获取
      const monsters = {
        common: [
          {
            id: 'thug',
            name: '暴徒',
            description: '普通的街头暴徒',
            hitPoints: 12,
            armor: 0,
            build: 1,
            type: 'human'
          },
          {
            id: 'guard',
            name: '警卫',
            description: '训练有素的警卫',
            hitPoints: 15,
            armor: 1,
            build: 1,
            type: 'human'
          }
        ],
        animal: [
          {
            id: 'dog',
            name: '恶犬',
            description: '凶猛的看门犬',
            hitPoints: 8,
            armor: 0,
            build: 0,
            type: 'animal'
          }
        ]
      }
      
      return monsters[category] || []
    },
    
    addMonster(monster) {
      const newNPC = {
        ...monster,
        id: `${monster.id}_${Date.now()}`,
        currentHP: monster.hitPoints,
        maxHP: monster.hitPoints,
        isPlayer: false,
        faction: 'enemies'
      }
      
      this.selectedNPCs.push(newNPC)
      this.showMonsterLibrary = false
      this.addCombatLog('add_npc', `添加了 ${monster.name}`)
    },
    
    createNPC() {
      const npc = {
        ...this.newNPC,
        id: `npc_${Date.now()}`,
        currentHP: this.newNPC.hitPoints,
        maxHP: this.newNPC.hitPoints,
        isPlayer: false,
        faction: 'npcs'
      }
      
      this.selectedNPCs.push(npc)
      this.showNPCCreator = false
      
      // 重置表单
      this.newNPC = {
        name: '',
        type: 'human',
        hitPoints: 10,
        armor: 0
      }
      
      this.addCombatLog('create_npc', `创建了NPC ${npc.name}`)
    },
    
    removeNPC(npcId) {
      const index = this.selectedNPCs.findIndex(npc => npc.id === npcId)
      if (index > -1) {
        const npc = this.selectedNPCs[index]
        this.selectedNPCs.splice(index, 1)
        this.addCombatLog('remove_npc', `移除了 ${npc.name}`)
      }
    },
    
    editNPC(npc) {
      // 实现NPC编辑功能
      this.$emit('edit-npc', npc)
    },
    
    editParticipant(participant) {
      // 实现参与者编辑功能
      this.$emit('edit-participant', participant)
    },
    
    // 战斗工具
    rollInitiative() {
      if (this.initiativeOrder.length === 0) return
      
      // 重新计算先攻
      this.initiativeOrder.forEach(participant => {
        participant.initiative = combatSystem.calculateInitiative(participant)
      })
      
      // 重新排序
      this.initiativeOrder.sort((a, b) => b.initiative - a.initiative)
      this.currentTurn = 0
      
      this.addCombatLog('reroll_initiative', '重新投掷先攻')
    },
    
    applyDamage() {
      // 打开伤害应用界面
      this.$emit('show-damage-dialog')
    },
    
    healCharacter() {
      // 打开治疗界面
      this.$emit('show-heal-dialog')
    },
    
    addStatusEffect() {
      // 打开状态效果界面
      this.$emit('show-status-dialog')
    },
    
    showDiceRoller() {
      // 显示骰子工具
      this.$emit('show-dice-roller')
    },
    
    showCombatLog() {
      this.showCombatLogModal = true
    },
    
    // 状态效果
    getStatusEffectIcon(effect) {
      const icons = {
        bleeding: '🩸',
        poisoned: '☠️',
        stunned: '😵',
        frightened: '😨',
        blessed: '✨',
        cursed: '💀',
        prone: '⬇️',
        grappled: '🤝',
        unconscious: '😴'
      }
      return icons[effect] || '❓'
    },
    
    // 战斗日志
    addCombatLog(type, message) {
      this.combatLog.push({
        id: Date.now(),
        type,
        message,
        timestamp: new Date(),
        round: this.currentRound
      })
    },
    
    formatTime(timestamp) {
      return timestamp.toLocaleTimeString()
    }
  },
  
  watch: {
    combatActive(newVal) {
      if (newVal) {
        // 战斗开始时的初始化
        this.$emit('combat-mode-changed', true)
      } else {
        // 战斗结束时的清理
        this.$emit('combat-mode-changed', false)
      }
    }
  }
}
</script>    <!-
- 参与者管理 -->
    <div class="participants-section" v-if="!combatActive">
      <h4>参与者管理</h4>
      
      <!-- 玩家角色列表 -->
      <div class="player-characters">
        <h5>玩家角色</h5>
        <div class="character-list">
          <div 
            v-for="character in playerCharacters" 
            :key="character.id"
            class="character-item"
            :class="{ selected: selectedParticipants.includes(character.id) }"
            @click="toggleParticipant(character.id)"
          >
            <div class="character-avatar">
              <img :src="character.avatar" :alt="character.name" v-if="character.avatar">
              <div v-else class="default-avatar">{{ character.name.charAt(0) }}</div>
            </div>
            <div class="character-info">
              <span class="character-name">{{ character.name }}</span>
              <span class="character-stats">HP: {{ character.currentHP }}/{{ character.maxHP }}</span>
            </div>
            <div class="character-actions">
              <i class="fas fa-check" v-if="selectedParticipants.includes(character.id)"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- NPC和怪物管理 -->
      <div class="npc-management">
        <h5>NPC和怪物</h5>
        <div class="npc-controls">
          <button @click="showMonsterLibrary = true" class="btn-secondary">
            <i class="fas fa-plus"></i>
            添加怪物
          </button>
          <button @click="showNPCCreator = true" class="btn-secondary">
            <i class="fas fa-user-plus"></i>
            创建NPC
          </button>
        </div>
        
        <div class="npc-list">
          <div 
            v-for="npc in selectedNPCs" 
            :key="npc.id"
            class="npc-item"
          >
            <div class="npc-info">
              <span class="npc-name">{{ npc.name }}</span>
              <span class="npc-type">{{ npc.type }}</span>
              <span class="npc-stats">HP: {{ npc.currentHP }}/{{ npc.maxHP }}</span>
            </div>
            <div class="npc-actions">
              <button @click="editNPC(npc)" class="btn-icon">
                <i class="fas fa-edit"></i>
              </button>
              <button @click="removeNPC(npc.id)" class="btn-icon danger">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 战斗中控制面板 -->
    <div class="combat-control" v-if="combatActive">
      <!-- 先攻追踪器 -->
      <div class="initiative-tracker">
        <h4>先攻顺序</h4>
        <div class="initiative-list">
          <div 
            v-for="(participant, index) in initiativeOrder" 
            :key="participant.id"
            class="initiative-item"
            :class="{ 
              current: index === currentTurn,
              player: participant.isPlayer,
              npc: !participant.isPlayer
            }"
          >
            <div class="initiative-number">{{ participant.initiative }}</div>
            <div class="participant-info">
              <span class="participant-name">{{ participant.name }}</span>
              <div class="participant-status">
                <span class="hp-status">{{ participant.currentHP }}/{{ participant.maxHP }}</span>
                <div class="status-effects">
                  <span 
                    v-for="effect in participant.conditions" 
                    :key="effect"
                    class="status-effect"
                    :class="effect"
                  >
                    {{ getStatusEffectIcon(effect) }}
                  </span>
                </div>
              </div>
            </div>
            <div class="participant-actions">
              <button @click="delayParticipant(participant.id)" class="btn-icon">
                <i class="fas fa-clock"></i>
              </button>
              <button @click="editParticipant(participant)" class="btn-icon">
                <i class="fas fa-edit"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 回合控制 -->
      <div class="turn-control">
        <div class="current-turn-info">
          <h4>当前回合</h4>
          <div class="turn-details">
            <span class="round-number">第 {{ currentRound }} 轮</span>
            <span class="current-participant">
              {{ currentParticipant?.name || '无' }} 的回合
            </span>
          </div>
        </div>
        
        <div class="turn-actions">
          <button @click="nextTurn" class="btn-primary">
            <i class="fas fa-forward"></i>
            下一个回合
          </button>
          <button @click="previousTurn" class="btn-secondary">
            <i class="fas fa-backward"></i>
            上一个回合
          </button>
          <button @click="endRound" class="btn-warning">
            <i class="fas fa-refresh"></i>
            结束本轮
          </button>
        </div>
      </div>

    <!-- 战斗工具 -->
    <div class="combat-tools" v-if="combatActive">
      <h4>战斗工具</h4>
      <div class="tool-grid">
        <button @click="rollInitiative" class="tool-btn">
          <i class="fas fa-dice"></i>
          重新投先攻
        </button>
        <button @click="applyDamage" class="tool-btn">
          <i class="fas fa-heart-broken"></i>
          应用伤害
        </button>
        <button @click="healCharacter" class="tool-btn">
          <i class="fas fa-heart"></i>
          治疗角色
        </button>
        <button @click="addStatusEffect" class="tool-btn">
          <i class="fas fa-magic"></i>
          添加状态
        </button>
        <button @click="showDiceRoller" class="tool-btn">
          <i class="fas fa-dice-d20"></i>
          投骰工具
        </button>
        <button @click="showCombatLog" class="tool-btn">
          <i class="fas fa-scroll"></i>
          战斗日志
        </button>
      </div>
    </div>

    <!-- 怪物库弹窗 -->
    <div class="modal-overlay" v-if="showMonsterLibrary" @click="showMonsterLibrary = false">
      <div class="modal-content monster-library" @click.stop>
        <div class="modal-header">
          <h3>怪物库</h3>
          <button @click="showMonsterLibrary = false" class="btn-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="monster-categories">
            <button 
              v-for="category in monsterCategories" 
              :key="category.id"
              @click="selectedCategory = category.id"
              class="category-btn"
              :class="{ active: selectedCategory === category.id }"
            >
              {{ category.name }}
            </button>
          </div>
          <div class="monster-list">
            <div 
              v-for="monster in filteredMonsters" 
              :key="monster.id"
              class="monster-card"
              @click="addMonster(monster)"
            >
              <div class="monster-info">
                <h4>{{ monster.name }}</h4>
                <p>{{ monster.description }}</p>
                <div class="monster-stats">
                  <span>HP: {{ monster.hitPoints }}</span>
                  <span>护甲: {{ monster.armor }}</span>
                  <span>体格: {{ monster.build }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- NPC创建器弹窗 -->
    <div class="modal-overlay" v-if="showNPCCreator" @click="showNPCCreator = false">
      <div class="modal-content npc-creator" @click.stop>
        <div class="modal-header">
          <h3>创建NPC</h3>
          <button @click="showNPCCreator = false" class="btn-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="createNPC">
            <div class="form-group">
              <label>名称:</label>
              <input v-model="newNPC.name" type="text" required>
            </div>
            <div class="form-group">
              <label>类型:</label>
              <select v-model="newNPC.type">
                <option value="human">人类</option>
                <option value="animal">动物</option>
                <option value="monster">怪物</option>
                <option value="supernatural">超自然</option>
              </select>
            </div>
            <div class="form-group">
              <label>生命值:</label>
              <input v-model.number="newNPC.hitPoints" type="number" min="1" required>
            </div>
            <div class="form-group">
              <label>护甲值:</label>
              <input v-model.number="newNPC.armor" type="number" min="0">
            </div>
            <div class="form-actions">
              <button type="submit" class="btn-primary">创建</button>
              <button type="button" @click="showNPCCreator = false" class="btn-secondary">取消</button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 战斗日志弹窗 -->
    <div class="modal-overlay" v-if="showCombatLogModal" @click="showCombatLogModal = false">
      <div class="modal-content combat-log-modal" @click.stop>
        <div class="modal-header">
          <h3>战斗日志</h3>
          <button @click="showCombatLogModal = false" class="btn-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="log-entries">
            <div 
              v-for="entry in combatLog" 
              :key="entry.id"
              class="log-entry"
              :class="entry.type"
            >
              <span class="log-time">{{ formatTime(entry.timestamp) }}</span>
              <span class="log-content">{{ entry.message }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
.keeper-combat-panel {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #0f3460;
  border-radius: 12px;
  padding: 20px;
  color: #e94560;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* 头部样式 */
.combat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid #0f3460;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title h3 {
  margin: 0;
  color: #e94560;
  font-size: 1.5rem;
  font-weight: bold;
}

.combat-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.combat-status.inactive {
  background: #666;
  color: #ccc;
}

.combat-status.active {
  background: #e94560;
  color: white;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #e94560 0%, #f27121 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(233, 69, 96, 0.4);
}

.btn-primary:disabled {
  background: #666;
  cursor: not-allowed;
}

.btn-secondary {
  background: #0f3460;
  color: #e94560;
  border: 1px solid #e94560;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #e94560;
  color: white;
}

/* 战场设置 */
.combat-setup {
  margin-bottom: 20px;
}

.setup-section h4 {
  color: #e94560;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.battlefield-config {
  display: grid;
  gap: 12px;
}

.config-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-row label {
  min-width: 80px;
  color: #ccc;
  font-weight: bold;
}

.config-row select {
  flex: 1;
  padding: 8px;
  border: 1px solid #0f3460;
  border-radius: 4px;
  background: #1a1a2e;
  color: #e94560;
}

/* 参与者管理 */
.participants-section {
  margin-bottom: 20px;
}

.character-list, .npc-list {
  display: grid;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.character-item, .npc-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(15, 52, 96, 0.3);
  border: 1px solid #0f3460;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.character-item:hover, .npc-item:hover {
  background: rgba(15, 52, 96, 0.5);
  border-color: #e94560;
}

.character-item.selected {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
}

.character-avatar, .default-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e94560;
  color: white;
  font-weight: bold;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.character-info, .npc-info {
  flex: 1;
}

.character-name, .npc-name {
  display: block;
  font-weight: bold;
  color: #e94560;
}

.character-stats, .npc-stats {
  display: block;
  font-size: 0.9rem;
  color: #ccc;
}

/* 先攻追踪器 */
.initiative-tracker {
  margin-bottom: 20px;
}

.initiative-list {
  display: grid;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.initiative-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(15, 52, 96, 0.3);
  border: 1px solid #0f3460;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.initiative-item.current {
  background: rgba(233, 69, 96, 0.3);
  border-color: #e94560;
  box-shadow: 0 0 12px rgba(233, 69, 96, 0.3);
}

.initiative-item.player {
  border-left: 4px solid #4CAF50;
}

.initiative-item.npc {
  border-left: 4px solid #FF9800;
}

.initiative-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e94560;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
}

.participant-info {
  flex: 1;
}

.participant-name {
  display: block;
  font-weight: bold;
  color: #e94560;
  margin-bottom: 4px;
}

.participant-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hp-status {
  color: #ccc;
  font-size: 0.9rem;
}

.status-effects {
  display: flex;
  gap: 4px;
}

.status-effect {
  font-size: 1.2rem;
}

/* 回合控制 */
.turn-control {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(15, 52, 96, 0.2);
  border-radius: 8px;
}

.current-turn-info {
  margin-bottom: 15px;
}

.turn-details {
  display: flex;
  gap: 20px;
  color: #ccc;
}

.round-number, .current-participant {
  font-weight: bold;
}

.turn-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 战斗工具 */
.combat-tools {
  margin-bottom: 20px;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
}

.tool-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px 10px;
  background: rgba(15, 52, 96, 0.3);
  border: 1px solid #0f3460;
  border-radius: 8px;
  color: #e94560;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tool-btn:hover {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
  transform: translateY(-2px);
}

.tool-btn i {
  font-size: 1.5rem;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1a1a2e;
  border: 2px solid #0f3460;
  border-radius: 12px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #0f3460;
}

.modal-header h3 {
  margin: 0;
  color: #e94560;
}

.btn-close {
  background: none;
  border: none;
  color: #e94560;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
}

.modal-body {
  padding: 20px;
}

/* 怪物库样式 */
.monster-categories {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 8px 16px;
  background: rgba(15, 52, 96, 0.3);
  border: 1px solid #0f3460;
  border-radius: 6px;
  color: #e94560;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-btn.active,
.category-btn:hover {
  background: #e94560;
  color: white;
}

.monster-list {
  display: grid;
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.monster-card {
  padding: 15px;
  background: rgba(15, 52, 96, 0.2);
  border: 1px solid #0f3460;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.monster-card:hover {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
}

.monster-info h4 {
  margin: 0 0 8px 0;
  color: #e94560;
}

.monster-info p {
  margin: 0 0 12px 0;
  color: #ccc;
  font-size: 0.9rem;
}

.monster-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #ccc;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #e94560;
  font-weight: bold;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #0f3460;
  border-radius: 4px;
  background: #1a1a2e;
  color: #e94560;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 战斗日志 */
.log-entries {
  max-height: 400px;
  overflow-y: auto;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid rgba(15, 52, 96, 0.3);
}

.log-time {
  color: #666;
  font-size: 0.8rem;
  min-width: 80px;
}

.log-content {
  color: #ccc;
  flex: 1;
}

.log-entry.combat_start .log-content {
  color: #4CAF50;
  font-weight: bold;
}

.log-entry.combat_end .log-content {
  color: #e94560;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .keeper-combat-panel {
    padding: 15px;
  }
  
  .combat-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .turn-actions {
    justify-content: center;
  }
  
  .tool-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .modal-content {
    margin: 20px;
    max-width: calc(100% - 40px);
  }
}
</style>