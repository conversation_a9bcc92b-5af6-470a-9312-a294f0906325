{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"combat-test\"\n};\nvar _hoisted_2 = {\n  \"class\": \"test-controls\"\n};\nvar _hoisted_3 = {\n  \"class\": \"combat-log-test\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  \"class\": \"initiative-test\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_CombatLog = _resolveComponent(\"CombatLog\");\n  var _component_InitiativeTracker = _resolveComponent(\"InitiativeTracker\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[4] || (_cache[4] = _createElementVNode(\"h1\", null, \"战斗系统测试页面\", -1 /* CACHED */)), _createCommentVNode(\" 测试按钮 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.toggleCombat && $options.toggleCombat.apply($options, arguments);\n    }),\n    \"class\": \"test-btn\"\n  }, _toDisplayString($data.combatActive ? '结束战斗' : '开始战斗'), 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.addTestLog && $options.addTestLog.apply($options, arguments);\n    }),\n    \"class\": \"test-btn\"\n  }, \" 添加测试日志 \")]), _createCommentVNode(\" 战斗日志测试 \"), _createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"h2\", null, \"战斗日志测试\", -1 /* CACHED */)), _createVNode(_component_CombatLog, {\n    \"combat-logs\": $data.testLogs,\n    onClearLog: $options.clearTestLogs,\n    onToggleAutoScroll: $options.handleAutoScroll\n  }, null, 8 /* PROPS */, [\"combat-logs\", \"onClearLog\", \"onToggleAutoScroll\"])]), _createCommentVNode(\" 先攻追踪器测试 \"), $data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_cache[3] || (_cache[3] = _createElementVNode(\"h2\", null, \"先攻追踪器测试\", -1 /* CACHED */)), _createVNode(_component_InitiativeTracker, {\n    \"initiative-order\": $data.testInitiative,\n    \"current-round\": $data.currentRound,\n    \"current-turn\": $data.currentTurn,\n    onParticipantSelected: $options.handleParticipantSelect\n  }, null, 8 /* PROPS */, [\"initiative-order\", \"current-round\", \"current-turn\", \"onParticipantSelected\"])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "onClick", "_cache", "$options", "toggleCombat", "apply", "arguments", "$data", "combatActive", "addTestLog", "_hoisted_3", "_createVNode", "_component_CombatLog", "testLogs", "onClearLog", "clearTestLogs", "onToggleAutoScroll", "handleAutoScroll", "_hoisted_4", "_component_InitiativeTracker", "testInitiative", "currentRound", "currentTurn", "onParticipantSelected", "handleParticipantSelect"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CombatTest.vue"], "sourcesContent": ["<template>\r\n  <div class=\"combat-test\">\r\n    <h1>战斗系统测试页面</h1>\r\n    \r\n    <!-- 测试按钮 -->\r\n    <div class=\"test-controls\">\r\n      <button @click=\"toggleCombat\" class=\"test-btn\">\r\n        {{ combatActive ? '结束战斗' : '开始战斗' }}\r\n      </button>\r\n      <button @click=\"addTestLog\" class=\"test-btn\">\r\n        添加测试日志\r\n      </button>\r\n    </div>\r\n    \r\n    <!-- 战斗日志测试 -->\r\n    <div class=\"combat-log-test\">\r\n      <h2>战斗日志测试</h2>\r\n      <CombatLog \r\n        :combat-logs=\"testLogs\"\r\n        @clear-log=\"clearTestLogs\"\r\n        @toggle-auto-scroll=\"handleAutoScroll\"\r\n      />\r\n    </div>\r\n    \r\n    <!-- 先攻追踪器测试 -->\r\n    <div class=\"initiative-test\" v-if=\"combatActive\">\r\n      <h2>先攻追踪器测试</h2>\r\n      <InitiativeTracker\r\n        :initiative-order=\"testInitiative\"\r\n        :current-round=\"currentRound\"\r\n        :current-turn=\"currentTurn\"\r\n        @participant-selected=\"handleParticipantSelect\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CombatLog from '@/components/combat/CombatLog.vue'\r\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue'\r\n\r\nexport default {\r\n  name: 'CombatTest',\r\n  components: {\r\n    CombatLog,\r\n    InitiativeTracker\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      combatActive: false,\r\n      currentRound: 1,\r\n      currentTurn: 0,\r\n      \r\n      testLogs: [\r\n        {\r\n          id: 1,\r\n          type: 'system',\r\n          message: '战斗开始！',\r\n          timestamp: new Date(),\r\n          round: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          type: 'attack',\r\n          message: '侦探约翰对邪教徒发起攻击',\r\n          timestamp: new Date(),\r\n          round: 1,\r\n          participant: {\r\n            name: '侦探约翰',\r\n            avatar: '/images/default-avatar.png'\r\n          },\r\n          diceRoll: {\r\n            formula: '1d100',\r\n            rolls: [45],\r\n            total: 45\r\n          },\r\n          successLevel: 'regular'\r\n        },\r\n        {\r\n          id: 3,\r\n          type: 'damage',\r\n          message: '攻击命中，造成8点伤害',\r\n          timestamp: new Date(),\r\n          round: 1,\r\n          severity: 'critical'\r\n        }\r\n      ],\r\n      \r\n      testInitiative: [\r\n        {\r\n          id: 1,\r\n          name: '侦探约翰',\r\n          initiative: 85,\r\n          isPlayer: true,\r\n          avatar: '/images/default-avatar.png',\r\n          currentHP: 85,\r\n          maxHP: 100,\r\n          hasActed: false\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '邪教徒',\r\n          initiative: 72,\r\n          isPlayer: false,\r\n          avatar: '/images/default-enemy.png',\r\n          currentHP: 42,\r\n          maxHP: 50,\r\n          hasActed: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '记者玛丽',\r\n          initiative: 68,\r\n          isPlayer: true,\r\n          avatar: '/images/default-avatar.png',\r\n          currentHP: 78,\r\n          maxHP: 90,\r\n          hasActed: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    toggleCombat() {\r\n      this.combatActive = !this.combatActive\r\n      \r\n      if (this.combatActive) {\r\n        this.addTestLog({\r\n          type: 'system',\r\n          message: '战斗开始！',\r\n          round: this.currentRound\r\n        })\r\n      } else {\r\n        this.addTestLog({\r\n          type: 'system',\r\n          message: '战斗结束！'\r\n        })\r\n      }\r\n    },\r\n    \r\n    addTestLog(customLog = null) {\r\n      const newLog = customLog || {\r\n        id: Date.now(),\r\n        type: 'skill',\r\n        message: `测试日志 - ${new Date().toLocaleTimeString()}`,\r\n        timestamp: new Date(),\r\n        round: this.currentRound,\r\n        participant: {\r\n          name: '测试角色',\r\n          avatar: '/images/default-avatar.png'\r\n        }\r\n      }\r\n      \r\n      if (!newLog.id) {\r\n        newLog.id = Date.now()\r\n      }\r\n      if (!newLog.timestamp) {\r\n        newLog.timestamp = new Date()\r\n      }\r\n      \r\n      this.testLogs.push(newLog)\r\n    },\r\n    \r\n    clearTestLogs() {\r\n      this.testLogs = []\r\n    },\r\n    \r\n    handleAutoScroll(enabled) {\r\n      console.log('自动滚动:', enabled)\r\n    },\r\n    \r\n    handleParticipantSelect(participant) {\r\n      console.log('选中参与者:', participant)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.combat-test {\r\n  padding: 20px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.test-controls {\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.test-btn {\r\n  padding: 10px 20px;\r\n  background: #007bff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.test-btn:hover {\r\n  background: #0056b3;\r\n}\r\n\r\n.combat-log-test {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.combat-log-test h2,\r\n.initiative-test h2 {\r\n  margin-bottom: 15px;\r\n  color: #333;\r\n}\r\n\r\n.initiative-test {\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 为测试页面调整战斗日志高度 */\r\n.combat-log-test :deep(.combat-log) {\r\n  height: 400px;\r\n}\r\n</style>"], "mappings": ";;EACO,SAAM;AAAa;;EAIjB,SAAM;AAAe;;EAUrB,SAAM;AAAiB;;;EAUvB,SAAM;;;;;uBAxBbA,mBAAA,CAiCM,OAjCNC,UAiCM,G,0BAhCJC,mBAAA,CAAiB,YAAb,UAAQ,qBAEZC,mBAAA,UAAa,EACbD,mBAAA,CAOM,OAPNE,UAOM,GANJF,mBAAA,CAES;IAFAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAC,YAAA,IAAAD,QAAA,CAAAC,YAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;IAAA;IAAE,SAAM;sBAC/BC,KAAA,CAAAC,YAAY,oCAEjBV,mBAAA,CAES;IAFAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAM,UAAA,IAAAN,QAAA,CAAAM,UAAA,CAAAJ,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAU;IAAA;IAAE,SAAM;KAAW,UAE7C,E,GAGFP,mBAAA,YAAe,EACfD,mBAAA,CAOM,OAPNY,UAOM,G,0BANJZ,mBAAA,CAAe,YAAX,QAAM,qBACVa,YAAA,CAIEC,oBAAA;IAHC,aAAW,EAAEL,KAAA,CAAAM,QAAQ;IACrBC,UAAS,EAAEX,QAAA,CAAAY,aAAa;IACxBC,kBAAkB,EAAEb,QAAA,CAAAc;kFAIzBlB,mBAAA,aAAgB,EACmBQ,KAAA,CAAAC,YAAY,I,cAA/CZ,mBAAA,CAQM,OARNsB,UAQM,G,0BAPJpB,mBAAA,CAAgB,YAAZ,SAAO,qBACXa,YAAA,CAKEQ,4BAAA;IAJC,kBAAgB,EAAEZ,KAAA,CAAAa,cAAc;IAChC,eAAa,EAAEb,KAAA,CAAAc,YAAY;IAC3B,cAAY,EAAEd,KAAA,CAAAe,WAAW;IACzBC,qBAAoB,EAAEpB,QAAA,CAAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}