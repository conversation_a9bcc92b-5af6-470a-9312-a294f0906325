<template>
  <!-- 2D战场上的角色令牌 -->
  <g 
    class="character-token"
    :class="{ 
      selected, 
      'current-turn': currentTurn,
      'can-move': canMove,
      'can-act': canAct,
      player: character.isPlayer,
      enemy: !character.isPlayer
    }"
    :transform="`translate(${x}, ${y})`"
    @click="$emit('select', character)"
    @contextmenu.prevent="$emit('context-menu', character, $event)"
  >
    <!-- 选中光环 -->
    <circle 
      v-if="selected"
      :r="tokenRadius + 8"
      fill="none"
      stroke="#3498db"
      stroke-width="3"
      opacity="0.8"
    >
      <animate 
        attributeName="stroke-opacity" 
        values="0.8;0.3;0.8" 
        dur="2s" 
        repeatCount="indefinite"
      />
    </circle>
    
    <!-- 当前回合光环 -->
    <circle 
      v-if="currentTurn"
      :r="tokenRadius + 12"
      fill="none"
      stroke="#e74c3c"
      stroke-width="2"
      stroke-dasharray="5,5"
      opacity="0.9"
    >
      <animateTransform
        attributeName="transform"
        type="rotate"
        values="0;360"
        dur="3s"
        repeatCount="indefinite"
      />
    </circle>
    
    <!-- 移动范围指示 -->
    <circle 
      v-if="showMovementRange && canMove"
      :r="movementRange * gridSize"
      fill="rgba(76, 175, 80, 0.1)"
      stroke="#4caf50"
      stroke-width="2"
      stroke-dasharray="3,3"
      opacity="0.6"
    />
    
    <!-- 攻击范围指示 -->
    <circle 
      v-if="showAttackRange && canAct"
      :r="attackRange * gridSize"
      fill="rgba(244, 67, 54, 0.1)"
      stroke="#f44336"
      stroke-width="2"
      stroke-dasharray="5,5"
      opacity="0.5"
    />
    
    <!-- 角色主体 -->
    <g class="character-body">
      <!-- 背景圆 -->
      <circle 
        :r="tokenRadius"
        :fill="character.isPlayer ? '#2196f3' : '#f44336'"
        stroke="#fff"
        stroke-width="2"
        opacity="0.9"
      />
      
      <!-- 角色头像 -->
      <defs>
        <pattern 
          :id="`avatar-${character.id}`" 
          patternUnits="objectBoundingBox" 
          width="100%" 
          height="100%"
        >
          <image 
            :href="character.avatar || getDefaultAvatar()"
            x="0" 
            y="0" 
            :width="tokenRadius * 2" 
            :height="tokenRadius * 2"
            preserveAspectRatio="xMidYMid slice"
          />
        </pattern>
      </defs>
      
      <circle 
        :r="tokenRadius - 3"
        :fill="`url(#avatar-${character.id})`"
        opacity="0.95"
      />
      
      <!-- 生命值环 -->
      <circle 
        :r="tokenRadius + 2"
        fill="none"
        stroke="#ddd"
        stroke-width="4"
        opacity="0.3"
      />
      <circle 
        :r="tokenRadius + 2"
        fill="none"
        :stroke="getHealthColor()"
        stroke-width="4"
        :stroke-dasharray="getHealthDashArray()"
        stroke-linecap="round"
        transform="rotate(-90)"
        opacity="0.8"
      />
    </g>
    
    <!-- 角色名称 -->
    <text 
      :y="tokenRadius + 20"
      text-anchor="middle"
      font-size="12"
      font-weight="bold"
      :fill="character.isPlayer ? '#2196f3' : '#f44336'"
      class="character-name-text"
    >
      {{ character.name }}
    </text>
    
    <!-- 状态效果图标 -->
    <g class="status-effects" :transform="`translate(${-tokenRadius}, ${-tokenRadius - 15})`">
      <g 
        v-for="(condition, index) in character.conditions" 
        :key="condition"
        :transform="`translate(${index * 16}, 0)`"
      >
        <circle r="8" fill="#333" opacity="0.8"/>
        <text 
          text-anchor="middle" 
          dy="4" 
          font-size="10" 
          fill="white"
        >
          {{ getConditionIcon(condition) }}
        </text>
      </g>
    </g>
    
    <!-- 行动状态指示器 -->
    <g class="action-indicators" :transform="`translate(${tokenRadius - 8}, ${-tokenRadius + 8})`">
      <!-- 已行动指示 -->
      <circle 
        v-if="character.hasActed"
        r="6"
        fill="#ff9800"
        stroke="#fff"
        stroke-width="1"
      />
      <text 
        v-if="character.hasActed"
        text-anchor="middle"
        dy="3"
        font-size="8"
        fill="white"
        font-weight="bold"
      >
        ✓
      </text>
      
      <!-- 延迟行动指示 -->
      <circle 
        v-if="character.isDelaying"
        r="6"
        fill="#9c27b0"
        stroke="#fff"
        stroke-width="1"
      />
      <text 
        v-if="character.isDelaying"
        text-anchor="middle"
        dy="3"
        font-size="8"
        fill="white"
        font-weight="bold"
      >
        ⏸
      </text>
    </g>
    
    <!-- 伤害数字动画 -->
    <g v-if="damageAnimation" class="damage-animation">
      <text 
        :y="-tokenRadius - 10"
        text-anchor="middle"
        font-size="16"
        font-weight="bold"
        :fill="damageAnimation.color"
        opacity="0"
      >
        {{ damageAnimation.text }}
        <animate 
          attributeName="y" 
          :values="`${-tokenRadius - 10};${-tokenRadius - 40}`"
          dur="1.5s"
        />
        <animate 
          attributeName="opacity" 
          values="0;1;1;0"
          dur="1.5s"
        />
      </text>
    </g>
    
    <!-- 拖拽手柄 (仅在可移动时显示) -->
    <circle 
      v-if="canMove && selected"
      :r="tokenRadius"
      fill="transparent"
      stroke="none"
      style="cursor: move"
      @mousedown="startDrag"
    />
  </g>
</template>

<script>
export default {
  name: 'CharacterToken',
  props: {
    character: {
      type: Object,
      required: true
    },
    gridSize: {
      type: Number,
      default: 40
    },
    zoom: {
      type: Number,
      default: 1
    },
    selected: {
      type: Boolean,
      default: false
    },
    currentTurn: {
      type: Boolean,
      default: false
    },
    canMove: {
      type: Boolean,
      default: false
    },
    canAct: {
      type: Boolean,
      default: false
    },
    showMovementRange: {
      type: Boolean,
      default: false
    },
    showAttackRange: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      tokenRadius: 20,
      isDragging: false,
      dragStart: null,
      damageAnimation: null
    }
  },
  
  computed: {
    x() {
      return (this.character.position?.x || 0) * this.gridSize * this.zoom
    },
    
    y() {
      return (this.character.position?.y || 0) * this.gridSize * this.zoom
    },
    
    movementRange() {
      // 根据角色移动力计算移动范围
      return this.character.movement || 3
    },
    
    attackRange() {
      // 根据装备武器计算攻击范围
      const weapon = this.character.equippedWeapon
      if (!weapon) return 1.5 // 徒手攻击
      
      if (weapon.type === 'melee') {
        return weapon.reach || 1.5
      } else if (weapon.type === 'ranged') {
        return Math.min(weapon.range?.base || 10, 10) // 限制显示范围
      }
      
      return 1.5
    }
  },
  
  methods: {
    /**
     * 获取默认头像
     */
    getDefaultAvatar() {
      return this.character.isPlayer 
        ? '/images/default-player-avatar.png'
        : '/images/default-enemy-avatar.png'
    },
    
    /**
     * 获取生命值颜色
     */
    getHealthColor() {
      const healthPercent = this.getHealthPercentage()
      
      if (healthPercent > 75) return '#4caf50'
      if (healthPercent > 50) return '#ff9800'
      if (healthPercent > 25) return '#f44336'
      return '#9c27b0'
    },
    
    /**
     * 获取生命值百分比
     */
    getHealthPercentage() {
      const current = this.character.currentHP || this.character.hitPoints
      const max = this.character.maxHP || this.character.hitPoints
      return max > 0 ? (current / max) * 100 : 0
    },
    
    /**
     * 获取生命值环的虚线数组
     */
    getHealthDashArray() {
      const circumference = 2 * Math.PI * (this.tokenRadius + 2)
      const healthPercent = this.getHealthPercentage() / 100
      const healthLength = circumference * healthPercent
      const gapLength = circumference - healthLength
      
      return `${healthLength} ${gapLength}`
    },
    
    /**
     * 获取状态效果图标
     */
    getConditionIcon(condition) {
      const iconMap = {
        'unconscious': '💤',
        'dying': '💀',
        'prone': '⬇️',
        'stunned': '😵',
        'restrained': '🔒',
        'frightened': '😨',
        'poisoned': '☠️',
        'bleeding': '🩸',
        'burning': '🔥',
        'frozen': '❄️',
        'paralyzed': '⚡',
        'blinded': '👁️',
        'deafened': '👂',
        'charmed': '💖',
        'confused': '❓'
      }
      
      return iconMap[condition] || '?'
    },
    
    /**
     * 开始拖拽
     */
    startDrag(event) {
      if (!this.canMove) return
      
      this.isDragging = true
      this.dragStart = {
        x: event.clientX,
        y: event.clientY,
        characterX: this.character.position.x,
        characterY: this.character.position.y
      }
      
      document.addEventListener('mousemove', this.handleDrag)
      document.addEventListener('mouseup', this.endDrag)
      
      event.stopPropagation()
    },
    
    /**
     * 处理拖拽
     */
    handleDrag(event) {
      if (!this.isDragging || !this.dragStart) return
      
      const deltaX = event.clientX - this.dragStart.x
      const deltaY = event.clientY - this.dragStart.y
      
      const gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom))
      const gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom))
      
      const newX = this.dragStart.characterX + gridDeltaX
      const newY = this.dragStart.characterY + gridDeltaY
      
      // 检查移动范围
      const distance = Math.sqrt(
        Math.pow(newX - this.dragStart.characterX, 2) + 
        Math.pow(newY - this.dragStart.characterY, 2)
      )
      
      if (distance <= this.movementRange) {
        this.$emit('move', this.character, { x: newX, y: newY })
      }
    },
    
    /**
     * 结束拖拽
     */
    endDrag() {
      this.isDragging = false
      this.dragStart = null
      
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.endDrag)
    },
    
    /**
     * 播放伤害动画
     */
    playDamageAnimation(damage, type = 'normal') {
      const colors = {
        normal: '#f44336',
        critical: '#e91e63',
        healing: '#4caf50',
        miss: '#9e9e9e'
      }
      
      const texts = {
        normal: `-${damage}`,
        critical: `CRIT! -${damage}`,
        healing: `+${damage}`,
        miss: 'MISS'
      }
      
      this.damageAnimation = {
        text: texts[type] || `-${damage}`,
        color: colors[type] || '#f44336'
      }
      
      // 1.5秒后清除动画
      setTimeout(() => {
        this.damageAnimation = null
      }, 1500)
    },
    
    /**
     * 播放治疗动画
     */
    playHealingAnimation(healing) {
      this.playDamageAnimation(healing, 'healing')
    },
    
    /**
     * 播放闪避动画
     */
    playMissAnimation() {
      this.playDamageAnimation(0, 'miss')
    }
  }
}
</script>

<style scoped>
.character-token {
  cursor: pointer;
  transition: all 0.3s ease;
}

.character-token:hover {
  filter: brightness(1.1);
}

.character-token.selected {
  filter: drop-shadow(0 0 8px rgba(52, 152, 219, 0.8));
}

.character-token.current-turn {
  filter: drop-shadow(0 0 12px rgba(231, 76, 60, 0.9));
}

.character-token.can-move {
  cursor: move;
}

.character-token.player .character-body circle:first-child {
  filter: drop-shadow(0 2px 4px rgba(33, 150, 243, 0.3));
}

.character-token.enemy .character-body circle:first-child {
  filter: drop-shadow(0 2px 4px rgba(244, 67, 54, 0.3));
}

.character-name-text {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  font-family: 'Arial', sans-serif;
}

.status-effects circle {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.action-indicators circle {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.damage-animation text {
  font-family: 'Arial Black', sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.3; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes damageFloat {
  0% { 
    transform: translateY(0); 
    opacity: 0; 
  }
  20% { 
    opacity: 1; 
  }
  80% { 
    opacity: 1; 
  }
  100% { 
    transform: translateY(-30px); 
    opacity: 0; 
  }
}
</style>