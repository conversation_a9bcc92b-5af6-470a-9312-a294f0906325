{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, withModifiers as _withModifiers } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"initiative-tracker\"\n};\nvar _hoisted_2 = {\n  \"class\": \"tracker-header\"\n};\nvar _hoisted_3 = {\n  \"class\": \"header-right\"\n};\nvar _hoisted_4 = {\n  \"class\": \"round-info\"\n};\nvar _hoisted_5 = {\n  \"class\": \"round-number\"\n};\nvar _hoisted_6 = {\n  \"class\": \"initiative-list\",\n  ref: \"initiativeList\"\n};\nvar _hoisted_7 = [\"onClick\"];\nvar _hoisted_8 = {\n  \"class\": \"initiative-value\"\n};\nvar _hoisted_9 = {\n  \"class\": \"initiative-number\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  \"class\": \"initiative-modifier\"\n};\nvar _hoisted_11 = {\n  \"class\": \"participant-info\"\n};\nvar _hoisted_12 = {\n  \"class\": \"participant-avatar\"\n};\nvar _hoisted_13 = [\"src\", \"alt\"];\nvar _hoisted_14 = {\n  key: 0,\n  \"class\": \"current-indicator\"\n};\nvar _hoisted_15 = {\n  \"class\": \"participant-details\"\n};\nvar _hoisted_16 = {\n  \"class\": \"participant-name\"\n};\nvar _hoisted_17 = {\n  \"class\": \"participant-type\"\n};\nvar _hoisted_18 = {\n  \"class\": \"participant-status\"\n};\nvar _hoisted_19 = {\n  \"class\": \"health-indicator\"\n};\nvar _hoisted_20 = {\n  \"class\": \"health-bar\"\n};\nvar _hoisted_21 = {\n  \"class\": \"health-text\"\n};\nvar _hoisted_22 = {\n  key: 0,\n  \"class\": \"status-effects\"\n};\nvar _hoisted_23 = [\"title\"];\nvar _hoisted_24 = [\"title\"];\nvar _hoisted_25 = {\n  key: 0,\n  \"class\": \"action-controls\"\n};\nvar _hoisted_26 = [\"onClick\"];\nvar _hoisted_27 = [\"onClick\"];\nvar _hoisted_28 = [\"onClick\", \"disabled\"];\nvar _hoisted_29 = [\"onClick\"];\nvar _hoisted_30 = {\n  key: 1,\n  \"class\": \"weapon-info\"\n};\nvar _hoisted_31 = {\n  \"class\": \"weapon-name\"\n};\nvar _hoisted_32 = {\n  key: 0,\n  \"class\": \"round-controls\"\n};\nvar _hoisted_33 = {\n  \"class\": \"control-group\"\n};\nvar _hoisted_34 = [\"disabled\"];\nvar _hoisted_35 = {\n  \"class\": \"control-group\"\n};\nvar _hoisted_36 = {\n  \"class\": \"tracker-stats\"\n};\nvar _hoisted_37 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_38 = {\n  \"class\": \"stat-value\"\n};\nvar _hoisted_39 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_40 = {\n  \"class\": \"stat-value\"\n};\nvar _hoisted_41 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_42 = {\n  \"class\": \"stat-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 追踪器头部 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n    \"class\": \"header-left\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-list-ol\"\n  }), _createElementVNode(\"h3\", null, \"先攻顺序\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n    \"class\": \"round-label\"\n  }, \"第\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_5, _toDisplayString($props.currentRound), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"span\", {\n    \"class\": \"round-label\"\n  }, \"轮\", -1 /* CACHED */))]), $props.canReroll ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.rerollInitiative && $options.rerollInitiative.apply($options, arguments);\n    }),\n    \"class\": \"reroll-btn\",\n    title: \"重新投掷先攻\"\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-dice\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 先攻列表 \"), _createElementVNode(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.initiativeOrder, function (participant, index) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: participant.id,\n      \"class\": _normalizeClass([\"initiative-item\", {\n        current: index === $props.currentTurn,\n        acted: participant.hasActed,\n        player: participant.isPlayer,\n        enemy: !participant.isPlayer,\n        unconscious: participant.currentHP <= 0,\n        delayed: participant.delayedAction\n      }]),\n      onClick: function onClick($event) {\n        return $options.selectParticipant(participant, index);\n      }\n    }, [_createCommentVNode(\" 先攻值 \"), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString(participant.initiative), 1 /* TEXT */), $options.getInitiativeModifier(participant) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, _toDisplayString($options.getInitiativeModifier(participant)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 参与者信息 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"img\", {\n      src: participant.avatar || '/default-avatar.png',\n      alt: participant.name,\n      \"class\": \"avatar-image\"\n    }, null, 8 /* PROPS */, _hoisted_13), _createCommentVNode(\" 当前回合指示器 \"), index === $props.currentTurn ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, _toConsumableArray(_cache[9] || (_cache[9] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-play\"\n    }, null, -1 /* CACHED */)])))) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString(participant.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_17, _toDisplayString(participant.isPlayer ? '玩家' : participant.type || 'NPC'), 1 /* TEXT */)])]), _createCommentVNode(\" 状态信息 \"), _createElementVNode(\"div\", _hoisted_18, [_createCommentVNode(\" 生命值指示器 \"), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"health-fill\", $options.getHealthClass(participant)]),\n      style: _normalizeStyle({\n        width: $options.getHealthPercentage(participant) + '%'\n      })\n    }, null, 6 /* CLASS, STYLE */)]), _createElementVNode(\"div\", _hoisted_21, _toDisplayString(participant.currentHP || 0) + \"/\" + _toDisplayString(participant.maxHP || participant.hitPoints || 0), 1 /* TEXT */)]), _createCommentVNode(\" 状态效果 \"), participant.conditions && participant.conditions.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(participant.conditions.slice(0, 3), function (effect) {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: effect,\n        \"class\": _normalizeClass([\"status-effect\", effect]),\n        title: $options.getStatusEffectName(effect)\n      }, _toDisplayString($options.getStatusEffectIcon(effect)), 11 /* TEXT, CLASS, PROPS */, _hoisted_23);\n    }), 128 /* KEYED_FRAGMENT */)), participant.conditions.length > 3 ? (_openBlock(), _createElementBlock(\"div\", {\n      key: 0,\n      \"class\": \"status-effect more\",\n      title: \"\\u8FD8\\u6709\".concat(participant.conditions.length - 3, \"\\u4E2A\\u72B6\\u6001\\u6548\\u679C\")\n    }, \" +\" + _toDisplayString(participant.conditions.length - 3), 9 /* TEXT, PROPS */, _hoisted_24)) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 行动控制 (仅KP可见) \"), $props.isKeeper ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [!participant.hasActed && index === $props.currentTurn ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 0,\n      onClick: _withModifiers(function ($event) {\n        return $options.markAsActed(participant);\n      }, [\"stop\"]),\n      \"class\": \"control-btn acted-btn\",\n      title: \"标记为已行动\"\n    }, _toConsumableArray(_cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-check\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_26)) : _createCommentVNode(\"v-if\", true), participant.hasActed ? (_openBlock(), _createElementBlock(\"button\", {\n      key: 1,\n      onClick: _withModifiers(function ($event) {\n        return $options.markAsNotActed(participant);\n      }, [\"stop\"]),\n      \"class\": \"control-btn unacted-btn\",\n      title: \"标记为未行动\"\n    }, _toConsumableArray(_cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-undo\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_27)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.delayAction(participant, index);\n      }, [\"stop\"]),\n      \"class\": \"control-btn delay-btn\",\n      title: \"延迟行动\",\n      disabled: participant.delayedAction\n    }, _toConsumableArray(_cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-clock\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_28), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.removeParticipant(participant);\n      }, [\"stop\"]),\n      \"class\": \"control-btn remove-btn\",\n      title: \"移除参与者\"\n    }, _toConsumableArray(_cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-times\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_29)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 武器信息 \"), participant.currentWeapon ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createElementVNode(\"i\", {\n      \"class\": _normalizeClass($options.getWeaponIcon(participant.currentWeapon))\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_31, _toDisplayString(participant.currentWeapon.name), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), _createCommentVNode(\" 回合控制 (仅KP可见) \"), $props.isKeeper ? (_openBlock(), _createElementBlock(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.previousTurn && $options.previousTurn.apply($options, arguments);\n    }),\n    \"class\": \"control-btn\",\n    disabled: $props.currentTurn === 0\n  }, _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-step-backward\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"上一个\", -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_34), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = function () {\n      return $options.nextTurn && $options.nextTurn.apply($options, arguments);\n    }),\n    \"class\": \"control-btn primary\"\n  }, _cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-step-forward\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"下一个\", -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = function () {\n      return $options.endRound && $options.endRound.apply($options, arguments);\n    }),\n    \"class\": \"control-btn warning\"\n  }, _cache[16] || (_cache[16] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-refresh\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"结束本轮\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function () {\n      return $options.resetInitiative && $options.resetInitiative.apply($options, arguments);\n    }),\n    \"class\": \"control-btn danger\"\n  }, _cache[17] || (_cache[17] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-redo\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"重置先攻\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 统计信息 \"), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"div\", _hoisted_37, [_cache[18] || (_cache[18] = _createElementVNode(\"span\", {\n    \"class\": \"stat-label\"\n  }, \"参与者:\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_38, _toDisplayString($props.initiativeOrder.length), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_39, [_cache[19] || (_cache[19] = _createElementVNode(\"span\", {\n    \"class\": \"stat-label\"\n  }, \"已行动:\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_40, _toDisplayString($options.actedCount) + \"/\" + _toDisplayString($props.initiativeOrder.length), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_41, [_cache[20] || (_cache[20] = _createElementVNode(\"span\", {\n    \"class\": \"stat-label\"\n  }, \"存活:\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_42, _toDisplayString($options.aliveCount) + \"/\" + _toDisplayString($props.initiativeOrder.length), 1 /* TEXT */)])])]);\n}", "map": {"version": 3, "names": ["ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_toDisplayString", "$props", "currentRound", "can<PERSON><PERSON><PERSON>", "onClick", "_cache", "$options", "rerollInitiative", "apply", "arguments", "title", "_hoisted_6", "_Fragment", "_renderList", "initiativeOrder", "participant", "index", "key", "id", "_normalizeClass", "currentTurn", "hasActed", "isPlayer", "currentHP", "delayedAction", "$event", "selectParticipant", "_hoisted_8", "_hoisted_9", "initiative", "getInitiativeModifier", "_hoisted_10", "_hoisted_11", "_hoisted_12", "src", "avatar", "alt", "name", "_hoisted_14", "_toConsumableArray", "_hoisted_15", "_hoisted_16", "_hoisted_17", "type", "_hoisted_18", "_hoisted_19", "_hoisted_20", "getHealthClass", "style", "_normalizeStyle", "width", "getHealthPercentage", "_hoisted_21", "maxHP", "hitPoints", "conditions", "length", "_hoisted_22", "slice", "effect", "getStatusEffectName", "getStatusEffectIcon", "_hoisted_23", "concat", "_hoisted_24", "<PERSON><PERSON><PERSON><PERSON>", "_hoisted_25", "_withModifiers", "markAsActed", "markAsNotActed", "delayAction", "disabled", "removeParticipant", "currentWeapon", "_hoisted_30", "getWeaponIcon", "_hoisted_31", "_hoisted_32", "_hoisted_33", "previousTurn", "nextTurn", "_hoisted_35", "endRound", "resetInitiative", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "actedCount", "_hoisted_41", "_hoisted_42", "aliveCount"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\InitiativeTracker.vue"], "sourcesContent": ["<template>\r\n  <div class=\"initiative-tracker\">\r\n    <!-- 追踪器头部 -->\r\n    <div class=\"tracker-header\">\r\n      <div class=\"header-left\">\r\n        <i class=\"fas fa-list-ol\"></i>\r\n        <h3>先攻顺序</h3>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"round-info\">\r\n          <span class=\"round-label\">第</span>\r\n          <span class=\"round-number\">{{ currentRound }}</span>\r\n          <span class=\"round-label\">轮</span>\r\n        </div>\r\n        <button \r\n          v-if=\"canReroll\" \r\n          @click=\"rerollInitiative\" \r\n          class=\"reroll-btn\"\r\n          title=\"重新投掷先攻\"\r\n        >\r\n          <i class=\"fas fa-dice\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 先攻列表 -->\r\n    <div class=\"initiative-list\" ref=\"initiativeList\">\r\n      <div \r\n        v-for=\"(participant, index) in initiativeOrder\"\r\n        :key=\"participant.id\"\r\n        class=\"initiative-item\"\r\n        :class=\"{ \r\n          current: index === currentTurn,\r\n          acted: participant.hasActed,\r\n          player: participant.isPlayer,\r\n          enemy: !participant.isPlayer,\r\n          unconscious: participant.currentHP <= 0,\r\n          delayed: participant.delayedAction\r\n        }\"\r\n        @click=\"selectParticipant(participant, index)\"\r\n      >\r\n        <!-- 先攻值 -->\r\n        <div class=\"initiative-value\">\r\n          <div class=\"initiative-number\">{{ participant.initiative }}</div>\r\n          <div class=\"initiative-modifier\" v-if=\"getInitiativeModifier(participant)\">\r\n            {{ getInitiativeModifier(participant) }}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 参与者信息 -->\r\n        <div class=\"participant-info\">\r\n          <div class=\"participant-avatar\">\r\n            <img \r\n              :src=\"participant.avatar || '/default-avatar.png'\" \r\n              :alt=\"participant.name\"\r\n              class=\"avatar-image\"\r\n            >\r\n            <!-- 当前回合指示器 -->\r\n            <div v-if=\"index === currentTurn\" class=\"current-indicator\">\r\n              <i class=\"fas fa-play\"></i>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"participant-details\">\r\n            <div class=\"participant-name\">{{ participant.name }}</div>\r\n            <div class=\"participant-type\">\r\n              {{ participant.isPlayer ? '玩家' : (participant.type || 'NPC') }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 状态信息 -->\r\n        <div class=\"participant-status\">\r\n          <!-- 生命值指示器 -->\r\n          <div class=\"health-indicator\">\r\n            <div class=\"health-bar\">\r\n              <div \r\n                class=\"health-fill\" \r\n                :class=\"getHealthClass(participant)\"\r\n                :style=\"{ width: getHealthPercentage(participant) + '%' }\"\r\n              ></div>\r\n            </div>\r\n            <div class=\"health-text\">\r\n              {{ participant.currentHP || 0 }}/{{ participant.maxHP || participant.hitPoints || 0 }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态效果 -->\r\n          <div class=\"status-effects\" v-if=\"participant.conditions && participant.conditions.length > 0\">\r\n            <div \r\n              v-for=\"effect in participant.conditions.slice(0, 3)\" \r\n              :key=\"effect\"\r\n              class=\"status-effect\"\r\n              :class=\"effect\"\r\n              :title=\"getStatusEffectName(effect)\"\r\n            >\r\n              {{ getStatusEffectIcon(effect) }}\r\n            </div>\r\n            <div \r\n              v-if=\"participant.conditions.length > 3\"\r\n              class=\"status-effect more\"\r\n              :title=\"`还有${participant.conditions.length - 3}个状态效果`\"\r\n            >\r\n              +{{ participant.conditions.length - 3 }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 行动控制 (仅KP可见) -->\r\n        <div class=\"action-controls\" v-if=\"isKeeper\">\r\n          <button \r\n            v-if=\"!participant.hasActed && index === currentTurn\"\r\n            @click.stop=\"markAsActed(participant)\"\r\n            class=\"control-btn acted-btn\"\r\n            title=\"标记为已行动\"\r\n          >\r\n            <i class=\"fas fa-check\"></i>\r\n          </button>\r\n          \r\n          <button \r\n            v-if=\"participant.hasActed\"\r\n            @click.stop=\"markAsNotActed(participant)\"\r\n            class=\"control-btn unacted-btn\"\r\n            title=\"标记为未行动\"\r\n          >\r\n            <i class=\"fas fa-undo\"></i>\r\n          </button>\r\n          \r\n          <button \r\n            @click.stop=\"delayAction(participant, index)\"\r\n            class=\"control-btn delay-btn\"\r\n            title=\"延迟行动\"\r\n            :disabled=\"participant.delayedAction\"\r\n          >\r\n            <i class=\"fas fa-clock\"></i>\r\n          </button>\r\n          \r\n          <button \r\n            @click.stop=\"removeParticipant(participant)\"\r\n            class=\"control-btn remove-btn\"\r\n            title=\"移除参与者\"\r\n          >\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 武器信息 -->\r\n        <div class=\"weapon-info\" v-if=\"participant.currentWeapon\">\r\n          <i :class=\"getWeaponIcon(participant.currentWeapon)\"></i>\r\n          <span class=\"weapon-name\">{{ participant.currentWeapon.name }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 回合控制 (仅KP可见) -->\r\n    <div class=\"round-controls\" v-if=\"isKeeper\">\r\n      <div class=\"control-group\">\r\n        <button @click=\"previousTurn\" class=\"control-btn\" :disabled=\"currentTurn === 0\">\r\n          <i class=\"fas fa-step-backward\"></i>\r\n          <span>上一个</span>\r\n        </button>\r\n        \r\n        <button @click=\"nextTurn\" class=\"control-btn primary\">\r\n          <i class=\"fas fa-step-forward\"></i>\r\n          <span>下一个</span>\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"control-group\">\r\n        <button @click=\"endRound\" class=\"control-btn warning\">\r\n          <i class=\"fas fa-refresh\"></i>\r\n          <span>结束本轮</span>\r\n        </button>\r\n        \r\n        <button @click=\"resetInitiative\" class=\"control-btn danger\">\r\n          <i class=\"fas fa-redo\"></i>\r\n          <span>重置先攻</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息 -->\r\n    <div class=\"tracker-stats\">\r\n      <div class=\"stat-item\">\r\n        <span class=\"stat-label\">参与者:</span>\r\n        <span class=\"stat-value\">{{ initiativeOrder.length }}</span>\r\n      </div>\r\n      <div class=\"stat-item\">\r\n        <span class=\"stat-label\">已行动:</span>\r\n        <span class=\"stat-value\">{{ actedCount }}/{{ initiativeOrder.length }}</span>\r\n      </div>\r\n      <div class=\"stat-item\">\r\n        <span class=\"stat-label\">存活:</span>\r\n        <span class=\"stat-value\">{{ aliveCount }}/{{ initiativeOrder.length }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'InitiativeTracker',\r\n  props: {\r\n    initiativeOrder: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    currentRound: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    currentTurn: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    isKeeper: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canReroll: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 已行动的参与者数量\r\n    actedCount() {\r\n      return this.initiativeOrder.filter(p => p.hasActed).length\r\n    },\r\n    \r\n    // 存活的参与者数量\r\n    aliveCount() {\r\n      return this.initiativeOrder.filter(p => (p.currentHP || 0) > 0).length\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    currentTurn(newTurn) {\r\n      // 自动滚动到当前回合\r\n      this.$nextTick(() => {\r\n        this.scrollToCurrentTurn(newTurn)\r\n      })\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 选择参与者\r\n    selectParticipant(participant, index) {\r\n      this.$emit('participant-selected', participant, index)\r\n    },\r\n    \r\n    // 获取生命值百分比\r\n    getHealthPercentage(participant) {\r\n      const current = participant.currentHP || 0\r\n      const max = participant.maxHP || participant.hitPoints || 1\r\n      return Math.max(0, Math.min(100, (current / max) * 100))\r\n    },\r\n    \r\n    // 获取生命值样式类\r\n    getHealthClass(participant) {\r\n      const percentage = this.getHealthPercentage(participant)\r\n      if (percentage > 75) return 'healthy'\r\n      if (percentage > 50) return 'injured'\r\n      if (percentage > 25) return 'wounded'\r\n      return 'critical'\r\n    },\r\n    \r\n    // 获取先攻修正值显示\r\n    getInitiativeModifier(participant) {\r\n      if (participant.initiativeModifier) {\r\n        return participant.initiativeModifier > 0 \r\n          ? `+${participant.initiativeModifier}` \r\n          : `${participant.initiativeModifier}`\r\n      }\r\n      return null\r\n    },\r\n    \r\n    // 获取状态效果名称\r\n    getStatusEffectName(effect) {\r\n      const names = {\r\n        bleeding: '流血',\r\n        poisoned: '中毒',\r\n        stunned: '眩晕',\r\n        frightened: '恐惧',\r\n        blessed: '祝福',\r\n        cursed: '诅咒',\r\n        prone: '倒地',\r\n        grappled: '被擒抱',\r\n        unconscious: '昏迷'\r\n      }\r\n      return names[effect] || effect\r\n    },\r\n    \r\n    // 获取状态效果图标\r\n    getStatusEffectIcon(effect) {\r\n      const icons = {\r\n        bleeding: '🩸',\r\n        poisoned: '☠️',\r\n        stunned: '😵',\r\n        frightened: '😨',\r\n        blessed: '✨',\r\n        cursed: '💀',\r\n        prone: '⬇️',\r\n        grappled: '🤝',\r\n        unconscious: '😴'\r\n      }\r\n      return icons[effect] || '❓'\r\n    },\r\n    \r\n    // 获取武器图标\r\n    getWeaponIcon(weapon) {\r\n      if (!weapon) return 'fas fa-fist-raised'\r\n      \r\n      const icons = {\r\n        melee: 'fas fa-sword',\r\n        ranged: 'fas fa-crosshairs',\r\n        firearm: 'fas fa-gun',\r\n        thrown: 'fas fa-hand-paper'\r\n      }\r\n      \r\n      return icons[weapon.type] || 'fas fa-fist-raised'\r\n    },\r\n    \r\n    // 标记为已行动\r\n    markAsActed(participant) {\r\n      this.$emit('mark-acted', participant)\r\n    },\r\n    \r\n    // 标记为未行动\r\n    markAsNotActed(participant) {\r\n      this.$emit('mark-not-acted', participant)\r\n    },\r\n    \r\n    // 延迟行动\r\n    delayAction(participant, index) {\r\n      this.$emit('delay-action', participant, index)\r\n    },\r\n    \r\n    // 移除参与者\r\n    removeParticipant(participant) {\r\n      this.$emit('remove-participant', participant)\r\n    },\r\n    \r\n    // 上一个回合\r\n    previousTurn() {\r\n      this.$emit('previous-turn')\r\n    },\r\n    \r\n    // 下一个回合\r\n    nextTurn() {\r\n      this.$emit('next-turn')\r\n    },\r\n    \r\n    // 结束本轮\r\n    endRound() {\r\n      this.$emit('end-round')\r\n    },\r\n    \r\n    // 重新投掷先攻\r\n    rerollInitiative() {\r\n      this.$emit('reroll-initiative')\r\n    },\r\n    \r\n    // 重置先攻\r\n    resetInitiative() {\r\n      this.$emit('reset-initiative')\r\n    },\r\n    \r\n    // 滚动到当前回合\r\n    scrollToCurrentTurn(turnIndex) {\r\n      const list = this.$refs.initiativeList\r\n      if (!list) return\r\n      \r\n      const items = list.querySelectorAll('.initiative-item')\r\n      const currentItem = items[turnIndex]\r\n      \r\n      if (currentItem) {\r\n        currentItem.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'nearest'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.initiative-tracker {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 2px solid #0f3460;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  color: #e94560;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  max-height: 600px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 追踪器头部 */\r\n.tracker-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #0f3460;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left h3 {\r\n  margin: 0;\r\n  color: #e94560;\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.round-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  padding: 6px 12px;\r\n  border-radius: 20px;\r\n  border: 1px solid #0f3460;\r\n}\r\n\r\n.round-label {\r\n  font-size: 0.9rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.round-number {\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.reroll-btn {\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 6px;\r\n  color: #e94560;\r\n  padding: 6px 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reroll-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n/* 先攻列表 */\r\n.initiative-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 16px;\r\n  max-height: 400px;\r\n}\r\n\r\n.initiative-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.initiative-item:hover {\r\n  background: rgba(15, 52, 96, 0.5);\r\n  border-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.initiative-item.current {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n  box-shadow: 0 0 12px rgba(233, 69, 96, 0.3);\r\n}\r\n\r\n.initiative-item.acted {\r\n  opacity: 0.7;\r\n  background: rgba(39, 174, 96, 0.2);\r\n  border-color: rgba(39, 174, 96, 0.5);\r\n}\r\n\r\n.initiative-item.player {\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.initiative-item.enemy {\r\n  border-left: 4px solid #e74c3c;\r\n}\r\n\r\n.initiative-item.unconscious {\r\n  opacity: 0.5;\r\n  filter: grayscale(50%);\r\n}\r\n\r\n.initiative-item.delayed {\r\n  border-style: dashed;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 先攻值 */\r\n.initiative-value {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-width: 50px;\r\n}\r\n\r\n.initiative-number {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #e94560, #f27121);\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 1.1rem;\r\n  box-shadow: 0 2px 8px rgba(233, 69, 96, 0.3);\r\n}\r\n\r\n.initiative-modifier {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n  margin-top: 2px;\r\n}\r\n\r\n/* 参与者信息 */\r\n.participant-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n}\r\n\r\n.participant-avatar {\r\n  position: relative;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.current-indicator {\r\n  position: absolute;\r\n  top: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  background: #27ae60;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 8px;\r\n  animation: current-pulse 1s infinite;\r\n}\r\n\r\n@keyframes current-pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.2); }\r\n}\r\n\r\n.participant-details {\r\n  flex: 1;\r\n}\r\n\r\n.participant-name {\r\n  font-weight: bold;\r\n  color: #ecf0f1;\r\n  font-size: 1rem;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.participant-type {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n/* 状态信息 */\r\n.participant-status {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 4px;\r\n  min-width: 80px;\r\n}\r\n\r\n.health-indicator {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.health-bar {\r\n  width: 60px;\r\n  height: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.health-fill {\r\n  height: 100%;\r\n  border-radius: 4px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.health-fill.healthy { background: #27ae60; }\r\n.health-fill.injured { background: #f39c12; }\r\n.health-fill.wounded { background: #e67e22; }\r\n.health-fill.critical { background: #e74c3c; }\r\n\r\n.health-text {\r\n  font-size: 0.8rem;\r\n  color: #ecf0f1;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-effects {\r\n  display: flex;\r\n  gap: 2px;\r\n  flex-wrap: wrap;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.status-effect {\r\n  width: 16px;\r\n  height: 16px;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.status-effect.more {\r\n  background: rgba(233, 69, 96, 0.8);\r\n  color: white;\r\n  font-size: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 行动控制 */\r\n.action-controls {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.control-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.acted-btn {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.unacted-btn {\r\n  background: #f39c12;\r\n  color: white;\r\n}\r\n\r\n.delay-btn {\r\n  background: #3498db;\r\n  color: white;\r\n}\r\n\r\n.remove-btn {\r\n  background: #e74c3c;\r\n  color: white;\r\n}\r\n\r\n.control-btn:hover:not(:disabled) {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.control-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 武器信息 */\r\n.weapon-info {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 0.7rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.weapon-name {\r\n  max-width: 60px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 回合控制 */\r\n.round-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n  margin-bottom: 12px;\r\n  padding-top: 12px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.control-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.round-controls .control-btn {\r\n  padding: 8px 12px;\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 6px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 0.9rem;\r\n  height: auto;\r\n  width: auto;\r\n}\r\n\r\n.round-controls .control-btn:hover:not(:disabled) {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.round-controls .control-btn.primary {\r\n  background: linear-gradient(135deg, #e94560, #f27121);\r\n  color: white;\r\n  border-color: #e94560;\r\n}\r\n\r\n.round-controls .control-btn.warning {\r\n  background: rgba(243, 156, 18, 0.2);\r\n  border-color: #f39c12;\r\n  color: #f39c12;\r\n}\r\n\r\n.round-controls .control-btn.danger {\r\n  background: rgba(231, 76, 60, 0.2);\r\n  border-color: #e74c3c;\r\n  color: #e74c3c;\r\n}\r\n\r\n/* 统计信息 */\r\n.tracker-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 8px 12px;\r\n  background: rgba(15, 52, 96, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 1rem;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .initiative-tracker {\r\n    padding: 12px;\r\n    max-height: 400px;\r\n  }\r\n  \r\n  .initiative-item {\r\n    padding: 8px;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .initiative-number {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .participant-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n  \r\n  .participant-name {\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .health-bar {\r\n    width: 50px;\r\n    height: 6px;\r\n  }\r\n  \r\n  .round-controls {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .control-group {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;EACO,SAAM;AAAoB;;EAExB,SAAM;AAAgB;;EAKpB,SAAM;AAAc;;EAClB,SAAM;AAAY;;EAEf,SAAM;AAAc;;EAe3B,SAAM,iBAAiB;EAACA,GAAG,EAAC;;;;EAgBxB,SAAM;AAAkB;;EACtB,SAAM;AAAmB;;;EACzB,SAAM;;;EAMR,SAAM;AAAkB;;EACtB,SAAM;AAAoB;;;;EAOK,SAAM;;;EAKrC,SAAM;AAAqB;;EACzB,SAAM;AAAkB;;EACxB,SAAM;AAAkB;;EAO5B,SAAM;AAAoB;;EAExB,SAAM;AAAkB;;EACtB,SAAM;AAAY;;EAOlB,SAAM;AAAa;;;EAMrB,SAAM;;;;;;EAqBR,SAAM;;;;;;;;EAsCN,SAAM;;;EAEH,SAAM;AAAa;;;EAM1B,SAAM;;;EACJ,SAAM;AAAe;;;EAYrB,SAAM;AAAe;;EAcvB,SAAM;AAAe;;EACnB,SAAM;AAAW;;EAEd,SAAM;AAAY;;EAErB,SAAM;AAAW;;EAEd,SAAM;AAAY;;EAErB,SAAM;AAAW;;EAEd,SAAM;AAAY;;uBAhM9BC,mBAAA,CAmMM,OAnMNC,UAmMM,GAlMJC,mBAAA,WAAc,EACdC,mBAAA,CAoBM,OApBNC,UAoBM,G,0BAnBJD,mBAAA,CAGM;IAHD,SAAM;EAAa,IACtBA,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,IACzBA,mBAAA,CAAa,YAAT,MAAI,E,qBAEVA,mBAAA,CAcM,OAdNE,UAcM,GAbJF,mBAAA,CAIM,OAJNG,UAIM,G,0BAHJH,mBAAA,CAAkC;IAA5B,SAAM;EAAa,GAAC,GAAC,qBAC3BA,mBAAA,CAAoD,QAApDI,UAAoD,EAAAC,gBAAA,CAAtBC,MAAA,CAAAC,YAAY,kB,0BAC1CP,mBAAA,CAAkC;IAA5B,SAAM;EAAa,GAAC,GAAC,oB,GAGrBM,MAAA,CAAAE,SAAS,I,cADjBX,mBAAA,CAOS;;IALNY,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;IACxB,SAAM,YAAY;IAClBC,KAAK,EAAC;gCAENf,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,6CAK5BD,mBAAA,UAAa,EACbC,mBAAA,CA8HM,OA9HNgB,UA8HM,I,kBA7HJnB,mBAAA,CA4HMoB,SAAA,QAAAC,WAAA,CA3H2BZ,MAAA,CAAAa,eAAe,YAAtCC,WAAW,EAAEC,KAAK;yBAD5BxB,mBAAA,CA4HM;MA1HHyB,GAAG,EAAEF,WAAW,CAACG,EAAE;MACpB,SAAKC,eAAA,EAAC,iBAAiB;iBACQH,KAAK,KAAKf,MAAA,CAAAmB,WAAW;eAAoBL,WAAW,CAACM,QAAQ;gBAAqBN,WAAW,CAACO,QAAQ;gBAAqBP,WAAW,CAACO,QAAQ;qBAA0BP,WAAW,CAACQ,SAAS;iBAA2BR,WAAW,CAACS;;MAQnQpB,OAAK,WAALA,OAAKA,CAAAqB,MAAA;QAAA,OAAEnB,QAAA,CAAAoB,iBAAiB,CAACX,WAAW,EAAEC,KAAK;MAAA;QAE5CtB,mBAAA,SAAY,EACZC,mBAAA,CAKM,OALNgC,UAKM,GAJJhC,mBAAA,CAAiE,OAAjEiC,UAAiE,EAAA5B,gBAAA,CAA/Be,WAAW,CAACc,UAAU,kBACjBvB,QAAA,CAAAwB,qBAAqB,CAACf,WAAW,K,cAAxEvB,mBAAA,CAEM,OAFNuC,WAEM,EAAA/B,gBAAA,CADDM,QAAA,CAAAwB,qBAAqB,CAACf,WAAW,qB,qCAIxCrB,mBAAA,WAAc,EACdC,mBAAA,CAmBM,OAnBNqC,WAmBM,GAlBJrC,mBAAA,CAUM,OAVNsC,WAUM,GATJtC,mBAAA,CAIC;MAHEuC,GAAG,EAAEnB,WAAW,CAACoB,MAAM;MACvBC,GAAG,EAAErB,WAAW,CAACsB,IAAI;MACtB,SAAM;0CAER3C,mBAAA,aAAgB,EACLsB,KAAK,KAAKf,MAAA,CAAAmB,WAAW,I,cAAhC5B,mBAAA,CAEM,OAFN8C,WAEM,EAAAC,kBAAA,CAAAlC,MAAA,QAAAA,MAAA,OADJV,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B,4CAI1BA,mBAAA,CAKM,OALN6C,WAKM,GAJJ7C,mBAAA,CAA0D,OAA1D8C,WAA0D,EAAAzC,gBAAA,CAAzBe,WAAW,CAACsB,IAAI,kBACjD1C,mBAAA,CAEM,OAFN+C,WAEM,EAAA1C,gBAAA,CADDe,WAAW,CAACO,QAAQ,UAAWP,WAAW,CAAC4B,IAAI,0B,KAKxDjD,mBAAA,UAAa,EACbC,mBAAA,CAkCM,OAlCNiD,WAkCM,GAjCJlD,mBAAA,YAAe,EACfC,mBAAA,CAWM,OAXNkD,WAWM,GAVJlD,mBAAA,CAMM,OANNmD,WAMM,GALJnD,mBAAA,CAIO;MAHL,SAAKwB,eAAA,EAAC,aAAa,EACXb,QAAA,CAAAyC,cAAc,CAAChC,WAAW;MACjCiC,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAW5C,QAAA,CAAA6C,mBAAmB,CAACpC,WAAW;MAAA;sCAGpDpB,mBAAA,CAEM,OAFNyD,WAEM,EAAApD,gBAAA,CADDe,WAAW,CAACQ,SAAS,SAAQ,GAAC,GAAAvB,gBAAA,CAAGe,WAAW,CAACsC,KAAK,IAAItC,WAAW,CAACuC,SAAS,sB,GAIlF5D,mBAAA,UAAa,EACqBqB,WAAW,CAACwC,UAAU,IAAIxC,WAAW,CAACwC,UAAU,CAACC,MAAM,Q,cAAzFhE,mBAAA,CAiBM,OAjBNiE,WAiBM,I,kBAhBJjE,mBAAA,CAQMoB,SAAA,QAAAC,WAAA,CAPaE,WAAW,CAACwC,UAAU,CAACG,KAAK,kBAAtCC,MAAM;2BADfnE,mBAAA,CAQM;QANHyB,GAAG,EAAE0C,MAAM;QACZ,SAAKxC,eAAA,EAAC,eAAe,EACbwC,MAAM;QACbjD,KAAK,EAAEJ,QAAA,CAAAsD,mBAAmB,CAACD,MAAM;0BAE/BrD,QAAA,CAAAuD,mBAAmB,CAACF,MAAM,iCAAAG,WAAA;oCAGvB/C,WAAW,CAACwC,UAAU,CAACC,MAAM,Q,cADrChE,mBAAA,CAMM;;MAJJ,SAAM,oBAAoB;MACzBkB,KAAK,iBAAAqD,MAAA,CAAOhD,WAAW,CAACwC,UAAU,CAACC,MAAM;OAC3C,IACE,GAAAxD,gBAAA,CAAGe,WAAW,CAACwC,UAAU,CAACC,MAAM,4BAAAQ,WAAA,K,4EAKvCtE,mBAAA,kBAAqB,EACcO,MAAA,CAAAgE,QAAQ,I,cAA3CzE,mBAAA,CAmCM,OAnCN0E,WAmCM,G,CAjCKnD,WAAW,CAACM,QAAQ,IAAIL,KAAK,KAAKf,MAAA,CAAAmB,WAAW,I,cADtD5B,mBAAA,CAOS;;MALNY,OAAK,EAAA+D,cAAA,WAAA1C,MAAA;QAAA,OAAOnB,QAAA,CAAA8D,WAAW,CAACrD,WAAW;MAAA;MACpC,SAAM,uBAAuB;MAC7BL,KAAK,EAAC;uDAENf,mBAAA,CAA4B;MAAzB,SAAM;IAAc,0B,sEAIjBoB,WAAW,CAACM,QAAQ,I,cAD5B7B,mBAAA,CAOS;;MALNY,OAAK,EAAA+D,cAAA,WAAA1C,MAAA;QAAA,OAAOnB,QAAA,CAAA+D,cAAc,CAACtD,WAAW;MAAA;MACvC,SAAM,yBAAyB;MAC/BL,KAAK,EAAC;uDAENf,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B,sEAGxBA,mBAAA,CAOS;MANNS,OAAK,EAAA+D,cAAA,WAAA1C,MAAA;QAAA,OAAOnB,QAAA,CAAAgE,WAAW,CAACvD,WAAW,EAAEC,KAAK;MAAA;MAC3C,SAAM,uBAAuB;MAC7BN,KAAK,EAAC,MAAM;MACX6D,QAAQ,EAAExD,WAAW,CAACS;uDAEvB7B,mBAAA,CAA4B;MAAzB,SAAM;IAAc,0B,iCAGzBA,mBAAA,CAMS;MALNS,OAAK,EAAA+D,cAAA,WAAA1C,MAAA;QAAA,OAAOnB,QAAA,CAAAkE,iBAAiB,CAACzD,WAAW;MAAA;MAC1C,SAAM,wBAAwB;MAC9BL,KAAK,EAAC;uDAENf,mBAAA,CAA4B;MAAzB,SAAM;IAAc,0B,wEAI3BD,mBAAA,UAAa,EACkBqB,WAAW,CAAC0D,aAAa,I,cAAxDjF,mBAAA,CAGM,OAHNkF,WAGM,GAFJ/E,mBAAA,CAAyD;MAArD,SAAKwB,eAAA,CAAEb,QAAA,CAAAqE,aAAa,CAAC5D,WAAW,CAAC0D,aAAa;6BAClD9E,mBAAA,CAAqE,QAArEiF,WAAqE,EAAA5E,gBAAA,CAAxCe,WAAW,CAAC0D,aAAa,CAACpC,IAAI,iB;0DAKjE3C,mBAAA,kBAAqB,EACaO,MAAA,CAAAgE,QAAQ,I,cAA1CzE,mBAAA,CAwBM,OAxBNqF,WAwBM,GAvBJlF,mBAAA,CAUM,OAVNmF,WAUM,GATJnF,mBAAA,CAGS;IAHAS,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAyE,YAAA,IAAAzE,QAAA,CAAAyE,YAAA,CAAAvE,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;IAAA;IAAE,SAAM,aAAa;IAAE8D,QAAQ,EAAEtE,MAAA,CAAAmB,WAAW;kCACtEzB,mBAAA,CAAoC;IAAjC,SAAM;EAAsB,2BAC/BA,mBAAA,CAAgB,cAAV,KAAG,mB,gCAGXA,mBAAA,CAGS;IAHAS,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA0E,QAAA,IAAA1E,QAAA,CAAA0E,QAAA,CAAAxE,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAQ;IAAA;IAAE,SAAM;kCAC9Bd,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,2BAC9BA,mBAAA,CAAgB,cAAV,KAAG,mB,MAIbA,mBAAA,CAUM,OAVNsF,WAUM,GATJtF,mBAAA,CAGS;IAHAS,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA4E,QAAA,IAAA5E,QAAA,CAAA4E,QAAA,CAAA1E,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAQ;IAAA;IAAE,SAAM;kCAC9Bd,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2BACzBA,mBAAA,CAAiB,cAAX,MAAI,mB,IAGZA,mBAAA,CAGS;IAHAS,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA6E,eAAA,IAAA7E,QAAA,CAAA6E,eAAA,CAAA3E,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAAE,SAAM;kCACrCd,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,6CAKhBD,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNyF,WAaM,GAZJzF,mBAAA,CAGM,OAHN0F,WAGM,G,4BAFJ1F,mBAAA,CAAoC;IAA9B,SAAM;EAAY,GAAC,MAAI,qBAC7BA,mBAAA,CAA4D,QAA5D2F,WAA4D,EAAAtF,gBAAA,CAAhCC,MAAA,CAAAa,eAAe,CAAC0C,MAAM,iB,GAEpD7D,mBAAA,CAGM,OAHN4F,WAGM,G,4BAFJ5F,mBAAA,CAAoC;IAA9B,SAAM;EAAY,GAAC,MAAI,qBAC7BA,mBAAA,CAA6E,QAA7E6F,WAA6E,EAAAxF,gBAAA,CAAjDM,QAAA,CAAAmF,UAAU,IAAG,GAAC,GAAAzF,gBAAA,CAAGC,MAAA,CAAAa,eAAe,CAAC0C,MAAM,iB,GAErE7D,mBAAA,CAGM,OAHN+F,WAGM,G,4BAFJ/F,mBAAA,CAAmC;IAA7B,SAAM;EAAY,GAAC,KAAG,qBAC5BA,mBAAA,CAA6E,QAA7EgG,WAA6E,EAAA3F,gBAAA,CAAjDM,QAAA,CAAAsF,UAAU,IAAG,GAAC,GAAA5F,gBAAA,CAAGC,MAAA,CAAAa,eAAe,CAAC0C,MAAM,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}