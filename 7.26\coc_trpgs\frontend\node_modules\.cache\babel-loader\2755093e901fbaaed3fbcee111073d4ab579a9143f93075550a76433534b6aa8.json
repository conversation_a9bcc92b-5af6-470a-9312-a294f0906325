{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _objectSpread from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _regenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"core-js/modules/es.symbol.js\";\nimport \"core-js/modules/es.symbol.description.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.find.js\";\nimport \"core-js/modules/es.array.find-index.js\";\nimport \"core-js/modules/es.array.for-each.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.reduce.js\";\nimport \"core-js/modules/es.array.some.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.array.unshift.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.date.to-iso-string.js\";\nimport \"core-js/modules/es.date.to-json.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.parse-int.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.trim.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.timers.js\";\nimport ChatBox from '@/components/ChatBox.vue';\nimport Modal from '@/components/Modal.vue';\nimport FloatingPanel from '@/components/FloatingPanel.vue';\nimport FloatingCharacterSheet from '@/components/FloatingCharacterSheet.vue';\nimport FloatingDiceRoller from '@/components/FloatingDiceRoller.vue';\nimport FloatingNotes from '@/components/FloatingNotes.vue';\nimport AIModelSettings from '@/components/AIModelSettings.vue';\nimport MapSystem from '@/components/MapSystem.vue';\nimport ClueBoard from '@/components/ClueBoard.vue';\nimport AudioSystem from '@/components/AudioSystem.vue';\nimport VoiceChat from '@/components/VoiceChat.vue';\nimport PrivateChatManager from '@/components/PrivateChatManager.vue';\nimport GroupChat from '@/components/GroupChat.vue';\nimport GameSaveManager from '@/components/GameSaveManager.vue';\nimport ScenarioViewer from '@/components/ScenarioViewer.vue';\nimport AnnouncementViewer from '@/components/AnnouncementViewer.vue';\nimport apiService from '@/services/api';\nimport websocketService from '@/services/websocket';\nimport { storageMixin } from '@/mixins/storageMixin';\nexport default {\n  name: 'Room',\n  mixins: [storageMixin],\n  components: {\n    ChatBox: ChatBox,\n    Modal: Modal,\n    FloatingPanel: FloatingPanel,\n    FloatingCharacterSheet: FloatingCharacterSheet,\n    FloatingDiceRoller: FloatingDiceRoller,\n    FloatingNotes: FloatingNotes,\n    AIModelSettings: AIModelSettings,\n    MapSystem: MapSystem,\n    ClueBoard: ClueBoard,\n    CombatSystem: function CombatSystem() {\n      return import('@/components/CombatSystem.vue');\n    },\n    SkillCheckSystem: function SkillCheckSystem() {\n      return import('@/components/SkillCheckSystem.vue');\n    },\n    EquipmentSystem: function EquipmentSystem() {\n      return import('@/components/EquipmentSystem.vue');\n    },\n    ExperiencePackSystem: function ExperiencePackSystem() {\n      return import('@/components/ExperiencePackSystem.vue');\n    },\n    SpellSystem: function SpellSystem() {\n      return import('@/components/SpellSystem.vue');\n    },\n    MadnessSystem: function MadnessSystem() {\n      return import('@/components/MadnessSystem.vue');\n    },\n    MythosLibrary: function MythosLibrary() {\n      return import('@/components/MythosLibrary.vue');\n    },\n    AudioSystem: AudioSystem,\n    VoiceChat: VoiceChat,\n    PrivateChatManager: PrivateChatManager,\n    GroupChat: GroupChat,\n    GameSaveManager: GameSaveManager,\n    ScenarioViewer: ScenarioViewer,\n    AnnouncementViewer: AnnouncementViewer\n  },\n  props: {\n    // 不再使用props传递roomId\n  },\n  data: function data() {\n    return {\n      room: {},\n      messages: [],\n      characters: [],\n      selectedCharacter: null,\n      websocket: null,\n      showCharacterModal: false,\n      showSettingsModal: false,\n      diceHistory: [],\n      gameNotes: '',\n      onlineUsers: [],\n      isTyping: false,\n      typingUsername: '',\n      typingTimer: null,\n      lastTypingTime: 0,\n      fontSize: 16,\n      playSounds: true,\n      showNotifications: false,\n      isToolbarCollapsed: false,\n      isMobile: false,\n      internalRoomId: '',\n      // 使用内部变量存储房间ID\n      hasUnsavedChanges: false,\n      // 跟踪是否有未保存的更改\n      visiblePanels: {\n        character: false,\n        dice: false,\n        notes: false,\n        aiSettings: false,\n        map: false,\n        clueBoard: false,\n        audioSystem: false,\n        voiceChat: false,\n        privateChat: false,\n        // 私聊面板状态\n        groupChat: false,\n        gameSaves: false,\n        // 游戏存档面板状态\n        scenarioViewer: false,\n        // 剧本查看器面板状态\n        announcement: false,\n        // 公告展示面板状态\n        combat: false,\n        // 战斗系统面板状态\n        skillCheck: false,\n        // 技能检定系统面板状态\n        equipment: false,\n        // 装备系统面板状态\n        experiencePack: false,\n        // 经历包系统面板状态\n        spells: false,\n        // 法术系统面板状态\n        madness: false,\n        // 疯狂症状系统面板状态\n        library: false // 神话典籍图书馆面板状态\n      },\n      currentAIMode: 'normal',\n      // 将在 mounted 中从存储加载\n\n      // 新功能相关状态\n      skillCheckCharacter: null,\n      // 技能检定选中的角色\n      skillCheckSkill: '',\n      // 技能检定选中的技能\n      pendingSceneDescription: null,\n      // AI组件状态\n      aiStatus: {\n        active: false,\n        busy: false\n      },\n      // 房主状态\n      isRoomOwner: false,\n      // 控制API配置提醒显示\n      showConfigAlert: true,\n      // 角色同步定时器\n      characterSyncTimer: null,\n      // 移除默认场景数据\n      scenes: [],\n      currentSceneIndex: 0,\n      currentCharacterId: null,\n      showNotes: false,\n      // 添加缺失的变量\n      isKP: false,\n      currentUser: null,\n      users: [],\n      isLoading: false,\n      showDiceRoller: false,\n      showCharacterSheet: false,\n      // 添加方法引用\n      rollDice: null,\n      uploadScript: null,\n      quickSaveId: null,\n      // 存储快速存档的ID\n      isPreviewMode: false,\n      tempState: null,\n      windowWidth: window.innerWidth,\n      windowHeight: window.innerHeight,\n      showSaveOptions: false,\n      // 剧本相关数据\n      scenarioContent: '',\n      scenarioTitle: '',\n      scenarioFileSize: '',\n      // 公告相关数据\n      announcementContent: '',\n      announcementTitle: '房间公告',\n      announcementTime: new Date().toLocaleString(),\n      // 房间名称编辑相关\n      isEditingRoomName: false,\n      editableRoomName: ''\n    };\n  },\n  computed: {\n    isDarkMode: function isDarkMode() {\n      return this.$store.getters.isDarkTheme;\n    },\n    hasQuickSave: function hasQuickSave() {\n      // 检查是否有快速存档\n      return this.quickSaveId !== null;\n    }\n  },\n  created: function created() {\n    var _this = this;\n    return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n      var routeId, numericId, savedToolbarState, _error$response, mockRoom, _t;\n      return _regenerator().w(function (_context) {\n        while (1) switch (_context.n) {\n          case 0:\n            _context.p = 0;\n            // 从路由参数获取房间ID\n            routeId = _this.$route.params.id;\n            console.log(\"Room\\u7EC4\\u4EF6\\u521B\\u5EFA, \\u8DEF\\u7531\\u53C2\\u6570id = \".concat(routeId));\n\n            // 清除之前的房间数据，确保每次都获取新的房间\n            _this.$store.commit('CLEAR_CURRENT_ROOM');\n\n            // 尝试将路由ID转换为数字\n            numericId = parseInt(routeId); // 如果转换成功并且是有效数字，使用数字ID；否则使用默认ID 1\n            if (!isNaN(numericId)) {\n              _this.internalRoomId = numericId;\n            } else {\n              console.error(\"\\u65E0\\u6548\\u7684\\u623F\\u95F4ID: \".concat(routeId, \"\\uFF0C\\u4F7F\\u7528\\u9ED8\\u8BA4ID 1\"));\n              _this.internalRoomId = 1;\n            }\n            console.log(\"Room\\u7EC4\\u4EF6\\u521B\\u5EFA, \\u539F\\u59CBroomId = \".concat(routeId, \", \\u5904\\u7406\\u540EroomId = \").concat(_this.internalRoomId));\n\n            // 初始化用户数据\n            _this.currentUser = _this.$store.getters.currentUser || {\n              username: '玩家'\n            };\n            _this.isKP = _this.currentUser.id === 1; // 假设ID为1的是KP\n\n            // 初始化窗口尺寸\n            _this.updateWindowSize();\n\n            // 检查移动设备\n            _this.checkMobileDevice();\n\n            // 加载工具栏折叠状态\n            savedToolbarState = _this.safeGetItem(\"toolbar_collapsed_\".concat(_this.internalRoomId));\n            if (savedToolbarState !== null) {\n              _this.isToolbarCollapsed = savedToolbarState === 'true';\n              console.log('加载工具栏状态:', _this.isToolbarCollapsed ? '已折叠' : '已展开');\n            } else {\n              // 默认不折叠\n              _this.isToolbarCollapsed = false;\n            }\n\n            // 首先加载房间数据\n            _context.n = 1;\n            return _this.fetchRoomData();\n          case 1:\n            console.log('房间数据加载完成', _this.room);\n\n            // 检查当前用户是否是房主\n            _this.checkRoomOwnership();\n\n            // 强制设置为房主（临时解决方案）\n            _this.isRoomOwner = true;\n\n            // 建立WebSocket连接\n            _this.connectWebSocket();\n\n            // 获取用户角色\n            _context.n = 2;\n            return _this.fetchUserCharacters();\n          case 2:\n            console.log('角色数据加载完成', _this.characters);\n\n            // 获取历史消息 (之后获取，因为需要WebSocket连接)\n            setTimeout(function () {\n              _this.fetchRoomMessages();\n            }, 1000);\n\n            // 加载用户设置\n            _this.loadUserSettings();\n\n            // 从本地存储加载公告\n            _this.loadAnnouncementFromLocalStorage();\n\n            // 从本地存储加载房间名称\n            _this.loadRoomNameFromLocalStorage();\n\n            // 默认显示公告面板\n            _this.togglePanel('announcement');\n\n            // 默认显示角色卡和骰子面板\n            // 注意：这里不需要设置默认值，因为在loadUserSettings中已经处理了\n            // 只需要确保同步状态到显示变量\n            _this.showCharacterSheet = _this.visiblePanels.character;\n            _this.showDiceRoller = _this.visiblePanels.dice;\n\n            // 设置延时检查系统状态\n            setTimeout(function () {\n              _this.checkSystemStatus();\n            }, 2000);\n\n            // 检查是否已经关闭过提醒\n            if (_this.safeGetItem('aiConfigAlertDismissed') === 'true') {\n              _this.showConfigAlert = false;\n            }\n\n            // 检查API密钥状态\n            _this.checkApiKeyStatus();\n\n            // 同步选中角色的状态\n            _this.syncSelectedCharacterStatus();\n\n            // 设置定时同步角色状态\n            _this.setupCharacterSyncTimer();\n\n            // 初始化方法引用\n            _this.rollDice = _this.handleRollDice;\n            _this.uploadScript = _this.handleUploadScript;\n\n            // 初始化用户列表\n            _this.users = _this.onlineUsers;\n\n            // 添加键盘事件监听\n            window.addEventListener('keydown', _this.handleKeyDown);\n            _context.n = 4;\n            break;\n          case 3:\n            _context.p = 3;\n            _t = _context.v;\n            console.error('初始化房间失败', _t);\n            if (_t.message === \"房间不存在\" || ((_error$response = _t.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.detail) === \"房间不存在\") {\n              // 如果房间不存在，创建一个新房间\n              console.log('尝试创建房间', _this.internalRoomId);\n              mockRoom = {\n                id: parseInt(_this.internalRoomId),\n                name: \"\\u6D4B\\u8BD5\\u623F\\u95F4 \".concat(_this.internalRoomId),\n                description: '这是一个自动创建的房间'\n              };\n              _this.$store.commit('SET_CURRENT_ROOM', mockRoom);\n              _this.room = mockRoom;\n              // 重新尝试WebSocket连接\n              _this.connectWebSocket();\n\n              // 添加系统消息提示\n              _this.showSystemMessage('已自动创建测试房间');\n\n              // 获取用户角色\n              _this.fetchUserCharacters()[\"catch\"](function (e) {\n                return console.error('获取角色失败', e);\n              });\n            } else {\n              _this.showSystemMessage('加载房间失败，请尝试重新进入');\n              setTimeout(function () {\n                return _this.$router.push('/rooms');\n              }, 2000);\n            }\n          case 4:\n            return _context.a(2);\n        }\n      }, _callee, null, [[0, 3]]);\n    }))();\n  },\n  mounted: function mounted() {\n    // 注意：房间ID已在created钩子中设置，这里不需要重新设置\n    console.log(\"Room\\u7EC4\\u4EF6mounted, \\u5F53\\u524D\\u623F\\u95F4ID = \".concat(this.internalRoomId));\n\n    // 加载用户设置\n    this.loadUserSettings();\n\n    // 检查设备类型\n    this.detectDevice();\n    window.addEventListener('resize', this.detectDevice);\n\n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('mozfullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('MSFullscreenChange', this.handleFullScreenChange);\n\n    // 尝试预先检测浏览器全屏支持情况\n    var elem = document.querySelector('.room-container');\n    if (!elem.requestFullscreen && !elem.webkitRequestFullscreen && !elem.mozRequestFullScreen && !elem.msRequestFullscreen) {\n      console.warn('当前浏览器可能不完全支持全屏API');\n    } else {\n      console.log('浏览器支持全屏API');\n    }\n\n    // 添加键盘快捷键监听\n    window.addEventListener('keydown', this.handleKeyboardShortcuts);\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 移除键盘事件监听\n    window.removeEventListener('keydown', this.handleKeyDown);\n\n    // 清除角色同步定时器\n    if (this.characterSyncTimer) {\n      clearInterval(this.characterSyncTimer);\n    }\n\n    // 关闭WebSocket连接\n    if (this.websocket) {\n      this.websocket.close();\n    }\n\n    // 断开WebSocket连接\n    this.disconnectWebSocket();\n\n    // 移除事件监听器\n    window.removeEventListener('resize', this.detectDevice);\n\n    // 移除全屏状态变化监听器\n    document.removeEventListener('fullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('webkitfullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('mozfullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('MSFullscreenChange', this.handleFullScreenChange);\n\n    // 如果离开页面时处于全屏状态，尝试退出全屏\n    try {\n      var isFullScreen = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;\n      if (isFullScreen) {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitExitFullscreen) {\n          document.webkitExitFullscreen();\n        } else if (document.mozCancelFullScreen) {\n          document.mozCancelFullScreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      }\n    } catch (error) {\n      console.error('退出全屏失败:', error);\n    }\n\n    // 移除键盘快捷键监听\n    window.removeEventListener('keydown', this.handleKeyboardShortcuts);\n  },\n  methods: {\n    fetchRoomData: function fetchRoomData() {\n      var _this2 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var _this2$$store$getters, currentRoom, roomId, room, _t2, _t3;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              _context2.p = 0;\n              currentRoom = _this2.$store.getters.currentRoomData; // 确保房间ID是有效的数字\n              roomId = _this2.internalRoomId;\n              console.log(\"\\u5C1D\\u8BD5\\u83B7\\u53D6\\u623F\\u95F4\\u6570\\u636E\\uFF0C\\u623F\\u95F4ID: \".concat(roomId, \", \\u5F53\\u524D\\u623F\\u95F4:\"), currentRoom);\n\n              // 如果没有当前房间数据，或者当前房间ID与请求的不一致，则获取新的房间数据\n              if (!(!currentRoom || parseInt(currentRoom.id) !== parseInt(roomId))) {\n                _context2.n = 5;\n                break;\n              }\n              _context2.p = 1;\n              console.log(\"\\u5F53\\u524D\\u623F\\u95F4\\u6570\\u636E\\u4E0D\\u5339\\u914D\\uFF0C\\u4ECE\\u670D\\u52A1\\u5668\\u83B7\\u53D6\\u623F\\u95F4ID: \".concat(roomId));\n\n              // 使用store的joinRoom方法，它已经包含了错误处理逻辑\n              _context2.n = 2;\n              return _this2.$store.dispatch('joinRoom', {\n                roomId: roomId\n              });\n            case 2:\n              room = _context2.v;\n              // 确保房间ID是数字类型\n              room.id = parseInt(room.id);\n              _this2.room = room;\n              console.log('获取到的房间数据:', room);\n\n              // 如果房间ID与请求的ID不一致，重定向到正确的房间\n              if (room.id !== parseInt(roomId)) {\n                console.warn(\"\\u623F\\u95F4ID\\u4E0D\\u5339\\u914D\\uFF0C\\u8BF7\\u6C42ID: \".concat(roomId, \", \\u8FD4\\u56DEID: \").concat(room.id));\n                // 更新URL但不重新加载页面\n                window.history.replaceState(null, '', \"/room/\".concat(room.id));\n                _this2.internalRoomId = room.id;\n              }\n              _context2.n = 4;\n              break;\n            case 3:\n              _context2.p = 3;\n              _t2 = _context2.v;\n              console.error('加载房间失败', _t2);\n              throw _t2;\n            case 4:\n              _context2.n = 6;\n              break;\n            case 5:\n              console.log('使用当前房间数据:', currentRoom);\n              _this2.room = currentRoom;\n            case 6:\n              // 模拟在线用户数据\n              _this2.onlineUsers = [{\n                id: 1,\n                username: 'KP'\n              }, {\n                id: 2,\n                username: ((_this2$$store$getters = _this2.$store.getters.currentUser) === null || _this2$$store$getters === void 0 ? void 0 : _this2$$store$getters.username) || '玩家'\n              }];\n              _context2.n = 8;\n              break;\n            case 7:\n              _context2.p = 7;\n              _t3 = _context2.v;\n              console.error('fetchRoomData失败:', _t3);\n              throw _t3;\n            case 8:\n              return _context2.a(2);\n          }\n        }, _callee2, null, [[1, 3], [0, 7]]);\n      }))();\n    },\n    fetchUserCharacters: function fetchUserCharacters() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        var _t4;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              _context3.p = 0;\n              _context3.n = 1;\n              return _this3.$store.dispatch('fetchCharacters');\n            case 1:\n              _this3.characters = _this3.$store.getters.userCharacters;\n\n              // 如果有角色，默认选择第一个\n              if (_this3.characters.length > 0) {\n                _this3.selectedCharacter = _this3.characters[0];\n              }\n              _context3.n = 3;\n              break;\n            case 2:\n              _context3.p = 2;\n              _t4 = _context3.v;\n              console.error('加载角色失败', _t4);\n            case 3:\n              return _context3.a(2);\n          }\n        }, _callee3, null, [[0, 2]]);\n      }))();\n    },\n    fetchRoomMessages: function fetchRoomMessages() {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              // 这里简化处理，实际应从API获取历史消息\n              // 演示用的假消息\n              _this4.messages = [{\n                type: 'system',\n                content: '欢迎来到房间',\n                timestamp: Date.now() - 5000\n              }, {\n                type: 'chat',\n                user_id: 1,\n                username: 'KP',\n                content: '你们面前是一扇古老的门，上面刻着奇怪的符号...',\n                timestamp: Date.now() - 3000\n              }];\n            case 1:\n              return _context4.a(2);\n          }\n        }, _callee4);\n      }))();\n    },\n    connectWebSocket: function connectWebSocket() {\n      var _this5 = this;\n      try {\n        var _this$$store$getters$, _this$room;\n        // 获取当前用户ID\n        var userId = ((_this$$store$getters$ = this.$store.getters.currentUser) === null || _this$$store$getters$ === void 0 ? void 0 : _this$$store$getters$.id) || 'guest';\n\n        // 设置WebSocket事件监听器\n        websocketService.onConnect = function () {\n          var _this5$room, _this5$$store$getters;\n          console.log('WebSocket连接成功');\n          _this5.showSystemMessage('已成功连接到房间');\n\n          // 连接成功后，请求加入房间\n          // 优先使用this.room.id，因为这是已经确认的房间ID\n          var roomId = ((_this5$room = _this5.room) === null || _this5$room === void 0 ? void 0 : _this5$room.id) || _this5.internalRoomId;\n          console.log(\"WebSocket\\u8FDE\\u63A5\\u6210\\u529F\\uFF0C\\u52A0\\u5165\\u623F\\u95F4ID: \".concat(roomId));\n          websocketService.sendMessage({\n            type: 'join_room',\n            room_id: roomId,\n            user_id: userId,\n            username: ((_this5$$store$getters = _this5.$store.getters.currentUser) === null || _this5$$store$getters === void 0 ? void 0 : _this5$$store$getters.username) || '玩家'\n          });\n        };\n        websocketService.onDisconnect = function (reason) {\n          console.log('WebSocket连接断开:', reason);\n          _this5.showSystemMessage('与服务器的连接已断开，正在尝试重新连接...');\n        };\n        websocketService.onError = function (error) {\n          console.error('WebSocket错误:', error);\n          _this5.showSystemMessage('连接发生错误: ' + error.message);\n        };\n\n        // 设置自定义事件监听器\n        websocketService.setEventListeners({\n          'chat_message': this.handleChatMessage,\n          'user_joined': this.handleUserJoined,\n          'user_left': this.handleUserLeft,\n          'typing': this.handleTypingEvent,\n          'dice_roll': this.handleDiceRoll,\n          'skill_check': this.handleSkillCheck,\n          'character_update': this.handleCharacterUpdate,\n          'announcement_update': this.handleAnnouncementUpdate,\n          'room_name_update': this.handleRoomNameUpdate\n        });\n\n        // 确保房间ID有效\n        // 优先使用this.room.id，因为这是已经确认的房间ID\n        var roomId = ((_this$room = this.room) === null || _this$room === void 0 ? void 0 : _this$room.id) || this.internalRoomId;\n        console.log(\"\\u5C1D\\u8BD5\\u8FDE\\u63A5WebSocket\\uFF0C\\u623F\\u95F4ID: \".concat(roomId, \", \\u7528\\u6237ID: \").concat(userId));\n\n        // 连接WebSocket\n        websocketService.connect(roomId, userId);\n\n        // 添加离开页面前的提示\n        window.addEventListener('beforeunload', this.handleBeforeUnload);\n      } catch (error) {\n        console.error('连接WebSocket失败:', error);\n        this.showSystemMessage('连接服务器失败，请检查网络连接或刷新页面重试');\n\n        // 在开发环境中，提供更多调试信息\n        if (process.env.NODE_ENV === 'development') {\n          this.showSystemMessage('开发环境提示: 请确保WebSocket服务器已启动，端口为8084');\n\n          // 5秒后显示模拟连接成功的消息，方便开发调试\n          setTimeout(function () {\n            var _this5$$store$getters2;\n            _this5.showSystemMessage('开发环境模拟: 已成功连接到房间 (模拟)');\n\n            // 添加一些模拟的用户\n            _this5.onlineUsers = [{\n              id: 1,\n              username: 'KP'\n            }, {\n              id: 2,\n              username: ((_this5$$store$getters2 = _this5.$store.getters.currentUser) === null || _this5$$store$getters2 === void 0 ? void 0 : _this5$$store$getters2.username) || '玩家'\n            }, {\n              id: 3,\n              username: '模拟玩家1'\n            }, {\n              id: 4,\n              username: '模拟玩家2'\n            }];\n          }, 5000);\n        }\n      }\n    },\n    disconnectWebSocket: function disconnectWebSocket() {\n      websocketService.disconnect();\n      this.$store.dispatch('leaveRoom');\n    },\n    handleRoll: function handleRoll(rollData) {\n      var _this$$store$getters$2;\n      // 添加用户名\n      rollData.username = ((_this$$store$getters$2 = this.$store.getters.currentUser) === null || _this$$store$getters$2 === void 0 ? void 0 : _this$$store$getters$2.username) || '玩家';\n\n      // 如果没有结果，则在本地生成骰子结果\n      if (!rollData.results || !rollData.total) {\n        // 生成骰子结果\n        var count = parseInt(rollData.count) || 1;\n        var faces = parseInt(rollData.faces) || 100;\n        var modifier = parseInt(rollData.modifier) || 0;\n        var results = [];\n        for (var i = 0; i < count; i++) {\n          results.push(Math.floor(Math.random() * faces) + 1);\n        }\n        var total = results.reduce(function (sum, val) {\n          return sum + val;\n        }, 0) + modifier;\n        rollData.results = results;\n        rollData.total = total;\n      }\n\n      // 将骰子结果添加到本地历史记录\n      this.diceHistory.unshift({\n        description: \"\".concat(rollData.count, \"D\").concat(rollData.faces).concat(rollData.modifier > 0 ? '+' + rollData.modifier : ''),\n        total: rollData.total,\n        results: rollData.results\n      });\n\n      // 限制历史记录长度\n      if (this.diceHistory.length > 10) {\n        this.diceHistory.pop();\n      }\n\n      // 创建骰子结果消息\n      var diceMessage = {\n        type: 'system',\n        content: \"\".concat(rollData.username, \" \\u6295\\u63B7 \").concat(rollData.count, \"D\").concat(rollData.faces).concat(rollData.modifier > 0 ? '+' + rollData.modifier : '', \" = \").concat(rollData.total, \" [\").concat(rollData.results.join(', ')).concat(rollData.modifier > 0 ? ' + ' + rollData.modifier : '', \"]\"),\n        timestamp: new Date().toISOString(),\n        roll_data: {\n          count: rollData.count,\n          faces: rollData.faces,\n          modifier: rollData.modifier,\n          results: rollData.results,\n          total: rollData.total\n        }\n      };\n\n      // 将结果添加到消息列表\n      this.messages.push(diceMessage);\n\n      // 使用websocketService发送消息\n      websocketService.sendMessage(rollData);\n      if (this.playSounds) {\n        this.playDiceSound();\n      }\n    },\n    handleSkillCheck: function handleSkillCheck(skillData) {\n      // 添加必要的信息\n      skillData.type = 'skill-check';\n      if (!skillData.username) {\n        var _this$$store$getters$3;\n        skillData.username = ((_this$$store$getters$3 = this.$store.getters.currentUser) === null || _this$$store$getters$3 === void 0 ? void 0 : _this$$store$getters$3.username) || '玩家';\n      }\n\n      // 如果有角色信息，添加角色相关数据\n      if (!skillData.character_name && this.selectedCharacter) {\n        skillData.character_name = this.selectedCharacter.name;\n        skillData.character_id = this.selectedCharacter.id;\n      }\n      console.log('Room组件处理技能检定:', skillData);\n\n      // 使用websocketService发送消息\n      websocketService.sendMessage(skillData);\n\n      // 播放骰子声音\n      if (this.playSounds) {\n        this.playDiceSound();\n      }\n\n      // 在本地执行技能检定并显示结果\n      this.performLocalSkillCheck(skillData);\n    },\n    // 在本地执行技能检定并显示结果\n    performLocalSkillCheck: function performLocalSkillCheck(skillData) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var _this6$$store$getters, diceResult, skillValue, success, level, skillName, characterName, resultMessage;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              try {\n                // 生成1-100的随机数\n                diceResult = Math.floor(Math.random() * 100) + 1;\n                skillValue = skillData.skill_value; // 判定成功等级\n                success = false;\n                level = \"失败\";\n                if (diceResult <= skillValue) {\n                  success = true;\n                  if (diceResult === 1) {\n                    level = \"大成功\";\n                  } else if (diceResult <= skillValue / 5) {\n                    level = \"极难成功\";\n                  } else if (diceResult <= skillValue / 2) {\n                    level = \"困难成功\";\n                  } else {\n                    level = \"成功\";\n                  }\n                } else {\n                  if (diceResult >= 96 && skillValue < 50) {\n                    level = \"大失败\";\n                  } else if (diceResult === 100) {\n                    level = \"大失败\";\n                  } else {\n                    level = \"失败\";\n                  }\n                }\n\n                // 创建技能检定结果消息\n                skillName = skillData.skill_name || \"技能\";\n                characterName = skillData.character_name || ((_this6$$store$getters = _this6.$store.getters.currentUser) === null || _this6$$store$getters === void 0 ? void 0 : _this6$$store$getters.username) || '玩家';\n                resultMessage = {\n                  type: 'system',\n                  content: \"\".concat(characterName, \" \\u8FDB\\u884C \").concat(skillName, \" \\u68C0\\u5B9A\\uFF1A\\u63B7\\u51FA\\u4E86 \").concat(diceResult, \"/\").concat(skillValue, \"\\uFF0C\").concat(level, \"\\uFF01\"),\n                  timestamp: new Date().toISOString(),\n                  roll_result: {\n                    dice: diceResult,\n                    skill: skillValue,\n                    success: success,\n                    level: level\n                  }\n                }; // 将结果添加到消息列表\n                _this6.messages.push(resultMessage);\n              } catch (error) {\n                console.error('本地技能检定失败:', error);\n                _this6.showSystemMessage('技能检定处理失败');\n              }\n            case 1:\n              return _context5.a(2);\n          }\n        }, _callee5);\n      }))();\n    },\n    sendMessage: function sendMessage(message) {\n      var _this7 = this;\n      var messageData;\n      console.log('Room组件收到消息:', message);\n\n      // 检查message是否为字符串\n      if (typeof message === 'string') {\n        if (!message.trim()) return;\n        messageData = {\n          type: 'chat',\n          content: message,\n          timestamp: new Date().toISOString(),\n          character_id: this.selectedCharacter ? this.selectedCharacter.id : null,\n          ai_mode: this.currentAIMode\n        };\n      } else {\n        // message已经是一个对象\n        messageData = message;\n\n        // 确保有时间戳\n        if (!messageData.timestamp) {\n          messageData.timestamp = new Date().toISOString();\n        }\n\n        // 添加AI模式信息\n        if (!messageData.ai_mode) {\n          messageData.ai_mode = this.currentAIMode;\n        }\n      }\n      console.log('处理后的消息数据:', messageData);\n\n      // 确保消息有用户名\n      if (messageData.type === 'chat' && !messageData.username) {\n        var _this$$store$getters$4, _this$$store$getters$5;\n        messageData.username = ((_this$$store$getters$4 = this.$store.getters.currentUser) === null || _this$$store$getters$4 === void 0 ? void 0 : _this$$store$getters$4.username) || '玩家';\n        messageData.user_id = ((_this$$store$getters$5 = this.$store.getters.currentUser) === null || _this$$store$getters$5 === void 0 ? void 0 : _this$$store$getters$5.id) || 2; // 确保用户ID不是1 (KP)\n      }\n\n      // 过滤心跳消息，不显示在UI中\n      if (messageData.type === 'heartbeat') {\n        console.log('收到心跳消息，不添加到UI:', messageData);\n        websocketService.sendMessage(messageData);\n        return;\n      }\n\n      // 先将消息添加到UI，确保即使WebSocket发送失败也能看到消息\n      if (messageData.type === 'chat' || messageData.type === 'system') {\n        console.log('直接将消息添加到UI:', messageData);\n        this.messages.push(_objectSpread({}, messageData));\n\n        // 检查是非AI生成的玩家消息，并且AI KP处于活动状态，则处理该消息\n        if (messageData.type === 'chat' && !messageData.ai_generated && this.aiStatus.active) {\n          console.log('检测到普通玩家消息，但AI KP功能已移至ChatBox中');\n        }\n      }\n\n      // 检查WebSocket连接状态\n      if (!websocketService.isConnected || !websocketService.socket) {\n        console.log('WebSocket未连接，尝试重新连接');\n        this.showSystemMessage('连接已断开，正在尝试重新连接...');\n        this.connectWebSocket();\n\n        // 将消息推入队列等待重连\n        setTimeout(function () {\n          var result = websocketService.sendMessage(messageData);\n          console.log('消息发送结果:', result);\n        }, 1000);\n        return;\n      }\n      console.log('发送消息到WebSocket:', messageData);\n\n      // 直接发送消息，不依赖WebSocket的readyState属性\n      var sent = websocketService.sendMessage(messageData);\n      console.log('消息发送结果:', sent);\n\n      // 如果消息发送成功，清空输入框\n      if (typeof message === 'string' && sent) {\n        this.messageInput = '';\n      }\n\n      // 检查是否有待处理的场景描述且玩家回复开始游戏\n      if (this.pendingSceneDescription && messageData.type === 'chat' && messageData.content && typeof messageData.content === 'string' && !messageData.ai_generated) {\n        // 检查玩家消息是否为开始游戏的确认\n        var startGameKeywords = ['开始游戏', '开始', '准备好了', '开始冒险', '我准备好了', '出发', '是的'];\n        var playerMessage = messageData.content.toLowerCase();\n        if (startGameKeywords.some(function (keyword) {\n          return playerMessage.includes(keyword);\n        })) {\n          console.log('检测到玩家确认开始游戏');\n\n          // 发送玩家的确认消息\n          websocketService.sendMessage(messageData);\n\n          // 稍后发送场景描述\n          setTimeout(function () {\n            console.log('发送延迟的场景描述');\n            var sceneMessage = {\n              type: \"chat\",\n              user_id: 1,\n              // 使用KP的用户ID\n              username: \"KP\",\n              content: _this7.pendingSceneDescription,\n              ai_generated: true,\n              timestamp: Date.now(),\n              ai_mode: _this7.currentAIMode\n            };\n\n            // 保存一个标记，表示这是一个重要的AI消息\n            _this7.$store.commit('SET_IMPORTANT_MESSAGE', sceneMessage);\n\n            // 直接添加到UI\n            _this7.messages.push(_objectSpread({}, sceneMessage));\n\n            // 发送消息\n            websocketService.sendMessage(sceneMessage);\n            _this7.pendingSceneDescription = null; // 清除待处理的场景描述\n          }, 1000);\n        }\n      }\n    },\n    selectCharacter: function selectCharacter(character) {\n      var _this$$store$getters$6;\n      this.selectedCharacter = character;\n      this.showCharacterModal = false;\n\n      // 发送系统消息通知角色选择\n      var messageData = {\n        type: 'system',\n        content: \"\".concat(((_this$$store$getters$6 = this.$store.getters.currentUser) === null || _this$$store$getters$6 === void 0 ? void 0 : _this$$store$getters$6.username) || '玩家', \" \\u9009\\u62E9\\u4E86\\u89D2\\u8272: \").concat(character.name),\n        timestamp: Date.now()\n      };\n      this.sendMessage(messageData);\n\n      // 同步选中角色的状态\n      this.syncSelectedCharacterStatus();\n      this.updateCurrentCharacterId(character.id);\n    },\n    // 新增功能\n    playDiceSound: function playDiceSound() {\n      // 播放骰子声音\n      var audio = new Audio('/sounds/');\n      audio.volume = 0.5;\n      audio.play()[\"catch\"](function (err) {\n        return console.warn('无法播放音效:', err);\n      });\n    },\n    sendTypingStatus: function sendTypingStatus(isTyping) {\n      // 防止过于频繁发送状态更新\n      var now = Date.now();\n      if (now - this.lastTypingTime < 2000 && isTyping) {\n        return;\n      }\n\n      // 发送正在输入状态\n      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n        var _this$$store$getters$7;\n        websocketService.sendMessage({\n          type: 'typing',\n          username: ((_this$$store$getters$7 = this.$store.getters.currentUser) === null || _this$$store$getters$7 === void 0 ? void 0 : _this$$store$getters$7.username) || '玩家',\n          isTyping: isTyping\n        });\n        this.lastTypingTime = now;\n      }\n    },\n    toggleDarkMode: function toggleDarkMode() {\n      this.$store.dispatch('toggleTheme');\n    },\n    changeFontSize: function changeFontSize(change) {\n      this.fontSize = Math.max(12, Math.min(24, this.fontSize + change));\n      document.documentElement.style.setProperty('--base-font-size', \"\".concat(this.fontSize, \"px\"));\n      this.safeSetItem('fontSize', this.fontSize);\n    },\n    saveNotes: function saveNotes(notes) {\n      if (notes !== undefined) {\n        this.gameNotes = notes;\n      }\n      this.safeSetItem(\"notes_\".concat(this.internalRoomId), this.gameNotes);\n    },\n    saveSettings: function saveSettings() {\n      this.safeSetItem('fontSize', this.fontSize);\n      this.safeSetItem('playSounds', this.playSounds);\n      this.safeSetItem('showNotifications', this.showNotifications);\n      this.showSettingsModal = false;\n    },\n    loadUserSettings: function loadUserSettings() {\n      // 加载AI模式设置\n      this.currentAIMode = this.safeGetItem('ai_mode') || 'normal';\n\n      // 加载之前保存的笔记\n      this.gameNotes = this.safeGetItem(\"notes_\".concat(this.internalRoomId)) || '';\n\n      // 加载其他设置\n      var savedFontSize = this.safeGetItem('fontSize');\n      if (savedFontSize) {\n        this.fontSize = parseInt(savedFontSize);\n        document.documentElement.style.setProperty('--base-font-size', \"\".concat(this.fontSize, \"px\"));\n      }\n      var playSounds = this.safeGetItem('playSounds');\n      if (playSounds) {\n        this.playSounds = playSounds === 'true';\n      }\n      var showNotifications = this.safeGetItem('showNotifications');\n      if (showNotifications) {\n        this.showNotifications = showNotifications === 'true';\n      }\n\n      // 加载面板可见性设置\n      var panelSettings = this.safeGetItem(\"panels_\".concat(this.internalRoomId));\n      if (panelSettings) {\n        try {\n          this.visiblePanels = JSON.parse(panelSettings);\n          console.log('已加载面板设置:', this.visiblePanels);\n        } catch (e) {\n          console.error('解析面板设置失败', e);\n        }\n      } else {\n        // 如果没有保存的设置，设置默认值\n        this.visiblePanels.character = true;\n        this.visiblePanels.dice = true;\n        this.visiblePanels.privateChat = false; // 默认不显示私聊面板\n        console.log('使用默认面板设置');\n      }\n\n      // 单独检查私聊面板设置\n      var privateChatSetting = this.safeGetItem(\"panel_privateChat_\".concat(this.internalRoomId));\n      if (privateChatSetting) {\n        this.visiblePanels.privateChat = privateChatSetting === 'visible';\n        console.log('已加载私聊面板设置:', this.visiblePanels.privateChat ? '显示' : '隐藏');\n      }\n\n      // 加载AI状态设置\n      var aiStatusSettings = this.safeGetItem(\"ai_status_\".concat(this.internalRoomId));\n      if (aiStatusSettings) {\n        try {\n          this.aiStatus = JSON.parse(aiStatusSettings);\n        } catch (e) {\n          console.error('解析AI状态设置失败', e);\n        }\n      }\n\n      // 确保状态与面板可见性一致\n      this.aiStatus.active = this.visiblePanels.ai;\n\n      // 同步面板状态到其他变量\n      this.showCharacterSheet = this.visiblePanels.character;\n      this.showDiceRoller = this.visiblePanels.dice;\n    },\n    sendDesktopNotification: function sendDesktopNotification(message) {\n      // 检查浏览器是否支持通知\n      if (\"Notification\" in window) {\n        if (Notification.permission === \"granted\") {\n          new Notification(\"\".concat(message.username || '用户', \" \\u5728 \").concat(this.room.name), {\n            body: message.content,\n            icon: '/favicon.ico'\n          });\n        } else if (Notification.permission !== \"denied\") {\n          Notification.requestPermission();\n        }\n      }\n    },\n    // 通用面板切换方法\n    togglePanel: function togglePanel(panelName) {\n      var forceClose = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      if (forceClose) {\n        this.visiblePanels[panelName] = false;\n      } else {\n        this.visiblePanels[panelName] = !this.visiblePanels[panelName];\n      }\n\n      // 保存面板状态到本地存储\n      try {\n        // 保存单独的面板状态\n        this.safeSetItem(\"panel_\".concat(panelName, \"_\").concat(this.internalRoomId), this.visiblePanels[panelName] ? 'visible' : 'hidden');\n\n        // 同时保存整个面板状态对象\n        this.safeSetJSON(\"panels_\".concat(this.internalRoomId), this.visiblePanels);\n\n        // 同步面板状态到显示变量\n        if (panelName === 'character') {\n          this.showCharacterSheet = this.visiblePanels.character;\n        } else if (panelName === 'dice') {\n          this.showDiceRoller = this.visiblePanels.dice;\n        }\n      } catch (e) {\n        console.warn('无法保存面板状态到本地存储:', e);\n      }\n      console.log(\"\\u9762\\u677F \".concat(panelName, \" \\u72B6\\u6001: \").concat(this.visiblePanels[panelName] ? '显示' : '隐藏'));\n    },\n    handleTyping: function handleTyping() {\n      // 发送正在输入状态\n      this.sendTypingStatus(true);\n    },\n    handleTypingStopped: function handleTypingStopped() {\n      // 发送停止输入状态\n      this.sendTypingStatus(false);\n    },\n    // 添加显示系统消息的方法\n    showSystemMessage: function showSystemMessage(content) {\n      this.$store.dispatch('addMessage', {\n        type: 'system',\n        content: content,\n        timestamp: new Date().toISOString()\n      });\n    },\n    handleSanityUpdate: function handleSanityUpdate(data) {\n      // 更新角色的理智值\n      if (this.selectedCharacter && this.selectedCharacter.id === data.characterId) {\n        // 创建一个新对象，避免直接修改props\n        this.selectedCharacter = _objectSpread(_objectSpread({}, this.selectedCharacter), {}, {\n          sanity: data.newSanity\n        });\n\n        // 发送系统消息通知理智值变化\n        var messageData = {\n          type: 'system',\n          content: \"\".concat(this.selectedCharacter.name, \" \\u7684\\u7406\\u667A\\u503C\\u53D8\\u4E3A \").concat(data.newSanity),\n          timestamp: Date.now()\n        };\n        this.sendMessage(messageData);\n      }\n    },\n    // 检查当前用户是否是房主\n    checkRoomOwnership: function checkRoomOwnership() {\n      try {\n        var currentUser = this.$store.getters.currentUser;\n        if (!currentUser) {\n          console.log('未登录用户，默认不是房主');\n          this.isRoomOwner = false;\n          return;\n        }\n        if (!this.room) {\n          console.log('房间数据不存在，无法检查房主状态');\n          this.isRoomOwner = false;\n          return;\n        }\n\n        // 如果是模拟房间，默认当前用户是房主\n        if (this.room.is_mock) {\n          console.log('模拟房间，当前用户设为房主');\n          this.isRoomOwner = true;\n          return;\n        }\n\n        // 检查当前用户是否是房间创建者\n        this.isRoomOwner = this.room.keeper_id === currentUser.id;\n        console.log('房主检查结果:', this.isRoomOwner ? '是房主' : '不是房主');\n      } catch (error) {\n        console.error('检查房主状态失败:', error);\n        this.isRoomOwner = false;\n      }\n    },\n    // 检查系统组件状态\n    checkSystemStatus: function checkSystemStatus() {\n      console.log('检查系统组件状态...');\n\n      // 检查WebSocket连接\n      var wsStatus = websocketService.isConnected ? '已连接' : '未连接';\n      console.log(\"WebSocket\\u72B6\\u6001: \".concat(wsStatus));\n\n      // 检查服务锁状态\n      console.log('服务锁状态:', websocketService.resourceLocks);\n\n      // 检查消息队列\n      console.log(\"\\u6D88\\u606F\\u961F\\u5217\\u957F\\u5EA6: \".concat(websocketService.messageQueue.length));\n\n      // 检查AI组件状态\n      console.log('AI组件状态:', this.aiStatus);\n\n      // 检查可见面板状态\n      console.log('可见面板:', this.visiblePanels);\n      if (!websocketService.isConnected) {\n        console.log('WebSocket未连接，尝试重连');\n        this.showSystemMessage('正在重新建立连接...');\n        this.connectWebSocket();\n      }\n\n      // 确保聊天输入框可用\n      this.ensureChatInputEnabled();\n\n      // 确认组件正常工作，主动发送一条系统消息\n      this.showSystemMessage('系统检查完成，您可以正常发送消息');\n    },\n    // 确保聊天输入框可用\n    ensureChatInputEnabled: function ensureChatInputEnabled() {\n      var _this8 = this;\n      console.log('确保聊天输入框可用');\n      setTimeout(function () {\n        // 查找所有可能的输入框并确保它们可用\n        var textareas = document.querySelectorAll('textarea');\n        textareas.forEach(function (textarea) {\n          textarea.disabled = false;\n          console.log('已启用输入框:', textarea);\n        });\n\n        // 如果有ChatBox引用，尝试直接操作\n        if (_this8.$refs.chatBox && _this8.$refs.chatBox.$refs.messageTextarea) {\n          _this8.$refs.chatBox.$refs.messageTextarea.disabled = false;\n          console.log('已直接启用ChatBox输入框');\n        }\n      }, 500);\n    },\n    // 处理AI设置更新\n    handleAISettingsUpdated: function handleAISettingsUpdated(settings) {\n      console.log('AI设置已更新:', settings);\n      this.showSystemMessage('AI模型设置已更新');\n\n      // 从本地存储加载AI设置并应用\n      try {\n        var parsedSettings = this.safeGetJSON(\"aiSettings_\".concat(this.roomId));\n        if (parsedSettings) {\n          console.log('应用本地AI设置:', parsedSettings);\n\n          // 如果有AI模式设置，更新当前模式\n          if (parsedSettings.ai_mode) {\n            this.updateAIMode(parsedSettings.ai_mode);\n          }\n        }\n      } catch (error) {\n        console.error('加载本地AI设置失败:', error);\n      }\n    },\n    // 更新AI模式\n    updateAIMode: function updateAIMode(mode) {\n      var _this9 = this;\n      if (this.currentAIMode !== mode) {\n        console.log(\"AI\\u6A21\\u5F0F\\u4ECE \".concat(this.currentAIMode, \" \\u5207\\u6362\\u5230 \").concat(mode));\n        this.currentAIMode = mode;\n\n        // 保存到本地存储\n        this.safeSetItem('ai_mode', mode);\n\n        // 通知WebSocket服务\n        websocketService.setAIMode(mode);\n\n        // 显示系统消息\n        this.showSystemMessage(\"AI\\u6A21\\u5F0F\\u5DF2\\u5207\\u6362\\u5230: \".concat(mode === 'normal' ? '普通模式' : '剧本模式'));\n\n        // 如果切换到剧本模式，自动打开群聊面板\n        if (mode === 'script' && !this.visiblePanels.groupChat) {\n          setTimeout(function () {\n            _this9.togglePanel('groupChat', false);\n          }, 500);\n        }\n      }\n    },\n    // 跳转到AI设置\n    goToAISettings: function goToAISettings() {\n      var _this0 = this;\n      this.togglePanel('settings');\n      setTimeout(function () {\n        // 聚焦到AI设置选项卡，如果有多个选项卡的话\n        _this0.$refs.aiSettings && _this0.$refs.aiSettings.focus();\n      }, 300);\n    },\n    // 关闭配置提醒\n    dismissConfigAlert: function dismissConfigAlert() {\n      this.showConfigAlert = false;\n      // 可以保存到本地存储，避免重复显示\n      this.safeSetItem('aiConfigAlertDismissed', 'true');\n    },\n    // 检查API密钥状态\n    checkApiKeyStatus: function checkApiKeyStatus() {\n      var _this1 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n        var response, settings, apiTested, _t5;\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              _context6.p = 0;\n              _context6.n = 1;\n              return apiService.aiSettings.getSettings(_this1.roomId);\n            case 1:\n              response = _context6.v;\n              if (response.data && response.data.success) {\n                settings = response.data.settings; // 将设置保存到store\n                _this1.$store.commit('UPDATE_AI_SETTINGS', {\n                  apiKey: settings.api_key,\n                  apiUrl: settings.api_url,\n                  modelName: settings.model_name\n                });\n\n                // 如果之前通过测试，则标记为有效\n                apiTested = _this1.safeGetItem(\"apiTested_\".concat(_this1.roomId));\n                if (apiTested === 'true') {\n                  _this1.$store.commit('SET_API_VALID', true);\n                  _this1.showConfigAlert = false;\n                } else {\n                  _this1.$store.commit('SET_API_VALID', false);\n                  _this1.showConfigAlert = true;\n                }\n              }\n              _context6.n = 3;\n              break;\n            case 2:\n              _context6.p = 2;\n              _t5 = _context6.v;\n              console.error('检查API密钥状态失败:', _t5);\n            case 3:\n              return _context6.a(2);\n          }\n        }, _callee6, null, [[0, 2]]);\n      }))();\n    },\n    // 同步选中角色的状态\n    syncSelectedCharacterStatus: function syncSelectedCharacterStatus() {\n      var _this10 = this;\n      if (!this.selectedCharacter || !this.selectedCharacter.id) return;\n\n      // 请求从服务器同步角色状态\n      this.$store.dispatch('syncCharacterData', this.selectedCharacter.id).then(function () {\n        console.log('角色状态同步完成:', _this10.selectedCharacter.name);\n        _this10.showSystemMessage(\"\\u89D2\\u8272 \".concat(_this10.selectedCharacter.name, \" \\u6570\\u636E\\u5DF2\\u540C\\u6B65\"));\n      })[\"catch\"](function (error) {\n        console.error('角色状态同步失败:', error);\n      });\n    },\n    // 处理角色同步事件\n    handleCharacterSync: function handleCharacterSync(characterId) {\n      console.log(\"\\u6536\\u5230\\u89D2\\u8272\\u540C\\u6B65\\u4E8B\\u4EF6: \".concat(characterId));\n\n      // 更新角色显示\n      if (this.selectedCharacter && this.selectedCharacter.id === characterId) {\n        // 从store获取最新的角色数据\n        var updatedCharacter = this.$store.getters.userCharacters.find(function (c) {\n          return c.id === characterId;\n        });\n        if (updatedCharacter) {\n          this.selectedCharacter = updatedCharacter;\n          console.log('角色数据已更新:', updatedCharacter.name);\n        }\n      }\n    },\n    // 设置定时同步角色状态\n    setupCharacterSyncTimer: function setupCharacterSyncTimer() {\n      var _this11 = this;\n      // 每隔5分钟同步一次角色状态\n      this.characterSyncTimer = setInterval(function () {\n        if (_this11.selectedCharacter && _this11.selectedCharacter.id) {\n          console.log('定时同步角色状态:', _this11.selectedCharacter.name);\n          _this11.syncSelectedCharacterStatus();\n        }\n      }, 5 * 60 * 1000); // 5分钟\n    },\n    // 移除场景相关方法\n    handleSceneSave: function handleSceneSave(data) {\n      // 保存场景数据\n      this.scenes[data.sceneIndex] = data.scene;\n\n      // 发送场景更新消息\n      this.sendSystemMessage(\"\\u573A\\u666F \\\"\".concat(data.scene.name, \"\\\" \\u5DF2\\u66F4\\u65B0\"));\n\n      // 持久化场景数据\n      this.saveScenesToServer();\n    },\n    saveScenesToServer: function saveScenesToServer() {\n      // 这里可以添加将场景数据保存到服务器的逻辑\n      // 例如通过WebSocket发送或API调用\n      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n        this.websocket.send(JSON.stringify({\n          type: 'save-scenes',\n          scenes: this.scenes\n        }));\n      }\n    },\n    loadScenesFromServer: function loadScenesFromServer() {\n      // 从服务器加载场景数据\n      // 这里可以添加API调用或从WebSocket接收场景数据的逻辑\n    },\n    handleNoteSaved: function handleNoteSaved(note) {\n      // 可以在这里处理笔记保存事件\n      console.log('笔记已保存:', note);\n    },\n    updateCurrentCharacterId: function updateCurrentCharacterId(characterId) {\n      this.currentCharacterId = characterId;\n    },\n    // 切换骰子面板显示状态\n    toggleDiceRoller: function toggleDiceRoller() {\n      this.visiblePanels.dice = !this.visiblePanels.dice;\n      this.showDiceRoller = this.visiblePanels.dice;\n    },\n    // 切换角色卡面板显示状态\n    toggleCharacterSheet: function toggleCharacterSheet() {\n      this.visiblePanels.character = !this.visiblePanels.character;\n      this.showCharacterSheet = this.visiblePanels.character;\n    },\n    // 切换笔记面板显示状态\n    toggleNotes: function toggleNotes() {\n      this.showNotes = !this.showNotes;\n    },\n    // 切换地图面板显示状态\n    toggleMap: function toggleMap() {\n      this.visiblePanels.map = !this.visiblePanels.map;\n    },\n    // 切换线索墙面板显示状态\n    toggleClueBoard: function toggleClueBoard() {\n      this.togglePanel('clueBoard');\n    },\n    // 切换音频系统面板显示状态\n    toggleAudioSystem: function toggleAudioSystem() {\n      this.togglePanel('audioSystem');\n    },\n    // 切换语音聊天面板显示状态\n    toggleVoiceChat: function toggleVoiceChat() {\n      this.togglePanel('voiceChat');\n    },\n    // 切换私聊面板显示状态\n    togglePrivateChat: function togglePrivateChat() {\n      this.togglePanel('privateChat');\n      console.log('私聊面板状态:', this.visiblePanels.privateChat ? '显示' : '隐藏');\n    },\n    // 切换群聊面板显示状态\n    toggleGroupChat: function toggleGroupChat() {\n      this.togglePanel('groupChat');\n    },\n    // 处理语音聊天连接事件\n    handleVoiceConnected: function handleVoiceConnected() {\n      this.showSystemMessage('您已加入语音聊天');\n    },\n    handleVoiceDisconnected: function handleVoiceDisconnected() {\n      this.showSystemMessage('您已离开语音聊天');\n    },\n    // 添加骰子投掷处理方法\n    handleRollDice: function handleRollDice(diceData) {\n      var _this$currentUser;\n      if (!diceData) return;\n\n      // 创建骰子消息\n      var diceMessage = {\n        type: 'roll',\n        count: diceData.count || 1,\n        faces: diceData.faces || 100,\n        modifier: diceData.modifier || 0,\n        username: ((_this$currentUser = this.currentUser) === null || _this$currentUser === void 0 ? void 0 : _this$currentUser.username) || '玩家',\n        timestamp: new Date().toISOString()\n      };\n\n      // 发送到WebSocket\n      if (websocketService.isConnected) {\n        websocketService.sendMessage(diceMessage);\n      } else {\n        // 本地模拟骰子结果\n        var results = [];\n        var total = 0;\n        for (var i = 0; i < diceMessage.count; i++) {\n          var result = Math.floor(Math.random() * diceMessage.faces) + 1;\n          results.push(result);\n          total += result;\n        }\n        total += diceMessage.modifier;\n\n        // 显示本地骰子结果\n        this.showSystemMessage(\"\".concat(diceMessage.username, \" \\u6295\\u63B7 \").concat(diceMessage.count, \"D\").concat(diceMessage.faces).concat(diceMessage.modifier > 0 ? '+' + diceMessage.modifier : '', \" = \").concat(total, \" [\").concat(results.join(', ')).concat(diceMessage.modifier > 0 ? ' + ' + diceMessage.modifier : '', \"]\"));\n      }\n    },\n    // 添加脚本上传处理方法\n    handleUploadScript: function handleUploadScript(file) {\n      var _this12 = this;\n      if (!file) return;\n\n      // 显示上传中消息\n      this.showSystemMessage('正在上传剧本文件...');\n\n      // 模拟上传过程\n      setTimeout(function () {\n        _this12.showSystemMessage('剧本文件上传成功！');\n      }, 1500);\n\n      // 实际项目中，这里应该调用API上传文件\n      console.log('上传剧本文件:', file.name);\n    },\n    toggleToolbar: function toggleToolbar() {\n      this.isToolbarCollapsed = !this.isToolbarCollapsed;\n      console.log('工具栏状态切换:', this.isToolbarCollapsed ? '已折叠' : '已展开');\n\n      // 保存折叠状态到本地存储\n      this.safeSetItem(\"toolbar_collapsed_\".concat(this.roomId), this.isToolbarCollapsed);\n    },\n    checkMobileDevice: function checkMobileDevice() {\n      this.isMobile = window.innerWidth < 768;\n      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');\n    },\n    // WebSocket事件处理函数\n    handleChatMessage: function handleChatMessage(message) {\n      console.log('收到聊天消息:', message);\n      this.$store.dispatch('addMessage', message);\n    },\n    handleUserJoined: function handleUserJoined(data) {\n      console.log('用户加入房间:', data);\n\n      // 添加到在线用户列表\n      if (!this.onlineUsers.find(function (user) {\n        return user.id === data.user_id;\n      })) {\n        this.onlineUsers.push({\n          id: data.user_id,\n          username: data.username\n        });\n      }\n\n      // 显示系统消息\n      this.showSystemMessage(\"\".concat(data.username, \" \\u52A0\\u5165\\u4E86\\u623F\\u95F4\"));\n    },\n    handleUserLeft: function handleUserLeft(data) {\n      console.log('用户离开房间:', data);\n\n      // 从在线用户列表中移除\n      var index = this.onlineUsers.findIndex(function (user) {\n        return user.id === data.user_id;\n      });\n      if (index !== -1) {\n        this.onlineUsers.splice(index, 1);\n      }\n\n      // 显示系统消息\n      this.showSystemMessage(\"\".concat(data.username, \" \\u79BB\\u5F00\\u4E86\\u623F\\u95F4\"));\n    },\n    handleTypingEvent: function handleTypingEvent(data) {\n      var _this$$store$getters$8,\n        _this13 = this;\n      if (data.user_id !== ((_this$$store$getters$8 = this.$store.getters.currentUser) === null || _this$$store$getters$8 === void 0 ? void 0 : _this$$store$getters$8.id)) {\n        this.isTyping = data.typing;\n        this.typingUsername = data.username;\n\n        // 设置超时，如果长时间没有收到新的typing事件，则自动清除\n        clearTimeout(this.typingTimer);\n        if (this.isTyping) {\n          this.typingTimer = setTimeout(function () {\n            _this13.isTyping = false;\n          }, 5000);\n        }\n      }\n    },\n    handleDiceRoll: function handleDiceRoll(message) {\n      console.log('收到骰子消息:', message);\n\n      // 创建骰子结果消息\n      var diceMessage = {\n        type: 'system',\n        content: \"\".concat(message.username, \" \\u6295\\u63B7 \").concat(message.count, \"D\").concat(message.faces).concat(message.modifier > 0 ? '+' + message.modifier : '', \" = \").concat(message.total, \" [\").concat(message.results.join(', ')).concat(message.modifier > 0 ? ' + ' + message.modifier : '', \"]\"),\n        timestamp: message.timestamp || new Date().toISOString(),\n        roll_data: {\n          count: message.count,\n          faces: message.faces,\n          modifier: message.modifier,\n          results: message.results,\n          total: message.total\n        }\n      };\n\n      // 添加到消息列表\n      this.$store.dispatch('addMessage', diceMessage);\n\n      // 添加到骰子历史记录\n      this.diceHistory.push(_objectSpread(_objectSpread({}, message), {}, {\n        timestamp: new Date().toISOString()\n      }));\n\n      // 保持历史记录不超过20条\n      if (this.diceHistory.length > 20) {\n        this.diceHistory.shift();\n      }\n    },\n    // 这里已删除重复的handleSkillCheck和handleCharacterUpdate方法\n    // 检测设备类型\n    detectDevice: function detectDevice() {\n      this.isMobile = window.innerWidth < 768;\n      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');\n    },\n    // 处理页面关闭前的操作\n    handleBeforeUnload: function handleBeforeUnload(event) {\n      this.saveNotes();\n      // 提示用户确认离开\n      if (this.hasUnsavedChanges) {\n        event.preventDefault();\n        event.returnValue = '您有未保存的更改，确定要离开吗？';\n        return event.returnValue;\n      }\n    },\n    // 切换全屏模式\n    toggleFullScreen: function toggleFullScreen() {\n      var _this14 = this;\n      try {\n        // 使用.room-container作为全屏目标元素\n        var elem = document.querySelector('.room-container');\n        if (!elem) {\n          console.error('找不到.room-container元素');\n          this.showSystemMessage('全屏切换失败: 找不到目标元素');\n          return;\n        }\n        console.log('开始全屏操作，目标元素:', elem);\n\n        // 检查当前是否在全屏模式\n        var isFullScreen = document.fullscreenElement || document.webkitFullscreenElement || document.mozFullScreenElement || document.msFullscreenElement;\n        if (!isFullScreen) {\n          // 进入全屏模式\n          console.log('尝试进入全屏模式');\n\n          // 保存请求全屏前的状态\n          elem.setAttribute('data-was-fullscreen', 'true');\n\n          // 为目标元素添加全屏样式\n          elem.classList.add('preparing-fullscreen');\n\n          // 尝试所有可能的全屏API\n          var requestFullScreen = elem.requestFullscreen || elem.webkitRequestFullscreen || elem.mozRequestFullScreen || elem.msRequestFullscreen;\n          if (requestFullScreen) {\n            // 使用apply调用适当的方法\n            requestFullScreen.apply(elem).then(function () {\n              console.log('全屏请求成功');\n              elem.classList.add('in-fullscreen');\n              elem.classList.remove('preparing-fullscreen');\n              _this14.showSystemMessage('已进入全屏模式，再次双击顶部栏或按ESC可退出');\n            })[\"catch\"](function (err) {\n              console.error('全屏请求被拒绝:', err);\n              elem.classList.remove('preparing-fullscreen');\n              _this14.showSystemMessage('进入全屏失败: ' + err.message);\n            });\n          } else {\n            // 如果浏览器不支持全屏API，尝试使用CSS模拟全屏\n            console.warn('浏览器不支持全屏API，使用CSS模拟全屏');\n            elem.classList.add('in-fullscreen');\n            elem.classList.remove('preparing-fullscreen');\n            document.body.classList.add('fullscreen-mode');\n            this.showSystemMessage('已使用模拟全屏模式，再次双击顶部栏可退出');\n          }\n        } else {\n          // 退出全屏模式\n          console.log('尝试退出全屏模式');\n\n          // 尝试所有可能的退出全屏API\n          var exitFullScreen = document.exitFullscreen || document.webkitExitFullscreen || document.mozCancelFullScreen || document.msExitFullscreen;\n          if (exitFullScreen) {\n            exitFullScreen.apply(document).then(function () {\n              console.log('已退出全屏模式');\n              elem.classList.remove('in-fullscreen');\n              _this14.showSystemMessage('已退出全屏模式');\n            })[\"catch\"](function (err) {\n              console.error('退出全屏失败:', err);\n              _this14.showSystemMessage('退出全屏失败: ' + err.message);\n            });\n          } else {\n            // 如果使用CSS模拟全屏，则移除相关样式\n            elem.classList.remove('in-fullscreen');\n            document.body.classList.remove('fullscreen-mode');\n            this.showSystemMessage('已退出全屏模式');\n          }\n        }\n      } catch (error) {\n        console.error('全屏切换失败:', error);\n        this.showSystemMessage('全屏切换失败: ' + error.message);\n\n        // 移除可能残留的全屏状态\n        try {\n          var _elem = document.querySelector('.room-container');\n          if (_elem) {\n            _elem.classList.remove('preparing-fullscreen');\n            _elem.classList.remove('in-fullscreen');\n          }\n          document.body.classList.remove('fullscreen-mode');\n        } catch (e) {\n          console.error('清理全屏状态失败:', e);\n        }\n      }\n    },\n    // 处理全屏状态变化\n    handleFullScreenChange: function handleFullScreenChange() {\n      try {\n        var isFullScreen = !!document.fullscreenElement || !!document.webkitFullscreenElement || !!document.mozFullScreenElement || !!document.msFullscreenElement;\n        var container = document.querySelector('.room-container');\n        if (!container) return;\n        if (isFullScreen) {\n          console.log('已进入全屏模式');\n          // 确保应用全屏样式\n          document.body.classList.add('fullscreen-mode');\n          container.classList.add('in-fullscreen');\n\n          // 不显示全屏提示\n        } else {\n          console.log('已退出全屏模式');\n          // 移除全屏样式\n          document.body.classList.remove('fullscreen-mode');\n          container.classList.remove('in-fullscreen');\n\n          // 移除可能存在的全屏提示\n          var notice = container.querySelector('.fullscreen-notice');\n          if (notice) {\n            notice.parentNode.removeChild(notice);\n          }\n        }\n      } catch (error) {\n        console.error('处理全屏状态变化时出错:', error);\n      }\n    },\n    // 处理剧本模式设置相关方法\n    openChatBoxSettings: function openChatBoxSettings() {\n      // 直接调用ChatBox组件的方法（通过引用）\n      if (this.$refs.chatBox) {\n        this.$refs.chatBox.showAISettings = true;\n      } else {\n        console.warn('找不到ChatBox组件引用');\n      }\n    },\n    handleMemoryCleared: function handleMemoryCleared() {\n      this.showSystemMessage('AI记忆已清除，将重新开始对话');\n    },\n    handleScriptSettingsSaved: function handleScriptSettingsSaved(settings) {\n      this.showSystemMessage('剧本模式设置已保存');\n\n      // 如果有API密钥，更新到全局设置\n      if (settings.apiKey) {\n        this.$store.commit('UPDATE_AI_SETTINGS', {\n          apiKey: settings.apiKey\n        });\n      }\n    },\n    // 打开剧本查看器\n    openScenarioViewer: function openScenarioViewer(content, title, fileSize) {\n      this.scenarioContent = content || '';\n      this.scenarioTitle = title || '未命名剧本';\n      this.scenarioFileSize = fileSize || '';\n      this.togglePanel('scenarioViewer');\n    },\n    handleScriptUploaded: function handleScriptUploaded(fileInfo) {\n      this.showSystemMessage(\"\\u5267\\u672C\\u6587\\u4EF6 \".concat(fileInfo.fileName, \" \\u5DF2\\u4E0A\\u4F20\\u6210\\u529F\"));\n    },\n    // 添加游戏存档相关方法\n    toggleGameSaves: function toggleGameSaves() {\n      this.togglePanel('gameSaves');\n    },\n    // 处理存档创建成功\n    handleSaveCreated: function handleSaveCreated(save) {\n      this.showSystemMessage(\"\\u5B58\\u6863 \\\"\".concat(save.name, \"\\\" \\u521B\\u5EFA\\u6210\\u529F\"));\n    },\n    // 处理存档更新成功\n    handleSaveUpdated: function handleSaveUpdated(save) {\n      this.showSystemMessage(\"\\u5B58\\u6863 \\\"\".concat(save.name, \"\\\" \\u66F4\\u65B0\\u6210\\u529F\"));\n    },\n    // 处理存档删除成功\n    handleSaveDeleted: function handleSaveDeleted() {\n      this.showSystemMessage('存档已成功删除');\n    },\n    // 处理自动存档通知\n    handleSaveAutoCreated: function handleSaveAutoCreated(save) {\n      this.showSystemMessage(\"\\u81EA\\u52A8\\u5B58\\u6863\\u5DF2\\u5B8C\\u6210\", 'info', 3000);\n    },\n    // 处理存档消息\n    handleSaveMessage: function handleSaveMessage(message) {\n      this.showSystemMessage(message);\n    },\n    // 处理存档版本恢复\n    handleSaveRestored: function handleSaveRestored(save) {\n      this.showSystemMessage(\"\\u5B58\\u6863\\u5DF2\\u6062\\u590D\\u5230\\u7248\\u672C \".concat(save.version));\n      // 重新加载存档内容\n      this.handleSaveLoad(save);\n    },\n    // 处理存档预览\n    handleSavePreview: function handleSavePreview(saveData) {\n      // 这里可以实现预览功能，比如临时显示存档内容\n      this.showSystemMessage('正在预览存档历史版本，加载完成后点击\"恢复\"可应用此版本');\n\n      // 临时存储当前状态\n      this.tempState = {\n        messages: _toConsumableArray(this.messages),\n        characters: _toConsumableArray(this.characters),\n        scenes: _toConsumableArray(this.scenes),\n        currentSceneIndex: this.currentSceneIndex,\n        diceHistory: _toConsumableArray(this.diceHistory)\n      };\n\n      // 临时加载预览内容\n      this.messages = saveData.save_data.messages || [];\n      this.characters = saveData.save_data.characters || [];\n      this.scenes = saveData.save_data.scenes || [];\n      this.currentSceneIndex = saveData.save_data.currentScene || 0;\n      this.diceHistory = saveData.save_data.diceHistory || [];\n\n      // 设置预览模式\n      this.isPreviewMode = true;\n    },\n    // 退出预览模式\n    exitPreviewMode: function exitPreviewMode() {\n      if (!this.isPreviewMode || !this.tempState) return;\n\n      // 恢复临时存储的状态\n      this.messages = this.tempState.messages;\n      this.characters = this.tempState.characters;\n      this.scenes = this.tempState.scenes;\n      this.currentSceneIndex = this.tempState.currentSceneIndex;\n      this.diceHistory = this.tempState.diceHistory;\n\n      // 重置预览模式\n      this.isPreviewMode = false;\n      this.tempState = null;\n      this.showSystemMessage('已退出预览模式');\n    },\n    // 处理存档加载\n    handleSaveLoad: function handleSaveLoad(save) {\n      var _this15 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {\n        var _t6;\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.n) {\n            case 0:\n              _context7.p = 0;\n              _context7.n = 1;\n              return _this15.$store.dispatch('loadSave', save);\n            case 1:\n              // 更新UI状态\n              _this15.messages = _this15.$store.state.messages || [];\n              _this15.characters = _this15.$store.state.characters || [];\n              _this15.scenes = _this15.$store.state.scenes || [];\n              _this15.currentSceneIndex = _this15.$store.state.currentSceneIndex || 0;\n              _this15.diceHistory = _this15.$store.state.diceHistory || [];\n\n              // 关闭存档面板\n              _this15.togglePanel('gameSaves', true);\n              _this15.showSystemMessage(\"\\u5B58\\u6863 \\\"\".concat(save.name, \"\\\" \\u52A0\\u8F7D\\u6210\\u529F\"));\n              _context7.n = 3;\n              break;\n            case 2:\n              _context7.p = 2;\n              _t6 = _context7.v;\n              console.error('加载存档失败:', _t6);\n              _this15.showSystemMessage('加载存档失败');\n            case 3:\n              return _context7.a(2);\n          }\n        }, _callee7, null, [[0, 2]]);\n      }))();\n    },\n    // 处理存档错误\n    handleSaveError: function handleSaveError(errorMessage) {\n      this.showSystemMessage(errorMessage);\n    },\n    // 收集游戏状态用于存档\n    collectGameState: function collectGameState(customData) {\n      // 添加额外的状态数据\n      customData.roomName = this.room.name;\n      customData.roomDescription = this.room.description;\n      customData.onlineUsers = this.onlineUsers.map(function (user) {\n        return {\n          id: user.id,\n          username: user.username\n        };\n      });\n\n      // 添加其他需要保存的状态\n    },\n    // 快速存档功能\n    quickSave: function quickSave() {\n      var _this16 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        var _this16$$store$getter, gameState, saveData, response, _t7;\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              _context8.p = 0;\n              // 获取当前游戏状态\n              gameState = _this16.collectGameStateForQuickSave();\n              saveData = {\n                name: \"\\u5FEB\\u901F\\u5B58\\u6863 - \".concat(new Date().toLocaleString('zh-CN')),\n                description: '自动创建的快速存档',\n                room_id: parseInt(_this16.internalRoomId),\n                creator_id: ((_this16$$store$getter = _this16.$store.getters.currentUser) === null || _this16$$store$getter === void 0 ? void 0 : _this16$$store$getter.id) || 1,\n                save_data: gameState,\n                thumbnail: null,\n                is_auto_save: false // 快速存档不是自动存档\n              };\n              if (!_this16.quickSaveId) {\n                _context8.n = 2;\n                break;\n              }\n              _context8.n = 1;\n              return apiService.gameSaves.updateSave(_this16.quickSaveId, {\n                name: saveData.name,\n                description: saveData.description,\n                save_data: saveData.save_data\n              });\n            case 1:\n              response = _context8.v;\n              _context8.n = 4;\n              break;\n            case 2:\n              _context8.n = 3;\n              return apiService.gameSaves.createSave(saveData);\n            case 3:\n              response = _context8.v;\n            case 4:\n              if (response.data) {\n                _this16.quickSaveId = response.data.id;\n                _this16.showSystemMessage('快速存档成功');\n              }\n              _context8.n = 6;\n              break;\n            case 5:\n              _context8.p = 5;\n              _t7 = _context8.v;\n              console.error('快速存档失败:', _t7);\n              _this16.showSystemMessage('快速存档失败');\n            case 6:\n              return _context8.a(2);\n          }\n        }, _callee8, null, [[0, 5]]);\n      }))();\n    },\n    // 快速读档功能\n    quickLoad: function quickLoad() {\n      var _this17 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee9() {\n        var response, _t8;\n        return _regenerator().w(function (_context9) {\n          while (1) switch (_context9.n) {\n            case 0:\n              if (_this17.quickSaveId) {\n                _context9.n = 1;\n                break;\n              }\n              _this17.showSystemMessage('没有可用的快速存档');\n              return _context9.a(2);\n            case 1:\n              _context9.p = 1;\n              _context9.n = 2;\n              return apiService.gameSaves.getSave(_this17.quickSaveId);\n            case 2:\n              response = _context9.v;\n              if (!response.data) {\n                _context9.n = 4;\n                break;\n              }\n              _context9.n = 3;\n              return _this17.$store.dispatch('loadSave', response.data);\n            case 3:\n              // 更新UI状态\n              _this17.messages = _this17.$store.state.messages || [];\n              _this17.characters = _this17.$store.state.characters || [];\n              _this17.scenes = _this17.$store.state.scenes || [];\n              _this17.currentSceneIndex = _this17.$store.state.currentSceneIndex || 0;\n              _this17.diceHistory = _this17.$store.state.diceHistory || [];\n              _this17.showSystemMessage('快速读档成功');\n            case 4:\n              _context9.n = 6;\n              break;\n            case 5:\n              _context9.p = 5;\n              _t8 = _context9.v;\n              console.error('快速读档失败:', _t8);\n              _this17.showSystemMessage('快速读档失败');\n            case 6:\n              return _context9.a(2);\n          }\n        }, _callee9, null, [[1, 5]]);\n      }))();\n    },\n    // 收集游戏状态用于快速存档\n    collectGameStateForQuickSave: function collectGameStateForQuickSave() {\n      // 收集当前游戏状态\n      var gameState = {\n        timestamp: Date.now(),\n        messages: this.messages,\n        characters: this.characters,\n        scenes: this.scenes,\n        currentScene: this.currentSceneIndex,\n        diceHistory: this.diceHistory,\n        notes: this.$store.state.notes || {},\n        clues: this.$store.state.clues || [],\n        aiSettings: this.$store.state.aiSettings || {},\n        customData: {\n          roomName: this.room.name,\n          roomDescription: this.room.description,\n          onlineUsers: this.onlineUsers.map(function (user) {\n            return {\n              id: user.id,\n              username: user.username\n            };\n          })\n        }\n      };\n      return gameState;\n    },\n    // 处理键盘快捷键 - 已移除所有快捷键绑定\n    handleKeyDown: function handleKeyDown(event) {\n      // 所有快捷键绑定已移除\n    },\n    // 处理键盘快捷键 - 已移除所有快捷键绑定\n    handleKeyboardShortcuts: function handleKeyboardShortcuts(event) {\n      // 所有快捷键绑定已移除\n    },\n    // 更新窗口尺寸\n    updateWindowSize: function updateWindowSize() {\n      this.windowWidth = window.innerWidth;\n      this.windowHeight = window.innerHeight;\n    },\n    // 切换存档选项面板\n    toggleSaveOptions: function toggleSaveOptions() {\n      var _this18 = this;\n      this.showSaveOptions = !this.showSaveOptions;\n\n      // 点击其他区域关闭面板\n      if (this.showSaveOptions) {\n        setTimeout(function () {\n          var _closePanel = function closePanel(e) {\n            var panel = document.querySelector('.save-options-panel');\n            var button = document.querySelector('.toolbar-btn.active');\n            if (panel && !panel.contains(e.target) && (!button || !button.contains(e.target))) {\n              _this18.showSaveOptions = false;\n              document.removeEventListener('click', _closePanel);\n            }\n          };\n          document.addEventListener('click', _closePanel);\n        }, 100);\n      }\n    },\n    // 处理查看剧本事件\n    handleViewScenario: function handleViewScenario(data) {\n      this.openScenarioViewer(data.content, data.title, data.fileSize);\n    },\n    // 打开公告展示面板\n    openAnnouncement: function openAnnouncement() {\n      // 如果没有公告内容且是房主，显示默认编辑提示\n      if (!this.announcementContent && this.isRoomOwner) {\n        this.announcementContent = '在此输入公告内容，玩家可以查看和复制这些信息。\\n\\n可以放置：\\n- 规则提示\\n- 背景设定\\n- 重要信息\\n- 其他需要玩家了解的内容';\n      }\n      this.togglePanel('announcement');\n    },\n    // 处理公告保存\n    handleAnnouncementSave: function handleAnnouncementSave(data) {\n      this.announcementContent = data.content;\n      this.announcementTitle = data.title;\n      this.announcementTime = new Date().toLocaleString();\n\n      // 保存到本地存储\n      this.saveAnnouncementToLocalStorage();\n\n      // 使用新的方法发送公告更新消息\n      if (websocketService.isConnected) {\n        websocketService.sendAnnouncementUpdate(this.internalRoomId, {\n          content: data.content,\n          title: data.title,\n          timestamp: data.timestamp\n        });\n      }\n      this.showSystemMessage('公告已更新');\n    },\n    // 保存公告到本地存储\n    saveAnnouncementToLocalStorage: function saveAnnouncementToLocalStorage() {\n      try {\n        var announcementData = {\n          content: this.announcementContent,\n          title: this.announcementTitle,\n          time: this.announcementTime,\n          roomId: this.internalRoomId\n        };\n        this.safeSetJSON(\"room_announcement_\".concat(this.internalRoomId), announcementData);\n      } catch (error) {\n        console.error('保存公告到本地存储失败:', error);\n      }\n    },\n    // 从本地存储加载公告\n    loadAnnouncementFromLocalStorage: function loadAnnouncementFromLocalStorage() {\n      try {\n        var announcementData = this.safeGetJSON(\"room_announcement_\".concat(this.internalRoomId));\n        if (announcementData) {\n          this.announcementContent = announcementData.content || '';\n          this.announcementTitle = announcementData.title || '房间公告';\n          this.announcementTime = announcementData.time || new Date().toLocaleString();\n        }\n      } catch (error) {\n        console.error('从本地存储加载公告失败:', error);\n      }\n    },\n    // 处理WebSocket接收到的公告更新\n    handleAnnouncementUpdate: function handleAnnouncementUpdate(data) {\n      // 只有非房主才接收更新，房主是发送方\n      if (!this.isRoomOwner) {\n        this.announcementContent = data.content;\n        this.announcementTitle = data.title;\n        this.announcementTime = new Date(data.timestamp).toLocaleString();\n\n        // 保存到本地存储\n        this.saveAnnouncementToLocalStorage();\n\n        // 显示通知\n        this.showSystemMessage('房主更新了公告，点击顶部公告按钮查看');\n      }\n    },\n    // 从本地存储加载房间名称\n    loadRoomNameFromLocalStorage: function loadRoomNameFromLocalStorage() {\n      try {\n        // 确保房间ID是数字\n        var roomId = parseInt(this.internalRoomId) || this.room.id;\n        console.log('尝试从本地存储加载房间名称:', {\n          roomId: roomId,\n          internalRoomId: this.internalRoomId\n        });\n        var storedName = this.safeGetItem(\"room_name_\".concat(roomId));\n        if (storedName) {\n          console.log('找到存储的房间名称:', storedName);\n          this.room.name = storedName;\n\n          // 同步更新到store\n          if (this.$store.state.rooms && this.$store.state.rooms.length > 0) {\n            this.$store.commit('UPDATE_ROOM_LIST_NAME', {\n              roomId: roomId,\n              name: storedName\n            });\n          }\n        } else {\n          console.log('未找到存储的房间名称');\n        }\n      } catch (error) {\n        console.error('从本地存储加载房间名称失败:', error);\n      }\n    },\n    // 开始编辑房间名称\n    startEditRoomName: function startEditRoomName() {\n      var _this19 = this;\n      if (!this.isRoomOwner) return;\n      this.isEditingRoomName = true;\n      this.editableRoomName = this.room.name || '';\n\n      // 等待DOM更新后聚焦输入框\n      this.$nextTick(function () {\n        if (_this19.$refs.roomNameInput) {\n          _this19.$refs.roomNameInput.focus();\n        }\n      });\n    },\n    // 保存房间名称\n    saveRoomName: function saveRoomName() {\n      var _this20 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee0() {\n        var newName, roomId;\n        return _regenerator().w(function (_context0) {\n          while (1) switch (_context0.n) {\n            case 0:\n              if (_this20.isRoomOwner) {\n                _context0.n = 1;\n                break;\n              }\n              return _context0.a(2);\n            case 1:\n              newName = _this20.editableRoomName.trim();\n              if (newName) {\n                _context0.n = 2;\n                break;\n              }\n              _this20.cancelEditRoomName();\n              return _context0.a(2);\n            case 2:\n              // 调试信息\n              console.log('保存房间名称:', {\n                roomId: _this20.internalRoomId,\n                oldName: _this20.room.name,\n                newName: newName,\n                roomObject: _this20.room\n              });\n\n              // 更新房间名称\n              if (!(newName !== _this20.room.name)) {\n                _context0.n = 4;\n                break;\n              }\n              // 确保房间ID是数字\n              roomId = parseInt(_this20.internalRoomId) || _this20.room.id; // 使用store的action更新房间名称\n              _context0.n = 3;\n              return _this20.$store.dispatch('updateRoomName', {\n                roomId: roomId,\n                name: newName\n              });\n            case 3:\n              // 如果有WebSocket连接，发送房间名称更新\n              if (websocketService.isConnected) {\n                websocketService.sendMessage({\n                  type: 'room_name_update',\n                  roomId: roomId,\n                  name: newName\n                });\n              }\n              _this20.showSystemMessage('房间名称已更新');\n            case 4:\n              _this20.isEditingRoomName = false;\n            case 5:\n              return _context0.a(2);\n          }\n        }, _callee0);\n      }))();\n    },\n    // 取消编辑房间名称\n    cancelEditRoomName: function cancelEditRoomName() {\n      this.isEditingRoomName = false;\n    },\n    // 处理房间名称更新\n    handleRoomNameUpdate: function handleRoomNameUpdate(data) {\n      if (!this.isRoomOwner) {\n        // 确保房间ID是数字\n        var roomId = parseInt(this.internalRoomId) || this.room.id;\n        console.log('接收到房间名称更新:', {\n          roomId: roomId,\n          receivedData: data\n        });\n\n        // 使用store的action更新房间名称\n        this.$store.dispatch('updateRoomName', {\n          roomId: roomId,\n          name: data.name\n        });\n        this.showSystemMessage('房主更新了房间名称');\n      }\n    },\n    // 新功能相关方法\n    toggleCombat: function toggleCombat() {\n      this.togglePanel('combat');\n    },\n    toggleSkillCheck: function toggleSkillCheck() {\n      this.togglePanel('skillCheck');\n    },\n    toggleEquipment: function toggleEquipment() {\n      this.togglePanel('equipment');\n    },\n    toggleExperiencePack: function toggleExperiencePack() {\n      this.togglePanel('experiencePack');\n    },\n    toggleSpells: function toggleSpells() {\n      this.togglePanel('spells');\n    },\n    toggleMadness: function toggleMadness() {\n      this.togglePanel('madness');\n    },\n    toggleLibrary: function toggleLibrary() {\n      this.togglePanel('library');\n    },\n    // 战斗系统事件处理\n    handleCombatStarted: function handleCombatStarted() {\n      this.showSystemMessage('战斗开始！');\n    },\n    handleCombatEnded: function handleCombatEnded() {\n      this.showSystemMessage('战斗结束！');\n    },\n    openSkillCheckForCharacter: function openSkillCheckForCharacter(character) {\n      this.skillCheckCharacter = character;\n      this.skillCheckSkill = '';\n      this.togglePanel('skillCheck', false);\n    },\n    // 技能检定事件处理\n    handleSkillCheckResult: function handleSkillCheckResult(result) {\n      var message = \"\".concat(result.characterName, \" \\u8FDB\\u884C \").concat(result.skillName, \" \\u68C0\\u5B9A: \").concat(result.roll, \"/\").concat(result.targetValue, \" \").concat(this.getSuccessLevelText(result.successLevel));\n      this.sendMessage(message, 'system');\n    },\n    handleOpposedCheckResult: function handleOpposedCheckResult(result) {\n      var message = \"\\u5BF9\\u6297\\u68C0\\u5B9A: \".concat(result.initiator.name, \"(\").concat(result.initiator.roll, \") VS \").concat(result.opponent.name, \"(\").concat(result.opponent.roll, \") - \\u80DC\\u8005: \").concat(result.winner || '平局');\n      this.sendMessage(message, 'system');\n    },\n    handleSkillGrowth: function handleSkillGrowth(data) {\n      var message = \"\".concat(data.character.name, \" \\u7684 \").concat(data.skill, \" \\u6280\\u80FD\\u6210\\u957F +\").concat(data.improvement);\n      this.sendMessage(message, 'system');\n    },\n    handleGrowthCheckResult: function handleGrowthCheckResult(result) {\n      var message = \"\".concat(result.character, \" \\u8FDB\\u884C \").concat(result.skill, \" \\u6210\\u957F\\u68C0\\u5B9A: \").concat(result.roll, \"/\").concat(result.currentValue, \" \").concat(result.success ? '成功' : '失败').concat(result.success ? \" +\".concat(result.improvement) : '');\n      this.sendMessage(message, 'system');\n    },\n    handleSkillCheckCharacterSelected: function handleSkillCheckCharacterSelected(character) {\n      this.skillCheckCharacter = character;\n    },\n    // 装备系统事件处理\n    handleWeaponEquipped: function handleWeaponEquipped(weapon) {\n      this.showSystemMessage(\"\\u88C5\\u5907\\u4E86\\u6B66\\u5668: \".concat(weapon.name));\n    },\n    handleWeaponUnequipped: function handleWeaponUnequipped(weapon) {\n      this.showSystemMessage(\"\\u5378\\u4E0B\\u4E86\\u6B66\\u5668: \".concat(weapon.name));\n    },\n    handleWeaponUsed: function handleWeaponUsed(weapon) {\n      this.showSystemMessage(\"\\u4F7F\\u7528\\u4E86\\u6B66\\u5668: \".concat(weapon.name));\n    },\n    handleArmorEquipped: function handleArmorEquipped(armor) {\n      this.showSystemMessage(\"\\u88C5\\u5907\\u4E86\\u9632\\u5177: \".concat(armor.name));\n    },\n    handleArmorUnequipped: function handleArmorUnequipped(armor) {\n      this.showSystemMessage(\"\\u5378\\u4E0B\\u4E86\\u9632\\u5177: \".concat(armor.name));\n    },\n    handleItemUsed: function handleItemUsed(item) {\n      this.showSystemMessage(\"\\u4F7F\\u7528\\u4E86\\u7269\\u54C1: \".concat(item.name));\n    },\n    handleItemPurchased: function handleItemPurchased(item) {\n      this.showSystemMessage(\"\\u8D2D\\u4E70\\u4E86\\u7269\\u54C1: \".concat(item.name));\n    },\n    handleItemDropped: function handleItemDropped(item) {\n      this.showSystemMessage(\"\\u4E22\\u5F03\\u4E86\\u7269\\u54C1: \".concat(item.name));\n    },\n    handleHealCharacter: function handleHealCharacter(data) {\n      var message = \"\".concat(data.character.name, \" \\u4F7F\\u7528 \").concat(data.source, \" \\u6062\\u590D\\u4E86 \").concat(data.amount, \" \\u70B9\\u751F\\u547D\\u503C\");\n      this.sendMessage(message, 'system');\n    },\n    handleReadBook: function handleReadBook(data) {\n      var message = \"\".concat(data.character.name, \" \\u9605\\u8BFB\\u4E86 \").concat(data.book.name);\n      this.sendMessage(message, 'system');\n    },\n    // 法术系统事件处理\n    handleSpellCast: function handleSpellCast(data) {\n      var message = \"\".concat(data.caster.name, \" \\u65BD\\u653E\\u4E86 \").concat(data.spell.name);\n      this.sendMessage(message, 'system');\n\n      // 如果有目标，添加目标信息\n      if (data.target && data.target !== 'self') {\n        this.sendMessage(\"\\u76EE\\u6807: \".concat(data.target), 'system');\n      }\n\n      // 如果有描述，添加描述\n      if (data.description) {\n        this.sendMessage(\"\\u65BD\\u6CD5\\u63CF\\u8FF0: \".concat(data.description), 'system');\n      }\n    },\n    // 角色更新处理\n    handleCharacterUpdate: function handleCharacterUpdate(character) {\n      // 更新角色数据\n      this.$emit('character-updated', character);\n\n      // 如果是当前选中的角色，更新本地状态\n      if (this.selectedCharacter && this.selectedCharacter.id === character.id) {\n        this.selectedCharacter = character;\n      }\n    },\n    // 辅助方法\n    getSuccessLevelText: function getSuccessLevelText(level) {\n      var levels = {\n        'extreme': '极难成功',\n        'hard': '困难成功',\n        'normal': '常规成功',\n        'failure': '失败'\n      };\n      return levels[level] || '未知';\n    }\n  }\n};", "map": {"version": 3, "names": ["ChatBox", "Modal", "FloatingPanel", "FloatingCharacterSheet", "FloatingDice<PERSON>oller", "FloatingNotes", "AIModelSettings", "MapSystem", "ClueBoard", "AudioSystem", "VoiceChat", "PrivateChatManager", "GroupChat", "GameSaveManager", "Sc<PERSON>rioViewer", "Announcement<PERSON>iewer", "apiService", "websocketService", "storageMixin", "name", "mixins", "components", "CombatSystem", "SkillCheckSystem", "EquipmentSystem", "ExperiencePackSystem", "SpellSystem", "MadnessSystem", "MythosLibrary", "props", "data", "room", "messages", "characters", "<PERSON><PERSON><PERSON><PERSON>", "websocket", "showCharacterModal", "showSettingsModal", "diceHistory", "gameNotes", "onlineUsers", "isTyping", "typingUsername", "typingTimer", "lastTypingTime", "fontSize", "playSounds", "showNotifications", "isToolbarCollapsed", "isMobile", "internalRoomId", "hasUnsavedChanges", "visiblePanels", "character", "dice", "notes", "aiSettings", "map", "clueBoard", "audioSystem", "voiceChat", "privateChat", "groupChat", "gameSaves", "<PERSON><PERSON><PERSON><PERSON>", "announcement", "combat", "<PERSON><PERSON><PERSON><PERSON>", "equipment", "experiencePack", "spells", "madness", "library", "currentAIMode", "skillCheckCharacter", "skillCheckSkill", "pendingSceneDescription", "aiStatus", "active", "busy", "isRoomOwner", "showConfigAlert", "characterSyncTimer", "scenes", "currentSceneIndex", "currentCharacterId", "showNotes", "isKP", "currentUser", "users", "isLoading", "showDiceRoller", "showCharacterSheet", "rollDice", "uploadScript", "quickSaveId", "isPreviewMode", "tempState", "windowWidth", "window", "innerWidth", "windowHeight", "innerHeight", "showSaveOptions", "scenarioContent", "scenarioTitle", "scenarioFileSize", "announcementContent", "announcementTitle", "announcementTime", "Date", "toLocaleString", "isEditingRoomName", "editableRoomName", "computed", "isDarkMode", "$store", "getters", "isDarkTheme", "hasQuickSave", "created", "_this", "_asyncToGenerator", "_regenerator", "m", "_callee", "routeId", "numericId", "savedToolbarState", "_error$response", "mockRoom", "_t", "w", "_context", "n", "p", "$route", "params", "id", "console", "log", "concat", "commit", "parseInt", "isNaN", "error", "username", "updateWindowSize", "checkMobileDevice", "safeGetItem", "fetchRoomData", "checkRoomOwnership", "connectWebSocket", "fetchUserCharacters", "setTimeout", "fetchRoomMessages", "loadUserSettings", "loadAnnouncementFromLocalStorage", "loadRoomNameFromLocalStorage", "togglePanel", "checkSystemStatus", "checkApiKeyStatus", "syncSelectedCharacterStatus", "setupCharacterSyncTimer", "handleRollDice", "handleUploadScript", "addEventListener", "handleKeyDown", "v", "message", "response", "detail", "description", "showSystemMessage", "e", "$router", "push", "a", "mounted", "detectDevice", "document", "handleFullScreenChange", "elem", "querySelector", "requestFullscreen", "webkitRequestFullscreen", "mozRequestFullScreen", "msRequestFullscreen", "warn", "handleKeyboardShortcuts", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "clearInterval", "close", "disconnectWebSocket", "isFullScreen", "fullscreenElement", "webkitFullscreenElement", "mozFullScreenElement", "msFullscreenElement", "exitFullscreen", "webkitExitFullscreen", "mozCancelFullScreen", "msExitFullscreen", "methods", "_this2", "_callee2", "_this2$$store$getters", "currentRoom", "roomId", "_t2", "_t3", "_context2", "currentRoomData", "dispatch", "history", "replaceState", "_this3", "_callee3", "_t4", "_context3", "userCharacters", "length", "_this4", "_callee4", "_context4", "type", "content", "timestamp", "now", "user_id", "_this5", "_this$$store$getters$", "_this$room", "userId", "onConnect", "_this5$room", "_this5$$store$getters", "sendMessage", "room_id", "onDisconnect", "reason", "onError", "setEventListeners", "handleChatMessage", "handleUserJoined", "handleUserLeft", "handleTypingEvent", "handleDiceRoll", "handleSkillCheck", "handleCharacterUpdate", "handleAnnouncementUpdate", "handleRoomNameUpdate", "connect", "handleBeforeUnload", "process", "env", "NODE_ENV", "_this5$$store$getters2", "disconnect", "handleRoll", "rollData", "_this$$store$getters$2", "results", "total", "count", "faces", "modifier", "i", "Math", "floor", "random", "reduce", "sum", "val", "unshift", "pop", "diceMessage", "join", "toISOString", "roll_data", "playDiceSound", "skillData", "_this$$store$getters$3", "character_name", "character_id", "performLocalSkillCheck", "_this6", "_callee5", "_this6$$store$getters", "diceResult", "skillValue", "success", "level", "skillName", "<PERSON><PERSON><PERSON>", "resultMessage", "_context5", "skill_value", "skill_name", "roll_result", "skill", "_this7", "messageData", "trim", "ai_mode", "_this$$store$getters$4", "_this$$store$getters$5", "_objectSpread", "ai_generated", "isConnected", "socket", "result", "sent", "messageInput", "startGameKeywords", "playerMessage", "toLowerCase", "some", "keyword", "includes", "sceneMessage", "selectCharacter", "_this$$store$getters$6", "updateCurrentCharacterId", "audio", "Audio", "volume", "play", "err", "sendTypingStatus", "readyState", "WebSocket", "OPEN", "_this$$store$getters$7", "toggleDarkMode", "changeFontSize", "change", "max", "min", "documentElement", "style", "setProperty", "safeSetItem", "saveNotes", "undefined", "saveSettings", "savedFontSize", "panelSettings", "JSON", "parse", "privateChatSetting", "aiStatusSettings", "ai", "sendDesktopNotification", "Notification", "permission", "body", "icon", "requestPermission", "panelName", "forceClose", "arguments", "safeSetJSON", "handleTyping", "handleTypingStopped", "handleSanityUpdate", "characterId", "sanity", "newSanity", "is_mock", "keeper_id", "wsStatus", "resourceLocks", "messageQueue", "ensureChatInputEnabled", "_this8", "textareas", "querySelectorAll", "for<PERSON>ach", "textarea", "disabled", "$refs", "chatBox", "messageTextarea", "handleAISettingsUpdated", "settings", "parsedSettings", "safeGetJSON", "updateAIMode", "mode", "_this9", "setAIMode", "goToAISettings", "_this0", "focus", "dismissConfig<PERSON>lert", "_this1", "_callee6", "apiTested", "_t5", "_context6", "getSettings", "<PERSON><PERSON><PERSON><PERSON>", "api_key", "apiUrl", "api_url", "modelName", "model_name", "_this10", "then", "handleCharacterSync", "updatedCharacter", "find", "c", "_this11", "setInterval", "handleSceneSave", "sceneIndex", "scene", "sendSystemMessage", "saveScenesToServer", "send", "stringify", "loadScenesFromServer", "handleNoteSaved", "note", "toggle<PERSON>ice<PERSON>oller", "toggleCharacterSheet", "toggleNotes", "toggleMap", "toggleClueBoard", "toggleAudioSystem", "toggleVoiceChat", "togglePrivateChat", "toggleGroupChat", "handleVoiceConnected", "handleVoiceDisconnected", "diceData", "_this$currentUser", "file", "_this12", "toggleToolbar", "user", "index", "findIndex", "splice", "_this$$store$getters$8", "_this13", "typing", "clearTimeout", "shift", "event", "preventDefault", "returnValue", "toggleFullScreen", "_this14", "setAttribute", "classList", "add", "requestFullScreen", "apply", "remove", "exitFullScreen", "container", "notice", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "openChatBoxSettings", "showAISettings", "handleMemoryCleared", "handleScriptSettingsSaved", "openScenarioViewer", "title", "fileSize", "handleScriptUploaded", "fileInfo", "fileName", "toggleGameSaves", "handleSaveCreated", "save", "handleSaveUpdated", "handleSaveDeleted", "handleSaveAutoCreated", "handleSaveMessage", "handleSaveRestored", "version", "handleSaveLoad", "handleSavePreview", "saveData", "_toConsumableArray", "save_data", "currentScene", "exitPreviewMode", "_this15", "_callee7", "_t6", "_context7", "state", "handleSaveError", "errorMessage", "collectGameState", "customData", "roomName", "roomDescription", "quickSave", "_this16", "_callee8", "_this16$$store$getter", "gameState", "_t7", "_context8", "collectGameStateForQuickSave", "creator_id", "thumbnail", "is_auto_save", "updateSave", "createSave", "quickLoad", "_this17", "_callee9", "_t8", "_context9", "getSave", "clues", "toggleSaveOptions", "_this18", "closePanel", "panel", "button", "contains", "target", "handleViewScenario", "openAnnouncement", "handleAnnouncementSave", "saveAnnouncementToLocalStorage", "sendAnnouncementUpdate", "announcementData", "time", "storedName", "rooms", "startEditRoomName", "_this19", "$nextTick", "roomNameInput", "saveRoomName", "_this20", "_callee0", "newName", "_context0", "cancelEditRoomName", "old<PERSON>ame", "roomObject", "receivedData", "toggleCombat", "toggleS<PERSON><PERSON>he<PERSON>", "toggleEquipment", "toggleExperiencePack", "toggleSpells", "toggleMadness", "toggleLibrary", "handleCombatStarted", "handleCombatEnded", "openSkillCheckForCharacter", "handleSkillCheckResult", "roll", "targetValue", "getSuccessLevelText", "successLevel", "handleOpposedCheckResult", "initiator", "opponent", "winner", "handleSkillGrowth", "improvement", "handleGrowthCheckResult", "currentValue", "handleSkillCheckCharacterSelected", "handleWeaponEquipped", "weapon", "handleWeaponUnequipped", "handleWeaponUsed", "handleArmorEquipped", "armor", "handleArmorUnequipped", "handleItemUsed", "item", "handleItemPurchased", "handleItemDropped", "handleHealCharacter", "source", "amount", "handleReadBook", "book", "handleSpellCast", "caster", "spell", "$emit", "levels"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Room.vue"], "sourcesContent": ["<template>\n  <div class=\"room-container\">\n    <!-- 用户身份显示区域 -->\n    <div class=\"user-role-indicator\">\n      <i class=\"fas\" :class=\"isRoomOwner ? 'fa-crown' : 'fa-user'\"></i>\n      <span>{{ isRoomOwner ? '房主' : '玩家' }}</span>\n    </div>\n    \n    <!-- 房间顶部栏，添加双击事件 -->\n    <div class=\"room-header\" @dblclick=\"toggleFullScreen\">\n      <div class=\"room-title\">\n        <h2 v-if=\"!isEditingRoomName || !isRoomOwner\" @click=\"startEditRoomName\" :class=\"{ 'editable': isRoomOwner }\">\n          {{ room.name || '加载中...' }}\n          <i v-if=\"isRoomOwner\" class=\"fas fa-edit edit-icon\"></i>\n        </h2>\n        <input \n          v-else\n          type=\"text\" \n          v-model=\"editableRoomName\" \n          @blur=\"saveRoomName\"\n          @keyup.enter=\"saveRoomName\"\n          @keyup.esc=\"cancelEditRoomName\"\n          ref=\"roomNameInput\"\n          class=\"room-name-input\"\n        />\n        <span class=\"room-participants\" v-if=\"onlineUsers.length > 0\">\n          {{ onlineUsers.length }} 人在线\n        </span>\n      </div>\n      <div class=\"room-actions\">\n        <button class=\"room-action-btn announcement-btn\" @click=\"openAnnouncement\" title=\"公告\">\n          <i class=\"fas fa-bullhorn\"></i>\n          <span class=\"btn-text\">公告</span>\n        </button>\n        <button class=\"room-action-btn toggle-btn\" @click=\"toggleToolbar\">\n          <i :class=\"isToolbarCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'\"></i>\n          <span class=\"btn-text\">{{ isToolbarCollapsed ? '展开' : '收起' }}</span>\n        </button>\n        <button class=\"room-action-btn\" @click=\"toggleFullScreen\" title=\"全屏\">\n          <i class=\"fas fa-expand\"></i>\n        </button>\n        <button class=\"room-action-btn\" @click=\"showSettingsModal = true\" title=\"设置\">\n          <i class=\"fas fa-cog\"></i>\n        </button>\n      </div>\n    </div>\n    \n    <div class=\"room-controls\">\n      <span class=\"room-participants\">👥 {{ onlineUsers.length }} 人在线</span>\n      <button @click=\"toggleDarkMode\" class=\"control-btn theme-btn\">\n        {{ isDarkMode ? '深色模式' : '浅色模式' }}\n      </button>\n      \n      <!-- 添加剧本模式设置按钮 -->\n      <button v-if=\"currentAIMode === 'script'\" @click=\"openChatBoxSettings\" class=\"control-btn script-btn\">\n        <i class=\"fas fa-book-open\"></i>\n        剧本模式设置\n      </button>\n      \n      <!-- 添加快速存档按钮 -->\n      <button v-if=\"isRoomOwner\" @click=\"quickSave\" class=\"control-btn save-btn\">\n        <i class=\"fas fa-save\"></i>\n        快速存档\n      </button>\n      \n      <!-- 添加快速读档按钮 -->\n      <button v-if=\"isRoomOwner && hasQuickSave\" @click=\"quickLoad\" class=\"control-btn load-btn\">\n        <i class=\"fas fa-folder-open\"></i>\n        快速读档\n      </button>\n    </div>\n    \n    <div class=\"main-content\">\n      \n      <!-- 消息区域 -->\n      <div class=\"message-area\">\n        <chat-box\n          ref=\"chatBox\"\n          :messages=\"messages\"\n          :currentUser=\"currentUser\"\n          :users=\"users\"\n          :isLoading=\"isLoading\"\n          :roomChatMode=\"room?.chat_mode || 'normal'\"\n          @send-message=\"sendMessage\"\n          @roll-dice=\"rollDice\"\n          @upload-script=\"uploadScript\"\n          @view-scenario=\"handleViewScenario\"\n        />\n      </div>\n    </div>\n    \n    <!-- 浮动面板 -->\n    <floating-character-sheet\n      v-if=\"visiblePanels.character\"\n      :character=\"selectedCharacter\"\n      :initial-x=\"50\"\n      :initial-y=\"100\"\n      @close=\"togglePanel('character', true)\"\n      @skill-check=\"handleSkillCheck\"\n      @select-character=\"showCharacterModal = true\"\n      @update-sanity=\"handleSanityUpdate\"\n      @update-character=\"handleCharacterUpdate\"\n      @character-sync=\"handleCharacterSync\"\n    />\n    \n    <floating-dice-roller\n      v-if=\"visiblePanels.dice\"\n      :initial-x=\"500\"\n      :initial-y=\"100\"\n      :dice-history=\"diceHistory\"\n      @close=\"togglePanel('dice', true)\"\n      @roll=\"handleRoll\"\n      @skill-check=\"handleSkillCheck\"\n    />\n    \n    <floating-notes\n      v-if=\"showNotes\"\n      :initial-x=\"300\"\n      :initial-y=\"300\"\n      :character-id=\"currentCharacterId\"\n      @close=\"toggleNotes\"\n      @note-saved=\"handleNoteSaved\"\n    />\n    \n    <!-- 群聊面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.groupChat && currentAIMode === 'script'\"\n      title=\"玩家讨论区\"\n      :initial-x=\"400\"\n      :initial-y=\"200\"\n      :initial-width=\"500\"\n      :initial-height=\"400\"\n      :min-width=\"300\"\n      :min-height=\"300\"\n      @close=\"togglePanel('groupChat', true)\"\n    >\n      <group-chat :room-id=\"internalRoomId\" :ai-mode=\"currentAIMode\" />\n    </floating-panel>\n    \n    <!-- 地图系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.map\"\n      title=\"地图系统\"\n      :initial-x=\"100\"\n      :initial-y=\"150\"\n      :initial-width=\"600\"\n      :initial-height=\"450\"\n      :min-width=\"400\"\n      :min-height=\"350\"\n      @close=\"togglePanel('map', true)\"\n    >\n      <map-system :room-id=\"internalRoomId\" />\n    </floating-panel>\n    \n    <!-- 线索墙面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.clueBoard\"\n      title=\"线索墙\"\n      :initial-x=\"150\"\n      :initial-y=\"200\"\n      :initial-width=\"700\"\n      :initial-height=\"500\"\n      :min-width=\"400\"\n      :min-height=\"400\"\n      @close=\"togglePanel('clueBoard', true)\"\n    >\n      <clue-board :room-id=\"internalRoomId\" />\n    </floating-panel>\n\n    <!-- 战斗系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.combat\"\n      title=\"战斗系统\"\n      :initial-x=\"100\"\n      :initial-y=\"100\"\n      :initial-width=\"800\"\n      :initial-height=\"600\"\n      :min-width=\"600\"\n      :min-height=\"500\"\n      @close=\"togglePanel('combat', true)\"\n    >\n      <combat-system\n        @combat-started=\"handleCombatStarted\"\n        @combat-ended=\"handleCombatEnded\"\n        @open-skill-check=\"openSkillCheckForCharacter\"\n      />\n    </floating-panel>\n\n    <!-- 技能检定系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.skillCheck\"\n      title=\"技能检定系统\"\n      :initial-x=\"200\"\n      :initial-y=\"150\"\n      :initial-width=\"700\"\n      :initial-height=\"550\"\n      :min-width=\"500\"\n      :min-height=\"450\"\n      @close=\"togglePanel('skillCheck', true)\"\n    >\n      <skill-check-system\n        :initial-character=\"skillCheckCharacter\"\n        :initial-skill=\"skillCheckSkill\"\n        @close=\"togglePanel('skillCheck', true)\"\n        @skill-check-result=\"handleSkillCheckResult\"\n        @opposed-check-result=\"handleOpposedCheckResult\"\n        @skill-growth=\"handleSkillGrowth\"\n        @growth-check-result=\"handleGrowthCheckResult\"\n        @character-selected=\"handleSkillCheckCharacterSelected\"\n      />\n    </floating-panel>\n\n    <!-- 装备系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.equipment\"\n      title=\"装备系统\"\n      :initial-x=\"150\"\n      :initial-y=\"100\"\n      :initial-width=\"900\"\n      :initial-height=\"650\"\n      :min-width=\"700\"\n      :min-height=\"500\"\n      @close=\"togglePanel('equipment', true)\"\n    >\n      <equipment-system\n        @weapon-equipped=\"handleWeaponEquipped\"\n        @weapon-unequipped=\"handleWeaponUnequipped\"\n        @weapon-used=\"handleWeaponUsed\"\n        @armor-equipped=\"handleArmorEquipped\"\n        @armor-unequipped=\"handleArmorUnequipped\"\n        @item-used=\"handleItemUsed\"\n        @item-purchased=\"handleItemPurchased\"\n        @item-dropped=\"handleItemDropped\"\n        @heal-character=\"handleHealCharacter\"\n        @read-book=\"handleReadBook\"\n      />\n    </floating-panel>\n\n    <!-- 经历包系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.experiencePack\"\n      title=\"经历包系统\"\n      :initial-x=\"250\"\n      :initial-y=\"150\"\n      :initial-width=\"800\"\n      :initial-height=\"600\"\n      :min-width=\"600\"\n      :min-height=\"500\"\n      @close=\"togglePanel('experiencePack', true)\"\n    >\n      <experience-pack-system\n        @character-updated=\"handleCharacterUpdate\"\n      />\n    </floating-panel>\n\n    <!-- 法术系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.spells\"\n      title=\"法术系统\"\n      :initial-x=\"100\"\n      :initial-y=\"120\"\n      :initial-width=\"900\"\n      :initial-height=\"700\"\n      :min-width=\"700\"\n      :min-height=\"600\"\n      @close=\"togglePanel('spells', true)\"\n    >\n      <spell-system\n        @character-updated=\"handleCharacterUpdate\"\n        @spell-cast=\"handleSpellCast\"\n      />\n    </floating-panel>\n\n    <!-- 疯狂症状系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.madness\"\n      title=\"疯狂症状系统\"\n      :initial-x=\"150\"\n      :initial-y=\"100\"\n      :initial-width=\"850\"\n      :initial-height=\"650\"\n      :min-width=\"650\"\n      :min-height=\"550\"\n      @close=\"togglePanel('madness', true)\"\n    >\n      <madness-system\n        @character-updated=\"handleCharacterUpdate\"\n      />\n    </floating-panel>\n\n    <!-- 神话典籍图书馆面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.library\"\n      title=\"神话典籍图书馆\"\n      :initial-x=\"200\"\n      :initial-y=\"80\"\n      :initial-width=\"950\"\n      :initial-height=\"750\"\n      :min-width=\"750\"\n      :min-height=\"650\"\n      @close=\"togglePanel('library', true)\"\n    >\n      <mythos-library\n        @character-updated=\"handleCharacterUpdate\"\n      />\n    </floating-panel>\n    \n    <!-- 音频系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.audioSystem && isRoomOwner\"\n      title=\"音频系统\"\n      :initial-x=\"200\"\n      :initial-y=\"250\"\n      :initial-width=\"600\"\n      :initial-height=\"400\"\n      :min-width=\"350\"\n      :min-height=\"300\"\n      @close=\"togglePanel('audioSystem', true)\"\n    >\n      <audio-system :roomId=\"internalRoomId\" :isKP=\"isRoomOwner\" />\n    </floating-panel>\n    \n    <!-- 语音聊天面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.voiceChat\"\n      title=\"语音聊天\"\n      :initial-x=\"250\"\n      :initial-y=\"300\"\n      :initial-width=\"300\"\n      :initial-height=\"400\"\n      :min-width=\"250\"\n      :min-height=\"300\"\n      @close=\"togglePanel('voiceChat', true)\"\n    >\n      <voice-chat \n        :roomId=\"internalRoomId\" \n        :username=\"$store.getters.currentUser?.username || '玩家'\" \n        :isKP=\"isKP\"\n        @voice-connected=\"handleVoiceConnected\" \n        @voice-disconnected=\"handleVoiceDisconnected\" \n      />\n    </floating-panel>\n    \n    <div class=\"room-toolbar\" :class=\"{ 'collapsed': isToolbarCollapsed }\">\n      <div class=\"toolbar-buttons\">\n        <!-- 角色相关工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleDiceRoller\" class=\"toolbar-btn\" :class=\"{ 'active': showDiceRoller }\">\n            <i class=\"fas fa-dice-d20\"></i>\n            <span class=\"tooltip\">骰子</span>\n          </button>\n\n          <button @click=\"toggleCharacterSheet\" class=\"toolbar-btn\" :class=\"{ 'active': showCharacterSheet }\">\n            <i class=\"fas fa-user\"></i>\n            <span class=\"tooltip\">角色卡</span>\n          </button>\n\n          <button @click=\"toggleSkillCheck\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.skillCheck }\">\n            <i class=\"fas fa-cog\"></i>\n            <span class=\"tooltip\">技能检定</span>\n          </button>\n\n          <button @click=\"toggleNotes\" class=\"toolbar-btn\" :class=\"{ 'active': showNotes }\">\n            <i class=\"fas fa-sticky-note\"></i>\n            <span class=\"tooltip\">笔记</span>\n          </button>\n        </div>\n\n        <!-- 战斗和装备工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleCombat\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.combat }\">\n            <i class=\"fas fa-sword\"></i>\n            <span class=\"tooltip\">战斗</span>\n          </button>\n\n          <button @click=\"toggleEquipment\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.equipment }\">\n            <i class=\"fas fa-shield-alt\"></i>\n            <span class=\"tooltip\">装备</span>\n          </button>\n\n          <button @click=\"toggleExperiencePack\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.experiencePack }\">\n            <i class=\"fas fa-star\"></i>\n            <span class=\"tooltip\">经历包</span>\n          </button>\n        </div>\n\n        <!-- 神话和魔法工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleSpells\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.spells }\">\n            <i class=\"fas fa-magic\"></i>\n            <span class=\"tooltip\">法术</span>\n          </button>\n\n          <button @click=\"toggleMadness\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.madness }\">\n            <i class=\"fas fa-brain\"></i>\n            <span class=\"tooltip\">疯狂</span>\n          </button>\n\n          <button @click=\"toggleLibrary\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.library }\">\n            <i class=\"fas fa-book-open\"></i>\n            <span class=\"tooltip\">典籍</span>\n          </button>\n        </div>\n        \n        <!-- 地图和线索工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleMap\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.map }\">\n            <i class=\"fas fa-map\"></i>\n            <span class=\"tooltip\">地图</span>\n          </button>\n          \n          <button @click=\"toggleClueBoard\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.clueBoard }\">\n            <i class=\"fas fa-thumbtack\"></i>\n            <span class=\"tooltip\">线索墙</span>\n          </button>\n          \n          <button \n            v-if=\"isRoomOwner\"\n            @click=\"toggleAudioSystem\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.audioSystem }\"\n          >\n            <i class=\"fas fa-music\"></i>\n            <span class=\"tooltip\">音频</span>\n          </button>\n        </div>\n        \n        <!-- 游戏存档按钮 -->\n        <div class=\"toolbar-group\" v-if=\"isRoomOwner\">\n          <button \n            @click=\"toggleSaveOptions\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': showSaveOptions }\"\n          >\n            <i class=\"fas fa-save\"></i>\n            <span class=\"tooltip\">游戏存档</span>\n          </button>\n        </div>\n        \n        <!-- 聊天工具 -->\n        <div class=\"toolbar-group\">\n          <button \n            @click=\"toggleVoiceChat\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.voiceChat }\"\n          >\n            <i class=\"fas fa-microphone\"></i>\n            <span class=\"tooltip\">语音</span>\n          </button>\n          \n          <button \n            @click=\"togglePrivateChat\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.privateChat }\"\n          >\n            <i class=\"fas fa-comments\"></i>\n            <span class=\"tooltip\">私聊</span>\n          </button>\n          \n          <button \n            v-if=\"currentAIMode === 'script'\"\n            @click=\"toggleGroupChat\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.groupChat }\"\n          >\n            <i class=\"fas fa-users\"></i>\n            <span class=\"tooltip\">群聊</span>\n          </button>\n        </div>\n      </div>\n      \n      <!-- 折叠按钮 -->\n      <button @click=\"toggleToolbar\" class=\"collapse-btn\">\n        <i :class=\"[\n          'fas', \n          isToolbarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'\n        ]\"></i>\n      </button>\n    </div>\n    \n    <!-- 浮动面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.privateChat\"\n      title=\"私聊\"\n      :initial-x=\"windowWidth - 350\"\n      :initial-y=\"windowHeight - 450\"\n      :initial-width=\"320\"\n      :initial-height=\"400\"\n      :min-width=\"250\"\n      :min-height=\"300\"\n      @close=\"togglePanel('privateChat', true)\"\n    >\n      <private-chat-manager :online-users=\"onlineUsers\" />\n    </floating-panel>\n    \n    <!-- 私聊管理器 -->\n    <!-- <private-chat-manager :online-users=\"onlineUsers\" :hidden=\"isToolbarCollapsed\" /> -->\n    \n    <!-- 角色选择模态框 -->\n    <modal v-if=\"showCharacterModal\" @close=\"showCharacterModal = false\">\n      <template #header>\n        <h3>选择角色</h3>\n      </template>\n      <div class=\"character-list\">\n        <div \n          v-for=\"character in characters\" \n          :key=\"character.id\" \n          @click=\"selectCharacter(character)\"\n          class=\"character-item\"\n        >\n          <h4>{{ character.name }}</h4>\n          <p>{{ character.occupation }}</p>\n        </div>\n      </div>\n    </modal>\n    \n    <!-- 设置模态框 -->\n    <modal v-if=\"showSettingsModal\" @close=\"showSettingsModal = false\">\n      <template #header>\n        <h3>房间设置</h3>\n      </template>\n      <div class=\"settings-content\">\n        <div class=\"setting-group\">\n          <label>字体大小</label>\n          <div class=\"font-size-controls\">\n            <button @click=\"changeFontSize(-1)\" class=\"font-btn\">A-</button>\n            <span class=\"current-size\">{{ fontSize }}px</span>\n            <button @click=\"changeFontSize(1)\" class=\"font-btn\">A+</button>\n          </div>\n        </div>\n        \n        <div class=\"setting-group\">\n          <label>通知设置</label>\n          <div class=\"toggle-switch\">\n            <input type=\"checkbox\" id=\"soundToggle\" v-model=\"playSounds\">\n            <label for=\"soundToggle\">骰子声音</label>\n          </div>\n          <div class=\"toggle-switch\">\n            <input type=\"checkbox\" id=\"notifyToggle\" v-model=\"showNotifications\">\n            <label for=\"notifyToggle\">桌面通知</label>\n          </div>\n        </div>\n        \n        <div class=\"setting-actions\">\n          <button @click=\"saveSettings\" class=\"save-settings-btn\">保存设置</button>\n        </div>\n      </div>\n    </modal>\n    \n    <!-- 剧本模式设置已移至ChatBox.vue -->\n    \n    <!-- AI模型设置面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.aiSettings && isRoomOwner\"\n      title=\"AI模型设置\"\n      :initial-x=\"200\"\n      :initial-y=\"150\"\n      :initial-width=\"500\"\n      :initial-height=\"550\"\n      @close=\"togglePanel('aiSettings', true)\"\n    >\n      <a-i-model-settings \n        :room-id=\"roomId\"\n        :is-room-owner=\"isRoomOwner\"\n        @settings-updated=\"handleAISettingsUpdated\"\n      />\n    </floating-panel>\n    \n    <!-- 游戏存档管理面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.gameSaves && isRoomOwner\"\n      title=\"游戏存档\"\n      :initial-x=\"200\"\n      :initial-y=\"250\"\n      :initial-width=\"500\"\n      :initial-height=\"400\"\n      @close=\"togglePanel('gameSaves', true)\"\n    >\n      <game-save-manager \n        :room-id=\"internalRoomId\" \n        @save-created=\"handleSaveCreated\" \n        @save-updated=\"handleSaveUpdated\"\n        @save-deleted=\"handleSaveDeleted\"\n        @save-load=\"handleSaveLoad\" \n        @save-error=\"handleSaveError\" \n        @collect-state=\"collectGameState\"\n        @save-auto-created=\"handleSaveAutoCreated\"\n        @save-message=\"handleSaveMessage\"\n        @save-restored=\"handleSaveRestored\"\n        @save-preview=\"handleSavePreview\"\n      />\n    </floating-panel>\n    \n    <!-- 剧本查看器面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.scenarioViewer && scenarioContent\"\n      title=\"剧本查看器\"\n      :initial-x=\"300\"\n      :initial-y=\"150\"\n      :initial-width=\"700\"\n      :initial-height=\"500\"\n      :min-width=\"400\"\n      :min-height=\"350\"\n      @close=\"togglePanel('scenarioViewer', true)\"\n    >\n      <scenario-viewer \n        :scenario-content=\"scenarioContent\" \n        :scenario-title=\"scenarioTitle\"\n        :file-size=\"scenarioFileSize\"\n      />\n    </floating-panel>\n    \n    <!-- 公告展示面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.announcement\"\n      title=\"公告展示\"\n      :initial-x=\"300\"\n      :initial-y=\"150\"\n      :initial-width=\"700\"\n      :initial-height=\"500\"\n      :min-width=\"400\"\n      :min-height=\"350\"\n      @close=\"togglePanel('announcement', true)\"\n    >\n      <announcement-viewer \n        :content=\"announcementContent\" \n        :title=\"announcementTitle\"\n        :publish-time=\"announcementTime\"\n        :is-editable=\"isRoomOwner\"\n        @save=\"handleAnnouncementSave\"\n      />\n    </floating-panel>\n    \n    <!-- 在模板中添加预览模式提示 -->\n    <div class=\"preview-mode-indicator\" v-if=\"isPreviewMode\">\n      <div class=\"preview-info\">\n        <i class=\"fas fa-eye\"></i>\n        <span>预览模式</span>\n      </div>\n      <div class=\"preview-actions\">\n        <button class=\"exit-preview-btn\" @click=\"exitPreviewMode\">\n          <i class=\"fas fa-times\"></i> 退出预览\n        </button>\n      </div>\n    </div>\n    \n    <!-- 存档选项面板 -->\n    <div class=\"save-options-panel\" v-if=\"showSaveOptions\">\n      <div class=\"save-options-content\">\n        <div class=\"save-option\" @click=\"quickSave\">\n          <i class=\"fas fa-save\"></i>\n          <span>快速存档</span>\n          <small>Ctrl+S</small>\n        </div>\n        <div class=\"save-option\" @click=\"quickLoad\" :class=\"{ 'disabled': !quickSaveId }\">\n          <i class=\"fas fa-folder-open\"></i>\n          <span>快速读档</span>\n          <small>Ctrl+O</small>\n        </div>\n        <div class=\"save-option\" @click=\"toggleGameSaves\">\n          <i class=\"fas fa-history\"></i>\n          <span>存档管理</span>\n          <small>Ctrl+H</small>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ChatBox from '@/components/ChatBox.vue';\nimport Modal from '@/components/Modal.vue';\nimport FloatingPanel from '@/components/FloatingPanel.vue';\nimport FloatingCharacterSheet from '@/components/FloatingCharacterSheet.vue';\nimport FloatingDiceRoller from '@/components/FloatingDiceRoller.vue';\nimport FloatingNotes from '@/components/FloatingNotes.vue';\nimport AIModelSettings from '@/components/AIModelSettings.vue';\nimport MapSystem from '@/components/MapSystem.vue';\nimport ClueBoard from '@/components/ClueBoard.vue';\n\nimport AudioSystem from '@/components/AudioSystem.vue';\nimport VoiceChat from '@/components/VoiceChat.vue';\nimport PrivateChatManager from '@/components/PrivateChatManager.vue';\nimport GroupChat from '@/components/GroupChat.vue';\nimport GameSaveManager from '@/components/GameSaveManager.vue';\nimport ScenarioViewer from '@/components/ScenarioViewer.vue';\nimport AnnouncementViewer from '@/components/AnnouncementViewer.vue';\nimport apiService from '@/services/api';\nimport websocketService from '@/services/websocket';\nimport { storageMixin } from '@/mixins/storageMixin';\n\nexport default {\n  name: 'Room',\n  mixins: [storageMixin],\n  components: {\n    ChatBox,\n    Modal,\n    FloatingPanel,\n    FloatingCharacterSheet,\n    FloatingDiceRoller,\n    FloatingNotes,\n    AIModelSettings,\n    MapSystem,\n    ClueBoard,\n    CombatSystem: () => import('@/components/CombatSystem.vue'),\n    SkillCheckSystem: () => import('@/components/SkillCheckSystem.vue'),\n    EquipmentSystem: () => import('@/components/EquipmentSystem.vue'),\n    ExperiencePackSystem: () => import('@/components/ExperiencePackSystem.vue'),\n    SpellSystem: () => import('@/components/SpellSystem.vue'),\n    MadnessSystem: () => import('@/components/MadnessSystem.vue'),\n    MythosLibrary: () => import('@/components/MythosLibrary.vue'),\n\n    AudioSystem,\n    VoiceChat,\n    PrivateChatManager,\n    GroupChat,\n    GameSaveManager,\n    ScenarioViewer,\n    AnnouncementViewer\n  },\n  props: {\n    // 不再使用props传递roomId\n  },\n  data() {\n    return {\n      room: {},\n      messages: [],\n      characters: [],\n      selectedCharacter: null,\n      websocket: null,\n      showCharacterModal: false,\n      showSettingsModal: false,\n      diceHistory: [],\n      gameNotes: '',\n      onlineUsers: [],\n      isTyping: false,\n      typingUsername: '',\n      typingTimer: null,\n      lastTypingTime: 0,\n      fontSize: 16,\n      playSounds: true,\n      showNotifications: false,\n      isToolbarCollapsed: false,\n      isMobile: false,\n      internalRoomId: '', // 使用内部变量存储房间ID\n      hasUnsavedChanges: false, // 跟踪是否有未保存的更改\n      visiblePanels: {\n        character: false,\n        dice: false,\n        notes: false,\n        aiSettings: false,\n        map: false,\n        clueBoard: false,\n        audioSystem: false,\n        voiceChat: false,\n        privateChat: false, // 私聊面板状态\n        groupChat: false,\n        gameSaves: false,  // 游戏存档面板状态\n        scenarioViewer: false, // 剧本查看器面板状态\n        announcement: false, // 公告展示面板状态\n        combat: false, // 战斗系统面板状态\n        skillCheck: false, // 技能检定系统面板状态\n        equipment: false, // 装备系统面板状态\n        experiencePack: false, // 经历包系统面板状态\n        spells: false, // 法术系统面板状态\n        madness: false, // 疯狂症状系统面板状态\n        library: false // 神话典籍图书馆面板状态\n      },\n      currentAIMode: 'normal', // 将在 mounted 中从存储加载\n\n      // 新功能相关状态\n      skillCheckCharacter: null, // 技能检定选中的角色\n      skillCheckSkill: '', // 技能检定选中的技能\n      pendingSceneDescription: null,\n      // AI组件状态\n      aiStatus: {\n          active: false,\n          busy: false\n      },\n      // 房主状态\n      isRoomOwner: false,\n      // 控制API配置提醒显示\n      showConfigAlert: true,\n      // 角色同步定时器\n      characterSyncTimer: null,\n      // 移除默认场景数据\n      scenes: [],\n      currentSceneIndex: 0,\n      currentCharacterId: null,\n      showNotes: false,\n      // 添加缺失的变量\n      isKP: false,\n      currentUser: null,\n      users: [],\n      isLoading: false,\n      showDiceRoller: false,\n      showCharacterSheet: false,\n      // 添加方法引用\n      rollDice: null,\n      uploadScript: null,\n      quickSaveId: null, // 存储快速存档的ID\n      isPreviewMode: false,\n      tempState: null,\n      windowWidth: window.innerWidth,\n      windowHeight: window.innerHeight,\n      showSaveOptions: false,\n      // 剧本相关数据\n      scenarioContent: '',\n      scenarioTitle: '',\n      scenarioFileSize: '',\n      \n      // 公告相关数据\n      announcementContent: '',\n      announcementTitle: '房间公告',\n      announcementTime: new Date().toLocaleString(),\n      \n      // 房间名称编辑相关\n      isEditingRoomName: false,\n      editableRoomName: '',\n    };\n  },\n  computed: {\n    isDarkMode() {\n      return this.$store.getters.isDarkTheme;\n    },\n    hasQuickSave() {\n      // 检查是否有快速存档\n      return this.quickSaveId !== null;\n    }\n  },\n  async created() {\n    try {\n      // 从路由参数获取房间ID\n      const routeId = this.$route.params.id;\n      \n      console.log(`Room组件创建, 路由参数id = ${routeId}`);\n      \n      // 清除之前的房间数据，确保每次都获取新的房间\n      this.$store.commit('CLEAR_CURRENT_ROOM');\n      \n      // 尝试将路由ID转换为数字\n      const numericId = parseInt(routeId);\n      \n      // 如果转换成功并且是有效数字，使用数字ID；否则使用默认ID 1\n      if (!isNaN(numericId)) {\n        this.internalRoomId = numericId;\n      } else {\n        console.error(`无效的房间ID: ${routeId}，使用默认ID 1`);\n        this.internalRoomId = 1;\n      }\n      \n      console.log(`Room组件创建, 原始roomId = ${routeId}, 处理后roomId = ${this.internalRoomId}`);\n      \n      // 初始化用户数据\n      this.currentUser = this.$store.getters.currentUser || { username: '玩家' };\n      this.isKP = this.currentUser.id === 1; // 假设ID为1的是KP\n      \n      // 初始化窗口尺寸\n      this.updateWindowSize();\n      \n      // 检查移动设备\n      this.checkMobileDevice();\n      \n      // 加载工具栏折叠状态\n      const savedToolbarState = this.safeGetItem(`toolbar_collapsed_${this.internalRoomId}`);\n      if (savedToolbarState !== null) {\n        this.isToolbarCollapsed = savedToolbarState === 'true';\n        console.log('加载工具栏状态:', this.isToolbarCollapsed ? '已折叠' : '已展开');\n      } else {\n        // 默认不折叠\n        this.isToolbarCollapsed = false;\n      }\n      \n      // 首先加载房间数据\n      await this.fetchRoomData();\n      console.log('房间数据加载完成', this.room);\n      \n      // 检查当前用户是否是房主\n      this.checkRoomOwnership();\n      \n      // 强制设置为房主（临时解决方案）\n      this.isRoomOwner = true;\n      \n      // 建立WebSocket连接\n      this.connectWebSocket();\n      \n      // 获取用户角色\n      await this.fetchUserCharacters();\n      console.log('角色数据加载完成', this.characters);\n      \n      // 获取历史消息 (之后获取，因为需要WebSocket连接)\n      setTimeout(() => {\n        this.fetchRoomMessages();\n      }, 1000);\n      \n      // 加载用户设置\n      this.loadUserSettings();\n      \n      // 从本地存储加载公告\n      this.loadAnnouncementFromLocalStorage();\n      \n      // 从本地存储加载房间名称\n      this.loadRoomNameFromLocalStorage();\n      \n      // 默认显示公告面板\n      this.togglePanel('announcement');\n      \n      // 默认显示角色卡和骰子面板\n      // 注意：这里不需要设置默认值，因为在loadUserSettings中已经处理了\n      // 只需要确保同步状态到显示变量\n      this.showCharacterSheet = this.visiblePanels.character;\n      this.showDiceRoller = this.visiblePanels.dice;\n      \n      // 设置延时检查系统状态\n      setTimeout(() => {\n        this.checkSystemStatus();\n      }, 2000);\n      \n      // 检查是否已经关闭过提醒\n      if (this.safeGetItem('aiConfigAlertDismissed') === 'true') {\n        this.showConfigAlert = false;\n      }\n      \n      // 检查API密钥状态\n      this.checkApiKeyStatus();\n      \n      // 同步选中角色的状态\n      this.syncSelectedCharacterStatus();\n      \n      // 设置定时同步角色状态\n      this.setupCharacterSyncTimer();\n      \n      // 初始化方法引用\n      this.rollDice = this.handleRollDice;\n      this.uploadScript = this.handleUploadScript;\n      \n      // 初始化用户列表\n      this.users = this.onlineUsers;\n      \n      // 添加键盘事件监听\n      window.addEventListener('keydown', this.handleKeyDown);\n      \n    } catch (error) {\n      console.error('初始化房间失败', error);\n      if (error.message === \"房间不存在\" || error.response?.data?.detail === \"房间不存在\") {\n        // 如果房间不存在，创建一个新房间\n        console.log('尝试创建房间', this.internalRoomId);\n        const mockRoom = { \n          id: parseInt(this.internalRoomId), \n          name: `测试房间 ${this.internalRoomId}`, \n          description: '这是一个自动创建的房间' \n        };\n        this.$store.commit('SET_CURRENT_ROOM', mockRoom);\n        this.room = mockRoom;\n        // 重新尝试WebSocket连接\n        this.connectWebSocket();\n        \n        // 添加系统消息提示\n        this.showSystemMessage('已自动创建测试房间');\n        \n        // 获取用户角色\n        this.fetchUserCharacters().catch(e => console.error('获取角色失败', e));\n      } else {\n        this.showSystemMessage('加载房间失败，请尝试重新进入');\n        setTimeout(() => this.$router.push('/rooms'), 2000);\n      }\n    }\n  },\n  mounted() {\n    // 注意：房间ID已在created钩子中设置，这里不需要重新设置\n    console.log(`Room组件mounted, 当前房间ID = ${this.internalRoomId}`);\n    \n    // 加载用户设置\n    this.loadUserSettings();\n    \n    // 检查设备类型\n    this.detectDevice();\n    window.addEventListener('resize', this.detectDevice);\n    \n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('mozfullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('MSFullscreenChange', this.handleFullScreenChange);\n    \n    // 尝试预先检测浏览器全屏支持情况\n    const elem = document.querySelector('.room-container');\n    if (!elem.requestFullscreen && \n        !elem.webkitRequestFullscreen && \n        !elem.mozRequestFullScreen &&\n        !elem.msRequestFullscreen) {\n      console.warn('当前浏览器可能不完全支持全屏API');\n    } else {\n      console.log('浏览器支持全屏API');\n    }\n    \n    // 添加键盘快捷键监听\n    window.addEventListener('keydown', this.handleKeyboardShortcuts);\n  },\n  beforeDestroy() {\n    // 移除键盘事件监听\n    window.removeEventListener('keydown', this.handleKeyDown);\n    \n    // 清除角色同步定时器\n    if (this.characterSyncTimer) {\n      clearInterval(this.characterSyncTimer);\n    }\n    \n    // 关闭WebSocket连接\n    if (this.websocket) {\n      this.websocket.close();\n    }\n    \n    // 断开WebSocket连接\n    this.disconnectWebSocket();\n    \n    // 移除事件监听器\n    window.removeEventListener('resize', this.detectDevice);\n    \n    // 移除全屏状态变化监听器\n    document.removeEventListener('fullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('webkitfullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('mozfullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('MSFullscreenChange', this.handleFullScreenChange);\n    \n    // 如果离开页面时处于全屏状态，尝试退出全屏\n    try {\n      const isFullScreen = document.fullscreenElement || \n                         document.webkitFullscreenElement || \n                         document.mozFullScreenElement || \n                         document.msFullscreenElement;\n      if (isFullScreen) {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitExitFullscreen) {\n          document.webkitExitFullscreen();\n        } else if (document.mozCancelFullScreen) {\n          document.mozCancelFullScreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      }\n    } catch (error) {\n      console.error('退出全屏失败:', error);\n    }\n    \n    // 移除键盘快捷键监听\n    window.removeEventListener('keydown', this.handleKeyboardShortcuts);\n  },\n  methods: {\n    async fetchRoomData() {\n      try {\n        const currentRoom = this.$store.getters.currentRoomData;\n        \n        // 确保房间ID是有效的数字\n        const roomId = this.internalRoomId;\n        \n        console.log(`尝试获取房间数据，房间ID: ${roomId}, 当前房间:`, currentRoom);\n        \n        // 如果没有当前房间数据，或者当前房间ID与请求的不一致，则获取新的房间数据\n        if (!currentRoom || parseInt(currentRoom.id) !== parseInt(roomId)) {\n          // 尝试从服务器获取房间信息\n          try {\n            console.log(`当前房间数据不匹配，从服务器获取房间ID: ${roomId}`);\n            \n            // 使用store的joinRoom方法，它已经包含了错误处理逻辑\n            const room = await this.$store.dispatch('joinRoom', { roomId });\n            \n            // 确保房间ID是数字类型\n            room.id = parseInt(room.id);\n            this.room = room;\n            \n            console.log('获取到的房间数据:', room);\n            \n            // 如果房间ID与请求的ID不一致，重定向到正确的房间\n            if (room.id !== parseInt(roomId)) {\n              console.warn(`房间ID不匹配，请求ID: ${roomId}, 返回ID: ${room.id}`);\n              // 更新URL但不重新加载页面\n              window.history.replaceState(null, '', `/room/${room.id}`);\n              this.internalRoomId = room.id;\n            }\n          } catch (error) {\n            console.error('加载房间失败', error);\n            throw error;\n          }\n        } else {\n          console.log('使用当前房间数据:', currentRoom);\n          this.room = currentRoom;\n        }\n        \n        // 模拟在线用户数据\n        this.onlineUsers = [\n          { id: 1, username: 'KP' },\n          { id: 2, username: this.$store.getters.currentUser?.username || '玩家' }\n        ];\n      } catch (error) {\n        console.error('fetchRoomData失败:', error);\n        throw error; // 确保错误被传递出去\n      }\n    },\n    async fetchUserCharacters() {\n      try {\n        await this.$store.dispatch('fetchCharacters');\n        this.characters = this.$store.getters.userCharacters;\n        \n        // 如果有角色，默认选择第一个\n        if (this.characters.length > 0) {\n          this.selectedCharacter = this.characters[0];\n        }\n      } catch (error) {\n        console.error('加载角色失败', error);\n      }\n    },\n    async fetchRoomMessages() {\n      // 这里简化处理，实际应从API获取历史消息\n      // 演示用的假消息\n      this.messages = [\n        {\n          type: 'system',\n          content: '欢迎来到房间',\n          timestamp: Date.now() - 5000\n        },\n        {\n          type: 'chat',\n          user_id: 1,\n          username: 'KP',\n          content: '你们面前是一扇古老的门，上面刻着奇怪的符号...',\n          timestamp: Date.now() - 3000\n        }\n      ];\n    },\n    connectWebSocket() {\n      try {\n        // 获取当前用户ID\n        const userId = this.$store.getters.currentUser?.id || 'guest';\n        \n        // 设置WebSocket事件监听器\n        websocketService.onConnect = () => {\n          console.log('WebSocket连接成功');\n          this.showSystemMessage('已成功连接到房间');\n          \n          // 连接成功后，请求加入房间\n          // 优先使用this.room.id，因为这是已经确认的房间ID\n          const roomId = this.room?.id || this.internalRoomId;\n          \n          console.log(`WebSocket连接成功，加入房间ID: ${roomId}`);\n          \n          websocketService.sendMessage({\n            type: 'join_room',\n            room_id: roomId,\n            user_id: userId,\n            username: this.$store.getters.currentUser?.username || '玩家'\n          });\n        };\n        \n        websocketService.onDisconnect = (reason) => {\n          console.log('WebSocket连接断开:', reason);\n          this.showSystemMessage('与服务器的连接已断开，正在尝试重新连接...');\n        };\n        \n        websocketService.onError = (error) => {\n          console.error('WebSocket错误:', error);\n          this.showSystemMessage('连接发生错误: ' + error.message);\n        };\n        \n        // 设置自定义事件监听器\n        websocketService.setEventListeners({\n          'chat_message': this.handleChatMessage,\n          'user_joined': this.handleUserJoined,\n          'user_left': this.handleUserLeft,\n          'typing': this.handleTypingEvent,\n          'dice_roll': this.handleDiceRoll,\n          'skill_check': this.handleSkillCheck,\n          'character_update': this.handleCharacterUpdate,\n          'announcement_update': this.handleAnnouncementUpdate,\n          'room_name_update': this.handleRoomNameUpdate\n        });\n        \n        // 确保房间ID有效\n        // 优先使用this.room.id，因为这是已经确认的房间ID\n        const roomId = this.room?.id || this.internalRoomId;\n        \n        console.log(`尝试连接WebSocket，房间ID: ${roomId}, 用户ID: ${userId}`);\n        \n        // 连接WebSocket\n        websocketService.connect(roomId, userId);\n        \n        // 添加离开页面前的提示\n        window.addEventListener('beforeunload', this.handleBeforeUnload);\n      } catch (error) {\n        console.error('连接WebSocket失败:', error);\n        this.showSystemMessage('连接服务器失败，请检查网络连接或刷新页面重试');\n        \n        // 在开发环境中，提供更多调试信息\n        if (process.env.NODE_ENV === 'development') {\n          this.showSystemMessage('开发环境提示: 请确保WebSocket服务器已启动，端口为8084');\n          \n          // 5秒后显示模拟连接成功的消息，方便开发调试\n          setTimeout(() => {\n            this.showSystemMessage('开发环境模拟: 已成功连接到房间 (模拟)');\n            \n            // 添加一些模拟的用户\n            this.onlineUsers = [\n              { id: 1, username: 'KP' },\n              { id: 2, username: this.$store.getters.currentUser?.username || '玩家' },\n              { id: 3, username: '模拟玩家1' },\n              { id: 4, username: '模拟玩家2' }\n            ];\n          }, 5000);\n        }\n      }\n    },\n    disconnectWebSocket() {\n      websocketService.disconnect();\n      this.$store.dispatch('leaveRoom');\n    },\n    handleRoll(rollData) {\n        // 添加用户名\n        rollData.username = this.$store.getters.currentUser?.username || '玩家';\n      \n      // 如果没有结果，则在本地生成骰子结果\n      if (!rollData.results || !rollData.total) {\n        // 生成骰子结果\n        const count = parseInt(rollData.count) || 1;\n        const faces = parseInt(rollData.faces) || 100;\n        const modifier = parseInt(rollData.modifier) || 0;\n        \n        const results = [];\n        for (let i = 0; i < count; i++) {\n          results.push(Math.floor(Math.random() * faces) + 1);\n        }\n        \n        const total = results.reduce((sum, val) => sum + val, 0) + modifier;\n        \n        rollData.results = results;\n        rollData.total = total;\n      }\n      \n      // 将骰子结果添加到本地历史记录\n      this.diceHistory.unshift({\n        description: `${rollData.count}D${rollData.faces}${rollData.modifier > 0 ? '+' + rollData.modifier : ''}`,\n        total: rollData.total,\n        results: rollData.results\n      });\n      \n      // 限制历史记录长度\n      if (this.diceHistory.length > 10) {\n        this.diceHistory.pop();\n      }\n      \n      // 创建骰子结果消息\n      const diceMessage = {\n        type: 'system',\n        content: `${rollData.username} 投掷 ${rollData.count}D${rollData.faces}${rollData.modifier > 0 ? '+' + rollData.modifier : ''} = ${rollData.total} [${rollData.results.join(', ')}${rollData.modifier > 0 ? ' + ' + rollData.modifier : ''}]`,\n        timestamp: new Date().toISOString(),\n        roll_data: {\n          count: rollData.count,\n          faces: rollData.faces,\n          modifier: rollData.modifier,\n          results: rollData.results,\n          total: rollData.total\n        }\n      };\n      \n      // 将结果添加到消息列表\n      this.messages.push(diceMessage);\n      \n      // 使用websocketService发送消息\n        websocketService.sendMessage(rollData);\n        \n        if (this.playSounds) {\n          this.playDiceSound();\n      }\n    },\n    handleSkillCheck(skillData) {\n      // 添加必要的信息\n        skillData.type = 'skill-check';\n      if (!skillData.username) {\n        skillData.username = this.$store.getters.currentUser?.username || '玩家';\n      }\n      \n      // 如果有角色信息，添加角色相关数据\n      if (!skillData.character_name && this.selectedCharacter) {\n        skillData.character_name = this.selectedCharacter.name;\n        skillData.character_id = this.selectedCharacter.id;\n      }\n      \n      console.log('Room组件处理技能检定:', skillData);\n      \n      // 使用websocketService发送消息\n        websocketService.sendMessage(skillData);\n      \n      // 播放骰子声音\n      if (this.playSounds) {\n        this.playDiceSound();\n      }\n      \n      // 在本地执行技能检定并显示结果\n      this.performLocalSkillCheck(skillData);\n    },\n    // 在本地执行技能检定并显示结果\n    async performLocalSkillCheck(skillData) {\n      try {\n        // 生成1-100的随机数\n        const diceResult = Math.floor(Math.random() * 100) + 1;\n        const skillValue = skillData.skill_value;\n        \n        // 判定成功等级\n        let success = false;\n        let level = \"失败\";\n        \n        if (diceResult <= skillValue) {\n          success = true;\n          if (diceResult === 1) {\n            level = \"大成功\";\n          } else if (diceResult <= skillValue / 5) {\n            level = \"极难成功\";\n          } else if (diceResult <= skillValue / 2) {\n            level = \"困难成功\";\n          } else {\n            level = \"成功\";\n          }\n        } else {\n          if (diceResult >= 96 && skillValue < 50) {\n            level = \"大失败\";\n          } else if (diceResult === 100) {\n            level = \"大失败\";\n          } else {\n            level = \"失败\";\n          }\n        }\n        \n        // 创建技能检定结果消息\n        const skillName = skillData.skill_name || \"技能\";\n        const characterName = skillData.character_name || this.$store.getters.currentUser?.username || '玩家';\n        \n        const resultMessage = {\n          type: 'system',\n          content: `${characterName} 进行 ${skillName} 检定：掷出了 ${diceResult}/${skillValue}，${level}！`,\n          timestamp: new Date().toISOString(),\n          roll_result: {\n            dice: diceResult,\n            skill: skillValue,\n            success: success,\n            level: level\n          }\n        };\n        \n        // 将结果添加到消息列表\n        this.messages.push(resultMessage);\n        \n      } catch (error) {\n        console.error('本地技能检定失败:', error);\n        this.showSystemMessage('技能检定处理失败');\n      }\n    },\n    sendMessage(message) {\n      let messageData;\n      \n      console.log('Room组件收到消息:', message);\n      \n      // 检查message是否为字符串\n      if (typeof message === 'string') {\n        if (!message.trim()) return;\n        \n        messageData = {\n          type: 'chat',\n          content: message,\n          timestamp: new Date().toISOString(),\n          character_id: this.selectedCharacter ? this.selectedCharacter.id : null,\n          ai_mode: this.currentAIMode\n        };\n      } else {\n        // message已经是一个对象\n        messageData = message;\n        \n        // 确保有时间戳\n        if (!messageData.timestamp) {\n          messageData.timestamp = new Date().toISOString();\n        }\n        \n        // 添加AI模式信息\n        if (!messageData.ai_mode) {\n          messageData.ai_mode = this.currentAIMode;\n        }\n      }\n      \n      console.log('处理后的消息数据:', messageData);\n      \n      // 确保消息有用户名\n      if (messageData.type === 'chat' && !messageData.username) {\n        messageData.username = this.$store.getters.currentUser?.username || '玩家';\n        messageData.user_id = this.$store.getters.currentUser?.id || 2; // 确保用户ID不是1 (KP)\n      }\n      \n      // 过滤心跳消息，不显示在UI中\n      if (messageData.type === 'heartbeat') {\n        console.log('收到心跳消息，不添加到UI:', messageData);\n        websocketService.sendMessage(messageData);\n        return;\n      }\n      \n      // 先将消息添加到UI，确保即使WebSocket发送失败也能看到消息\n      if (messageData.type === 'chat' || messageData.type === 'system') {\n        console.log('直接将消息添加到UI:', messageData);\n        this.messages.push({...messageData});\n        \n        // 检查是非AI生成的玩家消息，并且AI KP处于活动状态，则处理该消息\n        if (messageData.type === 'chat' && \n            !messageData.ai_generated && \n          this.aiStatus.active) {\n          console.log('检测到普通玩家消息，但AI KP功能已移至ChatBox中');\n        }\n      }\n      \n      // 检查WebSocket连接状态\n      if (!websocketService.isConnected || !websocketService.socket) {\n        console.log('WebSocket未连接，尝试重新连接');\n        this.showSystemMessage('连接已断开，正在尝试重新连接...');\n        this.connectWebSocket();\n        \n        // 将消息推入队列等待重连\n        setTimeout(() => {\n          const result = websocketService.sendMessage(messageData);\n          console.log('消息发送结果:', result);\n        }, 1000);\n        return;\n      }\n      \n      console.log('发送消息到WebSocket:', messageData);\n      \n      // 直接发送消息，不依赖WebSocket的readyState属性\n      const sent = websocketService.sendMessage(messageData);\n      console.log('消息发送结果:', sent);\n\n      // 如果消息发送成功，清空输入框\n      if (typeof message === 'string' && sent) {\n        this.messageInput = '';\n      }\n      \n      // 检查是否有待处理的场景描述且玩家回复开始游戏\n      if (this.pendingSceneDescription && \n          messageData.type === 'chat' && \n          messageData.content && \n          typeof messageData.content === 'string' &&\n          !messageData.ai_generated) {\n        \n        // 检查玩家消息是否为开始游戏的确认\n        const startGameKeywords = ['开始游戏', '开始', '准备好了', '开始冒险', '我准备好了', '出发', '是的'];\n        const playerMessage = messageData.content.toLowerCase();\n        \n        if (startGameKeywords.some(keyword => playerMessage.includes(keyword))) {\n          console.log('检测到玩家确认开始游戏');\n          \n          // 发送玩家的确认消息\n          websocketService.sendMessage(messageData);\n          \n          // 稍后发送场景描述\n          setTimeout(() => {\n            console.log('发送延迟的场景描述');\n            const sceneMessage = {\n              type: \"chat\",\n              user_id: 1, // 使用KP的用户ID\n              username: \"KP\",\n              content: this.pendingSceneDescription,\n              ai_generated: true,\n              timestamp: Date.now(),\n              ai_mode: this.currentAIMode\n            };\n            \n            // 保存一个标记，表示这是一个重要的AI消息\n            this.$store.commit('SET_IMPORTANT_MESSAGE', sceneMessage);\n            \n            // 直接添加到UI\n            this.messages.push({...sceneMessage});\n            \n            // 发送消息\n            websocketService.sendMessage(sceneMessage);\n            this.pendingSceneDescription = null; // 清除待处理的场景描述\n          }, 1000);\n        }\n      }\n    },\n    selectCharacter(character) {\n      this.selectedCharacter = character;\n      this.showCharacterModal = false;\n      \n      // 发送系统消息通知角色选择\n      const messageData = {\n        type: 'system',\n        content: `${this.$store.getters.currentUser?.username || '玩家'} 选择了角色: ${character.name}`,\n        timestamp: Date.now()\n      };\n      this.sendMessage(messageData);\n      \n      // 同步选中角色的状态\n      this.syncSelectedCharacterStatus();\n      this.updateCurrentCharacterId(character.id);\n    },\n  \n    // 新增功能\n    playDiceSound() {\n      // 播放骰子声音\n      const audio = new Audio('/sounds/');\n      audio.volume = 0.5;\n      audio.play().catch(err => console.warn('无法播放音效:', err));\n    },\n    sendTypingStatus(isTyping) {\n      // 防止过于频繁发送状态更新\n      const now = Date.now();\n      if (now - this.lastTypingTime < 2000 && isTyping) {\n        return;\n      }\n      \n      // 发送正在输入状态\n      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n        websocketService.sendMessage({\n          type: 'typing',\n          username: this.$store.getters.currentUser?.username || '玩家',\n          isTyping: isTyping\n        });\n        this.lastTypingTime = now;\n      }\n    },\n    toggleDarkMode() {\n      this.$store.dispatch('toggleTheme');\n    },\n    changeFontSize(change) {\n      this.fontSize = Math.max(12, Math.min(24, this.fontSize + change));\n      document.documentElement.style.setProperty('--base-font-size', `${this.fontSize}px`);\n      this.safeSetItem('fontSize', this.fontSize);\n    },\n    saveNotes(notes) {\n      if (notes !== undefined) {\n        this.gameNotes = notes;\n      }\n            this.safeSetItem(`notes_${this.internalRoomId}`, this.gameNotes);\n    },\n    saveSettings() {\n      this.safeSetItem('fontSize', this.fontSize);\n      this.safeSetItem('playSounds', this.playSounds);\n      this.safeSetItem('showNotifications', this.showNotifications);\n      \n      this.showSettingsModal = false;\n    },\n    loadUserSettings() {\n      // 加载AI模式设置\n      this.currentAIMode = this.safeGetItem('ai_mode') || 'normal';\n      \n      // 加载之前保存的笔记\n      this.gameNotes = this.safeGetItem(`notes_${this.internalRoomId}`) || '';\n      \n      // 加载其他设置\n      const savedFontSize = this.safeGetItem('fontSize');\n      if (savedFontSize) {\n        this.fontSize = parseInt(savedFontSize);\n        document.documentElement.style.setProperty('--base-font-size', `${this.fontSize}px`);\n      }\n      \n      const playSounds = this.safeGetItem('playSounds');\n      if (playSounds) {\n        this.playSounds = playSounds === 'true';\n      }\n      \n      const showNotifications = this.safeGetItem('showNotifications');\n      if (showNotifications) {\n        this.showNotifications = showNotifications === 'true';\n      }\n      \n      // 加载面板可见性设置\n      const panelSettings = this.safeGetItem(`panels_${this.internalRoomId}`);\n      if (panelSettings) {\n        try {\n          this.visiblePanels = JSON.parse(panelSettings);\n          console.log('已加载面板设置:', this.visiblePanels);\n        } catch (e) {\n          console.error('解析面板设置失败', e);\n        }\n      } else {\n        // 如果没有保存的设置，设置默认值\n        this.visiblePanels.character = true;\n        this.visiblePanels.dice = true;\n        this.visiblePanels.privateChat = false; // 默认不显示私聊面板\n        console.log('使用默认面板设置');\n      }\n      \n      // 单独检查私聊面板设置\n      const privateChatSetting = this.safeGetItem(`panel_privateChat_${this.internalRoomId}`);\n      if (privateChatSetting) {\n        this.visiblePanels.privateChat = privateChatSetting === 'visible';\n        console.log('已加载私聊面板设置:', this.visiblePanels.privateChat ? '显示' : '隐藏');\n      }\n      \n      // 加载AI状态设置\n      const aiStatusSettings = this.safeGetItem(`ai_status_${this.internalRoomId}`);\n      if (aiStatusSettings) {\n        try {\n          this.aiStatus = JSON.parse(aiStatusSettings);\n        } catch (e) {\n          console.error('解析AI状态设置失败', e);\n        }\n      }\n      \n      // 确保状态与面板可见性一致\n      this.aiStatus.active = this.visiblePanels.ai;\n      \n      // 同步面板状态到其他变量\n      this.showCharacterSheet = this.visiblePanels.character;\n      this.showDiceRoller = this.visiblePanels.dice;\n    },\n    sendDesktopNotification(message) {\n      // 检查浏览器是否支持通知\n      if (\"Notification\" in window) {\n        if (Notification.permission === \"granted\") {\n          new Notification(`${message.username || '用户'} 在 ${this.room.name}`, {\n            body: message.content,\n            icon: '/favicon.ico'\n          });\n        } else if (Notification.permission !== \"denied\") {\n          Notification.requestPermission();\n        }\n      }\n    },\n    // 通用面板切换方法\n    togglePanel(panelName, forceClose = false) {\n      if (forceClose) {\n        this.visiblePanels[panelName] = false;\n      } else {\n        this.visiblePanels[panelName] = !this.visiblePanels[panelName];\n      }\n      \n      // 保存面板状态到本地存储\n      try {\n        // 保存单独的面板状态\n        this.safeSetItem(`panel_${panelName}_${this.internalRoomId}`, \n          this.visiblePanels[panelName] ? 'visible' : 'hidden');\n          \n        // 同时保存整个面板状态对象\n        this.safeSetJSON(`panels_${this.internalRoomId}`, this.visiblePanels);\n        \n        // 同步面板状态到显示变量\n        if (panelName === 'character') {\n          this.showCharacterSheet = this.visiblePanels.character;\n        } else if (panelName === 'dice') {\n          this.showDiceRoller = this.visiblePanels.dice;\n        }\n      } catch (e) {\n        console.warn('无法保存面板状态到本地存储:', e);\n      }\n      \n      console.log(`面板 ${panelName} 状态: ${this.visiblePanels[panelName] ? '显示' : '隐藏'}`);\n    },\n    handleTyping() {\n      // 发送正在输入状态\n      this.sendTypingStatus(true);\n    },\n    handleTypingStopped() {\n      // 发送停止输入状态\n      this.sendTypingStatus(false);\n    },\n    // 添加显示系统消息的方法\n    showSystemMessage(content) {\n      this.$store.dispatch('addMessage', {\n        type: 'system',\n        content: content,\n        timestamp: new Date().toISOString()\n      });\n    },\n    handleSanityUpdate(data) {\n      // 更新角色的理智值\n      if (this.selectedCharacter && this.selectedCharacter.id === data.characterId) {\n        // 创建一个新对象，避免直接修改props\n        this.selectedCharacter = {\n          ...this.selectedCharacter,\n          sanity: data.newSanity\n        };\n        \n        // 发送系统消息通知理智值变化\n        const messageData = {\n          type: 'system',\n          content: `${this.selectedCharacter.name} 的理智值变为 ${data.newSanity}`,\n          timestamp: Date.now()\n        };\n        this.sendMessage(messageData);\n      }\n    },\n\n\n    // 检查当前用户是否是房主\n    checkRoomOwnership() {\n      try {\n        const currentUser = this.$store.getters.currentUser;\n        \n        if (!currentUser) {\n          console.log('未登录用户，默认不是房主');\n          this.isRoomOwner = false;\n          return;\n        }\n        \n        if (!this.room) {\n          console.log('房间数据不存在，无法检查房主状态');\n          this.isRoomOwner = false;\n          return;\n        }\n        \n        // 如果是模拟房间，默认当前用户是房主\n        if (this.room.is_mock) {\n          console.log('模拟房间，当前用户设为房主');\n        this.isRoomOwner = true;\n          return;\n        }\n        \n        // 检查当前用户是否是房间创建者\n        this.isRoomOwner = this.room.keeper_id === currentUser.id;\n        console.log('房主检查结果:', this.isRoomOwner ? '是房主' : '不是房主');\n      } catch (error) {\n        console.error('检查房主状态失败:', error);\n        this.isRoomOwner = false;\n      }\n    },\n    // 检查系统组件状态\n    checkSystemStatus() {\n      console.log('检查系统组件状态...');\n      \n      // 检查WebSocket连接\n      const wsStatus = websocketService.isConnected ? '已连接' : '未连接';\n      console.log(`WebSocket状态: ${wsStatus}`);\n      \n      // 检查服务锁状态\n      console.log('服务锁状态:', websocketService.resourceLocks);\n      \n      // 检查消息队列\n      console.log(`消息队列长度: ${websocketService.messageQueue.length}`);\n      \n      // 检查AI组件状态\n      console.log('AI组件状态:', this.aiStatus);\n      \n      // 检查可见面板状态\n      console.log('可见面板:', this.visiblePanels);\n      \n      if (!websocketService.isConnected) {\n        console.log('WebSocket未连接，尝试重连');\n        this.showSystemMessage('正在重新建立连接...');\n        this.connectWebSocket();\n      }\n      \n      // 确保聊天输入框可用\n      this.ensureChatInputEnabled();\n      \n      // 确认组件正常工作，主动发送一条系统消息\n      this.showSystemMessage('系统检查完成，您可以正常发送消息');\n    },\n    // 确保聊天输入框可用\n    ensureChatInputEnabled() {\n      console.log('确保聊天输入框可用');\n      setTimeout(() => {\n        // 查找所有可能的输入框并确保它们可用\n        const textareas = document.querySelectorAll('textarea');\n        textareas.forEach(textarea => {\n          textarea.disabled = false;\n          console.log('已启用输入框:', textarea);\n        });\n        \n        // 如果有ChatBox引用，尝试直接操作\n        if (this.$refs.chatBox && this.$refs.chatBox.$refs.messageTextarea) {\n          this.$refs.chatBox.$refs.messageTextarea.disabled = false;\n          console.log('已直接启用ChatBox输入框');\n        }\n      }, 500);\n    },\n    // 处理AI设置更新\n    handleAISettingsUpdated(settings) {\n      console.log('AI设置已更新:', settings);\n      this.showSystemMessage('AI模型设置已更新');\n      \n      // 从本地存储加载AI设置并应用\n      try {\n        const parsedSettings = this.safeGetJSON(`aiSettings_${this.roomId}`);\n        if (parsedSettings) {\n          console.log('应用本地AI设置:', parsedSettings);\n          \n          // 如果有AI模式设置，更新当前模式\n          if (parsedSettings.ai_mode) {\n            this.updateAIMode(parsedSettings.ai_mode);\n          }\n        }\n      } catch (error) {\n        console.error('加载本地AI设置失败:', error);\n      }\n    },\n    \n    // 更新AI模式\n    updateAIMode(mode) {\n      if (this.currentAIMode !== mode) {\n        console.log(`AI模式从 ${this.currentAIMode} 切换到 ${mode}`);\n        this.currentAIMode = mode;\n        \n        // 保存到本地存储\n        this.safeSetItem('ai_mode', mode);\n        \n        // 通知WebSocket服务\n        websocketService.setAIMode(mode);\n        \n        // 显示系统消息\n        this.showSystemMessage(`AI模式已切换到: ${mode === 'normal' ? '普通模式' : '剧本模式'}`);\n        \n        // 如果切换到剧本模式，自动打开群聊面板\n        if (mode === 'script' && !this.visiblePanels.groupChat) {\n          setTimeout(() => {\n            this.togglePanel('groupChat', false);\n          }, 500);\n        }\n      }\n    },\n    // 跳转到AI设置\n    goToAISettings() {\n      this.togglePanel('settings');\n      setTimeout(() => {\n        // 聚焦到AI设置选项卡，如果有多个选项卡的话\n        this.$refs.aiSettings && this.$refs.aiSettings.focus();\n      }, 300);\n    },\n    // 关闭配置提醒\n    dismissConfigAlert() {\n      this.showConfigAlert = false;\n      // 可以保存到本地存储，避免重复显示\n      this.safeSetItem('aiConfigAlertDismissed', 'true');\n    },\n    // 检查API密钥状态\n    async checkApiKeyStatus() {\n      try {\n        const response = await apiService.aiSettings.getSettings(this.roomId);\n        if (response.data && response.data.success) {\n          const settings = response.data.settings;\n          \n          // 将设置保存到store\n          this.$store.commit('UPDATE_AI_SETTINGS', {\n            apiKey: settings.api_key,\n            apiUrl: settings.api_url,\n            modelName: settings.model_name\n          });\n          \n          // 如果之前通过测试，则标记为有效\n          const apiTested = this.safeGetItem(`apiTested_${this.roomId}`);\n          if (apiTested === 'true') {\n            this.$store.commit('SET_API_VALID', true);\n            this.showConfigAlert = false;\n          } else {\n            this.$store.commit('SET_API_VALID', false);\n            this.showConfigAlert = true;\n          }\n        }\n      } catch (error) {\n        console.error('检查API密钥状态失败:', error);\n      }\n    },\n    // 同步选中角色的状态\n    syncSelectedCharacterStatus() {\n      if (!this.selectedCharacter || !this.selectedCharacter.id) return;\n      \n      // 请求从服务器同步角色状态\n      this.$store.dispatch('syncCharacterData', this.selectedCharacter.id)\n        .then(() => {\n          console.log('角色状态同步完成:', this.selectedCharacter.name);\n          this.showSystemMessage(`角色 ${this.selectedCharacter.name} 数据已同步`);\n        })\n        .catch(error => {\n          console.error('角色状态同步失败:', error);\n        });\n    },\n    // 处理角色同步事件\n    handleCharacterSync(characterId) {\n      console.log(`收到角色同步事件: ${characterId}`);\n      \n      // 更新角色显示\n      if (this.selectedCharacter && this.selectedCharacter.id === characterId) {\n        // 从store获取最新的角色数据\n        const updatedCharacter = this.$store.getters.userCharacters.find(c => c.id === characterId);\n        if (updatedCharacter) {\n          this.selectedCharacter = updatedCharacter;\n          console.log('角色数据已更新:', updatedCharacter.name);\n        }\n      }\n    },\n    // 设置定时同步角色状态\n    setupCharacterSyncTimer() {\n      // 每隔5分钟同步一次角色状态\n      this.characterSyncTimer = setInterval(() => {\n        if (this.selectedCharacter && this.selectedCharacter.id) {\n          console.log('定时同步角色状态:', this.selectedCharacter.name);\n          this.syncSelectedCharacterStatus();\n        }\n      }, 5 * 60 * 1000); // 5分钟\n    },\n    // 移除场景相关方法\n    \n    handleSceneSave(data) {\n      // 保存场景数据\n      this.scenes[data.sceneIndex] = data.scene;\n      \n      // 发送场景更新消息\n      this.sendSystemMessage(`场景 \"${data.scene.name}\" 已更新`);\n      \n      // 持久化场景数据\n      this.saveScenesToServer();\n    },\n    \n    saveScenesToServer() {\n      // 这里可以添加将场景数据保存到服务器的逻辑\n      // 例如通过WebSocket发送或API调用\n      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n        this.websocket.send(JSON.stringify({\n          type: 'save-scenes',\n          scenes: this.scenes\n        }));\n      }\n    },\n    \n    loadScenesFromServer() {\n      // 从服务器加载场景数据\n      // 这里可以添加API调用或从WebSocket接收场景数据的逻辑\n    },\n    handleNoteSaved(note) {\n      // 可以在这里处理笔记保存事件\n      console.log('笔记已保存:', note);\n    },\n    updateCurrentCharacterId(characterId) {\n      this.currentCharacterId = characterId;\n    },\n    // 切换骰子面板显示状态\n    toggleDiceRoller() {\n      this.visiblePanels.dice = !this.visiblePanels.dice;\n      this.showDiceRoller = this.visiblePanels.dice;\n    },\n    // 切换角色卡面板显示状态\n    toggleCharacterSheet() {\n      this.visiblePanels.character = !this.visiblePanels.character;\n      this.showCharacterSheet = this.visiblePanels.character;\n    },\n    // 切换笔记面板显示状态\n    toggleNotes() {\n      this.showNotes = !this.showNotes;\n    },\n    // 切换地图面板显示状态\n    toggleMap() {\n      this.visiblePanels.map = !this.visiblePanels.map;\n    },\n    // 切换线索墙面板显示状态\n    toggleClueBoard() {\n      this.togglePanel('clueBoard');\n    },\n    // 切换音频系统面板显示状态\n    toggleAudioSystem() {\n      this.togglePanel('audioSystem');\n    },\n    // 切换语音聊天面板显示状态\n    toggleVoiceChat() {\n      this.togglePanel('voiceChat');\n    },\n    // 切换私聊面板显示状态\n    togglePrivateChat() {\n      this.togglePanel('privateChat');\n      console.log('私聊面板状态:', this.visiblePanels.privateChat ? '显示' : '隐藏');\n    },\n    // 切换群聊面板显示状态\n    toggleGroupChat() {\n      this.togglePanel('groupChat');\n    },\n    // 处理语音聊天连接事件\n    handleVoiceConnected() {\n      this.showSystemMessage('您已加入语音聊天');\n    },\n    handleVoiceDisconnected() {\n      this.showSystemMessage('您已离开语音聊天');\n    },\n    // 添加骰子投掷处理方法\n    handleRollDice(diceData) {\n      if (!diceData) return;\n      \n      // 创建骰子消息\n      const diceMessage = {\n        type: 'roll',\n        count: diceData.count || 1,\n        faces: diceData.faces || 100,\n        modifier: diceData.modifier || 0,\n        username: this.currentUser?.username || '玩家',\n        timestamp: new Date().toISOString()\n      };\n      \n      // 发送到WebSocket\n      if (websocketService.isConnected) {\n        websocketService.sendMessage(diceMessage);\n      } else {\n        // 本地模拟骰子结果\n        const results = [];\n        let total = 0;\n        for (let i = 0; i < diceMessage.count; i++) {\n          const result = Math.floor(Math.random() * diceMessage.faces) + 1;\n          results.push(result);\n          total += result;\n        }\n        total += diceMessage.modifier;\n        \n        // 显示本地骰子结果\n        this.showSystemMessage(`${diceMessage.username} 投掷 ${diceMessage.count}D${diceMessage.faces}${diceMessage.modifier > 0 ? '+' + diceMessage.modifier : ''} = ${total} [${results.join(', ')}${diceMessage.modifier > 0 ? ' + ' + diceMessage.modifier : ''}]`);\n      }\n    },\n    // 添加脚本上传处理方法\n    handleUploadScript(file) {\n      if (!file) return;\n      \n      // 显示上传中消息\n      this.showSystemMessage('正在上传剧本文件...');\n      \n      // 模拟上传过程\n      setTimeout(() => {\n        this.showSystemMessage('剧本文件上传成功！');\n      }, 1500);\n      \n      // 实际项目中，这里应该调用API上传文件\n      console.log('上传剧本文件:', file.name);\n    },\n    toggleToolbar() {\n      this.isToolbarCollapsed = !this.isToolbarCollapsed;\n      console.log('工具栏状态切换:', this.isToolbarCollapsed ? '已折叠' : '已展开');\n      \n      // 保存折叠状态到本地存储\n      this.safeSetItem(`toolbar_collapsed_${this.roomId}`, this.isToolbarCollapsed);\n    },\n    checkMobileDevice() {\n      this.isMobile = window.innerWidth < 768;\n      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');\n    },\n    // WebSocket事件处理函数\n    handleChatMessage(message) {\n      console.log('收到聊天消息:', message);\n      this.$store.dispatch('addMessage', message);\n    },\n    \n    handleUserJoined(data) {\n      console.log('用户加入房间:', data);\n      \n      // 添加到在线用户列表\n      if (!this.onlineUsers.find(user => user.id === data.user_id)) {\n        this.onlineUsers.push({\n          id: data.user_id,\n          username: data.username\n        });\n      }\n      \n      // 显示系统消息\n      this.showSystemMessage(`${data.username} 加入了房间`);\n    },\n    \n    handleUserLeft(data) {\n      console.log('用户离开房间:', data);\n      \n      // 从在线用户列表中移除\n      const index = this.onlineUsers.findIndex(user => user.id === data.user_id);\n      if (index !== -1) {\n        this.onlineUsers.splice(index, 1);\n      }\n      \n      // 显示系统消息\n      this.showSystemMessage(`${data.username} 离开了房间`);\n    },\n    \n    handleTypingEvent(data) {\n      if (data.user_id !== this.$store.getters.currentUser?.id) {\n        this.isTyping = data.typing;\n        this.typingUsername = data.username;\n        \n        // 设置超时，如果长时间没有收到新的typing事件，则自动清除\n        clearTimeout(this.typingTimer);\n        if (this.isTyping) {\n          this.typingTimer = setTimeout(() => {\n            this.isTyping = false;\n          }, 5000);\n        }\n      }\n    },\n    \n    handleDiceRoll(message) {\n      console.log('收到骰子消息:', message);\n      \n      // 创建骰子结果消息\n      const diceMessage = {\n        type: 'system',\n        content: `${message.username} 投掷 ${message.count}D${message.faces}${message.modifier > 0 ? '+' + message.modifier : ''} = ${message.total} [${message.results.join(', ')}${message.modifier > 0 ? ' + ' + message.modifier : ''}]`,\n        timestamp: message.timestamp || new Date().toISOString(),\n        roll_data: {\n          count: message.count,\n          faces: message.faces,\n          modifier: message.modifier,\n          results: message.results,\n          total: message.total\n        }\n      };\n      \n      // 添加到消息列表\n      this.$store.dispatch('addMessage', diceMessage);\n      \n      // 添加到骰子历史记录\n      this.diceHistory.push({\n        ...message,\n        timestamp: new Date().toISOString()\n      });\n      \n      // 保持历史记录不超过20条\n      if (this.diceHistory.length > 20) {\n        this.diceHistory.shift();\n      }\n    },\n    \n    // 这里已删除重复的handleSkillCheck和handleCharacterUpdate方法\n    // 检测设备类型\n    detectDevice() {\n      this.isMobile = window.innerWidth < 768;\n      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');\n    },\n    // 处理页面关闭前的操作\n    handleBeforeUnload(event) {\n      this.saveNotes();\n      // 提示用户确认离开\n      if (this.hasUnsavedChanges) {\n        event.preventDefault();\n        event.returnValue = '您有未保存的更改，确定要离开吗？';\n        return event.returnValue;\n      }\n    },\n    \n    // 切换全屏模式\n    toggleFullScreen() {\n      try {\n        // 使用.room-container作为全屏目标元素\n        const elem = document.querySelector('.room-container');\n        if (!elem) {\n          console.error('找不到.room-container元素');\n          this.showSystemMessage('全屏切换失败: 找不到目标元素');\n          return;\n        }\n        \n        console.log('开始全屏操作，目标元素:', elem);\n        \n        // 检查当前是否在全屏模式\n        const isFullScreen = \n          document.fullscreenElement || \n          document.webkitFullscreenElement || \n          document.mozFullScreenElement || \n          document.msFullscreenElement;\n        \n        if (!isFullScreen) {\n          // 进入全屏模式\n          console.log('尝试进入全屏模式');\n          \n          // 保存请求全屏前的状态\n          elem.setAttribute('data-was-fullscreen', 'true');\n          \n          // 为目标元素添加全屏样式\n          elem.classList.add('preparing-fullscreen');\n          \n          // 尝试所有可能的全屏API\n          const requestFullScreen = elem.requestFullscreen || \n                                   elem.webkitRequestFullscreen || \n                                   elem.mozRequestFullScreen || \n                                   elem.msRequestFullscreen;\n                                   \n          if (requestFullScreen) {\n            // 使用apply调用适当的方法\n            requestFullScreen.apply(elem).then(() => {\n              console.log('全屏请求成功');\n              elem.classList.add('in-fullscreen');\n              elem.classList.remove('preparing-fullscreen');\n              this.showSystemMessage('已进入全屏模式，再次双击顶部栏或按ESC可退出');\n            }).catch(err => {\n              console.error('全屏请求被拒绝:', err);\n              elem.classList.remove('preparing-fullscreen');\n              this.showSystemMessage('进入全屏失败: ' + err.message);\n            });\n          } else {\n            // 如果浏览器不支持全屏API，尝试使用CSS模拟全屏\n            console.warn('浏览器不支持全屏API，使用CSS模拟全屏');\n            elem.classList.add('in-fullscreen');\n            elem.classList.remove('preparing-fullscreen');\n            document.body.classList.add('fullscreen-mode');\n            this.showSystemMessage('已使用模拟全屏模式，再次双击顶部栏可退出');\n          }\n        } else {\n          // 退出全屏模式\n          console.log('尝试退出全屏模式');\n          \n          // 尝试所有可能的退出全屏API\n          const exitFullScreen = document.exitFullscreen || \n                               document.webkitExitFullscreen || \n                               document.mozCancelFullScreen || \n                               document.msExitFullscreen;\n                               \n          if (exitFullScreen) {\n            exitFullScreen.apply(document).then(() => {\n              console.log('已退出全屏模式');\n              elem.classList.remove('in-fullscreen');\n              this.showSystemMessage('已退出全屏模式');\n            }).catch(err => {\n              console.error('退出全屏失败:', err);\n              this.showSystemMessage('退出全屏失败: ' + err.message);\n            });\n          } else {\n            // 如果使用CSS模拟全屏，则移除相关样式\n            elem.classList.remove('in-fullscreen');\n            document.body.classList.remove('fullscreen-mode');\n            this.showSystemMessage('已退出全屏模式');\n          }\n        }\n      } catch (error) {\n        console.error('全屏切换失败:', error);\n        this.showSystemMessage('全屏切换失败: ' + error.message);\n        \n        // 移除可能残留的全屏状态\n        try {\n          const elem = document.querySelector('.room-container');\n          if (elem) {\n            elem.classList.remove('preparing-fullscreen');\n            elem.classList.remove('in-fullscreen');\n          }\n          document.body.classList.remove('fullscreen-mode');\n        } catch (e) {\n          console.error('清理全屏状态失败:', e);\n        }\n      }\n    },\n    // 处理全屏状态变化\n    handleFullScreenChange() {\n      try {\n        const isFullScreen = !!document.fullscreenElement || \n                            !!document.webkitFullscreenElement || \n                            !!document.mozFullScreenElement || \n                            !!document.msFullscreenElement;\n        \n        const container = document.querySelector('.room-container');\n        if (!container) return;\n        \n        if (isFullScreen) {\n          console.log('已进入全屏模式');\n          // 确保应用全屏样式\n          document.body.classList.add('fullscreen-mode');\n          container.classList.add('in-fullscreen');\n          \n          // 不显示全屏提示\n        } else {\n          console.log('已退出全屏模式');\n          // 移除全屏样式\n          document.body.classList.remove('fullscreen-mode');\n          container.classList.remove('in-fullscreen');\n          \n          // 移除可能存在的全屏提示\n          const notice = container.querySelector('.fullscreen-notice');\n          if (notice) {\n            notice.parentNode.removeChild(notice);\n          }\n        }\n      } catch (error) {\n        console.error('处理全屏状态变化时出错:', error);\n      }\n    },\n    // 处理剧本模式设置相关方法\n    openChatBoxSettings() {\n      // 直接调用ChatBox组件的方法（通过引用）\n      if (this.$refs.chatBox) {\n        this.$refs.chatBox.showAISettings = true;\n      } else {\n        console.warn('找不到ChatBox组件引用');\n      }\n    },\n    \n    handleMemoryCleared() {\n      this.showSystemMessage('AI记忆已清除，将重新开始对话');\n    },\n    \n    handleScriptSettingsSaved(settings) {\n      this.showSystemMessage('剧本模式设置已保存');\n      \n      // 如果有API密钥，更新到全局设置\n      if (settings.apiKey) {\n        this.$store.commit('UPDATE_AI_SETTINGS', {\n          apiKey: settings.apiKey\n        });\n      }\n    },\n    \n    // 打开剧本查看器\n    openScenarioViewer(content, title, fileSize) {\n      this.scenarioContent = content || '';\n      this.scenarioTitle = title || '未命名剧本';\n      this.scenarioFileSize = fileSize || '';\n      this.togglePanel('scenarioViewer');\n    },\n    \n    handleScriptUploaded(fileInfo) {\n      this.showSystemMessage(`剧本文件 ${fileInfo.fileName} 已上传成功`);\n    },\n    \n    // 添加游戏存档相关方法\n    toggleGameSaves() {\n      this.togglePanel('gameSaves');\n    },\n    \n    // 处理存档创建成功\n    handleSaveCreated(save) {\n      this.showSystemMessage(`存档 \"${save.name}\" 创建成功`);\n    },\n    \n    // 处理存档更新成功\n    handleSaveUpdated(save) {\n      this.showSystemMessage(`存档 \"${save.name}\" 更新成功`);\n    },\n    \n    // 处理存档删除成功\n    handleSaveDeleted() {\n      this.showSystemMessage('存档已成功删除');\n    },\n    \n    // 处理自动存档通知\n    handleSaveAutoCreated(save) {\n      this.showSystemMessage(`自动存档已完成`, 'info', 3000);\n    },\n    \n    // 处理存档消息\n    handleSaveMessage(message) {\n      this.showSystemMessage(message);\n    },\n    \n    // 处理存档版本恢复\n    handleSaveRestored(save) {\n      this.showSystemMessage(`存档已恢复到版本 ${save.version}`);\n      // 重新加载存档内容\n      this.handleSaveLoad(save);\n    },\n    \n    // 处理存档预览\n    handleSavePreview(saveData) {\n      // 这里可以实现预览功能，比如临时显示存档内容\n      this.showSystemMessage('正在预览存档历史版本，加载完成后点击\"恢复\"可应用此版本');\n      \n      // 临时存储当前状态\n      this.tempState = {\n        messages: [...this.messages],\n        characters: [...this.characters],\n        scenes: [...this.scenes],\n        currentSceneIndex: this.currentSceneIndex,\n        diceHistory: [...this.diceHistory]\n      };\n      \n      // 临时加载预览内容\n      this.messages = saveData.save_data.messages || [];\n      this.characters = saveData.save_data.characters || [];\n      this.scenes = saveData.save_data.scenes || [];\n      this.currentSceneIndex = saveData.save_data.currentScene || 0;\n      this.diceHistory = saveData.save_data.diceHistory || [];\n      \n      // 设置预览模式\n      this.isPreviewMode = true;\n    },\n    \n    // 退出预览模式\n    exitPreviewMode() {\n      if (!this.isPreviewMode || !this.tempState) return;\n      \n      // 恢复临时存储的状态\n      this.messages = this.tempState.messages;\n      this.characters = this.tempState.characters;\n      this.scenes = this.tempState.scenes;\n      this.currentSceneIndex = this.tempState.currentSceneIndex;\n      this.diceHistory = this.tempState.diceHistory;\n      \n      // 重置预览模式\n      this.isPreviewMode = false;\n      this.tempState = null;\n      \n      this.showSystemMessage('已退出预览模式');\n    },\n    \n    // 处理存档加载\n    async handleSaveLoad(save) {\n      try {\n        // 使用Vuex加载存档\n        await this.$store.dispatch('loadSave', save);\n        \n        // 更新UI状态\n        this.messages = this.$store.state.messages || [];\n        this.characters = this.$store.state.characters || [];\n        this.scenes = this.$store.state.scenes || [];\n        this.currentSceneIndex = this.$store.state.currentSceneIndex || 0;\n        this.diceHistory = this.$store.state.diceHistory || [];\n        \n        // 关闭存档面板\n        this.togglePanel('gameSaves', true);\n        \n        this.showSystemMessage(`存档 \"${save.name}\" 加载成功`);\n      } catch (error) {\n        console.error('加载存档失败:', error);\n        this.showSystemMessage('加载存档失败');\n      }\n    },\n    \n    // 处理存档错误\n    handleSaveError(errorMessage) {\n      this.showSystemMessage(errorMessage);\n    },\n    \n    // 收集游戏状态用于存档\n    collectGameState(customData) {\n      // 添加额外的状态数据\n      customData.roomName = this.room.name;\n      customData.roomDescription = this.room.description;\n      customData.onlineUsers = this.onlineUsers.map(user => ({\n        id: user.id,\n        username: user.username\n      }));\n      \n      // 添加其他需要保存的状态\n    },\n    \n    // 快速存档功能\n    async quickSave() {\n      try {\n        // 获取当前游戏状态\n        const gameState = this.collectGameStateForQuickSave();\n        \n        const saveData = {\n          name: `快速存档 - ${new Date().toLocaleString('zh-CN')}`,\n          description: '自动创建的快速存档',\n          room_id: parseInt(this.internalRoomId),\n          creator_id: this.$store.getters.currentUser?.id || 1,\n          save_data: gameState,\n          thumbnail: null,\n          is_auto_save: false // 快速存档不是自动存档\n        };\n        \n        let response;\n        \n        if (this.quickSaveId) {\n          // 更新现有的快速存档\n          response = await apiService.gameSaves.updateSave(this.quickSaveId, {\n            name: saveData.name,\n            description: saveData.description,\n            save_data: saveData.save_data\n          });\n        } else {\n          // 创建新的快速存档\n          response = await apiService.gameSaves.createSave(saveData);\n        }\n        \n        if (response.data) {\n          this.quickSaveId = response.data.id;\n          this.showSystemMessage('快速存档成功');\n        }\n      } catch (error) {\n        console.error('快速存档失败:', error);\n        this.showSystemMessage('快速存档失败');\n      }\n    },\n    \n    // 快速读档功能\n    async quickLoad() {\n      if (!this.quickSaveId) {\n        this.showSystemMessage('没有可用的快速存档');\n        return;\n      }\n      \n      try {\n        // 获取快速存档数据\n        const response = await apiService.gameSaves.getSave(this.quickSaveId);\n        \n        if (response.data) {\n          // 使用Vuex加载存档\n          await this.$store.dispatch('loadSave', response.data);\n          \n          // 更新UI状态\n          this.messages = this.$store.state.messages || [];\n          this.characters = this.$store.state.characters || [];\n          this.scenes = this.$store.state.scenes || [];\n          this.currentSceneIndex = this.$store.state.currentSceneIndex || 0;\n          this.diceHistory = this.$store.state.diceHistory || [];\n          \n          this.showSystemMessage('快速读档成功');\n        }\n      } catch (error) {\n        console.error('快速读档失败:', error);\n        this.showSystemMessage('快速读档失败');\n      }\n    },\n    \n    // 收集游戏状态用于快速存档\n    collectGameStateForQuickSave() {\n      // 收集当前游戏状态\n      const gameState = {\n        timestamp: Date.now(),\n        messages: this.messages,\n        characters: this.characters,\n        scenes: this.scenes,\n        currentScene: this.currentSceneIndex,\n        diceHistory: this.diceHistory,\n        notes: this.$store.state.notes || {},\n        clues: this.$store.state.clues || [],\n        aiSettings: this.$store.state.aiSettings || {},\n        customData: {\n          roomName: this.room.name,\n          roomDescription: this.room.description,\n          onlineUsers: this.onlineUsers.map(user => ({\n            id: user.id,\n            username: user.username\n          }))\n        }\n      };\n      \n      return gameState;\n    },\n    // 处理键盘快捷键 - 已移除所有快捷键绑定\n    handleKeyDown(event) {\n      // 所有快捷键绑定已移除\n    },\n    // 处理键盘快捷键 - 已移除所有快捷键绑定\n    handleKeyboardShortcuts(event) {\n      // 所有快捷键绑定已移除\n    },\n    // 更新窗口尺寸\n    updateWindowSize() {\n      this.windowWidth = window.innerWidth;\n      this.windowHeight = window.innerHeight;\n    },\n    \n    // 切换存档选项面板\n    toggleSaveOptions() {\n      this.showSaveOptions = !this.showSaveOptions;\n      \n      // 点击其他区域关闭面板\n      if (this.showSaveOptions) {\n        setTimeout(() => {\n          const closePanel = (e) => {\n            const panel = document.querySelector('.save-options-panel');\n            const button = document.querySelector('.toolbar-btn.active');\n            if (panel && !panel.contains(e.target) && (!button || !button.contains(e.target))) {\n              this.showSaveOptions = false;\n              document.removeEventListener('click', closePanel);\n            }\n          };\n          document.addEventListener('click', closePanel);\n        }, 100);\n      }\n    },\n    // 处理查看剧本事件\n    handleViewScenario(data) {\n      this.openScenarioViewer(data.content, data.title, data.fileSize);\n    },\n    \n    // 打开公告展示面板\n    openAnnouncement() {\n      // 如果没有公告内容且是房主，显示默认编辑提示\n      if (!this.announcementContent && this.isRoomOwner) {\n        this.announcementContent = '在此输入公告内容，玩家可以查看和复制这些信息。\\n\\n可以放置：\\n- 规则提示\\n- 背景设定\\n- 重要信息\\n- 其他需要玩家了解的内容';\n      }\n      \n      this.togglePanel('announcement');\n    },\n    \n    // 处理公告保存\n    handleAnnouncementSave(data) {\n      this.announcementContent = data.content;\n      this.announcementTitle = data.title;\n      this.announcementTime = new Date().toLocaleString();\n      \n      // 保存到本地存储\n      this.saveAnnouncementToLocalStorage();\n      \n      // 使用新的方法发送公告更新消息\n      if (websocketService.isConnected) {\n        websocketService.sendAnnouncementUpdate(this.internalRoomId, {\n          content: data.content,\n          title: data.title,\n          timestamp: data.timestamp\n        });\n      }\n      \n      this.showSystemMessage('公告已更新');\n    },\n    \n    // 保存公告到本地存储\n    saveAnnouncementToLocalStorage() {\n      try {\n        const announcementData = {\n          content: this.announcementContent,\n          title: this.announcementTitle,\n          time: this.announcementTime,\n          roomId: this.internalRoomId\n        };\n        \n        this.safeSetJSON(`room_announcement_${this.internalRoomId}`, announcementData);\n      } catch (error) {\n        console.error('保存公告到本地存储失败:', error);\n      }\n    },\n    \n    // 从本地存储加载公告\n    loadAnnouncementFromLocalStorage() {\n      try {\n        const announcementData = this.safeGetJSON(`room_announcement_${this.internalRoomId}`);\n        if (announcementData) {\n          this.announcementContent = announcementData.content || '';\n          this.announcementTitle = announcementData.title || '房间公告';\n          this.announcementTime = announcementData.time || new Date().toLocaleString();\n        }\n      } catch (error) {\n        console.error('从本地存储加载公告失败:', error);\n      }\n    },\n    \n    // 处理WebSocket接收到的公告更新\n    handleAnnouncementUpdate(data) {\n      // 只有非房主才接收更新，房主是发送方\n      if (!this.isRoomOwner) {\n        this.announcementContent = data.content;\n        this.announcementTitle = data.title;\n        this.announcementTime = new Date(data.timestamp).toLocaleString();\n        \n        // 保存到本地存储\n        this.saveAnnouncementToLocalStorage();\n        \n        // 显示通知\n        this.showSystemMessage('房主更新了公告，点击顶部公告按钮查看');\n      }\n    },\n    \n    // 从本地存储加载房间名称\n    loadRoomNameFromLocalStorage() {\n      try {\n        // 确保房间ID是数字\n        const roomId = parseInt(this.internalRoomId) || this.room.id;\n        \n        console.log('尝试从本地存储加载房间名称:', {\n          roomId: roomId,\n          internalRoomId: this.internalRoomId\n        });\n        \n        const storedName = this.safeGetItem(`room_name_${roomId}`);\n        if (storedName) {\n          console.log('找到存储的房间名称:', storedName);\n          this.room.name = storedName;\n          \n          // 同步更新到store\n          if (this.$store.state.rooms && this.$store.state.rooms.length > 0) {\n            this.$store.commit('UPDATE_ROOM_LIST_NAME', { \n              roomId: roomId, \n              name: storedName \n            });\n          }\n        } else {\n          console.log('未找到存储的房间名称');\n        }\n      } catch (error) {\n        console.error('从本地存储加载房间名称失败:', error);\n      }\n    },\n    \n    // 开始编辑房间名称\n    startEditRoomName() {\n      if (!this.isRoomOwner) return;\n      \n      this.isEditingRoomName = true;\n      this.editableRoomName = this.room.name || '';\n      \n      // 等待DOM更新后聚焦输入框\n      this.$nextTick(() => {\n        if (this.$refs.roomNameInput) {\n          this.$refs.roomNameInput.focus();\n        }\n      });\n    },\n    \n    // 保存房间名称\n    async saveRoomName() {\n      if (!this.isRoomOwner) return;\n      \n      const newName = this.editableRoomName.trim();\n      if (!newName) {\n        this.cancelEditRoomName();\n        return;\n      }\n      \n      // 调试信息\n      console.log('保存房间名称:', {\n        roomId: this.internalRoomId,\n        oldName: this.room.name,\n        newName: newName,\n        roomObject: this.room\n      });\n      \n      // 更新房间名称\n      if (newName !== this.room.name) {\n        // 确保房间ID是数字\n        const roomId = parseInt(this.internalRoomId) || this.room.id;\n        \n        // 使用store的action更新房间名称\n        await this.$store.dispatch('updateRoomName', {\n          roomId: roomId,\n          name: newName\n        });\n        \n        // 如果有WebSocket连接，发送房间名称更新\n        if (websocketService.isConnected) {\n          websocketService.sendMessage({\n            type: 'room_name_update',\n            roomId: roomId,\n            name: newName\n          });\n        }\n        \n        this.showSystemMessage('房间名称已更新');\n      }\n      \n      this.isEditingRoomName = false;\n    },\n    \n    // 取消编辑房间名称\n    cancelEditRoomName() {\n      this.isEditingRoomName = false;\n    },\n    \n    // 处理房间名称更新\n    handleRoomNameUpdate(data) {\n      if (!this.isRoomOwner) {\n        // 确保房间ID是数字\n        const roomId = parseInt(this.internalRoomId) || this.room.id;\n        \n        console.log('接收到房间名称更新:', {\n          roomId: roomId,\n          receivedData: data\n        });\n        \n        // 使用store的action更新房间名称\n        this.$store.dispatch('updateRoomName', {\n          roomId: roomId,\n          name: data.name\n        });\n        this.showSystemMessage('房主更新了房间名称');\n      }\n    },\n\n    // 新功能相关方法\n    toggleCombat() {\n      this.togglePanel('combat');\n    },\n\n    toggleSkillCheck() {\n      this.togglePanel('skillCheck');\n    },\n\n    toggleEquipment() {\n      this.togglePanel('equipment');\n    },\n\n    toggleExperiencePack() {\n      this.togglePanel('experiencePack');\n    },\n\n    toggleSpells() {\n      this.togglePanel('spells');\n    },\n\n    toggleMadness() {\n      this.togglePanel('madness');\n    },\n\n    toggleLibrary() {\n      this.togglePanel('library');\n    },\n\n    // 战斗系统事件处理\n    handleCombatStarted() {\n      this.showSystemMessage('战斗开始！');\n    },\n\n    handleCombatEnded() {\n      this.showSystemMessage('战斗结束！');\n    },\n\n    openSkillCheckForCharacter(character) {\n      this.skillCheckCharacter = character;\n      this.skillCheckSkill = '';\n      this.togglePanel('skillCheck', false);\n    },\n\n    // 技能检定事件处理\n    handleSkillCheckResult(result) {\n      const message = `${result.characterName} 进行 ${result.skillName} 检定: ${result.roll}/${result.targetValue} ${this.getSuccessLevelText(result.successLevel)}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleOpposedCheckResult(result) {\n      const message = `对抗检定: ${result.initiator.name}(${result.initiator.roll}) VS ${result.opponent.name}(${result.opponent.roll}) - 胜者: ${result.winner || '平局'}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleSkillGrowth(data) {\n      const message = `${data.character.name} 的 ${data.skill} 技能成长 +${data.improvement}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleGrowthCheckResult(result) {\n      const message = `${result.character} 进行 ${result.skill} 成长检定: ${result.roll}/${result.currentValue} ${result.success ? '成功' : '失败'}${result.success ? ` +${result.improvement}` : ''}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleSkillCheckCharacterSelected(character) {\n      this.skillCheckCharacter = character;\n    },\n\n    // 装备系统事件处理\n    handleWeaponEquipped(weapon) {\n      this.showSystemMessage(`装备了武器: ${weapon.name}`);\n    },\n\n    handleWeaponUnequipped(weapon) {\n      this.showSystemMessage(`卸下了武器: ${weapon.name}`);\n    },\n\n    handleWeaponUsed(weapon) {\n      this.showSystemMessage(`使用了武器: ${weapon.name}`);\n    },\n\n    handleArmorEquipped(armor) {\n      this.showSystemMessage(`装备了防具: ${armor.name}`);\n    },\n\n    handleArmorUnequipped(armor) {\n      this.showSystemMessage(`卸下了防具: ${armor.name}`);\n    },\n\n    handleItemUsed(item) {\n      this.showSystemMessage(`使用了物品: ${item.name}`);\n    },\n\n    handleItemPurchased(item) {\n      this.showSystemMessage(`购买了物品: ${item.name}`);\n    },\n\n    handleItemDropped(item) {\n      this.showSystemMessage(`丢弃了物品: ${item.name}`);\n    },\n\n    handleHealCharacter(data) {\n      const message = `${data.character.name} 使用 ${data.source} 恢复了 ${data.amount} 点生命值`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleReadBook(data) {\n      const message = `${data.character.name} 阅读了 ${data.book.name}`;\n      this.sendMessage(message, 'system');\n    },\n\n    // 法术系统事件处理\n    handleSpellCast(data) {\n      const message = `${data.caster.name} 施放了 ${data.spell.name}`;\n      this.sendMessage(message, 'system');\n\n      // 如果有目标，添加目标信息\n      if (data.target && data.target !== 'self') {\n        this.sendMessage(`目标: ${data.target}`, 'system');\n      }\n\n      // 如果有描述，添加描述\n      if (data.description) {\n        this.sendMessage(`施法描述: ${data.description}`, 'system');\n      }\n    },\n\n    // 角色更新处理\n    handleCharacterUpdate(character) {\n      // 更新角色数据\n      this.$emit('character-updated', character);\n\n      // 如果是当前选中的角色，更新本地状态\n      if (this.selectedCharacter && this.selectedCharacter.id === character.id) {\n        this.selectedCharacter = character;\n      }\n    },\n\n    // 辅助方法\n    getSuccessLevelText(level) {\n      const levels = {\n        'extreme': '极难成功',\n        'hard': '困难成功',\n        'normal': '常规成功',\n        'failure': '失败'\n      };\n      return levels[level] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n:root {\n  --base-font-size: 16px;\n}\n\n/* 全屏提示样式 */\n.fullscreen-notice {\n  position: fixed;\n  top: 15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 10px 20px;\n  border-radius: 5px;\n  font-size: 14px;\n  z-index: 10000;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);\n  animation: fadeInOut 3s forwards;\n  pointer-events: none;\n}\n\n@keyframes fadeInOut {\n  0% { opacity: 0; }\n  10% { opacity: 1; }\n  80% { opacity: 1; }\n  100% { opacity: 0; }\n}\n\n/* 准备全屏的过渡样式 */\n.preparing-fullscreen {\n  transition: all 0.3s ease-out;\n}\n\n.room-container {\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 80px);\n  padding: 10px;\n  overflow: hidden;\n  font-size: var(--base-font-size);\n}\n\n.room-header {\n  margin-bottom: 10px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background-color: rgba(40, 44, 52, 0.8);\n  border-radius: 6px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  cursor: pointer;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.room-header:hover {\n  background-color: rgba(50, 55, 65, 0.9);\n}\n\n.room-header:hover::after {\n  content: \"\";\n  display: none;\n}\n\n.room-container.in-fullscreen .room-header:hover::after {\n  content: \"\";\n  display: none;\n}\n\n.room-title {\n  display: flex;\n  align-items: center;\n}\n\n.room-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.room-action-btn {\n  padding: 5px 10px;\n  border-radius: 4px;\n  border: none;\n  background-color: #444;\n  color: #e0e0e0;\n  cursor: pointer;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  margin-left: 5px;\n  min-width: 80px;\n  justify-content: center;\n}\n\n.room-action-btn:hover {\n  background-color: #555;\n}\n\n.ai-settings-btn {\n  background-color: #2c3e50;\n}\n\n.ai-settings-btn:hover {\n  background-color: #34495e;\n}\n\n.room-header h2 {\n  margin: 0;\n  color: #e0e0e0;\n  font-size: clamp(1.2rem, 3vw, 1.5rem);\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.room-header h2.editable {\n  cursor: pointer;\n  padding: 2px 6px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.room-header h2.editable:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.room-header h2 .edit-icon {\n  font-size: 0.8em;\n  opacity: 0.6;\n}\n\n.room-header h2.editable:hover .edit-icon {\n  opacity: 1;\n}\n\n.room-name-input {\n  background-color: rgba(0, 0, 0, 0.3);\n  border: 1px solid #555;\n  color: #e0e0e0;\n  font-size: clamp(1.2rem, 3vw, 1.5rem);\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-weight: bold;\n  width: 100%;\n  max-width: 300px;\n}\n\n.room-participants {\n  font-size: 0.8rem;\n  color: #b0b0b0;\n  padding: 3px 6px;\n  background: rgba(255,255,255,0.1);\n  border-radius: 10px;\n}\n\n.room-header p {\n  color: #b0b0b0;\n  margin: 0 0 8px;\n  font-size: 0.9rem;\n}\n\n.room-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.panel-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: auto;\n  min-height: 400px;\n  height: calc(100vh - 220px);\n}\n\n.online-users {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n  margin-bottom: 8px;\n  padding: 6px;\n  background: rgba(255,255,255,0.05);\n  border-radius: 4px;\n}\n\n.user-badge {\n  background: #3498db;\n  color: white;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n}\n\n.character-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  padding: 15px;\n}\n\n.character-item {\n  background: #333333;\n  border: 1px solid #444;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.character-item:hover {\n  background: #3a3a3a;\n  transform: translateY(-2px);\n}\n\n.character-item h4 {\n  margin: 0 0 5px;\n  color: #e0e0e0;\n}\n\n.character-item p {\n  margin: 0;\n  color: #b0b0b0;\n  font-size: 0.9em;\n}\n\n/* 设置模态框样式 */\n.settings-content {\n  padding: 20px;\n}\n\n.setting-group {\n  margin-bottom: 20px;\n}\n\n.setting-group label {\n  display: block;\n  margin-bottom: 10px;\n  color: #e0e0e0;\n  font-weight: 500;\n}\n\n.font-size-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.font-btn {\n  background: #3498db;\n  color: white;\n  border: none;\n  width: 40px;\n  height: 40px;\n  border-radius: 4px;\n  font-weight: bold;\n  font-size: 1.2rem;\n  cursor: pointer;\n}\n\n.current-size {\n  font-weight: 500;\n  color: #e0e0e0;\n}\n\n.toggle-switch {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n}\n\n.toggle-switch input {\n  margin-right: 10px;\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n.toggle-switch label {\n  display: inline;\n  margin: 0;\n  cursor: pointer;\n}\n\n.setting-actions {\n  text-align: right;\n  margin-top: 20px;\n}\n\n.save-settings-btn {\n  background-color: #3498db;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n/* 深色/浅色模式支持 */\n:root {\n  --text-color: #e0e0e0;\n  --bg-color: #2a2a2a;\n  --secondary-bg: #333333;\n  --border-color: #444;\n  --highlight-color: #3498db;\n}\n\n.dark-mode .room-container {\n  --text-color: #e0e0e0;\n  --bg-color: #2a2a2a;\n  --secondary-bg: #333333;\n  --border-color: #444;\n}\n\n/* 媒体查询 - 响应式布局 */\n@media (max-width: 768px) {\n  .room-header h2 {\n    font-size: 1.2rem;\n  }\n  \n  .room-container {\n    padding: 5px;\n    height: calc(100vh - 60px);\n  }\n  \n  .room-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .room-title {\n    margin-bottom: 10px;\n    width: 100%;\n  }\n  \n  .room-actions {\n    width: 100%;\n    justify-content: flex-start;\n  }\n  \n  .control-btn {\n    padding: 5px 6px;\n    font-size: 0.8rem;\n    min-width: 60px;\n  }\n  \n  .room-controls {\n    overflow-x: auto;\n    padding-bottom: 5px;\n    -webkit-overflow-scrolling: touch;\n    scroll-snap-type: x mandatory;\n    white-space: nowrap;\n    width: 100%;\n  }\n  \n  .panel-content {\n    height: calc(100vh - 180px);\n  }\n  \n  .message-input textarea {\n    height: 40px;\n  }\n  \n  .btn-text {\n    display: none;\n  }\n  \n  .toggle-btn {\n    min-width: 40px;\n  }\n  \n  .room-header:hover::after {\n    right: 150px;\n  }\n}\n\n.room-controls {\n  display: flex;\n  gap: 8px;\n  margin-top: 8px;\n  margin-bottom: 10px;\n  flex-wrap: wrap;\n}\n\n.control-btn {\n  background: rgba(255,255,255,0.1);\n  border: none;\n  color: #e0e0e0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  white-space: nowrap;\n  min-width: 80px;\n  justify-content: center;\n}\n\n.control-btn:hover {\n  background: rgba(255,255,255,0.2);\n}\n\n.script-btn {\n  background-color: rgba(142, 68, 173, 0.2);\n  border: 1px solid #8e44ad;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.script-btn:hover {\n  background-color: rgba(142, 68, 173, 0.3);\n}\n\n.script-btn i {\n  font-size: 0.9rem;\n}\n\n.ai-config-alert {\n  background-color: rgba(255, 193, 7, 0.1);\n  border-left: 4px solid #ffc107;\n  margin-bottom: 10px;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.ai-config-alert .alert-content {\n  padding: 10px 15px;\n  position: relative;\n  font-size: 0.9rem;\n}\n\n.ai-config-alert p {\n  margin: 5px 0;\n}\n\n.ai-config-alert .link-button {\n  background: none;\n  border: none;\n  color: #3498db;\n  padding: 0;\n  text-decoration: underline;\n  cursor: pointer;\n}\n\n.ai-config-alert .close-button {\n  position: absolute;\n  top: 5px;\n  right: 5px;\n  background: none;\n  border: none;\n  font-size: 1.2rem;\n  cursor: pointer;\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.ai-config-alert .close-button:hover {\n  opacity: 1;\n}\n\n/* 添加场景相关样式 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.scene-area {\n  flex: 1;\n  min-height: 300px;\n  margin-bottom: 15px;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.message-area {\n  flex: 1;\n  min-height: 300px;\n}\n\n/* 在大屏幕上使用水平布局 */\n@media (min-width: 1024px) {\n  .main-content {\n    flex-direction: row;\n    gap: 15px;\n  }\n  \n  .scene-area {\n    flex: 1;\n    margin-bottom: 0;\n  }\n  \n  .message-area {\n    flex: 1;\n  }\n}\n\n.room-toolbar {\n  position: fixed;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n  background-color: rgba(52, 152, 219, 0.95);\n  border-radius: 0 10px 10px 0;\n  padding: 20px 15px 20px 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(5px);\n  border-right: 5px solid #2ecc71;\n  border-left: none;\n}\n\n/* 修改侧边栏样式 */\n.room-toolbar {\n  background-color: #2980b9;\n  border-radius: 0 10px 10px 0;\n  padding: 15px;\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);\n  border-right: 3px solid #3498db;\n  width: 85px;\n  position: fixed;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  z-index: 9999;\n  transition: all 0.3s ease;\n}\n\n.room-toolbar.collapsed {\n  left: -85px;\n}\n\n.toolbar-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  transition: all 0.3s ease;\n  width: 100%;\n  align-items: center;\n}\n\n/* 工具组样式 */\n.toolbar-group {\n  background-color: #2c3e50;\n  border-radius: 8px;\n  margin-bottom: 10px;\n  padding: 10px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  width: 65px;\n}\n\n.toolbar-group:last-child {\n  border-bottom: none;\n}\n\n.collapse-btn {\n  background: #1a5276;\n  border: 2px solid #ffffff;\n  color: #ffffff;\n  cursor: pointer;\n  font-size: 1.2rem;\n  transition: all 0.3s;\n  position: absolute;\n  right: -30px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 30px;\n  height: 80px;\n  border-radius: 0 5px 5px 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);\n  animation: pulse-left 2s infinite;\n}\n\n@keyframes pulse-left {\n  0% {\n    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);\n  }\n  50% {\n    box-shadow: 3px 0 15px rgba(46, 204, 113, 0.7);\n  }\n  100% {\n    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.collapse-btn:hover {\n  background-color: #2980b9;\n  transform: translateY(-50%) scale(1.1);\n}\n\n.room-toolbar.collapsed .collapse-btn {\n  right: 0;\n  left: auto;\n  border-right: none;\n  border-radius: 0;\n  animation: none;\n}\n\n.toolbar-btn {\n  width: 45px;\n  height: 45px;\n  border-radius: 6px;\n  background-color: #2c3e50;\n  border: none;\n  color: #e0e0e0;\n  font-size: 1.2rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.toolbar-btn:hover {\n  background-color: #2980b9;\n  transform: scale(1.1);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n}\n\n.toolbar-btn.active {\n  background-color: #16a085;\n  box-shadow: 0 0 12px rgba(22, 160, 133, 0.7);\n  border-color: #2ecc71;\n}\n\n.toolbar-btn:active {\n  transform: scale(0.95);\n}\n\n.tooltip {\n  position: absolute;\n  left: 60px;\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  opacity: 0;\n  transition: opacity 0.2s, transform 0.2s;\n  pointer-events: none;\n  white-space: nowrap;\n  transform: translateX(-10px);\n  font-weight: 500;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);\n  z-index: 10001;\n}\n\n.toolbar-btn:hover .tooltip {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .room-toolbar {\n    left: 0;\n    right: 0;\n    top: auto;\n    bottom: 0;\n    transform: none;\n    width: 100%;\n    border-radius: 8px 8px 0 0;\n    padding: 10px;\n    justify-content: center;\n    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);\n    flex-direction: row;\n    border-right: none;\n    border-left: none;\n    border-top: 5px solid #2ecc71;\n  }\n  \n  .room-toolbar.collapsed {\n    bottom: -70px;\n    left: 0;\n    right: 0;\n  }\n  \n  .collapse-btn {\n    top: -30px;\n    left: 50%;\n    right: auto;\n    transform: translateX(-50%);\n    width: 80px;\n    height: 30px;\n    border-radius: 5px 5px 0 0;\n    animation: pulse-mobile 2s infinite;\n  }\n  \n  @keyframes pulse-mobile {\n    0% {\n      box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);\n    }\n    50% {\n      box-shadow: 0 -3px 15px rgba(46, 204, 113, 0.7);\n    }\n    100% {\n      box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);\n    }\n  }\n  \n  .collapse-btn:hover {\n    transform: translateX(-50%) scale(1.1);\n  }\n  \n  .room-toolbar.collapsed .collapse-btn {\n    top: 0;\n    left: 50%;\n    right: auto;\n    animation: none;\n    border-bottom: none;\n    border-right: 2px solid #ffffff;\n  }\n  \n  .toolbar-buttons {\n    flex-direction: row;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n  \n  .toolbar-group {\n    flex-direction: row;\n    padding: 0;\n    border-bottom: none;\n    border-right: 1px solid rgba(255, 255, 255, 0.2);\n    padding-right: 10px;\n    margin-right: 10px;\n    width: auto;\n    gap: 10px;\n  }\n  \n  .toolbar-group:last-child {\n    border-right: none;\n    padding-right: 0;\n    margin-right: 0;\n  }\n  \n  .toolbar-btn {\n    width: 36px;\n    height: 36px;\n    font-size: 1rem;\n    margin: 0;\n  }\n  \n  .tooltip {\n    left: 50%;\n    transform: translateX(-50%);\n    bottom: 45px;\n    top: auto;\n  }\n}\n\n/* 全屏模式样式 */\n.fullscreen-mode .room-container,\n.room-container.in-fullscreen {\n  height: 100vh !important;\n  padding: 10px;\n  box-sizing: border-box;\n  width: 100vw !important;\n  max-width: 100vw !important;\n  overflow: auto;\n  z-index: 9999;\n  background-color: var(--bg-color, #2a2a2a);\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  margin: 0;\n}\n\n.fullscreen-mode .room-header,\n.room-container.in-fullscreen .room-header {\n  background-color: rgba(30, 33, 40, 0.95);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.fullscreen-mode .panel-content,\n.room-container.in-fullscreen .panel-content {\n  height: calc(100vh - 150px);\n}\n\n/* 在全屏模式下特别突出显示双击提示 */\n.room-container.in-fullscreen .room-header:hover::after {\n  content: \"\";\n  display: none;\n}\n\n@keyframes pulse {\n  0% { opacity: 0.8; }\n  50% { opacity: 1; }\n  100% { opacity: 0.8; }\n}\n\n.toggle-btn {\n  min-width: 80px;\n}\n\n.btn-text {\n  margin-left: 5px;\n}\n\n@media (max-width: 768px) {\n  .btn-text {\n    display: none;\n  }\n  \n  .toggle-btn {\n    min-width: 40px;\n  }\n  \n  .room-header:hover::after {\n    right: 150px;\n  }\n}\n\n.control-btn.save-btn {\n  background-color: #4caf50;\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.control-btn.save-btn:hover {\n  background-color: #45a049;\n}\n\n.control-btn.load-btn {\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.control-btn.load-btn:hover {\n  background-color: #0b7dda;\n}\n\n.preview-mode-indicator {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background-color: rgba(76, 175, 80, 0.9);\n  color: white;\n  padding: 8px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  z-index: 1000;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.preview-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: bold;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.exit-preview-btn {\n  background-color: white;\n  color: #4caf50;\n  border: none;\n  border-radius: 4px;\n  padding: 4px 10px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.exit-preview-btn:hover {\n  background-color: #f5f5f5;\n}\n\n.toolbar-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.toolbar-btn {\n  background-color: #333;\n  color: #e0e0e0;\n  border: none;\n  border-radius: 4px;\n  padding: 6px 12px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 0.9rem;\n  transition: background-color 0.2s;\n}\n\n.toolbar-btn:hover {\n  background-color: #444;\n}\n\n.toolbar-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.toolbar-btn i {\n  font-size: 1rem;\n}\n\n/* 修改按钮样式 */\n.toolbar-btn {\n  width: 40px;\n  height: 40px;\n  border-radius: 4px;\n  background-color: #333;\n  border: none;\n  color: #e0e0e0;\n  font-size: 1.1rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s;\n  position: relative;\n  margin: 0;\n}\n\n.toolbar-btn:hover {\n  background-color: #2980b9;\n  transform: scale(1.1);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n}\n\n.toolbar-btn.active {\n  background-color: #16a085;\n  box-shadow: 0 0 12px rgba(22, 160, 133, 0.7);\n  border-color: #2ecc71;\n}\n\n/* 存档选项面板样式 */\n.save-options-panel {\n  position: fixed;\n  left: 95px;\n  top: 50%;\n  transform: translateY(-50%);\n  background-color: #2c3e50;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);\n  z-index: 9998;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 10px;\n  width: 180px;\n}\n\n.save-options-panel::before {\n  content: \"\";\n  position: absolute;\n  left: -10px;\n  top: 50%;\n  transform: translateY(-50%);\n  border-width: 10px 10px 10px 0;\n  border-style: solid;\n  border-color: transparent #2c3e50 transparent transparent;\n}\n\n.save-options-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.save-option {\n  display: flex;\n  align-items: center;\n  padding: 10px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  position: relative;\n}\n\n.save-option:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.save-option i {\n  font-size: 1.2rem;\n  margin-right: 12px;\n  width: 20px;\n  text-align: center;\n}\n\n.save-option span {\n  flex: 1;\n  font-size: 0.95rem;\n}\n\n.save-option small {\n  font-size: 0.75rem;\n  opacity: 0.7;\n  position: absolute;\n  right: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.save-option.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.user-role-indicator {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #e0e0e0;\n  font-size: 0.95rem;\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  background-color: rgba(44, 62, 80, 0.9);\n  padding: 8px 15px;\n  border-radius: 6px;\n  font-weight: 600;\n  z-index: 1000;\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(5px);\n  transition: all 0.3s ease;\n}\n\n.user-role-indicator:hover {\n  background-color: rgba(52, 73, 94, 0.95);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.5);\n}\n\n.user-role-indicator i {\n  font-size: 1.3rem;\n}\n\n.user-role-indicator i.fa-crown {\n  color: #f1c40f;\n  text-shadow: 0 0 5px rgba(241, 196, 15, 0.5);\n}\n\n.user-role-indicator i.fa-user {\n  color: #3498db;\n  text-shadow: 0 0 5px rgba(52, 152, 219, 0.5);\n}\n\n.room-action-btn.announcement-btn {\n  background-color: #4caf50;\n  color: white;\n}\n\n.room-action-btn.announcement-btn:hover {\n  background-color: #388e3c;\n}\n\n.room-action-btn.announcement-btn i {\n  color: white;\n}\n\n\n\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8pBA,OAAOA,OAAM,MAAO,0BAA0B;AAC9C,OAAOC,KAAI,MAAO,wBAAwB;AAC1C,OAAOC,aAAY,MAAO,gCAAgC;AAC1D,OAAOC,sBAAqB,MAAO,yCAAyC;AAC5E,OAAOC,kBAAiB,MAAO,qCAAqC;AACpE,OAAOC,aAAY,MAAO,gCAAgC;AAC1D,OAAOC,eAAc,MAAO,kCAAkC;AAC9D,OAAOC,SAAQ,MAAO,4BAA4B;AAClD,OAAOC,SAAQ,MAAO,4BAA4B;AAElD,OAAOC,WAAU,MAAO,8BAA8B;AACtD,OAAOC,SAAQ,MAAO,4BAA4B;AAClD,OAAOC,kBAAiB,MAAO,qCAAqC;AACpE,OAAOC,SAAQ,MAAO,4BAA4B;AAClD,OAAOC,eAAc,MAAO,kCAAkC;AAC9D,OAAOC,cAAa,MAAO,iCAAiC;AAC5D,OAAOC,kBAAiB,MAAO,qCAAqC;AACpE,OAAOC,UAAS,MAAO,gBAAgB;AACvC,OAAOC,gBAAe,MAAO,sBAAsB;AACnD,SAASC,YAAW,QAAS,uBAAuB;AAEpD,eAAe;EACbC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,CAACF,YAAY,CAAC;EACtBG,UAAU,EAAE;IACVrB,OAAO,EAAPA,OAAO;IACPC,KAAK,EAALA,KAAK;IACLC,aAAa,EAAbA,aAAa;IACbC,sBAAsB,EAAtBA,sBAAsB;IACtBC,kBAAkB,EAAlBA,kBAAkB;IAClBC,aAAa,EAAbA,aAAa;IACbC,eAAe,EAAfA,eAAe;IACfC,SAAS,EAATA,SAAS;IACTC,SAAS,EAATA,SAAS;IACTc,YAAY,EAAE,SAAdA,YAAYA,CAAA;MAAA,OAAQ,MAAM,CAAC,+BAA+B,CAAC;IAAA;IAC3DC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA;MAAA,OAAQ,MAAM,CAAC,mCAAmC,CAAC;IAAA;IACnEC,eAAe,EAAE,SAAjBA,eAAeA,CAAA;MAAA,OAAQ,MAAM,CAAC,kCAAkC,CAAC;IAAA;IACjEC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA;MAAA,OAAQ,MAAM,CAAC,uCAAuC,CAAC;IAAA;IAC3EC,WAAW,EAAE,SAAbA,WAAWA,CAAA;MAAA,OAAQ,MAAM,CAAC,8BAA8B,CAAC;IAAA;IACzDC,aAAa,EAAE,SAAfA,aAAaA,CAAA;MAAA,OAAQ,MAAM,CAAC,gCAAgC,CAAC;IAAA;IAC7DC,aAAa,EAAE,SAAfA,aAAaA,CAAA;MAAA,OAAQ,MAAM,CAAC,gCAAgC,CAAC;IAAA;IAE7DnB,WAAW,EAAXA,WAAW;IACXC,SAAS,EAATA,SAAS;IACTC,kBAAkB,EAAlBA,kBAAkB;IAClBC,SAAS,EAATA,SAAS;IACTC,eAAe,EAAfA,eAAe;IACfC,cAAc,EAAdA,cAAc;IACdC,kBAAiB,EAAjBA;EACF,CAAC;EACDc,KAAK,EAAE;IACL;EAAA,CACD;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,IAAI,EAAE,CAAC,CAAC;MACRC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,iBAAiB,EAAE,IAAI;MACvBC,SAAS,EAAE,IAAI;MACfC,kBAAkB,EAAE,KAAK;MACzBC,iBAAiB,EAAE,KAAK;MACxBC,WAAW,EAAE,EAAE;MACfC,SAAS,EAAE,EAAE;MACbC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,KAAK;MACfC,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,IAAI;MACjBC,cAAc,EAAE,CAAC;MACjBC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,IAAI;MAChBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAE,KAAK;MACzBC,QAAQ,EAAE,KAAK;MACfC,cAAc,EAAE,EAAE;MAAE;MACpBC,iBAAiB,EAAE,KAAK;MAAE;MAC1BC,aAAa,EAAE;QACbC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE,KAAK;QACXC,KAAK,EAAE,KAAK;QACZC,UAAU,EAAE,KAAK;QACjBC,GAAG,EAAE,KAAK;QACVC,SAAS,EAAE,KAAK;QAChBC,WAAW,EAAE,KAAK;QAClBC,SAAS,EAAE,KAAK;QAChBC,WAAW,EAAE,KAAK;QAAE;QACpBC,SAAS,EAAE,KAAK;QAChBC,SAAS,EAAE,KAAK;QAAG;QACnBC,cAAc,EAAE,KAAK;QAAE;QACvBC,YAAY,EAAE,KAAK;QAAE;QACrBC,MAAM,EAAE,KAAK;QAAE;QACfC,UAAU,EAAE,KAAK;QAAE;QACnBC,SAAS,EAAE,KAAK;QAAE;QAClBC,cAAc,EAAE,KAAK;QAAE;QACvBC,MAAM,EAAE,KAAK;QAAE;QACfC,OAAO,EAAE,KAAK;QAAE;QAChBC,OAAO,EAAE,KAAI,CAAE;MACjB,CAAC;MACDC,aAAa,EAAE,QAAQ;MAAE;;MAEzB;MACAC,mBAAmB,EAAE,IAAI;MAAE;MAC3BC,eAAe,EAAE,EAAE;MAAE;MACrBC,uBAAuB,EAAE,IAAI;MAC7B;MACAC,QAAQ,EAAE;QACNC,MAAM,EAAE,KAAK;QACbC,IAAI,EAAE;MACV,CAAC;MACD;MACAC,WAAW,EAAE,KAAK;MAClB;MACAC,eAAe,EAAE,IAAI;MACrB;MACAC,kBAAkB,EAAE,IAAI;MACxB;MACAC,MAAM,EAAE,EAAE;MACVC,iBAAiB,EAAE,CAAC;MACpBC,kBAAkB,EAAE,IAAI;MACxBC,SAAS,EAAE,KAAK;MAChB;MACAC,IAAI,EAAE,KAAK;MACXC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,KAAK;MACrBC,kBAAkB,EAAE,KAAK;MACzB;MACAC,QAAQ,EAAE,IAAI;MACdC,YAAY,EAAE,IAAI;MAClBC,WAAW,EAAE,IAAI;MAAE;MACnBC,aAAa,EAAE,KAAK;MACpBC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAEC,MAAM,CAACC,UAAU;MAC9BC,YAAY,EAAEF,MAAM,CAACG,WAAW;MAChCC,eAAe,EAAE,KAAK;MACtB;MACAC,eAAe,EAAE,EAAE;MACnBC,aAAa,EAAE,EAAE;MACjBC,gBAAgB,EAAE,EAAE;MAEpB;MACAC,mBAAmB,EAAE,EAAE;MACvBC,iBAAiB,EAAE,MAAM;MACzBC,gBAAgB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;MAE7C;MACAC,iBAAiB,EAAE,KAAK;MACxBC,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,MAAM,CAACC,OAAO,CAACC,WAAW;IACxC,CAAC;IACDC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb;MACA,OAAO,IAAI,CAACxB,WAAU,KAAM,IAAI;IAClC;EACF,CAAC;EACKyB,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;MAAA,IAAAC,OAAA,EAAAC,SAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAC,QAAA,EAAAC,EAAA;MAAA,OAAAR,YAAA,GAAAS,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAEZ;YACMT,OAAM,GAAIL,KAAI,CAACe,MAAM,CAACC,MAAM,CAACC,EAAE;YAErCC,OAAO,CAACC,GAAG,+DAAAC,MAAA,CAAuBf,OAAO,CAAE,CAAC;;YAE5C;YACAL,KAAI,CAACL,MAAM,CAAC0B,MAAM,CAAC,oBAAoB,CAAC;;YAExC;YACMf,SAAQ,GAAIgB,QAAQ,CAACjB,OAAO,CAAC,EAEnC;YACA,IAAI,CAACkB,KAAK,CAACjB,SAAS,CAAC,EAAE;cACrBN,KAAI,CAACvE,cAAa,GAAI6E,SAAS;YACjC,OAAO;cACLY,OAAO,CAACM,KAAK,sCAAAJ,MAAA,CAAaf,OAAO,uCAAW,CAAC;cAC7CL,KAAI,CAACvE,cAAa,GAAI,CAAC;YACzB;YAEAyF,OAAO,CAACC,GAAG,uDAAAC,MAAA,CAAyBf,OAAO,mCAAAe,MAAA,CAAiBpB,KAAI,CAACvE,cAAc,CAAE,CAAC;;YAElF;YACAuE,KAAI,CAACjC,WAAU,GAAIiC,KAAI,CAACL,MAAM,CAACC,OAAO,CAAC7B,WAAU,IAAK;cAAE0D,QAAQ,EAAE;YAAK,CAAC;YACxEzB,KAAI,CAAClC,IAAG,GAAIkC,KAAI,CAACjC,WAAW,CAACkD,EAAC,KAAM,CAAC,EAAE;;YAEvC;YACAjB,KAAI,CAAC0B,gBAAgB,CAAC,CAAC;;YAEvB;YACA1B,KAAI,CAAC2B,iBAAiB,CAAC,CAAC;;YAExB;YACMpB,iBAAgB,GAAIP,KAAI,CAAC4B,WAAW,sBAAAR,MAAA,CAAsBpB,KAAI,CAACvE,cAAc,CAAE,CAAC;YACtF,IAAI8E,iBAAgB,KAAM,IAAI,EAAE;cAC9BP,KAAI,CAACzE,kBAAiB,GAAIgF,iBAAgB,KAAM,MAAM;cACtDW,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEnB,KAAI,CAACzE,kBAAiB,GAAI,KAAI,GAAI,KAAK,CAAC;YAClE,OAAO;cACL;cACAyE,KAAI,CAACzE,kBAAiB,GAAI,KAAK;YACjC;;YAEA;YAAAqF,QAAA,CAAAC,CAAA;YAAA,OACMb,KAAI,CAAC6B,aAAa,CAAC,CAAC;UAAA;YAC1BX,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEnB,KAAI,CAAC1F,IAAI,CAAC;;YAElC;YACA0F,KAAI,CAAC8B,kBAAkB,CAAC,CAAC;;YAEzB;YACA9B,KAAI,CAACzC,WAAU,GAAI,IAAI;;YAEvB;YACAyC,KAAI,CAAC+B,gBAAgB,CAAC,CAAC;;YAEvB;YAAAnB,QAAA,CAAAC,CAAA;YAAA,OACMb,KAAI,CAACgC,mBAAmB,CAAC,CAAC;UAAA;YAChCd,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEnB,KAAI,CAACxF,UAAU,CAAC;;YAExC;YACAyH,UAAU,CAAC,YAAM;cACfjC,KAAI,CAACkC,iBAAiB,CAAC,CAAC;YAC1B,CAAC,EAAE,IAAI,CAAC;;YAER;YACAlC,KAAI,CAACmC,gBAAgB,CAAC,CAAC;;YAEvB;YACAnC,KAAI,CAACoC,gCAAgC,CAAC,CAAC;;YAEvC;YACApC,KAAI,CAACqC,4BAA4B,CAAC,CAAC;;YAEnC;YACArC,KAAI,CAACsC,WAAW,CAAC,cAAc,CAAC;;YAEhC;YACA;YACA;YACAtC,KAAI,CAAC7B,kBAAiB,GAAI6B,KAAI,CAACrE,aAAa,CAACC,SAAS;YACtDoE,KAAI,CAAC9B,cAAa,GAAI8B,KAAI,CAACrE,aAAa,CAACE,IAAI;;YAE7C;YACAoG,UAAU,CAAC,YAAM;cACfjC,KAAI,CAACuC,iBAAiB,CAAC,CAAC;YAC1B,CAAC,EAAE,IAAI,CAAC;;YAER;YACA,IAAIvC,KAAI,CAAC4B,WAAW,CAAC,wBAAwB,MAAM,MAAM,EAAE;cACzD5B,KAAI,CAACxC,eAAc,GAAI,KAAK;YAC9B;;YAEA;YACAwC,KAAI,CAACwC,iBAAiB,CAAC,CAAC;;YAExB;YACAxC,KAAI,CAACyC,2BAA2B,CAAC,CAAC;;YAElC;YACAzC,KAAI,CAAC0C,uBAAuB,CAAC,CAAC;;YAE9B;YACA1C,KAAI,CAAC5B,QAAO,GAAI4B,KAAI,CAAC2C,cAAc;YACnC3C,KAAI,CAAC3B,YAAW,GAAI2B,KAAI,CAAC4C,kBAAkB;;YAE3C;YACA5C,KAAI,CAAChC,KAAI,GAAIgC,KAAI,CAACjF,WAAW;;YAE7B;YACA2D,MAAM,CAACmE,gBAAgB,CAAC,SAAS,EAAE7C,KAAI,CAAC8C,aAAa,CAAC;YAAAlC,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAE,CAAA;YAAAJ,EAAA,GAAAE,QAAA,CAAAmC,CAAA;YAGtD7B,OAAO,CAACM,KAAK,CAAC,SAAS,EAAAd,EAAO,CAAC;YAC/B,IAAIA,EAAA,CAAMsC,OAAM,KAAM,OAAM,IAAK,EAAAxC,eAAA,GAAAE,EAAA,CAAMuC,QAAQ,cAAAzC,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgBnG,IAAI,cAAAmG,eAAA,uBAApBA,eAAA,CAAsB0C,MAAK,MAAM,OAAO,EAAE;cACzE;cACAhC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEnB,KAAI,CAACvE,cAAc,CAAC;cACpCgF,QAAO,GAAI;gBACfQ,EAAE,EAAEK,QAAQ,CAACtB,KAAI,CAACvE,cAAc,CAAC;gBACjC/B,IAAI,8BAAA0H,MAAA,CAAUpB,KAAI,CAACvE,cAAc,CAAE;gBACnC0H,WAAW,EAAE;cACf,CAAC;cACDnD,KAAI,CAACL,MAAM,CAAC0B,MAAM,CAAC,kBAAkB,EAAEZ,QAAQ,CAAC;cAChDT,KAAI,CAAC1F,IAAG,GAAImG,QAAQ;cACpB;cACAT,KAAI,CAAC+B,gBAAgB,CAAC,CAAC;;cAEvB;cACA/B,KAAI,CAACoD,iBAAiB,CAAC,WAAW,CAAC;;cAEnC;cACApD,KAAI,CAACgC,mBAAmB,CAAC,CAAC,SAAM,CAAC,UAAAqB,CAAA;gBAAA,OAAKnC,OAAO,CAACM,KAAK,CAAC,QAAQ,EAAE6B,CAAC,CAAC;cAAA,EAAC;YACnE,OAAO;cACLrD,KAAI,CAACoD,iBAAiB,CAAC,gBAAgB,CAAC;cACxCnB,UAAU,CAAC;gBAAA,OAAMjC,KAAI,CAACsD,OAAO,CAACC,IAAI,CAAC,QAAQ,CAAC;cAAA,GAAE,IAAI,CAAC;YACrD;UAAA;YAAA,OAAA3C,QAAA,CAAA4C,CAAA;QAAA;MAAA,GAAApD,OAAA;IAAA;EAEJ,CAAC;EACDqD,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACAvC,OAAO,CAACC,GAAG,0DAAAC,MAAA,CAA4B,IAAI,CAAC3F,cAAc,CAAE,CAAC;;IAE7D;IACA,IAAI,CAAC0G,gBAAgB,CAAC,CAAC;;IAEvB;IACA,IAAI,CAACuB,YAAY,CAAC,CAAC;IACnBhF,MAAM,CAACmE,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACa,YAAY,CAAC;;IAEpD;IACAC,QAAQ,CAACd,gBAAgB,CAAC,kBAAkB,EAAE,IAAI,CAACe,sBAAsB,CAAC;IAC1ED,QAAQ,CAACd,gBAAgB,CAAC,wBAAwB,EAAE,IAAI,CAACe,sBAAsB,CAAC;IAChFD,QAAQ,CAACd,gBAAgB,CAAC,qBAAqB,EAAE,IAAI,CAACe,sBAAsB,CAAC;IAC7ED,QAAQ,CAACd,gBAAgB,CAAC,oBAAoB,EAAE,IAAI,CAACe,sBAAsB,CAAC;;IAE5E;IACA,IAAMC,IAAG,GAAIF,QAAQ,CAACG,aAAa,CAAC,iBAAiB,CAAC;IACtD,IAAI,CAACD,IAAI,CAACE,iBAAgB,IACtB,CAACF,IAAI,CAACG,uBAAsB,IAC5B,CAACH,IAAI,CAACI,oBAAmB,IACzB,CAACJ,IAAI,CAACK,mBAAmB,EAAE;MAC7BhD,OAAO,CAACiD,IAAI,CAAC,mBAAmB,CAAC;IACnC,OAAO;MACLjD,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IAC3B;;IAEA;IACAzC,MAAM,CAACmE,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACuB,uBAAuB,CAAC;EAClE,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd;IACA3F,MAAM,CAAC4F,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACxB,aAAa,CAAC;;IAEzD;IACA,IAAI,IAAI,CAACrF,kBAAkB,EAAE;MAC3B8G,aAAa,CAAC,IAAI,CAAC9G,kBAAkB,CAAC;IACxC;;IAEA;IACA,IAAI,IAAI,CAAC/C,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAAC8J,KAAK,CAAC,CAAC;IACxB;;IAEA;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;;IAE1B;IACA/F,MAAM,CAAC4F,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACZ,YAAY,CAAC;;IAEvD;IACAC,QAAQ,CAACW,mBAAmB,CAAC,kBAAkB,EAAE,IAAI,CAACV,sBAAsB,CAAC;IAC7ED,QAAQ,CAACW,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,CAACV,sBAAsB,CAAC;IACnFD,QAAQ,CAACW,mBAAmB,CAAC,qBAAqB,EAAE,IAAI,CAACV,sBAAsB,CAAC;IAChFD,QAAQ,CAACW,mBAAmB,CAAC,oBAAoB,EAAE,IAAI,CAACV,sBAAsB,CAAC;;IAE/E;IACA,IAAI;MACF,IAAMc,YAAW,GAAIf,QAAQ,CAACgB,iBAAgB,IAC3BhB,QAAQ,CAACiB,uBAAsB,IAC/BjB,QAAQ,CAACkB,oBAAmB,IAC5BlB,QAAQ,CAACmB,mBAAmB;MAC/C,IAAIJ,YAAY,EAAE;QAChB,IAAIf,QAAQ,CAACoB,cAAc,EAAE;UAC3BpB,QAAQ,CAACoB,cAAc,CAAC,CAAC;QAC3B,OAAO,IAAIpB,QAAQ,CAACqB,oBAAoB,EAAE;UACxCrB,QAAQ,CAACqB,oBAAoB,CAAC,CAAC;QACjC,OAAO,IAAIrB,QAAQ,CAACsB,mBAAmB,EAAE;UACvCtB,QAAQ,CAACsB,mBAAmB,CAAC,CAAC;QAChC,OAAO,IAAItB,QAAQ,CAACuB,gBAAgB,EAAE;UACpCvB,QAAQ,CAACuB,gBAAgB,CAAC,CAAC;QAC7B;MACF;IACF,EAAE,OAAO1D,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;;IAEA;IACA9C,MAAM,CAAC4F,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACF,uBAAuB,CAAC;EACrE,CAAC;EACDe,OAAO,EAAE;IACDtD,aAAa,WAAbA,aAAaA,CAAA,EAAG;MAAA,IAAAuD,MAAA;MAAA,OAAAnF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAkF,SAAA;QAAA,IAAAC,qBAAA,EAAAC,WAAA,EAAAC,MAAA,EAAAlL,IAAA,EAAAmL,GAAA,EAAAC,GAAA;QAAA,OAAAxF,YAAA,GAAAS,CAAA,WAAAgF,SAAA;UAAA,kBAAAA,SAAA,CAAA9E,CAAA;YAAA;cAAA8E,SAAA,CAAA7E,CAAA;cAEZyE,WAAU,GAAIH,MAAI,CAACzF,MAAM,CAACC,OAAO,CAACgG,eAAe,EAEvD;cACMJ,MAAK,GAAIJ,MAAI,CAAC3J,cAAc;cAElCyF,OAAO,CAACC,GAAG,0EAAAC,MAAA,CAAmBoE,MAAM,kCAAWD,WAAW,CAAC;;cAE3D;cAAA,MACI,CAACA,WAAU,IAAKjE,QAAQ,CAACiE,WAAW,CAACtE,EAAE,MAAMK,QAAQ,CAACkE,MAAM,CAAC;gBAAAG,SAAA,CAAA9E,CAAA;gBAAA;cAAA;cAAA8E,SAAA,CAAA7E,CAAA;cAG7DI,OAAO,CAACC,GAAG,oHAAAC,MAAA,CAA0BoE,MAAM,CAAE,CAAC;;cAE9C;cAAAG,SAAA,CAAA9E,CAAA;cAAA,OACmBuE,MAAI,CAACzF,MAAM,CAACkG,QAAQ,CAAC,UAAU,EAAE;gBAAEL,MAAK,EAALA;cAAO,CAAC,CAAC;YAAA;cAAzDlL,IAAG,GAAAqL,SAAA,CAAA5C,CAAA;cAET;cACAzI,IAAI,CAAC2G,EAAC,GAAIK,QAAQ,CAAChH,IAAI,CAAC2G,EAAE,CAAC;cAC3BmE,MAAI,CAAC9K,IAAG,GAAIA,IAAI;cAEhB4G,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE7G,IAAI,CAAC;;cAE9B;cACA,IAAIA,IAAI,CAAC2G,EAAC,KAAMK,QAAQ,CAACkE,MAAM,CAAC,EAAE;gBAChCtE,OAAO,CAACiD,IAAI,0DAAA/C,MAAA,CAAkBoE,MAAM,wBAAApE,MAAA,CAAW9G,IAAI,CAAC2G,EAAE,CAAE,CAAC;gBACzD;gBACAvC,MAAM,CAACoH,OAAO,CAACC,YAAY,CAAC,IAAI,EAAE,EAAE,WAAA3E,MAAA,CAAW9G,IAAI,CAAC2G,EAAE,CAAE,CAAC;gBACzDmE,MAAI,CAAC3J,cAAa,GAAInB,IAAI,CAAC2G,EAAE;cAC/B;cAAA0E,SAAA,CAAA9E,CAAA;cAAA;YAAA;cAAA8E,SAAA,CAAA7E,CAAA;cAAA2E,GAAA,GAAAE,SAAA,CAAA5C,CAAA;cAEA7B,OAAO,CAACM,KAAK,CAAC,QAAQ,EAAAiE,GAAO,CAAC;cAAA,MAAAA,GAAA;YAAA;cAAAE,SAAA,CAAA9E,CAAA;cAAA;YAAA;cAIhCK,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoE,WAAW,CAAC;cACrCH,MAAI,CAAC9K,IAAG,GAAIiL,WAAW;YAAA;cAGzB;cACAH,MAAI,CAACrK,WAAU,GAAI,CACjB;gBAAEkG,EAAE,EAAE,CAAC;gBAAEQ,QAAQ,EAAE;cAAK,CAAC,EACzB;gBAAER,EAAE,EAAE,CAAC;gBAAEQ,QAAQ,EAAE,EAAA6D,qBAAA,GAAAF,MAAI,CAACzF,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAuH,qBAAA,uBAA/BA,qBAAA,CAAiC7D,QAAO,KAAK;cAAK,EACtE;cAAAkE,SAAA,CAAA9E,CAAA;cAAA;YAAA;cAAA8E,SAAA,CAAA7E,CAAA;cAAA4E,GAAA,GAAAC,SAAA,CAAA5C,CAAA;cAED7B,OAAO,CAACM,KAAK,CAAC,kBAAkB,EAAAkE,GAAO,CAAC;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAnC,CAAA;UAAA;QAAA,GAAA6B,QAAA;MAAA;IAG5C,CAAC;IACKrD,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MAAA,IAAAgE,MAAA;MAAA,OAAA/F,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA8F,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAAhG,YAAA,GAAAS,CAAA,WAAAwF,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,CAAA;YAAA;cAAAsF,SAAA,CAAArF,CAAA;cAAAqF,SAAA,CAAAtF,CAAA;cAAA,OAElBmF,MAAI,CAACrG,MAAM,CAACkG,QAAQ,CAAC,iBAAiB,CAAC;YAAA;cAC7CG,MAAI,CAACxL,UAAS,GAAIwL,MAAI,CAACrG,MAAM,CAACC,OAAO,CAACwG,cAAc;;cAEpD;cACA,IAAIJ,MAAI,CAACxL,UAAU,CAAC6L,MAAK,GAAI,CAAC,EAAE;gBAC9BL,MAAI,CAACvL,iBAAgB,GAAIuL,MAAI,CAACxL,UAAU,CAAC,CAAC,CAAC;cAC7C;cAAA2L,SAAA,CAAAtF,CAAA;cAAA;YAAA;cAAAsF,SAAA,CAAArF,CAAA;cAAAoF,GAAA,GAAAC,SAAA,CAAApD,CAAA;cAEA7B,OAAO,CAACM,KAAK,CAAC,QAAQ,EAAA0E,GAAO,CAAC;YAAA;cAAA,OAAAC,SAAA,CAAA3C,CAAA;UAAA;QAAA,GAAAyC,QAAA;MAAA;IAElC,CAAC;IACK/D,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAAA,IAAAoE,MAAA;MAAA,OAAArG,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAoG,SAAA;QAAA,OAAArG,YAAA,GAAAS,CAAA,WAAA6F,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,CAAA;YAAA;cACxB;cACA;cACAyF,MAAI,CAAC/L,QAAO,GAAI,CACd;gBACEkM,IAAI,EAAE,QAAQ;gBACdC,OAAO,EAAE,QAAQ;gBACjBC,SAAS,EAAEtH,IAAI,CAACuH,GAAG,CAAC,IAAI;cAC1B,CAAC,EACD;gBACEH,IAAI,EAAE,MAAM;gBACZI,OAAO,EAAE,CAAC;gBACVpF,QAAQ,EAAE,IAAI;gBACdiF,OAAO,EAAE,0BAA0B;gBACnCC,SAAS,EAAEtH,IAAI,CAACuH,GAAG,CAAC,IAAI;cAC1B,EACD;YAAA;cAAA,OAAAJ,SAAA,CAAAhD,CAAA;UAAA;QAAA,GAAA+C,QAAA;MAAA;IACH,CAAC;IACDxE,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MAAA,IAAA+E,MAAA;MACjB,IAAI;QAAA,IAAAC,qBAAA,EAAAC,UAAA;QACF;QACA,IAAMC,MAAK,GAAI,EAAAF,qBAAA,OAAI,CAACpH,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAgJ,qBAAA,uBAA/BA,qBAAA,CAAiC9F,EAAC,KAAK,OAAO;;QAE7D;QACAzH,gBAAgB,CAAC0N,SAAQ,GAAI,YAAM;UAAA,IAAAC,WAAA,EAAAC,qBAAA;UACjClG,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;UAC5B2F,MAAI,CAAC1D,iBAAiB,CAAC,UAAU,CAAC;;UAElC;UACA;UACA,IAAMoC,MAAK,GAAI,EAAA2B,WAAA,GAAAL,MAAI,CAACxM,IAAI,cAAA6M,WAAA,uBAATA,WAAA,CAAWlG,EAAC,KAAK6F,MAAI,CAACrL,cAAc;UAEnDyF,OAAO,CAACC,GAAG,uEAAAC,MAAA,CAA0BoE,MAAM,CAAE,CAAC;UAE9ChM,gBAAgB,CAAC6N,WAAW,CAAC;YAC3BZ,IAAI,EAAE,WAAW;YACjBa,OAAO,EAAE9B,MAAM;YACfqB,OAAO,EAAEI,MAAM;YACfxF,QAAQ,EAAE,EAAA2F,qBAAA,GAAAN,MAAI,CAACnH,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAqJ,qBAAA,uBAA/BA,qBAAA,CAAiC3F,QAAO,KAAK;UACzD,CAAC,CAAC;QACJ,CAAC;QAEDjI,gBAAgB,CAAC+N,YAAW,GAAI,UAACC,MAAM,EAAK;UAC1CtG,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqG,MAAM,CAAC;UACrCV,MAAI,CAAC1D,iBAAiB,CAAC,wBAAwB,CAAC;QAClD,CAAC;QAED5J,gBAAgB,CAACiO,OAAM,GAAI,UAACjG,KAAK,EAAK;UACpCN,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;UACpCsF,MAAI,CAAC1D,iBAAiB,CAAC,UAAS,GAAI5B,KAAK,CAACwB,OAAO,CAAC;QACpD,CAAC;;QAED;QACAxJ,gBAAgB,CAACkO,iBAAiB,CAAC;UACjC,cAAc,EAAE,IAAI,CAACC,iBAAiB;UACtC,aAAa,EAAE,IAAI,CAACC,gBAAgB;UACpC,WAAW,EAAE,IAAI,CAACC,cAAc;UAChC,QAAQ,EAAE,IAAI,CAACC,iBAAiB;UAChC,WAAW,EAAE,IAAI,CAACC,cAAc;UAChC,aAAa,EAAE,IAAI,CAACC,gBAAgB;UACpC,kBAAkB,EAAE,IAAI,CAACC,qBAAqB;UAC9C,qBAAqB,EAAE,IAAI,CAACC,wBAAwB;UACpD,kBAAkB,EAAE,IAAI,CAACC;QAC3B,CAAC,CAAC;;QAEF;QACA;QACA,IAAM3C,MAAK,GAAI,EAAAwB,UAAA,OAAI,CAAC1M,IAAI,cAAA0M,UAAA,uBAATA,UAAA,CAAW/F,EAAC,KAAK,IAAI,CAACxF,cAAc;QAEnDyF,OAAO,CAACC,GAAG,2DAAAC,MAAA,CAAwBoE,MAAM,wBAAApE,MAAA,CAAW6F,MAAM,CAAE,CAAC;;QAE7D;QACAzN,gBAAgB,CAAC4O,OAAO,CAAC5C,MAAM,EAAEyB,MAAM,CAAC;;QAExC;QACAvI,MAAM,CAACmE,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACwF,kBAAkB,CAAC;MAClE,EAAE,OAAO7G,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAAC4B,iBAAiB,CAAC,wBAAwB,CAAC;;QAEhD;QACA,IAAIkF,OAAO,CAACC,GAAG,CAACC,QAAO,KAAM,aAAa,EAAE;UAC1C,IAAI,CAACpF,iBAAiB,CAAC,oCAAoC,CAAC;;UAE5D;UACAnB,UAAU,CAAC,YAAM;YAAA,IAAAwG,sBAAA;YACf3B,MAAI,CAAC1D,iBAAiB,CAAC,uBAAuB,CAAC;;YAE/C;YACA0D,MAAI,CAAC/L,WAAU,GAAI,CACjB;cAAEkG,EAAE,EAAE,CAAC;cAAEQ,QAAQ,EAAE;YAAK,CAAC,EACzB;cAAER,EAAE,EAAE,CAAC;cAAEQ,QAAQ,EAAE,EAAAgH,sBAAA,GAAA3B,MAAI,CAACnH,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAA0K,sBAAA,uBAA/BA,sBAAA,CAAiChH,QAAO,KAAK;YAAK,CAAC,EACtE;cAAER,EAAE,EAAE,CAAC;cAAEQ,QAAQ,EAAE;YAAQ,CAAC,EAC5B;cAAER,EAAE,EAAE,CAAC;cAAEQ,QAAQ,EAAE;YAAQ,EAC5B;UACH,CAAC,EAAE,IAAI,CAAC;QACV;MACF;IACF,CAAC;IACDgD,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpBjL,gBAAgB,CAACkP,UAAU,CAAC,CAAC;MAC7B,IAAI,CAAC/I,MAAM,CAACkG,QAAQ,CAAC,WAAW,CAAC;IACnC,CAAC;IACD8C,UAAU,WAAVA,UAAUA,CAACC,QAAQ,EAAE;MAAA,IAAAC,sBAAA;MACjB;MACAD,QAAQ,CAACnH,QAAO,GAAI,EAAAoH,sBAAA,OAAI,CAAClJ,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAA8K,sBAAA,uBAA/BA,sBAAA,CAAiCpH,QAAO,KAAK,IAAI;;MAEvE;MACA,IAAI,CAACmH,QAAQ,CAACE,OAAM,IAAK,CAACF,QAAQ,CAACG,KAAK,EAAE;QACxC;QACA,IAAMC,KAAI,GAAI1H,QAAQ,CAACsH,QAAQ,CAACI,KAAK,KAAK,CAAC;QAC3C,IAAMC,KAAI,GAAI3H,QAAQ,CAACsH,QAAQ,CAACK,KAAK,KAAK,GAAG;QAC7C,IAAMC,QAAO,GAAI5H,QAAQ,CAACsH,QAAQ,CAACM,QAAQ,KAAK,CAAC;QAEjD,IAAMJ,OAAM,GAAI,EAAE;QAClB,KAAK,IAAIK,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIH,KAAK,EAAEG,CAAC,EAAE,EAAE;UAC9BL,OAAO,CAACvF,IAAI,CAAC6F,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIL,KAAK,IAAI,CAAC,CAAC;QACrD;QAEA,IAAMF,KAAI,GAAID,OAAO,CAACS,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG;UAAA,OAAKD,GAAE,GAAIC,GAAG;QAAA,GAAE,CAAC,IAAIP,QAAQ;QAEnEN,QAAQ,CAACE,OAAM,GAAIA,OAAO;QAC1BF,QAAQ,CAACG,KAAI,GAAIA,KAAK;MACxB;;MAEA;MACA,IAAI,CAAClO,WAAW,CAAC6O,OAAO,CAAC;QACvBvG,WAAW,KAAA/B,MAAA,CAAKwH,QAAQ,CAACI,KAAK,OAAA5H,MAAA,CAAIwH,QAAQ,CAACK,KAAK,EAAA7H,MAAA,CAAGwH,QAAQ,CAACM,QAAO,GAAI,IAAI,GAAE,GAAIN,QAAQ,CAACM,QAAO,GAAI,EAAE,CAAE;QACzGH,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBD,OAAO,EAAEF,QAAQ,CAACE;MACpB,CAAC,CAAC;;MAEF;MACA,IAAI,IAAI,CAACjO,WAAW,CAACwL,MAAK,GAAI,EAAE,EAAE;QAChC,IAAI,CAACxL,WAAW,CAAC8O,GAAG,CAAC,CAAC;MACxB;;MAEA;MACA,IAAMC,WAAU,GAAI;QAClBnD,IAAI,EAAE,QAAQ;QACdC,OAAO,KAAAtF,MAAA,CAAKwH,QAAQ,CAACnH,QAAQ,oBAAAL,MAAA,CAAOwH,QAAQ,CAACI,KAAK,OAAA5H,MAAA,CAAIwH,QAAQ,CAACK,KAAK,EAAA7H,MAAA,CAAGwH,QAAQ,CAACM,QAAO,GAAI,IAAI,GAAE,GAAIN,QAAQ,CAACM,QAAO,GAAI,EAAE,SAAA9H,MAAA,CAAMwH,QAAQ,CAACG,KAAK,QAAA3H,MAAA,CAAKwH,QAAQ,CAACE,OAAO,CAACe,IAAI,CAAC,IAAI,CAAC,EAAAzI,MAAA,CAAGwH,QAAQ,CAACM,QAAO,GAAI,IAAI,KAAI,GAAIN,QAAQ,CAACM,QAAO,GAAI,EAAE,MAAG;QAC3OvC,SAAS,EAAE,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE;UACTf,KAAK,EAAEJ,QAAQ,CAACI,KAAK;UACrBC,KAAK,EAAEL,QAAQ,CAACK,KAAK;UACrBC,QAAQ,EAAEN,QAAQ,CAACM,QAAQ;UAC3BJ,OAAO,EAAEF,QAAQ,CAACE,OAAO;UACzBC,KAAK,EAAEH,QAAQ,CAACG;QAClB;MACF,CAAC;;MAED;MACA,IAAI,CAACxO,QAAQ,CAACgJ,IAAI,CAACqG,WAAW,CAAC;;MAE/B;MACEpQ,gBAAgB,CAAC6N,WAAW,CAACuB,QAAQ,CAAC;MAEtC,IAAI,IAAI,CAACvN,UAAU,EAAE;QACnB,IAAI,CAAC2O,aAAa,CAAC,CAAC;MACxB;IACF,CAAC;IACDhC,gBAAgB,WAAhBA,gBAAgBA,CAACiC,SAAS,EAAE;MAC1B;MACEA,SAAS,CAACxD,IAAG,GAAI,aAAa;MAChC,IAAI,CAACwD,SAAS,CAACxI,QAAQ,EAAE;QAAA,IAAAyI,sBAAA;QACvBD,SAAS,CAACxI,QAAO,GAAI,EAAAyI,sBAAA,OAAI,CAACvK,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAmM,sBAAA,uBAA/BA,sBAAA,CAAiCzI,QAAO,KAAK,IAAI;MACxE;;MAEA;MACA,IAAI,CAACwI,SAAS,CAACE,cAAa,IAAK,IAAI,CAAC1P,iBAAiB,EAAE;QACvDwP,SAAS,CAACE,cAAa,GAAI,IAAI,CAAC1P,iBAAiB,CAACf,IAAI;QACtDuQ,SAAS,CAACG,YAAW,GAAI,IAAI,CAAC3P,iBAAiB,CAACwG,EAAE;MACpD;MAEAC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8I,SAAS,CAAC;;MAEvC;MACEzQ,gBAAgB,CAAC6N,WAAW,CAAC4C,SAAS,CAAC;;MAEzC;MACA,IAAI,IAAI,CAAC5O,UAAU,EAAE;QACnB,IAAI,CAAC2O,aAAa,CAAC,CAAC;MACtB;;MAEA;MACA,IAAI,CAACK,sBAAsB,CAACJ,SAAS,CAAC;IACxC,CAAC;IACD;IACMI,sBAAsB,WAAtBA,sBAAsBA,CAACJ,SAAS,EAAE;MAAA,IAAAK,MAAA;MAAA,OAAArK,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAoK,SAAA;QAAA,IAAAC,qBAAA,EAAAC,UAAA,EAAAC,UAAA,EAAAC,OAAA,EAAAC,KAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,aAAA;QAAA,OAAA7K,YAAA,GAAAS,CAAA,WAAAqK,SAAA;UAAA,kBAAAA,SAAA,CAAAnK,CAAA;YAAA;cACtC,IAAI;gBACF;gBACM4J,UAAS,GAAIrB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;gBAChDoB,UAAS,GAAIT,SAAS,CAACgB,WAAW,EAExC;gBACIN,OAAM,GAAI,KAAK;gBACfC,KAAI,GAAI,IAAI;gBAEhB,IAAIH,UAAS,IAAKC,UAAU,EAAE;kBAC5BC,OAAM,GAAI,IAAI;kBACd,IAAIF,UAAS,KAAM,CAAC,EAAE;oBACpBG,KAAI,GAAI,KAAK;kBACf,OAAO,IAAIH,UAAS,IAAKC,UAAS,GAAI,CAAC,EAAE;oBACvCE,KAAI,GAAI,MAAM;kBAChB,OAAO,IAAIH,UAAS,IAAKC,UAAS,GAAI,CAAC,EAAE;oBACvCE,KAAI,GAAI,MAAM;kBAChB,OAAO;oBACLA,KAAI,GAAI,IAAI;kBACd;gBACF,OAAO;kBACL,IAAIH,UAAS,IAAK,EAAC,IAAKC,UAAS,GAAI,EAAE,EAAE;oBACvCE,KAAI,GAAI,KAAK;kBACf,OAAO,IAAIH,UAAS,KAAM,GAAG,EAAE;oBAC7BG,KAAI,GAAI,KAAK;kBACf,OAAO;oBACLA,KAAI,GAAI,IAAI;kBACd;gBACF;;gBAEA;gBACMC,SAAQ,GAAIZ,SAAS,CAACiB,UAAS,IAAK,IAAI;gBACxCJ,aAAY,GAAIb,SAAS,CAACE,cAAa,MAAAK,qBAAA,GAAKF,MAAI,CAAC3K,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAyM,qBAAA,uBAA/BA,qBAAA,CAAiC/I,QAAO,KAAK,IAAI;gBAE7FsJ,aAAY,GAAI;kBACpBtE,IAAI,EAAE,QAAQ;kBACdC,OAAO,KAAAtF,MAAA,CAAK0J,aAAa,oBAAA1J,MAAA,CAAOyJ,SAAS,4CAAAzJ,MAAA,CAAWqJ,UAAU,OAAArJ,MAAA,CAAIsJ,UAAU,YAAAtJ,MAAA,CAAIwJ,KAAK,WAAG;kBACxFjE,SAAS,EAAE,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC;kBACnCqB,WAAW,EAAE;oBACXtP,IAAI,EAAE4O,UAAU;oBAChBW,KAAK,EAAEV,UAAU;oBACjBC,OAAO,EAAEA,OAAO;oBAChBC,KAAK,EAAEA;kBACT;gBACF,CAAC,EAED;gBACAN,MAAI,CAAC/P,QAAQ,CAACgJ,IAAI,CAACwH,aAAa,CAAC;cAEnC,EAAE,OAAOvJ,KAAK,EAAE;gBACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;gBACjC8I,MAAI,CAAClH,iBAAiB,CAAC,UAAU,CAAC;cACpC;YAAA;cAAA,OAAA4H,SAAA,CAAAxH,CAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA;IACF,CAAC;IACDlD,WAAW,WAAXA,WAAWA,CAACrE,OAAO,EAAE;MAAA,IAAAqI,MAAA;MACnB,IAAIC,WAAW;MAEfpK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE6B,OAAO,CAAC;;MAEnC;MACA,IAAI,OAAOA,OAAM,KAAM,QAAQ,EAAE;QAC/B,IAAI,CAACA,OAAO,CAACuI,IAAI,CAAC,CAAC,EAAE;QAErBD,WAAU,GAAI;UACZ7E,IAAI,EAAE,MAAM;UACZC,OAAO,EAAE1D,OAAO;UAChB2D,SAAS,EAAE,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC;UACnCM,YAAY,EAAE,IAAI,CAAC3P,iBAAgB,GAAI,IAAI,CAACA,iBAAiB,CAACwG,EAAC,GAAI,IAAI;UACvEuK,OAAO,EAAE,IAAI,CAACxO;QAChB,CAAC;MACH,OAAO;QACL;QACAsO,WAAU,GAAItI,OAAO;;QAErB;QACA,IAAI,CAACsI,WAAW,CAAC3E,SAAS,EAAE;UAC1B2E,WAAW,CAAC3E,SAAQ,GAAI,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC;QAClD;;QAEA;QACA,IAAI,CAACwB,WAAW,CAACE,OAAO,EAAE;UACxBF,WAAW,CAACE,OAAM,GAAI,IAAI,CAACxO,aAAa;QAC1C;MACF;MAEAkE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmK,WAAW,CAAC;;MAErC;MACA,IAAIA,WAAW,CAAC7E,IAAG,KAAM,MAAK,IAAK,CAAC6E,WAAW,CAAC7J,QAAQ,EAAE;QAAA,IAAAgK,sBAAA,EAAAC,sBAAA;QACxDJ,WAAW,CAAC7J,QAAO,GAAI,EAAAgK,sBAAA,OAAI,CAAC9L,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAA0N,sBAAA,uBAA/BA,sBAAA,CAAiChK,QAAO,KAAK,IAAI;QACxE6J,WAAW,CAACzE,OAAM,GAAI,EAAA6E,sBAAA,OAAI,CAAC/L,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAA2N,sBAAA,uBAA/BA,sBAAA,CAAiCzK,EAAC,KAAK,CAAC,EAAE;MAClE;;MAEA;MACA,IAAIqK,WAAW,CAAC7E,IAAG,KAAM,WAAW,EAAE;QACpCvF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmK,WAAW,CAAC;QAC1C9R,gBAAgB,CAAC6N,WAAW,CAACiE,WAAW,CAAC;QACzC;MACF;;MAEA;MACA,IAAIA,WAAW,CAAC7E,IAAG,KAAM,MAAK,IAAK6E,WAAW,CAAC7E,IAAG,KAAM,QAAQ,EAAE;QAChEvF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEmK,WAAW,CAAC;QACvC,IAAI,CAAC/Q,QAAQ,CAACgJ,IAAI,CAAAoI,aAAA,KAAKL,WAAW,CAAC,CAAC;;QAEpC;QACA,IAAIA,WAAW,CAAC7E,IAAG,KAAM,MAAK,IAC1B,CAAC6E,WAAW,CAACM,YAAW,IAC1B,IAAI,CAACxO,QAAQ,CAACC,MAAM,EAAE;UACtB6D,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C;MACF;;MAEA;MACA,IAAI,CAAC3H,gBAAgB,CAACqS,WAAU,IAAK,CAACrS,gBAAgB,CAACsS,MAAM,EAAE;QAC7D5K,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClC,IAAI,CAACiC,iBAAiB,CAAC,mBAAmB,CAAC;QAC3C,IAAI,CAACrB,gBAAgB,CAAC,CAAC;;QAEvB;QACAE,UAAU,CAAC,YAAM;UACf,IAAM8J,MAAK,GAAIvS,gBAAgB,CAAC6N,WAAW,CAACiE,WAAW,CAAC;UACxDpK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE4K,MAAM,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC;QACR;MACF;MAEA7K,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEmK,WAAW,CAAC;;MAE3C;MACA,IAAMU,IAAG,GAAIxS,gBAAgB,CAAC6N,WAAW,CAACiE,WAAW,CAAC;MACtDpK,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6K,IAAI,CAAC;;MAE5B;MACA,IAAI,OAAOhJ,OAAM,KAAM,QAAO,IAAKgJ,IAAI,EAAE;QACvC,IAAI,CAACC,YAAW,GAAI,EAAE;MACxB;;MAEA;MACA,IAAI,IAAI,CAAC9O,uBAAsB,IAC3BmO,WAAW,CAAC7E,IAAG,KAAM,MAAK,IAC1B6E,WAAW,CAAC5E,OAAM,IAClB,OAAO4E,WAAW,CAAC5E,OAAM,KAAM,QAAO,IACtC,CAAC4E,WAAW,CAACM,YAAY,EAAE;QAE7B;QACA,IAAMM,iBAAgB,GAAI,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;QAC7E,IAAMC,aAAY,GAAIb,WAAW,CAAC5E,OAAO,CAAC0F,WAAW,CAAC,CAAC;QAEvD,IAAIF,iBAAiB,CAACG,IAAI,CAAC,UAAAC,OAAM;UAAA,OAAKH,aAAa,CAACI,QAAQ,CAACD,OAAO,CAAC;QAAA,EAAC,EAAE;UACtEpL,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;UAE1B;UACA3H,gBAAgB,CAAC6N,WAAW,CAACiE,WAAW,CAAC;;UAEzC;UACArJ,UAAU,CAAC,YAAM;YACff,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;YACxB,IAAMqL,YAAW,GAAI;cACnB/F,IAAI,EAAE,MAAM;cACZI,OAAO,EAAE,CAAC;cAAE;cACZpF,QAAQ,EAAE,IAAI;cACdiF,OAAO,EAAE2E,MAAI,CAAClO,uBAAuB;cACrCyO,YAAY,EAAE,IAAI;cAClBjF,SAAS,EAAEtH,IAAI,CAACuH,GAAG,CAAC,CAAC;cACrB4E,OAAO,EAAEH,MAAI,CAACrO;YAChB,CAAC;;YAED;YACAqO,MAAI,CAAC1L,MAAM,CAAC0B,MAAM,CAAC,uBAAuB,EAAEmL,YAAY,CAAC;;YAEzD;YACAnB,MAAI,CAAC9Q,QAAQ,CAACgJ,IAAI,CAAAoI,aAAA,KAAKa,YAAY,CAAC,CAAC;;YAErC;YACAhT,gBAAgB,CAAC6N,WAAW,CAACmF,YAAY,CAAC;YAC1CnB,MAAI,CAAClO,uBAAsB,GAAI,IAAI,EAAE;UACvC,CAAC,EAAE,IAAI,CAAC;QACV;MACF;IACF,CAAC;IACDsP,eAAe,WAAfA,eAAeA,CAAC7Q,SAAS,EAAE;MAAA,IAAA8Q,sBAAA;MACzB,IAAI,CAACjS,iBAAgB,GAAImB,SAAS;MAClC,IAAI,CAACjB,kBAAiB,GAAI,KAAK;;MAE/B;MACA,IAAM2Q,WAAU,GAAI;QAClB7E,IAAI,EAAE,QAAQ;QACdC,OAAO,KAAAtF,MAAA,CAAK,EAAAsL,sBAAA,OAAI,CAAC/M,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAA2O,sBAAA,uBAA/BA,sBAAA,CAAiCjL,QAAO,KAAK,IAAI,uCAAAL,MAAA,CAAWxF,SAAS,CAAClC,IAAI,CAAE;QACxFiN,SAAS,EAAEtH,IAAI,CAACuH,GAAG,CAAC;MACtB,CAAC;MACD,IAAI,CAACS,WAAW,CAACiE,WAAW,CAAC;;MAE7B;MACA,IAAI,CAAC7I,2BAA2B,CAAC,CAAC;MAClC,IAAI,CAACkK,wBAAwB,CAAC/Q,SAAS,CAACqF,EAAE,CAAC;IAC7C,CAAC;IAED;IACA+I,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd;MACA,IAAM4C,KAAI,GAAI,IAAIC,KAAK,CAAC,UAAU,CAAC;MACnCD,KAAK,CAACE,MAAK,GAAI,GAAG;MAClBF,KAAK,CAACG,IAAI,CAAC,CAAC,SAAM,CAAC,UAAAC,GAAE;QAAA,OAAK9L,OAAO,CAACiD,IAAI,CAAC,SAAS,EAAE6I,GAAG,CAAC;MAAA,EAAC;IACzD,CAAC;IACDC,gBAAgB,WAAhBA,gBAAgBA,CAACjS,QAAQ,EAAE;MACzB;MACA,IAAM4L,GAAE,GAAIvH,IAAI,CAACuH,GAAG,CAAC,CAAC;MACtB,IAAIA,GAAE,GAAI,IAAI,CAACzL,cAAa,GAAI,IAAG,IAAKH,QAAQ,EAAE;QAChD;MACF;;MAEA;MACA,IAAI,IAAI,CAACN,SAAQ,IAAK,IAAI,CAACA,SAAS,CAACwS,UAAS,KAAMC,SAAS,CAACC,IAAI,EAAE;QAAA,IAAAC,sBAAA;QAClE7T,gBAAgB,CAAC6N,WAAW,CAAC;UAC3BZ,IAAI,EAAE,QAAQ;UACdhF,QAAQ,EAAE,EAAA4L,sBAAA,OAAI,CAAC1N,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAsP,sBAAA,uBAA/BA,sBAAA,CAAiC5L,QAAO,KAAK,IAAI;UAC3DzG,QAAQ,EAAEA;QACZ,CAAC,CAAC;QACF,IAAI,CAACG,cAAa,GAAIyL,GAAG;MAC3B;IACF,CAAC;IACD0G,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAI,CAAC3N,MAAM,CAACkG,QAAQ,CAAC,aAAa,CAAC;IACrC,CAAC;IACD0H,cAAc,WAAdA,cAAcA,CAACC,MAAM,EAAE;MACrB,IAAI,CAACpS,QAAO,GAAIgO,IAAI,CAACqE,GAAG,CAAC,EAAE,EAAErE,IAAI,CAACsE,GAAG,CAAC,EAAE,EAAE,IAAI,CAACtS,QAAO,GAAIoS,MAAM,CAAC,CAAC;MAClE7J,QAAQ,CAACgK,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,kBAAkB,KAAAzM,MAAA,CAAK,IAAI,CAAChG,QAAQ,OAAI,CAAC;MACpF,IAAI,CAAC0S,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC1S,QAAQ,CAAC;IAC7C,CAAC;IACD2S,SAAS,WAATA,SAASA,CAACjS,KAAK,EAAE;MACf,IAAIA,KAAI,KAAMkS,SAAS,EAAE;QACvB,IAAI,CAAClT,SAAQ,GAAIgB,KAAK;MACxB;MACM,IAAI,CAACgS,WAAW,UAAA1M,MAAA,CAAU,IAAI,CAAC3F,cAAc,GAAI,IAAI,CAACX,SAAS,CAAC;IACxE,CAAC;IACDmT,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACH,WAAW,CAAC,UAAU,EAAE,IAAI,CAAC1S,QAAQ,CAAC;MAC3C,IAAI,CAAC0S,WAAW,CAAC,YAAY,EAAE,IAAI,CAACzS,UAAU,CAAC;MAC/C,IAAI,CAACyS,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAACxS,iBAAiB,CAAC;MAE7D,IAAI,CAACV,iBAAgB,GAAI,KAAK;IAChC,CAAC;IACDuH,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB;MACA,IAAI,CAACnF,aAAY,GAAI,IAAI,CAAC4E,WAAW,CAAC,SAAS,KAAK,QAAQ;;MAE5D;MACA,IAAI,CAAC9G,SAAQ,GAAI,IAAI,CAAC8G,WAAW,UAAAR,MAAA,CAAU,IAAI,CAAC3F,cAAc,CAAE,KAAK,EAAE;;MAEvE;MACA,IAAMyS,aAAY,GAAI,IAAI,CAACtM,WAAW,CAAC,UAAU,CAAC;MAClD,IAAIsM,aAAa,EAAE;QACjB,IAAI,CAAC9S,QAAO,GAAIkG,QAAQ,CAAC4M,aAAa,CAAC;QACvCvK,QAAQ,CAACgK,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,kBAAkB,KAAAzM,MAAA,CAAK,IAAI,CAAChG,QAAQ,OAAI,CAAC;MACtF;MAEA,IAAMC,UAAS,GAAI,IAAI,CAACuG,WAAW,CAAC,YAAY,CAAC;MACjD,IAAIvG,UAAU,EAAE;QACd,IAAI,CAACA,UAAS,GAAIA,UAAS,KAAM,MAAM;MACzC;MAEA,IAAMC,iBAAgB,GAAI,IAAI,CAACsG,WAAW,CAAC,mBAAmB,CAAC;MAC/D,IAAItG,iBAAiB,EAAE;QACrB,IAAI,CAACA,iBAAgB,GAAIA,iBAAgB,KAAM,MAAM;MACvD;;MAEA;MACA,IAAM6S,aAAY,GAAI,IAAI,CAACvM,WAAW,WAAAR,MAAA,CAAW,IAAI,CAAC3F,cAAc,CAAE,CAAC;MACvE,IAAI0S,aAAa,EAAE;QACjB,IAAI;UACF,IAAI,CAACxS,aAAY,GAAIyS,IAAI,CAACC,KAAK,CAACF,aAAa,CAAC;UAC9CjN,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACxF,aAAa,CAAC;QAC7C,EAAE,OAAO0H,CAAC,EAAE;UACVnC,OAAO,CAACM,KAAK,CAAC,UAAU,EAAE6B,CAAC,CAAC;QAC9B;MACF,OAAO;QACL;QACA,IAAI,CAAC1H,aAAa,CAACC,SAAQ,GAAI,IAAI;QACnC,IAAI,CAACD,aAAa,CAACE,IAAG,GAAI,IAAI;QAC9B,IAAI,CAACF,aAAa,CAACS,WAAU,GAAI,KAAK,EAAE;QACxC8E,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;MACzB;;MAEA;MACA,IAAMmN,kBAAiB,GAAI,IAAI,CAAC1M,WAAW,sBAAAR,MAAA,CAAsB,IAAI,CAAC3F,cAAc,CAAE,CAAC;MACvF,IAAI6S,kBAAkB,EAAE;QACtB,IAAI,CAAC3S,aAAa,CAACS,WAAU,GAAIkS,kBAAiB,KAAM,SAAS;QACjEpN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACxF,aAAa,CAACS,WAAU,GAAI,IAAG,GAAI,IAAI,CAAC;MACzE;;MAEA;MACA,IAAMmS,gBAAe,GAAI,IAAI,CAAC3M,WAAW,cAAAR,MAAA,CAAc,IAAI,CAAC3F,cAAc,CAAE,CAAC;MAC7E,IAAI8S,gBAAgB,EAAE;QACpB,IAAI;UACF,IAAI,CAACnR,QAAO,GAAIgR,IAAI,CAACC,KAAK,CAACE,gBAAgB,CAAC;QAC9C,EAAE,OAAOlL,CAAC,EAAE;UACVnC,OAAO,CAACM,KAAK,CAAC,YAAY,EAAE6B,CAAC,CAAC;QAChC;MACF;;MAEA;MACA,IAAI,CAACjG,QAAQ,CAACC,MAAK,GAAI,IAAI,CAAC1B,aAAa,CAAC6S,EAAE;;MAE5C;MACA,IAAI,CAACrQ,kBAAiB,GAAI,IAAI,CAACxC,aAAa,CAACC,SAAS;MACtD,IAAI,CAACsC,cAAa,GAAI,IAAI,CAACvC,aAAa,CAACE,IAAI;IAC/C,CAAC;IACD4S,uBAAuB,WAAvBA,uBAAuBA,CAACzL,OAAO,EAAE;MAC/B;MACA,IAAI,cAAa,IAAKtE,MAAM,EAAE;QAC5B,IAAIgQ,YAAY,CAACC,UAAS,KAAM,SAAS,EAAE;UACzC,IAAID,YAAY,IAAAtN,MAAA,CAAI4B,OAAO,CAACvB,QAAO,IAAK,IAAI,cAAAL,MAAA,CAAM,IAAI,CAAC9G,IAAI,CAACZ,IAAI,GAAI;YAClEkV,IAAI,EAAE5L,OAAO,CAAC0D,OAAO;YACrBmI,IAAI,EAAE;UACR,CAAC,CAAC;QACJ,OAAO,IAAIH,YAAY,CAACC,UAAS,KAAM,QAAQ,EAAE;UAC/CD,YAAY,CAACI,iBAAiB,CAAC,CAAC;QAClC;MACF;IACF,CAAC;IACD;IACAxM,WAAW,WAAXA,WAAWA,CAACyM,SAAS,EAAsB;MAAA,IAApBC,UAAS,GAAAC,SAAA,CAAA5I,MAAA,QAAA4I,SAAA,QAAAjB,SAAA,GAAAiB,SAAA,MAAI,KAAK;MACvC,IAAID,UAAU,EAAE;QACd,IAAI,CAACrT,aAAa,CAACoT,SAAS,IAAI,KAAK;MACvC,OAAO;QACL,IAAI,CAACpT,aAAa,CAACoT,SAAS,IAAI,CAAC,IAAI,CAACpT,aAAa,CAACoT,SAAS,CAAC;MAChE;;MAEA;MACA,IAAI;QACF;QACA,IAAI,CAACjB,WAAW,UAAA1M,MAAA,CAAU2N,SAAS,OAAA3N,MAAA,CAAI,IAAI,CAAC3F,cAAc,GACxD,IAAI,CAACE,aAAa,CAACoT,SAAS,IAAI,SAAQ,GAAI,QAAQ,CAAC;;QAEvD;QACA,IAAI,CAACG,WAAW,WAAA9N,MAAA,CAAW,IAAI,CAAC3F,cAAc,GAAI,IAAI,CAACE,aAAa,CAAC;;QAErE;QACA,IAAIoT,SAAQ,KAAM,WAAW,EAAE;UAC7B,IAAI,CAAC5Q,kBAAiB,GAAI,IAAI,CAACxC,aAAa,CAACC,SAAS;QACxD,OAAO,IAAImT,SAAQ,KAAM,MAAM,EAAE;UAC/B,IAAI,CAAC7Q,cAAa,GAAI,IAAI,CAACvC,aAAa,CAACE,IAAI;QAC/C;MACF,EAAE,OAAOwH,CAAC,EAAE;QACVnC,OAAO,CAACiD,IAAI,CAAC,gBAAgB,EAAEd,CAAC,CAAC;MACnC;MAEAnC,OAAO,CAACC,GAAG,iBAAAC,MAAA,CAAO2N,SAAS,qBAAA3N,MAAA,CAAQ,IAAI,CAACzF,aAAa,CAACoT,SAAS,IAAI,IAAG,GAAI,IAAI,CAAE,CAAC;IACnF,CAAC;IACDI,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb;MACA,IAAI,CAAClC,gBAAgB,CAAC,IAAI,CAAC;IAC7B,CAAC;IACDmC,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB;MACA,IAAI,CAACnC,gBAAgB,CAAC,KAAK,CAAC;IAC9B,CAAC;IACD;IACA7J,iBAAiB,WAAjBA,iBAAiBA,CAACsD,OAAO,EAAE;MACzB,IAAI,CAAC/G,MAAM,CAACkG,QAAQ,CAAC,YAAY,EAAE;QACjCY,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAEA,OAAO;QAChBC,SAAS,EAAE,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC;IACDuF,kBAAkB,WAAlBA,kBAAkBA,CAAChV,IAAI,EAAE;MACvB;MACA,IAAI,IAAI,CAACI,iBAAgB,IAAK,IAAI,CAACA,iBAAiB,CAACwG,EAAC,KAAM5G,IAAI,CAACiV,WAAW,EAAE;QAC5E;QACA,IAAI,CAAC7U,iBAAgB,GAAAkR,aAAA,CAAAA,aAAA,KAChB,IAAI,CAAClR,iBAAiB;UACzB8U,MAAM,EAAElV,IAAI,CAACmV;QAAQ,EACtB;;QAED;QACA,IAAMlE,WAAU,GAAI;UAClB7E,IAAI,EAAE,QAAQ;UACdC,OAAO,KAAAtF,MAAA,CAAK,IAAI,CAAC3G,iBAAiB,CAACf,IAAI,4CAAA0H,MAAA,CAAW/G,IAAI,CAACmV,SAAS,CAAE;UAClE7I,SAAS,EAAEtH,IAAI,CAACuH,GAAG,CAAC;QACtB,CAAC;QACD,IAAI,CAACS,WAAW,CAACiE,WAAW,CAAC;MAC/B;IACF,CAAC;IAGD;IACAxJ,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAI;QACF,IAAM/D,WAAU,GAAI,IAAI,CAAC4B,MAAM,CAACC,OAAO,CAAC7B,WAAW;QAEnD,IAAI,CAACA,WAAW,EAAE;UAChBmD,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC3B,IAAI,CAAC5D,WAAU,GAAI,KAAK;UACxB;QACF;QAEA,IAAI,CAAC,IAAI,CAACjD,IAAI,EAAE;UACd4G,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;UAC/B,IAAI,CAAC5D,WAAU,GAAI,KAAK;UACxB;QACF;;QAEA;QACA,IAAI,IAAI,CAACjD,IAAI,CAACmV,OAAO,EAAE;UACrBvO,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;UAC9B,IAAI,CAAC5D,WAAU,GAAI,IAAI;UACrB;QACF;;QAEA;QACA,IAAI,CAACA,WAAU,GAAI,IAAI,CAACjD,IAAI,CAACoV,SAAQ,KAAM3R,WAAW,CAACkD,EAAE;QACzDC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC5D,WAAU,GAAI,KAAI,GAAI,MAAM,CAAC;MAC3D,EAAE,OAAOiE,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,IAAI,CAACjE,WAAU,GAAI,KAAK;MAC1B;IACF,CAAC;IACD;IACAgF,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClBrB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;;MAE1B;MACA,IAAMwO,QAAO,GAAInW,gBAAgB,CAACqS,WAAU,GAAI,KAAI,GAAI,KAAK;MAC7D3K,OAAO,CAACC,GAAG,2BAAAC,MAAA,CAAiBuO,QAAQ,CAAE,CAAC;;MAEvC;MACAzO,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE3H,gBAAgB,CAACoW,aAAa,CAAC;;MAErD;MACA1O,OAAO,CAACC,GAAG,0CAAAC,MAAA,CAAY5H,gBAAgB,CAACqW,YAAY,CAACxJ,MAAM,CAAE,CAAC;;MAE9D;MACAnF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC/D,QAAQ,CAAC;;MAErC;MACA8D,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACxF,aAAa,CAAC;MAExC,IAAI,CAACnC,gBAAgB,CAACqS,WAAW,EAAE;QACjC3K,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;QAChC,IAAI,CAACiC,iBAAiB,CAAC,aAAa,CAAC;QACrC,IAAI,CAACrB,gBAAgB,CAAC,CAAC;MACzB;;MAEA;MACA,IAAI,CAAC+N,sBAAsB,CAAC,CAAC;;MAE7B;MACA,IAAI,CAAC1M,iBAAiB,CAAC,kBAAkB,CAAC;IAC5C,CAAC;IACD;IACA0M,sBAAsB,WAAtBA,sBAAsBA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACvB7O,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MACxBc,UAAU,CAAC,YAAM;QACf;QACA,IAAM+N,SAAQ,GAAIrM,QAAQ,CAACsM,gBAAgB,CAAC,UAAU,CAAC;QACvDD,SAAS,CAACE,OAAO,CAAC,UAAAC,QAAO,EAAK;UAC5BA,QAAQ,CAACC,QAAO,GAAI,KAAK;UACzBlP,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEgP,QAAQ,CAAC;QAClC,CAAC,CAAC;;QAEF;QACA,IAAIJ,MAAI,CAACM,KAAK,CAACC,OAAM,IAAKP,MAAI,CAACM,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,eAAe,EAAE;UAClER,MAAI,CAACM,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,eAAe,CAACH,QAAO,GAAI,KAAK;UACzDlP,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAChC;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IACD;IACAqP,uBAAuB,WAAvBA,uBAAuBA,CAACC,QAAQ,EAAE;MAChCvP,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEsP,QAAQ,CAAC;MACjC,IAAI,CAACrN,iBAAiB,CAAC,WAAW,CAAC;;MAEnC;MACA,IAAI;QACF,IAAMsN,cAAa,GAAI,IAAI,CAACC,WAAW,eAAAvP,MAAA,CAAe,IAAI,CAACoE,MAAM,CAAE,CAAC;QACpE,IAAIkL,cAAc,EAAE;UAClBxP,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEuP,cAAc,CAAC;;UAExC;UACA,IAAIA,cAAc,CAAClF,OAAO,EAAE;YAC1B,IAAI,CAACoF,YAAY,CAACF,cAAc,CAAClF,OAAO,CAAC;UAC3C;QACF;MACF,EAAE,OAAOhK,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC;IAED;IACAoP,YAAY,WAAZA,YAAYA,CAACC,IAAI,EAAE;MAAA,IAAAC,MAAA;MACjB,IAAI,IAAI,CAAC9T,aAAY,KAAM6T,IAAI,EAAE;QAC/B3P,OAAO,CAACC,GAAG,yBAAAC,MAAA,CAAU,IAAI,CAACpE,aAAa,0BAAAoE,MAAA,CAAQyP,IAAI,CAAE,CAAC;QACtD,IAAI,CAAC7T,aAAY,GAAI6T,IAAI;;QAEzB;QACA,IAAI,CAAC/C,WAAW,CAAC,SAAS,EAAE+C,IAAI,CAAC;;QAEjC;QACArX,gBAAgB,CAACuX,SAAS,CAACF,IAAI,CAAC;;QAEhC;QACA,IAAI,CAACzN,iBAAiB,4CAAAhC,MAAA,CAAcyP,IAAG,KAAM,QAAO,GAAI,MAAK,GAAI,MAAM,CAAE,CAAC;;QAE1E;QACA,IAAIA,IAAG,KAAM,QAAO,IAAK,CAAC,IAAI,CAAClV,aAAa,CAACU,SAAS,EAAE;UACtD4F,UAAU,CAAC,YAAM;YACf6O,MAAI,CAACxO,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC;UACtC,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF,CAAC;IACD;IACA0O,cAAc,WAAdA,cAAcA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACf,IAAI,CAAC3O,WAAW,CAAC,UAAU,CAAC;MAC5BL,UAAU,CAAC,YAAM;QACf;QACAgP,MAAI,CAACZ,KAAK,CAACtU,UAAS,IAAKkV,MAAI,CAACZ,KAAK,CAACtU,UAAU,CAACmV,KAAK,CAAC,CAAC;MACxD,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;IACD;IACAC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC3T,eAAc,GAAI,KAAK;MAC5B;MACA,IAAI,CAACsQ,WAAW,CAAC,wBAAwB,EAAE,MAAM,CAAC;IACpD,CAAC;IACD;IACMtL,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAAA,IAAA4O,MAAA;MAAA,OAAAnR,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAkR,SAAA;QAAA,IAAApO,QAAA,EAAAwN,QAAA,EAAAa,SAAA,EAAAC,GAAA;QAAA,OAAArR,YAAA,GAAAS,CAAA,WAAA6Q,SAAA;UAAA,kBAAAA,SAAA,CAAA3Q,CAAA;YAAA;cAAA2Q,SAAA,CAAA1Q,CAAA;cAAA0Q,SAAA,CAAA3Q,CAAA;cAAA,OAECtH,UAAU,CAACwC,UAAU,CAAC0V,WAAW,CAACL,MAAI,CAAC5L,MAAM,CAAC;YAAA;cAA/DvC,QAAO,GAAAuO,SAAA,CAAAzO,CAAA;cACb,IAAIE,QAAQ,CAAC5I,IAAG,IAAK4I,QAAQ,CAAC5I,IAAI,CAACsQ,OAAO,EAAE;gBACpC8F,QAAO,GAAIxN,QAAQ,CAAC5I,IAAI,CAACoW,QAAQ,EAEvC;gBACAW,MAAI,CAACzR,MAAM,CAAC0B,MAAM,CAAC,oBAAoB,EAAE;kBACvCqQ,MAAM,EAAEjB,QAAQ,CAACkB,OAAO;kBACxBC,MAAM,EAAEnB,QAAQ,CAACoB,OAAO;kBACxBC,SAAS,EAAErB,QAAQ,CAACsB;gBACtB,CAAC,CAAC;;gBAEF;gBACMT,SAAQ,GAAIF,MAAI,CAACxP,WAAW,cAAAR,MAAA,CAAcgQ,MAAI,CAAC5L,MAAM,CAAE,CAAC;gBAC9D,IAAI8L,SAAQ,KAAM,MAAM,EAAE;kBACxBF,MAAI,CAACzR,MAAM,CAAC0B,MAAM,CAAC,eAAe,EAAE,IAAI,CAAC;kBACzC+P,MAAI,CAAC5T,eAAc,GAAI,KAAK;gBAC9B,OAAO;kBACL4T,MAAI,CAACzR,MAAM,CAAC0B,MAAM,CAAC,eAAe,EAAE,KAAK,CAAC;kBAC1C+P,MAAI,CAAC5T,eAAc,GAAI,IAAI;gBAC7B;cACF;cAAAgU,SAAA,CAAA3Q,CAAA;cAAA;YAAA;cAAA2Q,SAAA,CAAA1Q,CAAA;cAAAyQ,GAAA,GAAAC,SAAA,CAAAzO,CAAA;cAEA7B,OAAO,CAACM,KAAK,CAAC,cAAc,EAAA+P,GAAO,CAAC;YAAA;cAAA,OAAAC,SAAA,CAAAhO,CAAA;UAAA;QAAA,GAAA6N,QAAA;MAAA;IAExC,CAAC;IACD;IACA5O,2BAA2B,WAA3BA,2BAA2BA,CAAA,EAAG;MAAA,IAAAuP,OAAA;MAC5B,IAAI,CAAC,IAAI,CAACvX,iBAAgB,IAAK,CAAC,IAAI,CAACA,iBAAiB,CAACwG,EAAE,EAAE;;MAE3D;MACA,IAAI,CAACtB,MAAM,CAACkG,QAAQ,CAAC,mBAAmB,EAAE,IAAI,CAACpL,iBAAiB,CAACwG,EAAE,EAChEgR,IAAI,CAAC,YAAM;QACV/Q,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE6Q,OAAI,CAACvX,iBAAiB,CAACf,IAAI,CAAC;QACrDsY,OAAI,CAAC5O,iBAAiB,iBAAAhC,MAAA,CAAO4Q,OAAI,CAACvX,iBAAiB,CAACf,IAAI,oCAAQ,CAAC;MACnE,CAAC,UACK,CAAC,UAAA8H,KAAI,EAAK;QACdN,OAAO,CAACM,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC,CAAC,CAAC;IACN,CAAC;IACD;IACA0Q,mBAAmB,WAAnBA,mBAAmBA,CAAC5C,WAAW,EAAE;MAC/BpO,OAAO,CAACC,GAAG,sDAAAC,MAAA,CAAckO,WAAW,CAAE,CAAC;;MAEvC;MACA,IAAI,IAAI,CAAC7U,iBAAgB,IAAK,IAAI,CAACA,iBAAiB,CAACwG,EAAC,KAAMqO,WAAW,EAAE;QACvE;QACA,IAAM6C,gBAAe,GAAI,IAAI,CAACxS,MAAM,CAACC,OAAO,CAACwG,cAAc,CAACgM,IAAI,CAAC,UAAAC,CAAA;UAAA,OAAKA,CAAC,CAACpR,EAAC,KAAMqO,WAAW;QAAA,EAAC;QAC3F,IAAI6C,gBAAgB,EAAE;UACpB,IAAI,CAAC1X,iBAAgB,GAAI0X,gBAAgB;UACzCjR,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgR,gBAAgB,CAACzY,IAAI,CAAC;QAChD;MACF;IACF,CAAC;IACD;IACAgJ,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;MAAA,IAAA4P,OAAA;MACxB;MACA,IAAI,CAAC7U,kBAAiB,GAAI8U,WAAW,CAAC,YAAM;QAC1C,IAAID,OAAI,CAAC7X,iBAAgB,IAAK6X,OAAI,CAAC7X,iBAAiB,CAACwG,EAAE,EAAE;UACvDC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmR,OAAI,CAAC7X,iBAAiB,CAACf,IAAI,CAAC;UACrD4Y,OAAI,CAAC7P,2BAA2B,CAAC,CAAC;QACpC;MACF,CAAC,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC,EAAE;IACrB,CAAC;IACD;IAEA+P,eAAe,WAAfA,eAAeA,CAACnY,IAAI,EAAE;MACpB;MACA,IAAI,CAACqD,MAAM,CAACrD,IAAI,CAACoY,UAAU,IAAIpY,IAAI,CAACqY,KAAK;;MAEzC;MACA,IAAI,CAACC,iBAAiB,mBAAAvR,MAAA,CAAQ/G,IAAI,CAACqY,KAAK,CAAChZ,IAAI,0BAAO,CAAC;;MAErD;MACA,IAAI,CAACkZ,kBAAkB,CAAC,CAAC;IAC3B,CAAC;IAEDA,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB;MACA;MACA,IAAI,IAAI,CAAClY,SAAQ,IAAK,IAAI,CAACA,SAAS,CAACwS,UAAS,KAAMC,SAAS,CAACC,IAAI,EAAE;QAClE,IAAI,CAAC1S,SAAS,CAACmY,IAAI,CAACzE,IAAI,CAAC0E,SAAS,CAAC;UACjCrM,IAAI,EAAE,aAAa;UACnB/I,MAAM,EAAE,IAAI,CAACA;QACf,CAAC,CAAC,CAAC;MACL;IACF,CAAC;IAEDqV,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB;MACA;IAAA,CACD;IACDC,eAAe,WAAfA,eAAeA,CAACC,IAAI,EAAE;MACpB;MACA/R,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE8R,IAAI,CAAC;IAC7B,CAAC;IACDtG,wBAAwB,WAAxBA,wBAAwBA,CAAC2C,WAAW,EAAE;MACpC,IAAI,CAAC1R,kBAAiB,GAAI0R,WAAW;IACvC,CAAC;IACD;IACA4D,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACvX,aAAa,CAACE,IAAG,GAAI,CAAC,IAAI,CAACF,aAAa,CAACE,IAAI;MAClD,IAAI,CAACqC,cAAa,GAAI,IAAI,CAACvC,aAAa,CAACE,IAAI;IAC/C,CAAC;IACD;IACAsX,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACxX,aAAa,CAACC,SAAQ,GAAI,CAAC,IAAI,CAACD,aAAa,CAACC,SAAS;MAC5D,IAAI,CAACuC,kBAAiB,GAAI,IAAI,CAACxC,aAAa,CAACC,SAAS;IACxD,CAAC;IACD;IACAwX,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACvV,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAS;IAClC,CAAC;IACD;IACAwV,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAAC1X,aAAa,CAACK,GAAE,GAAI,CAAC,IAAI,CAACL,aAAa,CAACK,GAAG;IAClD,CAAC;IACD;IACAsX,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAChR,WAAW,CAAC,WAAW,CAAC;IAC/B,CAAC;IACD;IACAiR,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACjR,WAAW,CAAC,aAAa,CAAC;IACjC,CAAC;IACD;IACAkR,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAClR,WAAW,CAAC,WAAW,CAAC;IAC/B,CAAC;IACD;IACAmR,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACnR,WAAW,CAAC,aAAa,CAAC;MAC/BpB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACxF,aAAa,CAACS,WAAU,GAAI,IAAG,GAAI,IAAI,CAAC;IACtE,CAAC;IACD;IACAsX,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAACpR,WAAW,CAAC,WAAW,CAAC;IAC/B,CAAC;IACD;IACAqR,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACvQ,iBAAiB,CAAC,UAAU,CAAC;IACpC,CAAC;IACDwQ,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;MACxB,IAAI,CAACxQ,iBAAiB,CAAC,UAAU,CAAC;IACpC,CAAC;IACD;IACAT,cAAc,WAAdA,cAAcA,CAACkR,QAAQ,EAAE;MAAA,IAAAC,iBAAA;MACvB,IAAI,CAACD,QAAQ,EAAE;;MAEf;MACA,IAAMjK,WAAU,GAAI;QAClBnD,IAAI,EAAE,MAAM;QACZuC,KAAK,EAAE6K,QAAQ,CAAC7K,KAAI,IAAK,CAAC;QAC1BC,KAAK,EAAE4K,QAAQ,CAAC5K,KAAI,IAAK,GAAG;QAC5BC,QAAQ,EAAE2K,QAAQ,CAAC3K,QAAO,IAAK,CAAC;QAChCzH,QAAQ,EAAE,EAAAqS,iBAAA,OAAI,CAAC/V,WAAW,cAAA+V,iBAAA,uBAAhBA,iBAAA,CAAkBrS,QAAO,KAAK,IAAI;QAC5CkF,SAAS,EAAE,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,IAAItQ,gBAAgB,CAACqS,WAAW,EAAE;QAChCrS,gBAAgB,CAAC6N,WAAW,CAACuC,WAAW,CAAC;MAC3C,OAAO;QACL;QACA,IAAMd,OAAM,GAAI,EAAE;QAClB,IAAIC,KAAI,GAAI,CAAC;QACb,KAAK,IAAII,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAIS,WAAW,CAACZ,KAAK,EAAEG,CAAC,EAAE,EAAE;UAC1C,IAAM4C,MAAK,GAAI3C,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,IAAIM,WAAW,CAACX,KAAK,IAAI,CAAC;UAChEH,OAAO,CAACvF,IAAI,CAACwI,MAAM,CAAC;UACpBhD,KAAI,IAAKgD,MAAM;QACjB;QACAhD,KAAI,IAAKa,WAAW,CAACV,QAAQ;;QAE7B;QACA,IAAI,CAAC9F,iBAAiB,IAAAhC,MAAA,CAAIwI,WAAW,CAACnI,QAAQ,oBAAAL,MAAA,CAAOwI,WAAW,CAACZ,KAAK,OAAA5H,MAAA,CAAIwI,WAAW,CAACX,KAAK,EAAA7H,MAAA,CAAGwI,WAAW,CAACV,QAAO,GAAI,IAAI,GAAE,GAAIU,WAAW,CAACV,QAAO,GAAI,EAAE,SAAA9H,MAAA,CAAM2H,KAAK,QAAA3H,MAAA,CAAK0H,OAAO,CAACe,IAAI,CAAC,IAAI,CAAC,EAAAzI,MAAA,CAAGwI,WAAW,CAACV,QAAO,GAAI,IAAI,KAAI,GAAIU,WAAW,CAACV,QAAO,GAAI,EAAE,MAAG,CAAC;MAC/P;IACF,CAAC;IACD;IACAtG,kBAAkB,WAAlBA,kBAAkBA,CAACmR,IAAI,EAAE;MAAA,IAAAC,OAAA;MACvB,IAAI,CAACD,IAAI,EAAE;;MAEX;MACA,IAAI,CAAC3Q,iBAAiB,CAAC,aAAa,CAAC;;MAErC;MACAnB,UAAU,CAAC,YAAM;QACf+R,OAAI,CAAC5Q,iBAAiB,CAAC,WAAW,CAAC;MACrC,CAAC,EAAE,IAAI,CAAC;;MAER;MACAlC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE4S,IAAI,CAACra,IAAI,CAAC;IACnC,CAAC;IACDua,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAAC1Y,kBAAiB,GAAI,CAAC,IAAI,CAACA,kBAAkB;MAClD2F,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC5F,kBAAiB,GAAI,KAAI,GAAI,KAAK,CAAC;;MAEhE;MACA,IAAI,CAACuS,WAAW,sBAAA1M,MAAA,CAAsB,IAAI,CAACoE,MAAM,GAAI,IAAI,CAACjK,kBAAkB,CAAC;IAC/E,CAAC;IACDoG,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACnG,QAAO,GAAIkD,MAAM,CAACC,UAAS,GAAI,GAAG;MACvCuC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC3F,QAAO,GAAI,MAAK,GAAI,MAAM,CAAC;IACvD,CAAC;IACD;IACAmM,iBAAiB,WAAjBA,iBAAiBA,CAAC3E,OAAO,EAAE;MACzB9B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6B,OAAO,CAAC;MAC/B,IAAI,CAACrD,MAAM,CAACkG,QAAQ,CAAC,YAAY,EAAE7C,OAAO,CAAC;IAC7C,CAAC;IAED4E,gBAAgB,WAAhBA,gBAAgBA,CAACvN,IAAI,EAAE;MACrB6G,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE9G,IAAI,CAAC;;MAE5B;MACA,IAAI,CAAC,IAAI,CAACU,WAAW,CAACqX,IAAI,CAAC,UAAA8B,IAAG;QAAA,OAAKA,IAAI,CAACjT,EAAC,KAAM5G,IAAI,CAACwM,OAAO;MAAA,EAAC,EAAE;QAC5D,IAAI,CAAC9L,WAAW,CAACwI,IAAI,CAAC;UACpBtC,EAAE,EAAE5G,IAAI,CAACwM,OAAO;UAChBpF,QAAQ,EAAEpH,IAAI,CAACoH;QACjB,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI,CAAC2B,iBAAiB,IAAAhC,MAAA,CAAI/G,IAAI,CAACoH,QAAQ,oCAAQ,CAAC;IAClD,CAAC;IAEDoG,cAAc,WAAdA,cAAcA,CAACxN,IAAI,EAAE;MACnB6G,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE9G,IAAI,CAAC;;MAE5B;MACA,IAAM8Z,KAAI,GAAI,IAAI,CAACpZ,WAAW,CAACqZ,SAAS,CAAC,UAAAF,IAAG;QAAA,OAAKA,IAAI,CAACjT,EAAC,KAAM5G,IAAI,CAACwM,OAAO;MAAA,EAAC;MAC1E,IAAIsN,KAAI,KAAM,CAAC,CAAC,EAAE;QAChB,IAAI,CAACpZ,WAAW,CAACsZ,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACnC;;MAEA;MACA,IAAI,CAAC/Q,iBAAiB,IAAAhC,MAAA,CAAI/G,IAAI,CAACoH,QAAQ,oCAAQ,CAAC;IAClD,CAAC;IAEDqG,iBAAiB,WAAjBA,iBAAiBA,CAACzN,IAAI,EAAE;MAAA,IAAAia,sBAAA;QAAAC,OAAA;MACtB,IAAIla,IAAI,CAACwM,OAAM,OAAAyN,sBAAA,GAAM,IAAI,CAAC3U,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAuW,sBAAA,uBAA/BA,sBAAA,CAAiCrT,EAAE,GAAE;QACxD,IAAI,CAACjG,QAAO,GAAIX,IAAI,CAACma,MAAM;QAC3B,IAAI,CAACvZ,cAAa,GAAIZ,IAAI,CAACoH,QAAQ;;QAEnC;QACAgT,YAAY,CAAC,IAAI,CAACvZ,WAAW,CAAC;QAC9B,IAAI,IAAI,CAACF,QAAQ,EAAE;UACjB,IAAI,CAACE,WAAU,GAAI+G,UAAU,CAAC,YAAM;YAClCsS,OAAI,CAACvZ,QAAO,GAAI,KAAK;UACvB,CAAC,EAAE,IAAI,CAAC;QACV;MACF;IACF,CAAC;IAED+M,cAAc,WAAdA,cAAcA,CAAC/E,OAAO,EAAE;MACtB9B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE6B,OAAO,CAAC;;MAE/B;MACA,IAAM4G,WAAU,GAAI;QAClBnD,IAAI,EAAE,QAAQ;QACdC,OAAO,KAAAtF,MAAA,CAAK4B,OAAO,CAACvB,QAAQ,oBAAAL,MAAA,CAAO4B,OAAO,CAACgG,KAAK,OAAA5H,MAAA,CAAI4B,OAAO,CAACiG,KAAK,EAAA7H,MAAA,CAAG4B,OAAO,CAACkG,QAAO,GAAI,IAAI,GAAE,GAAIlG,OAAO,CAACkG,QAAO,GAAI,EAAE,SAAA9H,MAAA,CAAM4B,OAAO,CAAC+F,KAAK,QAAA3H,MAAA,CAAK4B,OAAO,CAAC8F,OAAO,CAACe,IAAI,CAAC,IAAI,CAAC,EAAAzI,MAAA,CAAG4B,OAAO,CAACkG,QAAO,GAAI,IAAI,KAAI,GAAIlG,OAAO,CAACkG,QAAO,GAAI,EAAE,MAAG;QAClOvC,SAAS,EAAE3D,OAAO,CAAC2D,SAAQ,IAAK,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC,CAAC;QACxDC,SAAS,EAAE;UACTf,KAAK,EAAEhG,OAAO,CAACgG,KAAK;UACpBC,KAAK,EAAEjG,OAAO,CAACiG,KAAK;UACpBC,QAAQ,EAAElG,OAAO,CAACkG,QAAQ;UAC1BJ,OAAO,EAAE9F,OAAO,CAAC8F,OAAO;UACxBC,KAAK,EAAE/F,OAAO,CAAC+F;QACjB;MACF,CAAC;;MAED;MACA,IAAI,CAACpJ,MAAM,CAACkG,QAAQ,CAAC,YAAY,EAAE+D,WAAW,CAAC;;MAE/C;MACA,IAAI,CAAC/O,WAAW,CAAC0I,IAAI,CAAAoI,aAAA,CAAAA,aAAA,KAChB3I,OAAO;QACV2D,SAAS,EAAE,IAAItH,IAAI,CAAC,CAAC,CAACyK,WAAW,CAAC;MAAA,EACnC,CAAC;;MAEF;MACA,IAAI,IAAI,CAACjP,WAAW,CAACwL,MAAK,GAAI,EAAE,EAAE;QAChC,IAAI,CAACxL,WAAW,CAAC6Z,KAAK,CAAC,CAAC;MAC1B;IACF,CAAC;IAED;IACA;IACAhR,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAAClI,QAAO,GAAIkD,MAAM,CAACC,UAAS,GAAI,GAAG;MACvCuC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC3F,QAAO,GAAI,MAAK,GAAI,MAAM,CAAC;IACvD,CAAC;IACD;IACA6M,kBAAkB,WAAlBA,kBAAkBA,CAACsM,KAAK,EAAE;MACxB,IAAI,CAAC5G,SAAS,CAAC,CAAC;MAChB;MACA,IAAI,IAAI,CAACrS,iBAAiB,EAAE;QAC1BiZ,KAAK,CAACC,cAAc,CAAC,CAAC;QACtBD,KAAK,CAACE,WAAU,GAAI,kBAAkB;QACtC,OAAOF,KAAK,CAACE,WAAW;MAC1B;IACF,CAAC;IAED;IACAC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MAAA,IAAAC,OAAA;MACjB,IAAI;QACF;QACA,IAAMlR,IAAG,GAAIF,QAAQ,CAACG,aAAa,CAAC,iBAAiB,CAAC;QACtD,IAAI,CAACD,IAAI,EAAE;UACT3C,OAAO,CAACM,KAAK,CAAC,sBAAsB,CAAC;UACrC,IAAI,CAAC4B,iBAAiB,CAAC,iBAAiB,CAAC;UACzC;QACF;QAEAlC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE0C,IAAI,CAAC;;QAEjC;QACA,IAAMa,YAAW,GACff,QAAQ,CAACgB,iBAAgB,IACzBhB,QAAQ,CAACiB,uBAAsB,IAC/BjB,QAAQ,CAACkB,oBAAmB,IAC5BlB,QAAQ,CAACmB,mBAAmB;QAE9B,IAAI,CAACJ,YAAY,EAAE;UACjB;UACAxD,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;;UAEvB;UACA0C,IAAI,CAACmR,YAAY,CAAC,qBAAqB,EAAE,MAAM,CAAC;;UAEhD;UACAnR,IAAI,CAACoR,SAAS,CAACC,GAAG,CAAC,sBAAsB,CAAC;;UAE1C;UACA,IAAMC,iBAAgB,GAAItR,IAAI,CAACE,iBAAgB,IACtBF,IAAI,CAACG,uBAAsB,IAC3BH,IAAI,CAACI,oBAAmB,IACxBJ,IAAI,CAACK,mBAAmB;UAEjD,IAAIiR,iBAAiB,EAAE;YACrB;YACAA,iBAAiB,CAACC,KAAK,CAACvR,IAAI,CAAC,CAACoO,IAAI,CAAC,YAAM;cACvC/Q,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;cACrB0C,IAAI,CAACoR,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;cACnCrR,IAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,sBAAsB,CAAC;cAC7CN,OAAI,CAAC3R,iBAAiB,CAAC,yBAAyB,CAAC;YACnD,CAAC,CAAC,SAAM,CAAC,UAAA4J,GAAE,EAAK;cACd9L,OAAO,CAACM,KAAK,CAAC,UAAU,EAAEwL,GAAG,CAAC;cAC9BnJ,IAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,sBAAsB,CAAC;cAC7CN,OAAI,CAAC3R,iBAAiB,CAAC,UAAS,GAAI4J,GAAG,CAAChK,OAAO,CAAC;YAClD,CAAC,CAAC;UACJ,OAAO;YACL;YACA9B,OAAO,CAACiD,IAAI,CAAC,uBAAuB,CAAC;YACrCN,IAAI,CAACoR,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;YACnCrR,IAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,sBAAsB,CAAC;YAC7C1R,QAAQ,CAACiL,IAAI,CAACqG,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;YAC9C,IAAI,CAAC9R,iBAAiB,CAAC,sBAAsB,CAAC;UAChD;QACF,OAAO;UACL;UACAlC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;;UAEvB;UACA,IAAMmU,cAAa,GAAI3R,QAAQ,CAACoB,cAAa,IACxBpB,QAAQ,CAACqB,oBAAmB,IAC5BrB,QAAQ,CAACsB,mBAAkB,IAC3BtB,QAAQ,CAACuB,gBAAgB;UAE9C,IAAIoQ,cAAc,EAAE;YAClBA,cAAc,CAACF,KAAK,CAACzR,QAAQ,CAAC,CAACsO,IAAI,CAAC,YAAM;cACxC/Q,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;cACtB0C,IAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,eAAe,CAAC;cACtCN,OAAI,CAAC3R,iBAAiB,CAAC,SAAS,CAAC;YACnC,CAAC,CAAC,SAAM,CAAC,UAAA4J,GAAE,EAAK;cACd9L,OAAO,CAACM,KAAK,CAAC,SAAS,EAAEwL,GAAG,CAAC;cAC7B+H,OAAI,CAAC3R,iBAAiB,CAAC,UAAS,GAAI4J,GAAG,CAAChK,OAAO,CAAC;YAClD,CAAC,CAAC;UACJ,OAAO;YACL;YACAa,IAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,eAAe,CAAC;YACtC1R,QAAQ,CAACiL,IAAI,CAACqG,SAAS,CAACI,MAAM,CAAC,iBAAiB,CAAC;YACjD,IAAI,CAACjS,iBAAiB,CAAC,SAAS,CAAC;UACnC;QACF;MACF,EAAE,OAAO5B,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,IAAI,CAAC4B,iBAAiB,CAAC,UAAS,GAAI5B,KAAK,CAACwB,OAAO,CAAC;;QAElD;QACA,IAAI;UACF,IAAMa,KAAG,GAAIF,QAAQ,CAACG,aAAa,CAAC,iBAAiB,CAAC;UACtD,IAAID,KAAI,EAAE;YACRA,KAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,sBAAsB,CAAC;YAC7CxR,KAAI,CAACoR,SAAS,CAACI,MAAM,CAAC,eAAe,CAAC;UACxC;UACA1R,QAAQ,CAACiL,IAAI,CAACqG,SAAS,CAACI,MAAM,CAAC,iBAAiB,CAAC;QACnD,EAAE,OAAOhS,CAAC,EAAE;UACVnC,OAAO,CAACM,KAAK,CAAC,WAAW,EAAE6B,CAAC,CAAC;QAC/B;MACF;IACF,CAAC;IACD;IACAO,sBAAsB,WAAtBA,sBAAsBA,CAAA,EAAG;MACvB,IAAI;QACF,IAAMc,YAAW,GAAI,CAAC,CAACf,QAAQ,CAACgB,iBAAgB,IAC5B,CAAC,CAAChB,QAAQ,CAACiB,uBAAsB,IACjC,CAAC,CAACjB,QAAQ,CAACkB,oBAAmB,IAC9B,CAAC,CAAClB,QAAQ,CAACmB,mBAAmB;QAElD,IAAMyQ,SAAQ,GAAI5R,QAAQ,CAACG,aAAa,CAAC,iBAAiB,CAAC;QAC3D,IAAI,CAACyR,SAAS,EAAE;QAEhB,IAAI7Q,YAAY,EAAE;UAChBxD,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;UACtB;UACAwC,QAAQ,CAACiL,IAAI,CAACqG,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;UAC9CK,SAAS,CAACN,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;;UAExC;QACF,OAAO;UACLhU,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;UACtB;UACAwC,QAAQ,CAACiL,IAAI,CAACqG,SAAS,CAACI,MAAM,CAAC,iBAAiB,CAAC;UACjDE,SAAS,CAACN,SAAS,CAACI,MAAM,CAAC,eAAe,CAAC;;UAE3C;UACA,IAAMG,MAAK,GAAID,SAAS,CAACzR,aAAa,CAAC,oBAAoB,CAAC;UAC5D,IAAI0R,MAAM,EAAE;YACVA,MAAM,CAACC,UAAU,CAACC,WAAW,CAACF,MAAM,CAAC;UACvC;QACF;MACF,EAAE,OAAOhU,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;IACD;IACAmU,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB;MACA,IAAI,IAAI,CAACtF,KAAK,CAACC,OAAO,EAAE;QACtB,IAAI,CAACD,KAAK,CAACC,OAAO,CAACsF,cAAa,GAAI,IAAI;MAC1C,OAAO;QACL1U,OAAO,CAACiD,IAAI,CAAC,gBAAgB,CAAC;MAChC;IACF,CAAC;IAED0R,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACzS,iBAAiB,CAAC,iBAAiB,CAAC;IAC3C,CAAC;IAED0S,yBAAyB,WAAzBA,yBAAyBA,CAACrF,QAAQ,EAAE;MAClC,IAAI,CAACrN,iBAAiB,CAAC,WAAW,CAAC;;MAEnC;MACA,IAAIqN,QAAQ,CAACiB,MAAM,EAAE;QACnB,IAAI,CAAC/R,MAAM,CAAC0B,MAAM,CAAC,oBAAoB,EAAE;UACvCqQ,MAAM,EAAEjB,QAAQ,CAACiB;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED;IACAqE,kBAAkB,WAAlBA,kBAAkBA,CAACrP,OAAO,EAAEsP,KAAK,EAAEC,QAAQ,EAAE;MAC3C,IAAI,CAAClX,eAAc,GAAI2H,OAAM,IAAK,EAAE;MACpC,IAAI,CAAC1H,aAAY,GAAIgX,KAAI,IAAK,OAAO;MACrC,IAAI,CAAC/W,gBAAe,GAAIgX,QAAO,IAAK,EAAE;MACtC,IAAI,CAAC3T,WAAW,CAAC,gBAAgB,CAAC;IACpC,CAAC;IAED4T,oBAAoB,WAApBA,oBAAoBA,CAACC,QAAQ,EAAE;MAC7B,IAAI,CAAC/S,iBAAiB,6BAAAhC,MAAA,CAAS+U,QAAQ,CAACC,QAAQ,oCAAQ,CAAC;IAC3D,CAAC;IAED;IACAC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC/T,WAAW,CAAC,WAAW,CAAC;IAC/B,CAAC;IAED;IACAgU,iBAAiB,WAAjBA,iBAAiBA,CAACC,IAAI,EAAE;MACtB,IAAI,CAACnT,iBAAiB,mBAAAhC,MAAA,CAAQmV,IAAI,CAAC7c,IAAI,gCAAQ,CAAC;IAClD,CAAC;IAED;IACA8c,iBAAiB,WAAjBA,iBAAiBA,CAACD,IAAI,EAAE;MACtB,IAAI,CAACnT,iBAAiB,mBAAAhC,MAAA,CAAQmV,IAAI,CAAC7c,IAAI,gCAAQ,CAAC;IAClD,CAAC;IAED;IACA+c,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACrT,iBAAiB,CAAC,SAAS,CAAC;IACnC,CAAC;IAED;IACAsT,qBAAqB,WAArBA,qBAAqBA,CAACH,IAAI,EAAE;MAC1B,IAAI,CAACnT,iBAAiB,+CAAY,MAAM,EAAE,IAAI,CAAC;IACjD,CAAC;IAED;IACAuT,iBAAiB,WAAjBA,iBAAiBA,CAAC3T,OAAO,EAAE;MACzB,IAAI,CAACI,iBAAiB,CAACJ,OAAO,CAAC;IACjC,CAAC;IAED;IACA4T,kBAAkB,WAAlBA,kBAAkBA,CAACL,IAAI,EAAE;MACvB,IAAI,CAACnT,iBAAiB,qDAAAhC,MAAA,CAAamV,IAAI,CAACM,OAAO,CAAE,CAAC;MAClD;MACA,IAAI,CAACC,cAAc,CAACP,IAAI,CAAC;IAC3B,CAAC;IAED;IACAQ,iBAAiB,WAAjBA,iBAAiBA,CAACC,QAAQ,EAAE;MAC1B;MACA,IAAI,CAAC5T,iBAAiB,CAAC,8BAA8B,CAAC;;MAEtD;MACA,IAAI,CAAC5E,SAAQ,GAAI;QACfjE,QAAQ,EAAA0c,kBAAA,CAAM,IAAI,CAAC1c,QAAQ,CAAC;QAC5BC,UAAU,EAAAyc,kBAAA,CAAM,IAAI,CAACzc,UAAU,CAAC;QAChCkD,MAAM,EAAAuZ,kBAAA,CAAM,IAAI,CAACvZ,MAAM,CAAC;QACxBC,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;QACzC9C,WAAW,EAAAoc,kBAAA,CAAM,IAAI,CAACpc,WAAW;MACnC,CAAC;;MAED;MACA,IAAI,CAACN,QAAO,GAAIyc,QAAQ,CAACE,SAAS,CAAC3c,QAAO,IAAK,EAAE;MACjD,IAAI,CAACC,UAAS,GAAIwc,QAAQ,CAACE,SAAS,CAAC1c,UAAS,IAAK,EAAE;MACrD,IAAI,CAACkD,MAAK,GAAIsZ,QAAQ,CAACE,SAAS,CAACxZ,MAAK,IAAK,EAAE;MAC7C,IAAI,CAACC,iBAAgB,GAAIqZ,QAAQ,CAACE,SAAS,CAACC,YAAW,IAAK,CAAC;MAC7D,IAAI,CAACtc,WAAU,GAAImc,QAAQ,CAACE,SAAS,CAACrc,WAAU,IAAK,EAAE;;MAEvD;MACA,IAAI,CAAC0D,aAAY,GAAI,IAAI;IAC3B,CAAC;IAED;IACA6Y,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAAC7Y,aAAY,IAAK,CAAC,IAAI,CAACC,SAAS,EAAE;;MAE5C;MACA,IAAI,CAACjE,QAAO,GAAI,IAAI,CAACiE,SAAS,CAACjE,QAAQ;MACvC,IAAI,CAACC,UAAS,GAAI,IAAI,CAACgE,SAAS,CAAChE,UAAU;MAC3C,IAAI,CAACkD,MAAK,GAAI,IAAI,CAACc,SAAS,CAACd,MAAM;MACnC,IAAI,CAACC,iBAAgB,GAAI,IAAI,CAACa,SAAS,CAACb,iBAAiB;MACzD,IAAI,CAAC9C,WAAU,GAAI,IAAI,CAAC2D,SAAS,CAAC3D,WAAW;;MAE7C;MACA,IAAI,CAAC0D,aAAY,GAAI,KAAK;MAC1B,IAAI,CAACC,SAAQ,GAAI,IAAI;MAErB,IAAI,CAAC4E,iBAAiB,CAAC,SAAS,CAAC;IACnC,CAAC;IAED;IACM0T,cAAc,WAAdA,cAAcA,CAACP,IAAI,EAAE;MAAA,IAAAc,OAAA;MAAA,OAAApX,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAmX,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAArX,YAAA,GAAAS,CAAA,WAAA6W,SAAA;UAAA,kBAAAA,SAAA,CAAA3W,CAAA;YAAA;cAAA2W,SAAA,CAAA1W,CAAA;cAAA0W,SAAA,CAAA3W,CAAA;cAAA,OAGjBwW,OAAI,CAAC1X,MAAM,CAACkG,QAAQ,CAAC,UAAU,EAAE0Q,IAAI,CAAC;YAAA;cAE5C;cACAc,OAAI,CAAC9c,QAAO,GAAI8c,OAAI,CAAC1X,MAAM,CAAC8X,KAAK,CAACld,QAAO,IAAK,EAAE;cAChD8c,OAAI,CAAC7c,UAAS,GAAI6c,OAAI,CAAC1X,MAAM,CAAC8X,KAAK,CAACjd,UAAS,IAAK,EAAE;cACpD6c,OAAI,CAAC3Z,MAAK,GAAI2Z,OAAI,CAAC1X,MAAM,CAAC8X,KAAK,CAAC/Z,MAAK,IAAK,EAAE;cAC5C2Z,OAAI,CAAC1Z,iBAAgB,GAAI0Z,OAAI,CAAC1X,MAAM,CAAC8X,KAAK,CAAC9Z,iBAAgB,IAAK,CAAC;cACjE0Z,OAAI,CAACxc,WAAU,GAAIwc,OAAI,CAAC1X,MAAM,CAAC8X,KAAK,CAAC5c,WAAU,IAAK,EAAE;;cAEtD;cACAwc,OAAI,CAAC/U,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC;cAEnC+U,OAAI,CAACjU,iBAAiB,mBAAAhC,MAAA,CAAQmV,IAAI,CAAC7c,IAAI,gCAAQ,CAAC;cAAA8d,SAAA,CAAA3W,CAAA;cAAA;YAAA;cAAA2W,SAAA,CAAA1W,CAAA;cAAAyW,GAAA,GAAAC,SAAA,CAAAzU,CAAA;cAEhD7B,OAAO,CAACM,KAAK,CAAC,SAAS,EAAA+V,GAAO,CAAC;cAC/BF,OAAI,CAACjU,iBAAiB,CAAC,QAAQ,CAAC;YAAA;cAAA,OAAAoU,SAAA,CAAAhU,CAAA;UAAA;QAAA,GAAA8T,QAAA;MAAA;IAEpC,CAAC;IAED;IACAI,eAAe,WAAfA,eAAeA,CAACC,YAAY,EAAE;MAC5B,IAAI,CAACvU,iBAAiB,CAACuU,YAAY,CAAC;IACtC,CAAC;IAED;IACAC,gBAAgB,WAAhBA,gBAAgBA,CAACC,UAAU,EAAE;MAC3B;MACAA,UAAU,CAACC,QAAO,GAAI,IAAI,CAACxd,IAAI,CAACZ,IAAI;MACpCme,UAAU,CAACE,eAAc,GAAI,IAAI,CAACzd,IAAI,CAAC6I,WAAW;MAClD0U,UAAU,CAAC9c,WAAU,GAAI,IAAI,CAACA,WAAW,CAACiB,GAAG,CAAC,UAAAkY,IAAG;QAAA,OAAM;UACrDjT,EAAE,EAAEiT,IAAI,CAACjT,EAAE;UACXQ,QAAQ,EAAEyS,IAAI,CAACzS;QACjB,CAAC;MAAA,CAAC,CAAC;;MAEH;IACF,CAAC;IAED;IACMuW,SAAS,WAATA,SAASA,CAAA,EAAG;MAAA,IAAAC,OAAA;MAAA,OAAAhY,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA+X,SAAA;QAAA,IAAAC,qBAAA,EAAAC,SAAA,EAAApB,QAAA,EAAA/T,QAAA,EAAAoV,GAAA;QAAA,OAAAnY,YAAA,GAAAS,CAAA,WAAA2X,SAAA;UAAA,kBAAAA,SAAA,CAAAzX,CAAA;YAAA;cAAAyX,SAAA,CAAAxX,CAAA;cAEd;cACMsX,SAAQ,GAAIH,OAAI,CAACM,4BAA4B,CAAC,CAAC;cAE/CvB,QAAO,GAAI;gBACftd,IAAI,gCAAA0H,MAAA,CAAY,IAAI/B,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,CAAE;gBACpD6D,WAAW,EAAE,WAAW;gBACxBmE,OAAO,EAAEhG,QAAQ,CAAC2W,OAAI,CAACxc,cAAc,CAAC;gBACtC+c,UAAU,EAAE,EAAAL,qBAAA,GAAAF,OAAI,CAACtY,MAAM,CAACC,OAAO,CAAC7B,WAAW,cAAAoa,qBAAA,uBAA/BA,qBAAA,CAAiClX,EAAC,KAAK,CAAC;gBACpDiW,SAAS,EAAEkB,SAAS;gBACpBK,SAAS,EAAE,IAAI;gBACfC,YAAY,EAAE,KAAI,CAAE;cACtB,CAAC;cAAA,KAIGT,OAAI,CAAC3Z,WAAW;gBAAAga,SAAA,CAAAzX,CAAA;gBAAA;cAAA;cAAAyX,SAAA,CAAAzX,CAAA;cAAA,OAEDtH,UAAU,CAAC+C,SAAS,CAACqc,UAAU,CAACV,OAAI,CAAC3Z,WAAW,EAAE;gBACjE5E,IAAI,EAAEsd,QAAQ,CAACtd,IAAI;gBACnByJ,WAAW,EAAE6T,QAAQ,CAAC7T,WAAW;gBACjC+T,SAAS,EAAEF,QAAQ,CAACE;cACtB,CAAC,CAAC;YAAA;cAJFjU,QAAO,GAAAqV,SAAA,CAAAvV,CAAA;cAAAuV,SAAA,CAAAzX,CAAA;cAAA;YAAA;cAAAyX,SAAA,CAAAzX,CAAA;cAAA,OAOUtH,UAAU,CAAC+C,SAAS,CAACsc,UAAU,CAAC5B,QAAQ,CAAC;YAAA;cAA1D/T,QAAO,GAAAqV,SAAA,CAAAvV,CAAA;YAAA;cAGT,IAAIE,QAAQ,CAAC5I,IAAI,EAAE;gBACjB4d,OAAI,CAAC3Z,WAAU,GAAI2E,QAAQ,CAAC5I,IAAI,CAAC4G,EAAE;gBACnCgX,OAAI,CAAC7U,iBAAiB,CAAC,QAAQ,CAAC;cAClC;cAAAkV,SAAA,CAAAzX,CAAA;cAAA;YAAA;cAAAyX,SAAA,CAAAxX,CAAA;cAAAuX,GAAA,GAAAC,SAAA,CAAAvV,CAAA;cAEA7B,OAAO,CAACM,KAAK,CAAC,SAAS,EAAA6W,GAAO,CAAC;cAC/BJ,OAAI,CAAC7U,iBAAiB,CAAC,QAAQ,CAAC;YAAA;cAAA,OAAAkV,SAAA,CAAA9U,CAAA;UAAA;QAAA,GAAA0U,QAAA;MAAA;IAEpC,CAAC;IAED;IACMW,SAAS,WAATA,SAASA,CAAA,EAAG;MAAA,IAAAC,OAAA;MAAA,OAAA7Y,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA4Y,SAAA;QAAA,IAAA9V,QAAA,EAAA+V,GAAA;QAAA,OAAA9Y,YAAA,GAAAS,CAAA,WAAAsY,SAAA;UAAA,kBAAAA,SAAA,CAAApY,CAAA;YAAA;cAAA,IACXiY,OAAI,CAACxa,WAAW;gBAAA2a,SAAA,CAAApY,CAAA;gBAAA;cAAA;cACnBiY,OAAI,CAAC1V,iBAAiB,CAAC,WAAW,CAAC;cAAA,OAAA6V,SAAA,CAAAzV,CAAA;YAAA;cAAAyV,SAAA,CAAAnY,CAAA;cAAAmY,SAAA,CAAApY,CAAA;cAAA,OAMZtH,UAAU,CAAC+C,SAAS,CAAC4c,OAAO,CAACJ,OAAI,CAACxa,WAAW,CAAC;YAAA;cAA/D2E,QAAO,GAAAgW,SAAA,CAAAlW,CAAA;cAAA,KAETE,QAAQ,CAAC5I,IAAI;gBAAA4e,SAAA,CAAApY,CAAA;gBAAA;cAAA;cAAAoY,SAAA,CAAApY,CAAA;cAAA,OAETiY,OAAI,CAACnZ,MAAM,CAACkG,QAAQ,CAAC,UAAU,EAAE5C,QAAQ,CAAC5I,IAAI,CAAC;YAAA;cAErD;cACAye,OAAI,CAACve,QAAO,GAAIue,OAAI,CAACnZ,MAAM,CAAC8X,KAAK,CAACld,QAAO,IAAK,EAAE;cAChDue,OAAI,CAACte,UAAS,GAAIse,OAAI,CAACnZ,MAAM,CAAC8X,KAAK,CAACjd,UAAS,IAAK,EAAE;cACpDse,OAAI,CAACpb,MAAK,GAAIob,OAAI,CAACnZ,MAAM,CAAC8X,KAAK,CAAC/Z,MAAK,IAAK,EAAE;cAC5Cob,OAAI,CAACnb,iBAAgB,GAAImb,OAAI,CAACnZ,MAAM,CAAC8X,KAAK,CAAC9Z,iBAAgB,IAAK,CAAC;cACjEmb,OAAI,CAACje,WAAU,GAAIie,OAAI,CAACnZ,MAAM,CAAC8X,KAAK,CAAC5c,WAAU,IAAK,EAAE;cAEtDie,OAAI,CAAC1V,iBAAiB,CAAC,QAAQ,CAAC;YAAA;cAAA6V,SAAA,CAAApY,CAAA;cAAA;YAAA;cAAAoY,SAAA,CAAAnY,CAAA;cAAAkY,GAAA,GAAAC,SAAA,CAAAlW,CAAA;cAGlC7B,OAAO,CAACM,KAAK,CAAC,SAAS,EAAAwX,GAAO,CAAC;cAC/BF,OAAI,CAAC1V,iBAAiB,CAAC,QAAQ,CAAC;YAAA;cAAA,OAAA6V,SAAA,CAAAzV,CAAA;UAAA;QAAA,GAAAuV,QAAA;MAAA;IAEpC,CAAC;IAED;IACAR,4BAA4B,WAA5BA,4BAA4BA,CAAA,EAAG;MAC7B;MACA,IAAMH,SAAQ,GAAI;QAChBzR,SAAS,EAAEtH,IAAI,CAACuH,GAAG,CAAC,CAAC;QACrBrM,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BkD,MAAM,EAAE,IAAI,CAACA,MAAM;QACnByZ,YAAY,EAAE,IAAI,CAACxZ,iBAAiB;QACpC9C,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BiB,KAAK,EAAE,IAAI,CAAC6D,MAAM,CAAC8X,KAAK,CAAC3b,KAAI,IAAK,CAAC,CAAC;QACpCqd,KAAK,EAAE,IAAI,CAACxZ,MAAM,CAAC8X,KAAK,CAAC0B,KAAI,IAAK,EAAE;QACpCpd,UAAU,EAAE,IAAI,CAAC4D,MAAM,CAAC8X,KAAK,CAAC1b,UAAS,IAAK,CAAC,CAAC;QAC9C8b,UAAU,EAAE;UACVC,QAAQ,EAAE,IAAI,CAACxd,IAAI,CAACZ,IAAI;UACxBqe,eAAe,EAAE,IAAI,CAACzd,IAAI,CAAC6I,WAAW;UACtCpI,WAAW,EAAE,IAAI,CAACA,WAAW,CAACiB,GAAG,CAAC,UAAAkY,IAAG;YAAA,OAAM;cACzCjT,EAAE,EAAEiT,IAAI,CAACjT,EAAE;cACXQ,QAAQ,EAAEyS,IAAI,CAACzS;YACjB,CAAC;UAAA,CAAC;QACJ;MACF,CAAC;MAED,OAAO2W,SAAS;IAClB,CAAC;IACD;IACAtV,aAAa,WAAbA,aAAaA,CAAC6R,KAAK,EAAE;MACnB;IAAA,CACD;IACD;IACAvQ,uBAAuB,WAAvBA,uBAAuBA,CAACuQ,KAAK,EAAE;MAC7B;IAAA,CACD;IACD;IACAjT,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACjD,WAAU,GAAIC,MAAM,CAACC,UAAU;MACpC,IAAI,CAACC,YAAW,GAAIF,MAAM,CAACG,WAAW;IACxC,CAAC;IAED;IACAua,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,OAAA;MAClB,IAAI,CAACva,eAAc,GAAI,CAAC,IAAI,CAACA,eAAe;;MAE5C;MACA,IAAI,IAAI,CAACA,eAAe,EAAE;QACxBmD,UAAU,CAAC,YAAM;UACf,IAAMqX,WAAS,GAAI,SAAbA,UAASA,CAAKjW,CAAC,EAAK;YACxB,IAAMkW,KAAI,GAAI5V,QAAQ,CAACG,aAAa,CAAC,qBAAqB,CAAC;YAC3D,IAAM0V,MAAK,GAAI7V,QAAQ,CAACG,aAAa,CAAC,qBAAqB,CAAC;YAC5D,IAAIyV,KAAI,IAAK,CAACA,KAAK,CAACE,QAAQ,CAACpW,CAAC,CAACqW,MAAM,MAAM,CAACF,MAAK,IAAK,CAACA,MAAM,CAACC,QAAQ,CAACpW,CAAC,CAACqW,MAAM,CAAC,CAAC,EAAE;cACjFL,OAAI,CAACva,eAAc,GAAI,KAAK;cAC5B6E,QAAQ,CAACW,mBAAmB,CAAC,OAAO,EAAEgV,WAAU,CAAC;YACnD;UACF,CAAC;UACD3V,QAAQ,CAACd,gBAAgB,CAAC,OAAO,EAAEyW,WAAU,CAAC;QAChD,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC;IACD;IACAK,kBAAkB,WAAlBA,kBAAkBA,CAACtf,IAAI,EAAE;MACvB,IAAI,CAAC0b,kBAAkB,CAAC1b,IAAI,CAACqM,OAAO,EAAErM,IAAI,CAAC2b,KAAK,EAAE3b,IAAI,CAAC4b,QAAQ,CAAC;IAClE,CAAC;IAED;IACA2D,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB;MACA,IAAI,CAAC,IAAI,CAAC1a,mBAAkB,IAAK,IAAI,CAAC3B,WAAW,EAAE;QACjD,IAAI,CAAC2B,mBAAkB,GAAI,yEAAyE;MACtG;MAEA,IAAI,CAACoD,WAAW,CAAC,cAAc,CAAC;IAClC,CAAC;IAED;IACAuX,sBAAsB,WAAtBA,sBAAsBA,CAACxf,IAAI,EAAE;MAC3B,IAAI,CAAC6E,mBAAkB,GAAI7E,IAAI,CAACqM,OAAO;MACvC,IAAI,CAACvH,iBAAgB,GAAI9E,IAAI,CAAC2b,KAAK;MACnC,IAAI,CAAC5W,gBAAe,GAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;;MAEnD;MACA,IAAI,CAACwa,8BAA8B,CAAC,CAAC;;MAErC;MACA,IAAItgB,gBAAgB,CAACqS,WAAW,EAAE;QAChCrS,gBAAgB,CAACugB,sBAAsB,CAAC,IAAI,CAACte,cAAc,EAAE;UAC3DiL,OAAO,EAAErM,IAAI,CAACqM,OAAO;UACrBsP,KAAK,EAAE3b,IAAI,CAAC2b,KAAK;UACjBrP,SAAS,EAAEtM,IAAI,CAACsM;QAClB,CAAC,CAAC;MACJ;MAEA,IAAI,CAACvD,iBAAiB,CAAC,OAAO,CAAC;IACjC,CAAC;IAED;IACA0W,8BAA8B,WAA9BA,8BAA8BA,CAAA,EAAG;MAC/B,IAAI;QACF,IAAME,gBAAe,GAAI;UACvBtT,OAAO,EAAE,IAAI,CAACxH,mBAAmB;UACjC8W,KAAK,EAAE,IAAI,CAAC7W,iBAAiB;UAC7B8a,IAAI,EAAE,IAAI,CAAC7a,gBAAgB;UAC3BoG,MAAM,EAAE,IAAI,CAAC/J;QACf,CAAC;QAED,IAAI,CAACyT,WAAW,sBAAA9N,MAAA,CAAsB,IAAI,CAAC3F,cAAc,GAAIue,gBAAgB,CAAC;MAChF,EAAE,OAAOxY,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;IAED;IACAY,gCAAgC,WAAhCA,gCAAgCA,CAAA,EAAG;MACjC,IAAI;QACF,IAAM4X,gBAAe,GAAI,IAAI,CAACrJ,WAAW,sBAAAvP,MAAA,CAAsB,IAAI,CAAC3F,cAAc,CAAE,CAAC;QACrF,IAAIue,gBAAgB,EAAE;UACpB,IAAI,CAAC9a,mBAAkB,GAAI8a,gBAAgB,CAACtT,OAAM,IAAK,EAAE;UACzD,IAAI,CAACvH,iBAAgB,GAAI6a,gBAAgB,CAAChE,KAAI,IAAK,MAAM;UACzD,IAAI,CAAC5W,gBAAe,GAAI4a,gBAAgB,CAACC,IAAG,IAAK,IAAI5a,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QAC9E;MACF,EAAE,OAAOkC,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;IAED;IACA0G,wBAAwB,WAAxBA,wBAAwBA,CAAC7N,IAAI,EAAE;MAC7B;MACA,IAAI,CAAC,IAAI,CAACkD,WAAW,EAAE;QACrB,IAAI,CAAC2B,mBAAkB,GAAI7E,IAAI,CAACqM,OAAO;QACvC,IAAI,CAACvH,iBAAgB,GAAI9E,IAAI,CAAC2b,KAAK;QACnC,IAAI,CAAC5W,gBAAe,GAAI,IAAIC,IAAI,CAAChF,IAAI,CAACsM,SAAS,CAAC,CAACrH,cAAc,CAAC,CAAC;;QAEjE;QACA,IAAI,CAACwa,8BAA8B,CAAC,CAAC;;QAErC;QACA,IAAI,CAAC1W,iBAAiB,CAAC,oBAAoB,CAAC;MAC9C;IACF,CAAC;IAED;IACAf,4BAA4B,WAA5BA,4BAA4BA,CAAA,EAAG;MAC7B,IAAI;QACF;QACA,IAAMmD,MAAK,GAAIlE,QAAQ,CAAC,IAAI,CAAC7F,cAAc,KAAK,IAAI,CAACnB,IAAI,CAAC2G,EAAE;QAE5DC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;UAC5BqE,MAAM,EAAEA,MAAM;UACd/J,cAAc,EAAE,IAAI,CAACA;QACvB,CAAC,CAAC;QAEF,IAAMye,UAAS,GAAI,IAAI,CAACtY,WAAW,cAAAR,MAAA,CAAcoE,MAAM,CAAE,CAAC;QAC1D,IAAI0U,UAAU,EAAE;UACdhZ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+Y,UAAU,CAAC;UACrC,IAAI,CAAC5f,IAAI,CAACZ,IAAG,GAAIwgB,UAAU;;UAE3B;UACA,IAAI,IAAI,CAACva,MAAM,CAAC8X,KAAK,CAAC0C,KAAI,IAAK,IAAI,CAACxa,MAAM,CAAC8X,KAAK,CAAC0C,KAAK,CAAC9T,MAAK,GAAI,CAAC,EAAE;YACjE,IAAI,CAAC1G,MAAM,CAAC0B,MAAM,CAAC,uBAAuB,EAAE;cAC1CmE,MAAM,EAAEA,MAAM;cACd9L,IAAI,EAAEwgB;YACR,CAAC,CAAC;UACJ;QACF,OAAO;UACLhZ,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;QAC3B;MACF,EAAE,OAAOK,KAAK,EAAE;QACdN,OAAO,CAACM,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACxC;IACF,CAAC;IAED;IACA4Y,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAAA,IAAAC,OAAA;MAClB,IAAI,CAAC,IAAI,CAAC9c,WAAW,EAAE;MAEvB,IAAI,CAACgC,iBAAgB,GAAI,IAAI;MAC7B,IAAI,CAACC,gBAAe,GAAI,IAAI,CAAClF,IAAI,CAACZ,IAAG,IAAK,EAAE;;MAE5C;MACA,IAAI,CAAC4gB,SAAS,CAAC,YAAM;QACnB,IAAID,OAAI,CAAChK,KAAK,CAACkK,aAAa,EAAE;UAC5BF,OAAI,CAAChK,KAAK,CAACkK,aAAa,CAACrJ,KAAK,CAAC,CAAC;QAClC;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACMsJ,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,OAAA;MAAA,OAAAxa,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAua,SAAA;QAAA,IAAAC,OAAA,EAAAnV,MAAA;QAAA,OAAAtF,YAAA,GAAAS,CAAA,WAAAia,SAAA;UAAA,kBAAAA,SAAA,CAAA/Z,CAAA;YAAA;cAAA,IACd4Z,OAAI,CAACld,WAAW;gBAAAqd,SAAA,CAAA/Z,CAAA;gBAAA;cAAA;cAAA,OAAA+Z,SAAA,CAAApX,CAAA;YAAA;cAEfmX,OAAM,GAAIF,OAAI,CAACjb,gBAAgB,CAAC+L,IAAI,CAAC,CAAC;cAAA,IACvCoP,OAAO;gBAAAC,SAAA,CAAA/Z,CAAA;gBAAA;cAAA;cACV4Z,OAAI,CAACI,kBAAkB,CAAC,CAAC;cAAA,OAAAD,SAAA,CAAApX,CAAA;YAAA;cAI3B;cACAtC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;gBACrBqE,MAAM,EAAEiV,OAAI,CAAChf,cAAc;gBAC3Bqf,OAAO,EAAEL,OAAI,CAACngB,IAAI,CAACZ,IAAI;gBACvBihB,OAAO,EAAEA,OAAO;gBAChBI,UAAU,EAAEN,OAAI,CAACngB;cACnB,CAAC,CAAC;;cAEF;cAAA,MACIqgB,OAAM,KAAMF,OAAI,CAACngB,IAAI,CAACZ,IAAI;gBAAAkhB,SAAA,CAAA/Z,CAAA;gBAAA;cAAA;cAC5B;cACM2E,MAAK,GAAIlE,QAAQ,CAACmZ,OAAI,CAAChf,cAAc,KAAKgf,OAAI,CAACngB,IAAI,CAAC2G,EAAE,EAE5D;cAAA2Z,SAAA,CAAA/Z,CAAA;cAAA,OACM4Z,OAAI,CAAC9a,MAAM,CAACkG,QAAQ,CAAC,gBAAgB,EAAE;gBAC3CL,MAAM,EAAEA,MAAM;gBACd9L,IAAI,EAAEihB;cACR,CAAC,CAAC;YAAA;cAEF;cACA,IAAInhB,gBAAgB,CAACqS,WAAW,EAAE;gBAChCrS,gBAAgB,CAAC6N,WAAW,CAAC;kBAC3BZ,IAAI,EAAE,kBAAkB;kBACxBjB,MAAM,EAAEA,MAAM;kBACd9L,IAAI,EAAEihB;gBACR,CAAC,CAAC;cACJ;cAEAF,OAAI,CAACrX,iBAAiB,CAAC,SAAS,CAAC;YAAA;cAGnCqX,OAAI,CAAClb,iBAAgB,GAAI,KAAK;YAAA;cAAA,OAAAqb,SAAA,CAAApX,CAAA;UAAA;QAAA,GAAAkX,QAAA;MAAA;IAChC,CAAC;IAED;IACAG,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAACtb,iBAAgB,GAAI,KAAK;IAChC,CAAC;IAED;IACA4I,oBAAoB,WAApBA,oBAAoBA,CAAC9N,IAAI,EAAE;MACzB,IAAI,CAAC,IAAI,CAACkD,WAAW,EAAE;QACrB;QACA,IAAMiI,MAAK,GAAIlE,QAAQ,CAAC,IAAI,CAAC7F,cAAc,KAAK,IAAI,CAACnB,IAAI,CAAC2G,EAAE;QAE5DC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE;UACxBqE,MAAM,EAAEA,MAAM;UACdwV,YAAY,EAAE3gB;QAChB,CAAC,CAAC;;QAEF;QACA,IAAI,CAACsF,MAAM,CAACkG,QAAQ,CAAC,gBAAgB,EAAE;UACrCL,MAAM,EAAEA,MAAM;UACd9L,IAAI,EAAEW,IAAI,CAACX;QACb,CAAC,CAAC;QACF,IAAI,CAAC0J,iBAAiB,CAAC,WAAW,CAAC;MACrC;IACF,CAAC;IAED;IACA6X,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC3Y,WAAW,CAAC,QAAQ,CAAC;IAC5B,CAAC;IAED4Y,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC5Y,WAAW,CAAC,YAAY,CAAC;IAChC,CAAC;IAED6Y,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC7Y,WAAW,CAAC,WAAW,CAAC;IAC/B,CAAC;IAED8Y,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAAC9Y,WAAW,CAAC,gBAAgB,CAAC;IACpC,CAAC;IAED+Y,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC/Y,WAAW,CAAC,QAAQ,CAAC;IAC5B,CAAC;IAEDgZ,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAAChZ,WAAW,CAAC,SAAS,CAAC;IAC7B,CAAC;IAEDiZ,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAACjZ,WAAW,CAAC,SAAS,CAAC;IAC7B,CAAC;IAED;IACAkZ,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,IAAI,CAACpY,iBAAiB,CAAC,OAAO,CAAC;IACjC,CAAC;IAEDqY,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACrY,iBAAiB,CAAC,OAAO,CAAC;IACjC,CAAC;IAEDsY,0BAA0B,WAA1BA,0BAA0BA,CAAC9f,SAAS,EAAE;MACpC,IAAI,CAACqB,mBAAkB,GAAIrB,SAAS;MACpC,IAAI,CAACsB,eAAc,GAAI,EAAE;MACzB,IAAI,CAACoF,WAAW,CAAC,YAAY,EAAE,KAAK,CAAC;IACvC,CAAC;IAED;IACAqZ,sBAAsB,WAAtBA,sBAAsBA,CAAC5P,MAAM,EAAE;MAC7B,IAAM/I,OAAM,MAAA5B,MAAA,CAAO2K,MAAM,CAACjB,aAAa,oBAAA1J,MAAA,CAAO2K,MAAM,CAAClB,SAAS,qBAAAzJ,MAAA,CAAQ2K,MAAM,CAAC6P,IAAI,OAAAxa,MAAA,CAAI2K,MAAM,CAAC8P,WAAW,OAAAza,MAAA,CAAI,IAAI,CAAC0a,mBAAmB,CAAC/P,MAAM,CAACgQ,YAAY,CAAC,CAAE;MAC1J,IAAI,CAAC1U,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;IACrC,CAAC;IAEDgZ,wBAAwB,WAAxBA,wBAAwBA,CAACjQ,MAAM,EAAE;MAC/B,IAAM/I,OAAM,gCAAA5B,MAAA,CAAa2K,MAAM,CAACkQ,SAAS,CAACviB,IAAI,OAAA0H,MAAA,CAAI2K,MAAM,CAACkQ,SAAS,CAACL,IAAI,WAAAxa,MAAA,CAAQ2K,MAAM,CAACmQ,QAAQ,CAACxiB,IAAI,OAAA0H,MAAA,CAAI2K,MAAM,CAACmQ,QAAQ,CAACN,IAAI,wBAAAxa,MAAA,CAAW2K,MAAM,CAACoQ,MAAK,IAAK,IAAI,CAAE;MAC7J,IAAI,CAAC9U,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;IACrC,CAAC;IAEDoZ,iBAAiB,WAAjBA,iBAAiBA,CAAC/hB,IAAI,EAAE;MACtB,IAAM2I,OAAM,MAAA5B,MAAA,CAAO/G,IAAI,CAACuB,SAAS,CAAClC,IAAI,cAAA0H,MAAA,CAAM/G,IAAI,CAAC+Q,KAAK,iCAAAhK,MAAA,CAAU/G,IAAI,CAACgiB,WAAW,CAAE;MAClF,IAAI,CAAChV,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;IACrC,CAAC;IAEDsZ,uBAAuB,WAAvBA,uBAAuBA,CAACvQ,MAAM,EAAE;MAC9B,IAAM/I,OAAM,MAAA5B,MAAA,CAAO2K,MAAM,CAACnQ,SAAS,oBAAAwF,MAAA,CAAO2K,MAAM,CAACX,KAAK,iCAAAhK,MAAA,CAAU2K,MAAM,CAAC6P,IAAI,OAAAxa,MAAA,CAAI2K,MAAM,CAACwQ,YAAY,OAAAnb,MAAA,CAAI2K,MAAM,CAACpB,OAAM,GAAI,IAAG,GAAI,IAAI,EAAAvJ,MAAA,CAAG2K,MAAM,CAACpB,OAAM,QAAAvJ,MAAA,CAAS2K,MAAM,CAACsQ,WAAW,IAAK,EAAE,CAAE;MACtL,IAAI,CAAChV,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;IACrC,CAAC;IAEDwZ,iCAAiC,WAAjCA,iCAAiCA,CAAC5gB,SAAS,EAAE;MAC3C,IAAI,CAACqB,mBAAkB,GAAIrB,SAAS;IACtC,CAAC;IAED;IACA6gB,oBAAoB,WAApBA,oBAAoBA,CAACC,MAAM,EAAE;MAC3B,IAAI,CAACtZ,iBAAiB,oCAAAhC,MAAA,CAAWsb,MAAM,CAAChjB,IAAI,CAAE,CAAC;IACjD,CAAC;IAEDijB,sBAAsB,WAAtBA,sBAAsBA,CAACD,MAAM,EAAE;MAC7B,IAAI,CAACtZ,iBAAiB,oCAAAhC,MAAA,CAAWsb,MAAM,CAAChjB,IAAI,CAAE,CAAC;IACjD,CAAC;IAEDkjB,gBAAgB,WAAhBA,gBAAgBA,CAACF,MAAM,EAAE;MACvB,IAAI,CAACtZ,iBAAiB,oCAAAhC,MAAA,CAAWsb,MAAM,CAAChjB,IAAI,CAAE,CAAC;IACjD,CAAC;IAEDmjB,mBAAmB,WAAnBA,mBAAmBA,CAACC,KAAK,EAAE;MACzB,IAAI,CAAC1Z,iBAAiB,oCAAAhC,MAAA,CAAW0b,KAAK,CAACpjB,IAAI,CAAE,CAAC;IAChD,CAAC;IAEDqjB,qBAAqB,WAArBA,qBAAqBA,CAACD,KAAK,EAAE;MAC3B,IAAI,CAAC1Z,iBAAiB,oCAAAhC,MAAA,CAAW0b,KAAK,CAACpjB,IAAI,CAAE,CAAC;IAChD,CAAC;IAEDsjB,cAAc,WAAdA,cAAcA,CAACC,IAAI,EAAE;MACnB,IAAI,CAAC7Z,iBAAiB,oCAAAhC,MAAA,CAAW6b,IAAI,CAACvjB,IAAI,CAAE,CAAC;IAC/C,CAAC;IAEDwjB,mBAAmB,WAAnBA,mBAAmBA,CAACD,IAAI,EAAE;MACxB,IAAI,CAAC7Z,iBAAiB,oCAAAhC,MAAA,CAAW6b,IAAI,CAACvjB,IAAI,CAAE,CAAC;IAC/C,CAAC;IAEDyjB,iBAAiB,WAAjBA,iBAAiBA,CAACF,IAAI,EAAE;MACtB,IAAI,CAAC7Z,iBAAiB,oCAAAhC,MAAA,CAAW6b,IAAI,CAACvjB,IAAI,CAAE,CAAC;IAC/C,CAAC;IAED0jB,mBAAmB,WAAnBA,mBAAmBA,CAAC/iB,IAAI,EAAE;MACxB,IAAM2I,OAAM,MAAA5B,MAAA,CAAO/G,IAAI,CAACuB,SAAS,CAAClC,IAAI,oBAAA0H,MAAA,CAAO/G,IAAI,CAACgjB,MAAM,0BAAAjc,MAAA,CAAQ/G,IAAI,CAACijB,MAAM,8BAAO;MAClF,IAAI,CAACjW,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;IACrC,CAAC;IAEDua,cAAc,WAAdA,cAAcA,CAACljB,IAAI,EAAE;MACnB,IAAM2I,OAAM,MAAA5B,MAAA,CAAO/G,IAAI,CAACuB,SAAS,CAAClC,IAAI,0BAAA0H,MAAA,CAAQ/G,IAAI,CAACmjB,IAAI,CAAC9jB,IAAI,CAAE;MAC9D,IAAI,CAAC2N,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;IACrC,CAAC;IAED;IACAya,eAAe,WAAfA,eAAeA,CAACpjB,IAAI,EAAE;MACpB,IAAM2I,OAAM,MAAA5B,MAAA,CAAO/G,IAAI,CAACqjB,MAAM,CAAChkB,IAAI,0BAAA0H,MAAA,CAAQ/G,IAAI,CAACsjB,KAAK,CAACjkB,IAAI,CAAE;MAC5D,IAAI,CAAC2N,WAAW,CAACrE,OAAO,EAAE,QAAQ,CAAC;;MAEnC;MACA,IAAI3I,IAAI,CAACqf,MAAK,IAAKrf,IAAI,CAACqf,MAAK,KAAM,MAAM,EAAE;QACzC,IAAI,CAACrS,WAAW,kBAAAjG,MAAA,CAAQ/G,IAAI,CAACqf,MAAM,GAAI,QAAQ,CAAC;MAClD;;MAEA;MACA,IAAIrf,IAAI,CAAC8I,WAAW,EAAE;QACpB,IAAI,CAACkE,WAAW,8BAAAjG,MAAA,CAAU/G,IAAI,CAAC8I,WAAW,GAAI,QAAQ,CAAC;MACzD;IACF,CAAC;IAED;IACA8E,qBAAqB,WAArBA,qBAAqBA,CAACrM,SAAS,EAAE;MAC/B;MACA,IAAI,CAACgiB,KAAK,CAAC,mBAAmB,EAAEhiB,SAAS,CAAC;;MAE1C;MACA,IAAI,IAAI,CAACnB,iBAAgB,IAAK,IAAI,CAACA,iBAAiB,CAACwG,EAAC,KAAMrF,SAAS,CAACqF,EAAE,EAAE;QACxE,IAAI,CAACxG,iBAAgB,GAAImB,SAAS;MACpC;IACF,CAAC;IAED;IACAkgB,mBAAmB,WAAnBA,mBAAmBA,CAAClR,KAAK,EAAE;MACzB,IAAMiT,MAAK,GAAI;QACb,SAAS,EAAE,MAAM;QACjB,MAAM,EAAE,MAAM;QACd,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE;MACb,CAAC;MACD,OAAOA,MAAM,CAACjT,KAAK,KAAK,IAAI;IAC9B;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}