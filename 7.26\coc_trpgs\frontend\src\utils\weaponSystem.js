/**
 * COC 7版武器装备系统
 * 处理武器切换、弹药管理、护甲系统
 */

import CombatRules from './combatRules.js'
import { diceRoller } from './diceRoller.js'

export class WeaponSystem {
  /**
   * 武器数据库
   */
  static weaponDatabase = {
    // 近战武器
    melee: {
      fist: {
        id: 'fist',
        name: '拳头',
        type: 'melee',
        skill: 'fighting_brawl',
        damage: '1d3',
        range: 1,
        attacks: 1,
        malfunction: null,
        cost: 0,
        weight: 0,
        description: '徒手攻击',
        canBlock: false,
        canParry: false,
        twoHanded: false,
        concealable: true
      },
      knife: {
        id: 'knife',
        name: '小刀',
        type: 'melee',
        skill: 'fighting_brawl',
        damage: '1d4',
        range: 1,
        attacks: 1,
        malfunction: null,
        cost: 5,
        weight: 0.5,
        description: '小型刀具',
        canBlock: false,
        canParry: true,
        twoHanded: false,
        concealable: true,
        impaling: true
      },
      sword: {
        id: 'sword',
        name: '剑',
        type: 'melee',
        skill: 'sword',
        damage: '1d8',
        range: 1,
        attacks: 1,
        malfunction: null,
        cost: 50,
        weight: 3,
        description: '单手剑',
        canBlock: true,
        canParry: true,
        twoHanded: false,
        concealable: false,
        impaling: true
      },
      two_handed_sword: {
        id: 'two_handed_sword',
        name: '双手剑',
        type: 'melee',
        skill: 'sword',
        damage: '1d10+2',
        range: 1,
        attacks: 1,
        malfunction: null,
        cost: 100,
        weight: 6,
        description: '双手剑，威力强大',
        canBlock: true,
        canParry: true,
        twoHanded: true,
        concealable: false,
        impaling: true
      },
      axe: {
        id: 'axe',
        name: '斧头',
        type: 'melee',
        skill: 'axe',
        damage: '1d8+2',
        range: 1,
        attacks: 1,
        malfunction: null,
        cost: 30,
        weight: 4,
        description: '单手斧',
        canBlock: false,
        canParry: false,
        twoHanded: false,
        concealable: false
      },
      spear: {
        id: 'spear',
        name: '长矛',
        type: 'melee',
        skill: 'spear',
        damage: '1d8+1',
        range: 2,
        attacks: 1,
        malfunction: null,
        cost: 15,
        weight: 3,
        description: '长柄武器，可以攻击2格距离',
        canBlock: true,
        canParry: true,
        twoHanded: true,
        concealable: false,
        impaling: true
      },
      club: {
        id: 'club',
        name: '棍棒',
        type: 'melee',
        skill: 'fighting_brawl',
        damage: '1d6',
        range: 1,
        attacks: 1,
        malfunction: null,
        cost: 2,
        weight: 2,
        description: '简单的钝器',
        canBlock: true,
        canParry: true,
        twoHanded: false,
        concealable: false
      }
    },

    // 远程武器
    ranged: {
      pistol: {
        id: 'pistol',
        name: '手枪',
        type: 'firearm',
        skill: 'firearms_handgun',
        damage: '1d10',
        range: { base: 15, long: 30, extreme: 60 },
        attacks: 1,
        ammo: 6,
        currentAmmo: 6,
        malfunction: 100,
        cost: 150,
        weight: 1,
        description: '标准手枪',
        ammoType: 'pistol_ammo',
        reloadTime: 1,
        concealable: true,
        automatic: false
      },
      revolver: {
        id: 'revolver',
        name: '左轮手枪',
        type: 'firearm',
        skill: 'firearms_handgun',
        damage: '1d10+2',
        range: { base: 15, long: 30, extreme: 60 },
        attacks: 1,
        ammo: 6,
        currentAmmo: 6,
        malfunction: 100,
        cost: 200,
        weight: 1.5,
        description: '威力更大的左轮手枪',
        ammoType: 'pistol_ammo',
        reloadTime: 3,
        concealable: true,
        automatic: false
      },
      rifle: {
        id: 'rifle',
        name: '步枪',
        type: 'firearm',
        skill: 'firearms_rifle',
        damage: '2d6+4',
        range: { base: 90, long: 180, extreme: 360 },
        attacks: 1,
        ammo: 5,
        currentAmmo: 5,
        malfunction: 100,
        cost: 300,
        weight: 4,
        description: '高威力步枪',
        ammoType: 'rifle_ammo',
        reloadTime: 3,
        concealable: false,
        automatic: false,
        impaling: true
      },
      shotgun: {
        id: 'shotgun',
        name: '霰弹枪',
        type: 'firearm',
        skill: 'firearms_shotgun',
        damage: '4d6/2d6/1d6',
        range: { base: 10, long: 20, extreme: 50 },
        attacks: 1,
        ammo: 2,
        currentAmmo: 2,
        malfunction: 100,
        cost: 250,
        weight: 3.5,
        description: '近距离威力巨大',
        ammoType: 'shotgun_shells',
        reloadTime: 2,
        concealable: false,
        automatic: false,
        spread: true
      },
      submachinegun: {
        id: 'submachinegun',
        name: '冲锋枪',
        type: 'firearm',
        skill: 'firearms_submachinegun',
        damage: '1d10',
        range: { base: 20, long: 40, extreme: 80 },
        attacks: 'burst/full',
        ammo: 30,
        currentAmmo: 30,
        malfunction: 96,
        cost: 500,
        weight: 3,
        description: '可全自动射击',
        ammoType: 'pistol_ammo',
        reloadTime: 1,
        concealable: false,
        automatic: true,
        burstSize: 3,
        fullAutoRate: 10
      },
      bow: {
        id: 'bow',
        name: '弓',
        type: 'ranged',
        skill: 'bow',
        damage: '1d6',
        range: { base: 30, long: 60, extreme: 120 },
        attacks: 1,
        ammo: 1,
        currentAmmo: 1,
        malfunction: null,
        cost: 50,
        weight: 2,
        description: '传统弓箭',
        ammoType: 'arrows',
        reloadTime: 1,
        concealable: false,
        automatic: false,
        impaling: true
      }
    },

    // 投掷武器
    thrown: {
      throwing_knife: {
        id: 'throwing_knife',
        name: '投掷刀',
        type: 'thrown',
        skill: 'throw',
        damage: '1d4',
        range: { base: 5, long: 10, extreme: 20 },
        attacks: 1,
        ammo: 1,
        malfunction: null,
        cost: 10,
        weight: 0.5,
        description: '投掷武器',
        concealable: true,
        impaling: true
      },
      grenade: {
        id: 'grenade',
        name: '手榴弹',
        type: 'thrown',
        skill: 'throw',
        damage: '4d6',
        range: { base: 10, long: 20, extreme: 30 },
        attacks: 1,
        ammo: 1,
        malfunction: null,
        cost: 100,
        weight: 0.5,
        description: '爆炸性武器',
        concealable: true,
        explosive: true,
        blastRadius: 3
      }
    }
  }

  /**
   * 护甲数据库
   */
  static armorDatabase = {
    none: {
      id: 'none',
      name: '无护甲',
      protection: 0,
      coverage: 'none',
      cost: 0,
      weight: 0,
      description: '没有护甲保护'
    },
    leather_jacket: {
      id: 'leather_jacket',
      name: '皮夹克',
      protection: 1,
      coverage: 'torso',
      cost: 30,
      weight: 2,
      description: '简单的皮质防护',
      concealable: true
    },
    thick_clothing: {
      id: 'thick_clothing',
      name: '厚衣服',
      protection: 1,
      coverage: 'body',
      cost: 20,
      weight: 1,
      description: '厚重的衣物',
      concealable: true
    },
    kevlar_vest: {
      id: 'kevlar_vest',
      name: '防弹背心',
      protection: 4,
      coverage: 'torso',
      type: 'ballistic',
      cost: 500,
      weight: 3,
      description: '现代防弹背心',
      concealable: true
    },
    riot_gear: {
      id: 'riot_gear',
      name: '防暴装备',
      protection: 3,
      coverage: 'body',
      cost: 800,
      weight: 8,
      description: '全身防暴装备',
      concealable: false
    },
    medieval_armor: {
      id: 'medieval_armor',
      name: '中世纪盔甲',
      protection: 6,
      coverage: 'body',
      cost: 2000,
      weight: 25,
      description: '全身板甲',
      concealable: false,
      dexterityPenalty: 2
    }
  }

  /**
   * 弹药数据库
   */
  static ammoDatabase = {
    pistol_ammo: {
      id: 'pistol_ammo',
      name: '手枪子弹',
      type: 'pistol',
      cost: 2,
      weight: 0.1,
      description: '标准手枪弹药'
    },
    rifle_ammo: {
      id: 'rifle_ammo',
      name: '步枪子弹',
      type: 'rifle',
      cost: 3,
      weight: 0.2,
      description: '高威力步枪弹药'
    },
    shotgun_shells: {
      id: 'shotgun_shells',
      name: '霰弹',
      type: 'shotgun',
      cost: 4,
      weight: 0.3,
      description: '霰弹枪弹药'
    },
    arrows: {
      id: 'arrows',
      name: '箭矢',
      type: 'bow',
      cost: 1,
      weight: 0.1,
      description: '弓箭用箭矢'
    }
  }

  /**
   * 装备武器
   * @param {Object} character 角色
   * @param {string} weaponId 武器ID
   * @returns {Object} 装备结果
   */
  static equipWeapon(character, weaponId) {
    const weapon = this.getWeaponFromInventory(character, weaponId)
    if (!weapon) {
      return { success: false, reason: '没有该武器' }
    }

    // 检查是否需要双手
    if (weapon.twoHanded && character.currentShield) {
      return { 
        success: false, 
        reason: '双手武器无法与盾牌同时使用',
        requiresChoice: true,
        options: ['卸下盾牌', '选择其他武器']
      }
    }

    // 保存之前的武器
    const previousWeapon = character.currentWeapon

    // 装备新武器
    character.currentWeapon = { ...weapon }
    
    // 计算装备时间
    const equipTime = this.calculateEquipTime(weapon, previousWeapon)

    return {
      success: true,
      previousWeapon: previousWeapon?.name || '无',
      newWeapon: weapon.name,
      equipTime,
      description: `装备${weapon.name}${equipTime > 0 ? ` (需要${equipTime}回合)` : ''}`
    }
  }

  /**
   * 装备护甲
   * @param {Object} character 角色
   * @param {string} armorId 护甲ID
   * @returns {Object} 装备结果
   */
  static equipArmor(character, armorId) {
    const armor = this.armorDatabase[armorId]
    if (!armor) {
      return { success: false, reason: '护甲不存在' }
    }

    // 检查是否拥有该护甲
    if (!this.hasArmorInInventory(character, armorId)) {
      return { success: false, reason: '没有该护甲' }
    }

    const previousArmor = character.currentArmor
    character.currentArmor = { ...armor }

    // 应用敏捷惩罚
    if (armor.dexterityPenalty) {
      if (!character.tempPenalties) character.tempPenalties = {}
      character.tempPenalties.dexterity = (character.tempPenalties.dexterity || 0) + armor.dexterityPenalty
    }

    return {
      success: true,
      previousArmor: previousArmor?.name || '无',
      newArmor: armor.name,
      protection: armor.protection,
      description: `装备${armor.name} (防护值${armor.protection})`
    }
  }

  /**
   * 切换武器
   * @param {Object} character 角色
   * @param {string} weaponId 目标武器ID
   * @returns {Object} 切换结果
   */
  static switchWeapon(character, weaponId) {
    const currentWeapon = character.currentWeapon
    const targetWeapon = this.getWeaponFromInventory(character, weaponId)

    if (!targetWeapon) {
      return { success: false, reason: '没有该武器' }
    }

    if (currentWeapon && currentWeapon.id === weaponId) {
      return { success: false, reason: '已经装备该武器' }
    }

    // 计算切换时间
    const switchTime = this.calculateSwitchTime(currentWeapon, targetWeapon)

    // 执行切换
    character.currentWeapon = { ...targetWeapon }

    return {
      success: true,
      fromWeapon: currentWeapon?.name || '无',
      toWeapon: targetWeapon.name,
      switchTime,
      description: `从${currentWeapon?.name || '无'}切换到${targetWeapon.name} (需要${switchTime}回合)`
    }
  }

  /**
   * 装填弹药
   * @param {Object} character 角色
   * @param {Object} weapon 武器 (可选，默认当前武器)
   * @returns {Object} 装填结果
   */
  static reloadWeapon(character, weapon = null) {
    const targetWeapon = weapon || character.currentWeapon
    
    if (!targetWeapon) {
      return { success: false, reason: '没有武器需要装填' }
    }

    if (!targetWeapon.ammoType) {
      return { success: false, reason: '该武器不需要弹药' }
    }

    if (targetWeapon.currentAmmo >= targetWeapon.ammo) {
      return { success: false, reason: '弹药已满' }
    }

    // 检查弹药库存
    const ammoCount = this.getAmmoCount(character, targetWeapon.ammoType)
    if (ammoCount <= 0) {
      return { success: false, reason: '没有对应弹药' }
    }

    // 计算需要的弹药数量
    const neededAmmo = targetWeapon.ammo - targetWeapon.currentAmmo
    const reloadAmount = Math.min(neededAmmo, ammoCount)

    // 消耗弹药
    this.consumeAmmo(character, targetWeapon.ammoType, reloadAmount)

    // 装填弹药
    targetWeapon.currentAmmo += reloadAmount

    return {
      success: true,
      weapon: targetWeapon.name,
      reloadAmount,
      currentAmmo: targetWeapon.currentAmmo,
      maxAmmo: targetWeapon.ammo,
      reloadTime: targetWeapon.reloadTime || 1,
      description: `为${targetWeapon.name}装填${reloadAmount}发子弹 (${targetWeapon.currentAmmo}/${targetWeapon.ammo})`
    }
  }

  /**
   * 射击消耗弹药
   * @param {Object} weapon 武器
   * @param {number} shots 射击次数
   * @returns {Object} 消耗结果
   */
  static consumeAmmoForShooting(weapon, shots = 1) {
    if (!weapon.ammoType) {
      return { success: true, ammoConsumed: 0 }
    }

    if (weapon.currentAmmo < shots) {
      return { 
        success: false, 
        reason: '弹药不足',
        currentAmmo: weapon.currentAmmo,
        requiredAmmo: shots
      }
    }

    weapon.currentAmmo -= shots

    return {
      success: true,
      ammoConsumed: shots,
      remainingAmmo: weapon.currentAmmo,
      description: `消耗${shots}发子弹，剩余${weapon.currentAmmo}发`
    }
  }

  /**
   * 检查武器故障
   * @param {Object} weapon 武器
   * @param {number} roll 投掷结果
   * @returns {boolean} 是否故障
   */
  static checkWeaponMalfunction(weapon, roll) {
    if (!weapon.malfunction) return false
    return roll >= weapon.malfunction
  }

  /**
   * 修理武器
   * @param {Object} character 角色
   * @param {Object} weapon 武器
   * @returns {Object} 修理结果
   */
  static repairWeapon(character, weapon) {
    if (!weapon.malfunctioned) {
      return { success: false, reason: '武器没有故障' }
    }

    const repairSkill = character.mechanical_repair || character.skills?.mechanical_repair || 20
    const roll = diceRoller.rollD100()
    const success = roll <= repairSkill

    if (success) {
      weapon.malfunctioned = false
      return {
        success: true,
        roll,
        skill: repairSkill,
        weapon: weapon.name,
        description: `成功修理${weapon.name}`
      }
    } else {
      return {
        success: false,
        roll,
        skill: repairSkill,
        weapon: weapon.name,
        description: `修理${weapon.name}失败`
      }
    }
  }

  /**
   * 计算装备时间
   * @param {Object} newWeapon 新武器
   * @param {Object} oldWeapon 旧武器
   * @returns {number} 装备时间 (回合数)
   */
  static calculateEquipTime(newWeapon, oldWeapon) {
    let time = 0

    // 卸下旧武器的时间
    if (oldWeapon) {
      if (oldWeapon.twoHanded) time += 1
      else time += 0.5
    }

    // 装备新武器的时间
    if (newWeapon.twoHanded) time += 1
    else time += 0.5

    // 特殊武器需要额外时间
    if (newWeapon.type === 'firearm') time += 0.5

    return Math.ceil(time)
  }

  /**
   * 计算切换时间
   * @param {Object} fromWeapon 原武器
   * @param {Object} toWeapon 目标武器
   * @returns {number} 切换时间 (回合数)
   */
  static calculateSwitchTime(fromWeapon, toWeapon) {
    let time = 1 // 基础切换时间

    // 双手武器需要更多时间
    if (fromWeapon?.twoHanded) time += 0.5
    if (toWeapon.twoHanded) time += 0.5

    // 从近战切换到远程需要额外时间
    if (fromWeapon?.type === 'melee' && toWeapon.type === 'firearm') {
      time += 0.5
    }

    return Math.ceil(time)
  }

  /**
   * 获取武器伤害 (考虑距离)
   * @param {Object} weapon 武器
   * @param {number} distance 距离
   * @returns {string} 伤害公式
   */
  static getWeaponDamage(weapon, distance = 1) {
    if (weapon.id === 'shotgun' && weapon.spread) {
      // 霰弹枪根据距离调整伤害
      if (distance <= weapon.range.base / 3) return '4d6' // 近距离
      if (distance <= weapon.range.base * 2 / 3) return '2d6' // 中距离
      return '1d6' // 远距离
    }

    return weapon.damage
  }

  /**
   * 从背包获取武器
   * @param {Object} character 角色
   * @param {string} weaponId 武器ID
   * @returns {Object|null} 武器对象
   */
  static getWeaponFromInventory(character, weaponId) {
    if (!character.inventory) return null

    // 在武器数据库中查找
    for (const category of Object.values(this.weaponDatabase)) {
      if (category[weaponId]) {
        // 检查角色是否拥有该武器
        const inventoryItem = character.inventory[weaponId]
        if (inventoryItem && inventoryItem.quantity > 0) {
          return {
            ...category[weaponId],
            currentAmmo: inventoryItem.currentAmmo || category[weaponId].currentAmmo
          }
        }
      }
    }

    return null
  }

  /**
   * 检查是否拥有护甲
   * @param {Object} character 角色
   * @param {string} armorId 护甲ID
   * @returns {boolean} 是否拥有
   */
  static hasArmorInInventory(character, armorId) {
    if (!character.inventory) return false
    const inventoryItem = character.inventory[armorId]
    return inventoryItem && inventoryItem.quantity > 0
  }

  /**
   * 获取弹药数量
   * @param {Object} character 角色
   * @param {string} ammoType 弹药类型
   * @returns {number} 弹药数量
   */
  static getAmmoCount(character, ammoType) {
    if (!character.inventory || !character.inventory[ammoType]) return 0
    return character.inventory[ammoType].quantity || 0
  }

  /**
   * 消耗弹药
   * @param {Object} character 角色
   * @param {string} ammoType 弹药类型
   * @param {number} amount 消耗数量
   */
  static consumeAmmo(character, ammoType, amount) {
    if (!character.inventory || !character.inventory[ammoType]) return

    character.inventory[ammoType].quantity = Math.max(0, 
      (character.inventory[ammoType].quantity || 0) - amount)

    if (character.inventory[ammoType].quantity <= 0) {
      delete character.inventory[ammoType]
    }
  }

  /**
   * 获取角色的所有武器
   * @param {Object} character 角色
   * @returns {Array} 武器列表
   */
  static getCharacterWeapons(character) {
    const weapons = []

    if (!character.inventory) return weapons

    for (const [category, weaponList] of Object.entries(this.weaponDatabase)) {
      for (const [weaponId, weaponData] of Object.entries(weaponList)) {
        const inventoryItem = character.inventory[weaponId]
        if (inventoryItem && inventoryItem.quantity > 0) {
          weapons.push({
            ...weaponData,
            currentAmmo: inventoryItem.currentAmmo || weaponData.currentAmmo,
            quantity: inventoryItem.quantity
          })
        }
      }
    }

    return weapons
  }

  /**
   * 获取武器详细信息
   * @param {string} weaponId 武器ID
   * @returns {Object|null} 武器详细信息
   */
  static getWeaponDetails(weaponId) {
    for (const category of Object.values(this.weaponDatabase)) {
      if (category[weaponId]) {
        return { ...category[weaponId] }
      }
    }
    return null
  }

  /**
   * 获取护甲详细信息
   * @param {string} armorId 护甲ID
   * @returns {Object|null} 护甲详细信息
   */
  static getArmorDetails(armorId) {
    return this.armorDatabase[armorId] ? { ...this.armorDatabase[armorId] } : null
  }
}

export default WeaponSystem