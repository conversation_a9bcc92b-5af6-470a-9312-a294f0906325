{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nvar _hoisted_1 = {\n  key: 0\n};\nvar _hoisted_2 = [\"d\"];\nvar _hoisted_3 = [\"dur\"];\nvar _hoisted_4 = [\"values\", \"dur\"];\nvar _hoisted_5 = [\"cx\", \"cy\"];\nvar _hoisted_6 = [\"dur\", \"begin\"];\nvar _hoisted_7 = [\"dur\", \"begin\"];\nvar _hoisted_8 = {\n  key: 1\n};\nvar _hoisted_9 = [\"cx\", \"cy\", \"fill\"];\nvar _hoisted_10 = [\"dur\", \"path\"];\nvar _hoisted_11 = [\"dur\"];\nvar _hoisted_12 = [\"x1\", \"y1\", \"x2\", \"y2\"];\nvar _hoisted_13 = [\"dur\"];\nvar _hoisted_14 = [\"transform\"];\nvar _hoisted_15 = {\n  r: \"0\",\n  fill: \"none\",\n  stroke: \"#ffaa00\",\n  \"stroke-width\": \"3\",\n  opacity: \"0\"\n};\nvar _hoisted_16 = [\"dur\", \"begin\"];\nvar _hoisted_17 = [\"dur\", \"begin\"];\nvar _hoisted_18 = [\"x2\", \"y2\"];\nvar _hoisted_19 = [\"dur\", \"begin\"];\nvar _hoisted_20 = {\n  key: 2\n};\nvar _hoisted_21 = [\"cx\", \"cy\", \"fill\"];\nvar _hoisted_22 = [\"dur\", \"path\"];\nvar _hoisted_23 = [\"dur\"];\nvar _hoisted_24 = [\"cx\", \"cy\", \"fill\"];\nvar _hoisted_25 = [\"dur\", \"path\"];\nvar _hoisted_26 = [\"dur\"];\nvar _hoisted_27 = [\"transform\"];\nvar _hoisted_28 = [\"fill\"];\nvar _hoisted_29 = [\"dur\", \"begin\"];\nvar _hoisted_30 = [\"dur\", \"begin\"];\nvar _hoisted_31 = {\n  key: 3\n};\nvar _hoisted_32 = [\"d\"];\nvar _hoisted_33 = [\"dur\"];\nvar _hoisted_34 = [\"dur\"];\nvar _hoisted_35 = [\"cx\", \"cy\"];\nvar _hoisted_36 = [\"dur\", \"path\"];\nvar _hoisted_37 = [\"dur\"];\nvar _hoisted_38 = {\n  key: 4\n};\nvar _hoisted_39 = [\"cx\", \"cy\"];\nvar _hoisted_40 = [\"dur\"];\nvar _hoisted_41 = [\"dur\"];\nvar _hoisted_42 = [\"cx\", \"cy\"];\nvar _hoisted_43 = [\"dur\", \"path\"];\nvar _hoisted_44 = [\"dur\"];\nvar _hoisted_45 = [\"transform\"];\nvar _hoisted_46 = {\n  x1: \"-20\",\n  y1: \"0\",\n  x2: \"20\",\n  y2: \"0\",\n  stroke: \"#66bb6a\",\n  \"stroke-width\": \"4\",\n  opacity: \"0\"\n};\nvar _hoisted_47 = [\"dur\", \"begin\"];\nvar _hoisted_48 = {\n  x1: \"0\",\n  y1: \"-20\",\n  x2: \"0\",\n  y2: \"20\",\n  stroke: \"#66bb6a\",\n  \"stroke-width\": \"4\",\n  opacity: \"0\"\n};\nvar _hoisted_49 = [\"dur\", \"begin\"];\nvar _hoisted_50 = {\n  key: 5\n};\nvar _hoisted_51 = {\n  key: 0\n};\nvar _hoisted_52 = [\"cx\", \"cy\"];\nvar _hoisted_53 = [\"dur\"];\nvar _hoisted_54 = [\"cx\", \"cy\"];\nvar _hoisted_55 = [\"dur\"];\nvar _hoisted_56 = [\"values\", \"dur\"];\nvar _hoisted_57 = {\n  key: 1\n};\nvar _hoisted_58 = [\"d\"];\nvar _hoisted_59 = [\"dur\"];\nvar _hoisted_60 = {\n  key: 6\n};\nvar _hoisted_61 = [\"cx\", \"cy\"];\nvar _hoisted_62 = [\"values\", \"dur\"];\nvar _hoisted_63 = [\"dur\"];\nvar _hoisted_64 = [\"transform\"];\nvar _hoisted_65 = [\"x2\", \"y2\"];\nvar _hoisted_66 = [\"dur\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 战斗动画效果组件 \"), _createElementVNode(\"g\", {\n    \"class\": _normalizeClass([\"combat-animation\", $props.animation.type])\n  }, [_createCommentVNode(\" 近战攻击动画 \"), $props.animation.type === 'melee_attack' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_1, [_createCommentVNode(\" 攻击轨迹 \"), _createElementVNode(\"path\", {\n    d: $options.getMeleeAttackPath(),\n    fill: \"none\",\n    stroke: \"#ff4444\",\n    \"stroke-width\": \"4\",\n    \"stroke-linecap\": \"round\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_3), _createElementVNode(\"animate\", {\n    attributeName: \"stroke-dasharray\",\n    values: \"0,\".concat($options.getPathLength(), \";\").concat($options.getPathLength(), \",0\"),\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_4)], 8 /* PROPS */, _hoisted_2), _createCommentVNode(\" 冲击波效果 \"), _createElementVNode(\"circle\", {\n    cx: $options.toX,\n    cy: $options.toY,\n    r: \"0\",\n    fill: \"none\",\n    stroke: \"#ff6666\",\n    \"stroke-width\": \"2\",\n    opacity: \"0.8\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"0;30;0\",\n    dur: $options.duration * 0.3 + 'ms',\n    begin: $options.duration * 0.7 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0.8;0\",\n    dur: $options.duration * 0.3 + 'ms',\n    begin: $options.duration * 0.7 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_7)], 8 /* PROPS */, _hoisted_5)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 远程攻击动画 \"), $props.animation.type === 'ranged_attack' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_8, [_createCommentVNode(\" 弹道轨迹 \"), _createElementVNode(\"circle\", {\n    cx: $options.fromX,\n    cy: $options.fromY,\n    r: \"3\",\n    fill: $options.getProjectileColor(),\n    opacity: \"0\"\n  }, [_createElementVNode(\"animateMotion\", {\n    dur: $options.duration + 'ms',\n    path: $options.getProjectilePath()\n  }, null, 8 /* PROPS */, _hoisted_10), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;1;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_11)], 8 /* PROPS */, _hoisted_9), _createCommentVNode(\" 轨迹线 \"), _createElementVNode(\"line\", {\n    x1: $options.fromX,\n    y1: $options.fromY,\n    x2: $options.toX,\n    y2: $options.toY,\n    stroke: \"#ffaa00\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"5,5\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;0.6;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_13)], 8 /* PROPS */, _hoisted_12), _createCommentVNode(\" 命中效果 \"), _createElementVNode(\"g\", {\n    transform: \"translate(\".concat($options.toX, \", \").concat($options.toY, \")\")\n  }, [_createElementVNode(\"circle\", _hoisted_15, [_createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"0;25\",\n    dur: $options.duration * 0.2 + 'ms',\n    begin: $options.duration * 0.8 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_16), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;0\",\n    dur: $options.duration * 0.2 + 'ms',\n    begin: $options.duration * 0.8 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_17)]), _createCommentVNode(\" 火花效果 \"), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(8, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"line\", {\n      x1: \"0\",\n      y1: \"0\",\n      x2: Math.cos(i * Math.PI / 4) * 15,\n      y2: Math.sin(i * Math.PI / 4) * 15,\n      stroke: \"#ffdd00\",\n      \"stroke-width\": \"2\",\n      opacity: \"0\"\n    }, [_createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;1;0\",\n      dur: $options.duration * 0.3 + 'ms',\n      begin: $options.duration * 0.8 + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_19)], 8 /* PROPS */, _hoisted_18)]);\n  }), 64 /* STABLE_FRAGMENT */))], 8 /* PROPS */, _hoisted_14)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 法术攻击动画 \"), $props.animation.type === 'spell_attack' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_20, [_createCommentVNode(\" 魔法光球 \"), _createElementVNode(\"circle\", {\n    cx: $options.fromX,\n    cy: $options.fromY,\n    r: \"8\",\n    fill: $options.getSpellColor(),\n    opacity: \"0\"\n  }, [_createElementVNode(\"animateMotion\", {\n    dur: $options.duration + 'ms',\n    path: $options.getSpellPath()\n  }, null, 8 /* PROPS */, _hoisted_22), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;1;0.5\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_23), _cache[0] || (_cache[0] = _createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"8;12;8\",\n    dur: 500 + 'ms',\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */))], 8 /* PROPS */, _hoisted_21), _createCommentVNode(\" 魔法粒子效果 \"), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(12, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"circle\", {\n      cx: $options.fromX + Math.cos(i * Math.PI / 6) * 20,\n      cy: $options.fromY + Math.sin(i * Math.PI / 6) * 20,\n      r: \"2\",\n      fill: $options.getSpellColor(),\n      opacity: \"0\"\n    }, [_createElementVNode(\"animateMotion\", {\n      dur: $options.duration + 'ms',\n      path: $options.getParticlePath(i)\n    }, null, 8 /* PROPS */, _hoisted_25), _createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;0.8;0\",\n      dur: $options.duration + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_26)], 8 /* PROPS */, _hoisted_24)]);\n  }), 64 /* STABLE_FRAGMENT */)), _createCommentVNode(\" 爆炸效果 \"), _createElementVNode(\"g\", {\n    transform: \"translate(\".concat($options.toX, \", \").concat($options.toY, \")\")\n  }, [_createElementVNode(\"circle\", {\n    r: \"0\",\n    fill: $options.getSpellColor(),\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"0;40;60\",\n    dur: $options.duration * 0.4 + 'ms',\n    begin: $options.duration * 0.8 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_29), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;0.8;0\",\n    dur: $options.duration * 0.4 + 'ms',\n    begin: $options.duration * 0.8 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_30)], 8 /* PROPS */, _hoisted_28)], 8 /* PROPS */, _hoisted_27)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 移动动画 \"), $props.animation.type === 'movement' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_31, [_createCommentVNode(\" 移动轨迹 \"), _createElementVNode(\"path\", {\n    d: $options.getMovementPath(),\n    fill: \"none\",\n    stroke: \"#4ecdc4\",\n    \"stroke-width\": \"3\",\n    \"stroke-dasharray\": \"8,4\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;0.8;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_33), _createElementVNode(\"animate\", {\n    attributeName: \"stroke-dashoffset\",\n    values: \"0;-100\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_34)], 8 /* PROPS */, _hoisted_32), _createCommentVNode(\" 移动粒子 \"), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(6, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"circle\", {\n      cx: $options.fromX,\n      cy: $options.fromY,\n      r: \"3\",\n      fill: \"#4ecdc4\",\n      opacity: \"0\"\n    }, [_createElementVNode(\"animateMotion\", {\n      dur: $options.duration + i * 100 + 'ms',\n      path: $options.getMovementPath()\n    }, null, 8 /* PROPS */, _hoisted_36), _createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;0.6;0\",\n      dur: $options.duration + i * 100 + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_37)], 8 /* PROPS */, _hoisted_35)]);\n  }), 64 /* STABLE_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 治疗动画 \"), $props.animation.type === 'healing' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_38, [_createCommentVNode(\" 治疗光环 \"), _createElementVNode(\"circle\", {\n    cx: $options.toX,\n    cy: $options.toY,\n    r: \"0\",\n    fill: \"none\",\n    stroke: \"#4caf50\",\n    \"stroke-width\": \"3\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"0;50;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_40), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;0.8;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_41)], 8 /* PROPS */, _hoisted_39), _createCommentVNode(\" 治疗粒子 \"), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(16, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"circle\", {\n      cx: $options.toX + Math.cos(i * Math.PI / 8) * 60,\n      cy: $options.toY + Math.sin(i * Math.PI / 8) * 60,\n      r: \"4\",\n      fill: \"#4caf50\",\n      opacity: \"0\"\n    }, [_createElementVNode(\"animateMotion\", {\n      dur: $options.duration + 'ms',\n      path: \"M 0,0 L \".concat(-Math.cos(i * Math.PI / 8) * 60, \",\").concat(-Math.sin(i * Math.PI / 8) * 60)\n    }, null, 8 /* PROPS */, _hoisted_43), _createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;1;0\",\n      dur: $options.duration + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_44)], 8 /* PROPS */, _hoisted_42)]);\n  }), 64 /* STABLE_FRAGMENT */)), _createCommentVNode(\" 十字光效 \"), _createElementVNode(\"g\", {\n    transform: \"translate(\".concat($options.toX, \", \").concat($options.toY, \")\")\n  }, [_createElementVNode(\"line\", _hoisted_46, [_createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;0\",\n    dur: $options.duration * 0.5 + 'ms',\n    begin: $options.duration * 0.3 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_47)]), _createElementVNode(\"line\", _hoisted_48, [_createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;0\",\n    dur: $options.duration * 0.5 + 'ms',\n    begin: $options.duration * 0.3 + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_49)])], 8 /* PROPS */, _hoisted_45)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 状态效果动画 \"), $props.animation.type === 'status_effect' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_50, [_createCommentVNode(\" 毒素效果 \"), $props.animation.effect === 'poison' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_51, [_createElementVNode(\"circle\", {\n    cx: $options.toX,\n    cy: $options.toY,\n    r: \"25\",\n    fill: \"none\",\n    stroke: \"#8bc34a\",\n    \"stroke-width\": \"2\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;0.6;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_53), _cache[1] || (_cache[1] = _createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"25;35;25\",\n    dur: 1000 + 'ms',\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */))], 8 /* PROPS */, _hoisted_52), _createCommentVNode(\" 毒气泡 \"), (_openBlock(), _createElementBlock(_Fragment, null, _renderList(8, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"circle\", {\n      cx: $options.toX + Math.cos(i * Math.PI / 4) * 20,\n      cy: $options.toY + Math.sin(i * Math.PI / 4) * 20,\n      r: \"3\",\n      fill: \"#689f38\",\n      opacity: \"0\"\n    }, [_createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;0.8;0\",\n      dur: $options.duration + i * 200 + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_55), _createElementVNode(\"animate\", {\n      attributeName: \"cy\",\n      values: \"\".concat($options.toY + Math.sin(i * Math.PI / 4) * 20, \";\").concat($options.toY + Math.sin(i * Math.PI / 4) * 20 - 30),\n      dur: $options.duration + i * 200 + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_56)], 8 /* PROPS */, _hoisted_54)]);\n  }), 64 /* STABLE_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 燃烧效果 \"), $props.animation.effect === 'burning' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_57, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(12, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"path\", {\n      d: \"M \".concat($options.toX + Math.cos(i * Math.PI / 6) * 15, \" \").concat($options.toY + Math.sin(i * Math.PI / 6) * 15, \" Q \").concat($options.toX + Math.cos(i * Math.PI / 6) * 10, \" \").concat($options.toY + Math.sin(i * Math.PI / 6) * 10 - 20, \" \").concat($options.toX + Math.cos(i * Math.PI / 6) * 5, \" \").concat($options.toY + Math.sin(i * Math.PI / 6) * 5 - 30),\n      fill: \"none\",\n      stroke: \"#ff5722\",\n      \"stroke-width\": \"2\",\n      opacity: \"0\"\n    }, [_createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;1;0\",\n      dur: $options.duration + i * 100 + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_59)], 8 /* PROPS */, _hoisted_58)]);\n  }), 64 /* STABLE_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 死亡动画 \"), $props.animation.type === 'death' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_60, [_createCommentVNode(\" 灵魂上升 \"), _createElementVNode(\"circle\", {\n    cx: $options.toX,\n    cy: $options.toY,\n    r: \"8\",\n    fill: \"#e0e0e0\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"cy\",\n    values: \"\".concat($options.toY, \";\").concat($options.toY - 100),\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_62), _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;0.8;0\",\n    dur: $options.duration + 'ms'\n  }, null, 8 /* PROPS */, _hoisted_63), _cache[2] || (_cache[2] = _createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"8;12;8\",\n    dur: 1000 + 'ms',\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */))], 8 /* PROPS */, _hoisted_61), _createCommentVNode(\" 光芒效果 \"), _createElementVNode(\"g\", {\n    transform: \"translate(\".concat($options.toX, \", \").concat($options.toY, \")\")\n  }, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(8, function (i) {\n    return _createElementVNode(\"g\", {\n      key: i\n    }, [_createElementVNode(\"line\", {\n      x1: \"0\",\n      y1: \"0\",\n      x2: Math.cos(i * Math.PI / 4) * 40,\n      y2: Math.sin(i * Math.PI / 4) * 40,\n      stroke: \"#ffffff\",\n      \"stroke-width\": \"2\",\n      opacity: \"0\"\n    }, [_createElementVNode(\"animate\", {\n      attributeName: \"opacity\",\n      values: \"0;0.8;0\",\n      dur: $options.duration * 0.5 + 'ms'\n    }, null, 8 /* PROPS */, _hoisted_66)], 8 /* PROPS */, _hoisted_65)]);\n  }), 64 /* STABLE_FRAGMENT */))], 8 /* PROPS */, _hoisted_64)])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["r", "fill", "stroke", "opacity", "x1", "y1", "x2", "y2", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "$props", "animation", "type", "_createElementBlock", "_hoisted_1", "d", "$options", "getMeleeAttackPath", "attributeName", "values", "dur", "duration", "concat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cx", "toX", "cy", "toY", "begin", "_hoisted_8", "fromX", "fromY", "getProjectileColor", "path", "getProjectilePath", "transform", "_hoisted_15", "_Fragment", "_renderList", "i", "key", "Math", "cos", "PI", "sin", "_hoisted_20", "getSpellColor", "getSpellPath", "repeatCount", "getParticlePath", "_hoisted_31", "getMovementPath", "_hoisted_38", "_hoisted_46", "_hoisted_48", "_hoisted_50", "effect", "_hoisted_51", "_hoisted_57", "_hoisted_60"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatAnimation.vue"], "sourcesContent": ["<template>\r\n  <!-- 战斗动画效果组件 -->\r\n  <g class=\"combat-animation\" :class=\"animation.type\">\r\n    <!-- 近战攻击动画 -->\r\n    <g v-if=\"animation.type === 'melee_attack'\">\r\n      <!-- 攻击轨迹 -->\r\n      <path \r\n        :d=\"getMeleeAttackPath()\"\r\n        fill=\"none\"\r\n        stroke=\"#ff4444\"\r\n        stroke-width=\"4\"\r\n        stroke-linecap=\"round\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"stroke-dasharray\" \r\n          :values=\"`0,${getPathLength()};${getPathLength()},0`\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </path>\r\n      \r\n      <!-- 冲击波效果 -->\r\n      <circle \r\n        :cx=\"toX\"\r\n        :cy=\"toY\"\r\n        r=\"0\"\r\n        fill=\"none\"\r\n        stroke=\"#ff6666\"\r\n        stroke-width=\"2\"\r\n        opacity=\"0.8\"\r\n      >\r\n        <animate \r\n          attributeName=\"r\" \r\n          values=\"0;30;0\"\r\n          :dur=\"duration * 0.3 + 'ms'\"\r\n          :begin=\"duration * 0.7 + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0.8;0\"\r\n          :dur=\"duration * 0.3 + 'ms'\"\r\n          :begin=\"duration * 0.7 + 'ms'\"\r\n        />\r\n      </circle>\r\n    </g>\r\n    \r\n    <!-- 远程攻击动画 -->\r\n    <g v-if=\"animation.type === 'ranged_attack'\">\r\n      <!-- 弹道轨迹 -->\r\n      <circle \r\n        :cx=\"fromX\"\r\n        :cy=\"fromY\"\r\n        r=\"3\"\r\n        :fill=\"getProjectileColor()\"\r\n        opacity=\"0\"\r\n      >\r\n        <animateMotion \r\n          :dur=\"duration + 'ms'\"\r\n          :path=\"getProjectilePath()\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </circle>\r\n      \r\n      <!-- 轨迹线 -->\r\n      <line \r\n        :x1=\"fromX\"\r\n        :y1=\"fromY\"\r\n        :x2=\"toX\"\r\n        :y2=\"toY\"\r\n        stroke=\"#ffaa00\"\r\n        stroke-width=\"2\"\r\n        stroke-dasharray=\"5,5\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;0.6;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </line>\r\n      \r\n      <!-- 命中效果 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <circle \r\n          r=\"0\"\r\n          fill=\"none\"\r\n          stroke=\"#ffaa00\"\r\n          stroke-width=\"3\"\r\n          opacity=\"0\"\r\n        >\r\n          <animate \r\n            attributeName=\"r\" \r\n            values=\"0;25\"\r\n            :dur=\"duration * 0.2 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;1;0\"\r\n            :dur=\"duration * 0.2 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n        </circle>\r\n        \r\n        <!-- 火花效果 -->\r\n        <g v-for=\"i in 8\" :key=\"i\">\r\n          <line \r\n            x1=\"0\" y1=\"0\"\r\n            :x2=\"Math.cos(i * Math.PI / 4) * 15\"\r\n            :y2=\"Math.sin(i * Math.PI / 4) * 15\"\r\n            stroke=\"#ffdd00\"\r\n            stroke-width=\"2\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate \r\n              attributeName=\"opacity\" \r\n              values=\"0;1;0\"\r\n              :dur=\"duration * 0.3 + 'ms'\"\r\n              :begin=\"duration * 0.8 + 'ms'\"\r\n            />\r\n          </line>\r\n        </g>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 法术攻击动画 -->\r\n    <g v-if=\"animation.type === 'spell_attack'\">\r\n      <!-- 魔法光球 -->\r\n      <circle \r\n        :cx=\"fromX\"\r\n        :cy=\"fromY\"\r\n        r=\"8\"\r\n        :fill=\"getSpellColor()\"\r\n        opacity=\"0\"\r\n      >\r\n        <animateMotion \r\n          :dur=\"duration + 'ms'\"\r\n          :path=\"getSpellPath()\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0.5\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"r\" \r\n          values=\"8;12;8\"\r\n          :dur=\"500 + 'ms'\"\r\n          repeatCount=\"indefinite\"\r\n        />\r\n      </circle>\r\n      \r\n      <!-- 魔法粒子效果 -->\r\n      <g v-for=\"i in 12\" :key=\"i\">\r\n        <circle \r\n          :cx=\"fromX + Math.cos(i * Math.PI / 6) * 20\"\r\n          :cy=\"fromY + Math.sin(i * Math.PI / 6) * 20\"\r\n          r=\"2\"\r\n          :fill=\"getSpellColor()\"\r\n          opacity=\"0\"\r\n        >\r\n          <animateMotion \r\n            :dur=\"duration + 'ms'\"\r\n            :path=\"getParticlePath(i)\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;0.8;0\"\r\n            :dur=\"duration + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n      \r\n      <!-- 爆炸效果 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <circle \r\n          r=\"0\"\r\n          :fill=\"getSpellColor()\"\r\n          opacity=\"0\"\r\n        >\r\n          <animate \r\n            attributeName=\"r\" \r\n            values=\"0;40;60\"\r\n            :dur=\"duration * 0.4 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;0.8;0\"\r\n            :dur=\"duration * 0.4 + 'ms'\"\r\n            :begin=\"duration * 0.8 + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 移动动画 -->\r\n    <g v-if=\"animation.type === 'movement'\">\r\n      <!-- 移动轨迹 -->\r\n      <path \r\n        :d=\"getMovementPath()\"\r\n        fill=\"none\"\r\n        stroke=\"#4ecdc4\"\r\n        stroke-width=\"3\"\r\n        stroke-dasharray=\"8,4\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;0.8;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"stroke-dashoffset\" \r\n          values=\"0;-100\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </path>\r\n      \r\n      <!-- 移动粒子 -->\r\n      <g v-for=\"i in 6\" :key=\"i\">\r\n        <circle \r\n          :cx=\"fromX\"\r\n          :cy=\"fromY\"\r\n          r=\"3\"\r\n          fill=\"#4ecdc4\"\r\n          opacity=\"0\"\r\n        >\r\n          <animateMotion \r\n            :dur=\"duration + i * 100 + 'ms'\"\r\n            :path=\"getMovementPath()\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;0.6;0\"\r\n            :dur=\"duration + i * 100 + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 治疗动画 -->\r\n    <g v-if=\"animation.type === 'healing'\">\r\n      <!-- 治疗光环 -->\r\n      <circle \r\n        :cx=\"toX\"\r\n        :cy=\"toY\"\r\n        r=\"0\"\r\n        fill=\"none\"\r\n        stroke=\"#4caf50\"\r\n        stroke-width=\"3\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"r\" \r\n          values=\"0;50;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;0.8;0\"\r\n          :dur=\"duration + 'ms'\"\r\n        />\r\n      </circle>\r\n      \r\n      <!-- 治疗粒子 -->\r\n      <g v-for=\"i in 16\" :key=\"i\">\r\n        <circle \r\n          :cx=\"toX + Math.cos(i * Math.PI / 8) * 60\"\r\n          :cy=\"toY + Math.sin(i * Math.PI / 8) * 60\"\r\n          r=\"4\"\r\n          fill=\"#4caf50\"\r\n          opacity=\"0\"\r\n        >\r\n          <animateMotion \r\n            :dur=\"duration + 'ms'\"\r\n            :path=\"`M 0,0 L ${-Math.cos(i * Math.PI / 8) * 60},${-Math.sin(i * Math.PI / 8) * 60}`\"\r\n          />\r\n          <animate \r\n            attributeName=\"opacity\" \r\n            values=\"0;1;0\"\r\n            :dur=\"duration + 'ms'\"\r\n          />\r\n        </circle>\r\n      </g>\r\n      \r\n      <!-- 十字光效 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <line x1=\"-20\" y1=\"0\" x2=\"20\" y2=\"0\" stroke=\"#66bb6a\" stroke-width=\"4\" opacity=\"0\">\r\n          <animate attributeName=\"opacity\" values=\"0;1;0\" :dur=\"duration * 0.5 + 'ms'\" :begin=\"duration * 0.3 + 'ms'\"/>\r\n        </line>\r\n        <line x1=\"0\" y1=\"-20\" x2=\"0\" y2=\"20\" stroke=\"#66bb6a\" stroke-width=\"4\" opacity=\"0\">\r\n          <animate attributeName=\"opacity\" values=\"0;1;0\" :dur=\"duration * 0.5 + 'ms'\" :begin=\"duration * 0.3 + 'ms'\"/>\r\n        </line>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 状态效果动画 -->\r\n    <g v-if=\"animation.type === 'status_effect'\">\r\n      <!-- 毒素效果 -->\r\n      <g v-if=\"animation.effect === 'poison'\">\r\n        <circle \r\n          :cx=\"toX\"\r\n          :cy=\"toY\"\r\n          r=\"25\"\r\n          fill=\"none\"\r\n          stroke=\"#8bc34a\"\r\n          stroke-width=\"2\"\r\n          opacity=\"0\"\r\n        >\r\n          <animate attributeName=\"opacity\" values=\"0;0.6;0\" :dur=\"duration + 'ms'\"/>\r\n          <animate attributeName=\"r\" values=\"25;35;25\" :dur=\"1000 + 'ms'\" repeatCount=\"indefinite\"/>\r\n        </circle>\r\n        \r\n        <!-- 毒气泡 -->\r\n        <g v-for=\"i in 8\" :key=\"i\">\r\n          <circle \r\n            :cx=\"toX + Math.cos(i * Math.PI / 4) * 20\"\r\n            :cy=\"toY + Math.sin(i * Math.PI / 4) * 20\"\r\n            r=\"3\"\r\n            fill=\"#689f38\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate attributeName=\"opacity\" values=\"0;0.8;0\" :dur=\"duration + i * 200 + 'ms'\"/>\r\n            <animate attributeName=\"cy\" :values=\"`${toY + Math.sin(i * Math.PI / 4) * 20};${toY + Math.sin(i * Math.PI / 4) * 20 - 30}`\" :dur=\"duration + i * 200 + 'ms'\"/>\r\n          </circle>\r\n        </g>\r\n      </g>\r\n      \r\n      <!-- 燃烧效果 -->\r\n      <g v-if=\"animation.effect === 'burning'\">\r\n        <g v-for=\"i in 12\" :key=\"i\">\r\n          <path \r\n            :d=\"`M ${toX + Math.cos(i * Math.PI / 6) * 15} ${toY + Math.sin(i * Math.PI / 6) * 15} Q ${toX + Math.cos(i * Math.PI / 6) * 10} ${toY + Math.sin(i * Math.PI / 6) * 10 - 20} ${toX + Math.cos(i * Math.PI / 6) * 5} ${toY + Math.sin(i * Math.PI / 6) * 5 - 30}`\"\r\n            fill=\"none\"\r\n            stroke=\"#ff5722\"\r\n            stroke-width=\"2\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate attributeName=\"opacity\" values=\"0;1;0\" :dur=\"duration + i * 100 + 'ms'\"/>\r\n          </path>\r\n        </g>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 死亡动画 -->\r\n    <g v-if=\"animation.type === 'death'\">\r\n      <!-- 灵魂上升 -->\r\n      <circle \r\n        :cx=\"toX\"\r\n        :cy=\"toY\"\r\n        r=\"8\"\r\n        fill=\"#e0e0e0\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate attributeName=\"cy\" :values=\"`${toY};${toY - 100}`\" :dur=\"duration + 'ms'\"/>\r\n        <animate attributeName=\"opacity\" values=\"0;0.8;0\" :dur=\"duration + 'ms'\"/>\r\n        <animate attributeName=\"r\" values=\"8;12;8\" :dur=\"1000 + 'ms'\" repeatCount=\"indefinite\"/>\r\n      </circle>\r\n      \r\n      <!-- 光芒效果 -->\r\n      <g :transform=\"`translate(${toX}, ${toY})`\">\r\n        <g v-for=\"i in 8\" :key=\"i\">\r\n          <line \r\n            x1=\"0\" y1=\"0\"\r\n            :x2=\"Math.cos(i * Math.PI / 4) * 40\"\r\n            :y2=\"Math.sin(i * Math.PI / 4) * 40\"\r\n            stroke=\"#ffffff\"\r\n            stroke-width=\"2\"\r\n            opacity=\"0\"\r\n          >\r\n            <animate attributeName=\"opacity\" values=\"0;0.8;0\" :dur=\"duration * 0.5 + 'ms'\"/>\r\n          </line>\r\n        </g>\r\n      </g>\r\n    </g>\r\n  </g>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CombatAnimation',\r\n  props: {\r\n    animation: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    },\r\n    zoom: {\r\n      type: Number,\r\n      default: 1\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      startTime: Date.now()\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    duration() {\r\n      return this.animation.duration || 1000\r\n    },\r\n    \r\n    fromX() {\r\n      return (this.animation.from?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    fromY() {\r\n      return (this.animation.from?.y || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    toX() {\r\n      return (this.animation.to?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    toY() {\r\n      return (this.animation.to?.y || 0) * this.gridSize * this.zoom\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 动画完成后触发回调\r\n    setTimeout(() => {\r\n      this.$emit('complete', this.animation.id)\r\n    }, this.duration)\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 获取近战攻击路径\r\n     */\r\n    getMeleeAttackPath() {\r\n      const midX = (this.fromX + this.toX) / 2\r\n      const midY = (this.fromY + this.toY) / 2\r\n      \r\n      // 创建弧形攻击轨迹\r\n      return `M ${this.fromX} ${this.fromY} Q ${midX + 20} ${midY - 20} ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取弹道路径\r\n     */\r\n    getProjectilePath() {\r\n      const midX = (this.fromX + this.toX) / 2\r\n      const midY = (this.fromY + this.toY) / 2 - 30 // 抛物线效果\r\n      \r\n      return `M ${this.fromX} ${this.fromY} Q ${midX} ${midY} ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取法术路径\r\n     */\r\n    getSpellPath() {\r\n      // 法术通常是直线飞行\r\n      return `M ${this.fromX} ${this.fromY} L ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取粒子路径\r\n     */\r\n    getParticlePath(index) {\r\n      const angle = (index * Math.PI * 2) / 12\r\n      const offsetX = Math.cos(angle) * 10\r\n      const offsetY = Math.sin(angle) * 10\r\n      \r\n      return `M ${this.fromX + offsetX} ${this.fromY + offsetY} L ${this.toX + offsetX} ${this.toY + offsetY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取移动路径\r\n     */\r\n    getMovementPath() {\r\n      return `M ${this.fromX} ${this.fromY} L ${this.toX} ${this.toY}`\r\n    },\r\n    \r\n    /**\r\n     * 获取路径长度\r\n     */\r\n    getPathLength() {\r\n      const dx = this.toX - this.fromX\r\n      const dy = this.toY - this.fromY\r\n      return Math.sqrt(dx * dx + dy * dy)\r\n    },\r\n    \r\n    /**\r\n     * 获取弹道颜色\r\n     */\r\n    getProjectileColor() {\r\n      const weapon = this.animation.weapon\r\n      if (!weapon) return '#ffaa00'\r\n      \r\n      const colorMap = {\r\n        'arrow': '#8d6e63',\r\n        'bullet': '#607d8b',\r\n        'bolt': '#795548',\r\n        'stone': '#9e9e9e',\r\n        'dart': '#546e7a'\r\n      }\r\n      \r\n      return colorMap[weapon.projectileType] || '#ffaa00'\r\n    },\r\n    \r\n    /**\r\n     * 获取法术颜色\r\n     */\r\n    getSpellColor() {\r\n      const spell = this.animation.spell\r\n      if (!spell) return '#9c27b0'\r\n      \r\n      const colorMap = {\r\n        'fire': '#f44336',\r\n        'ice': '#2196f3',\r\n        'lightning': '#ffeb3b',\r\n        'acid': '#4caf50',\r\n        'necrotic': '#9c27b0',\r\n        'radiant': '#fff176',\r\n        'force': '#e91e63',\r\n        'psychic': '#673ab7'\r\n      }\r\n      \r\n      return colorMap[spell.damageType] || '#9c27b0'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.combat-animation {\r\n  pointer-events: none;\r\n}\r\n\r\n/* 近战攻击动画样式 */\r\n.combat-animation.melee_attack path {\r\n  filter: drop-shadow(0 0 4px rgba(255, 68, 68, 0.6));\r\n}\r\n\r\n/* 远程攻击动画样式 */\r\n.combat-animation.ranged_attack circle {\r\n  filter: drop-shadow(0 0 3px rgba(255, 170, 0, 0.8));\r\n}\r\n\r\n/* 法术攻击动画样式 */\r\n.combat-animation.spell_attack circle {\r\n  filter: drop-shadow(0 0 6px rgba(156, 39, 176, 0.8));\r\n}\r\n\r\n/* 移动动画样式 */\r\n.combat-animation.movement path {\r\n  filter: drop-shadow(0 0 2px rgba(78, 205, 196, 0.6));\r\n}\r\n\r\n/* 治疗动画样式 */\r\n.combat-animation.healing circle {\r\n  filter: drop-shadow(0 0 4px rgba(76, 175, 80, 0.8));\r\n}\r\n\r\n/* 状态效果动画样式 */\r\n.combat-animation.status_effect circle {\r\n  filter: drop-shadow(0 0 3px rgba(139, 195, 74, 0.6));\r\n}\r\n\r\n/* 死亡动画样式 */\r\n.combat-animation.death circle {\r\n  filter: drop-shadow(0 0 8px rgba(224, 224, 224, 0.9));\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;EA6FUA,CAAC,EAAC,GAAG;EACLC,IAAI,EAAC,MAAM;EACXC,MAAM,EAAC,SAAS;EAChB,cAAY,EAAC,GAAG;EAChBC,OAAO,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwMJC,EAAE,EAAC,KAAK;EAACC,EAAE,EAAC,GAAG;EAACC,EAAE,EAAC,IAAI;EAACC,EAAE,EAAC,GAAG;EAACL,MAAM,EAAC,SAAS;EAAC,cAAY,EAAC,GAAG;EAACC,OAAO,EAAC;;;;EAGzEC,EAAE,EAAC,GAAG;EAACC,EAAE,EAAC,KAAK;EAACC,EAAE,EAAC,GAAG;EAACC,EAAE,EAAC,IAAI;EAACL,MAAM,EAAC,SAAS;EAAC,cAAY,EAAC,GAAG;EAACC,OAAO,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;6DA3SrFK,mBAAA,cAAiB,EACjBC,mBAAA,CA+XI;IA/XD,SAAKC,eAAA,EAAC,kBAAkB,EAASC,MAAA,CAAAC,SAAS,CAACC,IAAI;MAChDL,mBAAA,YAAe,EACNG,MAAA,CAAAC,SAAS,CAACC,IAAI,uB,cAAvBC,mBAAA,CA6CI,KAAAC,UAAA,GA5CFP,mBAAA,UAAa,EACbC,mBAAA,CAkBO;IAjBJO,CAAC,EAAEC,QAAA,CAAAC,kBAAkB;IACtBjB,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,gBAAc,EAAC,OAAO;IACtBC,OAAO,EAAC;MAERM,mBAAA,CAIE;IAHAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,OAAO;IACbC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;uCAEhBb,mBAAA,CAIE;IAHAU,aAAa,EAAC,kBAAkB;IAC/BC,MAAM,OAAAG,MAAA,CAAON,QAAA,CAAAO,aAAa,SAAAD,MAAA,CAAMN,QAAA,CAAAO,aAAa;IAC7CH,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;oEAIlBd,mBAAA,WAAc,EACdC,mBAAA,CAqBS;IApBNgB,EAAE,EAAER,QAAA,CAAAS,GAAG;IACPC,EAAE,EAAEV,QAAA,CAAAW,GAAG;IACR5B,CAAC,EAAC,GAAG;IACLC,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChBC,OAAO,EAAC;MAERM,mBAAA,CAKE;IAJAU,aAAa,EAAC,GAAG;IACjBC,MAAM,EAAC,QAAQ;IACdC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;uCAElBb,mBAAA,CAKE;IAJAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,OAAO;IACbC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;2GAKtBd,mBAAA,YAAe,EACNG,MAAA,CAAAC,SAAS,CAACC,IAAI,wB,cAAvBC,mBAAA,CAgFI,KAAAgB,UAAA,GA/EFtB,mBAAA,UAAa,EACbC,mBAAA,CAgBS;IAfNgB,EAAE,EAAER,QAAA,CAAAc,KAAK;IACTJ,EAAE,EAAEV,QAAA,CAAAe,KAAK;IACVhC,CAAC,EAAC,GAAG;IACJC,IAAI,EAAEgB,QAAA,CAAAgB,kBAAkB;IACzB9B,OAAO,EAAC;MAERM,mBAAA,CAGE;IAFCY,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbY,IAAI,EAAEjB,QAAA,CAAAkB,iBAAiB;wCAE1B1B,mBAAA,CAIE;IAHAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;qEAIlBd,mBAAA,SAAY,EACZC,mBAAA,CAeO;IAdJL,EAAE,EAAEa,QAAA,CAAAc,KAAK;IACT1B,EAAE,EAAEY,QAAA,CAAAe,KAAK;IACT1B,EAAE,EAAEW,QAAA,CAAAS,GAAG;IACPnB,EAAE,EAAEU,QAAA,CAAAW,GAAG;IACR1B,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtBC,OAAO,EAAC;MAERM,mBAAA,CAIE;IAHAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;sEAIlBd,mBAAA,UAAa,EACbC,mBAAA,CAwCI;IAxCA2B,SAAS,eAAAb,MAAA,CAAeN,QAAA,CAAAS,GAAG,QAAAH,MAAA,CAAKN,QAAA,CAAAW,GAAG;MACrCnB,mBAAA,CAmBS,UAnBT4B,WAmBS,GAZP5B,mBAAA,CAKE;IAJAU,aAAa,EAAC,GAAG;IACjBC,MAAM,EAAC,MAAM;IACZC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;wCAElBb,mBAAA,CAKE;IAJAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,OAAO;IACbC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;0CAIpBd,mBAAA,UAAa,G,cACbM,mBAAA,CAgBIwB,SAAA,QAAAC,WAAA,CAhBW,CAAC,YAANC,CAAC;WAAX/B,mBAAA,CAgBI;MAhBegC,GAAG,EAAED;IAAC,IACvB/B,mBAAA,CAcO;MAbLL,EAAE,EAAC,GAAG;MAACC,EAAE,EAAC,GAAG;MACZC,EAAE,EAAEoC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE;MACxBrC,EAAE,EAAEmC,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MACzB1C,MAAM,EAAC,SAAS;MAChB,cAAY,EAAC,GAAG;MAChBC,OAAO,EAAC;QAERM,mBAAA,CAKE;MAJAU,aAAa,EAAC,SAAS;MACvBC,MAAM,EAAC,OAAO;MACbC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;MACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;;uGAO1Bd,mBAAA,YAAe,EACNG,MAAA,CAAAC,SAAS,CAACC,IAAI,uB,cAAvBC,mBAAA,CAoEI,KAAAgC,WAAA,GAnEFtC,mBAAA,UAAa,EACbC,mBAAA,CAsBS;IArBNgB,EAAE,EAAER,QAAA,CAAAc,KAAK;IACTJ,EAAE,EAAEV,QAAA,CAAAe,KAAK;IACVhC,CAAC,EAAC,GAAG;IACJC,IAAI,EAAEgB,QAAA,CAAA8B,aAAa;IACpB5C,OAAO,EAAC;MAERM,mBAAA,CAGE;IAFCY,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbY,IAAI,EAAEjB,QAAA,CAAA+B,YAAY;wCAErBvC,mBAAA,CAIE;IAHAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,WAAW;IACjBC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;kEAEhBb,mBAAA,CAKE;IAJAU,aAAa,EAAC,GAAG;IACjBC,MAAM,EAAC,QAAQ;IACdC,GAAG,EAAE,UAAU;IAChB4B,WAAW,EAAC;4DAIhBzC,mBAAA,YAAe,G,cACfM,mBAAA,CAkBIwB,SAAA,QAAAC,WAAA,CAlBW,EAAE,YAAPC,CAAC;WAAX/B,mBAAA,CAkBI;MAlBgBgC,GAAG,EAAED;IAAC,IACxB/B,mBAAA,CAgBS;MAfNgB,EAAE,EAAER,QAAA,CAAAc,KAAK,GAAGW,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE;MAChCjB,EAAE,EAAEV,QAAA,CAAAe,KAAK,GAAGU,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MACjC5C,CAAC,EAAC,GAAG;MACJC,IAAI,EAAEgB,QAAA,CAAA8B,aAAa;MACpB5C,OAAO,EAAC;QAERM,mBAAA,CAGE;MAFCY,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;MACbY,IAAI,EAAEjB,QAAA,CAAAiC,eAAe,CAACV,CAAC;0CAE1B/B,mBAAA,CAIE;MAHAU,aAAa,EAAC,SAAS;MACvBC,MAAM,EAAC,SAAS;MACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;;kCAKpBd,mBAAA,UAAa,EACbC,mBAAA,CAmBI;IAnBA2B,SAAS,eAAAb,MAAA,CAAeN,QAAA,CAAAS,GAAG,QAAAH,MAAA,CAAKN,QAAA,CAAAW,GAAG;MACrCnB,mBAAA,CAiBS;IAhBPT,CAAC,EAAC,GAAG;IACJC,IAAI,EAAEgB,QAAA,CAAA8B,aAAa;IACpB5C,OAAO,EAAC;MAERM,mBAAA,CAKE;IAJAU,aAAa,EAAC,GAAG;IACjBC,MAAM,EAAC,SAAS;IACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;wCAElBb,mBAAA,CAKE;IAJAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IACbO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;2IAMxBd,mBAAA,UAAa,EACJG,MAAA,CAAAC,SAAS,CAACC,IAAI,mB,cAAvBC,mBAAA,CA0CI,KAAAqC,WAAA,GAzCF3C,mBAAA,UAAa,EACbC,mBAAA,CAkBO;IAjBJO,CAAC,EAAEC,QAAA,CAAAmC,eAAe;IACnBnD,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtBC,OAAO,EAAC;MAERM,mBAAA,CAIE;IAHAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;wCAEhBb,mBAAA,CAIE;IAHAU,aAAa,EAAC,mBAAmB;IACjCC,MAAM,EAAC,QAAQ;IACdC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;sEAIlBd,mBAAA,UAAa,G,cACbM,mBAAA,CAkBIwB,SAAA,QAAAC,WAAA,CAlBW,CAAC,YAANC,CAAC;WAAX/B,mBAAA,CAkBI;MAlBegC,GAAG,EAAED;IAAC,IACvB/B,mBAAA,CAgBS;MAfNgB,EAAE,EAAER,QAAA,CAAAc,KAAK;MACTJ,EAAE,EAAEV,QAAA,CAAAe,KAAK;MACVhC,CAAC,EAAC,GAAG;MACLC,IAAI,EAAC,SAAS;MACdE,OAAO,EAAC;QAERM,mBAAA,CAGE;MAFCY,GAAG,EAAEJ,QAAA,CAAAK,QAAQ,GAAGkB,CAAC;MACjBN,IAAI,EAAEjB,QAAA,CAAAmC,eAAe;0CAExB3C,mBAAA,CAIE;MAHAU,aAAa,EAAC,SAAS;MACvBC,MAAM,EAAC,SAAS;MACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ,GAAGkB,CAAC;;yEAM1BhC,mBAAA,UAAa,EACJG,MAAA,CAAAC,SAAS,CAACC,IAAI,kB,cAAvBC,mBAAA,CAqDI,KAAAuC,WAAA,GApDF7C,mBAAA,UAAa,EACbC,mBAAA,CAmBS;IAlBNgB,EAAE,EAAER,QAAA,CAAAS,GAAG;IACPC,EAAE,EAAEV,QAAA,CAAAW,GAAG;IACR5B,CAAC,EAAC,GAAG;IACLC,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChBC,OAAO,EAAC;MAERM,mBAAA,CAIE;IAHAU,aAAa,EAAC,GAAG;IACjBC,MAAM,EAAC,QAAQ;IACdC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;wCAEhBb,mBAAA,CAIE;IAHAU,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IACfC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;sEAIlBd,mBAAA,UAAa,G,cACbM,mBAAA,CAkBIwB,SAAA,QAAAC,WAAA,CAlBW,EAAE,YAAPC,CAAC;WAAX/B,mBAAA,CAkBI;MAlBgBgC,GAAG,EAAED;IAAC,IACxB/B,mBAAA,CAgBS;MAfNgB,EAAE,EAAER,QAAA,CAAAS,GAAG,GAAGgB,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE;MAC9BjB,EAAE,EAAEV,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MAC/B5C,CAAC,EAAC,GAAG;MACLC,IAAI,EAAC,SAAS;MACdE,OAAO,EAAC;QAERM,mBAAA,CAGE;MAFCY,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;MACbY,IAAI,aAAAX,MAAA,EAAcmB,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE,iBAAArB,MAAA,EAAemB,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;0CAE5EnC,mBAAA,CAIE;MAHAU,aAAa,EAAC,SAAS;MACvBC,MAAM,EAAC,OAAO;MACbC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;;kCAKpBd,mBAAA,UAAa,EACbC,mBAAA,CAOI;IAPA2B,SAAS,eAAAb,MAAA,CAAeN,QAAA,CAAAS,GAAG,QAAAH,MAAA,CAAKN,QAAA,CAAAW,GAAG;MACrCnB,mBAAA,CAEO,QAFP6C,WAEO,GADL7C,mBAAA,CAA6G;IAApGU,aAAa,EAAC,SAAS;IAACC,MAAM,EAAC,OAAO;IAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IAAgBO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;0CAE/Fb,mBAAA,CAEO,QAFP8C,WAEO,GADL9C,mBAAA,CAA6G;IAApGU,aAAa,EAAC,SAAS;IAACC,MAAM,EAAC,OAAO;IAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;IAAgBO,KAAK,EAAEZ,QAAA,CAAAK,QAAQ;+GAKnGd,mBAAA,YAAe,EACNG,MAAA,CAAAC,SAAS,CAACC,IAAI,wB,cAAvBC,mBAAA,CA6CI,KAAA0C,WAAA,GA5CFhD,mBAAA,UAAa,EACJG,MAAA,CAAAC,SAAS,CAAC6C,MAAM,iB,cAAzB3C,mBAAA,CA2BI,KAAA4C,WAAA,GA1BFjD,mBAAA,CAWS;IAVNgB,EAAE,EAAER,QAAA,CAAAS,GAAG;IACPC,EAAE,EAAEV,QAAA,CAAAW,GAAG;IACR5B,CAAC,EAAC,IAAI;IACNC,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChBC,OAAO,EAAC;MAERM,mBAAA,CAA0E;IAAjEU,aAAa,EAAC,SAAS;IAACC,MAAM,EAAC,SAAS;IAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;kEAChEb,mBAAA,CAA0F;IAAjFU,aAAa,EAAC,GAAG;IAACC,MAAM,EAAC,UAAU;IAAEC,GAAG,EAAE,WAAW;IAAE4B,WAAW,EAAC;4DAG9EzC,mBAAA,SAAY,G,cACZM,mBAAA,CAWIwB,SAAA,QAAAC,WAAA,CAXW,CAAC,YAANC,CAAC;WAAX/B,mBAAA,CAWI;MAXegC,GAAG,EAAED;IAAC,IACvB/B,mBAAA,CASS;MARNgB,EAAE,EAAER,QAAA,CAAAS,GAAG,GAAGgB,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE;MAC9BjB,EAAE,EAAEV,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MAC/B5C,CAAC,EAAC,GAAG;MACLC,IAAI,EAAC,SAAS;MACdE,OAAO,EAAC;QAERM,mBAAA,CAAoF;MAA3EU,aAAa,EAAC,SAAS;MAACC,MAAM,EAAC,SAAS;MAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ,GAAGkB,CAAC;0CACpE/B,mBAAA,CAA+J;MAAtJU,aAAa,EAAC,IAAI;MAAEC,MAAM,KAAAG,MAAA,CAAKN,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE,iBAAArB,MAAA,CAAcN,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MAAoBvB,GAAG,EAAEJ,QAAA,CAAAK,QAAQ,GAAGkB,CAAC;;yEAKrJhC,mBAAA,UAAa,EACJG,MAAA,CAAAC,SAAS,CAAC6C,MAAM,kB,cAAzB3C,mBAAA,CAYI,KAAA6C,WAAA,I,cAXF7C,mBAAA,CAUIwB,SAAA,QAAAC,WAAA,CAVW,EAAE,YAAPC,CAAC;WAAX/B,mBAAA,CAUI;MAVgBgC,GAAG,EAAED;IAAC,IACxB/B,mBAAA,CAQO;MAPJO,CAAC,OAAAO,MAAA,CAAON,QAAA,CAAAS,GAAG,GAAGgB,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE,iBAAArB,MAAA,CAAcN,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE,mBAAArB,MAAA,CAAgBN,QAAA,CAAAS,GAAG,GAAGgB,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE,iBAAArB,MAAA,CAAcN,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE,sBAAArB,MAAA,CAAmBN,QAAA,CAAAS,GAAG,GAAGgB,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE,gBAAArB,MAAA,CAAaN,QAAA,CAAAW,GAAG,GAAGc,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MACjP3C,IAAI,EAAC,MAAM;MACXC,MAAM,EAAC,SAAS;MAChB,cAAY,EAAC,GAAG;MAChBC,OAAO,EAAC;QAERM,mBAAA,CAAkF;MAAzEU,aAAa,EAAC,SAAS;MAACC,MAAM,EAAC,OAAO;MAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ,GAAGkB,CAAC;;gHAM1EhC,mBAAA,UAAa,EACJG,MAAA,CAAAC,SAAS,CAACC,IAAI,gB,cAAvBC,mBAAA,CA6BI,KAAA8C,WAAA,GA5BFpD,mBAAA,UAAa,EACbC,mBAAA,CAUS;IATNgB,EAAE,EAAER,QAAA,CAAAS,GAAG;IACPC,EAAE,EAAEV,QAAA,CAAAW,GAAG;IACR5B,CAAC,EAAC,GAAG;IACLC,IAAI,EAAC,SAAS;IACdE,OAAO,EAAC;MAERM,mBAAA,CAAoF;IAA3EU,aAAa,EAAC,IAAI;IAAEC,MAAM,KAAAG,MAAA,CAAKN,QAAA,CAAAW,GAAG,OAAAL,MAAA,CAAIN,QAAA,CAAAW,GAAG;IAAWP,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;wCAC1Eb,mBAAA,CAA0E;IAAjEU,aAAa,EAAC,SAAS;IAACC,MAAM,EAAC,SAAS;IAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ;kEAChEb,mBAAA,CAAwF;IAA/EU,aAAa,EAAC,GAAG;IAACC,MAAM,EAAC,QAAQ;IAAEC,GAAG,EAAE,WAAW;IAAE4B,WAAW,EAAC;4DAG5EzC,mBAAA,UAAa,EACbC,mBAAA,CAaI;IAbA2B,SAAS,eAAAb,MAAA,CAAeN,QAAA,CAAAS,GAAG,QAAAH,MAAA,CAAKN,QAAA,CAAAW,GAAG;qBACrCd,mBAAA,CAWIwB,SAAA,QAAAC,WAAA,CAXW,CAAC,YAANC,CAAC;WAAX/B,mBAAA,CAWI;MAXegC,GAAG,EAAED;IAAC,IACvB/B,mBAAA,CASO;MARLL,EAAE,EAAC,GAAG;MAACC,EAAE,EAAC,GAAG;MACZC,EAAE,EAAEoC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGE,IAAI,CAACE,EAAE;MACxBrC,EAAE,EAAEmC,IAAI,CAACG,GAAG,CAACL,CAAC,GAAGE,IAAI,CAACE,EAAE;MACzB1C,MAAM,EAAC,SAAS;MAChB,cAAY,EAAC,GAAG;MAChBC,OAAO,EAAC;QAERM,mBAAA,CAAgF;MAAvEU,aAAa,EAAC,SAAS;MAACC,MAAM,EAAC,SAAS;MAAEC,GAAG,EAAEJ,QAAA,CAAAK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}