[{"C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\main.js": "1", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\App.vue": "2", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ResourceLoader.vue": "3", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\imageCache.js": "4", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\audioCache.js": "5", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\config\\websocket.js": "6", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\index.js": "7", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\router\\index.js": "8", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Login.vue": "9", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Register.vue": "10", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Home.vue": "11", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CharacterManager.vue": "12", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CharacterGrowth.vue": "13", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CreateRoom.vue": "14", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Settings.vue": "15", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\ListenerCardCreator.vue": "16", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Profile.vue": "17", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\api.js": "18", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\websocket.js": "19", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\NavBar.vue": "20", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GlobalNotice.vue": "21", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\Modal.vue": "22", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterSheet.vue": "23", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterForm.vue": "24", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterGrowth.vue": "25", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingCharacterSheet.vue": "26", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ChatBox.vue": "27", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingPanel.vue": "28", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingDiceRoller.vue": "29", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\MapSystem.vue": "30", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\AIModelSettings.vue": "31", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\AudioSystem.vue": "32", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\VoiceChat.vue": "33", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingNotes.vue": "34", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\PrivateChatManager.vue": "35", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ClueBoard.vue": "36", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ScenarioViewer.vue": "37", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\AnnouncementViewer.vue": "38", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\MythosLibrary.vue": "39", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\MadnessSystem.vue": "40", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GroupChat.vue": "41", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ExperiencePackSystem.vue": "42", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GameSaveManager.vue": "43", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\SpellSystem.vue": "44", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\SkillCheckSystem.vue": "45", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CombatSystem.vue": "46", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\EquipmentSystem.vue": "47", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\api_adapter.js": "48", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\OccupationStep.vue": "49", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\AttributesStep.vue": "50", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\BasicInfoStep.vue": "51", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\BackgroundStep.vue": "52", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\SkillsStep.vue": "53", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\ReviewStep.vue": "54", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\DiceRoller.vue": "55", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterStatus.vue": "56", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\NotesSystem.vue": "57", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\messageHistoryService.js": "58", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\ChatInput.vue": "59", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\VirtualScroller.vue": "60", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\ChatMessage.vue": "61", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\PrivateChat.vue": "62", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\ChatAISettings.vue": "63", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\performance.js": "64", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\theme.js": "65", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\index.js": "66", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\StorageManager.js": "67", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\ErrorHandler.js": "68", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\adapters\\SessionStorageAdapter.js": "69", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\adapters\\MemoryStorageAdapter.js": "70", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\adapters\\LocalStorageAdapter.js": "71", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\plugins\\persistencePlugin.js": "72", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\notificationService.js": "73", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ErrorNotification.vue": "74", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\devTools.js": "75", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\tests\\testRunner.js": "76", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\tests\\storage.test.js": "77", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\StorageStatusMonitor.vue": "78", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\tests\\integration.test.js": "79", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\modules\\auth.js": "80", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\modules\\rooms.js": "81", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\mixins\\storageMixin.js": "82", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\HealthChecker.js": "83", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\MigrationTool.js": "84", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\PerformanceMonitor.js": "85", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\EventListener.js": "86", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\config.js": "87", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterDetailView.vue": "88", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomCombatIntegrated.vue": "89", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CombatTest.vue": "90", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatLog.vue": "91", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\InitiativeTracker.vue": "92", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomFixed.vue": "93", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\ForcedCombatMode.vue": "94", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\KeeperCombatPanel.vue": "95", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\BattlefieldGrid.vue": "96", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\MonsterToken.vue": "97", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatAnimation.vue": "98", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CharacterToken.vue": "99", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Room.vue": "100"}, {"size": 11757, "mtime": 1753968746190, "results": "101", "hashOfConfig": "102"}, {"size": 11088, "mtime": 1753963678018, "results": "103", "hashOfConfig": "102"}, {"size": 12987, "mtime": 1751547841715, "results": "104", "hashOfConfig": "102"}, {"size": 8078, "mtime": 1751546848175, "results": "105", "hashOfConfig": "102"}, {"size": 7221, "mtime": 1751546788970, "results": "106", "hashOfConfig": "102"}, {"size": 943, "mtime": 1753969234806, "results": "107", "hashOfConfig": "102"}, {"size": 48028, "mtime": 1754046873152, "results": "108", "hashOfConfig": "102"}, {"size": 2504, "mtime": 1754066008119, "results": "109", "hashOfConfig": "102"}, {"size": 9282, "mtime": 1753549848354, "results": "110", "hashOfConfig": "102"}, {"size": 14657, "mtime": 1753549756609, "results": "111", "hashOfConfig": "102"}, {"size": 61519, "mtime": 1754046632777, "results": "112", "hashOfConfig": "102"}, {"size": 46666, "mtime": 1754046820747, "results": "113", "hashOfConfig": "102"}, {"size": 380, "mtime": 1751115476902, "results": "114", "hashOfConfig": "102"}, {"size": 11959, "mtime": 1753207749438, "results": "115", "hashOfConfig": "102"}, {"size": 53821, "mtime": 1753966005397, "results": "116", "hashOfConfig": "102"}, {"size": 12276, "mtime": 1753103019773, "results": "117", "hashOfConfig": "102"}, {"size": 33122, "mtime": 1753870857343, "results": "118", "hashOfConfig": "102"}, {"size": 9018, "mtime": 1754047026159, "results": "119", "hashOfConfig": "102"}, {"size": 33363, "mtime": 1753961436090, "results": "120", "hashOfConfig": "102"}, {"size": 51834, "mtime": 1753978991015, "results": "121", "hashOfConfig": "102"}, {"size": 3916, "mtime": 1751809182066, "results": "122", "hashOfConfig": "102"}, {"size": 3773, "mtime": 1753094045526, "results": "123", "hashOfConfig": "102"}, {"size": 25909, "mtime": 1753167490840, "results": "124", "hashOfConfig": "102"}, {"size": 33936, "mtime": 1753972205264, "results": "125", "hashOfConfig": "102"}, {"size": 11973, "mtime": 1753103326321, "results": "126", "hashOfConfig": "102"}, {"size": 48739, "mtime": 1753971390286, "results": "127", "hashOfConfig": "102"}, {"size": 19369, "mtime": 1753806377742, "results": "128", "hashOfConfig": "102"}, {"size": 28257, "mtime": 1753808647303, "results": "129", "hashOfConfig": "102"}, {"size": 2355, "mtime": 1751529774610, "results": "130", "hashOfConfig": "102"}, {"size": 75139, "mtime": 1751806771278, "results": "131", "hashOfConfig": "102"}, {"size": 21567, "mtime": 1751794470260, "results": "132", "hashOfConfig": "102"}, {"size": 18245, "mtime": 1751727744775, "results": "133", "hashOfConfig": "102"}, {"size": 32888, "mtime": 1751553010170, "results": "134", "hashOfConfig": "102"}, {"size": 1184, "mtime": 1751529753715, "results": "135", "hashOfConfig": "102"}, {"size": 20419, "mtime": 1754047201021, "results": "136", "hashOfConfig": "102"}, {"size": 65783, "mtime": 1753965629757, "results": "137", "hashOfConfig": "102"}, {"size": 13849, "mtime": 1751818069257, "results": "138", "hashOfConfig": "102"}, {"size": 13849, "mtime": 1751818069257, "results": "139", "hashOfConfig": "102"}, {"size": 30513, "mtime": 1752945289535, "results": "140", "hashOfConfig": "102"}, {"size": 24391, "mtime": 1753009396007, "results": "141", "hashOfConfig": "102"}, {"size": 12557, "mtime": 1751788249130, "results": "142", "hashOfConfig": "102"}, {"size": 21917, "mtime": 1752943739623, "results": "143", "hashOfConfig": "102"}, {"size": 63126, "mtime": 1751809292097, "results": "144", "hashOfConfig": "102"}, {"size": 27273, "mtime": 1752945203588, "results": "145", "hashOfConfig": "102"}, {"size": 23045, "mtime": 1752943348603, "results": "146", "hashOfConfig": "102"}, {"size": 16811, "mtime": 1752943255112, "results": "147", "hashOfConfig": "102"}, {"size": 24302, "mtime": 1752943450374, "results": "148", "hashOfConfig": "102"}, {"size": 6732, "mtime": 1753101676820, "results": "149", "hashOfConfig": "102"}, {"size": 44686, "mtime": 1753533406820, "results": "150", "hashOfConfig": "102"}, {"size": 45331, "mtime": 1753533429614, "results": "151", "hashOfConfig": "102"}, {"size": 14195, "mtime": 1753015219075, "results": "152", "hashOfConfig": "102"}, {"size": 28737, "mtime": 1753098436463, "results": "153", "hashOfConfig": "102"}, {"size": 39611, "mtime": 1753533572198, "results": "154", "hashOfConfig": "102"}, {"size": 14438, "mtime": 1753094307276, "results": "155", "hashOfConfig": "102"}, {"size": 45364, "mtime": 1753876886311, "results": "156", "hashOfConfig": "102"}, {"size": 8691, "mtime": 1751444088601, "results": "157", "hashOfConfig": "102"}, {"size": 2464, "mtime": 1751450074913, "results": "158", "hashOfConfig": "102"}, {"size": 4418, "mtime": 1752157622001, "results": "159", "hashOfConfig": "102"}, {"size": 7136, "mtime": 1753208223630, "results": "160", "hashOfConfig": "102"}, {"size": 5146, "mtime": 1752860033820, "results": "161", "hashOfConfig": "102"}, {"size": 19409, "mtime": 1753806585926, "results": "162", "hashOfConfig": "102"}, {"size": 10122, "mtime": 1751785899543, "results": "163", "hashOfConfig": "102"}, {"size": 8648, "mtime": 1752157498298, "results": "164", "hashOfConfig": "102"}, {"size": 4568, "mtime": 1752157581915, "results": "165", "hashOfConfig": "102"}, {"size": 5863, "mtime": 1753964637414, "results": "166", "hashOfConfig": "102"}, {"size": 4447, "mtime": 1753958834038, "results": "167", "hashOfConfig": "102"}, {"size": 8363, "mtime": 1753968171968, "results": "168", "hashOfConfig": "102"}, {"size": 4931, "mtime": 1753961795888, "results": "169", "hashOfConfig": "102"}, {"size": 4225, "mtime": 1753964307456, "results": "170", "hashOfConfig": "102"}, {"size": 6197, "mtime": 1753958730759, "results": "171", "hashOfConfig": "102"}, {"size": 4197, "mtime": 1753964299988, "results": "172", "hashOfConfig": "102"}, {"size": 6841, "mtime": 1753961207491, "results": "173", "hashOfConfig": "102"}, {"size": 7413, "mtime": 1753963579453, "results": "174", "hashOfConfig": "102"}, {"size": 11944, "mtime": 1753804839225, "results": "175", "hashOfConfig": "102"}, {"size": 10585, "mtime": 1753962833713, "results": "176", "hashOfConfig": "102"}, {"size": 5459, "mtime": 1753962925409, "results": "177", "hashOfConfig": "102"}, {"size": 10435, "mtime": 1753962586986, "results": "178", "hashOfConfig": "102"}, {"size": 11812, "mtime": 1753962769219, "results": "179", "hashOfConfig": "102"}, {"size": 7763, "mtime": 1753963600384, "results": "180", "hashOfConfig": "102"}, {"size": 13864, "mtime": 1753969675293, "results": "181", "hashOfConfig": "102"}, {"size": 7645, "mtime": 1753969741607, "results": "182", "hashOfConfig": "102"}, {"size": 2374, "mtime": 1753964790512, "results": "183", "hashOfConfig": "102"}, {"size": 11946, "mtime": 1753976953466, "results": "184", "hashOfConfig": "102"}, {"size": 10092, "mtime": 1753968328636, "results": "185", "hashOfConfig": "102"}, {"size": 8397, "mtime": 1753968279387, "results": "186", "hashOfConfig": "102"}, {"size": 11562, "mtime": 1753968698367, "results": "187", "hashOfConfig": "102"}, {"size": 4603, "mtime": 1753968239946, "results": "188", "hashOfConfig": "102"}, {"size": 12774, "mtime": 1753975690451, "results": "189", "hashOfConfig": "102"}, {"size": 55927, "mtime": 1754058066340, "results": "190", "hashOfConfig": "102"}, {"size": 5056, "mtime": 1754057233799, "results": "191", "hashOfConfig": "102"}, {"size": 19362, "mtime": 1754055306354, "results": "192", "hashOfConfig": "102"}, {"size": 20049, "mtime": 1754055219979, "results": "193", "hashOfConfig": "102"}, {"size": 24391, "mtime": 1754066846736, "results": "194", "hashOfConfig": "102"}, {"size": 17995, "mtime": 1754057704505, "results": "195", "hashOfConfig": "102"}, {"size": 32520, "mtime": 1754066462872, "results": "196", "hashOfConfig": "102"}, {"size": 29966, "mtime": 1754056031460, "results": "197", "hashOfConfig": "102"}, {"size": 15517, "mtime": 1754055755981, "results": "198", "hashOfConfig": "102"}, {"size": 15472, "mtime": 1754055846700, "results": "199", "hashOfConfig": "102"}, {"size": 13379, "mtime": 1754055665442, "results": "200", "hashOfConfig": "102"}, {"size": 117458, "mtime": 1754046587340, "results": "201", "hashOfConfig": "102"}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, "i9otvs", {"filePath": "205", "messages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "208", "messages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "210", "messages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "212", "messages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "214", "messages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "216", "messages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "218", "messages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "220", "messages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "222", "messages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "224", "messages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "226", "messages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "228", "messages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "230", "messages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "232", "messages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "234", "messages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "236", "messages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "240", "messages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "242", "messages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "246", "messages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "249", "messages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "251", "messages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "253", "messages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "255", "messages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "257", "messages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "259", "messages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "261", "messages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "263", "messages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "265", "messages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "267", "messages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "269", "messages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "271", "messages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "273", "messages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "275", "messages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "277", "messages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "279", "messages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "281", "messages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "283", "messages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "285", "messages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "287", "messages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "289", "messages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "291", "messages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "293", "messages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "295", "messages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "297", "messages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "299", "messages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "301"}, {"filePath": "302", "messages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "304", "messages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "306", "messages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "308", "messages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "310", "messages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "312", "messages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "314", "messages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "316", "messages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "318", "messages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "320", "messages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "322", "messages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "324", "messages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "326", "messages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "328", "messages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "248"}, {"filePath": "330", "messages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "332", "messages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "334", "messages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "336", "messages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "338", "messages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "340", "messages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "342", "messages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "344", "messages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "346", "messages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "348", "messages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "350", "messages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "352", "messages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "354", "messages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "356", "messages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "358", "messages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "360", "messages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "362", "messages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "364", "messages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "366", "messages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "368", "messages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "370", "messages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "372", "messages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "374", "messages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "376", "messages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "378", "messages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "204"}, {"filePath": "380", "messages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "382", "messages": "383", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "386", "messages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "388", "messages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "390", "messages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "394", "messages": "395", "errorCount": 33, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "396", "usedDeprecatedRules": "207"}, {"filePath": "397", "messages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "399", "messages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "401", "messages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "403", "messages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "207"}, {"filePath": "405", "messages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\main.js", [], [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\App.vue", [], [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ResourceLoader.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\imageCache.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\audioCache.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\config\\websocket.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\index.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\router\\index.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Login.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Register.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Home.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CharacterManager.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CharacterGrowth.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CreateRoom.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Settings.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\ListenerCardCreator.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Profile.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\api.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\websocket.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\NavBar.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GlobalNotice.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\Modal.vue", [], [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterSheet.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterForm.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterGrowth.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingCharacterSheet.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ChatBox.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingPanel.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingDiceRoller.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\MapSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\AIModelSettings.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\AudioSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\VoiceChat.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\FloatingNotes.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\PrivateChatManager.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ClueBoard.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ScenarioViewer.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\AnnouncementViewer.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\MythosLibrary.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\MadnessSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GroupChat.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ExperiencePackSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GameSaveManager.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\SpellSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\SkillCheckSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CombatSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\EquipmentSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\api_adapter.js", [], [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\OccupationStep.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\AttributesStep.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\BasicInfoStep.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\BackgroundStep.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\SkillsStep.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\character-creator\\ReviewStep.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\DiceRoller.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterStatus.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\NotesSystem.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\messageHistoryService.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\ChatInput.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\VirtualScroller.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\ChatMessage.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\PrivateChat.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\chat\\ChatAISettings.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\performance.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\theme.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\index.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\StorageManager.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\ErrorHandler.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\adapters\\SessionStorageAdapter.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\adapters\\MemoryStorageAdapter.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\adapters\\LocalStorageAdapter.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\plugins\\persistencePlugin.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\services\\notificationService.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\ErrorNotification.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\devTools.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\tests\\testRunner.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\tests\\storage.test.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\StorageStatusMonitor.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\tests\\integration.test.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\modules\\auth.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\store\\modules\\rooms.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\mixins\\storageMixin.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\HealthChecker.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\MigrationTool.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\PerformanceMonitor.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\EventListener.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\utils\\storage\\config.js", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\CharacterDetailView.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomCombatIntegrated.vue", ["407"], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CombatTest.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatLog.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\InitiativeTracker.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomFixed.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\ForcedCombatMode.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\KeeperCombatPanel.vue", ["408", "409", "410", "411", "412", "413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423", "424", "425", "426", "427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438", "439", "440"], "<template>\r\n  <div class=\"keeper-combat-panel\" v-if=\"isKeeper\">\r\n    <!-- 战斗控制头部 -->\r\n    <div class=\"combat-header\">\r\n      <div class=\"header-title\">\r\n        <i class=\"fas fa-shield-alt\"></i>\r\n        <h3>KP战斗控制面板</h3>\r\n        <span class=\"combat-status\" :class=\"combatStatus\">\r\n          {{ combatStatusText }}\r\n        </span>\r\n      </div>\r\n      <div class=\"header-actions\">\r\n        <button \r\n          @click=\"toggleCombatMode\" \r\n          class=\"btn-primary\"\r\n          :disabled=\"!canToggleCombat\"\r\n        >\r\n          {{ combatActive ? '结束战斗' : '开始战斗' }}\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战斗设置面板 -->\r\n    <div class=\"combat-setup\" v-if=\"!combatActive\">\r\n      <div class=\"setup-section\">\r\n        <h4>战场设置</h4>\r\n        <div class=\"battlefield-config\">\r\n          <div class=\"config-row\">\r\n            <label>战场大小:</label>\r\n            <select v-model=\"battlefieldConfig.size\">\r\n              <option value=\"small\">小型 (15x10)</option>\r\n              <option value=\"medium\">中型 (20x15)</option>\r\n              <option value=\"large\">大型 (30x20)</option>\r\n              <option value=\"huge\">巨型 (40x30)</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"config-row\">\r\n            <label>环境:</label>\r\n            <select v-model=\"battlefieldConfig.environment\">\r\n              <option value=\"indoor\">室内</option>\r\n              <option value=\"outdoor\">室外</option>\r\n              <option value=\"underground\">地下</option>\r\n              <option value=\"supernatural\">超自然</option>\r\n            </select>\r\n          </div>\r\n          <div class=\"config-row\">\r\n            <label>光照:</label>\r\n            <select v-model=\"battlefieldConfig.lighting\">\r\n              <option value=\"bright\">明亮</option>\r\n              <option value=\"normal\">正常</option>\r\n              <option value=\"dim\">昏暗</option>\r\n              <option value=\"dark\">黑暗</option>\r\n              <option value=\"pitch_black\">完全黑暗</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { combatSystem } from '@/utils/combatSystem.js'\r\nimport CombatRules from '@/utils/combatRules.js'\r\n\r\nexport default {\r\n  name: 'KeeperCombatPanel',\r\n  props: {\r\n    isKeeper: {\r\n      type: Boolean,\r\n      required: true\r\n    },\r\n    roomData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      combatActive: false,\r\n      currentRound: 1,\r\n      currentTurn: 0,\r\n      \r\n      // 战场配置\r\n      battlefieldConfig: {\r\n        size: 'medium',\r\n        environment: 'indoor',\r\n        lighting: 'normal',\r\n        weather: 'clear',\r\n        terrain: 'normal'\r\n      },\r\n      \r\n      // 参与者管理\r\n      selectedParticipants: [],\r\n      selectedNPCs: [],\r\n      initiativeOrder: [],\r\n      \r\n      // 弹窗状态\r\n      showMonsterLibrary: false,\r\n      showNPCCreator: false,\r\n      showCombatLogModal: false,\r\n      \r\n      // 怪物库\r\n      selectedCategory: 'common',\r\n      monsterCategories: [\r\n        { id: 'common', name: '常见生物' },\r\n        { id: 'criminal', name: '罪犯' },\r\n        { id: 'cultist', name: '邪教徒' },\r\n        { id: 'animal', name: '动物' },\r\n        { id: 'supernatural', name: '超自然' },\r\n        { id: 'mythos', name: '神话生物' }\r\n      ],\r\n      \r\n      // NPC创建\r\n      newNPC: {\r\n        name: '',\r\n        type: 'human',\r\n        hitPoints: 10,\r\n        armor: 0,\r\n        attributes: {\r\n          strength: 50,\r\n          dexterity: 50,\r\n          constitution: 50,\r\n          size: 50\r\n        }\r\n      },\r\n      \r\n      // 战斗日志\r\n      combatLog: []\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    combatStatus() {\r\n      if (!this.combatActive) return 'inactive'\r\n      return 'active'\r\n    },\r\n    \r\n    combatStatusText() {\r\n      if (!this.combatActive) return '战斗未开始'\r\n      return `第${this.currentRound}轮 - ${this.currentParticipant?.name || '等待'}的回合`\r\n    },\r\n    \r\n    canToggleCombat() {\r\n      if (this.combatActive) return true\r\n      return this.selectedParticipants.length > 0 || this.selectedNPCs.length > 0\r\n    },\r\n    \r\n    playerCharacters() {\r\n      // 从房间数据获取玩家角色\r\n      return this.roomData.players?.map(player => ({\r\n        id: player.id,\r\n        name: player.character?.name || player.username,\r\n        avatar: player.character?.avatar,\r\n        currentHP: player.character?.currentHP || player.character?.hitPoints || 10,\r\n        maxHP: player.character?.hitPoints || 10,\r\n        isPlayer: true,\r\n        ...player.character\r\n      })) || []\r\n    },\r\n    \r\n    currentParticipant() {\r\n      return this.initiativeOrder[this.currentTurn] || null\r\n    },\r\n    \r\n    filteredMonsters() {\r\n      return this.getMonstersByCategory(this.selectedCategory)\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 战斗控制\r\n    toggleCombatMode() {\r\n      if (this.combatActive) {\r\n        this.endCombat()\r\n      } else {\r\n        this.startCombat()\r\n      }\r\n    },\r\n    \r\n    startCombat() {\r\n      // 收集所有参与者\r\n      const participants = [\r\n        ...this.getSelectedPlayerCharacters(),\r\n        ...this.selectedNPCs\r\n      ]\r\n      \r\n      if (participants.length === 0) {\r\n        this.$emit('show-notification', {\r\n          type: 'warning',\r\n          message: '请至少选择一个参与者'\r\n        })\r\n        return\r\n      }\r\n      \r\n      // 开始战斗\r\n      const combat = combatSystem.startCombat(participants, this.battlefieldConfig)\r\n      this.combatActive = true\r\n      this.initiativeOrder = combatSystem.participants\r\n      this.currentRound = 1\r\n      this.currentTurn = 0\r\n      \r\n      this.addCombatLog('combat_start', `战斗开始！参与者：${participants.length}人`)\r\n      \r\n      // 通知所有玩家战斗开始\r\n      this.$emit('combat-started', {\r\n        combatId: combat.id,\r\n        participants: this.initiativeOrder,\r\n        battlefield: this.battlefieldConfig\r\n      })\r\n    },\r\n    \r\n    endCombat() {\r\n      combatSystem.endCombat('keeper_ended')\r\n      this.combatActive = false\r\n      this.initiativeOrder = []\r\n      this.currentRound = 1\r\n      this.currentTurn = 0\r\n      \r\n      this.addCombatLog('combat_end', '战斗结束')\r\n      \r\n      // 通知所有玩家战斗结束\r\n      this.$emit('combat-ended')\r\n    },\r\n    \r\n    // 回合管理\r\n    nextTurn() {\r\n      const nextParticipant = combatSystem.nextTurn()\r\n      this.currentTurn = combatSystem.currentTurn\r\n      this.currentRound = combatSystem.roundNumber\r\n      \r\n      if (nextParticipant) {\r\n        this.addCombatLog('turn_change', `${nextParticipant.name} 的回合`)\r\n        this.$emit('turn-changed', {\r\n          participant: nextParticipant,\r\n          round: this.currentRound,\r\n          turn: this.currentTurn\r\n        })\r\n      }\r\n    },\r\n    \r\n    previousTurn() {\r\n      // 实现上一回合逻辑\r\n      if (this.currentTurn > 0) {\r\n        this.currentTurn--\r\n      } else if (this.currentRound > 1) {\r\n        this.currentRound--\r\n        this.currentTurn = this.initiativeOrder.length - 1\r\n      }\r\n      \r\n      const participant = this.currentParticipant\r\n      if (participant) {\r\n        this.addCombatLog('turn_change', `回到 ${participant.name} 的回合`)\r\n      }\r\n    },\r\n    \r\n    endRound() {\r\n      combatSystem.startNewRound()\r\n      this.currentRound = combatSystem.roundNumber\r\n      this.currentTurn = 0\r\n      \r\n      this.addCombatLog('round_end', `第${this.currentRound}轮开始`)\r\n      this.$emit('round-ended', this.currentRound)\r\n    },\r\n    \r\n    // 参与者管理\r\n    toggleParticipant(characterId) {\r\n      const index = this.selectedParticipants.indexOf(characterId)\r\n      if (index > -1) {\r\n        this.selectedParticipants.splice(index, 1)\r\n      } else {\r\n        this.selectedParticipants.push(characterId)\r\n      }\r\n    },\r\n    \r\n    getSelectedPlayerCharacters() {\r\n      return this.playerCharacters.filter(char => \r\n        this.selectedParticipants.includes(char.id)\r\n      )\r\n    },\r\n    \r\n    delayParticipant(participantId) {\r\n      const success = combatSystem.delayAction(participantId)\r\n      if (success) {\r\n        this.initiativeOrder = combatSystem.participants\r\n        const participant = combatSystem.getParticipant(participantId)\r\n        this.addCombatLog('delay_action', `${participant.name} 延迟行动`)\r\n      }\r\n    },\r\n    \r\n    // 怪物和NPC管理\r\n    getMonstersByCategory(category) {\r\n      // 这里应该从怪物数据库获取\r\n      const monsters = {\r\n        common: [\r\n          {\r\n            id: 'thug',\r\n            name: '暴徒',\r\n            description: '普通的街头暴徒',\r\n            hitPoints: 12,\r\n            armor: 0,\r\n            build: 1,\r\n            type: 'human'\r\n          },\r\n          {\r\n            id: 'guard',\r\n            name: '警卫',\r\n            description: '训练有素的警卫',\r\n            hitPoints: 15,\r\n            armor: 1,\r\n            build: 1,\r\n            type: 'human'\r\n          }\r\n        ],\r\n        animal: [\r\n          {\r\n            id: 'dog',\r\n            name: '恶犬',\r\n            description: '凶猛的看门犬',\r\n            hitPoints: 8,\r\n            armor: 0,\r\n            build: 0,\r\n            type: 'animal'\r\n          }\r\n        ]\r\n      }\r\n      \r\n      return monsters[category] || []\r\n    },\r\n    \r\n    addMonster(monster) {\r\n      const newNPC = {\r\n        ...monster,\r\n        id: `${monster.id}_${Date.now()}`,\r\n        currentHP: monster.hitPoints,\r\n        maxHP: monster.hitPoints,\r\n        isPlayer: false,\r\n        faction: 'enemies'\r\n      }\r\n      \r\n      this.selectedNPCs.push(newNPC)\r\n      this.showMonsterLibrary = false\r\n      this.addCombatLog('add_npc', `添加了 ${monster.name}`)\r\n    },\r\n    \r\n    createNPC() {\r\n      const npc = {\r\n        ...this.newNPC,\r\n        id: `npc_${Date.now()}`,\r\n        currentHP: this.newNPC.hitPoints,\r\n        maxHP: this.newNPC.hitPoints,\r\n        isPlayer: false,\r\n        faction: 'npcs'\r\n      }\r\n      \r\n      this.selectedNPCs.push(npc)\r\n      this.showNPCCreator = false\r\n      \r\n      // 重置表单\r\n      this.newNPC = {\r\n        name: '',\r\n        type: 'human',\r\n        hitPoints: 10,\r\n        armor: 0\r\n      }\r\n      \r\n      this.addCombatLog('create_npc', `创建了NPC ${npc.name}`)\r\n    },\r\n    \r\n    removeNPC(npcId) {\r\n      const index = this.selectedNPCs.findIndex(npc => npc.id === npcId)\r\n      if (index > -1) {\r\n        const npc = this.selectedNPCs[index]\r\n        this.selectedNPCs.splice(index, 1)\r\n        this.addCombatLog('remove_npc', `移除了 ${npc.name}`)\r\n      }\r\n    },\r\n    \r\n    editNPC(npc) {\r\n      // 实现NPC编辑功能\r\n      this.$emit('edit-npc', npc)\r\n    },\r\n    \r\n    editParticipant(participant) {\r\n      // 实现参与者编辑功能\r\n      this.$emit('edit-participant', participant)\r\n    },\r\n    \r\n    // 战斗工具\r\n    rollInitiative() {\r\n      if (this.initiativeOrder.length === 0) return\r\n      \r\n      // 重新计算先攻\r\n      this.initiativeOrder.forEach(participant => {\r\n        participant.initiative = combatSystem.calculateInitiative(participant)\r\n      })\r\n      \r\n      // 重新排序\r\n      this.initiativeOrder.sort((a, b) => b.initiative - a.initiative)\r\n      this.currentTurn = 0\r\n      \r\n      this.addCombatLog('reroll_initiative', '重新投掷先攻')\r\n    },\r\n    \r\n    applyDamage() {\r\n      // 打开伤害应用界面\r\n      this.$emit('show-damage-dialog')\r\n    },\r\n    \r\n    healCharacter() {\r\n      // 打开治疗界面\r\n      this.$emit('show-heal-dialog')\r\n    },\r\n    \r\n    addStatusEffect() {\r\n      // 打开状态效果界面\r\n      this.$emit('show-status-dialog')\r\n    },\r\n    \r\n    showDiceRoller() {\r\n      // 显示骰子工具\r\n      this.$emit('show-dice-roller')\r\n    },\r\n    \r\n    showCombatLog() {\r\n      this.showCombatLogModal = true\r\n    },\r\n    \r\n    // 状态效果\r\n    getStatusEffectIcon(effect) {\r\n      const icons = {\r\n        bleeding: '🩸',\r\n        poisoned: '☠️',\r\n        stunned: '😵',\r\n        frightened: '😨',\r\n        blessed: '✨',\r\n        cursed: '💀',\r\n        prone: '⬇️',\r\n        grappled: '🤝',\r\n        unconscious: '😴'\r\n      }\r\n      return icons[effect] || '❓'\r\n    },\r\n    \r\n    // 战斗日志\r\n    addCombatLog(type, message) {\r\n      this.combatLog.push({\r\n        id: Date.now(),\r\n        type,\r\n        message,\r\n        timestamp: new Date(),\r\n        round: this.currentRound\r\n      })\r\n    },\r\n    \r\n    formatTime(timestamp) {\r\n      return timestamp.toLocaleTimeString()\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    combatActive(newVal) {\r\n      if (newVal) {\r\n        // 战斗开始时的初始化\r\n        this.$emit('combat-mode-changed', true)\r\n      } else {\r\n        // 战斗结束时的清理\r\n        this.$emit('combat-mode-changed', false)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>    <!-\r\n- 参与者管理 -->\r\n    <div class=\"participants-section\" v-if=\"!combatActive\">\r\n      <h4>参与者管理</h4>\r\n      \r\n      <!-- 玩家角色列表 -->\r\n      <div class=\"player-characters\">\r\n        <h5>玩家角色</h5>\r\n        <div class=\"character-list\">\r\n          <div \r\n            v-for=\"character in playerCharacters\" \r\n            :key=\"character.id\"\r\n            class=\"character-item\"\r\n            :class=\"{ selected: selectedParticipants.includes(character.id) }\"\r\n            @click=\"toggleParticipant(character.id)\"\r\n          >\r\n            <div class=\"character-avatar\">\r\n              <img :src=\"character.avatar\" :alt=\"character.name\" v-if=\"character.avatar\">\r\n              <div v-else class=\"default-avatar\">{{ character.name.charAt(0) }}</div>\r\n            </div>\r\n            <div class=\"character-info\">\r\n              <span class=\"character-name\">{{ character.name }}</span>\r\n              <span class=\"character-stats\">HP: {{ character.currentHP }}/{{ character.maxHP }}</span>\r\n            </div>\r\n            <div class=\"character-actions\">\r\n              <i class=\"fas fa-check\" v-if=\"selectedParticipants.includes(character.id)\"></i>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- NPC和怪物管理 -->\r\n      <div class=\"npc-management\">\r\n        <h5>NPC和怪物</h5>\r\n        <div class=\"npc-controls\">\r\n          <button @click=\"showMonsterLibrary = true\" class=\"btn-secondary\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            添加怪物\r\n          </button>\r\n          <button @click=\"showNPCCreator = true\" class=\"btn-secondary\">\r\n            <i class=\"fas fa-user-plus\"></i>\r\n            创建NPC\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"npc-list\">\r\n          <div \r\n            v-for=\"npc in selectedNPCs\" \r\n            :key=\"npc.id\"\r\n            class=\"npc-item\"\r\n          >\r\n            <div class=\"npc-info\">\r\n              <span class=\"npc-name\">{{ npc.name }}</span>\r\n              <span class=\"npc-type\">{{ npc.type }}</span>\r\n              <span class=\"npc-stats\">HP: {{ npc.currentHP }}/{{ npc.maxHP }}</span>\r\n            </div>\r\n            <div class=\"npc-actions\">\r\n              <button @click=\"editNPC(npc)\" class=\"btn-icon\">\r\n                <i class=\"fas fa-edit\"></i>\r\n              </button>\r\n              <button @click=\"removeNPC(npc.id)\" class=\"btn-icon danger\">\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战斗中控制面板 -->\r\n    <div class=\"combat-control\" v-if=\"combatActive\">\r\n      <!-- 先攻追踪器 -->\r\n      <div class=\"initiative-tracker\">\r\n        <h4>先攻顺序</h4>\r\n        <div class=\"initiative-list\">\r\n          <div \r\n            v-for=\"(participant, index) in initiativeOrder\" \r\n            :key=\"participant.id\"\r\n            class=\"initiative-item\"\r\n            :class=\"{ \r\n              current: index === currentTurn,\r\n              player: participant.isPlayer,\r\n              npc: !participant.isPlayer\r\n            }\"\r\n          >\r\n            <div class=\"initiative-number\">{{ participant.initiative }}</div>\r\n            <div class=\"participant-info\">\r\n              <span class=\"participant-name\">{{ participant.name }}</span>\r\n              <div class=\"participant-status\">\r\n                <span class=\"hp-status\">{{ participant.currentHP }}/{{ participant.maxHP }}</span>\r\n                <div class=\"status-effects\">\r\n                  <span \r\n                    v-for=\"effect in participant.conditions\" \r\n                    :key=\"effect\"\r\n                    class=\"status-effect\"\r\n                    :class=\"effect\"\r\n                  >\r\n                    {{ getStatusEffectIcon(effect) }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"participant-actions\">\r\n              <button @click=\"delayParticipant(participant.id)\" class=\"btn-icon\">\r\n                <i class=\"fas fa-clock\"></i>\r\n              </button>\r\n              <button @click=\"editParticipant(participant)\" class=\"btn-icon\">\r\n                <i class=\"fas fa-edit\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 回合控制 -->\r\n      <div class=\"turn-control\">\r\n        <div class=\"current-turn-info\">\r\n          <h4>当前回合</h4>\r\n          <div class=\"turn-details\">\r\n            <span class=\"round-number\">第 {{ currentRound }} 轮</span>\r\n            <span class=\"current-participant\">\r\n              {{ currentParticipant?.name || '无' }} 的回合\r\n            </span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"turn-actions\">\r\n          <button @click=\"nextTurn\" class=\"btn-primary\">\r\n            <i class=\"fas fa-forward\"></i>\r\n            下一个回合\r\n          </button>\r\n          <button @click=\"previousTurn\" class=\"btn-secondary\">\r\n            <i class=\"fas fa-backward\"></i>\r\n            上一个回合\r\n          </button>\r\n          <button @click=\"endRound\" class=\"btn-warning\">\r\n            <i class=\"fas fa-refresh\"></i>\r\n            结束本轮\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n    <!-- 战斗工具 -->\r\n    <div class=\"combat-tools\" v-if=\"combatActive\">\r\n      <h4>战斗工具</h4>\r\n      <div class=\"tool-grid\">\r\n        <button @click=\"rollInitiative\" class=\"tool-btn\">\r\n          <i class=\"fas fa-dice\"></i>\r\n          重新投先攻\r\n        </button>\r\n        <button @click=\"applyDamage\" class=\"tool-btn\">\r\n          <i class=\"fas fa-heart-broken\"></i>\r\n          应用伤害\r\n        </button>\r\n        <button @click=\"healCharacter\" class=\"tool-btn\">\r\n          <i class=\"fas fa-heart\"></i>\r\n          治疗角色\r\n        </button>\r\n        <button @click=\"addStatusEffect\" class=\"tool-btn\">\r\n          <i class=\"fas fa-magic\"></i>\r\n          添加状态\r\n        </button>\r\n        <button @click=\"showDiceRoller\" class=\"tool-btn\">\r\n          <i class=\"fas fa-dice-d20\"></i>\r\n          投骰工具\r\n        </button>\r\n        <button @click=\"showCombatLog\" class=\"tool-btn\">\r\n          <i class=\"fas fa-scroll\"></i>\r\n          战斗日志\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 怪物库弹窗 -->\r\n    <div class=\"modal-overlay\" v-if=\"showMonsterLibrary\" @click=\"showMonsterLibrary = false\">\r\n      <div class=\"modal-content monster-library\" @click.stop>\r\n        <div class=\"modal-header\">\r\n          <h3>怪物库</h3>\r\n          <button @click=\"showMonsterLibrary = false\" class=\"btn-close\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div class=\"monster-categories\">\r\n            <button \r\n              v-for=\"category in monsterCategories\" \r\n              :key=\"category.id\"\r\n              @click=\"selectedCategory = category.id\"\r\n              class=\"category-btn\"\r\n              :class=\"{ active: selectedCategory === category.id }\"\r\n            >\r\n              {{ category.name }}\r\n            </button>\r\n          </div>\r\n          <div class=\"monster-list\">\r\n            <div \r\n              v-for=\"monster in filteredMonsters\" \r\n              :key=\"monster.id\"\r\n              class=\"monster-card\"\r\n              @click=\"addMonster(monster)\"\r\n            >\r\n              <div class=\"monster-info\">\r\n                <h4>{{ monster.name }}</h4>\r\n                <p>{{ monster.description }}</p>\r\n                <div class=\"monster-stats\">\r\n                  <span>HP: {{ monster.hitPoints }}</span>\r\n                  <span>护甲: {{ monster.armor }}</span>\r\n                  <span>体格: {{ monster.build }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- NPC创建器弹窗 -->\r\n    <div class=\"modal-overlay\" v-if=\"showNPCCreator\" @click=\"showNPCCreator = false\">\r\n      <div class=\"modal-content npc-creator\" @click.stop>\r\n        <div class=\"modal-header\">\r\n          <h3>创建NPC</h3>\r\n          <button @click=\"showNPCCreator = false\" class=\"btn-close\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <form @submit.prevent=\"createNPC\">\r\n            <div class=\"form-group\">\r\n              <label>名称:</label>\r\n              <input v-model=\"newNPC.name\" type=\"text\" required>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label>类型:</label>\r\n              <select v-model=\"newNPC.type\">\r\n                <option value=\"human\">人类</option>\r\n                <option value=\"animal\">动物</option>\r\n                <option value=\"monster\">怪物</option>\r\n                <option value=\"supernatural\">超自然</option>\r\n              </select>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label>生命值:</label>\r\n              <input v-model.number=\"newNPC.hitPoints\" type=\"number\" min=\"1\" required>\r\n            </div>\r\n            <div class=\"form-group\">\r\n              <label>护甲值:</label>\r\n              <input v-model.number=\"newNPC.armor\" type=\"number\" min=\"0\">\r\n            </div>\r\n            <div class=\"form-actions\">\r\n              <button type=\"submit\" class=\"btn-primary\">创建</button>\r\n              <button type=\"button\" @click=\"showNPCCreator = false\" class=\"btn-secondary\">取消</button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战斗日志弹窗 -->\r\n    <div class=\"modal-overlay\" v-if=\"showCombatLogModal\" @click=\"showCombatLogModal = false\">\r\n      <div class=\"modal-content combat-log-modal\" @click.stop>\r\n        <div class=\"modal-header\">\r\n          <h3>战斗日志</h3>\r\n          <button @click=\"showCombatLogModal = false\" class=\"btn-close\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"modal-body\">\r\n          <div class=\"log-entries\">\r\n            <div \r\n              v-for=\"entry in combatLog\" \r\n              :key=\"entry.id\"\r\n              class=\"log-entry\"\r\n              :class=\"entry.type\"\r\n            >\r\n              <span class=\"log-time\">{{ formatTime(entry.timestamp) }}</span>\r\n              <span class=\"log-content\">{{ entry.message }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template><sty\r\nle scoped>\r\n.keeper-combat-panel {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 2px solid #0f3460;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  color: #e94560;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 头部样式 */\r\n.combat-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 2px solid #0f3460;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.header-title h3 {\r\n  margin: 0;\r\n  color: #e94560;\r\n  font-size: 1.5rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.combat-status {\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 0.8rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.combat-status.inactive {\r\n  background: #666;\r\n  color: #ccc;\r\n}\r\n\r\n.combat-status.active {\r\n  background: #e94560;\r\n  color: white;\r\n}\r\n\r\n/* 按钮样式 */\r\n.btn-primary {\r\n  background: linear-gradient(135deg, #e94560 0%, #f27121 100%);\r\n  color: white;\r\n  border: none;\r\n  padding: 10px 20px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  font-weight: bold;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(233, 69, 96, 0.4);\r\n}\r\n\r\n.btn-primary:disabled {\r\n  background: #666;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.btn-secondary {\r\n  background: #0f3460;\r\n  color: #e94560;\r\n  border: 1px solid #e94560;\r\n  padding: 8px 16px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.btn-secondary:hover {\r\n  background: #e94560;\r\n  color: white;\r\n}\r\n\r\n/* 战场设置 */\r\n.combat-setup {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.setup-section h4 {\r\n  color: #e94560;\r\n  margin-bottom: 15px;\r\n  font-size: 1.2rem;\r\n}\r\n\r\n.battlefield-config {\r\n  display: grid;\r\n  gap: 12px;\r\n}\r\n\r\n.config-row {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.config-row label {\r\n  min-width: 80px;\r\n  color: #ccc;\r\n  font-weight: bold;\r\n}\r\n\r\n.config-row select {\r\n  flex: 1;\r\n  padding: 8px;\r\n  border: 1px solid #0f3460;\r\n  border-radius: 4px;\r\n  background: #1a1a2e;\r\n  color: #e94560;\r\n}\r\n\r\n/* 参与者管理 */\r\n.participants-section {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.character-list, .npc-list {\r\n  display: grid;\r\n  gap: 8px;\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.character-item, .npc-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.character-item:hover, .npc-item:hover {\r\n  background: rgba(15, 52, 96, 0.5);\r\n  border-color: #e94560;\r\n}\r\n\r\n.character-item.selected {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n.character-avatar, .default-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #e94560;\r\n  color: white;\r\n  font-weight: bold;\r\n}\r\n\r\n.character-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.character-info, .npc-info {\r\n  flex: 1;\r\n}\r\n\r\n.character-name, .npc-name {\r\n  display: block;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n}\r\n\r\n.character-stats, .npc-stats {\r\n  display: block;\r\n  font-size: 0.9rem;\r\n  color: #ccc;\r\n}\r\n\r\n/* 先攻追踪器 */\r\n.initiative-tracker {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.initiative-list {\r\n  display: grid;\r\n  gap: 8px;\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.initiative-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.initiative-item.current {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n  box-shadow: 0 0 12px rgba(233, 69, 96, 0.3);\r\n}\r\n\r\n.initiative-item.player {\r\n  border-left: 4px solid #4CAF50;\r\n}\r\n\r\n.initiative-item.npc {\r\n  border-left: 4px solid #FF9800;\r\n}\r\n\r\n.initiative-number {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: #e94560;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 1.1rem;\r\n}\r\n\r\n.participant-info {\r\n  flex: 1;\r\n}\r\n\r\n.participant-name {\r\n  display: block;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.participant-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.hp-status {\r\n  color: #ccc;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.status-effects {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.status-effect {\r\n  font-size: 1.2rem;\r\n}\r\n\r\n/* 回合控制 */\r\n.turn-control {\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background: rgba(15, 52, 96, 0.2);\r\n  border-radius: 8px;\r\n}\r\n\r\n.current-turn-info {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.turn-details {\r\n  display: flex;\r\n  gap: 20px;\r\n  color: #ccc;\r\n}\r\n\r\n.round-number, .current-participant {\r\n  font-weight: bold;\r\n}\r\n\r\n.turn-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 战斗工具 */\r\n.combat-tools {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.tool-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\r\n  gap: 10px;\r\n}\r\n\r\n.tool-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 15px 10px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 8px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.tool-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.tool-btn i {\r\n  font-size: 1.5rem;\r\n}\r\n\r\n/* 弹窗样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n  background: #1a1a2e;\r\n  border: 2px solid #0f3460;\r\n  border-radius: 12px;\r\n  max-width: 600px;\r\n  max-height: 80vh;\r\n  overflow-y: auto;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid #0f3460;\r\n}\r\n\r\n.modal-header h3 {\r\n  margin: 0;\r\n  color: #e94560;\r\n}\r\n\r\n.btn-close {\r\n  background: none;\r\n  border: none;\r\n  color: #e94560;\r\n  font-size: 1.5rem;\r\n  cursor: pointer;\r\n  padding: 5px;\r\n}\r\n\r\n.modal-body {\r\n  padding: 20px;\r\n}\r\n\r\n/* 怪物库样式 */\r\n.monster-categories {\r\n  display: flex;\r\n  gap: 8px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.category-btn {\r\n  padding: 8px 16px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 6px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.category-btn.active,\r\n.category-btn:hover {\r\n  background: #e94560;\r\n  color: white;\r\n}\r\n\r\n.monster-list {\r\n  display: grid;\r\n  gap: 12px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.monster-card {\r\n  padding: 15px;\r\n  background: rgba(15, 52, 96, 0.2);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.monster-card:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n.monster-info h4 {\r\n  margin: 0 0 8px 0;\r\n  color: #e94560;\r\n}\r\n\r\n.monster-info p {\r\n  margin: 0 0 12px 0;\r\n  color: #ccc;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.monster-stats {\r\n  display: flex;\r\n  gap: 15px;\r\n  font-size: 0.9rem;\r\n  color: #ccc;\r\n}\r\n\r\n/* 表单样式 */\r\n.form-group {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 5px;\r\n  color: #e94560;\r\n  font-weight: bold;\r\n}\r\n\r\n.form-group input,\r\n.form-group select {\r\n  width: 100%;\r\n  padding: 8px;\r\n  border: 1px solid #0f3460;\r\n  border-radius: 4px;\r\n  background: #1a1a2e;\r\n  color: #e94560;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 战斗日志 */\r\n.log-entries {\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.log-entry {\r\n  display: flex;\r\n  gap: 12px;\r\n  padding: 8px 0;\r\n  border-bottom: 1px solid rgba(15, 52, 96, 0.3);\r\n}\r\n\r\n.log-time {\r\n  color: #666;\r\n  font-size: 0.8rem;\r\n  min-width: 80px;\r\n}\r\n\r\n.log-content {\r\n  color: #ccc;\r\n  flex: 1;\r\n}\r\n\r\n.log-entry.combat_start .log-content {\r\n  color: #4CAF50;\r\n  font-weight: bold;\r\n}\r\n\r\n.log-entry.combat_end .log-content {\r\n  color: #e94560;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .keeper-combat-panel {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .combat-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .turn-actions {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .tool-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n  \r\n  .modal-content {\r\n    margin: 20px;\r\n    max-width: calc(100% - 40px);\r\n  }\r\n}\r\n</style>", "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\BattlefieldGrid.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\MonsterToken.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatAnimation.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CharacterToken.vue", [], "C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Room.vue", [], {"ruleId": null, "fatal": true, "severity": 2, "message": "441", "line": 634, "column": 4}, {"ruleId": "442", "severity": 2, "message": "443", "line": 473, "column": 16, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 492, "column": 13, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 500, "column": 11, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 501, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 502, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 537, "column": 11, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 538, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 539, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 540, "column": 5, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 573, "column": 15, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 574, "column": 13, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 583, "column": 11, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 584, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 585, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 597, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 613, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 644, "column": 5, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 682, "column": 15, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 683, "column": 13, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 684, "column": 11, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 685, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 686, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 687, "column": 5, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 725, "column": 11, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 726, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 727, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 728, "column": 5, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 750, "column": 11, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 751, "column": 9, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 752, "column": 7, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 753, "column": 5, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 754, "column": 3, "nodeType": "444"}, {"ruleId": "442", "severity": 2, "message": "445", "line": 755, "column": 1, "nodeType": "444"}, "Parsing error: Unexpected token, expected \",\" (205:4)", "vue/no-parsing-error", "Parsing error: incorrectly-opened-comment.", "VElement", "Parsing error: x-invalid-end-tag."]