{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeStyle as _normalizeStyle } from \"vue\";\nvar _hoisted_1 = {\n  key: 0,\n  \"class\": \"unread-badge\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  \"class\": \"chat-list-popup\"\n};\nvar _hoisted_3 = {\n  \"class\": \"popup-header\"\n};\nvar _hoisted_4 = {\n  \"class\": \"user-search\"\n};\nvar _hoisted_5 = {\n  \"class\": \"users-list\"\n};\nvar _hoisted_6 = {\n  key: 0,\n  \"class\": \"no-users\"\n};\nvar _hoisted_7 = [\"onClick\"];\nvar _hoisted_8 = {\n  \"class\": \"user-info\"\n};\nvar _hoisted_9 = {\n  \"class\": \"user-name\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  \"class\": \"last-message\"\n};\nvar _hoisted_11 = {\n  \"class\": \"user-status\"\n};\nvar _hoisted_12 = {\n  key: 0,\n  \"class\": \"unread-count\"\n};\nvar _hoisted_13 = {\n  key: 1,\n  \"class\": \"online-indicator\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_private_chat = _resolveComponent(\"private-chat\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    \"class\": _normalizeClass([\"private-chat-manager\", {\n      'hidden': $props.hidden\n    }])\n  }, [_createCommentVNode(\" 私聊按钮 \"), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"chat-button\", {\n      'has-unread': $options.hasUnreadMessages\n    }]),\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.toggleChatList && $options.toggleChatList.apply($options, arguments);\n    })\n  }, [_cache[4] || (_cache[4] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-comments\"\n  }, null, -1 /* CACHED */)), $options.hasUnreadMessages ? (_openBlock(), _createElementBlock(\"span\", _hoisted_1, _toDisplayString($options.totalUnreadCount), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */), _createCommentVNode(\" 用户列表弹窗 \"), $data.showChatList ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"私聊\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    \"class\": \"close-btn\",\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.toggleChatList && $options.toggleChatList.apply($options, arguments);\n    })\n  }, \"×\")]), _createElementVNode(\"div\", _hoisted_4, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $data.searchQuery = $event;\n    }),\n    placeholder: \"搜索用户...\",\n    onInput: _cache[3] || (_cache[3] = function () {\n      return $options.filterUsers && $options.filterUsers.apply($options, arguments);\n    })\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.searchQuery]])]), _createElementVNode(\"div\", _hoisted_5, [$data.filteredUsers.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, _cache[6] || (_cache[6] = [_createElementVNode(\"p\", null, \"没有找到用户\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.filteredUsers, function (user) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: user.id,\n      \"class\": _normalizeClass([\"user-item\", {\n        'has-unread': $data.unreadCounts[user.id] > 0\n      }]),\n      onClick: function onClick($event) {\n        return $options.startChat(user);\n      }\n    }, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n      \"class\": \"user-avatar\"\n    }, [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-user\"\n    })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, _toDisplayString(user.username), 1 /* TEXT */), $options.getLastMessage(user.id) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, _toDisplayString($options.formatLastMessage($options.getLastMessage(user.id))), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_11, [$data.unreadCounts[user.id] ? (_openBlock(), _createElementBlock(\"span\", _hoisted_12, _toDisplayString($data.unreadCounts[user.id]), 1 /* TEXT */)) : $options.isUserOnline(user.id) ? (_openBlock(), _createElementBlock(\"span\", _hoisted_13)) : _createCommentVNode(\"v-if\", true)])], 10 /* CLASS, PROPS */, _hoisted_7);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 活动聊天窗口 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.activeChats, function (chat) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: chat.userId,\n      \"class\": _normalizeClass([\"chat-window\", {\n        'active': chat.active\n      }]),\n      style: _normalizeStyle({\n        left: chat.position + 'px',\n        zIndex: chat.active ? 10 : 5\n      })\n    }, [_createVNode(_component_private_chat, {\n      \"target-user\": $options.getUserById(chat.userId),\n      onClose: function onClose($event) {\n        return $options.closeChat(chat.userId);\n      },\n      onMessageSent: $options.handleMessageSent,\n      onMessageReceived: $options.handleMessageReceived,\n      onNewPrivateMessage: $options.handleNewPrivateMessage\n    }, null, 8 /* PROPS */, [\"target-user\", \"onClose\", \"onMessageSent\", \"onMessageReceived\", \"onNewPrivateMessage\"])], 6 /* CLASS, STYLE */);\n  }), 128 /* KEYED_FRAGMENT */))], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_normalizeClass", "$props", "hidden", "_createCommentVNode", "_createElementVNode", "$options", "hasUnreadMessages", "onClick", "_cache", "toggleChatList", "apply", "arguments", "_hoisted_1", "_toDisplayString", "totalUnreadCount", "$data", "showChatList", "_hoisted_2", "_hoisted_3", "_hoisted_4", "type", "searchQuery", "$event", "placeholder", "onInput", "filterUsers", "_hoisted_5", "filteredUsers", "length", "_hoisted_6", "_Fragment", "_renderList", "user", "key", "id", "unreadCounts", "startChat", "_hoisted_8", "_hoisted_9", "username", "getLastMessage", "_hoisted_10", "formatLastMessage", "_hoisted_11", "_hoisted_12", "isUserOnline", "_hoisted_13", "activeChats", "chat", "userId", "active", "style", "_normalizeStyle", "left", "position", "zIndex", "_createVNode", "_component_private_chat", "getUserById", "onClose", "closeChat", "onMessageSent", "handleMessageSent", "onMessageReceived", "handleMessageReceived", "onNewPrivateMessage", "handleNewPrivateMessage"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\PrivateChatManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"private-chat-manager\" :class=\"{ 'hidden': hidden }\">\r\n    <!-- 私聊按钮 -->\r\n    <div class=\"chat-button\" @click=\"toggleChatList\" :class=\"{ 'has-unread': hasUnreadMessages }\">\r\n      <i class=\"fas fa-comments\"></i>\r\n      <span v-if=\"hasUnreadMessages\" class=\"unread-badge\">{{ totalUnreadCount }}</span>\r\n    </div>\r\n    \r\n    <!-- 用户列表弹窗 -->\r\n    <div v-if=\"showChatList\" class=\"chat-list-popup\">\r\n      <div class=\"popup-header\">\r\n        <h3>私聊</h3>\r\n        <button class=\"close-btn\" @click=\"toggleChatList\">×</button>\r\n      </div>\r\n      \r\n      <div class=\"user-search\">\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"searchQuery\" \r\n          placeholder=\"搜索用户...\" \r\n          @input=\"filterUsers\"\r\n        />\r\n      </div>\r\n      \r\n      <div class=\"users-list\">\r\n        <div v-if=\"filteredUsers.length === 0\" class=\"no-users\">\r\n          <p>没有找到用户</p>\r\n        </div>\r\n        \r\n        <div \r\n          v-for=\"user in filteredUsers\" \r\n          :key=\"user.id\"\r\n          class=\"user-item\"\r\n          :class=\"{ 'has-unread': unreadCounts[user.id] > 0 }\"\r\n          @click=\"startChat(user)\"\r\n        >\r\n          <div class=\"user-avatar\">\r\n            <i class=\"fas fa-user\"></i>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <div class=\"user-name\">{{ user.username }}</div>\r\n            <div class=\"last-message\" v-if=\"getLastMessage(user.id)\">\r\n              {{ formatLastMessage(getLastMessage(user.id)) }}\r\n            </div>\r\n          </div>\r\n          <div class=\"user-status\">\r\n            <span v-if=\"unreadCounts[user.id]\" class=\"unread-count\">{{ unreadCounts[user.id] }}</span>\r\n            <span v-else-if=\"isUserOnline(user.id)\" class=\"online-indicator\"></span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 活动聊天窗口 -->\r\n    <div v-for=\"chat in activeChats\" :key=\"chat.userId\" class=\"chat-window\"\r\n         :style=\"{ left: chat.position + 'px', zIndex: chat.active ? 10 : 5 }\"\r\n         :class=\"{ 'active': chat.active }\">\r\n      <private-chat\r\n        :target-user=\"getUserById(chat.userId)\"\r\n        @close=\"closeChat(chat.userId)\"\r\n        @message-sent=\"handleMessageSent\"\r\n        @message-received=\"handleMessageReceived\"\r\n        @new-private-message=\"handleNewPrivateMessage\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PrivateChat from './PrivateChat.vue';\r\nimport websocketService from '@/services/websocket';\r\nimport { storageMixin } from '@/mixins/storageMixin';\r\n\r\nexport default {\r\n  name: 'PrivateChatManager',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    PrivateChat\r\n  },\r\n  props: {\r\n    onlineUsers: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    hidden: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      showChatList: false,\r\n      activeChats: [], // 活动的聊天窗口\r\n      chatMessages: {}, // 所有聊天消息\r\n      unreadCounts: {}, // 未读消息计数\r\n      searchQuery: '',\r\n      filteredUsers: [],\r\n      chatWindowWidth: 320, // 聊天窗口宽度\r\n      chatWindowGap: 20, // 聊天窗口间隙\r\n      maxVisibleChats: 3 // 最大可见聊天窗口数\r\n    };\r\n  },\r\n  computed: {\r\n    currentUser() {\r\n      return this.$store.getters.currentUser || { id: 0, username: '游客' };\r\n    },\r\n    allUsers() {\r\n      // 过滤掉当前用户\r\n      return this.onlineUsers.filter(user => user.id !== this.currentUser.id);\r\n    },\r\n    hasUnreadMessages() {\r\n      return Object.values(this.unreadCounts).some(count => count > 0);\r\n    },\r\n    totalUnreadCount() {\r\n      return Object.values(this.unreadCounts).reduce((sum, count) => sum + count, 0);\r\n    }\r\n  },\r\n  watch: {\r\n    onlineUsers: {\r\n      handler(newUsers) {\r\n        // 更新过滤后的用户列表\r\n        this.filterUsers();\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化过滤后的用户列表\r\n    this.filteredUsers = [...this.allUsers];\r\n    \r\n    // 添加私聊消息监听器\r\n    websocketService.addCustomEventListener('private_message', this.handlePrivateMessage);\r\n    \r\n    // 加载未读消息计数\r\n    this.loadUnreadCounts();\r\n    \r\n    // 加载最近聊天记录\r\n    this.loadRecentChats();\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.updateMaxVisibleChats);\r\n    this.updateMaxVisibleChats();\r\n  },\r\n  beforeDestroy() {\r\n    // 移除私聊消息监听器\r\n    websocketService.removeCustomEventListener('private_message', this.handlePrivateMessage);\r\n    \r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.updateMaxVisibleChats);\r\n  },\r\n  methods: {\r\n    // 额外的安全存储访问方法\r\n    safeGetStorageLength() {\r\n      try {\r\n        if (window.storageManager && typeof window.storageManager.length === 'function') {\r\n          return window.storageManager.length();\r\n        } else if (window.storageManager && typeof window.storageManager.length === 'number') {\r\n          return window.storageManager.length;\r\n        }\r\n        return localStorage.length;\r\n      } catch (error) {\r\n        console.warn('[PrivateChatManager] 无法获取存储长度:', error.message);\r\n        return 0;\r\n      }\r\n    },\r\n    \r\n    safeGetStorageKey(index) {\r\n      try {\r\n        if (window.storageManager && window.storageManager.key) {\r\n          return window.storageManager.key(index);\r\n        }\r\n        return localStorage.key(index);\r\n      } catch (error) {\r\n        console.warn(`[PrivateChatManager] 无法获取存储键 ${index}:`, error.message);\r\n        return null;\r\n      }\r\n    },\r\n\r\n    // 切换聊天列表显示状态\r\n    toggleChatList() {\r\n      this.showChatList = !this.showChatList;\r\n      if (this.showChatList) {\r\n        this.filterUsers();\r\n      }\r\n    },\r\n    \r\n    // 根据搜索查询过滤用户\r\n    filterUsers() {\r\n      if (!this.searchQuery.trim()) {\r\n        this.filteredUsers = [...this.allUsers];\r\n        return;\r\n      }\r\n      \r\n      const query = this.searchQuery.toLowerCase();\r\n      this.filteredUsers = this.allUsers.filter(user => \r\n        user.username.toLowerCase().includes(query)\r\n      );\r\n    },\r\n    \r\n    // 开始与用户聊天\r\n    startChat(user) {\r\n      if (!user || !user.id) return;\r\n      \r\n      // 检查是否已经存在该聊天\r\n      const existingChat = this.activeChats.find(chat => chat.userId === user.id);\r\n      \r\n      if (existingChat) {\r\n        // 如果已存在，激活该聊天\r\n        this.activateChat(user.id);\r\n      } else {\r\n        // 如果不存在，创建新聊天\r\n        const position = this.calculateChatPosition(this.activeChats.length);\r\n        \r\n        this.activeChats.push({\r\n          userId: user.id,\r\n          position,\r\n          active: true,\r\n          minimized: false\r\n        });\r\n        \r\n        // 重新计算所有聊天窗口的位置\r\n        this.recalculateChatPositions();\r\n      }\r\n      \r\n      // 重置该用户的未读消息计数\r\n      this.unreadCounts[user.id] = 0;\r\n      this.saveUnreadCounts();\r\n      \r\n      // 关闭用户列表\r\n      this.showChatList = false;\r\n    },\r\n    \r\n    // 关闭聊天\r\n    closeChat(userId) {\r\n      const index = this.activeChats.findIndex(chat => chat.userId === userId);\r\n      if (index !== -1) {\r\n        this.activeChats.splice(index, 1);\r\n        \r\n        // 重新计算所有聊天窗口的位置\r\n        this.recalculateChatPositions();\r\n      }\r\n    },\r\n    \r\n    // 激活聊天\r\n    activateChat(userId) {\r\n      // 将所有聊天设为非活动状态\r\n      this.activeChats.forEach(chat => {\r\n        chat.active = chat.userId === userId;\r\n      });\r\n      \r\n      // 重置该用户的未读消息计数\r\n      this.unreadCounts[userId] = 0;\r\n      this.saveUnreadCounts();\r\n    },\r\n    \r\n    // 计算聊天窗口位置\r\n    calculateChatPosition(index) {\r\n      const visibleIndex = index % this.maxVisibleChats;\r\n      return visibleIndex * (this.chatWindowWidth + this.chatWindowGap);\r\n    },\r\n    \r\n    // 重新计算所有聊天窗口的位置\r\n    recalculateChatPositions() {\r\n      this.activeChats.forEach((chat, index) => {\r\n        chat.position = this.calculateChatPosition(index);\r\n      });\r\n    },\r\n    \r\n    // 更新最大可见聊天窗口数\r\n    updateMaxVisibleChats() {\r\n      const windowWidth = window.innerWidth;\r\n      this.maxVisibleChats = Math.max(1, Math.floor((windowWidth - 100) / (this.chatWindowWidth + this.chatWindowGap)));\r\n      \r\n      // 重新计算所有聊天窗口的位置\r\n      this.recalculateChatPositions();\r\n    },\r\n    \r\n    // 处理私聊消息\r\n    handlePrivateMessage(message) {\r\n      if (!message || !message.sender_id || !message.recipient_id) return;\r\n      \r\n      // 确定对话的另一方\r\n      const otherUserId = message.sender_id === this.currentUser.id ? \r\n        message.recipient_id : message.sender_id;\r\n      \r\n      // 初始化该用户的消息数组（如果不存在）\r\n      if (!this.chatMessages[otherUserId]) {\r\n        this.chatMessages[otherUserId] = [];\r\n      }\r\n      \r\n      // 添加消息到数组\r\n      this.chatMessages[otherUserId].push(message);\r\n      \r\n      // 如果是接收到的消息，且没有打开与该用户的聊天，增加未读计数\r\n      if (message.sender_id !== this.currentUser.id) {\r\n        const isActiveChatOpen = this.activeChats.some(chat => \r\n          chat.userId === message.sender_id && !chat.minimized\r\n        );\r\n        \r\n        if (!isActiveChatOpen) {\r\n          this.unreadCounts[message.sender_id] = (this.unreadCounts[message.sender_id] || 0) + 1;\r\n          this.saveUnreadCounts();\r\n          \r\n          // 发送桌面通知\r\n          this.sendNotification(message);\r\n        }\r\n      }\r\n      \r\n      // 保存消息到本地存储\r\n      this.saveChatMessages(otherUserId);\r\n    },\r\n    \r\n    // 处理发送的消息\r\n    handleMessageSent(message) {\r\n      this.handlePrivateMessage(message);\r\n    },\r\n    \r\n    // 处理接收的消息\r\n    handleMessageReceived(message) {\r\n      // 消息已经在handlePrivateMessage中处理\r\n    },\r\n    \r\n    // 处理新的私聊消息（不在当前活动聊天中）\r\n    handleNewPrivateMessage(message) {\r\n      // 检查是否需要自动打开聊天窗口\r\n      const autoOpenChat = this.safeGetItem('auto_open_private_chat') === 'true';\r\n      \r\n      if (autoOpenChat) {\r\n        // 自动打开与发送者的聊天窗口\r\n        const user = this.getUserById(message.sender_id);\r\n        if (user) {\r\n          this.startChat(user);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 发送桌面通知\r\n    sendNotification(message) {\r\n      // 检查是否启用了通知\r\n      const notificationsEnabled = this.safeGetItem('enable_notifications') !== 'false';\r\n      \r\n      if (notificationsEnabled && \"Notification\" in window) {\r\n        // 检查权限\r\n        if (Notification.permission === \"granted\") {\r\n          // 创建通知\r\n          const sender = this.getUserById(message.sender_id);\r\n          const senderName = sender ? sender.username : message.sender_name || '用户';\r\n          \r\n          const notification = new Notification(`新私聊消息 - ${senderName}`, {\r\n            body: message.content,\r\n            icon: '/favicon.ico'\r\n          });\r\n          \r\n          // 点击通知时打开聊天\r\n          notification.onclick = () => {\r\n            window.focus();\r\n            this.startChat(sender);\r\n          };\r\n        } else if (Notification.permission !== \"denied\") {\r\n          // 请求权限\r\n          Notification.requestPermission();\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取用户通过ID\r\n    getUserById(userId) {\r\n      return this.onlineUsers.find(user => user.id === userId);\r\n    },\r\n    \r\n    // 检查用户是否在线\r\n    isUserOnline(userId) {\r\n      return this.onlineUsers.some(user => user.id === userId);\r\n    },\r\n    \r\n    // 获取与用户的最后一条消息\r\n    getLastMessage(userId) {\r\n      const messages = this.chatMessages[userId];\r\n      if (!messages || messages.length === 0) return null;\r\n      \r\n      return messages[messages.length - 1];\r\n    },\r\n    \r\n    // 格式化最后一条消息\r\n    formatLastMessage(message) {\r\n      if (!message) return '';\r\n      \r\n      const isSelf = message.sender_id === this.currentUser.id;\r\n      const prefix = isSelf ? '我: ' : '';\r\n      const content = message.content.length > 15 ? \r\n        message.content.substring(0, 15) + '...' : \r\n        message.content;\r\n      \r\n      return prefix + content;\r\n    },\r\n    \r\n    // 加载未读消息计数\r\n    loadUnreadCounts() {\r\n      try {\r\n        const stored = this.safeGetItem(`unread_counts_${this.currentUser.id}`);\r\n        if (stored) {\r\n          this.unreadCounts = JSON.parse(stored);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载未读消息计数失败:', error);\r\n        this.unreadCounts = {};\r\n      }\r\n    },\r\n    \r\n    // 保存未读消息计数\r\n    saveUnreadCounts() {\r\n      try {\r\n        this.safeSetJSON(`unread_counts_${this.currentUser.id}`, this.unreadCounts);\r\n      } catch (error) {\r\n        console.error('保存未读消息计数失败:', error);\r\n      }\r\n    },\r\n    \r\n    // 加载聊天消息\r\n    loadChatMessages(userId) {\r\n      try {\r\n        const chatKey = `private_chat_${this.currentUser.id}_${userId}`;\r\n        const stored = this.safeGetItem(chatKey);\r\n        \r\n        if (stored) {\r\n          const parsed = JSON.parse(stored);\r\n          this.chatMessages[userId] = parsed.messages || [];\r\n        } else {\r\n          this.chatMessages[userId] = [];\r\n        }\r\n      } catch (error) {\r\n        console.error(`加载与用户 ${userId} 的聊天消息失败:`, error);\r\n        this.chatMessages[userId] = [];\r\n      }\r\n    },\r\n    \r\n    // 保存聊天消息\r\n    saveChatMessages(userId) {\r\n      try {\r\n        const messages = this.chatMessages[userId];\r\n        if (!messages) return;\r\n        \r\n        // 最多保存100条消息\r\n        const messagesToSave = messages.slice(-100);\r\n        \r\n        const chatKey = `private_chat_${this.currentUser.id}_${userId}`;\r\n        this.safeSetJSON(chatKey, {\r\n          lastUpdated: new Date().toISOString(),\r\n          messages: messagesToSave\r\n        });\r\n      } catch (error) {\r\n        console.error(`保存与用户 ${userId} 的聊天消息失败:`, error);\r\n      }\r\n    },\r\n    \r\n    // 加载最近聊天\r\n    loadRecentChats() {\r\n      try {\r\n        // 获取所有本地存储的聊天记录\r\n        const recentChats = [];\r\n        \r\n        for (let i = 0; i < this.safeGetStorageLength(); i++) {\r\n          const key = this.safeGetStorageKey(i);\r\n          if (key.startsWith(`private_chat_${this.currentUser.id}_`)) {\r\n            const userId = parseInt(key.split('_')[3]);\r\n            if (!isNaN(userId)) {\r\n              // 加载聊天消息\r\n              this.loadChatMessages(userId);\r\n              \r\n              // 获取最后更新时间\r\n              const stored = this.safeGetItem(key);\r\n              if (stored) {\r\n                try {\r\n                  const parsed = JSON.parse(stored);\r\n                  recentChats.push({\r\n                    userId,\r\n                    lastUpdated: parsed.lastUpdated || new Date(0).toISOString()\r\n                  });\r\n                } catch (e) {\r\n                  console.error('解析聊天记录失败:', e);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 按最后更新时间排序\r\n        recentChats.sort((a, b) => \r\n          new Date(b.lastUpdated) - new Date(a.lastUpdated)\r\n        );\r\n        \r\n        // 自动打开最近的聊天（可选）\r\n        const autoOpenRecent = this.safeGetItem('auto_open_recent_chat') === 'true';\r\n        if (autoOpenRecent && recentChats.length > 0) {\r\n          const mostRecent = recentChats[0];\r\n          const user = this.getUserById(mostRecent.userId);\r\n          if (user) {\r\n            this.startChat(user);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('加载最近聊天失败:', error);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.private-chat-manager {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 1000;\r\n  transition: transform 0.3s ease, opacity 0.3s ease;\r\n}\r\n\r\n.private-chat-manager.hidden {\r\n  transform: translateX(-100px);\r\n  opacity: 0;\r\n  pointer-events: none;\r\n}\r\n\r\n.chat-button {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: #3498db;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n  position: relative;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.chat-button:hover {\r\n  background-color: #2980b9;\r\n  transform: scale(1.05);\r\n}\r\n\r\n.chat-button.has-unread {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);\r\n  }\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background-color: #e74c3c;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 0.7rem;\r\n  font-weight: bold;\r\n  border: 2px solid #2a2a2a;\r\n}\r\n\r\n.chat-list-popup {\r\n  position: absolute;\r\n  bottom: 60px;\r\n  left: 0;\r\n  width: 280px;\r\n  max-height: 400px;\r\n  background-color: #2a2a2a;\r\n  border-radius: 8px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  z-index: 1001;\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  background-color: #333;\r\n  border-bottom: 1px solid #444;\r\n}\r\n\r\n.popup-header h3 {\r\n  margin: 0;\r\n  color: #e0e0e0;\r\n  font-size: 1rem;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #b0b0b0;\r\n  font-size: 1.2rem;\r\n  cursor: pointer;\r\n  padding: 0 5px;\r\n}\r\n\r\n.close-btn:hover {\r\n  color: #e74c3c;\r\n}\r\n\r\n.user-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #444;\r\n}\r\n\r\n.user-search input {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border-radius: 4px;\r\n  border: 1px solid #555;\r\n  background-color: #333;\r\n  color: #e0e0e0;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.user-search input:focus {\r\n  outline: none;\r\n  border-color: #3498db;\r\n}\r\n\r\n.users-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  max-height: 300px;\r\n}\r\n\r\n.no-users {\r\n  padding: 20px;\r\n  text-align: center;\r\n  color: #888;\r\n}\r\n\r\n.user-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  cursor: pointer;\r\n  border-bottom: 1px solid #444;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.user-item:hover {\r\n  background-color: #333;\r\n}\r\n\r\n.user-item.has-unread {\r\n  background-color: rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.user-avatar {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  background-color: #444;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 10px;\r\n  color: #e0e0e0;\r\n}\r\n\r\n.user-info {\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.user-name {\r\n  color: #e0e0e0;\r\n  font-weight: 500;\r\n  margin-bottom: 3px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.last-message {\r\n  color: #b0b0b0;\r\n  font-size: 0.8rem;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.user-status {\r\n  margin-left: 10px;\r\n}\r\n\r\n.online-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: #2ecc71;\r\n  display: inline-block;\r\n}\r\n\r\n.unread-count {\r\n  background-color: #e74c3c;\r\n  color: white;\r\n  border-radius: 10px;\r\n  padding: 2px 6px;\r\n  font-size: 0.7rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.chat-window {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  width: 320px;\r\n  height: 400px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n  transition: box-shadow 0.3s;\r\n}\r\n\r\n.chat-window.active {\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 768px) {\r\n  .chat-window {\r\n    width: 280px;\r\n    height: 350px;\r\n  }\r\n  \r\n  .chat-list-popup {\r\n    width: 250px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;EAKqC,SAAM;;;;EAId,SAAM;;;EACxB,SAAM;AAAc;;EAKpB,SAAM;AAAa;;EASnB,SAAM;AAAY;;;EACkB,SAAM;;;;EActC,SAAM;AAAW;;EACf,SAAM;AAAW;;;EACjB,SAAM;;;EAIR,SAAM;AAAa;;;EACa,SAAM;;;;EACD,SAAM;;;;uBA9CxDA,mBAAA,CAgEM;IAhED,SAAKC,eAAA,EAAC,sBAAsB;MAAA,UAAqBC,MAAA,CAAAC;IAAM;MAC1DC,mBAAA,UAAa,EACbC,mBAAA,CAGM;IAHD,SAAKJ,eAAA,EAAC,aAAa;MAAA,cAAiDK,QAAA,CAAAC;IAAiB;IAAhEC,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAI,cAAA,IAAAJ,QAAA,CAAAI,cAAA,CAAAC,KAAA,CAAAL,QAAA,EAAAM,SAAA,CAAc;IAAA;gCAC7CP,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,4BACdC,QAAA,CAAAC,iBAAiB,I,cAA7BP,mBAAA,CAAiF,QAAjFa,UAAiF,EAAAC,gBAAA,CAA1BR,QAAA,CAAAS,gBAAgB,oB,oDAGzEX,mBAAA,YAAe,EACJY,KAAA,CAAAC,YAAY,I,cAAvBjB,mBAAA,CA0CM,OA1CNkB,UA0CM,GAzCJb,mBAAA,CAGM,OAHNc,UAGM,G,0BAFJd,mBAAA,CAAW,YAAP,IAAE,qBACNA,mBAAA,CAA4D;IAApD,SAAM,WAAW;IAAEG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAI,cAAA,IAAAJ,QAAA,CAAAI,cAAA,CAAAC,KAAA,CAAAL,QAAA,EAAAM,SAAA,CAAc;IAAA;KAAE,GAAC,E,GAGrDP,mBAAA,CAOM,OAPNe,UAOM,G,gBANJf,mBAAA,CAKE;IAJAgB,IAAI,EAAC,MAAM;;aACFL,KAAA,CAAAM,WAAW,GAAAC,MAAA;IAAA;IACpBC,WAAW,EAAC,SAAS;IACpBC,OAAK,EAAAhB,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAoB,WAAA,IAAApB,QAAA,CAAAoB,WAAA,CAAAf,KAAA,CAAAL,QAAA,EAAAM,SAAA,CAAW;IAAA;iEAFVI,KAAA,CAAAM,WAAW,E,KAMxBjB,mBAAA,CA0BM,OA1BNsB,UA0BM,GAzBOX,KAAA,CAAAY,aAAa,CAACC,MAAM,U,cAA/B7B,mBAAA,CAEM,OAFN8B,UAEM,EAAArB,MAAA,QAAAA,MAAA,OADJJ,mBAAA,CAAa,WAAV,QAAM,mB,4DAGXL,mBAAA,CAoBM+B,SAAA,QAAAC,WAAA,CAnBWhB,KAAA,CAAAY,aAAa,YAArBK,IAAI;yBADbjC,mBAAA,CAoBM;MAlBHkC,GAAG,EAAED,IAAI,CAACE,EAAE;MACb,SAAKlC,eAAA,EAAC,WAAW;QAAA,cACOe,KAAA,CAAAoB,YAAY,CAACH,IAAI,CAACE,EAAE;MAAA;MAC3C3B,OAAK,WAALA,OAAKA,CAAAe,MAAA;QAAA,OAAEjB,QAAA,CAAA+B,SAAS,CAACJ,IAAI;MAAA;kCAEtB5B,mBAAA,CAEM;MAFD,SAAM;IAAa,IACtBA,mBAAA,CAA2B;MAAxB,SAAM;IAAa,G,qBAExBA,mBAAA,CAKM,OALNiC,UAKM,GAJJjC,mBAAA,CAAgD,OAAhDkC,UAAgD,EAAAzB,gBAAA,CAAtBmB,IAAI,CAACO,QAAQ,kBACPlC,QAAA,CAAAmC,cAAc,CAACR,IAAI,CAACE,EAAE,K,cAAtDnC,mBAAA,CAEM,OAFN0C,WAEM,EAAA5B,gBAAA,CADDR,QAAA,CAAAqC,iBAAiB,CAACrC,QAAA,CAAAmC,cAAc,CAACR,IAAI,CAACE,EAAE,sB,qCAG/C9B,mBAAA,CAGM,OAHNuC,WAGM,GAFQ5B,KAAA,CAAAoB,YAAY,CAACH,IAAI,CAACE,EAAE,K,cAAhCnC,mBAAA,CAA0F,QAA1F6C,WAA0F,EAAA/B,gBAAA,CAA/BE,KAAA,CAAAoB,YAAY,CAACH,IAAI,CAACE,EAAE,qBAC9D7B,QAAA,CAAAwC,YAAY,CAACb,IAAI,CAACE,EAAE,K,cAArCnC,mBAAA,CAAwE,QAAxE+C,WAAwE,K;2EAMhF3C,mBAAA,YAAe,G,kBACfJ,mBAAA,CAUM+B,SAAA,QAAAC,WAAA,CAVchB,KAAA,CAAAgC,WAAW,YAAnBC,IAAI;yBAAhBjD,mBAAA,CAUM;MAV4BkC,GAAG,EAAEe,IAAI,CAACC,MAAM;MAAE,SAAKjD,eAAA,EAAC,aAAa;QAAA,UAE9CgD,IAAI,CAACE;MAAM;MAD9BC,KAAK,EAAAC,eAAA;QAAAC,IAAA,EAAUL,IAAI,CAACM,QAAQ;QAAAC,MAAA,EAAiBP,IAAI,CAACE,MAAM;MAAA;QAE5DM,YAAA,CAMEC,uBAAA;MALC,aAAW,EAAEpD,QAAA,CAAAqD,WAAW,CAACV,IAAI,CAACC,MAAM;MACpCU,OAAK,WAALA,OAAKA,CAAArC,MAAA;QAAA,OAAEjB,QAAA,CAAAuD,SAAS,CAACZ,IAAI,CAACC,MAAM;MAAA;MAC5BY,aAAY,EAAExD,QAAA,CAAAyD,iBAAiB;MAC/BC,iBAAgB,EAAE1D,QAAA,CAAA2D,qBAAqB;MACvCC,mBAAmB,EAAE5D,QAAA,CAAA6D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}