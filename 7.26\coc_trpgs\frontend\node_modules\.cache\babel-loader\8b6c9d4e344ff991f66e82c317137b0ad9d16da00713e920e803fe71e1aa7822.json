{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.find.js\";\nimport \"core-js/modules/es.array.find-index.js\";\nimport \"core-js/modules/es.array.for-each.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.reduce.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.some.js\";\nimport \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.date.to-iso-string.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.object.values.js\";\nimport \"core-js/modules/es.parse-int.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.starts-with.js\";\nimport \"core-js/modules/es.string.trim.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport PrivateChat from './PrivateChat.vue';\nimport websocketService from '@/services/websocket';\nimport { storageMixin } from '@/mixins/storageMixin';\nexport default {\n  name: 'PrivateChatManager',\n  mixins: [storageMixin],\n  components: {\n    PrivateChat: PrivateChat\n  },\n  props: {\n    onlineUsers: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    hidden: {\n      type: Boolean,\n      \"default\": false\n    }\n  },\n  data: function data() {\n    return {\n      showChatList: false,\n      activeChats: [],\n      // 活动的聊天窗口\n      chatMessages: {},\n      // 所有聊天消息\n      unreadCounts: {},\n      // 未读消息计数\n      searchQuery: '',\n      filteredUsers: [],\n      chatWindowWidth: 320,\n      // 聊天窗口宽度\n      chatWindowGap: 20,\n      // 聊天窗口间隙\n      maxVisibleChats: 3 // 最大可见聊天窗口数\n    };\n  },\n  computed: {\n    currentUser: function currentUser() {\n      return this.$store.getters.currentUser || {\n        id: 0,\n        username: '游客'\n      };\n    },\n    allUsers: function allUsers() {\n      var _this = this;\n      // 过滤掉当前用户\n      return this.onlineUsers.filter(function (user) {\n        return user.id !== _this.currentUser.id;\n      });\n    },\n    hasUnreadMessages: function hasUnreadMessages() {\n      return Object.values(this.unreadCounts).some(function (count) {\n        return count > 0;\n      });\n    },\n    totalUnreadCount: function totalUnreadCount() {\n      return Object.values(this.unreadCounts).reduce(function (sum, count) {\n        return sum + count;\n      }, 0);\n    }\n  },\n  watch: {\n    onlineUsers: {\n      handler: function handler(newUsers) {\n        // 更新过滤后的用户列表\n        this.filterUsers();\n      },\n      deep: true\n    }\n  },\n  mounted: function mounted() {\n    // 初始化过滤后的用户列表\n    this.filteredUsers = _toConsumableArray(this.allUsers);\n\n    // 添加私聊消息监听器\n    websocketService.addCustomEventListener('private_message', this.handlePrivateMessage);\n\n    // 加载未读消息计数\n    this.loadUnreadCounts();\n\n    // 加载最近聊天记录\n    this.loadRecentChats();\n\n    // 监听窗口大小变化\n    window.addEventListener('resize', this.updateMaxVisibleChats);\n    this.updateMaxVisibleChats();\n  },\n  beforeDestroy: function beforeDestroy() {\n    // 移除私聊消息监听器\n    websocketService.removeCustomEventListener('private_message', this.handlePrivateMessage);\n\n    // 移除窗口大小变化监听\n    window.removeEventListener('resize', this.updateMaxVisibleChats);\n  },\n  methods: {\n    // 额外的安全存储访问方法\n    safeGetStorageLength: function safeGetStorageLength() {\n      try {\n        if (window.storageManager && typeof window.storageManager.length === 'function') {\n          return window.storageManager.length();\n        } else if (window.storageManager && typeof window.storageManager.length === 'number') {\n          return window.storageManager.length;\n        }\n        return localStorage.length;\n      } catch (error) {\n        console.warn('[PrivateChatManager] 无法获取存储长度:', error.message);\n        return 0;\n      }\n    },\n    safeGetStorageKey: function safeGetStorageKey(index) {\n      try {\n        if (window.storageManager && window.storageManager.key) {\n          return window.storageManager.key(index);\n        }\n        return localStorage.key(index);\n      } catch (error) {\n        console.warn(\"[PrivateChatManager] \\u65E0\\u6CD5\\u83B7\\u53D6\\u5B58\\u50A8\\u952E \".concat(index, \":\"), error.message);\n        return null;\n      }\n    },\n    // 切换聊天列表显示状态\n    toggleChatList: function toggleChatList() {\n      this.showChatList = !this.showChatList;\n      if (this.showChatList) {\n        this.filterUsers();\n      }\n    },\n    // 根据搜索查询过滤用户\n    filterUsers: function filterUsers() {\n      if (!this.searchQuery.trim()) {\n        this.filteredUsers = _toConsumableArray(this.allUsers);\n        return;\n      }\n      var query = this.searchQuery.toLowerCase();\n      this.filteredUsers = this.allUsers.filter(function (user) {\n        return user.username.toLowerCase().includes(query);\n      });\n    },\n    // 开始与用户聊天\n    startChat: function startChat(user) {\n      if (!user || !user.id) return;\n\n      // 检查是否已经存在该聊天\n      var existingChat = this.activeChats.find(function (chat) {\n        return chat.userId === user.id;\n      });\n      if (existingChat) {\n        // 如果已存在，激活该聊天\n        this.activateChat(user.id);\n      } else {\n        // 如果不存在，创建新聊天\n        var position = this.calculateChatPosition(this.activeChats.length);\n        this.activeChats.push({\n          userId: user.id,\n          position: position,\n          active: true,\n          minimized: false\n        });\n\n        // 重新计算所有聊天窗口的位置\n        this.recalculateChatPositions();\n      }\n\n      // 重置该用户的未读消息计数\n      this.unreadCounts[user.id] = 0;\n      this.saveUnreadCounts();\n\n      // 关闭用户列表\n      this.showChatList = false;\n    },\n    // 关闭聊天\n    closeChat: function closeChat(userId) {\n      var index = this.activeChats.findIndex(function (chat) {\n        return chat.userId === userId;\n      });\n      if (index !== -1) {\n        this.activeChats.splice(index, 1);\n\n        // 重新计算所有聊天窗口的位置\n        this.recalculateChatPositions();\n      }\n    },\n    // 激活聊天\n    activateChat: function activateChat(userId) {\n      // 将所有聊天设为非活动状态\n      this.activeChats.forEach(function (chat) {\n        chat.active = chat.userId === userId;\n      });\n\n      // 重置该用户的未读消息计数\n      this.unreadCounts[userId] = 0;\n      this.saveUnreadCounts();\n    },\n    // 计算聊天窗口位置\n    calculateChatPosition: function calculateChatPosition(index) {\n      var visibleIndex = index % this.maxVisibleChats;\n      return visibleIndex * (this.chatWindowWidth + this.chatWindowGap);\n    },\n    // 重新计算所有聊天窗口的位置\n    recalculateChatPositions: function recalculateChatPositions() {\n      var _this2 = this;\n      this.activeChats.forEach(function (chat, index) {\n        chat.position = _this2.calculateChatPosition(index);\n      });\n    },\n    // 更新最大可见聊天窗口数\n    updateMaxVisibleChats: function updateMaxVisibleChats() {\n      var windowWidth = window.innerWidth;\n      this.maxVisibleChats = Math.max(1, Math.floor((windowWidth - 100) / (this.chatWindowWidth + this.chatWindowGap)));\n\n      // 重新计算所有聊天窗口的位置\n      this.recalculateChatPositions();\n    },\n    // 处理私聊消息\n    handlePrivateMessage: function handlePrivateMessage(message) {\n      if (!message || !message.sender_id || !message.recipient_id) return;\n\n      // 确定对话的另一方\n      var otherUserId = message.sender_id === this.currentUser.id ? message.recipient_id : message.sender_id;\n\n      // 初始化该用户的消息数组（如果不存在）\n      if (!this.chatMessages[otherUserId]) {\n        this.chatMessages[otherUserId] = [];\n      }\n\n      // 添加消息到数组\n      this.chatMessages[otherUserId].push(message);\n\n      // 如果是接收到的消息，且没有打开与该用户的聊天，增加未读计数\n      if (message.sender_id !== this.currentUser.id) {\n        var isActiveChatOpen = this.activeChats.some(function (chat) {\n          return chat.userId === message.sender_id && !chat.minimized;\n        });\n        if (!isActiveChatOpen) {\n          this.unreadCounts[message.sender_id] = (this.unreadCounts[message.sender_id] || 0) + 1;\n          this.saveUnreadCounts();\n\n          // 发送桌面通知\n          this.sendNotification(message);\n        }\n      }\n\n      // 保存消息到本地存储\n      this.saveChatMessages(otherUserId);\n    },\n    // 处理发送的消息\n    handleMessageSent: function handleMessageSent(message) {\n      this.handlePrivateMessage(message);\n    },\n    // 处理接收的消息\n    handleMessageReceived: function handleMessageReceived(message) {\n      // 消息已经在handlePrivateMessage中处理\n    },\n    // 处理新的私聊消息（不在当前活动聊天中）\n    handleNewPrivateMessage: function handleNewPrivateMessage(message) {\n      // 检查是否需要自动打开聊天窗口\n      var autoOpenChat = this.safeGetItem('auto_open_private_chat') === 'true';\n      if (autoOpenChat) {\n        // 自动打开与发送者的聊天窗口\n        var user = this.getUserById(message.sender_id);\n        if (user) {\n          this.startChat(user);\n        }\n      }\n    },\n    // 发送桌面通知\n    sendNotification: function sendNotification(message) {\n      var _this3 = this;\n      // 检查是否启用了通知\n      var notificationsEnabled = this.safeGetItem('enable_notifications') !== 'false';\n      if (notificationsEnabled && \"Notification\" in window) {\n        // 检查权限\n        if (Notification.permission === \"granted\") {\n          // 创建通知\n          var sender = this.getUserById(message.sender_id);\n          var senderName = sender ? sender.username : message.sender_name || '用户';\n          var notification = new Notification(\"\\u65B0\\u79C1\\u804A\\u6D88\\u606F - \".concat(senderName), {\n            body: message.content,\n            icon: '/favicon.ico'\n          });\n\n          // 点击通知时打开聊天\n          notification.onclick = function () {\n            window.focus();\n            _this3.startChat(sender);\n          };\n        } else if (Notification.permission !== \"denied\") {\n          // 请求权限\n          Notification.requestPermission();\n        }\n      }\n    },\n    // 获取用户通过ID\n    getUserById: function getUserById(userId) {\n      return this.onlineUsers.find(function (user) {\n        return user.id === userId;\n      });\n    },\n    // 检查用户是否在线\n    isUserOnline: function isUserOnline(userId) {\n      return this.onlineUsers.some(function (user) {\n        return user.id === userId;\n      });\n    },\n    // 获取与用户的最后一条消息\n    getLastMessage: function getLastMessage(userId) {\n      var messages = this.chatMessages[userId];\n      if (!messages || messages.length === 0) return null;\n      return messages[messages.length - 1];\n    },\n    // 格式化最后一条消息\n    formatLastMessage: function formatLastMessage(message) {\n      if (!message) return '';\n      var isSelf = message.sender_id === this.currentUser.id;\n      var prefix = isSelf ? '我: ' : '';\n      var content = message.content.length > 15 ? message.content.substring(0, 15) + '...' : message.content;\n      return prefix + content;\n    },\n    // 加载未读消息计数\n    loadUnreadCounts: function loadUnreadCounts() {\n      try {\n        var stored = this.safeGetItem(\"unread_counts_\".concat(this.currentUser.id));\n        if (stored) {\n          this.unreadCounts = JSON.parse(stored);\n        }\n      } catch (error) {\n        console.error('加载未读消息计数失败:', error);\n        this.unreadCounts = {};\n      }\n    },\n    // 保存未读消息计数\n    saveUnreadCounts: function saveUnreadCounts() {\n      try {\n        this.safeSetJSON(\"unread_counts_\".concat(this.currentUser.id), this.unreadCounts);\n      } catch (error) {\n        console.error('保存未读消息计数失败:', error);\n      }\n    },\n    // 加载聊天消息\n    loadChatMessages: function loadChatMessages(userId) {\n      try {\n        var chatKey = \"private_chat_\".concat(this.currentUser.id, \"_\").concat(userId);\n        var stored = this.safeGetItem(chatKey);\n        if (stored) {\n          var parsed = JSON.parse(stored);\n          this.chatMessages[userId] = parsed.messages || [];\n        } else {\n          this.chatMessages[userId] = [];\n        }\n      } catch (error) {\n        console.error(\"\\u52A0\\u8F7D\\u4E0E\\u7528\\u6237 \".concat(userId, \" \\u7684\\u804A\\u5929\\u6D88\\u606F\\u5931\\u8D25:\"), error);\n        this.chatMessages[userId] = [];\n      }\n    },\n    // 保存聊天消息\n    saveChatMessages: function saveChatMessages(userId) {\n      try {\n        var messages = this.chatMessages[userId];\n        if (!messages) return;\n\n        // 最多保存100条消息\n        var messagesToSave = messages.slice(-100);\n        var chatKey = \"private_chat_\".concat(this.currentUser.id, \"_\").concat(userId);\n        this.safeSetJSON(chatKey, {\n          lastUpdated: new Date().toISOString(),\n          messages: messagesToSave\n        });\n      } catch (error) {\n        console.error(\"\\u4FDD\\u5B58\\u4E0E\\u7528\\u6237 \".concat(userId, \" \\u7684\\u804A\\u5929\\u6D88\\u606F\\u5931\\u8D25:\"), error);\n      }\n    },\n    // 加载最近聊天\n    loadRecentChats: function loadRecentChats() {\n      try {\n        // 获取所有本地存储的聊天记录\n        var recentChats = [];\n        for (var i = 0; i < this.safeGetStorageLength(); i++) {\n          var key = this.safeGetStorageKey(i);\n          if (key.startsWith(\"private_chat_\".concat(this.currentUser.id, \"_\"))) {\n            var userId = parseInt(key.split('_')[3]);\n            if (!isNaN(userId)) {\n              // 加载聊天消息\n              this.loadChatMessages(userId);\n\n              // 获取最后更新时间\n              var stored = this.safeGetItem(key);\n              if (stored) {\n                try {\n                  var parsed = JSON.parse(stored);\n                  recentChats.push({\n                    userId: userId,\n                    lastUpdated: parsed.lastUpdated || new Date(0).toISOString()\n                  });\n                } catch (e) {\n                  console.error('解析聊天记录失败:', e);\n                }\n              }\n            }\n          }\n        }\n\n        // 按最后更新时间排序\n        recentChats.sort(function (a, b) {\n          return new Date(b.lastUpdated) - new Date(a.lastUpdated);\n        });\n\n        // 自动打开最近的聊天（可选）\n        var autoOpenRecent = this.safeGetItem('auto_open_recent_chat') === 'true';\n        if (autoOpenRecent && recentChats.length > 0) {\n          var mostRecent = recentChats[0];\n          var user = this.getUserById(mostRecent.userId);\n          if (user) {\n            this.startChat(user);\n          }\n        }\n      } catch (error) {\n        console.error('加载最近聊天失败:', error);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["PrivateChat", "websocketService", "storageMixin", "name", "mixins", "components", "props", "onlineUsers", "type", "Array", "default", "hidden", "Boolean", "data", "showChatList", "activeChats", "chatMessages", "unreadCounts", "searchQuery", "filteredUsers", "chatWindowWidth", "chatWindowGap", "maxVisibleChats", "computed", "currentUser", "$store", "getters", "id", "username", "allUsers", "_this", "filter", "user", "hasUnreadMessages", "Object", "values", "some", "count", "totalUnreadCount", "reduce", "sum", "watch", "handler", "newUsers", "filterUsers", "deep", "mounted", "_toConsumableArray", "addCustomEventListener", "handlePrivateMessage", "loadUnreadCounts", "loadRecentChats", "window", "addEventListener", "updateMaxVisibleChats", "<PERSON><PERSON><PERSON><PERSON>", "removeCustomEventListener", "removeEventListener", "methods", "safeGetStorageLength", "storageManager", "length", "localStorage", "error", "console", "warn", "message", "safeGetStorageKey", "index", "key", "concat", "toggleChatList", "trim", "query", "toLowerCase", "includes", "startChat", "existingChat", "find", "chat", "userId", "activateChat", "position", "calculateChatPosition", "push", "active", "minimized", "recalculateChatPositions", "saveUnreadCounts", "closeChat", "findIndex", "splice", "for<PERSON>ach", "visibleIndex", "_this2", "windowWidth", "innerWidth", "Math", "max", "floor", "sender_id", "recipient_id", "otherUserId", "isActiveChatOpen", "sendNotification", "saveChatMessages", "handleMessageSent", "handleMessageReceived", "handleNewPrivateMessage", "autoOpenChat", "safeGetItem", "getUserById", "_this3", "notificationsEnabled", "Notification", "permission", "sender", "sender<PERSON>ame", "sender_name", "notification", "body", "content", "icon", "onclick", "focus", "requestPermission", "isUserOnline", "getLastMessage", "messages", "formatLastMessage", "isSelf", "prefix", "substring", "stored", "JSON", "parse", "safeSetJSON", "loadChatMessages", "<PERSON><PERSON><PERSON>", "parsed", "messagesToSave", "slice", "lastUpdated", "Date", "toISOString", "recentChats", "i", "startsWith", "parseInt", "split", "isNaN", "e", "sort", "a", "b", "autoOpenRecent", "mostRecent"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\PrivateChatManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"private-chat-manager\" :class=\"{ 'hidden': hidden }\">\r\n    <!-- 私聊按钮 -->\r\n    <div class=\"chat-button\" @click=\"toggleChatList\" :class=\"{ 'has-unread': hasUnreadMessages }\">\r\n      <i class=\"fas fa-comments\"></i>\r\n      <span v-if=\"hasUnreadMessages\" class=\"unread-badge\">{{ totalUnreadCount }}</span>\r\n    </div>\r\n    \r\n    <!-- 用户列表弹窗 -->\r\n    <div v-if=\"showChatList\" class=\"chat-list-popup\">\r\n      <div class=\"popup-header\">\r\n        <h3>私聊</h3>\r\n        <button class=\"close-btn\" @click=\"toggleChatList\">×</button>\r\n      </div>\r\n      \r\n      <div class=\"user-search\">\r\n        <input \r\n          type=\"text\" \r\n          v-model=\"searchQuery\" \r\n          placeholder=\"搜索用户...\" \r\n          @input=\"filterUsers\"\r\n        />\r\n      </div>\r\n      \r\n      <div class=\"users-list\">\r\n        <div v-if=\"filteredUsers.length === 0\" class=\"no-users\">\r\n          <p>没有找到用户</p>\r\n        </div>\r\n        \r\n        <div \r\n          v-for=\"user in filteredUsers\" \r\n          :key=\"user.id\"\r\n          class=\"user-item\"\r\n          :class=\"{ 'has-unread': unreadCounts[user.id] > 0 }\"\r\n          @click=\"startChat(user)\"\r\n        >\r\n          <div class=\"user-avatar\">\r\n            <i class=\"fas fa-user\"></i>\r\n          </div>\r\n          <div class=\"user-info\">\r\n            <div class=\"user-name\">{{ user.username }}</div>\r\n            <div class=\"last-message\" v-if=\"getLastMessage(user.id)\">\r\n              {{ formatLastMessage(getLastMessage(user.id)) }}\r\n            </div>\r\n          </div>\r\n          <div class=\"user-status\">\r\n            <span v-if=\"unreadCounts[user.id]\" class=\"unread-count\">{{ unreadCounts[user.id] }}</span>\r\n            <span v-else-if=\"isUserOnline(user.id)\" class=\"online-indicator\"></span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 活动聊天窗口 -->\r\n    <div v-for=\"chat in activeChats\" :key=\"chat.userId\" class=\"chat-window\"\r\n         :style=\"{ left: chat.position + 'px', zIndex: chat.active ? 10 : 5 }\"\r\n         :class=\"{ 'active': chat.active }\">\r\n      <private-chat\r\n        :target-user=\"getUserById(chat.userId)\"\r\n        @close=\"closeChat(chat.userId)\"\r\n        @message-sent=\"handleMessageSent\"\r\n        @message-received=\"handleMessageReceived\"\r\n        @new-private-message=\"handleNewPrivateMessage\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PrivateChat from './PrivateChat.vue';\r\nimport websocketService from '@/services/websocket';\r\nimport { storageMixin } from '@/mixins/storageMixin';\r\n\r\nexport default {\r\n  name: 'PrivateChatManager',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    PrivateChat\r\n  },\r\n  props: {\r\n    onlineUsers: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    hidden: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      showChatList: false,\r\n      activeChats: [], // 活动的聊天窗口\r\n      chatMessages: {}, // 所有聊天消息\r\n      unreadCounts: {}, // 未读消息计数\r\n      searchQuery: '',\r\n      filteredUsers: [],\r\n      chatWindowWidth: 320, // 聊天窗口宽度\r\n      chatWindowGap: 20, // 聊天窗口间隙\r\n      maxVisibleChats: 3 // 最大可见聊天窗口数\r\n    };\r\n  },\r\n  computed: {\r\n    currentUser() {\r\n      return this.$store.getters.currentUser || { id: 0, username: '游客' };\r\n    },\r\n    allUsers() {\r\n      // 过滤掉当前用户\r\n      return this.onlineUsers.filter(user => user.id !== this.currentUser.id);\r\n    },\r\n    hasUnreadMessages() {\r\n      return Object.values(this.unreadCounts).some(count => count > 0);\r\n    },\r\n    totalUnreadCount() {\r\n      return Object.values(this.unreadCounts).reduce((sum, count) => sum + count, 0);\r\n    }\r\n  },\r\n  watch: {\r\n    onlineUsers: {\r\n      handler(newUsers) {\r\n        // 更新过滤后的用户列表\r\n        this.filterUsers();\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    // 初始化过滤后的用户列表\r\n    this.filteredUsers = [...this.allUsers];\r\n    \r\n    // 添加私聊消息监听器\r\n    websocketService.addCustomEventListener('private_message', this.handlePrivateMessage);\r\n    \r\n    // 加载未读消息计数\r\n    this.loadUnreadCounts();\r\n    \r\n    // 加载最近聊天记录\r\n    this.loadRecentChats();\r\n    \r\n    // 监听窗口大小变化\r\n    window.addEventListener('resize', this.updateMaxVisibleChats);\r\n    this.updateMaxVisibleChats();\r\n  },\r\n  beforeDestroy() {\r\n    // 移除私聊消息监听器\r\n    websocketService.removeCustomEventListener('private_message', this.handlePrivateMessage);\r\n    \r\n    // 移除窗口大小变化监听\r\n    window.removeEventListener('resize', this.updateMaxVisibleChats);\r\n  },\r\n  methods: {\r\n    // 额外的安全存储访问方法\r\n    safeGetStorageLength() {\r\n      try {\r\n        if (window.storageManager && typeof window.storageManager.length === 'function') {\r\n          return window.storageManager.length();\r\n        } else if (window.storageManager && typeof window.storageManager.length === 'number') {\r\n          return window.storageManager.length;\r\n        }\r\n        return localStorage.length;\r\n      } catch (error) {\r\n        console.warn('[PrivateChatManager] 无法获取存储长度:', error.message);\r\n        return 0;\r\n      }\r\n    },\r\n    \r\n    safeGetStorageKey(index) {\r\n      try {\r\n        if (window.storageManager && window.storageManager.key) {\r\n          return window.storageManager.key(index);\r\n        }\r\n        return localStorage.key(index);\r\n      } catch (error) {\r\n        console.warn(`[PrivateChatManager] 无法获取存储键 ${index}:`, error.message);\r\n        return null;\r\n      }\r\n    },\r\n\r\n    // 切换聊天列表显示状态\r\n    toggleChatList() {\r\n      this.showChatList = !this.showChatList;\r\n      if (this.showChatList) {\r\n        this.filterUsers();\r\n      }\r\n    },\r\n    \r\n    // 根据搜索查询过滤用户\r\n    filterUsers() {\r\n      if (!this.searchQuery.trim()) {\r\n        this.filteredUsers = [...this.allUsers];\r\n        return;\r\n      }\r\n      \r\n      const query = this.searchQuery.toLowerCase();\r\n      this.filteredUsers = this.allUsers.filter(user => \r\n        user.username.toLowerCase().includes(query)\r\n      );\r\n    },\r\n    \r\n    // 开始与用户聊天\r\n    startChat(user) {\r\n      if (!user || !user.id) return;\r\n      \r\n      // 检查是否已经存在该聊天\r\n      const existingChat = this.activeChats.find(chat => chat.userId === user.id);\r\n      \r\n      if (existingChat) {\r\n        // 如果已存在，激活该聊天\r\n        this.activateChat(user.id);\r\n      } else {\r\n        // 如果不存在，创建新聊天\r\n        const position = this.calculateChatPosition(this.activeChats.length);\r\n        \r\n        this.activeChats.push({\r\n          userId: user.id,\r\n          position,\r\n          active: true,\r\n          minimized: false\r\n        });\r\n        \r\n        // 重新计算所有聊天窗口的位置\r\n        this.recalculateChatPositions();\r\n      }\r\n      \r\n      // 重置该用户的未读消息计数\r\n      this.unreadCounts[user.id] = 0;\r\n      this.saveUnreadCounts();\r\n      \r\n      // 关闭用户列表\r\n      this.showChatList = false;\r\n    },\r\n    \r\n    // 关闭聊天\r\n    closeChat(userId) {\r\n      const index = this.activeChats.findIndex(chat => chat.userId === userId);\r\n      if (index !== -1) {\r\n        this.activeChats.splice(index, 1);\r\n        \r\n        // 重新计算所有聊天窗口的位置\r\n        this.recalculateChatPositions();\r\n      }\r\n    },\r\n    \r\n    // 激活聊天\r\n    activateChat(userId) {\r\n      // 将所有聊天设为非活动状态\r\n      this.activeChats.forEach(chat => {\r\n        chat.active = chat.userId === userId;\r\n      });\r\n      \r\n      // 重置该用户的未读消息计数\r\n      this.unreadCounts[userId] = 0;\r\n      this.saveUnreadCounts();\r\n    },\r\n    \r\n    // 计算聊天窗口位置\r\n    calculateChatPosition(index) {\r\n      const visibleIndex = index % this.maxVisibleChats;\r\n      return visibleIndex * (this.chatWindowWidth + this.chatWindowGap);\r\n    },\r\n    \r\n    // 重新计算所有聊天窗口的位置\r\n    recalculateChatPositions() {\r\n      this.activeChats.forEach((chat, index) => {\r\n        chat.position = this.calculateChatPosition(index);\r\n      });\r\n    },\r\n    \r\n    // 更新最大可见聊天窗口数\r\n    updateMaxVisibleChats() {\r\n      const windowWidth = window.innerWidth;\r\n      this.maxVisibleChats = Math.max(1, Math.floor((windowWidth - 100) / (this.chatWindowWidth + this.chatWindowGap)));\r\n      \r\n      // 重新计算所有聊天窗口的位置\r\n      this.recalculateChatPositions();\r\n    },\r\n    \r\n    // 处理私聊消息\r\n    handlePrivateMessage(message) {\r\n      if (!message || !message.sender_id || !message.recipient_id) return;\r\n      \r\n      // 确定对话的另一方\r\n      const otherUserId = message.sender_id === this.currentUser.id ? \r\n        message.recipient_id : message.sender_id;\r\n      \r\n      // 初始化该用户的消息数组（如果不存在）\r\n      if (!this.chatMessages[otherUserId]) {\r\n        this.chatMessages[otherUserId] = [];\r\n      }\r\n      \r\n      // 添加消息到数组\r\n      this.chatMessages[otherUserId].push(message);\r\n      \r\n      // 如果是接收到的消息，且没有打开与该用户的聊天，增加未读计数\r\n      if (message.sender_id !== this.currentUser.id) {\r\n        const isActiveChatOpen = this.activeChats.some(chat => \r\n          chat.userId === message.sender_id && !chat.minimized\r\n        );\r\n        \r\n        if (!isActiveChatOpen) {\r\n          this.unreadCounts[message.sender_id] = (this.unreadCounts[message.sender_id] || 0) + 1;\r\n          this.saveUnreadCounts();\r\n          \r\n          // 发送桌面通知\r\n          this.sendNotification(message);\r\n        }\r\n      }\r\n      \r\n      // 保存消息到本地存储\r\n      this.saveChatMessages(otherUserId);\r\n    },\r\n    \r\n    // 处理发送的消息\r\n    handleMessageSent(message) {\r\n      this.handlePrivateMessage(message);\r\n    },\r\n    \r\n    // 处理接收的消息\r\n    handleMessageReceived(message) {\r\n      // 消息已经在handlePrivateMessage中处理\r\n    },\r\n    \r\n    // 处理新的私聊消息（不在当前活动聊天中）\r\n    handleNewPrivateMessage(message) {\r\n      // 检查是否需要自动打开聊天窗口\r\n      const autoOpenChat = this.safeGetItem('auto_open_private_chat') === 'true';\r\n      \r\n      if (autoOpenChat) {\r\n        // 自动打开与发送者的聊天窗口\r\n        const user = this.getUserById(message.sender_id);\r\n        if (user) {\r\n          this.startChat(user);\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 发送桌面通知\r\n    sendNotification(message) {\r\n      // 检查是否启用了通知\r\n      const notificationsEnabled = this.safeGetItem('enable_notifications') !== 'false';\r\n      \r\n      if (notificationsEnabled && \"Notification\" in window) {\r\n        // 检查权限\r\n        if (Notification.permission === \"granted\") {\r\n          // 创建通知\r\n          const sender = this.getUserById(message.sender_id);\r\n          const senderName = sender ? sender.username : message.sender_name || '用户';\r\n          \r\n          const notification = new Notification(`新私聊消息 - ${senderName}`, {\r\n            body: message.content,\r\n            icon: '/favicon.ico'\r\n          });\r\n          \r\n          // 点击通知时打开聊天\r\n          notification.onclick = () => {\r\n            window.focus();\r\n            this.startChat(sender);\r\n          };\r\n        } else if (Notification.permission !== \"denied\") {\r\n          // 请求权限\r\n          Notification.requestPermission();\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 获取用户通过ID\r\n    getUserById(userId) {\r\n      return this.onlineUsers.find(user => user.id === userId);\r\n    },\r\n    \r\n    // 检查用户是否在线\r\n    isUserOnline(userId) {\r\n      return this.onlineUsers.some(user => user.id === userId);\r\n    },\r\n    \r\n    // 获取与用户的最后一条消息\r\n    getLastMessage(userId) {\r\n      const messages = this.chatMessages[userId];\r\n      if (!messages || messages.length === 0) return null;\r\n      \r\n      return messages[messages.length - 1];\r\n    },\r\n    \r\n    // 格式化最后一条消息\r\n    formatLastMessage(message) {\r\n      if (!message) return '';\r\n      \r\n      const isSelf = message.sender_id === this.currentUser.id;\r\n      const prefix = isSelf ? '我: ' : '';\r\n      const content = message.content.length > 15 ? \r\n        message.content.substring(0, 15) + '...' : \r\n        message.content;\r\n      \r\n      return prefix + content;\r\n    },\r\n    \r\n    // 加载未读消息计数\r\n    loadUnreadCounts() {\r\n      try {\r\n        const stored = this.safeGetItem(`unread_counts_${this.currentUser.id}`);\r\n        if (stored) {\r\n          this.unreadCounts = JSON.parse(stored);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载未读消息计数失败:', error);\r\n        this.unreadCounts = {};\r\n      }\r\n    },\r\n    \r\n    // 保存未读消息计数\r\n    saveUnreadCounts() {\r\n      try {\r\n        this.safeSetJSON(`unread_counts_${this.currentUser.id}`, this.unreadCounts);\r\n      } catch (error) {\r\n        console.error('保存未读消息计数失败:', error);\r\n      }\r\n    },\r\n    \r\n    // 加载聊天消息\r\n    loadChatMessages(userId) {\r\n      try {\r\n        const chatKey = `private_chat_${this.currentUser.id}_${userId}`;\r\n        const stored = this.safeGetItem(chatKey);\r\n        \r\n        if (stored) {\r\n          const parsed = JSON.parse(stored);\r\n          this.chatMessages[userId] = parsed.messages || [];\r\n        } else {\r\n          this.chatMessages[userId] = [];\r\n        }\r\n      } catch (error) {\r\n        console.error(`加载与用户 ${userId} 的聊天消息失败:`, error);\r\n        this.chatMessages[userId] = [];\r\n      }\r\n    },\r\n    \r\n    // 保存聊天消息\r\n    saveChatMessages(userId) {\r\n      try {\r\n        const messages = this.chatMessages[userId];\r\n        if (!messages) return;\r\n        \r\n        // 最多保存100条消息\r\n        const messagesToSave = messages.slice(-100);\r\n        \r\n        const chatKey = `private_chat_${this.currentUser.id}_${userId}`;\r\n        this.safeSetJSON(chatKey, {\r\n          lastUpdated: new Date().toISOString(),\r\n          messages: messagesToSave\r\n        });\r\n      } catch (error) {\r\n        console.error(`保存与用户 ${userId} 的聊天消息失败:`, error);\r\n      }\r\n    },\r\n    \r\n    // 加载最近聊天\r\n    loadRecentChats() {\r\n      try {\r\n        // 获取所有本地存储的聊天记录\r\n        const recentChats = [];\r\n        \r\n        for (let i = 0; i < this.safeGetStorageLength(); i++) {\r\n          const key = this.safeGetStorageKey(i);\r\n          if (key.startsWith(`private_chat_${this.currentUser.id}_`)) {\r\n            const userId = parseInt(key.split('_')[3]);\r\n            if (!isNaN(userId)) {\r\n              // 加载聊天消息\r\n              this.loadChatMessages(userId);\r\n              \r\n              // 获取最后更新时间\r\n              const stored = this.safeGetItem(key);\r\n              if (stored) {\r\n                try {\r\n                  const parsed = JSON.parse(stored);\r\n                  recentChats.push({\r\n                    userId,\r\n                    lastUpdated: parsed.lastUpdated || new Date(0).toISOString()\r\n                  });\r\n                } catch (e) {\r\n                  console.error('解析聊天记录失败:', e);\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 按最后更新时间排序\r\n        recentChats.sort((a, b) => \r\n          new Date(b.lastUpdated) - new Date(a.lastUpdated)\r\n        );\r\n        \r\n        // 自动打开最近的聊天（可选）\r\n        const autoOpenRecent = this.safeGetItem('auto_open_recent_chat') === 'true';\r\n        if (autoOpenRecent && recentChats.length > 0) {\r\n          const mostRecent = recentChats[0];\r\n          const user = this.getUserById(mostRecent.userId);\r\n          if (user) {\r\n            this.startChat(user);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('加载最近聊天失败:', error);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.private-chat-manager {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  left: 20px;\r\n  z-index: 1000;\r\n  transition: transform 0.3s ease, opacity 0.3s ease;\r\n}\r\n\r\n.private-chat-manager.hidden {\r\n  transform: translateX(-100px);\r\n  opacity: 0;\r\n  pointer-events: none;\r\n}\r\n\r\n.chat-button {\r\n  width: 50px;\r\n  height: 50px;\r\n  border-radius: 50%;\r\n  background-color: #3498db;\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);\r\n  position: relative;\r\n  transition: all 0.3s;\r\n}\r\n\r\n.chat-button:hover {\r\n  background-color: #2980b9;\r\n  transform: scale(1.05);\r\n}\r\n\r\n.chat-button.has-unread {\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);\r\n  }\r\n}\r\n\r\n.unread-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background-color: #e74c3c;\r\n  color: white;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 0.7rem;\r\n  font-weight: bold;\r\n  border: 2px solid #2a2a2a;\r\n}\r\n\r\n.chat-list-popup {\r\n  position: absolute;\r\n  bottom: 60px;\r\n  left: 0;\r\n  width: 280px;\r\n  max-height: 400px;\r\n  background-color: #2a2a2a;\r\n  border-radius: 8px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  z-index: 1001;\r\n}\r\n\r\n.popup-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  background-color: #333;\r\n  border-bottom: 1px solid #444;\r\n}\r\n\r\n.popup-header h3 {\r\n  margin: 0;\r\n  color: #e0e0e0;\r\n  font-size: 1rem;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #b0b0b0;\r\n  font-size: 1.2rem;\r\n  cursor: pointer;\r\n  padding: 0 5px;\r\n}\r\n\r\n.close-btn:hover {\r\n  color: #e74c3c;\r\n}\r\n\r\n.user-search {\r\n  padding: 10px;\r\n  border-bottom: 1px solid #444;\r\n}\r\n\r\n.user-search input {\r\n  width: 100%;\r\n  padding: 8px 10px;\r\n  border-radius: 4px;\r\n  border: 1px solid #555;\r\n  background-color: #333;\r\n  color: #e0e0e0;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.user-search input:focus {\r\n  outline: none;\r\n  border-color: #3498db;\r\n}\r\n\r\n.users-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  max-height: 300px;\r\n}\r\n\r\n.no-users {\r\n  padding: 20px;\r\n  text-align: center;\r\n  color: #888;\r\n}\r\n\r\n.user-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px 15px;\r\n  cursor: pointer;\r\n  border-bottom: 1px solid #444;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.user-item:hover {\r\n  background-color: #333;\r\n}\r\n\r\n.user-item.has-unread {\r\n  background-color: rgba(52, 152, 219, 0.1);\r\n}\r\n\r\n.user-avatar {\r\n  width: 36px;\r\n  height: 36px;\r\n  border-radius: 50%;\r\n  background-color: #444;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 10px;\r\n  color: #e0e0e0;\r\n}\r\n\r\n.user-info {\r\n  flex: 1;\r\n  overflow: hidden;\r\n}\r\n\r\n.user-name {\r\n  color: #e0e0e0;\r\n  font-weight: 500;\r\n  margin-bottom: 3px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.last-message {\r\n  color: #b0b0b0;\r\n  font-size: 0.8rem;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.user-status {\r\n  margin-left: 10px;\r\n}\r\n\r\n.online-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background-color: #2ecc71;\r\n  display: inline-block;\r\n}\r\n\r\n.unread-count {\r\n  background-color: #e74c3c;\r\n  color: white;\r\n  border-radius: 10px;\r\n  padding: 2px 6px;\r\n  font-size: 0.7rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.chat-window {\r\n  position: fixed;\r\n  bottom: 20px;\r\n  width: 320px;\r\n  height: 400px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);\r\n  overflow: hidden;\r\n  transition: box-shadow 0.3s;\r\n}\r\n\r\n.chat-window.active {\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);\r\n}\r\n\r\n/* 移动设备适配 */\r\n@media (max-width: 768px) {\r\n  .chat-window {\r\n    width: 280px;\r\n    height: 350px;\r\n  }\r\n  \r\n  .chat-list-popup {\r\n    width: 250px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA,OAAOA,WAAU,MAAO,mBAAmB;AAC3C,OAAOC,gBAAe,MAAO,sBAAsB;AACnD,SAASC,YAAW,QAAS,uBAAuB;AAEpD,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,MAAM,EAAE,CAACF,YAAY,CAAC;EACtBG,UAAU,EAAE;IACVL,WAAU,EAAVA;EACF,CAAC;EACDM,KAAK,EAAE;IACLC,WAAW,EAAE;MACXC,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDC,MAAM,EAAE;MACNH,IAAI,EAAEI,OAAO;MACb,WAAS;IACX;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE,EAAE;MAAE;MACjBC,YAAY,EAAE,CAAC,CAAC;MAAE;MAClBC,YAAY,EAAE,CAAC,CAAC;MAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,eAAe,EAAE,GAAG;MAAE;MACtBC,aAAa,EAAE,EAAE;MAAE;MACnBC,eAAe,EAAE,EAAE;IACrB,CAAC;EACH,CAAC;EACDC,QAAQ,EAAE;IACRC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACC,MAAM,CAACC,OAAO,CAACF,WAAU,IAAK;QAAEG,EAAE,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC;IACrE,CAAC;IACDC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACT;MACA,OAAO,IAAI,CAACvB,WAAW,CAACwB,MAAM,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACL,EAAC,KAAMG,KAAI,CAACN,WAAW,CAACG,EAAE;MAAA,EAAC;IACzE,CAAC;IACDM,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,OAAOC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAClB,YAAY,CAAC,CAACmB,IAAI,CAAC,UAAAC,KAAI;QAAA,OAAKA,KAAI,GAAI,CAAC;MAAA,EAAC;IAClE,CAAC;IACDC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,OAAOJ,MAAM,CAACC,MAAM,CAAC,IAAI,CAAClB,YAAY,CAAC,CAACsB,MAAM,CAAC,UAACC,GAAG,EAAEH,KAAK;QAAA,OAAKG,GAAE,GAAIH,KAAK;MAAA,GAAE,CAAC,CAAC;IAChF;EACF,CAAC;EACDI,KAAK,EAAE;IACLlC,WAAW,EAAE;MACXmC,OAAO,WAAPA,OAAOA,CAACC,QAAQ,EAAE;QAChB;QACA,IAAI,CAACC,WAAW,CAAC,CAAC;MACpB,CAAC;MACDC,IAAI,EAAE;IACR;EACF,CAAC;EACDC,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR;IACA,IAAI,CAAC3B,aAAY,GAAA4B,kBAAA,CAAQ,IAAI,CAAClB,QAAQ,CAAC;;IAEvC;IACA5B,gBAAgB,CAAC+C,sBAAsB,CAAC,iBAAiB,EAAE,IAAI,CAACC,oBAAoB,CAAC;;IAErF;IACA,IAAI,CAACC,gBAAgB,CAAC,CAAC;;IAEvB;IACA,IAAI,CAACC,eAAe,CAAC,CAAC;;IAEtB;IACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,qBAAqB,CAAC;IAC7D,IAAI,CAACA,qBAAqB,CAAC,CAAC;EAC9B,CAAC;EACDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd;IACAtD,gBAAgB,CAACuD,yBAAyB,CAAC,iBAAiB,EAAE,IAAI,CAACP,oBAAoB,CAAC;;IAExF;IACAG,MAAM,CAACK,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACH,qBAAqB,CAAC;EAClE,CAAC;EACDI,OAAO,EAAE;IACP;IACAC,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,IAAI;QACF,IAAIP,MAAM,CAACQ,cAAa,IAAK,OAAOR,MAAM,CAACQ,cAAc,CAACC,MAAK,KAAM,UAAU,EAAE;UAC/E,OAAOT,MAAM,CAACQ,cAAc,CAACC,MAAM,CAAC,CAAC;QACvC,OAAO,IAAIT,MAAM,CAACQ,cAAa,IAAK,OAAOR,MAAM,CAACQ,cAAc,CAACC,MAAK,KAAM,QAAQ,EAAE;UACpF,OAAOT,MAAM,CAACQ,cAAc,CAACC,MAAM;QACrC;QACA,OAAOC,YAAY,CAACD,MAAM;MAC5B,EAAE,OAAOE,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,gCAAgC,EAAEF,KAAK,CAACG,OAAO,CAAC;QAC7D,OAAO,CAAC;MACV;IACF,CAAC;IAEDC,iBAAiB,WAAjBA,iBAAiBA,CAACC,KAAK,EAAE;MACvB,IAAI;QACF,IAAIhB,MAAM,CAACQ,cAAa,IAAKR,MAAM,CAACQ,cAAc,CAACS,GAAG,EAAE;UACtD,OAAOjB,MAAM,CAACQ,cAAc,CAACS,GAAG,CAACD,KAAK,CAAC;QACzC;QACA,OAAON,YAAY,CAACO,GAAG,CAACD,KAAK,CAAC;MAChC,EAAE,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,oEAAAK,MAAA,CAAiCF,KAAK,QAAKL,KAAK,CAACG,OAAO,CAAC;QACrE,OAAO,IAAI;MACb;IACF,CAAC;IAED;IACAK,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAI,CAACzD,YAAW,GAAI,CAAC,IAAI,CAACA,YAAY;MACtC,IAAI,IAAI,CAACA,YAAY,EAAE;QACrB,IAAI,CAAC8B,WAAW,CAAC,CAAC;MACpB;IACF,CAAC;IAED;IACAA,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC,IAAI,CAAC1B,WAAW,CAACsD,IAAI,CAAC,CAAC,EAAE;QAC5B,IAAI,CAACrD,aAAY,GAAA4B,kBAAA,CAAQ,IAAI,CAAClB,QAAQ,CAAC;QACvC;MACF;MAEA,IAAM4C,KAAI,GAAI,IAAI,CAACvD,WAAW,CAACwD,WAAW,CAAC,CAAC;MAC5C,IAAI,CAACvD,aAAY,GAAI,IAAI,CAACU,QAAQ,CAACE,MAAM,CAAC,UAAAC,IAAG;QAAA,OAC3CA,IAAI,CAACJ,QAAQ,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,KAAK;MAAA,CAC5C,CAAC;IACH,CAAC;IAED;IACAG,SAAS,WAATA,SAASA,CAAC5C,IAAI,EAAE;MACd,IAAI,CAACA,IAAG,IAAK,CAACA,IAAI,CAACL,EAAE,EAAE;;MAEvB;MACA,IAAMkD,YAAW,GAAI,IAAI,CAAC9D,WAAW,CAAC+D,IAAI,CAAC,UAAAC,IAAG;QAAA,OAAKA,IAAI,CAACC,MAAK,KAAMhD,IAAI,CAACL,EAAE;MAAA,EAAC;MAE3E,IAAIkD,YAAY,EAAE;QAChB;QACA,IAAI,CAACI,YAAY,CAACjD,IAAI,CAACL,EAAE,CAAC;MAC5B,OAAO;QACL;QACA,IAAMuD,QAAO,GAAI,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACpE,WAAW,CAAC8C,MAAM,CAAC;QAEpE,IAAI,CAAC9C,WAAW,CAACqE,IAAI,CAAC;UACpBJ,MAAM,EAAEhD,IAAI,CAACL,EAAE;UACfuD,QAAQ,EAARA,QAAQ;UACRG,MAAM,EAAE,IAAI;UACZC,SAAS,EAAE;QACb,CAAC,CAAC;;QAEF;QACA,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACjC;;MAEA;MACA,IAAI,CAACtE,YAAY,CAACe,IAAI,CAACL,EAAE,IAAI,CAAC;MAC9B,IAAI,CAAC6D,gBAAgB,CAAC,CAAC;;MAEvB;MACA,IAAI,CAAC1E,YAAW,GAAI,KAAK;IAC3B,CAAC;IAED;IACA2E,SAAS,WAATA,SAASA,CAACT,MAAM,EAAE;MAChB,IAAMZ,KAAI,GAAI,IAAI,CAACrD,WAAW,CAAC2E,SAAS,CAAC,UAAAX,IAAG;QAAA,OAAKA,IAAI,CAACC,MAAK,KAAMA,MAAM;MAAA,EAAC;MACxE,IAAIZ,KAAI,KAAM,CAAC,CAAC,EAAE;QAChB,IAAI,CAACrD,WAAW,CAAC4E,MAAM,CAACvB,KAAK,EAAE,CAAC,CAAC;;QAEjC;QACA,IAAI,CAACmB,wBAAwB,CAAC,CAAC;MACjC;IACF,CAAC;IAED;IACAN,YAAY,WAAZA,YAAYA,CAACD,MAAM,EAAE;MACnB;MACA,IAAI,CAACjE,WAAW,CAAC6E,OAAO,CAAC,UAAAb,IAAG,EAAK;QAC/BA,IAAI,CAACM,MAAK,GAAIN,IAAI,CAACC,MAAK,KAAMA,MAAM;MACtC,CAAC,CAAC;;MAEF;MACA,IAAI,CAAC/D,YAAY,CAAC+D,MAAM,IAAI,CAAC;MAC7B,IAAI,CAACQ,gBAAgB,CAAC,CAAC;IACzB,CAAC;IAED;IACAL,qBAAqB,WAArBA,qBAAqBA,CAACf,KAAK,EAAE;MAC3B,IAAMyB,YAAW,GAAIzB,KAAI,GAAI,IAAI,CAAC9C,eAAe;MACjD,OAAOuE,YAAW,IAAK,IAAI,CAACzE,eAAc,GAAI,IAAI,CAACC,aAAa,CAAC;IACnE,CAAC;IAED;IACAkE,wBAAwB,WAAxBA,wBAAwBA,CAAA,EAAG;MAAA,IAAAO,MAAA;MACzB,IAAI,CAAC/E,WAAW,CAAC6E,OAAO,CAAC,UAACb,IAAI,EAAEX,KAAK,EAAK;QACxCW,IAAI,CAACG,QAAO,GAAIY,MAAI,CAACX,qBAAqB,CAACf,KAAK,CAAC;MACnD,CAAC,CAAC;IACJ,CAAC;IAED;IACAd,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB,IAAMyC,WAAU,GAAI3C,MAAM,CAAC4C,UAAU;MACrC,IAAI,CAAC1E,eAAc,GAAI2E,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,KAAK,CAAC,CAACJ,WAAU,GAAI,GAAG,KAAK,IAAI,CAAC3E,eAAc,GAAI,IAAI,CAACC,aAAa,CAAC,CAAC,CAAC;;MAEjH;MACA,IAAI,CAACkE,wBAAwB,CAAC,CAAC;IACjC,CAAC;IAED;IACAtC,oBAAoB,WAApBA,oBAAoBA,CAACiB,OAAO,EAAE;MAC5B,IAAI,CAACA,OAAM,IAAK,CAACA,OAAO,CAACkC,SAAQ,IAAK,CAAClC,OAAO,CAACmC,YAAY,EAAE;;MAE7D;MACA,IAAMC,WAAU,GAAIpC,OAAO,CAACkC,SAAQ,KAAM,IAAI,CAAC5E,WAAW,CAACG,EAAC,GAC1DuC,OAAO,CAACmC,YAAW,GAAInC,OAAO,CAACkC,SAAS;;MAE1C;MACA,IAAI,CAAC,IAAI,CAACpF,YAAY,CAACsF,WAAW,CAAC,EAAE;QACnC,IAAI,CAACtF,YAAY,CAACsF,WAAW,IAAI,EAAE;MACrC;;MAEA;MACA,IAAI,CAACtF,YAAY,CAACsF,WAAW,CAAC,CAAClB,IAAI,CAAClB,OAAO,CAAC;;MAE5C;MACA,IAAIA,OAAO,CAACkC,SAAQ,KAAM,IAAI,CAAC5E,WAAW,CAACG,EAAE,EAAE;QAC7C,IAAM4E,gBAAe,GAAI,IAAI,CAACxF,WAAW,CAACqB,IAAI,CAAC,UAAA2C,IAAG;UAAA,OAChDA,IAAI,CAACC,MAAK,KAAMd,OAAO,CAACkC,SAAQ,IAAK,CAACrB,IAAI,CAACO,SAAQ;QAAA,CACrD,CAAC;QAED,IAAI,CAACiB,gBAAgB,EAAE;UACrB,IAAI,CAACtF,YAAY,CAACiD,OAAO,CAACkC,SAAS,IAAI,CAAC,IAAI,CAACnF,YAAY,CAACiD,OAAO,CAACkC,SAAS,KAAK,CAAC,IAAI,CAAC;UACtF,IAAI,CAACZ,gBAAgB,CAAC,CAAC;;UAEvB;UACA,IAAI,CAACgB,gBAAgB,CAACtC,OAAO,CAAC;QAChC;MACF;;MAEA;MACA,IAAI,CAACuC,gBAAgB,CAACH,WAAW,CAAC;IACpC,CAAC;IAED;IACAI,iBAAiB,WAAjBA,iBAAiBA,CAACxC,OAAO,EAAE;MACzB,IAAI,CAACjB,oBAAoB,CAACiB,OAAO,CAAC;IACpC,CAAC;IAED;IACAyC,qBAAqB,WAArBA,qBAAqBA,CAACzC,OAAO,EAAE;MAC7B;IAAA,CACD;IAED;IACA0C,uBAAuB,WAAvBA,uBAAuBA,CAAC1C,OAAO,EAAE;MAC/B;MACA,IAAM2C,YAAW,GAAI,IAAI,CAACC,WAAW,CAAC,wBAAwB,MAAM,MAAM;MAE1E,IAAID,YAAY,EAAE;QAChB;QACA,IAAM7E,IAAG,GAAI,IAAI,CAAC+E,WAAW,CAAC7C,OAAO,CAACkC,SAAS,CAAC;QAChD,IAAIpE,IAAI,EAAE;UACR,IAAI,CAAC4C,SAAS,CAAC5C,IAAI,CAAC;QACtB;MACF;IACF,CAAC;IAED;IACAwE,gBAAgB,WAAhBA,gBAAgBA,CAACtC,OAAO,EAAE;MAAA,IAAA8C,MAAA;MACxB;MACA,IAAMC,oBAAmB,GAAI,IAAI,CAACH,WAAW,CAAC,sBAAsB,MAAM,OAAO;MAEjF,IAAIG,oBAAmB,IAAK,cAAa,IAAK7D,MAAM,EAAE;QACpD;QACA,IAAI8D,YAAY,CAACC,UAAS,KAAM,SAAS,EAAE;UACzC;UACA,IAAMC,MAAK,GAAI,IAAI,CAACL,WAAW,CAAC7C,OAAO,CAACkC,SAAS,CAAC;UAClD,IAAMiB,UAAS,GAAID,MAAK,GAAIA,MAAM,CAACxF,QAAO,GAAIsC,OAAO,CAACoD,WAAU,IAAK,IAAI;UAEzE,IAAMC,YAAW,GAAI,IAAIL,YAAY,qCAAA5C,MAAA,CAAY+C,UAAU,GAAI;YAC7DG,IAAI,EAAEtD,OAAO,CAACuD,OAAO;YACrBC,IAAI,EAAE;UACR,CAAC,CAAC;;UAEF;UACAH,YAAY,CAACI,OAAM,GAAI,YAAM;YAC3BvE,MAAM,CAACwE,KAAK,CAAC,CAAC;YACdZ,MAAI,CAACpC,SAAS,CAACwC,MAAM,CAAC;UACxB,CAAC;QACH,OAAO,IAAIF,YAAY,CAACC,UAAS,KAAM,QAAQ,EAAE;UAC/C;UACAD,YAAY,CAACW,iBAAiB,CAAC,CAAC;QAClC;MACF;IACF,CAAC;IAED;IACAd,WAAW,WAAXA,WAAWA,CAAC/B,MAAM,EAAE;MAClB,OAAO,IAAI,CAACzE,WAAW,CAACuE,IAAI,CAAC,UAAA9C,IAAG;QAAA,OAAKA,IAAI,CAACL,EAAC,KAAMqD,MAAM;MAAA,EAAC;IAC1D,CAAC;IAED;IACA8C,YAAY,WAAZA,YAAYA,CAAC9C,MAAM,EAAE;MACnB,OAAO,IAAI,CAACzE,WAAW,CAAC6B,IAAI,CAAC,UAAAJ,IAAG;QAAA,OAAKA,IAAI,CAACL,EAAC,KAAMqD,MAAM;MAAA,EAAC;IAC1D,CAAC;IAED;IACA+C,cAAc,WAAdA,cAAcA,CAAC/C,MAAM,EAAE;MACrB,IAAMgD,QAAO,GAAI,IAAI,CAAChH,YAAY,CAACgE,MAAM,CAAC;MAC1C,IAAI,CAACgD,QAAO,IAAKA,QAAQ,CAACnE,MAAK,KAAM,CAAC,EAAE,OAAO,IAAI;MAEnD,OAAOmE,QAAQ,CAACA,QAAQ,CAACnE,MAAK,GAAI,CAAC,CAAC;IACtC,CAAC;IAED;IACAoE,iBAAiB,WAAjBA,iBAAiBA,CAAC/D,OAAO,EAAE;MACzB,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MAEvB,IAAMgE,MAAK,GAAIhE,OAAO,CAACkC,SAAQ,KAAM,IAAI,CAAC5E,WAAW,CAACG,EAAE;MACxD,IAAMwG,MAAK,GAAID,MAAK,GAAI,KAAI,GAAI,EAAE;MAClC,IAAMT,OAAM,GAAIvD,OAAO,CAACuD,OAAO,CAAC5D,MAAK,GAAI,EAAC,GACxCK,OAAO,CAACuD,OAAO,CAACW,SAAS,CAAC,CAAC,EAAE,EAAE,IAAI,KAAI,GACvClE,OAAO,CAACuD,OAAO;MAEjB,OAAOU,MAAK,GAAIV,OAAO;IACzB,CAAC;IAED;IACAvE,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI;QACF,IAAMmF,MAAK,GAAI,IAAI,CAACvB,WAAW,kBAAAxC,MAAA,CAAkB,IAAI,CAAC9C,WAAW,CAACG,EAAE,CAAE,CAAC;QACvE,IAAI0G,MAAM,EAAE;UACV,IAAI,CAACpH,YAAW,GAAIqH,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC;QACxC;MACF,EAAE,OAAOtE,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC,IAAI,CAAC9C,YAAW,GAAI,CAAC,CAAC;MACxB;IACF,CAAC;IAED;IACAuE,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI;QACF,IAAI,CAACgD,WAAW,kBAAAlE,MAAA,CAAkB,IAAI,CAAC9C,WAAW,CAACG,EAAE,GAAI,IAAI,CAACV,YAAY,CAAC;MAC7E,EAAE,OAAO8C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;IACF,CAAC;IAED;IACA0E,gBAAgB,WAAhBA,gBAAgBA,CAACzD,MAAM,EAAE;MACvB,IAAI;QACF,IAAM0D,OAAM,mBAAApE,MAAA,CAAoB,IAAI,CAAC9C,WAAW,CAACG,EAAE,OAAA2C,MAAA,CAAIU,MAAM,CAAE;QAC/D,IAAMqD,MAAK,GAAI,IAAI,CAACvB,WAAW,CAAC4B,OAAO,CAAC;QAExC,IAAIL,MAAM,EAAE;UACV,IAAMM,MAAK,GAAIL,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC;UACjC,IAAI,CAACrH,YAAY,CAACgE,MAAM,IAAI2D,MAAM,CAACX,QAAO,IAAK,EAAE;QACnD,OAAO;UACL,IAAI,CAAChH,YAAY,CAACgE,MAAM,IAAI,EAAE;QAChC;MACF,EAAE,OAAOjB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,mCAAAO,MAAA,CAAUU,MAAM,mDAAajB,KAAK,CAAC;QAChD,IAAI,CAAC/C,YAAY,CAACgE,MAAM,IAAI,EAAE;MAChC;IACF,CAAC;IAED;IACAyB,gBAAgB,WAAhBA,gBAAgBA,CAACzB,MAAM,EAAE;MACvB,IAAI;QACF,IAAMgD,QAAO,GAAI,IAAI,CAAChH,YAAY,CAACgE,MAAM,CAAC;QAC1C,IAAI,CAACgD,QAAQ,EAAE;;QAEf;QACA,IAAMY,cAAa,GAAIZ,QAAQ,CAACa,KAAK,CAAC,CAAC,GAAG,CAAC;QAE3C,IAAMH,OAAM,mBAAApE,MAAA,CAAoB,IAAI,CAAC9C,WAAW,CAACG,EAAE,OAAA2C,MAAA,CAAIU,MAAM,CAAE;QAC/D,IAAI,CAACwD,WAAW,CAACE,OAAO,EAAE;UACxBI,WAAW,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;UACrChB,QAAQ,EAAEY;QACZ,CAAC,CAAC;MACJ,EAAE,OAAO7E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,mCAAAO,MAAA,CAAUU,MAAM,mDAAajB,KAAK,CAAC;MAClD;IACF,CAAC;IAED;IACAZ,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI;QACF;QACA,IAAM8F,WAAU,GAAI,EAAE;QAEtB,KAAK,IAAIC,CAAA,GAAI,CAAC,EAAEA,CAAA,GAAI,IAAI,CAACvF,oBAAoB,CAAC,CAAC,EAAEuF,CAAC,EAAE,EAAE;UACpD,IAAM7E,GAAE,GAAI,IAAI,CAACF,iBAAiB,CAAC+E,CAAC,CAAC;UACrC,IAAI7E,GAAG,CAAC8E,UAAU,iBAAA7E,MAAA,CAAiB,IAAI,CAAC9C,WAAW,CAACG,EAAE,MAAG,CAAC,EAAE;YAC1D,IAAMqD,MAAK,GAAIoE,QAAQ,CAAC/E,GAAG,CAACgF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,CAACC,KAAK,CAACtE,MAAM,CAAC,EAAE;cAClB;cACA,IAAI,CAACyD,gBAAgB,CAACzD,MAAM,CAAC;;cAE7B;cACA,IAAMqD,MAAK,GAAI,IAAI,CAACvB,WAAW,CAACzC,GAAG,CAAC;cACpC,IAAIgE,MAAM,EAAE;gBACV,IAAI;kBACF,IAAMM,MAAK,GAAIL,IAAI,CAACC,KAAK,CAACF,MAAM,CAAC;kBACjCY,WAAW,CAAC7D,IAAI,CAAC;oBACfJ,MAAM,EAANA,MAAM;oBACN8D,WAAW,EAAEH,MAAM,CAACG,WAAU,IAAK,IAAIC,IAAI,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;kBAC7D,CAAC,CAAC;gBACJ,EAAE,OAAOO,CAAC,EAAE;kBACVvF,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEwF,CAAC,CAAC;gBAC/B;cACF;YACF;UACF;QACF;;QAEA;QACAN,WAAW,CAACO,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC;UAAA,OACpB,IAAIX,IAAI,CAACW,CAAC,CAACZ,WAAW,IAAI,IAAIC,IAAI,CAACU,CAAC,CAACX,WAAW;QAAA,CAClD,CAAC;;QAED;QACA,IAAMa,cAAa,GAAI,IAAI,CAAC7C,WAAW,CAAC,uBAAuB,MAAM,MAAM;QAC3E,IAAI6C,cAAa,IAAKV,WAAW,CAACpF,MAAK,GAAI,CAAC,EAAE;UAC5C,IAAM+F,UAAS,GAAIX,WAAW,CAAC,CAAC,CAAC;UACjC,IAAMjH,IAAG,GAAI,IAAI,CAAC+E,WAAW,CAAC6C,UAAU,CAAC5E,MAAM,CAAC;UAChD,IAAIhD,IAAI,EAAE;YACR,IAAI,CAAC4C,SAAS,CAAC5C,IAAI,CAAC;UACtB;QACF;MACF,EAAE,OAAO+B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}