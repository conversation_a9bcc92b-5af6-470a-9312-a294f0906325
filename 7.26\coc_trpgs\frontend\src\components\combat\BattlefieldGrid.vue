<template>
  <!-- 2D战场网格系统 -->
  <div class="battlefield-container">
    <!-- 战场工具栏 -->
    <div class="battlefield-toolbar">
      <div class="toolbar-left">
        <div class="zoom-controls">
          <button @click="zoomOut" class="zoom-btn" :disabled="zoom <= 0.5">
            <i class="fas fa-search-minus"></i>
          </button>
          <span class="zoom-display">{{ Math.round(zoom * 100) }}%</span>
          <button @click="zoomIn" class="zoom-btn" :disabled="zoom >= 2">
            <i class="fas fa-search-plus"></i>
          </button>
        </div>
        <div class="grid-controls">
          <button @click="toggleGrid" class="grid-btn" :class="{ active: showGrid }">
            <i class="fas fa-th"></i>
            网格
          </button>
          <button @click="toggleRuler" class="ruler-btn" :class="{ active: showRuler }">
            <i class="fas fa-ruler"></i>
            测距
          </button>
        </div>
      </div>
      <div class="toolbar-center">
        <div class="battlefield-info">
          <span class="round-info">第{{ currentRound }}轮</span>
          <span class="turn-info">{{ currentTurnCharacter?.name || '等待开始' }}的回合</span>
        </div>
      </div>
      <div class="toolbar-right">
        <div class="view-controls">
          <button @click="centerView" class="center-btn">
            <i class="fas fa-crosshairs"></i>
            居中
          </button>
          <button @click="fitToScreen" class="fit-btn">
            <i class="fas fa-expand-arrows-alt"></i>
            适应屏幕
          </button>
        </div>
      </div>
    </div>

    <!-- 战场主体 -->
    <div class="battlefield-main" ref="battlefieldContainer">
      <!-- SVG战场 -->
      <svg 
        ref="battlefieldSvg"
        class="battlefield-svg"
        :width="svgWidth"
        :height="svgHeight"
        :viewBox="viewBox"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
        @wheel="handleWheel"
      >
        <!-- 定义 -->
        <defs>
          <!-- 网格图案 -->
          <pattern id="grid" :width="gridSize * zoom" :height="gridSize * zoom" patternUnits="userSpaceOnUse">
            <path 
              :d="`M ${gridSize * zoom} 0 L 0 0 0 ${gridSize * zoom}`" 
              fill="none" 
              stroke="#e0e0e0" 
              stroke-width="1"
              opacity="0.5"
            />
          </pattern>
          <!-- 角色阴影滤镜 -->
          <filter id="character-shadow">
            <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
          </filter>
          <!-- 选中效果滤镜 -->
          <filter id="selection-glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        <!-- 背景 -->
        <rect 
          width="100%" 
          height="100%" 
          :fill="showGrid ? 'url(#grid)' : '#f8f9fa'"
        />

        <!-- 地形和障碍物 -->
        <g class="terrain-layer">
          <rect 
            v-for="obstacle in obstacles" 
            :key="obstacle.id"
            :x="obstacle.x * gridSize * zoom"
            :y="obstacle.y * gridSize * zoom"
            :width="obstacle.width * gridSize * zoom"
            :height="obstacle.height * gridSize * zoom"
            :fill="obstacle.color || '#8b4513'"
            :opacity="obstacle.opacity || 0.7"
            stroke="#654321"
            stroke-width="2"
          />
        </g>

        <!-- 测距线 -->
        <g class="ruler-layer" v-if="showRuler && rulerStart && rulerEnd">
          <line 
            :x1="rulerStart.x * zoom"
            :y1="rulerStart.y * zoom"
            :x2="rulerEnd.x * zoom"
            :y2="rulerEnd.y * zoom"
            stroke="#ff6b6b"
            stroke-width="2"
            stroke-dasharray="5,5"
          />
          <text 
            :x="(rulerStart.x + rulerEnd.x) / 2 * zoom"
            :y="(rulerStart.y + rulerEnd.y) / 2 * zoom - 10"
            text-anchor="middle"
            fill="#ff6b6b"
            font-size="14"
            font-weight="bold"
          >
            {{ getRulerDistance() }}米
          </text>
        </g>

        <!-- 移动路径预览 -->
        <g class="movement-preview" v-if="movementPreview">
          <path 
            :d="getMovementPath()"
            fill="none"
            stroke="#4ecdc4"
            stroke-width="3"
            stroke-dasharray="8,4"
            opacity="0.8"
          />
          <circle 
            :cx="movementPreview.end.x * gridSize * zoom"
            :cy="movementPreview.end.y * gridSize * zoom"
            :r="8 * zoom"
            fill="#4ecdc4"
            opacity="0.6"
          />
        </g>

        <!-- 攻击范围显示 -->
        <g class="attack-range" v-if="selectedCharacter && showAttackRange">
          <circle 
            :cx="selectedCharacter.position.x * gridSize * zoom"
            :cy="selectedCharacter.position.y * gridSize * zoom"
            :r="getAttackRange(selectedCharacter) * gridSize * zoom"
            fill="rgba(255, 107, 107, 0.2)"
            stroke="#ff6b6b"
            stroke-width="2"
            stroke-dasharray="5,5"
          />
        </g>

        <!-- 角色层 -->
        <g class="characters-layer">
          <CharacterToken
            v-for="character in characters"
            :key="character.id"
            :character="character"
            :grid-size="gridSize"
            :zoom="zoom"
            :selected="selectedCharacter?.id === character.id"
            :current-turn="currentTurnCharacter?.id === character.id"
            :can-move="canMoveCharacter(character)"
            :can-act="canActCharacter(character)"
            @select="selectCharacter"
            @move="moveCharacter"
            @action="characterAction"
            @context-menu="showCharacterMenu"
          />
        </g>

        <!-- 怪物层 -->
        <g class="monsters-layer">
          <MonsterToken
            v-for="monster in monsters"
            :key="monster.id"
            :monster="monster"
            :grid-size="gridSize"
            :zoom="zoom"
            :selected="selectedMonster?.id === monster.id"
            :can-control="isKeeper"
            @select="selectMonster"
            @move="moveMonster"
            @action="monsterAction"
            @context-menu="showMonsterMenu"
          />
        </g>

        <!-- 效果层 -->
        <g class="effects-layer">
          <!-- 法术效果区域 -->
          <circle 
            v-for="effect in areaEffects" 
            :key="effect.id"
            :cx="effect.position.x * gridSize * zoom"
            :cy="effect.position.y * gridSize * zoom"
            :r="effect.radius * gridSize * zoom"
            :fill="effect.color"
            :opacity="effect.opacity || 0.3"
            :stroke="effect.borderColor || effect.color"
            stroke-width="2"
          />
        </g>

        <!-- 动画层 -->
        <g class="animations-layer">
          <CombatAnimation
            v-for="animation in activeAnimations"
            :key="animation.id"
            :animation="animation"
            :grid-size="gridSize"
            :zoom="zoom"
            @complete="removeAnimation"
          />
        </g>
      </svg>

      <!-- 角色右键菜单 -->
      <div 
        v-if="contextMenu.show && contextMenu.type === 'character'"
        class="context-menu"
        :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      >
        <div class="menu-header">
          <img :src="contextMenu.target.avatar" :alt="contextMenu.target.name">
          <span>{{ contextMenu.target.name }}</span>
        </div>
        <div class="menu-items">
          <button @click="inspectCharacter(contextMenu.target)" class="menu-item">
            <i class="fas fa-search"></i>
            查看详情
          </button>
          <button 
            v-if="canMoveCharacter(contextMenu.target)"
            @click="startMovement(contextMenu.target)" 
            class="menu-item"
          >
            <i class="fas fa-walking"></i>
            移动
          </button>
          <button 
            v-if="canActCharacter(contextMenu.target)"
            @click="showActionMenu(contextMenu.target)" 
            class="menu-item"
          >
            <i class="fas fa-fist-raised"></i>
            行动
          </button>
          <button 
            v-if="isKeeper"
            @click="editCharacter(contextMenu.target)" 
            class="menu-item"
          >
            <i class="fas fa-edit"></i>
            编辑
          </button>
        </div>
      </div>

      <!-- 怪物右键菜单 -->
      <div 
        v-if="contextMenu.show && contextMenu.type === 'monster'"
        class="context-menu"
        :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
      >
        <div class="menu-header">
          <div class="monster-icon">
            <i :class="getMonsterIcon(contextMenu.target)"></i>
          </div>
          <span>{{ contextMenu.target.name }}</span>
        </div>
        <div class="menu-items">
          <button @click="inspectMonster(contextMenu.target)" class="menu-item">
            <i class="fas fa-search"></i>
            查看详情
          </button>
          <button 
            v-if="isKeeper"
            @click="controlMonster(contextMenu.target)" 
            class="menu-item"
          >
            <i class="fas fa-gamepad"></i>
            控制
          </button>
          <button 
            v-if="isKeeper"
            @click="editMonster(contextMenu.target)" 
            class="menu-item"
          >
            <i class="fas fa-edit"></i>
            编辑
          </button>
          <button 
            v-if="isKeeper"
            @click="removeMonster(contextMenu.target)" 
            class="menu-item danger"
          >
            <i class="fas fa-trash"></i>
            移除
          </button>
        </div>
      </div>
    </div>

    <!-- 战场状态栏 -->
    <div class="battlefield-status">
      <div class="status-left">
        <div class="selected-info" v-if="selectedCharacter || selectedMonster">
          <div class="selected-avatar">
            <img 
              v-if="selectedCharacter" 
              :src="selectedCharacter.avatar" 
              :alt="selectedCharacter.name"
            >
            <div v-else-if="selectedMonster" class="monster-avatar">
              <i :class="getMonsterIcon(selectedMonster)"></i>
            </div>
          </div>
          <div class="selected-details">
            <div class="selected-name">
              {{ (selectedCharacter || selectedMonster)?.name }}
            </div>
            <div class="selected-stats">
              <span class="hp">
                HP: {{ (selectedCharacter || selectedMonster)?.currentHP }}/{{ (selectedCharacter || selectedMonster)?.maxHP }}
              </span>
              <span class="position">
                位置: ({{ (selectedCharacter || selectedMonster)?.position.x }}, {{ (selectedCharacter || selectedMonster)?.position.y }})
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="status-center">
        <div class="battlefield-stats">
          <span class="stat-item">
            <i class="fas fa-users"></i>
            角色: {{ characters.length }}
          </span>
          <span class="stat-item">
            <i class="fas fa-dragon"></i>
            怪物: {{ monsters.length }}
          </span>
          <span class="stat-item">
            <i class="fas fa-map"></i>
            {{ battlefieldWidth }}×{{ battlefieldHeight }}
          </span>
        </div>
      </div>
      <div class="status-right">
        <div class="action-buttons">
          <button 
            v-if="isKeeper"
            @click="addMonster" 
            class="action-btn"
          >
            <i class="fas fa-plus"></i>
            添加怪物
          </button>
          <button 
            @click="resetView" 
            class="action-btn"
          >
            <i class="fas fa-home"></i>
            重置视图
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CharacterToken from './CharacterToken.vue'
import MonsterToken from './MonsterToken.vue'
import CombatAnimation from './CombatAnimation.vue'

export default {
  name: 'BattlefieldGrid',
  components: {
    CharacterToken,
    MonsterToken,
    CombatAnimation
  },
  props: {
    characters: {
      type: Array,
      default: () => []
    },
    monsters: {
      type: Array,
      default: () => []
    },
    obstacles: {
      type: Array,
      default: () => []
    },
    areaEffects: {
      type: Array,
      default: () => []
    },
    currentRound: {
      type: Number,
      default: 1
    },
    currentTurnCharacter: {
      type: Object,
      default: null
    },
    isKeeper: {
      type: Boolean,
      default: false
    },
    battlefieldWidth: {
      type: Number,
      default: 20
    },
    battlefieldHeight: {
      type: Number,
      default: 15
    },
    gridSize: {
      type: Number,
      default: 40
    }
  },
  
  data() {
    return {
      // 视图控制
      zoom: 1,
      viewBox: '0 0 800 600',
      svgWidth: 800,
      svgHeight: 600,
      // 显示控制
      showGrid: true,
      showRuler: false,
      showAttackRange: false,
      // 选择状态
      selectedCharacter: null,
      selectedMonster: null,
      // 交互状态
      isDragging: false,
      dragStart: null,
      movementPreview: null,
      // 测距工具
      rulerStart: null,
      rulerEnd: null,
      // 右键菜单
      contextMenu: {
        show: false,
        type: null,
        target: null,
        x: 0,
        y: 0
      },
      // 动画
      activeAnimations: []
    }
  },
  
  computed: {
    // 计算SVG视图框
    calculatedViewBox() {
      const width = this.battlefieldWidth * this.gridSize
      const height = this.battlefieldHeight * this.gridSize
      return `0 0 ${width} ${height}`
    }
  },
  
  mounted() {
    this.initializeBattlefield()
    this.setupEventListeners()
  },
  
  beforeUnmount() {
    this.removeEventListeners()
  },
  
  methods: {
    /**
     * 初始化战场
     */
    initializeBattlefield() {
      this.updateViewBox()
      this.centerView()
    },
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
      document.addEventListener('click', this.handleDocumentClick)
      document.addEventListener('keydown', this.handleKeyDown)
      window.addEventListener('resize', this.handleResize)
    },
    
    /**
     * 移除事件监听器
     */
    removeEventListeners() {
      document.removeEventListener('click', this.handleDocumentClick)
      document.removeEventListener('keydown', this.handleKeyDown)
      window.removeEventListener('resize', this.handleResize)
    },
    
    /**
     * 更新视图框
     */
    updateViewBox() {
      const width = this.battlefieldWidth * this.gridSize
      const height = this.battlefieldHeight * this.gridSize
      this.viewBox = `0 0 ${width} ${height}`
      this.svgWidth = width * this.zoom
      this.svgHeight = height * this.zoom
    },
    
    /**
     * 缩放控制
     */
    zoomIn() {
      this.zoom = Math.min(2, this.zoom + 0.1)
      this.updateViewBox()
    },
    
    zoomOut() {
      this.zoom = Math.max(0.5, this.zoom - 0.1)
      this.updateViewBox()
    },
    
    /**
     * 视图控制
     */
    centerView() {
      const container = this.$refs.battlefieldContainer
      if (container) {
        container.scrollLeft = (this.svgWidth - container.clientWidth) / 2
        container.scrollTop = (this.svgHeight - container.clientHeight) / 2
      }
    },
    
    fitToScreen() {
      const container = this.$refs.battlefieldContainer
      if (container) {
        const scaleX = container.clientWidth / (this.battlefieldWidth * this.gridSize)
        const scaleY = container.clientHeight / (this.battlefieldHeight * this.gridSize)
        this.zoom = Math.min(scaleX, scaleY, 2)
        this.updateViewBox()
        this.centerView()
      }
    },
    
    resetView() {
      this.zoom = 1
      this.updateViewBox()
      this.centerView()
    },
    
    /**
     * 网格和工具切换
     */
    toggleGrid() {
      this.showGrid = !this.showGrid
    },
    
    toggleRuler() {
      this.showRuler = !this.showRuler
      if (!this.showRuler) {
        this.rulerStart = null
        this.rulerEnd = null
      }
    },
    
    /**
     * 鼠标事件处理
     */
    handleMouseDown(event) {
      if (this.showRuler) {
        const rect = this.$refs.battlefieldSvg.getBoundingClientRect()
        const x = (event.clientX - rect.left) / this.zoom
        const y = (event.clientY - rect.top) / this.zoom
        
        if (!this.rulerStart) {
          this.rulerStart = { x, y }
        } else {
          this.rulerEnd = { x, y }
        }
      }
    },
    
    handleMouseMove(event) {
      if (this.showRuler && this.rulerStart && !this.rulerEnd) {
        const rect = this.$refs.battlefieldSvg.getBoundingClientRect()
        const x = (event.clientX - rect.left) / this.zoom
        const y = (event.clientY - rect.top) / this.zoom
        this.rulerEnd = { x, y }
      }
    },
    
    handleMouseUp(event) {
      // 处理鼠标释放
    },
    
    handleWheel(event) {
      event.preventDefault()
      if (event.deltaY < 0) {
        this.zoomIn()
      } else {
        this.zoomOut()
      }
    },
    
    handleDocumentClick(event) {
      if (!event.target.closest('.context-menu')) {
        this.contextMenu.show = false
      }
    },
    
    handleKeyDown(event) {
      switch (event.key) {
        case 'Escape':
          this.clearSelection()
          this.contextMenu.show = false
          break
        case 'g':
        case 'G':
          if (event.ctrlKey) {
            event.preventDefault()
            this.toggleGrid()
          }
          break
        case 'r':
        case 'R':
          if (event.ctrlKey) {
            event.preventDefault()
            this.toggleRuler()
          }
          break
      }
    },
    
    handleResize() {
      this.$nextTick(() => {
        this.updateViewBox()
      })
    },
    
    /**
     * 角色选择和操作
     */
    selectCharacter(character) {
      this.selectedCharacter = character
      this.selectedMonster = null
      this.showAttackRange = true
      this.$emit('character-selected', character)
    },
    
    selectMonster(monster) {
      this.selectedMonster = monster
      this.selectedCharacter = null
      this.showAttackRange = false
      this.$emit('monster-selected', monster)
    },
    
    clearSelection() {
      this.selectedCharacter = null
      this.selectedMonster = null
      this.showAttackRange = false
      this.movementPreview = null
    },
    
    /**
     * 移动相关
     */
    moveCharacter(character, newPosition) {
      this.$emit('character-moved', {
        character,
        oldPosition: character.position,
        newPosition
      })
    },
    
    moveMonster(monster, newPosition) {
      this.$emit('monster-moved', {
        monster,
        oldPosition: monster.position,
        newPosition
      })
    },
    
    startMovement(target) {
      this.movementPreview = {
        target,
        start: { ...target.position },
        end: { ...target.position }
      }
      this.contextMenu.show = false
    },
    
    /**
     * 行动相关
     */
    characterAction(character, action) {
      this.$emit('character-action', { character, action })
    },
    
    monsterAction(monster, action) {
      this.$emit('monster-action', { monster, action })
    },
    
    /**
     * 右键菜单
     */
    showCharacterMenu(character, event) {
      this.contextMenu = {
        show: true,
        type: 'character',
        target: character,
        x: event.clientX,
        y: event.clientY
      }
    },
    
    showMonsterMenu(monster, event) {
      this.contextMenu = {
        show: true,
        type: 'monster',
        target: monster,
        x: event.clientX,
        y: event.clientY
      }
    },
    
    /**
     * 菜单操作
     */
    inspectCharacter(character) {
      this.$emit('inspect-character', character)
      this.contextMenu.show = false
    },
    
    inspectMonster(monster) {
      this.$emit('inspect-monster', monster)
      this.contextMenu.show = false
    },
    
    editCharacter(character) {
      this.$emit('edit-character', character)
      this.contextMenu.show = false
    },
    
    editMonster(monster) {
      this.$emit('edit-monster', monster)
      this.contextMenu.show = false
    },
    
    controlMonster(monster) {
      this.$emit('control-monster', monster)
      this.contextMenu.show = false
    },
    
    removeMonster(monster) {
      this.$emit('remove-monster', monster)
      this.contextMenu.show = false
    },
    
    addMonster() {
      this.$emit('add-monster')
    },
    
    showActionMenu(character) {
      this.$emit('show-action-menu', character)
      this.contextMenu.show = false
    },
    
    /**
     * 工具函数
     */
    canMoveCharacter(character) {
      return this.currentTurnCharacter?.id === character.id && !character.hasActed
    },
    
    canActCharacter(character) {
      return this.currentTurnCharacter?.id === character.id && !character.hasActed
    },
    
    getAttackRange(character) {
      const weapon = character.equippedWeapon
      if (!weapon) return 1.5
      
      if (weapon.type === 'melee') {
        return weapon.reach || 1.5
      } else if (weapon.type === 'ranged') {
        return weapon.range?.base || 10
      }
      
      return 1.5
    },
    
    getRulerDistance() {
      if (!this.rulerStart || !this.rulerEnd) return 0
      
      const dx = this.rulerEnd.x - this.rulerStart.x
      const dy = this.rulerEnd.y - this.rulerStart.y
      const pixelDistance = Math.sqrt(dx * dx + dy * dy)
      const meterDistance = (pixelDistance / this.gridSize) * 1.5
      
      return Math.round(meterDistance * 10) / 10
    },
    
    getMovementPath() {
      if (!this.movementPreview) return ''
      
      const start = this.movementPreview.start
      const end = this.movementPreview.end
      
      return `M ${start.x * this.gridSize * this.zoom} ${start.y * this.gridSize * this.zoom} L ${end.x * this.gridSize * this.zoom} ${end.y * this.gridSize * this.zoom}`
    },
    
    getMonsterIcon(monster) {
      const iconMap = {
        'human': 'fas fa-user',
        'animal': 'fas fa-paw',
        'mythos': 'fas fa-eye',
        'undead': 'fas fa-skull',
        'demon': 'fas fa-fire',
        'construct': 'fas fa-robot'
      }
      
      return iconMap[monster.type] || 'fas fa-question'
    },
    
    /**
     * 动画管理
     */
    addAnimation(animation) {
      this.activeAnimations.push({
        ...animation,
        id: Date.now() + Math.random()
      })
    },
    
    removeAnimation(animationId) {
      const index = this.activeAnimations.findIndex(a => a.id === animationId)
      if (index !== -1) {
        this.activeAnimations.splice(index, 1)
      }
    },
    
    /**
     * 播放攻击动画
     */
    playAttackAnimation(attacker, target, result) {
      this.addAnimation({
        type: 'attack',
        from: attacker.position,
        to: target.position,
        result,
        duration: 1000
      })
    },
    
    /**
     * 播放移动动画
     */
    playMovementAnimation(character, from, to) {
      this.addAnimation({
        type: 'movement',
        character,
        from,
        to,
        duration: 800
      })
    }
  }
}
</script>

<style scoped>
.battlefield-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.battlefield-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: white;
  border-bottom: 2px solid #e74c3c;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.zoom-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.zoom-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.zoom-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-display {
  font-weight: bold;
  min-width: 50px;
  text-align: center;
}

.grid-controls {
  display: flex;
  gap: 8px;
}

.grid-btn,
.ruler-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.grid-btn:hover,
.ruler-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.grid-btn.active,
.ruler-btn.active {
  background: #e74c3c;
  border-color: #e74c3c;
}

.battlefield-info {
  display: flex;
  align-items: center;
  gap: 20px;
  font-weight: bold;
}

.round-info {
  color: #e74c3c;
  font-size: 1.1em;
}

.turn-info {
  color: #3498db;
}

.view-controls {
  display: flex;
  gap: 8px;
}

.center-btn,
.fit-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.center-btn:hover,
.fit-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.battlefield-main {
  flex: 1;
  overflow: auto;
  position: relative;
  background: #f8f9fa;
}

.battlefield-svg {
  display: block;
  cursor: crosshair;
}

.context-menu {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  min-width: 200px;
  overflow: hidden;
}

.menu-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.menu-header img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.monster-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6c757d;
  color: white;
  border-radius: 50%;
  font-size: 16px;
}

.menu-items {
  padding: 8px 0;
}

.menu-item {
  width: 100%;
  padding: 10px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: background 0.2s ease;
}

.menu-item:hover {
  background: #f8f9fa;
}

.menu-item.danger {
  color: #dc3545;
}

.menu-item.danger:hover {
  background: #f8d7da;
}

.battlefield-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #e9ecef;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selected-avatar img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.monster-avatar {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #6c757d;
  color: white;
  border-radius: 50%;
  font-size: 18px;
}

.selected-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.selected-name {
  font-weight: bold;
  color: #2c3e50;
}

.selected-stats {
  display: flex;
  gap: 16px;
  font-size: 0.9em;
  color: #6c757d;
}

.battlefield-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6c757d;
  font-size: 0.9em;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background 0.3s ease;
}

.action-btn:hover {
  background: #0056b3;
}
</style>