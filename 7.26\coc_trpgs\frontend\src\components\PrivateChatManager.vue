<template>
  <div class="private-chat-manager" :class="{ 'hidden': hidden }">
    <!-- 私聊按钮 -->
    <div class="chat-button" @click="toggleChatList" :class="{ 'has-unread': hasUnreadMessages }">
      <i class="fas fa-comments"></i>
      <span v-if="hasUnreadMessages" class="unread-badge">{{ totalUnreadCount }}</span>
    </div>
    
    <!-- 用户列表弹窗 -->
    <div v-if="showChatList" class="chat-list-popup">
      <div class="popup-header">
        <h3>私聊</h3>
        <button class="close-btn" @click="toggleChatList">×</button>
      </div>
      
      <div class="user-search">
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="搜索用户..." 
          @input="filterUsers"
        />
      </div>
      
      <div class="users-list">
        <div v-if="filteredUsers.length === 0" class="no-users">
          <p>没有找到用户</p>
        </div>
        
        <div 
          v-for="user in filteredUsers" 
          :key="user.id"
          class="user-item"
          :class="{ 'has-unread': unreadCounts[user.id] > 0 }"
          @click="startChat(user)"
        >
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="user-info">
            <div class="user-name">{{ user.username }}</div>
            <div class="last-message" v-if="getLastMessage(user.id)">
              {{ formatLastMessage(getLastMessage(user.id)) }}
            </div>
          </div>
          <div class="user-status">
            <span v-if="unreadCounts[user.id]" class="unread-count">{{ unreadCounts[user.id] }}</span>
            <span v-else-if="isUserOnline(user.id)" class="online-indicator"></span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 活动聊天窗口 -->
    <div v-for="chat in activeChats" :key="chat.userId" class="chat-window"
         :style="{ left: chat.position + 'px', zIndex: chat.active ? 10 : 5 }"
         :class="{ 'active': chat.active }">
      <private-chat
        :target-user="getUserById(chat.userId)"
        @close="closeChat(chat.userId)"
        @message-sent="handleMessageSent"
        @message-received="handleMessageReceived"
        @new-private-message="handleNewPrivateMessage"
      />
    </div>
  </div>
</template>

<script>
import PrivateChat from './PrivateChat.vue';
import websocketService from '@/services/websocket';
import { storageMixin } from '@/mixins/storageMixin';

export default {
  name: 'PrivateChatManager',
  mixins: [storageMixin],
  components: {
    PrivateChat
  },
  props: {
    onlineUsers: {
      type: Array,
      default: () => []
    },
    hidden: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showChatList: false,
      activeChats: [], // 活动的聊天窗口
      chatMessages: {}, // 所有聊天消息
      unreadCounts: {}, // 未读消息计数
      searchQuery: '',
      filteredUsers: [],
      chatWindowWidth: 320, // 聊天窗口宽度
      chatWindowGap: 20, // 聊天窗口间隙
      maxVisibleChats: 3 // 最大可见聊天窗口数
    };
  },
  computed: {
    currentUser() {
      return this.$store.getters.currentUser || { id: 0, username: '游客' };
    },
    allUsers() {
      // 过滤掉当前用户
      return this.onlineUsers.filter(user => user.id !== this.currentUser.id);
    },
    hasUnreadMessages() {
      return Object.values(this.unreadCounts).some(count => count > 0);
    },
    totalUnreadCount() {
      return Object.values(this.unreadCounts).reduce((sum, count) => sum + count, 0);
    }
  },
  watch: {
    onlineUsers: {
      handler(newUsers) {
        // 更新过滤后的用户列表
        this.filterUsers();
      },
      deep: true
    }
  },
  mounted() {
    // 初始化过滤后的用户列表
    this.filteredUsers = [...this.allUsers];
    
    // 添加私聊消息监听器
    websocketService.addCustomEventListener('private_message', this.handlePrivateMessage);
    
    // 加载未读消息计数
    this.loadUnreadCounts();
    
    // 加载最近聊天记录
    this.loadRecentChats();
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.updateMaxVisibleChats);
    this.updateMaxVisibleChats();
  },
  beforeDestroy() {
    // 移除私聊消息监听器
    websocketService.removeCustomEventListener('private_message', this.handlePrivateMessage);
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.updateMaxVisibleChats);
  },
  methods: {
    // 额外的安全存储访问方法
    safeGetStorageLength() {
      try {
        if (window.storageManager && typeof window.storageManager.length === 'function') {
          return window.storageManager.length();
        } else if (window.storageManager && typeof window.storageManager.length === 'number') {
          return window.storageManager.length;
        }
        return localStorage.length;
      } catch (error) {
        console.warn('[PrivateChatManager] 无法获取存储长度:', error.message);
        return 0;
      }
    },
    
    safeGetStorageKey(index) {
      try {
        if (window.storageManager && window.storageManager.key) {
          return window.storageManager.key(index);
        }
        return localStorage.key(index);
      } catch (error) {
        console.warn(`[PrivateChatManager] 无法获取存储键 ${index}:`, error.message);
        return null;
      }
    },

    // 切换聊天列表显示状态
    toggleChatList() {
      this.showChatList = !this.showChatList;
      if (this.showChatList) {
        this.filterUsers();
      }
    },
    
    // 根据搜索查询过滤用户
    filterUsers() {
      if (!this.searchQuery.trim()) {
        this.filteredUsers = [...this.allUsers];
        return;
      }
      
      const query = this.searchQuery.toLowerCase();
      this.filteredUsers = this.allUsers.filter(user => 
        user.username.toLowerCase().includes(query)
      );
    },
    
    // 开始与用户聊天
    startChat(user) {
      if (!user || !user.id) return;
      
      // 检查是否已经存在该聊天
      const existingChat = this.activeChats.find(chat => chat.userId === user.id);
      
      if (existingChat) {
        // 如果已存在，激活该聊天
        this.activateChat(user.id);
      } else {
        // 如果不存在，创建新聊天
        const position = this.calculateChatPosition(this.activeChats.length);
        
        this.activeChats.push({
          userId: user.id,
          position,
          active: true,
          minimized: false
        });
        
        // 重新计算所有聊天窗口的位置
        this.recalculateChatPositions();
      }
      
      // 重置该用户的未读消息计数
      this.unreadCounts[user.id] = 0;
      this.saveUnreadCounts();
      
      // 关闭用户列表
      this.showChatList = false;
    },
    
    // 关闭聊天
    closeChat(userId) {
      const index = this.activeChats.findIndex(chat => chat.userId === userId);
      if (index !== -1) {
        this.activeChats.splice(index, 1);
        
        // 重新计算所有聊天窗口的位置
        this.recalculateChatPositions();
      }
    },
    
    // 激活聊天
    activateChat(userId) {
      // 将所有聊天设为非活动状态
      this.activeChats.forEach(chat => {
        chat.active = chat.userId === userId;
      });
      
      // 重置该用户的未读消息计数
      this.unreadCounts[userId] = 0;
      this.saveUnreadCounts();
    },
    
    // 计算聊天窗口位置
    calculateChatPosition(index) {
      const visibleIndex = index % this.maxVisibleChats;
      return visibleIndex * (this.chatWindowWidth + this.chatWindowGap);
    },
    
    // 重新计算所有聊天窗口的位置
    recalculateChatPositions() {
      this.activeChats.forEach((chat, index) => {
        chat.position = this.calculateChatPosition(index);
      });
    },
    
    // 更新最大可见聊天窗口数
    updateMaxVisibleChats() {
      const windowWidth = window.innerWidth;
      this.maxVisibleChats = Math.max(1, Math.floor((windowWidth - 100) / (this.chatWindowWidth + this.chatWindowGap)));
      
      // 重新计算所有聊天窗口的位置
      this.recalculateChatPositions();
    },
    
    // 处理私聊消息
    handlePrivateMessage(message) {
      if (!message || !message.sender_id || !message.recipient_id) return;
      
      // 确定对话的另一方
      const otherUserId = message.sender_id === this.currentUser.id ? 
        message.recipient_id : message.sender_id;
      
      // 初始化该用户的消息数组（如果不存在）
      if (!this.chatMessages[otherUserId]) {
        this.chatMessages[otherUserId] = [];
      }
      
      // 添加消息到数组
      this.chatMessages[otherUserId].push(message);
      
      // 如果是接收到的消息，且没有打开与该用户的聊天，增加未读计数
      if (message.sender_id !== this.currentUser.id) {
        const isActiveChatOpen = this.activeChats.some(chat => 
          chat.userId === message.sender_id && !chat.minimized
        );
        
        if (!isActiveChatOpen) {
          this.unreadCounts[message.sender_id] = (this.unreadCounts[message.sender_id] || 0) + 1;
          this.saveUnreadCounts();
          
          // 发送桌面通知
          this.sendNotification(message);
        }
      }
      
      // 保存消息到本地存储
      this.saveChatMessages(otherUserId);
    },
    
    // 处理发送的消息
    handleMessageSent(message) {
      this.handlePrivateMessage(message);
    },
    
    // 处理接收的消息
    handleMessageReceived(message) {
      // 消息已经在handlePrivateMessage中处理
    },
    
    // 处理新的私聊消息（不在当前活动聊天中）
    handleNewPrivateMessage(message) {
      // 检查是否需要自动打开聊天窗口
      const autoOpenChat = this.safeGetItem('auto_open_private_chat') === 'true';
      
      if (autoOpenChat) {
        // 自动打开与发送者的聊天窗口
        const user = this.getUserById(message.sender_id);
        if (user) {
          this.startChat(user);
        }
      }
    },
    
    // 发送桌面通知
    sendNotification(message) {
      // 检查是否启用了通知
      const notificationsEnabled = this.safeGetItem('enable_notifications') !== 'false';
      
      if (notificationsEnabled && "Notification" in window) {
        // 检查权限
        if (Notification.permission === "granted") {
          // 创建通知
          const sender = this.getUserById(message.sender_id);
          const senderName = sender ? sender.username : message.sender_name || '用户';
          
          const notification = new Notification(`新私聊消息 - ${senderName}`, {
            body: message.content,
            icon: '/favicon.ico'
          });
          
          // 点击通知时打开聊天
          notification.onclick = () => {
            window.focus();
            this.startChat(sender);
          };
        } else if (Notification.permission !== "denied") {
          // 请求权限
          Notification.requestPermission();
        }
      }
    },
    
    // 获取用户通过ID
    getUserById(userId) {
      return this.onlineUsers.find(user => user.id === userId);
    },
    
    // 检查用户是否在线
    isUserOnline(userId) {
      return this.onlineUsers.some(user => user.id === userId);
    },
    
    // 获取与用户的最后一条消息
    getLastMessage(userId) {
      const messages = this.chatMessages[userId];
      if (!messages || messages.length === 0) return null;
      
      return messages[messages.length - 1];
    },
    
    // 格式化最后一条消息
    formatLastMessage(message) {
      if (!message) return '';
      
      const isSelf = message.sender_id === this.currentUser.id;
      const prefix = isSelf ? '我: ' : '';
      const content = message.content.length > 15 ? 
        message.content.substring(0, 15) + '...' : 
        message.content;
      
      return prefix + content;
    },
    
    // 加载未读消息计数
    loadUnreadCounts() {
      try {
        const stored = this.safeGetItem(`unread_counts_${this.currentUser.id}`);
        if (stored) {
          this.unreadCounts = JSON.parse(stored);
        }
      } catch (error) {
        console.error('加载未读消息计数失败:', error);
        this.unreadCounts = {};
      }
    },
    
    // 保存未读消息计数
    saveUnreadCounts() {
      try {
        this.safeSetJSON(`unread_counts_${this.currentUser.id}`, this.unreadCounts);
      } catch (error) {
        console.error('保存未读消息计数失败:', error);
      }
    },
    
    // 加载聊天消息
    loadChatMessages(userId) {
      try {
        const chatKey = `private_chat_${this.currentUser.id}_${userId}`;
        const stored = this.safeGetItem(chatKey);
        
        if (stored) {
          const parsed = JSON.parse(stored);
          this.chatMessages[userId] = parsed.messages || [];
        } else {
          this.chatMessages[userId] = [];
        }
      } catch (error) {
        console.error(`加载与用户 ${userId} 的聊天消息失败:`, error);
        this.chatMessages[userId] = [];
      }
    },
    
    // 保存聊天消息
    saveChatMessages(userId) {
      try {
        const messages = this.chatMessages[userId];
        if (!messages) return;
        
        // 最多保存100条消息
        const messagesToSave = messages.slice(-100);
        
        const chatKey = `private_chat_${this.currentUser.id}_${userId}`;
        this.safeSetJSON(chatKey, {
          lastUpdated: new Date().toISOString(),
          messages: messagesToSave
        });
      } catch (error) {
        console.error(`保存与用户 ${userId} 的聊天消息失败:`, error);
      }
    },
    
    // 加载最近聊天
    loadRecentChats() {
      try {
        // 获取所有本地存储的聊天记录
        const recentChats = [];
        
        for (let i = 0; i < this.safeGetStorageLength(); i++) {
          const key = this.safeGetStorageKey(i);
          if (key.startsWith(`private_chat_${this.currentUser.id}_`)) {
            const userId = parseInt(key.split('_')[3]);
            if (!isNaN(userId)) {
              // 加载聊天消息
              this.loadChatMessages(userId);
              
              // 获取最后更新时间
              const stored = this.safeGetItem(key);
              if (stored) {
                try {
                  const parsed = JSON.parse(stored);
                  recentChats.push({
                    userId,
                    lastUpdated: parsed.lastUpdated || new Date(0).toISOString()
                  });
                } catch (e) {
                  console.error('解析聊天记录失败:', e);
                }
              }
            }
          }
        }
        
        // 按最后更新时间排序
        recentChats.sort((a, b) => 
          new Date(b.lastUpdated) - new Date(a.lastUpdated)
        );
        
        // 自动打开最近的聊天（可选）
        const autoOpenRecent = this.safeGetItem('auto_open_recent_chat') === 'true';
        if (autoOpenRecent && recentChats.length > 0) {
          const mostRecent = recentChats[0];
          const user = this.getUserById(mostRecent.userId);
          if (user) {
            this.startChat(user);
          }
        }
      } catch (error) {
        console.error('加载最近聊天失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.private-chat-manager {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.private-chat-manager.hidden {
  transform: translateX(-100px);
  opacity: 0;
  pointer-events: none;
}

.chat-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  position: relative;
  transition: all 0.3s;
}

.chat-button:hover {
  background-color: #2980b9;
  transform: scale(1.05);
}

.chat-button.has-unread {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
  }
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  border: 2px solid #2a2a2a;
}

.chat-list-popup {
  position: absolute;
  bottom: 60px;
  left: 0;
  width: 280px;
  max-height: 400px;
  background-color: #2a2a2a;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1001;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #333;
  border-bottom: 1px solid #444;
}

.popup-header h3 {
  margin: 0;
  color: #e0e0e0;
  font-size: 1rem;
}

.close-btn {
  background: none;
  border: none;
  color: #b0b0b0;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 5px;
}

.close-btn:hover {
  color: #e74c3c;
}

.user-search {
  padding: 10px;
  border-bottom: 1px solid #444;
}

.user-search input {
  width: 100%;
  padding: 8px 10px;
  border-radius: 4px;
  border: 1px solid #555;
  background-color: #333;
  color: #e0e0e0;
  font-size: 0.9rem;
}

.user-search input:focus {
  outline: none;
  border-color: #3498db;
}

.users-list {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.no-users {
  padding: 20px;
  text-align: center;
  color: #888;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #444;
  transition: background-color 0.2s;
}

.user-item:hover {
  background-color: #333;
}

.user-item.has-unread {
  background-color: rgba(52, 152, 219, 0.1);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #444;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #e0e0e0;
}

.user-info {
  flex: 1;
  overflow: hidden;
}

.user-name {
  color: #e0e0e0;
  font-weight: 500;
  margin-bottom: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message {
  color: #b0b0b0;
  font-size: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-status {
  margin-left: 10px;
}

.online-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #2ecc71;
  display: inline-block;
}

.unread-count {
  background-color: #e74c3c;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.7rem;
  font-weight: bold;
}

.chat-window {
  position: fixed;
  bottom: 20px;
  width: 320px;
  height: 400px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  transition: box-shadow 0.3s;
}

.chat-window.active {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* 移动设备适配 */
@media (max-width: 768px) {
  .chat-window {
    width: 280px;
    height: 350px;
  }
  
  .chat-list-popup {
    width: 250px;
  }
}
</style> 