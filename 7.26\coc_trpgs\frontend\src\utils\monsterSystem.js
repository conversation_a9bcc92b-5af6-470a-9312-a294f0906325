/**
 * COC 7版怪物和NPC管理系统
 * 包含怪物数据库、AI逻辑和特殊能力处理
 */

import { diceRoller } from './diceRoller.js'
import CombatRules from './combatRules.js'

export class MonsterSystem {
  /**
   * 怪物数据库
   */
  static monsterDatabase = {
    // ===== 人类NPC =====
    humans: {
      civilian: {
        id: 'civilian',
        name: '平民',
        type: 'human',
        category: 'npc',
        
        // 基础属性
        attributes: {
          strength: 50,
          constitution: 50,
          size: 60,
          dexterity: 50,
          intelligence: 50,
          power: 50,
          appearance: 50,
          education: 50
        },
        
        // 衍生属性
        hitPoints: 11,
        magicPoints: 10,
        sanity: 50,
        luck: 50,
        
        // 技能
        skills: {
          dodge: 25,
          fighting_brawl: 25,
          listen: 25,
          spot_hidden: 25,
          psychology: 10
        },
        
        // 战斗数据
        damageBonus: '0',
        build: 0,
        movementRate: 8,
        
        // 攻击方式
        attacks: [
          {
            name: '拳头',
            skill: 'fighting_brawl',
            damage: '1d3',
            range: 1,
            attacks: 1
          }
        ],
        
        // 护甲
        armor: 0,
        
        // AI行为
        aiType: 'passive',
        morale: 30,
        
        description: '普通的平民，没有战斗经验'
      },
      
      police_officer: {
        id: 'police_officer',
        name: '警察',
        type: 'human',
        category: 'npc',
        
        attributes: {
          strength: 65,
          constitution: 60,
          size: 65,
          dexterity: 60,
          intelligence: 55,
          power: 50,
          appearance: 50,
          education: 60
        },
        
        hitPoints: 13,
        magicPoints: 10,
        sanity: 50,
        luck: 50,
        
        skills: {
          dodge: 35,
          fighting_brawl: 55,
          firearms_handgun: 60,
          listen: 40,
          spot_hidden: 50,
          psychology: 35,
          law: 40
        },
        
        damageBonus: '+1d4',
        build: 1,
        movementRate: 8,
        
        attacks: [
          {
            name: '拳头',
            skill: 'fighting_brawl',
            damage: '1d3+1d4',
            range: 1,
            attacks: 1
          },
          {
            name: '警棍',
            skill: 'fighting_brawl',
            damage: '1d6+1d4',
            range: 1,
            attacks: 1
          },
          {
            name: '.38左轮手枪',
            skill: 'firearms_handgun',
            damage: '1d10',
            range: 15,
            attacks: 1,
            ammo: 6,
            malfunction: 100
          }
        ],
        
        armor: 1, // 制服提供轻微保护
        
        aiType: 'defensive',
        morale: 60,
        
        description: '训练有素的警察，具备基本战斗技能'
      },
      
      gangster: {
        id: 'gangster',
        name: '歹徒',
        type: 'human',
        category: 'npc',
        
        attributes: {
          strength: 60,
          constitution: 55,
          size: 60,
          dexterity: 65,
          intelligence: 50,
          power: 45,
          appearance: 45,
          education: 40
        },
        
        hitPoints: 12,
        magicPoints: 9,
        sanity: 45,
        luck: 45,
        
        skills: {
          dodge: 40,
          fighting_brawl: 60,
          firearms_handgun: 55,
          stealth: 50,
          spot_hidden: 45,
          intimidate: 55
        },
        
        damageBonus: '+1d4',
        build: 1,
        movementRate: 8,
        
        attacks: [
          {
            name: '拳头',
            skill: 'fighting_brawl',
            damage: '1d3+1d4',
            range: 1,
            attacks: 1
          },
          {
            name: '小刀',
            skill: 'fighting_brawl',
            damage: '1d4+1d4',
            range: 1,
            attacks: 1,
            special: ['impaling']
          },
          {
            name: '.45自动手枪',
            skill: 'firearms_handgun',
            damage: '1d10+2',
            range: 15,
            attacks: 1,
            ammo: 7,
            malfunction: 100
          }
        ],
        
        armor: 0,
        
        aiType: 'aggressive',
        morale: 50,
        
        description: '危险的罪犯，不择手段'
      }
    },
    
    // ===== 动物 =====
    animals: {
      dog: {
        id: 'dog',
        name: '狗',
        type: 'animal',
        category: 'natural',
        
        attributes: {
          strength: 40,
          constitution: 55,
          size: 30,
          dexterity: 70,
          intelligence: 25,
          power: 40
        },
        
        hitPoints: 9,
        magicPoints: 8,
        sanity: 0, // 动物没有理智值
        
        skills: {
          dodge: 55,
          listen: 80,
          scent: 90,
          track: 80
        },
        
        damageBonus: '-1d4',
        build: -1,
        movementRate: 12,
        
        attacks: [
          {
            name: '撕咬',
            skill: 'bite',
            skillValue: 55,
            damage: '1d6-1d4',
            range: 1,
            attacks: 1,
            special: ['worry'] // 撕咬后可以摇摆造成额外伤害
          }
        ],
        
        armor: 0,
        
        aiType: 'territorial',
        morale: 40,
        
        specialAbilities: [
          {
            name: '敏锐嗅觉',
            description: '在追踪和发现隐藏目标时获得优势'
          }
        ],
        
        description: '普通的狗，可能是看门狗或野狗'
      },
      
      wolf: {
        id: 'wolf',
        name: '狼',
        type: 'animal',
        category: 'natural',
        
        attributes: {
          strength: 55,
          constitution: 60,
          size: 50,
          dexterity: 65,
          intelligence: 35,
          power: 50
        },
        
        hitPoints: 11,
        magicPoints: 10,
        sanity: 0,
        
        skills: {
          dodge: 50,
          listen: 70,
          scent: 85,
          track: 85,
          stealth: 60
        },
        
        damageBonus: '+1d4',
        build: 1,
        movementRate: 10,
        
        attacks: [
          {
            name: '撕咬',
            skill: 'bite',
            skillValue: 65,
            damage: '1d8+1d4',
            range: 1,
            attacks: 1,
            special: ['worry', 'knockdown']
          }
        ],
        
        armor: 1, // 厚毛皮
        
        aiType: 'pack_hunter',
        morale: 60,
        
        specialAbilities: [
          {
            name: '群体狩猎',
            description: '与其他狼协作时攻击获得加值'
          },
          {
            name: '夜视',
            description: '在黑暗中不受惩罚'
          }
        ],
        
        description: '野生的狼，聪明而危险的掠食者'
      }
    },
    
    // ===== 神话生物 =====
    mythos: {
      deep_one: {
        id: 'deep_one',
        name: '深潜者',
        type: 'mythos',
        category: 'aquatic',
        
        attributes: {
          strength: 80,
          constitution: 80,
          size: 70,
          dexterity: 60,
          intelligence: 60,
          power: 70
        },
        
        hitPoints: 15,
        magicPoints: 14,
        sanity: 0, // 神话生物没有理智值
        
        skills: {
          dodge: 35,
          fighting_brawl: 70,
          swim: 95,
          stealth: 40,
          cthulhu_mythos: 25
        },
        
        damageBonus: '+1d6',
        build: 2,
        movementRate: 6, // 陆地移动较慢
        swimRate: 12, // 水中移动很快
        
        attacks: [
          {
            name: '爪击',
            skill: 'fighting_brawl',
            damage: '1d6+1d6',
            range: 1,
            attacks: 2 // 双爪攻击
          },
          {
            name: '撕咬',
            skill: 'fighting_brawl',
            damage: '1d4+1d6',
            range: 1,
            attacks: 1,
            special: ['worry']
          }
        ],
        
        armor: 2, // 鳞片护甲
        
        aiType: 'mythos_aggressive',
        morale: 80, // 神话生物很少逃跑
        
        specialAbilities: [
          {
            name: '水陆两栖',
            description: '可以在水中和陆地上活动'
          },
          {
            name: '再生',
            description: '每轮恢复1点生命值'
          },
          {
            name: '恐怖形象',
            description: '初次见到需要进行理智检定(1/1d4+1)'
          }
        ],
        
        spells: ['召唤深潜者', '控制天气'],
        
        sanityLoss: {
          first: '1/1d4+1',
          subsequent: '0/1d4'
        },
        
        description: '来自海洋深处的可怖生物，侍奉克苏鲁'
      },
      
      shoggoth: {
        id: 'shoggoth',
        name: '修格斯',
        type: 'mythos',
        category: 'servitor',
        
        attributes: {
          strength: 100,
          constitution: 100,
          size: 200, // 巨大体型
          dexterity: 40,
          intelligence: 40,
          power: 80
        },
        
        hitPoints: 30,
        magicPoints: 16,
        sanity: 0,
        
        skills: {
          dodge: 20,
          fighting_brawl: 80,
          listen: 60
        },
        
        damageBonus: '+3d6',
        build: 6,
        movementRate: 8,
        
        attacks: [
          {
            name: '触手鞭打',
            skill: 'fighting_brawl',
            damage: '2d6+3d6',
            range: 3, // 长触手
            attacks: 3 // 多重攻击
          },
          {
            name: '吞噬',
            skill: 'fighting_brawl',
            damage: '自动',
            range: 1,
            attacks: 1,
            special: ['engulf'] // 吞噬目标
          }
        ],
        
        armor: 2,
        
        aiType: 'mythos_berserker',
        morale: 95,
        
        specialAbilities: [
          {
            name: '变形',
            description: '可以改变形状和大小'
          },
          {
            name: '免疫物理伤害',
            description: '普通武器只造成最小伤害'
          },
          {
            name: '吞噬',
            description: '可以吞噬较小的目标'
          },
          {
            name: '恐怖形象',
            description: '初次见到需要进行理智检定(1d10/1d20)'
          }
        ],
        
        resistances: {
          physical: 0.5, // 物理伤害减半
          fire: 2.0, // 火焰伤害加倍
          electrical: 2.0 // 电击伤害加倍
        },
        
        sanityLoss: {
          first: '1d10/1d20',
          subsequent: '1d4/1d10'
        },
        
        description: '古老者创造的可怖仆从，无定形的恐怖存在'
      }
    },
    
    // ===== 不死生物 =====
    undead: {
      zombie: {
        id: 'zombie',
        name: '僵尸',
        type: 'undead',
        category: 'reanimated',
        
        attributes: {
          strength: 70,
          constitution: 80,
          size: 65,
          dexterity: 30,
          intelligence: 10,
          power: 20
        },
        
        hitPoints: 14,
        magicPoints: 4,
        sanity: 0,
        
        skills: {
          dodge: 15,
          fighting_brawl: 50,
          listen: 20
        },
        
        damageBonus: '+1d4',
        build: 1,
        movementRate: 5, // 移动缓慢
        
        attacks: [
          {
            name: '撕咬',
            skill: 'fighting_brawl',
            damage: '1d6+1d4',
            range: 1,
            attacks: 1,
            special: ['infection'] // 可能传播疾病
          },
          {
            name: '爪击',
            skill: 'fighting_brawl',
            damage: '1d4+1d4',
            range: 1,
            attacks: 1
          }
        ],
        
        armor: 1, // 腐烂的皮肤提供轻微保护
        
        aiType: 'mindless_aggressive',
        morale: 100, // 不会逃跑
        
        specialAbilities: [
          {
            name: '不死',
            description: '免疫毒素、疾病和精神影响'
          },
          {
            name: '感知生命',
            description: '能够感知附近的活人'
          },
          {
            name: '恐怖形象',
            description: '初次见到需要进行理智检定(0/1d4)'
          }
        ],
        
        resistances: {
          poison: 0, // 免疫毒素
          disease: 0, // 免疫疾病
          mental: 0 // 免疫精神影响
        },
        
        weaknesses: {
          fire: 2.0, // 火焰伤害加倍
          headshot: 3.0 // 头部攻击伤害三倍
        },
        
        sanityLoss: {
          first: '0/1d4',
          subsequent: '0/1'
        },
        
        description: '死而复生的尸体，被邪恶力量驱动'
      }
    }
  }

  /**
   * AI行为类型定义
   */
  static aiTypes = {
    passive: {
      name: '被动',
      description: '不会主动攻击，只在受到威胁时反击',
      aggressiveness: 0.1,
      fleeThreshold: 0.8
    },
    defensive: {
      name: '防御',
      description: '保护自己和盟友，适度反击',
      aggressiveness: 0.3,
      fleeThreshold: 0.6
    },
    aggressive: {
      name: '攻击性',
      description: '主动寻找并攻击敌人',
      aggressiveness: 0.7,
      fleeThreshold: 0.3
    },
    territorial: {
      name: '领域性',
      description: '保护自己的领域，驱逐入侵者',
      aggressiveness: 0.5,
      fleeThreshold: 0.4
    },
    pack_hunter: {
      name: '群体狩猎',
      description: '与同类协作狩猎',
      aggressiveness: 0.6,
      fleeThreshold: 0.2
    },
    mythos_aggressive: {
      name: '神话攻击性',
      description: '神话生物的攻击性行为',
      aggressiveness: 0.8,
      fleeThreshold: 0.1
    },
    mythos_berserker: {
      name: '神话狂暴',
      description: '极度攻击性，几乎不会逃跑',
      aggressiveness: 0.9,
      fleeThreshold: 0.05
    },
    mindless_aggressive: {
      name: '无脑攻击',
      description: '没有智慧，只知道攻击',
      aggressiveness: 1.0,
      fleeThreshold: 0
    }
  }

  /**
   * 创建怪物实例
   * @param {string} monsterId 怪物ID
   * @param {Object} customizations 自定义属性
   * @returns {Object} 怪物实例
   */
  static createMonster(monsterId, customizations = {}) {
    const template = this.getMonsterTemplate(monsterId)
    if (!template) return null

    const monster = {
      ...template,
      instanceId: Date.now() + Math.random(),
      currentHP: template.hitPoints,
      currentMP: template.magicPoints,
      currentSAN: template.sanity || 0,
      conditions: [],
      position: { x: 0, y: 0 },
      facing: 0,
      actionPoints: 1,
      hasActed: false,
      ...customizations
    }

    // 初始化攻击方式
    if (monster.attacks) {
      monster.attacks = monster.attacks.map(attack => ({
        ...attack,
        currentAmmo: attack.ammo || null,
        malfunction: attack.malfunction || null
      }))
    }

    return monster
  }

  /**
   * 获取怪物模板
   * @param {string} monsterId 怪物ID
   * @returns {Object|null} 怪物模板
   */
  static getMonsterTemplate(monsterId) {
    for (const category of Object.values(this.monsterDatabase)) {
      if (category[monsterId]) {
        return { ...category[monsterId] }
      }
    }
    return null
  }

  /**
   * 获取分类下的所有怪物
   * @param {string} category 分类名
   * @returns {Array} 怪物列表
   */
  static getMonstersByCategory(category) {
    const categoryData = this.monsterDatabase[category]
    if (!categoryData) return []
    
    return Object.values(categoryData)
  }

  /**
   * AI决策系统
   * @param {Object} monster 怪物实例
   * @param {Array} targets 可能的目标
   * @param {Object} battlefield 战场状态
   * @returns {Object} AI决策结果
   */
  static makeAIDecision(monster, targets, battlefield) {
    const aiType = this.aiTypes[monster.aiType] || this.aiTypes.aggressive
    
    // 检查士气
    const healthPercentage = monster.currentHP / monster.hitPoints
    if (healthPercentage < aiType.fleeThreshold && aiType.fleeThreshold > 0) {
      return {
        action: 'flee',
        priority: 1.0,
        description: `${monster.name}试图逃跑`
      }
    }

    // 寻找最佳目标
    const bestTarget = this.selectBestTarget(monster, targets, aiType)
    if (!bestTarget) {
      return {
        action: 'wait',
        priority: 0.1,
        description: `${monster.name}没有发现目标`
      }
    }

    // 计算到目标的距离
    const distance = this.calculateDistance(monster.position, bestTarget.position)
    
    // 选择最佳攻击方式
    const bestAttack = this.selectBestAttack(monster, bestTarget, distance)
    
    if (bestAttack) {
      return {
        action: 'attack',
        target: bestTarget,
        attack: bestAttack,
        priority: aiType.aggressiveness,
        description: `${monster.name}使用${bestAttack.name}攻击${bestTarget.name}`
      }
    }

    // 如果无法攻击，尝试移动到更好的位置
    if (distance > bestAttack?.range || 1) {
      return {
        action: 'move',
        target: bestTarget,
        priority: 0.6,
        description: `${monster.name}向${bestTarget.name}移动`
      }
    }

    return {
      action: 'wait',
      priority: 0.1,
      description: `${monster.name}观察情况`
    }
  }

  /**
   * 选择最佳目标
   * @param {Object} monster 怪物
   * @param {Array} targets 目标列表
   * @param {Object} aiType AI类型
   * @returns {Object|null} 最佳目标
   */
  static selectBestTarget(monster, targets, aiType) {
    if (!targets || targets.length === 0) return null

    // 过滤有效目标
    const validTargets = targets.filter(target => {
      return target.currentHP > 0 && 
             target.type !== monster.type && 
             !target.conditions?.includes('hidden')
    })

    if (validTargets.length === 0) return null

    // 根据AI类型选择目标
    let bestTarget = null
    let bestScore = -1

    validTargets.forEach(target => {
      let score = 0
      
      // 距离因素
      const distance = this.calculateDistance(monster.position, target.position)
      score += Math.max(0, 100 - distance * 5) // 距离越近分数越高
      
      // 威胁等级
      const threatLevel = this.calculateThreatLevel(target)
      if (aiType.name === 'defensive') {
        score += threatLevel * 20 // 防御型优先攻击威胁大的目标
      } else {
        score += (100 - threatLevel) * 10 // 攻击型优先攻击弱的目标
      }
      
      // 生命值因素
      const healthPercentage = target.currentHP / (target.hitPoints || target.hp || 1)
      score += (100 - healthPercentage * 100) * 5 // 优先攻击受伤的目标
      
      // 随机因素
      score += Math.random() * 20
      
      if (score > bestScore) {
        bestScore = score
        bestTarget = target
      }
    })

    return bestTarget
  }

  /**
   * 选择最佳攻击方式
   * @param {Object} monster 怪物
   * @param {Object} target 目标
   * @param {number} distance 距离
   * @returns {Object|null} 最佳攻击
   */
  static selectBestAttack(monster, target, distance) {
    if (!monster.attacks) return null

    const availableAttacks = monster.attacks.filter(attack => {
      // 检查射程
      if (distance > (attack.range || 1)) return false
      
      // 检查弹药
      if (attack.ammo && (attack.currentAmmo || 0) <= 0) return false
      
      return true
    })

    if (availableAttacks.length === 0) return null

    // 选择伤害最高的攻击
    return availableAttacks.reduce((best, current) => {
      const currentDamage = this.estimateAverageDamage(current.damage)
      const bestDamage = this.estimateAverageDamage(best.damage)
      return currentDamage > bestDamage ? current : best
    })
  }

  /**
   * 计算威胁等级
   * @param {Object} target 目标
   * @returns {number} 威胁等级 (0-100)
   */
  static calculateThreatLevel(target) {
    let threat = 0
    
    // 基于属性的威胁
    threat += (target.strength || 50) * 0.3
    threat += (target.dexterity || 50) * 0.2
    threat += (target.constitution || 50) * 0.2
    
    // 基于技能的威胁
    const combatSkills = ['fighting_brawl', 'firearms_handgun', 'firearms_rifle']
    combatSkills.forEach(skill => {
      threat += (target[skill] || target.skills?.[skill] || 0) * 0.1
    })
    
    // 基于装备的威胁
    if (target.weapons) {
      threat += target.weapons.length * 5
    }
    
    return Math.min(100, threat)
  }

  /**
   * 估算平均伤害
   * @param {string} damageFormula 伤害公式
   * @returns {number} 平均伤害
   */
  static estimateAverageDamage(damageFormula) {
    if (!damageFormula) return 0
    
    // 简化的伤害估算
    const diceMatch = damageFormula.match(/(\d+)d(\d+)/g)
    let total = 0
    
    if (diceMatch) {
      diceMatch.forEach(dice => {
        const [, count, sides] = dice.match(/(\d+)d(\d+)/)
        total += parseInt(count) * (parseInt(sides) + 1) / 2
      })
    }
    
    // 处理修正值
    const modifierMatch = damageFormula.match(/[+-]\d+/)
    if (modifierMatch) {
      total += parseInt(modifierMatch[0])
    }
    
    return total
  }

  /**
   * 计算距离
   * @param {Object} pos1 位置1
   * @param {Object} pos2 位置2
   * @returns {number} 距离
   */
  static calculateDistance(pos1, pos2) {
    const dx = pos1.x - pos2.x
    const dy = pos1.y - pos2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  /**
   * 执行怪物行动
   * @param {Object} monster 怪物实例
   * @param {Object} decision AI决策
   * @param {Array} targets 目标列表
   * @returns {Object} 行动结果
   */
  static executeMonsterAction(monster, decision, targets) {
    switch (decision.action) {
      case 'attack':
        return this.executeAttack(monster, decision.target, decision.attack)
      
      case 'move':
        return this.executeMove(monster, decision.target)
      
      case 'flee':
        return this.executeFlee(monster, targets)
      
      case 'wait':
        return this.executeWait(monster)
      
      default:
        return {
          success: false,
          description: `${monster.name}不知道该做什么`
        }
    }
  }

  /**
   * 执行攻击
   * @param {Object} monster 攻击者
   * @param {Object} target 目标
   * @param {Object} attack 攻击方式
   * @returns {Object} 攻击结果
   */
  static executeAttack(monster, target, attack) {
    // 使用战斗规则系统执行攻击
    const skillValue = attack.skillValue || monster.skills?.[attack.skill] || 50
    const roll = diceRoller.rollD100()
    const successLevel = CombatRules.getSuccessLevel(roll, skillValue)
    
    if (successLevel === 'failure' || successLevel === 'fumble') {
      return {
        success: false,
        type: 'attack',
        attacker: monster.name,
        target: target.name,
        attack: attack.name,
        roll,
        skillValue,
        successLevel,
        description: `${monster.name}的${attack.name}攻击失败`
      }
    }

    // 计算伤害
    const damageRoll = diceRoller.roll(attack.damage)
    let damage = damageRoll.total
    
    // 应用成功等级修正
    if (successLevel === 'extreme' || successLevel === 'critical') {
      damage = Math.ceil(damage * 1.5)
    }
    
    // 应用护甲减伤
    damage = Math.max(1, damage - (target.armor || 0))
    
    // 应用伤害
    target.currentHP = Math.max(0, target.currentHP - damage)
    
    // 消耗弹药
    if (attack.ammo && attack.currentAmmo > 0) {
      attack.currentAmmo--
    }
    
    return {
      success: true,
      type: 'attack',
      attacker: monster.name,
      target: target.name,
      attack: attack.name,
      roll,
      skillValue,
      successLevel,
      damage,
      targetHP: target.currentHP,
      description: `${monster.name}用${attack.name}对${target.name}造成${damage}点伤害`
    }
  }

  /**
   * 执行移动
   * @param {Object} monster 怪物
   * @param {Object} target 目标
   * @returns {Object} 移动结果
   */
  static executeMove(monster, target) {
    const moveDistance = monster.movementRate || 8
    const direction = {
      x: target.position.x - monster.position.x,
      y: target.position.y - monster.position.y
    }
    
    const distance = Math.sqrt(direction.x * direction.x + direction.y * direction.y)
    if (distance > 0) {
      const normalizedX = direction.x / distance
      const normalizedY = direction.y / distance
      
      monster.position.x += normalizedX * Math.min(moveDistance, distance)
      monster.position.y += normalizedY * Math.min(moveDistance, distance)
    }
    
    return {
      success: true,
      type: 'move',
      character: monster.name,
      newPosition: { ...monster.position },
      description: `${monster.name}移动了${Math.min(moveDistance, distance)}米`
    }
  }

  /**
   * 执行逃跑
   * @param {Object} monster 怪物
   * @param {Array} targets 威胁目标
   * @returns {Object} 逃跑结果
   */
  static executeFlee(monster, targets) {
    // 寻找最安全的方向
    let fleeDirection = { x: 0, y: 0 }
    
    targets.forEach(target => {
      const direction = {
        x: monster.position.x - target.position.x,
        y: monster.position.y - target.position.y
      }
      const distance = Math.sqrt(direction.x * direction.x + direction.y * direction.y)
      if (distance > 0) {
        fleeDirection.x += direction.x / distance
        fleeDirection.y += direction.y / distance
      }
    })
    
    const fleeDistance = (monster.movementRate || 8) * 2 // 逃跑时移动更快
    const totalDistance = Math.sqrt(fleeDirection.x * fleeDirection.x + fleeDirection.y * fleeDirection.y)
    
    if (totalDistance > 0) {
      monster.position.x += (fleeDirection.x / totalDistance) * fleeDistance
      monster.position.y += (fleeDirection.y / totalDistance) * fleeDistance
    }
    
    return {
      success: true,
      type: 'flee',
      character: monster.name,
      newPosition: { ...monster.position },
      description: `${monster.name}试图逃跑`
    }
  }

  /**
   * 执行等待
   * @param {Object} monster 怪物
   * @returns {Object} 等待结果
   */
  static executeWait(monster) {
    return {
      success: true,
      type: 'wait',
      character: monster.name,
      description: `${monster.name}保持警戒`
    }
  }

  /**
   * 应用特殊能力
   * @param {Object} monster 怪物
   * @param {string} abilityName 能力名称
   * @param {Object} context 上下文
   * @returns {Object} 能力效果
   */
  static applySpecialAbility(monster, abilityName, context = {}) {
    const ability = monster.specialAbilities?.find(a => a.name === abilityName)
    if (!ability) return null

    switch (abilityName) {
      case '再生':
        if (monster.currentHP < monster.hitPoints) {
          monster.currentHP = Math.min(monster.hitPoints, monster.currentHP + 1)
          return {
            success: true,
            description: `${monster.name}恢复了1点生命值`
          }
        }
        break
        
      case '群体狩猎':
        const allies = context.allies?.filter(a => a.id === monster.id) || []
        if (allies.length > 1) {
          return {
            success: true,
            bonus: allies.length * 5,
            description: `${monster.name}从群体狩猎中获得+${allies.length * 5}攻击加值`
          }
        }
        break
        
      case '恐怖形象':
        if (context.target && !context.target.hasSeenBefore) {
          const sanityLoss = monster.sanityLoss?.first || '0/1d4'
          return {
            success: true,
            sanityLoss,
            description: `${context.target.name}看到${monster.name}的恐怖形象`
          }
        }
        break
    }

    return null
  }

  /**
   * 检查怪物状态
   * @param {Object} monster 怪物实例
   * @returns {Object} 状态信息
   */
  static checkMonsterStatus(monster) {
    const healthPercentage = monster.currentHP / monster.hitPoints
    let status = 'healthy'
    
    if (healthPercentage <= 0) {
      status = 'dead'
    } else if (healthPercentage <= 0.25) {
      status = 'critical'
    } else if (healthPercentage <= 0.5) {
      status = 'wounded'
    } else if (healthPercentage <= 0.75) {
      status = 'injured'
    }
    
    return {
      status,
      healthPercentage,
      isAlive: monster.currentHP > 0,
      canAct: monster.currentHP > 0 && !monster.conditions?.includes('unconscious')
    }
  }
}

export default MonsterSystem