{"ast": null, "code": "import _typeof from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/typeof.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.index-of.js\";\nimport \"core-js/modules/es.array.join.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.date.to-iso-string.js\";\nimport \"core-js/modules/es.date.to-json.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.json.stringify.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.regexp.exec.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.replace.js\";\nimport \"core-js/modules/es.string.trim.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.url.js\";\nimport \"core-js/modules/web.url.to-json.js\";\nimport \"core-js/modules/web.url-search-params.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nexport default {\n  name: 'CombatLog',\n  props: {\n    combatLogs: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    autoScroll: {\n      type: Boolean,\n      \"default\": true\n    }\n  },\n  data: function data() {\n    return {\n      // 过滤器\n      activeFilters: ['all'],\n      searchQuery: '',\n      // 日志过滤器配置\n      logFilters: [{\n        type: 'all',\n        name: '全部',\n        icon: 'fas fa-list'\n      }, {\n        type: 'attack',\n        name: '攻击',\n        icon: 'fas fa-sword'\n      }, {\n        type: 'damage',\n        name: '伤害',\n        icon: 'fas fa-heart-broken'\n      }, {\n        type: 'heal',\n        name: '治疗',\n        icon: 'fas fa-heart'\n      }, {\n        type: 'move',\n        name: '移动',\n        icon: 'fas fa-walking'\n      }, {\n        type: 'skill',\n        name: '技能',\n        icon: 'fas fa-dice-d20'\n      }, {\n        type: 'status',\n        name: '状态',\n        icon: 'fas fa-magic'\n      }, {\n        type: 'system',\n        name: '系统',\n        icon: 'fas fa-cog'\n      }],\n      // 自动滚动状态\n      autoScrollEnabled: true\n    };\n  },\n  computed: {\n    // 过滤后的日志\n    filteredLogs: function filteredLogs() {\n      var _this = this;\n      var logs = this.combatLogs;\n\n      // 类型过滤\n      if (!this.activeFilters.includes('all')) {\n        logs = logs.filter(function (log) {\n          return _this.activeFilters.includes(log.type);\n        });\n      }\n\n      // 搜索过滤\n      if (this.searchQuery.trim()) {\n        var query = this.searchQuery.toLowerCase();\n        logs = logs.filter(function (log) {\n          var _log$participant;\n          return log.message.toLowerCase().includes(query) || ((_log$participant = log.participant) === null || _log$participant === void 0 ? void 0 : _log$participant.name.toLowerCase().includes(query));\n        });\n      }\n      return logs;\n    }\n  },\n  watch: {\n    // 监听日志变化，自动滚动到底部\n    combatLogs: {\n      handler: function handler() {\n        var _this2 = this;\n        if (this.autoScrollEnabled) {\n          this.$nextTick(function () {\n            _this2.scrollToBottom();\n          });\n        }\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 切换过滤器\n    toggleFilter: function toggleFilter(filterType) {\n      if (filterType === 'all') {\n        this.activeFilters = ['all'];\n      } else {\n        var index = this.activeFilters.indexOf(filterType);\n        if (index > -1) {\n          this.activeFilters.splice(index, 1);\n          if (this.activeFilters.length === 0) {\n            this.activeFilters = ['all'];\n          }\n        } else {\n          this.activeFilters = this.activeFilters.filter(function (f) {\n            return f !== 'all';\n          });\n          this.activeFilters.push(filterType);\n        }\n      }\n    },\n    // 切换自动滚动\n    toggleAutoScroll: function toggleAutoScroll() {\n      this.autoScrollEnabled = !this.autoScrollEnabled;\n      this.$emit('toggle-auto-scroll', this.autoScrollEnabled);\n    },\n    // 清空日志\n    clearLog: function clearLog() {\n      this.$emit('clear-log');\n    },\n    // 导出日志\n    exportLog: function exportLog() {\n      var _this3 = this;\n      var logText = this.combatLogs.map(function (entry) {\n        var time = _this3.formatTime(entry.timestamp);\n        var round = entry.round ? \"[\\u7B2C\".concat(entry.round, \"\\u8F6E] \") : '';\n        return \"\".concat(time, \" \").concat(round).concat(entry.message);\n      }).join('\\n');\n      var blob = new Blob([logText], {\n        type: 'text/plain'\n      });\n      var url = URL.createObjectURL(blob);\n      var a = document.createElement('a');\n      a.href = url;\n      a.download = \"combat-log-\".concat(new Date().toISOString().slice(0, 10), \".txt\");\n      a.click();\n      URL.revokeObjectURL(url);\n    },\n    // 滚动到顶部\n    scrollToTop: function scrollToTop() {\n      var content = this.$refs.logContent;\n      if (content) {\n        content.scrollTop = 0;\n      }\n    },\n    // 滚动到底部\n    scrollToBottom: function scrollToBottom() {\n      var content = this.$refs.logContent;\n      if (content) {\n        content.scrollTop = content.scrollHeight;\n      }\n    },\n    // 格式化时间\n    formatTime: function formatTime(timestamp) {\n      var date = new Date(timestamp);\n      return date.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      });\n    },\n    // 格式化消息\n    formatMessage: function formatMessage(message) {\n      // 高亮关键词\n      return message.replace(/(\\d+)点伤害/g, '<span class=\"damage-highlight\">$1点伤害</span>').replace(/(\\d+)点治疗/g, '<span class=\"heal-highlight\">$1点治疗</span>').replace(/(大成功|极难成功|困难成功|常规成功|失败|大失败)/g, '<span class=\"success-highlight\">$1</span>').replace(/投掷(\\d+)/g, '<span class=\"roll-highlight\">投掷$1</span>');\n    },\n    // 获取日志图标\n    getLogIcon: function getLogIcon(type) {\n      var icons = {\n        attack: 'fas fa-sword',\n        damage: 'fas fa-heart-broken',\n        heal: 'fas fa-heart',\n        move: 'fas fa-walking',\n        skill: 'fas fa-dice-d20',\n        status: 'fas fa-magic',\n        system: 'fas fa-cog',\n        round_start: 'fas fa-play',\n        round_end: 'fas fa-stop',\n        combat_start: 'fas fa-flag',\n        combat_end: 'fas fa-flag-checkered'\n      };\n      return icons[type] || 'fas fa-info-circle';\n    },\n    // 格式化详细信息标签\n    formatDetailLabel: function formatDetailLabel(key) {\n      var labels = {\n        attacker: '攻击者',\n        target: '目标',\n        weapon: '武器',\n        damage: '伤害',\n        skill: '技能',\n        roll: '投掷',\n        modifier: '修正',\n        result: '结果'\n      };\n      return labels[key] || key;\n    },\n    // 格式化详细信息值\n    formatDetailValue: function formatDetailValue(value) {\n      if (_typeof(value) === 'object') {\n        return JSON.stringify(value);\n      }\n      return String(value);\n    },\n    // 获取骰子投掷样式类\n    getDiceRollClass: function getDiceRollClass(roll, diceRoll) {\n      if (diceRoll.formula.includes('d100')) {\n        if (roll === 1) return 'critical-success';\n        if (roll >= 96) return 'critical-failure';\n      } else if (diceRoll.formula.includes('d20')) {\n        if (roll === 20) return 'critical-success';\n        if (roll === 1) return 'critical-failure';\n      }\n      return 'normal';\n    },\n    // 获取成功等级文本\n    getSuccessLevelText: function getSuccessLevelText(level) {\n      var texts = {\n        critical: '大成功',\n        extreme: '极难成功',\n        hard: '困难成功',\n        regular: '常规成功',\n        failure: '失败',\n        fumble: '大失败'\n      };\n      return texts[level] || level;\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "combatLogs", "type", "Array", "default", "autoScroll", "Boolean", "data", "activeFilters", "searchQuery", "logFilters", "icon", "autoScrollEnabled", "computed", "filteredLogs", "_this", "logs", "includes", "filter", "log", "trim", "query", "toLowerCase", "_log$participant", "message", "participant", "watch", "handler", "_this2", "$nextTick", "scrollToBottom", "deep", "methods", "toggleFilter", "filterType", "index", "indexOf", "splice", "length", "f", "push", "toggleAutoScroll", "$emit", "clearLog", "exportLog", "_this3", "logText", "map", "entry", "time", "formatTime", "timestamp", "round", "concat", "join", "blob", "Blob", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "Date", "toISOString", "slice", "click", "revokeObjectURL", "scrollToTop", "content", "$refs", "logContent", "scrollTop", "scrollHeight", "date", "toLocaleTimeString", "hour", "minute", "second", "formatMessage", "replace", "getLogIcon", "icons", "attack", "damage", "heal", "move", "skill", "status", "system", "round_start", "round_end", "combat_start", "combat_end", "formatDetailLabel", "key", "labels", "attacker", "target", "weapon", "roll", "modifier", "result", "formatDetailValue", "value", "_typeof", "JSON", "stringify", "String", "getDiceRollClass", "diceRoll", "formula", "getSuccessLevelText", "level", "texts", "critical", "extreme", "hard", "regular", "failure", "fumble"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatLog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"combat-log\">\r\n    <!-- 日志头部 -->\r\n    <div class=\"log-header\">\r\n      <div class=\"header-left\">\r\n        <i class=\"fas fa-scroll\"></i>\r\n        <h3>战斗日志</h3>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <button @click=\"toggleAutoScroll\" class=\"auto-scroll-btn\" :class=\"{ active: autoScroll }\">\r\n          <i class=\"fas fa-arrow-down\"></i>\r\n        </button>\r\n        <button @click=\"clearLog\" class=\"clear-btn\" title=\"清空日志\">\r\n          <i class=\"fas fa-trash\"></i>\r\n        </button>\r\n        <button @click=\"exportLog\" class=\"export-btn\" title=\"导出日志\">\r\n          <i class=\"fas fa-download\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 过滤器 -->\r\n    <div class=\"log-filters\">\r\n      <div class=\"filter-group\">\r\n        <button \r\n          v-for=\"filter in logFilters\" \r\n          :key=\"filter.type\"\r\n          @click=\"toggleFilter(filter.type)\"\r\n          class=\"filter-btn\"\r\n          :class=\"{ active: activeFilters.includes(filter.type) }\"\r\n        >\r\n          <i :class=\"filter.icon\"></i>\r\n          <span>{{ filter.name }}</span>\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"search-box\">\r\n        <input \r\n          v-model=\"searchQuery\" \r\n          type=\"text\" \r\n          placeholder=\"搜索日志...\"\r\n          class=\"search-input\"\r\n        >\r\n        <i class=\"fas fa-search search-icon\"></i>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 日志内容 -->\r\n    <div class=\"log-content\" ref=\"logContent\">\r\n      <div \r\n        v-for=\"(entry, index) in filteredLogs\" \r\n        :key=\"entry.id || index\"\r\n        class=\"log-entry\"\r\n        :class=\"[entry.type, entry.severity || 'normal']\"\r\n      >\r\n        <!-- 时间戳 -->\r\n        <div class=\"log-timestamp\">\r\n          {{ formatTime(entry.timestamp) }}\r\n        </div>\r\n        \r\n        <!-- 轮次信息 -->\r\n        <div class=\"log-round\" v-if=\"entry.round\">\r\n          第{{ entry.round }}轮\r\n        </div>\r\n        \r\n        <!-- 日志图标 -->\r\n        <div class=\"log-icon\">\r\n          <i :class=\"getLogIcon(entry.type)\"></i>\r\n        </div>\r\n        \r\n        <!-- 日志内容 -->\r\n        <div class=\"log-message\">\r\n          <div class=\"message-text\" v-html=\"formatMessage(entry.message)\"></div>\r\n          \r\n          <!-- 详细信息 -->\r\n          <div class=\"message-details\" v-if=\"entry.details\">\r\n            <div \r\n              v-for=\"(detail, key) in entry.details\" \r\n              :key=\"key\"\r\n              class=\"detail-item\"\r\n            >\r\n              <span class=\"detail-label\">{{ formatDetailLabel(key) }}:</span>\r\n              <span class=\"detail-value\">{{ formatDetailValue(detail) }}</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 骰子结果 -->\r\n          <div class=\"dice-result\" v-if=\"entry.diceRoll\">\r\n            <div class=\"dice-formula\">{{ entry.diceRoll.formula }}</div>\r\n            <div class=\"dice-rolls\">\r\n              <span \r\n                v-for=\"(roll, i) in entry.diceRoll.rolls\" \r\n                :key=\"i\"\r\n                class=\"dice-roll\"\r\n                :class=\"getDiceRollClass(roll, entry.diceRoll)\"\r\n              >\r\n                {{ roll }}\r\n              </span>\r\n            </div>\r\n            <div class=\"dice-total\">总计: {{ entry.diceRoll.total }}</div>\r\n          </div>\r\n          \r\n          <!-- 成功等级 -->\r\n          <div class=\"success-level\" v-if=\"entry.successLevel\">\r\n            <span class=\"success-badge\" :class=\"entry.successLevel\">\r\n              {{ getSuccessLevelText(entry.successLevel) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 参与者头像 -->\r\n        <div class=\"log-participant\" v-if=\"entry.participant\">\r\n          <img \r\n            :src=\"entry.participant.avatar || '/default-avatar.png'\" \r\n            :alt=\"entry.participant.name\"\r\n            class=\"participant-avatar\"\r\n          >\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 空状态 -->\r\n      <div v-if=\"filteredLogs.length === 0\" class=\"empty-state\">\r\n        <i class=\"fas fa-inbox\"></i>\r\n        <p>{{ searchQuery ? '没有找到匹配的日志' : '暂无战斗日志' }}</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部状态 -->\r\n    <div class=\"log-footer\">\r\n      <div class=\"log-stats\">\r\n        <span>总计: {{ combatLogs.length }} 条</span>\r\n        <span>显示: {{ filteredLogs.length }} 条</span>\r\n      </div>\r\n      <div class=\"log-actions\">\r\n        <button @click=\"scrollToTop\" class=\"scroll-btn\">\r\n          <i class=\"fas fa-arrow-up\"></i>\r\n          顶部\r\n        </button>\r\n        <button @click=\"scrollToBottom\" class=\"scroll-btn\">\r\n          <i class=\"fas fa-arrow-down\"></i>\r\n          底部\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CombatLog',\r\n  props: {\r\n    combatLogs: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      // 过滤器\r\n      activeFilters: ['all'],\r\n      searchQuery: '',\r\n      \r\n      // 日志过滤器配置\r\n      logFilters: [\r\n        { type: 'all', name: '全部', icon: 'fas fa-list' },\r\n        { type: 'attack', name: '攻击', icon: 'fas fa-sword' },\r\n        { type: 'damage', name: '伤害', icon: 'fas fa-heart-broken' },\r\n        { type: 'heal', name: '治疗', icon: 'fas fa-heart' },\r\n        { type: 'move', name: '移动', icon: 'fas fa-walking' },\r\n        { type: 'skill', name: '技能', icon: 'fas fa-dice-d20' },\r\n        { type: 'status', name: '状态', icon: 'fas fa-magic' },\r\n        { type: 'system', name: '系统', icon: 'fas fa-cog' }\r\n      ],\r\n      \r\n      // 自动滚动状态\r\n      autoScrollEnabled: true\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 过滤后的日志\r\n    filteredLogs() {\r\n      let logs = this.combatLogs\r\n      \r\n      // 类型过滤\r\n      if (!this.activeFilters.includes('all')) {\r\n        logs = logs.filter(log => this.activeFilters.includes(log.type))\r\n      }\r\n      \r\n      // 搜索过滤\r\n      if (this.searchQuery.trim()) {\r\n        const query = this.searchQuery.toLowerCase()\r\n        logs = logs.filter(log => \r\n          log.message.toLowerCase().includes(query) ||\r\n          log.participant?.name.toLowerCase().includes(query)\r\n        )\r\n      }\r\n      \r\n      return logs\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    // 监听日志变化，自动滚动到底部\r\n    combatLogs: {\r\n      handler() {\r\n        if (this.autoScrollEnabled) {\r\n          this.$nextTick(() => {\r\n            this.scrollToBottom()\r\n          })\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 切换过滤器\r\n    toggleFilter(filterType) {\r\n      if (filterType === 'all') {\r\n        this.activeFilters = ['all']\r\n      } else {\r\n        const index = this.activeFilters.indexOf(filterType)\r\n        if (index > -1) {\r\n          this.activeFilters.splice(index, 1)\r\n          if (this.activeFilters.length === 0) {\r\n            this.activeFilters = ['all']\r\n          }\r\n        } else {\r\n          this.activeFilters = this.activeFilters.filter(f => f !== 'all')\r\n          this.activeFilters.push(filterType)\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 切换自动滚动\r\n    toggleAutoScroll() {\r\n      this.autoScrollEnabled = !this.autoScrollEnabled\r\n      this.$emit('toggle-auto-scroll', this.autoScrollEnabled)\r\n    },\r\n    \r\n    // 清空日志\r\n    clearLog() {\r\n      this.$emit('clear-log')\r\n    },\r\n    \r\n    // 导出日志\r\n    exportLog() {\r\n      const logText = this.combatLogs.map(entry => {\r\n        const time = this.formatTime(entry.timestamp)\r\n        const round = entry.round ? `[第${entry.round}轮] ` : ''\r\n        return `${time} ${round}${entry.message}`\r\n      }).join('\\n')\r\n      \r\n      const blob = new Blob([logText], { type: 'text/plain' })\r\n      const url = URL.createObjectURL(blob)\r\n      const a = document.createElement('a')\r\n      a.href = url\r\n      a.download = `combat-log-${new Date().toISOString().slice(0, 10)}.txt`\r\n      a.click()\r\n      URL.revokeObjectURL(url)\r\n    },\r\n    \r\n    // 滚动到顶部\r\n    scrollToTop() {\r\n      const content = this.$refs.logContent\r\n      if (content) {\r\n        content.scrollTop = 0\r\n      }\r\n    },\r\n    \r\n    // 滚动到底部\r\n    scrollToBottom() {\r\n      const content = this.$refs.logContent\r\n      if (content) {\r\n        content.scrollTop = content.scrollHeight\r\n      }\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp)\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    \r\n    // 格式化消息\r\n    formatMessage(message) {\r\n      // 高亮关键词\r\n      return message\r\n        .replace(/(\\d+)点伤害/g, '<span class=\"damage-highlight\">$1点伤害</span>')\r\n        .replace(/(\\d+)点治疗/g, '<span class=\"heal-highlight\">$1点治疗</span>')\r\n        .replace(/(大成功|极难成功|困难成功|常规成功|失败|大失败)/g, '<span class=\"success-highlight\">$1</span>')\r\n        .replace(/投掷(\\d+)/g, '<span class=\"roll-highlight\">投掷$1</span>')\r\n    },\r\n    \r\n    // 获取日志图标\r\n    getLogIcon(type) {\r\n      const icons = {\r\n        attack: 'fas fa-sword',\r\n        damage: 'fas fa-heart-broken',\r\n        heal: 'fas fa-heart',\r\n        move: 'fas fa-walking',\r\n        skill: 'fas fa-dice-d20',\r\n        status: 'fas fa-magic',\r\n        system: 'fas fa-cog',\r\n        round_start: 'fas fa-play',\r\n        round_end: 'fas fa-stop',\r\n        combat_start: 'fas fa-flag',\r\n        combat_end: 'fas fa-flag-checkered'\r\n      }\r\n      return icons[type] || 'fas fa-info-circle'\r\n    },\r\n    \r\n    // 格式化详细信息标签\r\n    formatDetailLabel(key) {\r\n      const labels = {\r\n        attacker: '攻击者',\r\n        target: '目标',\r\n        weapon: '武器',\r\n        damage: '伤害',\r\n        skill: '技能',\r\n        roll: '投掷',\r\n        modifier: '修正',\r\n        result: '结果'\r\n      }\r\n      return labels[key] || key\r\n    },\r\n    \r\n    // 格式化详细信息值\r\n    formatDetailValue(value) {\r\n      if (typeof value === 'object') {\r\n        return JSON.stringify(value)\r\n      }\r\n      return String(value)\r\n    },\r\n    \r\n    // 获取骰子投掷样式类\r\n    getDiceRollClass(roll, diceRoll) {\r\n      if (diceRoll.formula.includes('d100')) {\r\n        if (roll === 1) return 'critical-success'\r\n        if (roll >= 96) return 'critical-failure'\r\n      } else if (diceRoll.formula.includes('d20')) {\r\n        if (roll === 20) return 'critical-success'\r\n        if (roll === 1) return 'critical-failure'\r\n      }\r\n      return 'normal'\r\n    },\r\n    \r\n    // 获取成功等级文本\r\n    getSuccessLevelText(level) {\r\n      const texts = {\r\n        critical: '大成功',\r\n        extreme: '极难成功',\r\n        hard: '困难成功',\r\n        regular: '常规成功',\r\n        failure: '失败',\r\n        fumble: '大失败'\r\n      }\r\n      return texts[level] || level\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.combat-log {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 2px solid #0f3460;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  color: #e94560;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 日志头部 */\r\n.log-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #0f3460;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left h3 {\r\n  margin: 0;\r\n  color: #e94560;\r\n  font-size: 1.1rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 6px;\r\n}\r\n\r\n.auto-scroll-btn,\r\n.clear-btn,\r\n.export-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 4px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.auto-scroll-btn:hover,\r\n.clear-btn:hover,\r\n.export-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n.auto-scroll-btn.active {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n}\r\n\r\n/* 过滤器 */\r\n.log-filters {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  gap: 12px;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  gap: 4px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-btn {\r\n  padding: 4px 8px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n  color: #bdc3c7;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.filter-btn:hover {\r\n  background: rgba(15, 52, 96, 0.5);\r\n  border-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.filter-btn.active {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n  color: #e94560;\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  min-width: 150px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 6px 30px 6px 10px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n  color: #ecf0f1;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.search-input::placeholder {\r\n  color: #bdc3c7;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  right: 8px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #bdc3c7;\r\n  font-size: 0.8rem;\r\n}\r\n\r\n/* 日志内容 */\r\n.log-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 12px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.log-entry {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n  padding: 8px;\r\n  margin-bottom: 6px;\r\n  background: rgba(15, 52, 96, 0.2);\r\n  border-left: 3px solid transparent;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.log-entry:hover {\r\n  background: rgba(15, 52, 96, 0.3);\r\n}\r\n\r\n/* 日志类型样式 */\r\n.log-entry.attack { border-left-color: #e74c3c; }\r\n.log-entry.damage { border-left-color: #c0392b; }\r\n.log-entry.heal { border-left-color: #27ae60; }\r\n.log-entry.move { border-left-color: #3498db; }\r\n.log-entry.skill { border-left-color: #9b59b6; }\r\n.log-entry.status { border-left-color: #f39c12; }\r\n.log-entry.system { border-left-color: #95a5a6; }\r\n\r\n/* 严重程度样式 */\r\n.log-entry.critical {\r\n  background: rgba(231, 76, 60, 0.1);\r\n  border-left-color: #e74c3c;\r\n}\r\n\r\n.log-entry.warning {\r\n  background: rgba(243, 156, 18, 0.1);\r\n  border-left-color: #f39c12;\r\n}\r\n\r\n.log-entry.success {\r\n  background: rgba(39, 174, 96, 0.1);\r\n  border-left-color: #27ae60;\r\n}\r\n\r\n.log-timestamp {\r\n  font-size: 0.7rem;\r\n  color: #7f8c8d;\r\n  min-width: 60px;\r\n  text-align: right;\r\n}\r\n\r\n.log-round {\r\n  font-size: 0.7rem;\r\n  color: #e94560;\r\n  background: rgba(233, 69, 96, 0.2);\r\n  padding: 2px 6px;\r\n  border-radius: 10px;\r\n  min-width: 50px;\r\n  text-align: center;\r\n}\r\n\r\n.log-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #e94560;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.log-message {\r\n  flex: 1;\r\n  font-size: 0.9rem;\r\n  line-height: 1.4;\r\n}\r\n\r\n.message-text {\r\n  color: #ecf0f1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.message-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.detail-item {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.detail-label {\r\n  font-weight: bold;\r\n}\r\n\r\n.detail-value {\r\n  color: #ecf0f1;\r\n}\r\n\r\n/* 骰子结果 */\r\n.dice-result {\r\n  margin-top: 6px;\r\n  padding: 6px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.dice-formula {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.dice-rolls {\r\n  display: flex;\r\n  gap: 4px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.dice-roll {\r\n  padding: 2px 6px;\r\n  background: rgba(52, 73, 94, 0.8);\r\n  border-radius: 4px;\r\n  font-size: 0.8rem;\r\n  font-weight: bold;\r\n  color: #ecf0f1;\r\n}\r\n\r\n.dice-roll.critical-success {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.dice-roll.critical-failure {\r\n  background: #e74c3c;\r\n  color: white;\r\n}\r\n\r\n.dice-total {\r\n  font-size: 0.8rem;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n}\r\n\r\n/* 成功等级 */\r\n.success-level {\r\n  margin-top: 4px;\r\n}\r\n\r\n.success-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  font-size: 0.7rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.success-badge.critical { background: #27ae60; color: white; }\r\n.success-badge.extreme { background: #2ecc71; color: white; }\r\n.success-badge.hard { background: #3498db; color: white; }\r\n.success-badge.regular { background: #95a5a6; color: white; }\r\n.success-badge.failure { background: #e67e22; color: white; }\r\n.success-badge.fumble { background: #e74c3c; color: white; }\r\n\r\n.log-participant {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.participant-avatar {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 高亮样式 */\r\n.damage-highlight {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n}\r\n\r\n.heal-highlight {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n}\r\n\r\n.success-highlight {\r\n  color: #f39c12;\r\n  font-weight: bold;\r\n}\r\n\r\n.roll-highlight {\r\n  color: #3498db;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 200px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 3rem;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 底部状态 */\r\n.log-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: 8px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.log-stats {\r\n  display: flex;\r\n  gap: 12px;\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.log-actions {\r\n  display: flex;\r\n  gap: 6px;\r\n}\r\n\r\n.scroll-btn {\r\n  padding: 4px 8px;\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 4px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.scroll-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.log-content::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.log-content::-webkit-scrollbar-track {\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border-radius: 3px;\r\n}\r\n\r\n.log-content::-webkit-scrollbar-thumb {\r\n  background: rgba(233, 69, 96, 0.6);\r\n  border-radius: 3px;\r\n}\r\n\r\n.log-content::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(233, 69, 96, 0.8);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .combat-log {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .log-filters {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .filter-group {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .log-entry {\r\n    padding: 6px;\r\n    gap: 6px;\r\n  }\r\n  \r\n  .log-timestamp {\r\n    min-width: 50px;\r\n    font-size: 0.6rem;\r\n  }\r\n  \r\n  .log-round {\r\n    min-width: 40px;\r\n    font-size: 0.6rem;\r\n  }\r\n  \r\n  .message-text {\r\n    font-size: 0.8rem;\r\n  }\r\n  \r\n  .log-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoJA,eAAe;EACbA,IAAI,EAAE,WAAW;EACjBC,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDC,UAAU,EAAE;MACVH,IAAI,EAAEI,OAAO;MACb,WAAS;IACX;EACF,CAAC;EAEDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,aAAa,EAAE,CAAC,KAAK,CAAC;MACtBC,WAAW,EAAE,EAAE;MAEf;MACAC,UAAU,EAAE,CACV;QAAER,IAAI,EAAE,KAAK;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAc,CAAC,EAChD;QAAET,IAAI,EAAE,QAAQ;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAe,CAAC,EACpD;QAAET,IAAI,EAAE,QAAQ;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAsB,CAAC,EAC3D;QAAET,IAAI,EAAE,MAAM;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAe,CAAC,EAClD;QAAET,IAAI,EAAE,MAAM;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAiB,CAAC,EACpD;QAAET,IAAI,EAAE,OAAO;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAkB,CAAC,EACtD;QAAET,IAAI,EAAE,QAAQ;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAe,CAAC,EACpD;QAAET,IAAI,EAAE,QAAQ;QAAEH,IAAI,EAAE,IAAI;QAAEY,IAAI,EAAE;MAAa,EAClD;MAED;MACAC,iBAAiB,EAAE;IACrB;EACF,CAAC;EAEDC,QAAQ,EAAE;IACR;IACAC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACb,IAAIC,IAAG,GAAI,IAAI,CAACf,UAAS;;MAEzB;MACA,IAAI,CAAC,IAAI,CAACO,aAAa,CAACS,QAAQ,CAAC,KAAK,CAAC,EAAE;QACvCD,IAAG,GAAIA,IAAI,CAACE,MAAM,CAAC,UAAAC,GAAE;UAAA,OAAKJ,KAAI,CAACP,aAAa,CAACS,QAAQ,CAACE,GAAG,CAACjB,IAAI,CAAC;QAAA;MACjE;;MAEA;MACA,IAAI,IAAI,CAACO,WAAW,CAACW,IAAI,CAAC,CAAC,EAAE;QAC3B,IAAMC,KAAI,GAAI,IAAI,CAACZ,WAAW,CAACa,WAAW,CAAC;QAC3CN,IAAG,GAAIA,IAAI,CAACE,MAAM,CAAC,UAAAC,GAAE;UAAA,IAAAI,gBAAA;UAAA,OACnBJ,GAAG,CAACK,OAAO,CAACF,WAAW,CAAC,CAAC,CAACL,QAAQ,CAACI,KAAK,OAAAE,gBAAA,GACxCJ,GAAG,CAACM,WAAW,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBxB,IAAI,CAACuB,WAAW,CAAC,CAAC,CAACL,QAAQ,CAACI,KAAK;QAAA,CACpD;MACF;MAEA,OAAOL,IAAG;IACZ;EACF,CAAC;EAEDU,KAAK,EAAE;IACL;IACAzB,UAAU,EAAE;MACV0B,OAAO,WAAPA,OAAOA,CAAA,EAAG;QAAA,IAAAC,MAAA;QACR,IAAI,IAAI,CAAChB,iBAAiB,EAAE;UAC1B,IAAI,CAACiB,SAAS,CAAC,YAAM;YACnBD,MAAI,CAACE,cAAc,CAAC;UACtB,CAAC;QACH;MACF,CAAC;MACDC,IAAI,EAAE;IACR;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;IACAC,YAAY,WAAZA,YAAYA,CAACC,UAAU,EAAE;MACvB,IAAIA,UAAS,KAAM,KAAK,EAAE;QACxB,IAAI,CAAC1B,aAAY,GAAI,CAAC,KAAK;MAC7B,OAAO;QACL,IAAM2B,KAAI,GAAI,IAAI,CAAC3B,aAAa,CAAC4B,OAAO,CAACF,UAAU;QACnD,IAAIC,KAAI,GAAI,CAAC,CAAC,EAAE;UACd,IAAI,CAAC3B,aAAa,CAAC6B,MAAM,CAACF,KAAK,EAAE,CAAC;UAClC,IAAI,IAAI,CAAC3B,aAAa,CAAC8B,MAAK,KAAM,CAAC,EAAE;YACnC,IAAI,CAAC9B,aAAY,GAAI,CAAC,KAAK;UAC7B;QACF,OAAO;UACL,IAAI,CAACA,aAAY,GAAI,IAAI,CAACA,aAAa,CAACU,MAAM,CAAC,UAAAqB,CAAA;YAAA,OAAKA,CAAA,KAAM,KAAK;UAAA;UAC/D,IAAI,CAAC/B,aAAa,CAACgC,IAAI,CAACN,UAAU;QACpC;MACF;IACF,CAAC;IAED;IACAO,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC7B,iBAAgB,GAAI,CAAC,IAAI,CAACA,iBAAgB;MAC/C,IAAI,CAAC8B,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC9B,iBAAiB;IACzD,CAAC;IAED;IACA+B,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,IAAI,CAACD,KAAK,CAAC,WAAW;IACxB,CAAC;IAED;IACAE,SAAS,WAATA,SAASA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACV,IAAMC,OAAM,GAAI,IAAI,CAAC7C,UAAU,CAAC8C,GAAG,CAAC,UAAAC,KAAI,EAAK;QAC3C,IAAMC,IAAG,GAAIJ,MAAI,CAACK,UAAU,CAACF,KAAK,CAACG,SAAS;QAC5C,IAAMC,KAAI,GAAIJ,KAAK,CAACI,KAAI,aAAAC,MAAA,CAASL,KAAK,CAACI,KAAK,gBAAQ,EAAC;QACrD,UAAAC,MAAA,CAAUJ,IAAI,OAAAI,MAAA,CAAID,KAAK,EAAAC,MAAA,CAAGL,KAAK,CAACxB,OAAO;MACzC,CAAC,CAAC,CAAC8B,IAAI,CAAC,IAAI;MAEZ,IAAMC,IAAG,GAAI,IAAIC,IAAI,CAAC,CAACV,OAAO,CAAC,EAAE;QAAE5C,IAAI,EAAE;MAAa,CAAC;MACvD,IAAMuD,GAAE,GAAIC,GAAG,CAACC,eAAe,CAACJ,IAAI;MACpC,IAAMK,CAAA,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;MACpCF,CAAC,CAACG,IAAG,GAAIN,GAAE;MACXG,CAAC,CAACI,QAAO,iBAAAX,MAAA,CAAkB,IAAIY,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,SAAK;MACrEP,CAAC,CAACQ,KAAK,CAAC;MACRV,GAAG,CAACW,eAAe,CAACZ,GAAG;IACzB,CAAC;IAED;IACAa,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAMC,OAAM,GAAI,IAAI,CAACC,KAAK,CAACC,UAAS;MACpC,IAAIF,OAAO,EAAE;QACXA,OAAO,CAACG,SAAQ,GAAI;MACtB;IACF,CAAC;IAED;IACA5C,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAMyC,OAAM,GAAI,IAAI,CAACC,KAAK,CAACC,UAAS;MACpC,IAAIF,OAAO,EAAE;QACXA,OAAO,CAACG,SAAQ,GAAIH,OAAO,CAACI,YAAW;MACzC;IACF,CAAC;IAED;IACAzB,UAAU,WAAVA,UAAUA,CAACC,SAAS,EAAE;MACpB,IAAMyB,IAAG,GAAI,IAAIX,IAAI,CAACd,SAAS;MAC/B,OAAOyB,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC;IACH,CAAC;IAED;IACAC,aAAa,WAAbA,aAAaA,CAACzD,OAAO,EAAE;MACrB;MACA,OAAOA,OAAM,CACV0D,OAAO,CAAC,WAAW,EAAE,6CAA6C,EAClEA,OAAO,CAAC,WAAW,EAAE,2CAA2C,EAChEA,OAAO,CAAC,8BAA8B,EAAE,2CAA2C,EACnFA,OAAO,CAAC,UAAU,EAAE,0CAA0C;IACnE,CAAC;IAED;IACAC,UAAU,WAAVA,UAAUA,CAACjF,IAAI,EAAE;MACf,IAAMkF,KAAI,GAAI;QACZC,MAAM,EAAE,cAAc;QACtBC,MAAM,EAAE,qBAAqB;QAC7BC,IAAI,EAAE,cAAc;QACpBC,IAAI,EAAE,gBAAgB;QACtBC,KAAK,EAAE,iBAAiB;QACxBC,MAAM,EAAE,cAAc;QACtBC,MAAM,EAAE,YAAY;QACpBC,WAAW,EAAE,aAAa;QAC1BC,SAAS,EAAE,aAAa;QACxBC,YAAY,EAAE,aAAa;QAC3BC,UAAU,EAAE;MACd;MACA,OAAOX,KAAK,CAAClF,IAAI,KAAK,oBAAmB;IAC3C,CAAC;IAED;IACA8F,iBAAiB,WAAjBA,iBAAiBA,CAACC,GAAG,EAAE;MACrB,IAAMC,MAAK,GAAI;QACbC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,IAAI;QACZf,MAAM,EAAE,IAAI;QACZG,KAAK,EAAE,IAAI;QACXa,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE;MACV;MACA,OAAON,MAAM,CAACD,GAAG,KAAKA,GAAE;IAC1B,CAAC;IAED;IACAQ,iBAAiB,WAAjBA,iBAAiBA,CAACC,KAAK,EAAE;MACvB,IAAIC,OAAA,CAAOD,KAAI,MAAM,QAAQ,EAAE;QAC7B,OAAOE,IAAI,CAACC,SAAS,CAACH,KAAK;MAC7B;MACA,OAAOI,MAAM,CAACJ,KAAK;IACrB,CAAC;IAED;IACAK,gBAAgB,WAAhBA,gBAAgBA,CAACT,IAAI,EAAEU,QAAQ,EAAE;MAC/B,IAAIA,QAAQ,CAACC,OAAO,CAAChG,QAAQ,CAAC,MAAM,CAAC,EAAE;QACrC,IAAIqF,IAAG,KAAM,CAAC,EAAE,OAAO,kBAAiB;QACxC,IAAIA,IAAG,IAAK,EAAE,EAAE,OAAO,kBAAiB;MAC1C,OAAO,IAAIU,QAAQ,CAACC,OAAO,CAAChG,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC3C,IAAIqF,IAAG,KAAM,EAAE,EAAE,OAAO,kBAAiB;QACzC,IAAIA,IAAG,KAAM,CAAC,EAAE,OAAO,kBAAiB;MAC1C;MACA,OAAO,QAAO;IAChB,CAAC;IAED;IACAY,mBAAmB,WAAnBA,mBAAmBA,CAACC,KAAK,EAAE;MACzB,IAAMC,KAAI,GAAI;QACZC,QAAQ,EAAE,KAAK;QACfC,OAAO,EAAE,MAAM;QACfC,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,MAAM;QACfC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE;MACV;MACA,OAAON,KAAK,CAACD,KAAK,KAAKA,KAAI;IAC7B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}