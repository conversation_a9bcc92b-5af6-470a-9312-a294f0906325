{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/web.timers.js\";\nexport default {\n  name: 'CharacterToken',\n  props: {\n    character: {\n      type: Object,\n      required: true\n    },\n    gridSize: {\n      type: Number,\n      \"default\": 40\n    },\n    zoom: {\n      type: Number,\n      \"default\": 1\n    },\n    selected: {\n      type: <PERSON><PERSON>an,\n      \"default\": false\n    },\n    currentTurn: {\n      type: <PERSON><PERSON>an,\n      \"default\": false\n    },\n    canMove: {\n      type: <PERSON><PERSON><PERSON>,\n      \"default\": false\n    },\n    canAct: {\n      type: <PERSON><PERSON><PERSON>,\n      \"default\": false\n    },\n    showMovementRange: {\n      type: <PERSON><PERSON><PERSON>,\n      \"default\": false\n    },\n    showAttackRange: {\n      type: <PERSON><PERSON><PERSON>,\n      \"default\": false\n    }\n  },\n  data: function data() {\n    return {\n      tokenRadius: 20,\n      isDragging: false,\n      dragStart: null,\n      damageAnimation: null\n    };\n  },\n  computed: {\n    x: function x() {\n      var _this$character$posit;\n      return (((_this$character$posit = this.character.position) === null || _this$character$posit === void 0 ? void 0 : _this$character$posit.x) || 0) * this.gridSize * this.zoom;\n    },\n    y: function y() {\n      var _this$character$posit2;\n      return (((_this$character$posit2 = this.character.position) === null || _this$character$posit2 === void 0 ? void 0 : _this$character$posit2.y) || 0) * this.gridSize * this.zoom;\n    },\n    movementRange: function movementRange() {\n      // 根据角色移动力计算移动范围\n      return this.character.movement || 3;\n    },\n    attackRange: function attackRange() {\n      // 根据装备武器计算攻击范围\n      var weapon = this.character.equippedWeapon;\n      if (!weapon) return 1.5; // 徒手攻击\n\n      if (weapon.type === 'melee') {\n        return weapon.reach || 1.5;\n      } else if (weapon.type === 'ranged') {\n        var _weapon$range;\n        return Math.min(((_weapon$range = weapon.range) === null || _weapon$range === void 0 ? void 0 : _weapon$range.base) || 10, 10); // 限制显示范围\n      }\n      return 1.5;\n    }\n  },\n  methods: {\n    /**\r\n     * 获取默认头像\r\n     */\n    getDefaultAvatar: function getDefaultAvatar() {\n      return this.character.isPlayer ? '/images/default-player-avatar.png' : '/images/default-enemy-avatar.png';\n    },\n    /**\r\n     * 获取生命值颜色\r\n     */\n    getHealthColor: function getHealthColor() {\n      var healthPercent = this.getHealthPercentage();\n      if (healthPercent > 75) return '#4caf50';\n      if (healthPercent > 50) return '#ff9800';\n      if (healthPercent > 25) return '#f44336';\n      return '#9c27b0';\n    },\n    /**\r\n     * 获取生命值百分比\r\n     */\n    getHealthPercentage: function getHealthPercentage() {\n      var current = this.character.currentHP || this.character.hitPoints;\n      var max = this.character.maxHP || this.character.hitPoints;\n      return max > 0 ? current / max * 100 : 0;\n    },\n    /**\r\n     * 获取生命值环的虚线数组\r\n     */\n    getHealthDashArray: function getHealthDashArray() {\n      var circumference = 2 * Math.PI * (this.tokenRadius + 2);\n      var healthPercent = this.getHealthPercentage() / 100;\n      var healthLength = circumference * healthPercent;\n      var gapLength = circumference - healthLength;\n      return \"\".concat(healthLength, \" \").concat(gapLength);\n    },\n    /**\r\n     * 获取状态效果图标\r\n     */\n    getConditionIcon: function getConditionIcon(condition) {\n      var iconMap = {\n        'unconscious': '💤',\n        'dying': '💀',\n        'prone': '⬇️',\n        'stunned': '😵',\n        'restrained': '🔒',\n        'frightened': '😨',\n        'poisoned': '☠️',\n        'bleeding': '🩸',\n        'burning': '🔥',\n        'frozen': '❄️',\n        'paralyzed': '⚡',\n        'blinded': '👁️',\n        'deafened': '👂',\n        'charmed': '💖',\n        'confused': '❓'\n      };\n      return iconMap[condition] || '?';\n    },\n    /**\r\n     * 开始拖拽\r\n     */\n    startDrag: function startDrag(event) {\n      if (!this.canMove) return;\n      this.isDragging = true;\n      this.dragStart = {\n        x: event.clientX,\n        y: event.clientY,\n        characterX: this.character.position.x,\n        characterY: this.character.position.y\n      };\n      document.addEventListener('mousemove', this.handleDrag);\n      document.addEventListener('mouseup', this.endDrag);\n      event.stopPropagation();\n    },\n    /**\r\n     * 处理拖拽\r\n     */\n    handleDrag: function handleDrag(event) {\n      if (!this.isDragging || !this.dragStart) return;\n      var deltaX = event.clientX - this.dragStart.x;\n      var deltaY = event.clientY - this.dragStart.y;\n      var gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom));\n      var gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom));\n      var newX = this.dragStart.characterX + gridDeltaX;\n      var newY = this.dragStart.characterY + gridDeltaY;\n\n      // 检查移动范围\n      var distance = Math.sqrt(Math.pow(newX - this.dragStart.characterX, 2) + Math.pow(newY - this.dragStart.characterY, 2));\n      if (distance <= this.movementRange) {\n        this.$emit('move', this.character, {\n          x: newX,\n          y: newY\n        });\n      }\n    },\n    /**\r\n     * 结束拖拽\r\n     */\n    endDrag: function endDrag() {\n      this.isDragging = false;\n      this.dragStart = null;\n      document.removeEventListener('mousemove', this.handleDrag);\n      document.removeEventListener('mouseup', this.endDrag);\n    },\n    /**\r\n     * 播放伤害动画\r\n     */\n    playDamageAnimation: function playDamageAnimation(damage) {\n      var _this = this;\n      var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';\n      var colors = {\n        normal: '#f44336',\n        critical: '#e91e63',\n        healing: '#4caf50',\n        miss: '#9e9e9e'\n      };\n      var texts = {\n        normal: \"-\".concat(damage),\n        critical: \"CRIT! -\".concat(damage),\n        healing: \"+\".concat(damage),\n        miss: 'MISS'\n      };\n      this.damageAnimation = {\n        text: texts[type] || \"-\".concat(damage),\n        color: colors[type] || '#f44336'\n      };\n\n      // 1.5秒后清除动画\n      setTimeout(function () {\n        _this.damageAnimation = null;\n      }, 1500);\n    },\n    /**\r\n     * 播放治疗动画\r\n     */\n    playHealingAnimation: function playHealingAnimation(healing) {\n      this.playDamageAnimation(healing, 'healing');\n    },\n    /**\r\n     * 播放闪避动画\r\n     */\n    playMissAnimation: function playMissAnimation() {\n      this.playDamageAnimation(0, 'miss');\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "character", "type", "Object", "required", "gridSize", "Number", "zoom", "selected", "Boolean", "currentTurn", "canMove", "canAct", "showMovementRange", "showAttackRange", "data", "tokenRadius", "isDragging", "dragStart", "damageAnimation", "computed", "x", "_this$character$posit", "position", "y", "_this$character$posit2", "movementRange", "movement", "attackRange", "weapon", "equippedWeapon", "reach", "_weapon$range", "Math", "min", "range", "base", "methods", "getDefaultAvatar", "isPlayer", "getHealthColor", "healthPercent", "getHealthPercentage", "current", "currentHP", "hitPoints", "max", "maxHP", "getHealthDashArray", "circumference", "PI", "healthLength", "<PERSON><PERSON><PERSON><PERSON>", "concat", "getConditionIcon", "condition", "iconMap", "startDrag", "event", "clientX", "clientY", "characterX", "characterY", "document", "addEventListener", "handleDrag", "endDrag", "stopPropagation", "deltaX", "deltaY", "gridDeltaX", "round", "gridDeltaY", "newX", "newY", "distance", "sqrt", "pow", "$emit", "removeEventListener", "playDamageAnimation", "damage", "_this", "arguments", "length", "undefined", "colors", "normal", "critical", "healing", "miss", "texts", "text", "color", "setTimeout", "playHealingAnimation", "playMissAnimation"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CharacterToken.vue"], "sourcesContent": ["<template>\r\n  <!-- 2D战场上的角色令牌 -->\r\n  <g \r\n    class=\"character-token\"\r\n    :class=\"{ \r\n      selected, \r\n      'current-turn': currentTurn,\r\n      'can-move': canMove,\r\n      'can-act': canAct,\r\n      player: character.isPlayer,\r\n      enemy: !character.isPlayer\r\n    }\"\r\n    :transform=\"`translate(${x}, ${y})`\"\r\n    @click=\"$emit('select', character)\"\r\n    @contextmenu.prevent=\"$emit('context-menu', character, $event)\"\r\n  >\r\n    <!-- 选中光环 -->\r\n    <circle \r\n      v-if=\"selected\"\r\n      :r=\"tokenRadius + 8\"\r\n      fill=\"none\"\r\n      stroke=\"#3498db\"\r\n      stroke-width=\"3\"\r\n      opacity=\"0.8\"\r\n    >\r\n      <animate \r\n        attributeName=\"stroke-opacity\" \r\n        values=\"0.8;0.3;0.8\" \r\n        dur=\"2s\" \r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- 当前回合光环 -->\r\n    <circle \r\n      v-if=\"currentTurn\"\r\n      :r=\"tokenRadius + 12\"\r\n      fill=\"none\"\r\n      stroke=\"#e74c3c\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"5,5\"\r\n      opacity=\"0.9\"\r\n    >\r\n      <animateTransform\r\n        attributeName=\"transform\"\r\n        type=\"rotate\"\r\n        values=\"0;360\"\r\n        dur=\"3s\"\r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- 移动范围指示 -->\r\n    <circle \r\n      v-if=\"showMovementRange && canMove\"\r\n      :r=\"movementRange * gridSize\"\r\n      fill=\"rgba(76, 175, 80, 0.1)\"\r\n      stroke=\"#4caf50\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"3,3\"\r\n      opacity=\"0.6\"\r\n    />\r\n    \r\n    <!-- 攻击范围指示 -->\r\n    <circle \r\n      v-if=\"showAttackRange && canAct\"\r\n      :r=\"attackRange * gridSize\"\r\n      fill=\"rgba(244, 67, 54, 0.1)\"\r\n      stroke=\"#f44336\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"5,5\"\r\n      opacity=\"0.5\"\r\n    />\r\n    \r\n    <!-- 角色主体 -->\r\n    <g class=\"character-body\">\r\n      <!-- 背景圆 -->\r\n      <circle \r\n        :r=\"tokenRadius\"\r\n        :fill=\"character.isPlayer ? '#2196f3' : '#f44336'\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"2\"\r\n        opacity=\"0.9\"\r\n      />\r\n      \r\n      <!-- 角色头像 -->\r\n      <defs>\r\n        <pattern \r\n          :id=\"`avatar-${character.id}`\" \r\n          patternUnits=\"objectBoundingBox\" \r\n          width=\"100%\" \r\n          height=\"100%\"\r\n        >\r\n          <image \r\n            :href=\"character.avatar || getDefaultAvatar()\"\r\n            x=\"0\" \r\n            y=\"0\" \r\n            :width=\"tokenRadius * 2\" \r\n            :height=\"tokenRadius * 2\"\r\n            preserveAspectRatio=\"xMidYMid slice\"\r\n          />\r\n        </pattern>\r\n      </defs>\r\n      \r\n      <circle \r\n        :r=\"tokenRadius - 3\"\r\n        :fill=\"`url(#avatar-${character.id})`\"\r\n        opacity=\"0.95\"\r\n      />\r\n      \r\n      <!-- 生命值环 -->\r\n      <circle \r\n        :r=\"tokenRadius + 2\"\r\n        fill=\"none\"\r\n        stroke=\"#ddd\"\r\n        stroke-width=\"4\"\r\n        opacity=\"0.3\"\r\n      />\r\n      <circle \r\n        :r=\"tokenRadius + 2\"\r\n        fill=\"none\"\r\n        :stroke=\"getHealthColor()\"\r\n        stroke-width=\"4\"\r\n        :stroke-dasharray=\"getHealthDashArray()\"\r\n        stroke-linecap=\"round\"\r\n        transform=\"rotate(-90)\"\r\n        opacity=\"0.8\"\r\n      />\r\n    </g>\r\n    \r\n    <!-- 角色名称 -->\r\n    <text \r\n      :y=\"tokenRadius + 20\"\r\n      text-anchor=\"middle\"\r\n      font-size=\"12\"\r\n      font-weight=\"bold\"\r\n      :fill=\"character.isPlayer ? '#2196f3' : '#f44336'\"\r\n      class=\"character-name-text\"\r\n    >\r\n      {{ character.name }}\r\n    </text>\r\n    \r\n    <!-- 状态效果图标 -->\r\n    <g class=\"status-effects\" :transform=\"`translate(${-tokenRadius}, ${-tokenRadius - 15})`\">\r\n      <g \r\n        v-for=\"(condition, index) in character.conditions\" \r\n        :key=\"condition\"\r\n        :transform=\"`translate(${index * 16}, 0)`\"\r\n      >\r\n        <circle r=\"8\" fill=\"#333\" opacity=\"0.8\"/>\r\n        <text \r\n          text-anchor=\"middle\" \r\n          dy=\"4\" \r\n          font-size=\"10\" \r\n          fill=\"white\"\r\n        >\r\n          {{ getConditionIcon(condition) }}\r\n        </text>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 行动状态指示器 -->\r\n    <g class=\"action-indicators\" :transform=\"`translate(${tokenRadius - 8}, ${-tokenRadius + 8})`\">\r\n      <!-- 已行动指示 -->\r\n      <circle \r\n        v-if=\"character.hasActed\"\r\n        r=\"6\"\r\n        fill=\"#ff9800\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"character.hasActed\"\r\n        text-anchor=\"middle\"\r\n        dy=\"3\"\r\n        font-size=\"8\"\r\n        fill=\"white\"\r\n        font-weight=\"bold\"\r\n      >\r\n        ✓\r\n      </text>\r\n      \r\n      <!-- 延迟行动指示 -->\r\n      <circle \r\n        v-if=\"character.isDelaying\"\r\n        r=\"6\"\r\n        fill=\"#9c27b0\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"character.isDelaying\"\r\n        text-anchor=\"middle\"\r\n        dy=\"3\"\r\n        font-size=\"8\"\r\n        fill=\"white\"\r\n        font-weight=\"bold\"\r\n      >\r\n        ⏸\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 伤害数字动画 -->\r\n    <g v-if=\"damageAnimation\" class=\"damage-animation\">\r\n      <text \r\n        :y=\"-tokenRadius - 10\"\r\n        text-anchor=\"middle\"\r\n        font-size=\"16\"\r\n        font-weight=\"bold\"\r\n        :fill=\"damageAnimation.color\"\r\n        opacity=\"0\"\r\n      >\r\n        {{ damageAnimation.text }}\r\n        <animate \r\n          attributeName=\"y\" \r\n          :values=\"`${-tokenRadius - 10};${-tokenRadius - 40}`\"\r\n          dur=\"1.5s\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0\"\r\n          dur=\"1.5s\"\r\n        />\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 拖拽手柄 (仅在可移动时显示) -->\r\n    <circle \r\n      v-if=\"canMove && selected\"\r\n      :r=\"tokenRadius\"\r\n      fill=\"transparent\"\r\n      stroke=\"none\"\r\n      style=\"cursor: move\"\r\n      @mousedown=\"startDrag\"\r\n    />\r\n  </g>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CharacterToken',\r\n  props: {\r\n    character: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    },\r\n    zoom: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    selected: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    currentTurn: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canMove: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canAct: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showMovementRange: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showAttackRange: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      tokenRadius: 20,\r\n      isDragging: false,\r\n      dragStart: null,\r\n      damageAnimation: null\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    x() {\r\n      return (this.character.position?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    y() {\r\n      return (this.character.position?.y || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    movementRange() {\r\n      // 根据角色移动力计算移动范围\r\n      return this.character.movement || 3\r\n    },\r\n    \r\n    attackRange() {\r\n      // 根据装备武器计算攻击范围\r\n      const weapon = this.character.equippedWeapon\r\n      if (!weapon) return 1.5 // 徒手攻击\r\n      \r\n      if (weapon.type === 'melee') {\r\n        return weapon.reach || 1.5\r\n      } else if (weapon.type === 'ranged') {\r\n        return Math.min(weapon.range?.base || 10, 10) // 限制显示范围\r\n      }\r\n      \r\n      return 1.5\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 获取默认头像\r\n     */\r\n    getDefaultAvatar() {\r\n      return this.character.isPlayer \r\n        ? '/images/default-player-avatar.png'\r\n        : '/images/default-enemy-avatar.png'\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值颜色\r\n     */\r\n    getHealthColor() {\r\n      const healthPercent = this.getHealthPercentage()\r\n      \r\n      if (healthPercent > 75) return '#4caf50'\r\n      if (healthPercent > 50) return '#ff9800'\r\n      if (healthPercent > 25) return '#f44336'\r\n      return '#9c27b0'\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值百分比\r\n     */\r\n    getHealthPercentage() {\r\n      const current = this.character.currentHP || this.character.hitPoints\r\n      const max = this.character.maxHP || this.character.hitPoints\r\n      return max > 0 ? (current / max) * 100 : 0\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值环的虚线数组\r\n     */\r\n    getHealthDashArray() {\r\n      const circumference = 2 * Math.PI * (this.tokenRadius + 2)\r\n      const healthPercent = this.getHealthPercentage() / 100\r\n      const healthLength = circumference * healthPercent\r\n      const gapLength = circumference - healthLength\r\n      \r\n      return `${healthLength} ${gapLength}`\r\n    },\r\n    \r\n    /**\r\n     * 获取状态效果图标\r\n     */\r\n    getConditionIcon(condition) {\r\n      const iconMap = {\r\n        'unconscious': '💤',\r\n        'dying': '💀',\r\n        'prone': '⬇️',\r\n        'stunned': '😵',\r\n        'restrained': '🔒',\r\n        'frightened': '😨',\r\n        'poisoned': '☠️',\r\n        'bleeding': '🩸',\r\n        'burning': '🔥',\r\n        'frozen': '❄️',\r\n        'paralyzed': '⚡',\r\n        'blinded': '👁️',\r\n        'deafened': '👂',\r\n        'charmed': '💖',\r\n        'confused': '❓'\r\n      }\r\n      \r\n      return iconMap[condition] || '?'\r\n    },\r\n    \r\n    /**\r\n     * 开始拖拽\r\n     */\r\n    startDrag(event) {\r\n      if (!this.canMove) return\r\n      \r\n      this.isDragging = true\r\n      this.dragStart = {\r\n        x: event.clientX,\r\n        y: event.clientY,\r\n        characterX: this.character.position.x,\r\n        characterY: this.character.position.y\r\n      }\r\n      \r\n      document.addEventListener('mousemove', this.handleDrag)\r\n      document.addEventListener('mouseup', this.endDrag)\r\n      \r\n      event.stopPropagation()\r\n    },\r\n    \r\n    /**\r\n     * 处理拖拽\r\n     */\r\n    handleDrag(event) {\r\n      if (!this.isDragging || !this.dragStart) return\r\n      \r\n      const deltaX = event.clientX - this.dragStart.x\r\n      const deltaY = event.clientY - this.dragStart.y\r\n      \r\n      const gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom))\r\n      const gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom))\r\n      \r\n      const newX = this.dragStart.characterX + gridDeltaX\r\n      const newY = this.dragStart.characterY + gridDeltaY\r\n      \r\n      // 检查移动范围\r\n      const distance = Math.sqrt(\r\n        Math.pow(newX - this.dragStart.characterX, 2) + \r\n        Math.pow(newY - this.dragStart.characterY, 2)\r\n      )\r\n      \r\n      if (distance <= this.movementRange) {\r\n        this.$emit('move', this.character, { x: newX, y: newY })\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 结束拖拽\r\n     */\r\n    endDrag() {\r\n      this.isDragging = false\r\n      this.dragStart = null\r\n      \r\n      document.removeEventListener('mousemove', this.handleDrag)\r\n      document.removeEventListener('mouseup', this.endDrag)\r\n    },\r\n    \r\n    /**\r\n     * 播放伤害动画\r\n     */\r\n    playDamageAnimation(damage, type = 'normal') {\r\n      const colors = {\r\n        normal: '#f44336',\r\n        critical: '#e91e63',\r\n        healing: '#4caf50',\r\n        miss: '#9e9e9e'\r\n      }\r\n      \r\n      const texts = {\r\n        normal: `-${damage}`,\r\n        critical: `CRIT! -${damage}`,\r\n        healing: `+${damage}`,\r\n        miss: 'MISS'\r\n      }\r\n      \r\n      this.damageAnimation = {\r\n        text: texts[type] || `-${damage}`,\r\n        color: colors[type] || '#f44336'\r\n      }\r\n      \r\n      // 1.5秒后清除动画\r\n      setTimeout(() => {\r\n        this.damageAnimation = null\r\n      }, 1500)\r\n    },\r\n    \r\n    /**\r\n     * 播放治疗动画\r\n     */\r\n    playHealingAnimation(healing) {\r\n      this.playDamageAnimation(healing, 'healing')\r\n    },\r\n    \r\n    /**\r\n     * 播放闪避动画\r\n     */\r\n    playMissAnimation() {\r\n      this.playDamageAnimation(0, 'miss')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.character-token {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.character-token:hover {\r\n  filter: brightness(1.1);\r\n}\r\n\r\n.character-token.selected {\r\n  filter: drop-shadow(0 0 8px rgba(52, 152, 219, 0.8));\r\n}\r\n\r\n.character-token.current-turn {\r\n  filter: drop-shadow(0 0 12px rgba(231, 76, 60, 0.9));\r\n}\r\n\r\n.character-token.can-move {\r\n  cursor: move;\r\n}\r\n\r\n.character-token.player .character-body circle:first-child {\r\n  filter: drop-shadow(0 2px 4px rgba(33, 150, 243, 0.3));\r\n}\r\n\r\n.character-token.enemy .character-body circle:first-child {\r\n  filter: drop-shadow(0 2px 4px rgba(244, 67, 54, 0.3));\r\n}\r\n\r\n.character-name-text {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.status-effects circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.action-indicators circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.damage-animation text {\r\n  font-family: 'Arial Black', sans-serif;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 0.8; }\r\n  50% { opacity: 0.3; }\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes damageFloat {\r\n  0% { \r\n    transform: translateY(0); \r\n    opacity: 0; \r\n  }\r\n  20% { \r\n    opacity: 1; \r\n  }\r\n  80% { \r\n    opacity: 1; \r\n  }\r\n  100% { \r\n    transform: translateY(-30px); \r\n    opacity: 0; \r\n  }\r\n}\r\n</style>"], "mappings": ";;;AA+OA,eAAe;EACbA,IAAI,EAAE,gBAAgB;EACtBC,KAAK,EAAE;IACLC,SAAS,EAAE;MACTC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;MACRH,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDC,IAAI,EAAE;MACJL,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDC,WAAW,EAAE;MACXR,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDE,OAAO,EAAE;MACPT,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDG,MAAM,EAAE;MACNV,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDI,iBAAiB,EAAE;MACjBX,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDK,eAAe,EAAE;MACfZ,IAAI,EAAEO,OAAO;MACb,WAAS;IACX;EACF,CAAC;EAEDM,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,IAAI;MACfC,eAAe,EAAE;IACnB;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,CAAC,WAADA,CAACA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MACF,OAAO,CAAC,EAAAA,qBAAA,OAAI,CAACrB,SAAS,CAACsB,QAAQ,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBD,CAAA,KAAK,CAAC,IAAI,IAAI,CAAChB,QAAO,GAAI,IAAI,CAACE,IAAG;IACrE,CAAC;IAEDiB,CAAC,WAADA,CAACA,CAAA,EAAG;MAAA,IAAAC,sBAAA;MACF,OAAO,CAAC,EAAAA,sBAAA,OAAI,CAACxB,SAAS,CAACsB,QAAQ,cAAAE,sBAAA,uBAAvBA,sBAAA,CAAyBD,CAAA,KAAK,CAAC,IAAI,IAAI,CAACnB,QAAO,GAAI,IAAI,CAACE,IAAG;IACrE,CAAC;IAEDmB,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd;MACA,OAAO,IAAI,CAACzB,SAAS,CAAC0B,QAAO,IAAK;IACpC,CAAC;IAEDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ;MACA,IAAMC,MAAK,GAAI,IAAI,CAAC5B,SAAS,CAAC6B,cAAa;MAC3C,IAAI,CAACD,MAAM,EAAE,OAAO,GAAE,EAAE;;MAExB,IAAIA,MAAM,CAAC3B,IAAG,KAAM,OAAO,EAAE;QAC3B,OAAO2B,MAAM,CAACE,KAAI,IAAK,GAAE;MAC3B,OAAO,IAAIF,MAAM,CAAC3B,IAAG,KAAM,QAAQ,EAAE;QAAA,IAAA8B,aAAA;QACnC,OAAOC,IAAI,CAACC,GAAG,CAAC,EAAAF,aAAA,GAAAH,MAAM,CAACM,KAAK,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,IAAG,KAAK,EAAE,EAAE,EAAE,GAAE;MAChD;MAEA,OAAO,GAAE;IACX;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;;;IAGAC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACrC,SAAS,CAACsC,QAAO,GACzB,mCAAkC,GAClC,kCAAiC;IACvC,CAAC;IAED;;;IAGAC,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAMC,aAAY,GAAI,IAAI,CAACC,mBAAmB,CAAC;MAE/C,IAAID,aAAY,GAAI,EAAE,EAAE,OAAO,SAAQ;MACvC,IAAIA,aAAY,GAAI,EAAE,EAAE,OAAO,SAAQ;MACvC,IAAIA,aAAY,GAAI,EAAE,EAAE,OAAO,SAAQ;MACvC,OAAO,SAAQ;IACjB,CAAC;IAED;;;IAGAC,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,IAAMC,OAAM,GAAI,IAAI,CAAC1C,SAAS,CAAC2C,SAAQ,IAAK,IAAI,CAAC3C,SAAS,CAAC4C,SAAQ;MACnE,IAAMC,GAAE,GAAI,IAAI,CAAC7C,SAAS,CAAC8C,KAAI,IAAK,IAAI,CAAC9C,SAAS,CAAC4C,SAAQ;MAC3D,OAAOC,GAAE,GAAI,IAAKH,OAAM,GAAIG,GAAG,GAAI,GAAE,GAAI;IAC3C,CAAC;IAED;;;IAGAE,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAMC,aAAY,GAAI,IAAIhB,IAAI,CAACiB,EAAC,IAAK,IAAI,CAAClC,WAAU,GAAI,CAAC;MACzD,IAAMyB,aAAY,GAAI,IAAI,CAACC,mBAAmB,CAAC,IAAI,GAAE;MACrD,IAAMS,YAAW,GAAIF,aAAY,GAAIR,aAAY;MACjD,IAAMW,SAAQ,GAAIH,aAAY,GAAIE,YAAW;MAE7C,UAAAE,MAAA,CAAUF,YAAY,OAAAE,MAAA,CAAID,SAAS;IACrC,CAAC;IAED;;;IAGAE,gBAAgB,WAAhBA,gBAAgBA,CAACC,SAAS,EAAE;MAC1B,IAAMC,OAAM,GAAI;QACd,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,GAAG;QAChB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,UAAU,EAAE;MACd;MAEA,OAAOA,OAAO,CAACD,SAAS,KAAK,GAAE;IACjC,CAAC;IAED;;;IAGAE,SAAS,WAATA,SAASA,CAACC,KAAK,EAAE;MACf,IAAI,CAAC,IAAI,CAAC/C,OAAO,EAAE;MAEnB,IAAI,CAACM,UAAS,GAAI,IAAG;MACrB,IAAI,CAACC,SAAQ,GAAI;QACfG,CAAC,EAAEqC,KAAK,CAACC,OAAO;QAChBnC,CAAC,EAAEkC,KAAK,CAACE,OAAO;QAChBC,UAAU,EAAE,IAAI,CAAC5D,SAAS,CAACsB,QAAQ,CAACF,CAAC;QACrCyC,UAAU,EAAE,IAAI,CAAC7D,SAAS,CAACsB,QAAQ,CAACC;MACtC;MAEAuC,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACC,UAAU;MACtDF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACE,OAAO;MAEjDR,KAAK,CAACS,eAAe,CAAC;IACxB,CAAC;IAED;;;IAGAF,UAAU,WAAVA,UAAUA,CAACP,KAAK,EAAE;MAChB,IAAI,CAAC,IAAI,CAACzC,UAAS,IAAK,CAAC,IAAI,CAACC,SAAS,EAAE;MAEzC,IAAMkD,MAAK,GAAIV,KAAK,CAACC,OAAM,GAAI,IAAI,CAACzC,SAAS,CAACG,CAAA;MAC9C,IAAMgD,MAAK,GAAIX,KAAK,CAACE,OAAM,GAAI,IAAI,CAAC1C,SAAS,CAACM,CAAA;MAE9C,IAAM8C,UAAS,GAAIrC,IAAI,CAACsC,KAAK,CAACH,MAAK,IAAK,IAAI,CAAC/D,QAAO,GAAI,IAAI,CAACE,IAAI,CAAC;MAClE,IAAMiE,UAAS,GAAIvC,IAAI,CAACsC,KAAK,CAACF,MAAK,IAAK,IAAI,CAAChE,QAAO,GAAI,IAAI,CAACE,IAAI,CAAC;MAElE,IAAMkE,IAAG,GAAI,IAAI,CAACvD,SAAS,CAAC2C,UAAS,GAAIS,UAAS;MAClD,IAAMI,IAAG,GAAI,IAAI,CAACxD,SAAS,CAAC4C,UAAS,GAAIU,UAAS;;MAElD;MACA,IAAMG,QAAO,GAAI1C,IAAI,CAAC2C,IAAI,CACxB3C,IAAI,CAAC4C,GAAG,CAACJ,IAAG,GAAI,IAAI,CAACvD,SAAS,CAAC2C,UAAU,EAAE,CAAC,IAC5C5B,IAAI,CAAC4C,GAAG,CAACH,IAAG,GAAI,IAAI,CAACxD,SAAS,CAAC4C,UAAU,EAAE,CAAC,CAC9C;MAEA,IAAIa,QAAO,IAAK,IAAI,CAACjD,aAAa,EAAE;QAClC,IAAI,CAACoD,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC7E,SAAS,EAAE;UAAEoB,CAAC,EAAEoD,IAAI;UAAEjD,CAAC,EAAEkD;QAAK,CAAC;MACzD;IACF,CAAC;IAED;;;IAGAR,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,IAAI,CAACjD,UAAS,GAAI,KAAI;MACtB,IAAI,CAACC,SAAQ,GAAI,IAAG;MAEpB6C,QAAQ,CAACgB,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACd,UAAU;MACzDF,QAAQ,CAACgB,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACb,OAAO;IACtD,CAAC;IAED;;;IAGAc,mBAAmB,WAAnBA,mBAAmBA,CAACC,MAAM,EAAmB;MAAA,IAAAC,KAAA;MAAA,IAAjBhF,IAAG,GAAAiF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAI,QAAQ;MACzC,IAAMG,MAAK,GAAI;QACbC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,SAAS;QAClBC,IAAI,EAAE;MACR;MAEA,IAAMC,KAAI,GAAI;QACZJ,MAAM,MAAAlC,MAAA,CAAM4B,MAAM,CAAE;QACpBO,QAAQ,YAAAnC,MAAA,CAAY4B,MAAM,CAAE;QAC5BQ,OAAO,MAAApC,MAAA,CAAM4B,MAAM,CAAE;QACrBS,IAAI,EAAE;MACR;MAEA,IAAI,CAACvE,eAAc,GAAI;QACrByE,IAAI,EAAED,KAAK,CAACzF,IAAI,SAAAmD,MAAA,CAAS4B,MAAM,CAAE;QACjCY,KAAK,EAAEP,MAAM,CAACpF,IAAI,KAAK;MACzB;;MAEA;MACA4F,UAAU,CAAC,YAAM;QACfZ,KAAI,CAAC/D,eAAc,GAAI,IAAG;MAC5B,CAAC,EAAE,IAAI;IACT,CAAC;IAED;;;IAGA4E,oBAAoB,WAApBA,oBAAoBA,CAACN,OAAO,EAAE;MAC5B,IAAI,CAACT,mBAAmB,CAACS,OAAO,EAAE,SAAS;IAC7C,CAAC;IAED;;;IAGAO,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAAChB,mBAAmB,CAAC,CAAC,EAAE,MAAM;IACpC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}