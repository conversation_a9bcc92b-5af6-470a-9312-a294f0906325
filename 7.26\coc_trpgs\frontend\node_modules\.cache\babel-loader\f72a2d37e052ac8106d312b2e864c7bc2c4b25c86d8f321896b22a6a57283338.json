{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _regenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _objectSpread from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nvar _excluded = [\"id\"];\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.find.js\";\nimport \"core-js/modules/es.array.find-index.js\";\nimport \"core-js/modules/es.array.for-each.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.array.unshift.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.date.to-iso-string.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.object.keys.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.parse-int.js\";\nimport \"core-js/modules/es.regexp.to-string.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/es.string.substr.js\";\nimport \"core-js/modules/web.atob.js\";\nimport \"core-js/modules/web.dom-collections.for-each.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport \"core-js/modules/web.dom-exception.constructor.js\";\nimport \"core-js/modules/web.dom-exception.stack.js\";\nimport \"core-js/modules/web.dom-exception.to-string-tag.js\";\nimport { createStore } from 'vuex';\nimport axios from 'axios';\nimport { emitter } from '../main.js';\nimport { v4 as uuidv4 } from 'uuid';\nimport apiService from '@/services/api';\nimport storageManager from '@/utils/storage';\nimport persistencePlugin from './plugins/persistencePlugin';\nimport auth from './modules/auth';\nimport rooms from './modules/rooms';\nvar API_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000';\n\n// 安全地从存储中获取数据的辅助函数\nfunction safeGetFromStorage(key) {\n  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  try {\n    return storageManager.getItem(key) || defaultValue;\n  } catch (error) {\n    console.warn(\"[Store] \\u83B7\\u53D6\\u5B58\\u50A8\\u9879 \".concat(key, \" \\u5931\\u8D25:\"), error);\n    return defaultValue;\n  }\n}\nfunction safeGetObjectFromStorage(key) {\n  var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n  try {\n    return storageManager.getObject(key) || defaultValue;\n  } catch (error) {\n    console.warn(\"[Store] \\u83B7\\u53D6\\u5B58\\u50A8\\u5BF9\\u8C61 \".concat(key, \" \\u5931\\u8D25:\"), error);\n    return defaultValue;\n  }\n}\nexport default createStore({\n  state: {\n    // 用户认证状态\n    user: safeGetObjectFromStorage('user', null),\n    token: safeGetFromStorage('token', ''),\n    isAuthenticated: false,\n    authLoading: false,\n    authError: null,\n    tokenExpiry: safeGetFromStorage('tokenExpiry', null),\n    // 角色数据\n    characters: safeGetObjectFromStorage('characters', []),\n    currentCharacterId: safeGetFromStorage('currentCharacterId', null),\n    messages: [],\n    // 应用设置\n    theme: storageManager.getItem('theme') || 'dark',\n    aiSettings: {\n      apiKey: '',\n      apiUrl: '',\n      modelName: '',\n      isApiValid: false\n    },\n    // 同步状态\n    characterSyncStatus: {},\n    localCharacterCache: safeGetObjectFromStorage('localCharacterCache', {}),\n    lastSyncTime: safeGetFromStorage('lastSyncTime', null),\n    // 场景和笔记\n    scenes: safeGetObjectFromStorage('scenes', []),\n    currentSceneIndex: parseInt(safeGetFromStorage('currentSceneIndex', '0')),\n    notes: safeGetObjectFromStorage('notes', {}),\n    // 系统状态\n    apiValid: true,\n    isOfflineMode: false,\n    connectionStatus: 'online',\n    storageStatus: null,\n    // 添加存储状态跟踪\n\n    // 游戏数据 (保留基本结构，供其他功能使用)\n    occupations: {},\n    skills: {},\n    weapons: {},\n    armors: {},\n    // 存档系统\n    saves: [],\n    currentSave: null,\n    loadingSaves: false,\n    saveError: null,\n    saveHistory: [],\n    loadingSaveHistory: false,\n    isPreviewMode: false,\n    globalNotice: null\n  },\n  getters: {\n    // 认证相关\n    isAuthenticated: function isAuthenticated(state) {\n      if (!state.token || !state.user) return false;\n\n      // 检查token是否过期\n      if (state.tokenExpiry) {\n        var now = new Date().getTime();\n        var expiry = new Date(state.tokenExpiry).getTime();\n        if (now >= expiry) {\n          return false;\n        }\n      }\n      return true;\n    },\n    currentUser: function currentUser(state) {\n      return state.user;\n    },\n    authLoading: function authLoading(state) {\n      return state.authLoading;\n    },\n    authError: function authError(state) {\n      return state.authError;\n    },\n    tokenValid: function tokenValid(state) {\n      if (!state.token) return false;\n      if (!state.tokenExpiry) return true; // 如果没有过期时间，假设有效\n\n      var now = new Date().getTime();\n      var expiry = new Date(state.tokenExpiry).getTime();\n      return now < expiry;\n    },\n    // 角色相关\n    userCharacters: function userCharacters(state) {\n      return state.characters;\n    },\n    currentCharacter: function currentCharacter(state) {\n      if (!state.currentCharacterId) return null;\n      return state.characters.find(function (c) {\n        return c.id == state.currentCharacterId;\n      }) || null;\n    },\n    characterSyncStatus: function characterSyncStatus(state) {\n      return function (characterId) {\n        return state.characterSyncStatus[characterId] || {\n          lastSync: null,\n          syncing: false\n        };\n      };\n    },\n    getLocalCharacterData: function getLocalCharacterData(state) {\n      return function (characterId) {\n        return state.localCharacterCache[characterId] || null;\n      };\n    },\n    // 消息相关\n    roomMessages: function roomMessages(state) {\n      return state.messages;\n    },\n    // 场景和笔记\n    allScenes: function allScenes(state) {\n      return state.scenes;\n    },\n    currentScene: function currentScene(state) {\n      return state.scenes[state.currentSceneIndex] || null;\n    },\n    characterNotes: function characterNotes(state) {\n      return function (characterId) {\n        return state.notes[characterId] || [];\n      };\n    },\n    globalNotes: function globalNotes(state) {\n      return state.notes['global'] || [];\n    },\n    // 系统状态\n    currentTheme: function currentTheme(state) {\n      return state.theme;\n    },\n    isDarkTheme: function isDarkTheme(state) {\n      return state.theme === 'dark';\n    },\n    isOfflineMode: function isOfflineMode(state) {\n      return state.isOfflineMode;\n    },\n    connectionStatus: function connectionStatus(state) {\n      return state.connectionStatus;\n    },\n    lastSyncTime: function lastSyncTime(state) {\n      return state.lastSyncTime;\n    },\n    storageStatus: function storageStatus(state) {\n      return state.storageStatus;\n    },\n    isStorageDegraded: function isStorageDegraded(state) {\n      var _state$storageStatus;\n      return ((_state$storageStatus = state.storageStatus) === null || _state$storageStatus === void 0 ? void 0 : _state$storageStatus.isDegraded) || false;\n    },\n    // 存档相关\n    gameSaves: function gameSaves(state) {\n      return state.saves;\n    },\n    currentGameSave: function currentGameSave(state) {\n      return state.currentSave;\n    },\n    isLoadingSaves: function isLoadingSaves(state) {\n      return state.loadingSaves;\n    },\n    saveError: function saveError(state) {\n      return state.saveError;\n    },\n    saveHistory: function saveHistory(state) {\n      return state.saveHistory;\n    },\n    isLoadingSaveHistory: function isLoadingSaveHistory(state) {\n      return state.loadingSaveHistory;\n    },\n    isPreviewMode: function isPreviewMode(state) {\n      return state.isPreviewMode;\n    },\n    globalNotice: function globalNotice(state) {\n      return state.globalNotice;\n    }\n  },\n  mutations: {\n    // 认证相关mutations\n    SET_USER: function SET_USER(state, user) {\n      state.user = user;\n      state.isAuthenticated = !!user;\n      try {\n        if (user) {\n          storageManager.setObject('user', user);\n        } else {\n          storageManager.removeItem('user');\n        }\n      } catch (error) {\n        console.warn('[Store] 保存用户信息失败:', error);\n      }\n    },\n    SET_TOKEN: function SET_TOKEN(state, token) {\n      state.token = token;\n      try {\n        if (token) {\n          storageManager.setItem('token', token);\n\n          // 解析JWT token获取过期时间\n          try {\n            var payload = JSON.parse(atob(token.split('.')[1]));\n            if (payload.exp) {\n              var expiry = new Date(payload.exp * 1000).toISOString();\n              state.tokenExpiry = expiry;\n              storageManager.setItem('tokenExpiry', expiry);\n            }\n          } catch (e) {\n            console.warn('无法解析token过期时间:', e);\n          }\n        } else {\n          storageManager.removeItem('token');\n          storageManager.removeItem('tokenExpiry');\n          state.tokenExpiry = null;\n        }\n      } catch (error) {\n        console.warn('[Store] 保存token失败:', error);\n      }\n    },\n    SET_AUTH_LOADING: function SET_AUTH_LOADING(state, loading) {\n      state.authLoading = loading;\n    },\n    SET_AUTH_ERROR: function SET_AUTH_ERROR(state, error) {\n      state.authError = error;\n    },\n    CLEAR_AUTH: function CLEAR_AUTH(state) {\n      state.user = null;\n      state.token = '';\n      state.tokenExpiry = null;\n      state.isAuthenticated = false;\n      state.authError = null;\n\n      // 清除本地存储\n      try {\n        storageManager.removeItem('user');\n        storageManager.removeItem('token');\n        storageManager.removeItem('tokenExpiry');\n      } catch (error) {\n        console.warn('[Store] 清除认证信息失败:', error);\n      }\n    },\n    SET_CONNECTION_STATUS: function SET_CONNECTION_STATUS(state, status) {\n      state.connectionStatus = status;\n      if (status === 'offline') {\n        state.isOfflineMode = true;\n      }\n    },\n    SET_LAST_SYNC_TIME: function SET_LAST_SYNC_TIME(state, time) {\n      state.lastSyncTime = time;\n      try {\n        storageManager.setItem('lastSyncTime', time);\n      } catch (error) {\n        console.warn('[Store] 保存同步时间失败:', error);\n      }\n    },\n    SET_STORAGE_STATUS: function SET_STORAGE_STATUS(state, status) {\n      state.storageStatus = status;\n    },\n    UPDATE_USER_AVATAR: function UPDATE_USER_AVATAR(state, avatarUrl) {\n      if (state.user) {\n        state.user.avatar_url = avatarUrl;\n        // 同时更新存储中的用户信息\n        storageManager.setObject('user', state.user);\n      }\n    },\n    SET_CHARACTERS: function SET_CHARACTERS(state, characters) {\n      state.characters = characters;\n      try {\n        storageManager.setObject('characters', characters);\n      } catch (error) {\n        console.warn('[Store] 保存角色列表失败:', error);\n      }\n    },\n    ADD_CHARACTER: function ADD_CHARACTER(state, character) {\n      state.characters.push(character);\n      try {\n        storageManager.setObject('characters', state.characters);\n      } catch (error) {\n        console.warn('[Store] 保存新角色失败:', error);\n      }\n    },\n    UPDATE_CHARACTER: function UPDATE_CHARACTER(state, updatedCharacter) {\n      var index = state.characters.findIndex(function (c) {\n        return c.id === updatedCharacter.id;\n      });\n      if (index !== -1) {\n        state.characters.splice(index, 1, updatedCharacter);\n        try {\n          storageManager.setObject('characters', state.characters);\n          state.localCharacterCache[updatedCharacter.id] = _objectSpread(_objectSpread({}, updatedCharacter), {}, {\n            lastUpdated: new Date().toISOString()\n          });\n          storageManager.setObject('localCharacterCache', state.localCharacterCache);\n        } catch (error) {\n          console.warn('[Store] 保存角色更新失败:', error);\n        }\n        state.characterSyncStatus[updatedCharacter.id] = {\n          lastSync: new Date().toISOString(),\n          syncing: false\n        };\n      }\n    },\n    UPDATE_CHARACTER_FROM_SERVER: function UPDATE_CHARACTER_FROM_SERVER(state, characterData) {\n      var index = state.characters.findIndex(function (c) {\n        return c.id === characterData.id;\n      });\n      if (index !== -1) {\n        var mergedCharacter = _objectSpread(_objectSpread(_objectSpread({}, state.characters[index]), characterData), {}, {\n          _localData: state.characters[index]._localData || {}\n        });\n        state.characters.splice(index, 1, mergedCharacter);\n        storageManager.setObject('characters', state.characters);\n        state.localCharacterCache[characterData.id] = _objectSpread(_objectSpread({}, mergedCharacter), {}, {\n          lastUpdated: new Date().toISOString()\n        });\n        storageManager.setObject('localCharacterCache', state.localCharacterCache);\n        state.characterSyncStatus[characterData.id] = {\n          lastSync: new Date().toISOString(),\n          syncing: false\n        };\n      }\n    },\n    UPDATE_CHARACTER_ATTRIBUTE: function UPDATE_CHARACTER_ATTRIBUTE(state, _ref) {\n      var id = _ref.id,\n        field = _ref.field,\n        value = _ref.value;\n      var index = state.characters.findIndex(function (c) {\n        return c.id === id;\n      });\n      if (index !== -1) {\n        var updatedCharacter = _objectSpread({}, state.characters[index]);\n        if (field.includes('.')) {\n          var parts = field.split('.');\n          var obj = updatedCharacter;\n          for (var i = 0; i < parts.length - 1; i++) {\n            if (!obj[parts[i]]) obj[parts[i]] = {};\n            obj = obj[parts[i]];\n          }\n          obj[parts[parts.length - 1]] = value;\n        } else {\n          updatedCharacter[field] = value;\n        }\n        state.characters.splice(index, 1, updatedCharacter);\n        storageManager.setObject('characters', state.characters);\n        state.localCharacterCache[id] = _objectSpread(_objectSpread({}, updatedCharacter), {}, {\n          lastUpdated: new Date().toISOString()\n        });\n        storageManager.setObject('localCharacterCache', state.localCharacterCache);\n      }\n    },\n    SET_CURRENT_CHARACTER: function SET_CURRENT_CHARACTER(state, characterId) {\n      state.currentCharacterId = characterId;\n      storageManager.setItem('currentCharacterId', characterId);\n    },\n    DELETE_CHARACTER: function DELETE_CHARACTER(state, characterId) {\n      state.characters = state.characters.filter(function (c) {\n        return c.id !== characterId;\n      });\n      storageManager.setObject('characters', state.characters);\n      if (state.localCharacterCache[characterId]) {\n        delete state.localCharacterCache[characterId];\n        storageManager.setObject('localCharacterCache', state.localCharacterCache);\n      }\n      if (state.characterSyncStatus[characterId]) {\n        delete state.characterSyncStatus[characterId];\n      }\n    },\n    UPDATE_CHARACTER_SYNC_STATUS: function UPDATE_CHARACTER_SYNC_STATUS(state, _ref2) {\n      var characterId = _ref2.characterId,\n        status = _ref2.status;\n      state.characterSyncStatus[characterId] = _objectSpread(_objectSpread({}, state.characterSyncStatus[characterId]), status);\n    },\n    SET_ROOMS: function SET_ROOMS(state, rooms) {\n      state.rooms = rooms;\n    },\n    SET_CURRENT_ROOM: function SET_CURRENT_ROOM(state, roomData) {\n      state.currentRoom = roomData;\n    },\n    CLEAR_CURRENT_ROOM: function CLEAR_CURRENT_ROOM(state) {\n      state.currentRoom = null;\n      state.messages = [];\n    },\n    ADD_MESSAGE: function ADD_MESSAGE(state, message) {\n      console.log('添加消息到store:', message);\n      if (!message.timestamp) {\n        message.timestamp = new Date().toISOString();\n      }\n      if (message.type === 'chat' && !message.username) {\n        message.username = message.user_id === 1 ? 'KP' : '玩家';\n      }\n      message.id = Date.now() + Math.random().toString(36).substr(2, 9);\n      state.messages.push(message);\n      if (state.messages.length > 1000) {\n        state.messages = state.messages.slice(-500);\n      }\n      var roomId = state.currentRoom ? state.currentRoom.id : 'default';\n      var roomMessages = storageManager.getObject(\"room_messages_\".concat(roomId)) || [];\n      roomMessages.push(message);\n      if (roomMessages.length > 200) {\n        roomMessages.splice(0, roomMessages.length - 200);\n      }\n      storageManager.setObject(\"room_messages_\".concat(roomId), roomMessages);\n    },\n    SET_MESSAGES: function SET_MESSAGES(state, messages) {\n      state.messages = messages;\n      var roomId = state.currentRoom ? state.currentRoom.id : 'default';\n      storageManager.setObject(\"room_messages_\".concat(roomId), messages.slice(-200));\n    },\n    TOGGLE_THEME: function TOGGLE_THEME(state) {\n      state.theme = state.theme === 'dark' ? 'light' : 'dark';\n      storageManager.setItem('theme', state.theme);\n    },\n    CLEAR_MESSAGES: function CLEAR_MESSAGES(state) {\n      state.messages = [];\n      var roomId = state.currentRoom ? state.currentRoom.id : 'default';\n      storageManager.removeItem(\"room_messages_\".concat(roomId));\n    },\n    SET_IMPORTANT_MESSAGE: function SET_IMPORTANT_MESSAGE(state, message) {\n      state.importantMessage = message;\n    },\n    SET_API_VALID: function SET_API_VALID(state, isValid) {\n      state.aiSettings.isApiValid = isValid;\n    },\n    UPDATE_AI_SETTINGS: function UPDATE_AI_SETTINGS(state, settings) {\n      state.aiSettings = _objectSpread(_objectSpread({}, state.aiSettings), settings);\n    },\n    UPDATE_MESSAGE: function UPDATE_MESSAGE(state, _ref3) {\n      var index = _ref3.index,\n        message = _ref3.message;\n      if (index >= 0 && index < state.messages.length) {\n        state.messages.splice(index, 1, message);\n        var roomId = state.currentRoom ? state.currentRoom.id : 'default';\n        var roomMessages = storageManager.getObject(\"room_messages_\".concat(roomId)) || [];\n        var msgIndex = roomMessages.findIndex(function (m) {\n          return m.id === message.id;\n        });\n        if (msgIndex !== -1) {\n          roomMessages.splice(msgIndex, 1, message);\n          storageManager.setObject(\"room_messages_\".concat(roomId), roomMessages);\n        }\n      }\n    },\n    REMOVE_LAST_MESSAGE: function REMOVE_LAST_MESSAGE(state) {\n      if (state.messages.length > 0) {\n        state.messages.pop();\n        var roomId = state.currentRoom ? state.currentRoom.id : 'default';\n        var roomMessages = storageManager.getObject(\"room_messages_\".concat(roomId)) || [];\n        if (roomMessages.length > 0) {\n          roomMessages.pop();\n          storageManager.setObject(\"room_messages_\".concat(roomId), roomMessages);\n        }\n      }\n    },\n    SET_SCENES: function SET_SCENES(state, scenes) {\n      state.scenes = scenes;\n      storageManager.setObject('scenes', scenes);\n    },\n    ADD_SCENE: function ADD_SCENE(state, scene) {\n      state.scenes.push(scene);\n      storageManager.setObject('scenes', state.scenes);\n    },\n    UPDATE_SCENE: function UPDATE_SCENE(state, _ref4) {\n      var index = _ref4.index,\n        scene = _ref4.scene;\n      if (index >= 0 && index < state.scenes.length) {\n        state.scenes.splice(index, 1, scene);\n        storageManager.setObject('scenes', state.scenes);\n      }\n    },\n    DELETE_SCENE: function DELETE_SCENE(state, index) {\n      if (index >= 0 && index < state.scenes.length) {\n        state.scenes.splice(index, 1);\n        storageManager.setObject('scenes', state.scenes);\n        if (index <= state.currentSceneIndex && state.currentSceneIndex > 0) {\n          state.currentSceneIndex--;\n          storageManager.setItem('currentSceneIndex', state.currentSceneIndex.toString());\n        }\n      }\n    },\n    SET_CURRENT_SCENE_INDEX: function SET_CURRENT_SCENE_INDEX(state, index) {\n      if (index >= 0 && index < state.scenes.length) {\n        state.currentSceneIndex = index;\n        storageManager.setItem('currentSceneIndex', index.toString());\n      }\n    },\n    SET_NOTES: function SET_NOTES(state, _ref5) {\n      var characterId = _ref5.characterId,\n        notes = _ref5.notes;\n      var id = characterId || 'global';\n      state.notes[id] = notes;\n      storageManager.setObject('notes', state.notes);\n    },\n    ADD_NOTE: function ADD_NOTE(state, _ref6) {\n      var characterId = _ref6.characterId,\n        note = _ref6.note;\n      var id = characterId || 'global';\n      if (!state.notes[id]) {\n        state.notes[id] = [];\n      }\n      state.notes[id].unshift(note);\n      storageManager.setObject('notes', state.notes);\n    },\n    UPDATE_NOTE: function UPDATE_NOTE(state, _ref7) {\n      var characterId = _ref7.characterId,\n        index = _ref7.index,\n        note = _ref7.note;\n      var id = characterId || 'global';\n      if (state.notes[id] && index >= 0 && index < state.notes[id].length) {\n        state.notes[id].splice(index, 1, note);\n        storageManager.setObject('notes', state.notes);\n      }\n    },\n    DELETE_NOTE: function DELETE_NOTE(state, _ref8) {\n      var characterId = _ref8.characterId,\n        index = _ref8.index;\n      var id = characterId || 'global';\n      if (state.notes[id] && index >= 0 && index < state.notes[id].length) {\n        state.notes[id].splice(index, 1);\n        storageManager.setObject('notes', state.notes);\n      }\n    },\n    SET_OFFLINE_MODE: function SET_OFFLINE_MODE(state, isOffline) {\n      state.isOfflineMode = isOffline;\n    },\n    SET_SAVES: function SET_SAVES(state, saves) {\n      state.saves = saves;\n    },\n    SET_CURRENT_SAVE: function SET_CURRENT_SAVE(state, save) {\n      state.currentSave = save;\n    },\n    ADD_SAVE: function ADD_SAVE(state, save) {\n      state.saves.push(save);\n    },\n    UPDATE_SAVE: function UPDATE_SAVE(state, updatedSave) {\n      var index = state.saves.findIndex(function (save) {\n        return save.id === updatedSave.id;\n      });\n      if (index !== -1) {\n        state.saves[index] = updatedSave;\n      }\n    },\n    REMOVE_SAVE: function REMOVE_SAVE(state, saveId) {\n      state.saves = state.saves.filter(function (save) {\n        return save.id !== saveId;\n      });\n    },\n    SET_LOADING_SAVES: function SET_LOADING_SAVES(state, isLoading) {\n      state.loadingSaves = isLoading;\n    },\n    SET_SAVE_ERROR: function SET_SAVE_ERROR(state, error) {\n      state.saveError = error;\n    },\n    SET_SAVE_HISTORY: function SET_SAVE_HISTORY(state, history) {\n      state.saveHistory = history;\n    },\n    SET_LOADING_SAVE_HISTORY: function SET_LOADING_SAVE_HISTORY(state, isLoading) {\n      state.loadingSaveHistory = isLoading;\n    },\n    SET_PREVIEW_MODE: function SET_PREVIEW_MODE(state, isPreview) {\n      state.isPreviewMode = isPreview;\n    },\n    SET_GLOBAL_NOTICE: function SET_GLOBAL_NOTICE(state, notice) {\n      state.globalNotice = notice;\n    },\n    CLEAR_GLOBAL_NOTICE: function CLEAR_GLOBAL_NOTICE(state) {\n      state.globalNotice = null;\n    },\n    // 游戏数据mutations\n    SET_OCCUPATIONS: function SET_OCCUPATIONS(state, occupations) {\n      state.occupations = occupations || {};\n    },\n    SET_SKILLS: function SET_SKILLS(state, skills) {\n      state.skills = skills || {};\n    },\n    SET_WEAPONS: function SET_WEAPONS(state, weapons) {\n      state.weapons = weapons || {};\n    },\n    SET_ARMORS: function SET_ARMORS(state, armors) {\n      state.armors = armors || {};\n    },\n    // 更新当前房间的名称\n    UPDATE_ROOM_NAME: function UPDATE_ROOM_NAME(state, _ref9) {\n      var roomId = _ref9.roomId,\n        name = _ref9.name;\n      if (state.currentRoom && state.currentRoom.id === roomId) {\n        state.currentRoom.name = name;\n      }\n    },\n    // 更新房间列表中的房间名称\n    UPDATE_ROOM_LIST_NAME: function UPDATE_ROOM_LIST_NAME(state, _ref0) {\n      var roomId = _ref0.roomId,\n        name = _ref0.name;\n      if (state.rooms && state.rooms.length > 0) {\n        var roomIndex = state.rooms.findIndex(function (room) {\n          return room.id === roomId;\n        });\n        if (roomIndex !== -1) {\n          state.rooms[roomIndex].name = name;\n        }\n      }\n    }\n  },\n  actions: {\n    register: function register(_ref1, userData) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n        var commit, dispatch, response, _response$data, token, user, errorMessage, errorDetail, _t;\n        return _regenerator().w(function (_context) {\n          while (1) switch (_context.n) {\n            case 0:\n              commit = _ref1.commit, dispatch = _ref1.dispatch;\n              commit('SET_AUTH_LOADING', true);\n              commit('SET_AUTH_ERROR', null);\n              _context.p = 1;\n              console.log('🚀 开始注册用户:', userData.email);\n\n              // 验证输入数据\n              if (!(!userData.username || !userData.email || !userData.password)) {\n                _context.n = 2;\n                break;\n              }\n              throw new Error('请填写完整的注册信息');\n            case 2:\n              _context.n = 3;\n              return axios.post('/api/users/register', userData);\n            case 3:\n              response = _context.v;\n              console.log('✅ API注册成功:', response.data);\n              _response$data = response.data, token = _response$data.token, user = _response$data.user;\n              if (!(!token || !user)) {\n                _context.n = 4;\n                break;\n              }\n              throw new Error('服务器返回数据格式错误');\n            case 4:\n              // 更新状态\n              commit('SET_TOKEN', token);\n              commit('SET_USER', user);\n              commit('SET_CONNECTION_STATUS', 'online');\n              commit('SET_LAST_SYNC_TIME', new Date().toISOString());\n              console.log('✅ 注册成功，用户ID:', user.id);\n              return _context.a(2, user);\n            case 5:\n              _context.p = 5;\n              _t = _context.v;\n              console.error('❌ 注册失败:', _t);\n              errorMessage = '注册失败，请稍后重试';\n              if (_t.response && _t.response.data) {\n                errorDetail = _t.response.data.detail;\n                if (errorDetail === \"邮箱已被注册\") {\n                  errorMessage = '邮箱已被注册';\n                } else if (errorDetail === \"用户名已被注册\") {\n                  errorMessage = '用户名已被注册';\n                } else if (errorDetail) {\n                  errorMessage = errorDetail;\n                }\n              } else if (_t.code === 'NETWORK_ERROR' || !_t.response) {\n                errorMessage = '网络连接失败，请检查网络连接';\n                commit('SET_CONNECTION_STATUS', 'offline');\n              } else if (_t.message) {\n                errorMessage = _t.message;\n              }\n              commit('SET_AUTH_ERROR', errorMessage);\n              throw new Error(errorMessage);\n            case 6:\n              _context.p = 6;\n              commit('SET_AUTH_LOADING', false);\n              return _context.f(6);\n            case 7:\n              return _context.a(2);\n          }\n        }, _callee, null, [[1, 5, 6, 7]]);\n      }))();\n    },\n    login: function login(_ref10, credentials) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var commit, dispatch, response, _response$data2, token, user, errorMessage, errorDetail, _t2, _t3;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              commit = _ref10.commit, dispatch = _ref10.dispatch;\n              commit('SET_AUTH_LOADING', true);\n              commit('SET_AUTH_ERROR', null);\n              _context2.p = 1;\n              console.log('🚀 开始用户登录:', credentials.email);\n\n              // 验证输入数据\n              if (!(!credentials.email || !credentials.password)) {\n                _context2.n = 2;\n                break;\n              }\n              throw new Error('邮箱和密码不能为空');\n            case 2:\n              _context2.n = 3;\n              return axios.post('/api/users/login', credentials);\n            case 3:\n              response = _context2.v;\n              console.log('✅ API登录成功:', response.data);\n              _response$data2 = response.data, token = _response$data2.token, user = _response$data2.user;\n              if (!(!token || !user)) {\n                _context2.n = 4;\n                break;\n              }\n              throw new Error('服务器返回数据格式错误');\n            case 4:\n              // 更新状态\n              commit('SET_TOKEN', token);\n              commit('SET_USER', user);\n              commit('SET_CONNECTION_STATUS', 'online');\n              commit('SET_LAST_SYNC_TIME', new Date().toISOString());\n\n              // 登录成功后，尝试加载用户数据\n              _context2.p = 5;\n              _context2.n = 6;\n              return dispatch('fetchCharacters');\n            case 6:\n              _context2.n = 8;\n              break;\n            case 7:\n              _context2.p = 7;\n              _t2 = _context2.v;\n              console.warn('获取角色数据失败:', _t2);\n              // 不阻止登录流程\n            case 8:\n              console.log('✅ 登录成功，用户ID:', user.id);\n              return _context2.a(2, user);\n            case 9:\n              _context2.p = 9;\n              _t3 = _context2.v;\n              console.error('❌ 登录失败:', _t3);\n              errorMessage = '登录失败，请稍后重试';\n              if (_t3.response && _t3.response.data) {\n                errorDetail = _t3.response.data.detail;\n                if (errorDetail === '邮箱或密码错误') {\n                  errorMessage = '邮箱或密码错误';\n                } else if (errorDetail === '账户已被禁用') {\n                  errorMessage = '您的账户已被禁用，请联系管理员';\n                } else if (errorDetail) {\n                  errorMessage = errorDetail;\n                }\n              } else if (_t3.code === 'NETWORK_ERROR' || !_t3.response) {\n                errorMessage = '网络连接失败，请检查网络连接';\n                commit('SET_CONNECTION_STATUS', 'offline');\n              } else if (_t3.message) {\n                errorMessage = _t3.message;\n              }\n              commit('SET_AUTH_ERROR', errorMessage);\n              throw new Error(errorMessage);\n            case 10:\n              _context2.p = 10;\n              commit('SET_AUTH_LOADING', false);\n              return _context2.f(10);\n            case 11:\n              return _context2.a(2);\n          }\n        }, _callee2, null, [[5, 7], [1, 9, 10, 11]]);\n      }))();\n    },\n    logout: function logout(_ref11) {\n      var commit = _ref11.commit;\n      console.log('🚪 用户登出');\n      commit('CLEAR_AUTH');\n\n      // 清除所有相关的本地数据\n      storageManager.removeItem('characters');\n      storageManager.removeItem('currentCharacterId');\n      storageManager.removeItem('localCharacterCache');\n      storageManager.removeItem('lastSyncTime');\n\n      // 重置其他状态\n      commit('SET_CHARACTERS', []);\n      commit('SET_CURRENT_CHARACTER', null);\n      commit('CLEAR_CURRENT_ROOM');\n    },\n    // 验证token有效性\n    validateToken: function validateToken(_ref12) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        var commit, state, dispatch, now, expiry, response, _t4;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              commit = _ref12.commit, state = _ref12.state, dispatch = _ref12.dispatch;\n              if (state.token) {\n                _context3.n = 1;\n                break;\n              }\n              return _context3.a(2, false);\n            case 1:\n              _context3.p = 1;\n              if (!state.tokenExpiry) {\n                _context3.n = 2;\n                break;\n              }\n              now = new Date().getTime();\n              expiry = new Date(state.tokenExpiry).getTime();\n              if (!(now >= expiry)) {\n                _context3.n = 2;\n                break;\n              }\n              console.log('🔒 Token已过期，自动登出');\n              dispatch('logout');\n              return _context3.a(2, false);\n            case 2:\n              _context3.n = 3;\n              return axios.get('/api/users/me', {\n                headers: {\n                  Authorization: \"Bearer \".concat(state.token)\n                }\n              });\n            case 3:\n              response = _context3.v;\n              if (!response.data) {\n                _context3.n = 4;\n                break;\n              }\n              commit('SET_USER', response.data);\n              commit('SET_CONNECTION_STATUS', 'online');\n              return _context3.a(2, true);\n            case 4:\n              return _context3.a(2, false);\n            case 5:\n              _context3.p = 5;\n              _t4 = _context3.v;\n              console.error('❌ Token验证失败:', _t4);\n              if (_t4.response && _t4.response.status === 401) {\n                // Token无效，自动登出\n                dispatch('logout');\n              } else {\n                // 网络错误，设置离线模式\n                commit('SET_CONNECTION_STATUS', 'offline');\n              }\n              return _context3.a(2, false);\n          }\n        }, _callee3, null, [[1, 5]]);\n      }))();\n    },\n    // 初始化存储系统\n    initializeStorage: function initializeStorage(_ref13) {\n      var commit = _ref13.commit;\n      try {\n        // 获取存储状态\n        var storageStatus = storageManager.getStatus();\n        commit('SET_STORAGE_STATUS', storageStatus);\n        console.log('[Store] 存储系统初始化完成:', storageStatus);\n\n        // 如果存储降级，显示提示\n        if (storageStatus.isDegraded) {\n          console.warn('[Store] 存储系统处于降级模式:', storageStatus.storageType);\n        }\n        return true;\n      } catch (error) {\n        console.error('[Store] 存储系统初始化失败:', error);\n        return false;\n      }\n    },\n    // 初始化应用状态\n    initializeApp: function initializeApp(_ref14) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        var commit, dispatch, state, isValid, _t5, _t6;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              commit = _ref14.commit, dispatch = _ref14.dispatch, state = _ref14.state;\n              console.log('🚀 初始化应用状态');\n              _context4.p = 1;\n              // 首先初始化存储系统\n              dispatch('initializeStorage');\n\n              // 检查是否有保存的认证信息\n              if (!(state.token && state.user)) {\n                _context4.n = 7;\n                break;\n              }\n              console.log('📱 发现本地认证信息，验证token');\n              _context4.n = 2;\n              return dispatch('validateToken');\n            case 2:\n              isValid = _context4.v;\n              if (!isValid) {\n                _context4.n = 7;\n                break;\n              }\n              console.log('✅ Token有效，自动登录成功');\n\n              // 尝试加载用户数据\n              _context4.p = 3;\n              _context4.n = 4;\n              return dispatch('fetchCharacters');\n            case 4:\n              _context4.n = 6;\n              break;\n            case 5:\n              _context4.p = 5;\n              _t5 = _context4.v;\n              console.warn('获取角色数据失败:', _t5);\n            case 6:\n              return _context4.a(2, true);\n            case 7:\n              console.log('❌ 无有效认证信息');\n              return _context4.a(2, false);\n            case 8:\n              _context4.p = 8;\n              _t6 = _context4.v;\n              console.error('❌ 应用初始化失败:', _t6);\n              commit('SET_CONNECTION_STATUS', 'offline');\n              return _context4.a(2, false);\n          }\n        }, _callee4, null, [[3, 5], [1, 8]]);\n      }))();\n    },\n    // 刷新用户数据\n    refreshUserData: function refreshUserData(_ref15) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var commit, dispatch, state, isValid, _t7;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              commit = _ref15.commit, dispatch = _ref15.dispatch, state = _ref15.state;\n              if (state.token) {\n                _context5.n = 1;\n                break;\n              }\n              return _context5.a(2, false);\n            case 1:\n              _context5.p = 1;\n              _context5.n = 2;\n              return dispatch('validateToken');\n            case 2:\n              isValid = _context5.v;\n              if (!isValid) {\n                _context5.n = 4;\n                break;\n              }\n              _context5.n = 3;\n              return dispatch('fetchCharacters');\n            case 3:\n              commit('SET_LAST_SYNC_TIME', new Date().toISOString());\n              return _context5.a(2, true);\n            case 4:\n              return _context5.a(2, false);\n            case 5:\n              _context5.p = 5;\n              _t7 = _context5.v;\n              console.error('刷新用户数据失败:', _t7);\n              return _context5.a(2, false);\n          }\n        }, _callee5, null, [[1, 5]]);\n      }))();\n    },\n    fetchCharacters: function fetchCharacters(_ref16) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n        var commit, state, _state$user, response, characters, _error$response, _t8;\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              commit = _ref16.commit, state = _ref16.state;\n              _context6.p = 1;\n              console.log('🔍 开始获取角色列表，用户ID:', (_state$user = state.user) === null || _state$user === void 0 ? void 0 : _state$user.id);\n              if (!(!state.user || !state.user.id)) {\n                _context6.n = 2;\n                break;\n              }\n              throw new Error('用户未登录或用户ID不存在');\n            case 2:\n              _context6.n = 3;\n              return apiService.characters.getUserCharacters(state.user.id);\n            case 3:\n              response = _context6.v;\n              console.log('✅ 角色列表获取成功:', response.data);\n\n              // 后端返回格式: {success: true, data: [角色数组]}\n              characters = response.data.data || response.data;\n              console.log('📋 解析后的角色数据:', characters);\n              commit('SET_CHARACTERS', characters);\n              return _context6.a(2, characters);\n            case 4:\n              _context6.p = 4;\n              _t8 = _context6.v;\n              console.error('❌ 获取角色列表失败:', _t8);\n              console.error('错误详情:', ((_error$response = _t8.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || _t8.message);\n              throw _t8;\n            case 5:\n              return _context6.a(2);\n          }\n        }, _callee6, null, [[1, 4]]);\n      }))();\n    },\n    updateCharacter: function updateCharacter(_ref17, _ref18) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {\n        var commit, state, id, characterData, response, _t9;\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.n) {\n            case 0:\n              commit = _ref17.commit, state = _ref17.state;\n              id = _ref18.id, characterData = _objectWithoutProperties(_ref18, _excluded);\n              _context7.p = 1;\n              _context7.n = 2;\n              return axios.put(\"/api/characters/\".concat(id), characterData, {\n                headers: {\n                  Authorization: \"Bearer \".concat(state.token)\n                }\n              });\n            case 2:\n              response = _context7.v;\n              commit('UPDATE_CHARACTER', response.data);\n              import('@/services/websocket').then(function (module) {\n                var websocketService = module[\"default\"];\n                if (websocketService.isConnected) {\n                  var updatedCharacter = state.characters.find(function (c) {\n                    return c.id === id;\n                  });\n                  if (updatedCharacter) {\n                    websocketService.sendCharacterUpdate(updatedCharacter);\n                  }\n                }\n              });\n              return _context7.a(2, response.data);\n            case 3:\n              _context7.p = 3;\n              _t9 = _context7.v;\n              throw _t9;\n            case 4:\n              return _context7.a(2);\n          }\n        }, _callee7, null, [[1, 3]]);\n      }))();\n    },\n    deleteCharacter: function deleteCharacter(_ref19, characterId) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        var commit, state, response, _error$response2, _t0;\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              commit = _ref19.commit, state = _ref19.state;\n              _context8.p = 1;\n              console.log('🗑️ 删除角色:', characterId);\n              _context8.n = 2;\n              return apiService.characters.deleteCharacter(characterId);\n            case 2:\n              response = _context8.v;\n              if (!(response.status === 200)) {\n                _context8.n = 3;\n                break;\n              }\n              commit('DELETE_CHARACTER', characterId);\n              console.log('✅ 角色删除成功');\n              return _context8.a(2, true);\n            case 3:\n              _context8.n = 5;\n              break;\n            case 4:\n              _context8.p = 4;\n              _t0 = _context8.v;\n              console.error('❌ 删除角色失败:', _t0);\n              console.error('错误详情:', ((_error$response2 = _t0.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || _t0.message);\n              throw _t0;\n            case 5:\n              return _context8.a(2);\n          }\n        }, _callee8, null, [[1, 4]]);\n      }))();\n    },\n    fetchRooms: function fetchRooms(_ref20) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee9() {\n        var commit, state, response, _error$response3, mockRooms, _t1;\n        return _regenerator().w(function (_context9) {\n          while (1) switch (_context9.n) {\n            case 0:\n              commit = _ref20.commit, state = _ref20.state;\n              _context9.p = 1;\n              console.log('正在获取房间列表...');\n              _context9.n = 2;\n              return axios.get('/api/rooms/', {\n                headers: {\n                  Authorization: \"Bearer \".concat(state.token)\n                }\n              });\n            case 2:\n              response = _context9.v;\n              console.log('获取房间成功:', response.data);\n              commit('SET_ROOMS', response.data);\n              return _context9.a(2, response.data);\n            case 3:\n              _context9.p = 3;\n              _t1 = _context9.v;\n              console.error('获取房间失败:', _t1.message, (_error$response3 = _t1.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status);\n              if (!(_t1.message.includes('Network Error') || _t1.code === 'ECONNREFUSED')) {\n                _context9.n = 4;\n                break;\n              }\n              console.log('使用模拟数据');\n              mockRooms = [{\n                id: 1,\n                name: '模拟房间1',\n                description: '后端未连接，使用模拟数据',\n                keeper_id: 1,\n                is_private: false\n              }, {\n                id: 2,\n                name: '模拟房间2',\n                description: '请确保后端服务已启动',\n                keeper_id: 1,\n                is_private: false\n              }];\n              commit('SET_ROOMS', mockRooms);\n              return _context9.a(2, mockRooms);\n            case 4:\n              throw _t1;\n            case 5:\n              return _context9.a(2);\n          }\n        }, _callee9, null, [[1, 3]]);\n      }))();\n    },\n    createRoom: function createRoom(_ref21, roomData) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee0() {\n        var commit, state, response, newRoom, _t10;\n        return _regenerator().w(function (_context0) {\n          while (1) switch (_context0.n) {\n            case 0:\n              commit = _ref21.commit, state = _ref21.state;\n              _context0.p = 1;\n              _context0.n = 2;\n              return axios.post('/api/rooms/', _objectSpread(_objectSpread({}, roomData), {}, {\n                keeper_id: state.user.id\n              }), {\n                headers: {\n                  Authorization: \"Bearer \".concat(state.token)\n                }\n              });\n            case 2:\n              response = _context0.v;\n              newRoom = response.data;\n              commit('SET_ROOMS', [].concat(_toConsumableArray(state.rooms), [newRoom]));\n              return _context0.a(2, newRoom);\n            case 3:\n              _context0.p = 3;\n              _t10 = _context0.v;\n              throw _t10;\n            case 4:\n              return _context0.a(2);\n          }\n        }, _callee0, null, [[1, 3]]);\n      }))();\n    },\n    joinRoom: function joinRoom(_ref22, _ref23) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee1() {\n        var commit, roomId, numericRoomId, response, roomData, mockRoom, _t11, _t12;\n        return _regenerator().w(function (_context1) {\n          while (1) switch (_context1.n) {\n            case 0:\n              commit = _ref22.commit;\n              roomId = _ref23.roomId;\n              _context1.p = 1;\n              // 确保roomId是有效的数字\n              numericRoomId = parseInt(roomId);\n              if (!isNaN(numericRoomId)) {\n                _context1.n = 2;\n                break;\n              }\n              console.error('无效的房间ID:', roomId);\n              throw new Error('无效的房间ID');\n            case 2:\n              console.log(\"\\u5C1D\\u8BD5\\u52A0\\u5165\\u623F\\u95F4\\uFF0CID: \".concat(numericRoomId));\n              _context1.p = 3;\n              _context1.n = 4;\n              return apiService.getRoom(numericRoomId);\n            case 4:\n              response = _context1.v;\n              if (!response.data) {\n                _context1.n = 5;\n                break;\n              }\n              // 确保房间数据中的ID是数字类型\n              roomData = _objectSpread(_objectSpread({}, response.data), {}, {\n                id: parseInt(response.data.id)\n              });\n              console.log('从API获取的房间数据:', roomData);\n              commit('SET_CURRENT_ROOM', roomData);\n              return _context1.a(2, roomData);\n            case 5:\n              throw new Error('房间不存在');\n            case 6:\n              _context1.n = 8;\n              break;\n            case 7:\n              _context1.p = 7;\n              _t11 = _context1.v;\n              console.error('加入房间失败:', _t11);\n              // 创建模拟房间\n              mockRoom = {\n                id: numericRoomId,\n                name: \"\\u6D4B\\u8BD5\\u623F\\u95F4 \".concat(numericRoomId),\n                description: '这是一个自动创建的房间',\n                keeper_id: 1,\n                is_private: false,\n                is_mock: true\n              };\n              console.log('创建模拟房间:', mockRoom);\n              commit('SET_CURRENT_ROOM', mockRoom);\n              return _context1.a(2, mockRoom);\n            case 8:\n              _context1.n = 10;\n              break;\n            case 9:\n              _context1.p = 9;\n              _t12 = _context1.v;\n              console.error('处理房间ID失败:', _t12);\n              throw _t12;\n            case 10:\n              return _context1.a(2);\n          }\n        }, _callee1, null, [[3, 7], [1, 9]]);\n      }))();\n    },\n    leaveRoom: function leaveRoom(_ref24) {\n      var commit = _ref24.commit;\n      commit('CLEAR_CURRENT_ROOM');\n    },\n    updateRoomName: function updateRoomName(_ref25, _ref26) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee10() {\n        var commit, state, roomId, name, _t13;\n        return _regenerator().w(function (_context10) {\n          while (1) switch (_context10.n) {\n            case 0:\n              commit = _ref25.commit, state = _ref25.state;\n              roomId = _ref26.roomId, name = _ref26.name;\n              _context10.p = 1;\n              // 如果是当前房间，更新当前房间名称\n              if (state.currentRoom && state.currentRoom.id === roomId) {\n                commit('UPDATE_ROOM_NAME', {\n                  roomId: roomId,\n                  name: name\n                });\n              }\n\n              // 更新房间列表中的房间名称\n              if (state.rooms && state.rooms.length > 0) {\n                commit('UPDATE_ROOM_LIST_NAME', {\n                  roomId: roomId,\n                  name: name\n                });\n              }\n\n              // 保存到本地存储\n              try {\n                if (window.storageManager) {\n                  window.storageManager.setItem(\"room_name_\".concat(roomId), name);\n                } else {\n                  localStorage.setItem(\"room_name_\".concat(roomId), name);\n                }\n              } catch (error) {\n                console.warn('[Store] 无法保存房间名称:', error.message);\n              }\n\n              // 注释掉API调用，因为后端不支持此功能\n              // 仅依赖前端存储和WebSocket通信\n              /*\r\n              try {\r\n                await apiService.updateRoom(roomId, { name });\r\n              } catch (apiError) {\r\n                console.log('API更新房间名称失败，但不影响前端功能:', apiError.message);\r\n              }\r\n              */\n              return _context10.a(2, true);\n            case 2:\n              _context10.p = 2;\n              _t13 = _context10.v;\n              console.error('更新房间名称失败:', _t13);\n              // 即使出错，我们仍然保留本地更改\n              return _context10.a(2, false);\n          }\n        }, _callee10, null, [[1, 2]]);\n      }))();\n    },\n    toggleTheme: function toggleTheme(_ref27) {\n      var commit = _ref27.commit;\n      commit('TOGGLE_THEME');\n    },\n    addMessage: function addMessage(_ref28, message) {\n      var commit = _ref28.commit,\n        rootState = _ref28.rootState;\n      commit('ADD_MESSAGE', message);\n      emitter.emit('new-message', message);\n      return message;\n    },\n    clearMessages: function clearMessages(_ref29) {\n      var commit = _ref29.commit;\n      commit('CLEAR_MESSAGES');\n    },\n    updateMessage: function updateMessage(_ref30, _ref31) {\n      var commit = _ref30.commit;\n      var index = _ref31.index,\n        message = _ref31.message;\n      commit('UPDATE_MESSAGE', {\n        index: index,\n        message: message\n      });\n    },\n    removeLastMessage: function removeLastMessage(_ref32) {\n      var commit = _ref32.commit;\n      commit('REMOVE_LAST_MESSAGE');\n    },\n    updateCharacterFromServer: function updateCharacterFromServer(_ref33, characterData) {\n      var commit = _ref33.commit;\n      commit('UPDATE_CHARACTER_FROM_SERVER', characterData);\n    },\n    updateCharacterAttribute: function updateCharacterAttribute(_ref34, _ref35) {\n      var commit = _ref34.commit,\n        state = _ref34.state,\n        dispatch = _ref34.dispatch;\n      var id = _ref35.id,\n        field = _ref35.field,\n        value = _ref35.value;\n      commit('UPDATE_CHARACTER_ATTRIBUTE', {\n        id: id,\n        field: field,\n        value: value\n      });\n      import('@/services/websocket').then(function (module) {\n        var websocketService = module[\"default\"];\n        if (websocketService.isConnected) {\n          websocketService.sendCharacterAttributeUpdate(id, field, value);\n        }\n      });\n    },\n    setCurrentCharacter: function setCurrentCharacter(_ref36, characterId) {\n      var commit = _ref36.commit,\n        dispatch = _ref36.dispatch;\n      commit('SET_CURRENT_CHARACTER', characterId);\n      dispatch('syncCharacterData', characterId);\n    },\n    syncCharacterData: function syncCharacterData(_ref37, characterId) {\n      var commit = _ref37.commit,\n        state = _ref37.state;\n      commit('UPDATE_CHARACTER_SYNC_STATUS', {\n        characterId: characterId,\n        status: {\n          syncing: true\n        }\n      });\n      import('@/services/websocket').then(function (module) {\n        var websocketService = module[\"default\"];\n        if (websocketService.isConnected) {\n          websocketService.requestCharacterSync(characterId);\n        } else {\n          var cachedData = state.localCharacterCache[characterId];\n          if (cachedData) {\n            commit('UPDATE_CHARACTER_FROM_SERVER', cachedData);\n          }\n          commit('UPDATE_CHARACTER_SYNC_STATUS', {\n            characterId: characterId,\n            status: {\n              syncing: false,\n              lastSync: new Date().toISOString(),\n              error: 'WebSocket未连接，使用本地缓存'\n            }\n          });\n        }\n      });\n    },\n    loadRoomMessages: function loadRoomMessages(_ref38) {\n      var commit = _ref38.commit,\n        state = _ref38.state;\n      if (!state.currentRoom) return;\n      var roomId = state.currentRoom.id;\n      var storedMessages = storageManager.getItem(\"room_messages_\".concat(roomId));\n      if (storedMessages) {\n        try {\n          var messages = JSON.parse(storedMessages);\n          commit('SET_MESSAGES', messages);\n        } catch (error) {\n          console.error('加载房间消息失败:', error);\n        }\n      }\n    },\n    setScenes: function setScenes(_ref39, scenes) {\n      var commit = _ref39.commit;\n      commit('SET_SCENES', scenes);\n    },\n    addScene: function addScene(_ref40, scene) {\n      var commit = _ref40.commit;\n      commit('ADD_SCENE', scene);\n    },\n    updateScene: function updateScene(_ref41, _ref42) {\n      var commit = _ref41.commit;\n      var index = _ref42.index,\n        scene = _ref42.scene;\n      commit('UPDATE_SCENE', {\n        index: index,\n        scene: scene\n      });\n    },\n    deleteScene: function deleteScene(_ref43, index) {\n      var commit = _ref43.commit;\n      commit('DELETE_SCENE', index);\n    },\n    setCurrentSceneIndex: function setCurrentSceneIndex(_ref44, index) {\n      var commit = _ref44.commit;\n      commit('SET_CURRENT_SCENE_INDEX', index);\n    },\n    setNotes: function setNotes(_ref45, _ref46) {\n      var commit = _ref45.commit;\n      var characterId = _ref46.characterId,\n        notes = _ref46.notes;\n      commit('SET_NOTES', {\n        characterId: characterId,\n        notes: notes\n      });\n    },\n    addNote: function addNote(_ref47, _ref48) {\n      var commit = _ref47.commit;\n      var characterId = _ref48.characterId,\n        note = _ref48.note;\n      if (!note.createdAt) {\n        note.createdAt = new Date().toISOString();\n      }\n      commit('ADD_NOTE', {\n        characterId: characterId,\n        note: note\n      });\n    },\n    updateNote: function updateNote(_ref49, _ref50) {\n      var commit = _ref49.commit;\n      var characterId = _ref50.characterId,\n        index = _ref50.index,\n        note = _ref50.note;\n      note.updatedAt = new Date().toISOString();\n      commit('UPDATE_NOTE', {\n        characterId: characterId,\n        index: index,\n        note: note\n      });\n    },\n    deleteNote: function deleteNote(_ref51, _ref52) {\n      var commit = _ref51.commit;\n      var characterId = _ref52.characterId,\n        index = _ref52.index;\n      commit('DELETE_NOTE', {\n        characterId: characterId,\n        index: index\n      });\n    },\n    loadSceneData: function loadSceneData(_ref53) {\n      var commit = _ref53.commit,\n        state = _ref53.state;\n      var scenes = JSON.parse(storageManager.getItem('scenes') || '[]');\n      if (scenes.length > 0) {\n        commit('SET_SCENES', scenes);\n      }\n      var currentSceneIndex = parseInt(storageManager.getItem('currentSceneIndex') || '0');\n      if (currentSceneIndex >= 0 && currentSceneIndex < scenes.length) {\n        commit('SET_CURRENT_SCENE_INDEX', currentSceneIndex);\n      }\n      var notes = JSON.parse(storageManager.getItem('notes') || '{}');\n      if (notes) {\n        Object.keys(notes).forEach(function (characterId) {\n          commit('SET_NOTES', {\n            characterId: characterId,\n            notes: notes[characterId]\n          });\n        });\n      }\n    },\n    setOfflineMode: function setOfflineMode(_ref54, isOffline) {\n      var commit = _ref54.commit;\n      commit('SET_OFFLINE_MODE', isOffline);\n      if (isOffline) {\n        console.log('已启用离线模式');\n        // 发送系统消息通知用户\n        this.dispatch('addMessage', {\n          type: 'system',\n          content: '已启用离线模式，部分功能可能不可用',\n          timestamp: new Date().toISOString()\n        });\n      }\n    },\n    fetchSaves: function fetchSaves(_ref55, _ref56) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee11() {\n        var commit, roomId, _ref56$includeAutoSav, includeAutoSaves, response, _t14;\n        return _regenerator().w(function (_context11) {\n          while (1) switch (_context11.n) {\n            case 0:\n              commit = _ref55.commit;\n              roomId = _ref56.roomId, _ref56$includeAutoSav = _ref56.includeAutoSaves, includeAutoSaves = _ref56$includeAutoSav === void 0 ? true : _ref56$includeAutoSav;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context11.p = 1;\n              _context11.n = 2;\n              return apiService.gameSaves.getRoomSaves(roomId, 0, 100, includeAutoSaves);\n            case 2:\n              response = _context11.v;\n              commit('SET_SAVES', response.data || []);\n              return _context11.a(2, response.data || []);\n            case 3:\n              _context11.p = 3;\n              _t14 = _context11.v;\n              console.error('获取存档列表失败:', _t14);\n              commit('SET_SAVE_ERROR', '获取存档列表失败');\n              throw _t14;\n            case 4:\n              _context11.p = 4;\n              commit('SET_LOADING_SAVES', false);\n              return _context11.f(4);\n            case 5:\n              return _context11.a(2);\n          }\n        }, _callee11, null, [[1, 3, 4, 5]]);\n      }))();\n    },\n    createSave: function createSave(_ref57, saveData) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee12() {\n        var commit, response, _t15;\n        return _regenerator().w(function (_context12) {\n          while (1) switch (_context12.n) {\n            case 0:\n              commit = _ref57.commit;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context12.p = 1;\n              _context12.n = 2;\n              return apiService.gameSaves.createSave(saveData);\n            case 2:\n              response = _context12.v;\n              commit('ADD_SAVE', response.data);\n              return _context12.a(2, response.data);\n            case 3:\n              _context12.p = 3;\n              _t15 = _context12.v;\n              console.error('创建存档失败:', _t15);\n              commit('SET_SAVE_ERROR', '创建存档失败');\n              throw _t15;\n            case 4:\n              _context12.p = 4;\n              commit('SET_LOADING_SAVES', false);\n              return _context12.f(4);\n            case 5:\n              return _context12.a(2);\n          }\n        }, _callee12, null, [[1, 3, 4, 5]]);\n      }))();\n    },\n    updateSave: function updateSave(_ref58, _ref59) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee13() {\n        var commit, saveId, updateData, response, _t16;\n        return _regenerator().w(function (_context13) {\n          while (1) switch (_context13.n) {\n            case 0:\n              commit = _ref58.commit;\n              saveId = _ref59.saveId, updateData = _ref59.updateData;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context13.p = 1;\n              _context13.n = 2;\n              return apiService.gameSaves.updateSave(saveId, updateData);\n            case 2:\n              response = _context13.v;\n              commit('UPDATE_SAVE', response.data);\n              return _context13.a(2, response.data);\n            case 3:\n              _context13.p = 3;\n              _t16 = _context13.v;\n              console.error('更新存档失败:', _t16);\n              commit('SET_SAVE_ERROR', '更新存档失败');\n              throw _t16;\n            case 4:\n              _context13.p = 4;\n              commit('SET_LOADING_SAVES', false);\n              return _context13.f(4);\n            case 5:\n              return _context13.a(2);\n          }\n        }, _callee13, null, [[1, 3, 4, 5]]);\n      }))();\n    },\n    deleteSave: function deleteSave(_ref60, saveId) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee14() {\n        var commit, response, _t17;\n        return _regenerator().w(function (_context14) {\n          while (1) switch (_context14.n) {\n            case 0:\n              commit = _ref60.commit;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context14.p = 1;\n              _context14.n = 2;\n              return apiService.gameSaves.deleteSave(saveId);\n            case 2:\n              response = _context14.v;\n              if (!(response.data && response.data.success)) {\n                _context14.n = 3;\n                break;\n              }\n              commit('REMOVE_SAVE', saveId);\n              return _context14.a(2, true);\n            case 3:\n              return _context14.a(2, false);\n            case 4:\n              _context14.p = 4;\n              _t17 = _context14.v;\n              console.error('删除存档失败:', _t17);\n              commit('SET_SAVE_ERROR', '删除存档失败');\n              throw _t17;\n            case 5:\n              _context14.p = 5;\n              commit('SET_LOADING_SAVES', false);\n              return _context14.f(5);\n            case 6:\n              return _context14.a(2);\n          }\n        }, _callee14, null, [[1, 4, 5, 6]]);\n      }))();\n    },\n    createAutoSave: function createAutoSave(_ref61, _ref62) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee15() {\n        var commit, roomId, creatorId, saveData, previousAutoSaveId, response, _t18;\n        return _regenerator().w(function (_context15) {\n          while (1) switch (_context15.n) {\n            case 0:\n              commit = _ref61.commit;\n              roomId = _ref62.roomId, creatorId = _ref62.creatorId, saveData = _ref62.saveData, previousAutoSaveId = _ref62.previousAutoSaveId;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context15.p = 1;\n              _context15.n = 2;\n              return apiService.gameSaves.createAutoSave(roomId, creatorId, saveData, previousAutoSaveId);\n            case 2:\n              response = _context15.v;\n              if (previousAutoSaveId) {\n                commit('UPDATE_SAVE', response.data);\n              } else {\n                commit('ADD_SAVE', response.data);\n              }\n              return _context15.a(2, response.data);\n            case 3:\n              _context15.p = 3;\n              _t18 = _context15.v;\n              console.error('创建自动存档失败:', _t18);\n              commit('SET_SAVE_ERROR', '创建自动存档失败');\n              throw _t18;\n            case 4:\n              _context15.p = 4;\n              commit('SET_LOADING_SAVES', false);\n              return _context15.f(4);\n            case 5:\n              return _context15.a(2);\n          }\n        }, _callee15, null, [[1, 3, 4, 5]]);\n      }))();\n    },\n    fetchSaveHistory: function fetchSaveHistory(_ref63, saveId) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee16() {\n        var commit, response, _t19;\n        return _regenerator().w(function (_context16) {\n          while (1) switch (_context16.n) {\n            case 0:\n              commit = _ref63.commit;\n              commit('SET_LOADING_SAVE_HISTORY', true);\n              _context16.p = 1;\n              _context16.n = 2;\n              return apiService.gameSaves.getSaveHistory(saveId);\n            case 2:\n              response = _context16.v;\n              commit('SET_SAVE_HISTORY', response.data || []);\n              return _context16.a(2, response.data || []);\n            case 3:\n              _context16.p = 3;\n              _t19 = _context16.v;\n              console.error('获取存档历史记录失败:', _t19);\n              throw _t19;\n            case 4:\n              _context16.p = 4;\n              commit('SET_LOADING_SAVE_HISTORY', false);\n              return _context16.f(4);\n            case 5:\n              return _context16.a(2);\n          }\n        }, _callee16, null, [[1, 3, 4, 5]]);\n      }))();\n    },\n    getSaveHistoryVersion: function getSaveHistoryVersion(_ref64, _ref65) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee17() {\n        var commit, saveId, version, response, _t20;\n        return _regenerator().w(function (_context17) {\n          while (1) switch (_context17.n) {\n            case 0:\n              commit = _ref64.commit;\n              saveId = _ref65.saveId, version = _ref65.version;\n              _context17.p = 1;\n              _context17.n = 2;\n              return apiService.gameSaves.getSaveHistoryVersion(saveId, version);\n            case 2:\n              response = _context17.v;\n              return _context17.a(2, response.data);\n            case 3:\n              _context17.p = 3;\n              _t20 = _context17.v;\n              console.error('获取历史版本失败:', _t20);\n              throw _t20;\n            case 4:\n              return _context17.a(2);\n          }\n        }, _callee17, null, [[1, 3]]);\n      }))();\n    },\n    restoreVersion: function restoreVersion(_ref66, _ref67) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee18() {\n        var commit, saveId, version, response, _t21;\n        return _regenerator().w(function (_context18) {\n          while (1) switch (_context18.n) {\n            case 0:\n              commit = _ref66.commit;\n              saveId = _ref67.saveId, version = _ref67.version;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context18.p = 1;\n              _context18.n = 2;\n              return apiService.gameSaves.restoreVersion(saveId, version);\n            case 2:\n              response = _context18.v;\n              commit('UPDATE_SAVE', response.data);\n              return _context18.a(2, response.data);\n            case 3:\n              _context18.p = 3;\n              _t21 = _context18.v;\n              console.error('恢复历史版本失败:', _t21);\n              commit('SET_SAVE_ERROR', '恢复历史版本失败');\n              throw _t21;\n            case 4:\n              _context18.p = 4;\n              commit('SET_LOADING_SAVES', false);\n              return _context18.f(4);\n            case 5:\n              return _context18.a(2);\n          }\n        }, _callee18, null, [[1, 3, 4, 5]]);\n      }))();\n    },\n    loadSave: function loadSave(_ref68, save) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee19() {\n        var commit, dispatch, saveData, _t22;\n        return _regenerator().w(function (_context19) {\n          while (1) switch (_context19.n) {\n            case 0:\n              commit = _ref68.commit, dispatch = _ref68.dispatch;\n              commit('SET_LOADING_SAVES', true);\n              commit('SET_SAVE_ERROR', null);\n              _context19.p = 1;\n              // 设置当前存档\n              commit('SET_CURRENT_SAVE', save);\n\n              // 从存档数据中恢复状态\n              saveData = save.save_data; // 恢复各种状态\n              if (saveData.messages) {\n                commit('SET_MESSAGES', saveData.messages);\n              }\n              if (saveData.characters) {\n                commit('SET_CHARACTERS', saveData.characters);\n              }\n              if (saveData.scenes) {\n                commit('SET_SCENES', saveData.scenes);\n                if (saveData.currentScene !== undefined) {\n                  commit('SET_CURRENT_SCENE_INDEX', saveData.currentScene);\n                }\n              }\n              if (saveData.diceHistory) {\n                commit('SET_DICE_HISTORY', saveData.diceHistory);\n              }\n              if (saveData.notes) {\n                commit('SET_NOTES', saveData.notes);\n              }\n              if (saveData.clues) {\n                commit('SET_CLUES', saveData.clues);\n              }\n              if (saveData.aiSettings) {\n                commit('SET_AI_SETTINGS', saveData.aiSettings);\n              }\n              return _context19.a(2, true);\n            case 2:\n              _context19.p = 2;\n              _t22 = _context19.v;\n              console.error('加载存档失败:', _t22);\n              commit('SET_SAVE_ERROR', '加载存档失败');\n              throw _t22;\n            case 3:\n              _context19.p = 3;\n              commit('SET_LOADING_SAVES', false);\n              return _context19.f(3);\n            case 4:\n              return _context19.a(2);\n          }\n        }, _callee19, null, [[1, 2, 3, 4]]);\n      }))();\n    },\n    createCharacter: function createCharacter(_ref69, characterData) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee20() {\n        var commit, dispatch, state, _state$user2, userId, response, _t23;\n        return _regenerator().w(function (_context20) {\n          while (1) switch (_context20.n) {\n            case 0:\n              commit = _ref69.commit, dispatch = _ref69.dispatch, state = _ref69.state;\n              _context20.p = 1;\n              console.log('🎭 创建角色:', characterData.name);\n\n              // 获取当前用户ID\n              userId = ((_state$user2 = state.user) === null || _state$user2 === void 0 ? void 0 : _state$user2.id) || 1;\n              console.log('使用用户ID:', userId);\n              _context20.n = 2;\n              return apiService.characters.createCharacter(characterData, userId);\n            case 2:\n              response = _context20.v;\n              if (!response.data) {\n                _context20.n = 3;\n                break;\n              }\n              // 添加到角色列表\n              commit('ADD_CHARACTER', response.data);\n              console.log('✅ 角色创建成功:', response.data.name);\n              return _context20.a(2, response.data);\n            case 3:\n              _context20.n = 5;\n              break;\n            case 4:\n              _context20.p = 4;\n              _t23 = _context20.v;\n              console.error('❌ 角色创建失败:', _t23);\n              throw _t23;\n            case 5:\n              return _context20.a(2);\n          }\n        }, _callee20, null, [[1, 4]]);\n      }))();\n    },\n    loadUserCharacters: function loadUserCharacters(_ref70) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee21() {\n        var commit, state, response, _t24;\n        return _regenerator().w(function (_context21) {\n          while (1) switch (_context21.n) {\n            case 0:\n              commit = _ref70.commit, state = _ref70.state;\n              _context21.p = 1;\n              if (!(!state.user || !state.user.id)) {\n                _context21.n = 2;\n                break;\n              }\n              console.log('⚠️ 用户未登录，跳过加载角色');\n              return _context21.a(2);\n            case 2:\n              _context21.n = 3;\n              return apiService.characters.getUserCharacters(state.user.id);\n            case 3:\n              response = _context21.v;\n              if (response.data) {\n                commit('SET_CHARACTERS', response.data);\n                console.log('✅ 用户角色加载成功:', response.data.length, '个角色');\n              }\n              _context21.n = 5;\n              break;\n            case 4:\n              _context21.p = 4;\n              _t24 = _context21.v;\n              console.error('❌ 用户角色加载失败:', _t24);\n            case 5:\n              return _context21.a(2);\n          }\n        }, _callee21, null, [[1, 4]]);\n      }))();\n    },\n    calculateSkillPoints: function calculateSkillPoints(_ref71, characterData) {\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee22() {\n        var state, response, _t25;\n        return _regenerator().w(function (_context22) {\n          while (1) switch (_context22.n) {\n            case 0:\n              state = _ref71.state;\n              _context22.p = 1;\n              _context22.n = 2;\n              return apiService.characters.calculateSkillPoints(characterData);\n            case 2:\n              response = _context22.v;\n              return _context22.a(2, response.data);\n            case 3:\n              _context22.p = 3;\n              _t25 = _context22.v;\n              console.error('❌ 技能点计算失败:', _t25);\n              throw _t25;\n            case 4:\n              return _context22.a(2);\n          }\n        }, _callee22, null, [[1, 3]]);\n      }))();\n    }\n  },\n  modules: {\n    auth: auth,\n    rooms: rooms\n  },\n  plugins: [persistencePlugin]\n});", "map": {"version": 3, "names": ["createStore", "axios", "emitter", "v4", "uuidv4", "apiService", "storageManager", "persistencePlugin", "auth", "rooms", "API_URL", "process", "env", "VUE_APP_API_URL", "safeGetFromStorage", "key", "defaultValue", "arguments", "length", "undefined", "getItem", "error", "console", "warn", "concat", "safeGetObjectFromStorage", "getObject", "state", "user", "token", "isAuthenticated", "authLoading", "authError", "tokenExpiry", "characters", "currentCharacterId", "messages", "theme", "aiSettings", "<PERSON><PERSON><PERSON><PERSON>", "apiUrl", "modelName", "isApiValid", "characterSyncStatus", "localCharacterCache", "lastSyncTime", "scenes", "currentSceneIndex", "parseInt", "notes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isOfflineMode", "connectionStatus", "storageStatus", "occupations", "skills", "weapons", "armors", "saves", "currentSave", "loadingSaves", "saveError", "saveHistory", "loadingSaveHistory", "isPreviewMode", "globalNotice", "getters", "now", "Date", "getTime", "expiry", "currentUser", "tokenValid", "userCharacters", "currentCharacter", "find", "c", "id", "characterId", "lastSync", "syncing", "getLocalCharacterData", "roomMessages", "allScenes", "currentScene", "characterNotes", "globalNotes", "currentTheme", "isDarkTheme", "isStorageDegraded", "_state$storageStatus", "isDegraded", "gameSaves", "currentGameSave", "isLoadingSaves", "isLoadingSaveHistory", "mutations", "SET_USER", "setObject", "removeItem", "SET_TOKEN", "setItem", "payload", "JSON", "parse", "atob", "split", "exp", "toISOString", "e", "SET_AUTH_LOADING", "loading", "SET_AUTH_ERROR", "CLEAR_AUTH", "SET_CONNECTION_STATUS", "status", "SET_LAST_SYNC_TIME", "time", "SET_STORAGE_STATUS", "UPDATE_USER_AVATAR", "avatarUrl", "avatar_url", "SET_CHARACTERS", "ADD_CHARACTER", "character", "push", "UPDATE_CHARACTER", "updatedCharacter", "index", "findIndex", "splice", "_objectSpread", "lastUpdated", "UPDATE_CHARACTER_FROM_SERVER", "characterData", "mergedCharacter", "_localData", "UPDATE_CHARACTER_ATTRIBUTE", "_ref", "field", "value", "includes", "parts", "obj", "i", "SET_CURRENT_CHARACTER", "DELETE_CHARACTER", "filter", "UPDATE_CHARACTER_SYNC_STATUS", "_ref2", "SET_ROOMS", "SET_CURRENT_ROOM", "roomData", "currentRoom", "CLEAR_CURRENT_ROOM", "ADD_MESSAGE", "message", "log", "timestamp", "type", "username", "user_id", "Math", "random", "toString", "substr", "slice", "roomId", "SET_MESSAGES", "TOGGLE_THEME", "CLEAR_MESSAGES", "SET_IMPORTANT_MESSAGE", "importantMessage", "SET_API_VALID", "<PERSON><PERSON><PERSON><PERSON>", "UPDATE_AI_SETTINGS", "settings", "UPDATE_MESSAGE", "_ref3", "msgIndex", "m", "REMOVE_LAST_MESSAGE", "pop", "SET_SCENES", "ADD_SCENE", "scene", "UPDATE_SCENE", "_ref4", "DELETE_SCENE", "SET_CURRENT_SCENE_INDEX", "SET_NOTES", "_ref5", "ADD_NOTE", "_ref6", "note", "unshift", "UPDATE_NOTE", "_ref7", "DELETE_NOTE", "_ref8", "SET_OFFLINE_MODE", "isOffline", "SET_SAVES", "SET_CURRENT_SAVE", "save", "ADD_SAVE", "UPDATE_SAVE", "updatedSave", "REMOVE_SAVE", "saveId", "SET_LOADING_SAVES", "isLoading", "SET_SAVE_ERROR", "SET_SAVE_HISTORY", "history", "SET_LOADING_SAVE_HISTORY", "SET_PREVIEW_MODE", "isPreview", "SET_GLOBAL_NOTICE", "notice", "CLEAR_GLOBAL_NOTICE", "SET_OCCUPATIONS", "SET_SKILLS", "SET_WEAPONS", "SET_ARMORS", "UPDATE_ROOM_NAME", "_ref9", "name", "UPDATE_ROOM_LIST_NAME", "_ref0", "roomIndex", "room", "actions", "register", "_ref1", "userData", "_asyncToGenerator", "_regenerator", "_callee", "commit", "dispatch", "response", "_response$data", "errorMessage", "errorDetail", "_t", "w", "_context", "n", "p", "email", "password", "Error", "post", "v", "data", "a", "detail", "code", "f", "login", "_ref10", "credentials", "_callee2", "_response$data2", "_t2", "_t3", "_context2", "logout", "_ref11", "validateToken", "_ref12", "_callee3", "_t4", "_context3", "get", "headers", "Authorization", "initializeStorage", "_ref13", "getStatus", "storageType", "initializeApp", "_ref14", "_callee4", "_t5", "_t6", "_context4", "refreshUserData", "_ref15", "_callee5", "_t7", "_context5", "fetchCharacters", "_ref16", "_callee6", "_state$user", "_error$response", "_t8", "_context6", "getUserCharacters", "updateCharacter", "_ref17", "_ref18", "_callee7", "_t9", "_context7", "_objectWithoutProperties", "_excluded", "put", "then", "module", "websocketService", "isConnected", "sendCharacterUpdate", "deleteCharacter", "_ref19", "_callee8", "_error$response2", "_t0", "_context8", "fetchRooms", "_ref20", "_callee9", "_error$response3", "mockRooms", "_t1", "_context9", "description", "keeper_id", "is_private", "createRoom", "_ref21", "_callee0", "newRoom", "_t10", "_context0", "_toConsumableArray", "joinRoom", "_ref22", "_ref23", "_callee1", "numericRoomId", "mockRoom", "_t11", "_t12", "_context1", "isNaN", "getRoom", "is_mock", "leaveRoom", "_ref24", "updateRoomName", "_ref25", "_ref26", "_callee10", "_t13", "_context10", "window", "localStorage", "toggleTheme", "_ref27", "addMessage", "_ref28", "rootState", "emit", "clearMessages", "_ref29", "updateMessage", "_ref30", "_ref31", "removeLastMessage", "_ref32", "updateCharacterFromServer", "_ref33", "updateCharacterAttribute", "_ref34", "_ref35", "sendCharacterAttributeUpdate", "setCurrentCharacter", "_ref36", "syncCharacterData", "_ref37", "requestCharacterSync", "cachedData", "loadRoomMessages", "_ref38", "storedMessages", "setScenes", "_ref39", "addScene", "_ref40", "updateScene", "_ref41", "_ref42", "deleteScene", "_ref43", "setCurrentSceneIndex", "_ref44", "setNotes", "_ref45", "_ref46", "addNote", "_ref47", "_ref48", "createdAt", "updateNote", "_ref49", "_ref50", "updatedAt", "deleteNote", "_ref51", "_ref52", "loadSceneData", "_ref53", "Object", "keys", "for<PERSON>ach", "setOfflineMode", "_ref54", "content", "fetchSaves", "_ref55", "_ref56", "_callee11", "_ref56$includeAutoSav", "includeAutoSaves", "_t14", "_context11", "getRoomSaves", "createSave", "_ref57", "saveData", "_callee12", "_t15", "_context12", "updateSave", "_ref58", "_ref59", "_callee13", "updateData", "_t16", "_context13", "deleteSave", "_ref60", "_callee14", "_t17", "_context14", "success", "createAutoSave", "_ref61", "_ref62", "_callee15", "creatorId", "previousAutoSaveId", "_t18", "_context15", "fetchSaveHistory", "_ref63", "_callee16", "_t19", "_context16", "getSaveHistory", "getSaveHistoryVersion", "_ref64", "_ref65", "_callee17", "version", "_t20", "_context17", "restoreVersion", "_ref66", "_ref67", "_callee18", "_t21", "_context18", "loadSave", "_ref68", "_callee19", "_t22", "_context19", "save_data", "diceHistory", "clues", "createCharacter", "_ref69", "_callee20", "_state$user2", "userId", "_t23", "_context20", "loadUserCharacters", "_ref70", "_callee21", "_t24", "_context21", "calculateSkillPoints", "_ref71", "_callee22", "_t25", "_context22", "modules", "plugins"], "sources": ["C:/Users/<USER>/Desktop/最新的 - 副本/7.26/coc_trpgs/frontend/src/store/index.js"], "sourcesContent": ["import { createStore } from 'vuex'\r\nimport axios from 'axios'\r\nimport { emitter } from '../main.js'\r\nimport { v4 as uuidv4 } from 'uuid'\r\nimport apiService from '@/services/api'\r\nimport storageManager from '@/utils/storage'\r\nimport persistencePlugin from './plugins/persistencePlugin'\r\nimport auth from './modules/auth'\r\nimport rooms from './modules/rooms'\r\n\r\nconst API_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000'\r\n\r\n// 安全地从存储中获取数据的辅助函数\r\nfunction safeGetFromStorage(key, defaultValue = null) {\r\n  try {\r\n    return storageManager.getItem(key) || defaultValue\r\n  } catch (error) {\r\n    console.warn(`[Store] 获取存储项 ${key} 失败:`, error)\r\n    return defaultValue\r\n  }\r\n}\r\n\r\nfunction safeGetObjectFromStorage(key, defaultValue = null) {\r\n  try {\r\n    return storageManager.getObject(key) || defaultValue\r\n  } catch (error) {\r\n    console.warn(`[Store] 获取存储对象 ${key} 失败:`, error)\r\n    return defaultValue\r\n  }\r\n}\r\n\r\nexport default createStore({\r\n  state: {\r\n    // 用户认证状态\r\n    user: safeGetObjectFromStorage('user', null),\r\n    token: safeGetFromStorage('token', ''),\r\n    isAuthenticated: false,\r\n    authLoading: false,\r\n    authError: null,\r\n    tokenExpiry: safeGetFromStorage('tokenExpiry', null),\r\n\r\n    // 角色数据\r\n    characters: safeGetObjectFromStorage('characters', []),\r\n    currentCharacterId: safeGetFromStorage('currentCharacterId', null),\r\n    messages: [],\r\n\r\n    // 应用设置\r\n    theme: storageManager.getItem('theme') || 'dark',\r\n    aiSettings: {\r\n      apiKey: '',\r\n      apiUrl: '',\r\n      modelName: '',\r\n      isApiValid: false\r\n    },\r\n\r\n    // 同步状态\r\n    characterSyncStatus: {},\r\n    localCharacterCache: safeGetObjectFromStorage('localCharacterCache', {}),\r\n    lastSyncTime: safeGetFromStorage('lastSyncTime', null),\r\n\r\n    // 场景和笔记\r\n    scenes: safeGetObjectFromStorage('scenes', []),\r\n    currentSceneIndex: parseInt(safeGetFromStorage('currentSceneIndex', '0')),\r\n    notes: safeGetObjectFromStorage('notes', {}),\r\n\r\n    // 系统状态\r\n    apiValid: true,\r\n    isOfflineMode: false,\r\n    connectionStatus: 'online',\r\n    storageStatus: null, // 添加存储状态跟踪\r\n\r\n    // 游戏数据 (保留基本结构，供其他功能使用)\r\n    occupations: {},\r\n    skills: {},\r\n    weapons: {},\r\n    armors: {},\r\n\r\n    // 存档系统\r\n    saves: [],\r\n    currentSave: null,\r\n    loadingSaves: false,\r\n    saveError: null,\r\n    saveHistory: [],\r\n    loadingSaveHistory: false,\r\n    isPreviewMode: false,\r\n    globalNotice: null\r\n  },\r\n  getters: {\r\n    // 认证相关\r\n    isAuthenticated: state => {\r\n      if (!state.token || !state.user) return false;\r\n\r\n      // 检查token是否过期\r\n      if (state.tokenExpiry) {\r\n        const now = new Date().getTime();\r\n        const expiry = new Date(state.tokenExpiry).getTime();\r\n        if (now >= expiry) {\r\n          return false;\r\n        }\r\n      }\r\n\r\n      return true;\r\n    },\r\n\r\n    currentUser: state => state.user,\r\n    authLoading: state => state.authLoading,\r\n    authError: state => state.authError,\r\n    tokenValid: state => {\r\n      if (!state.token) return false;\r\n      if (!state.tokenExpiry) return true; // 如果没有过期时间，假设有效\r\n\r\n      const now = new Date().getTime();\r\n      const expiry = new Date(state.tokenExpiry).getTime();\r\n      return now < expiry;\r\n    },\r\n\r\n    // 角色相关\r\n    userCharacters: state => state.characters,\r\n    currentCharacter: state => {\r\n      if (!state.currentCharacterId) return null;\r\n      return state.characters.find(c => c.id == state.currentCharacterId) || null;\r\n    },\r\n    characterSyncStatus: state => characterId => {\r\n      return state.characterSyncStatus[characterId] || { lastSync: null, syncing: false };\r\n    },\r\n    getLocalCharacterData: state => characterId => {\r\n      return state.localCharacterCache[characterId] || null;\r\n    },\r\n\r\n    // 消息相关\r\n    roomMessages: state => state.messages,\r\n\r\n    // 场景和笔记\r\n    allScenes: state => state.scenes,\r\n    currentScene: state => state.scenes[state.currentSceneIndex] || null,\r\n    characterNotes: state => characterId => {\r\n      return state.notes[characterId] || [];\r\n    },\r\n    globalNotes: state => state.notes['global'] || [],\r\n\r\n    // 系统状态\r\n    currentTheme: state => state.theme,\r\n    isDarkTheme: state => state.theme === 'dark',\r\n    isOfflineMode: state => state.isOfflineMode,\r\n    connectionStatus: state => state.connectionStatus,\r\n    lastSyncTime: state => state.lastSyncTime,\r\n    storageStatus: state => state.storageStatus,\r\n    isStorageDegraded: state => state.storageStatus?.isDegraded || false,\r\n\r\n    // 存档相关\r\n    gameSaves: state => state.saves,\r\n    currentGameSave: state => state.currentSave,\r\n    isLoadingSaves: state => state.loadingSaves,\r\n    saveError: state => state.saveError,\r\n    saveHistory: state => state.saveHistory,\r\n    isLoadingSaveHistory: state => state.loadingSaveHistory,\r\n    isPreviewMode: state => state.isPreviewMode,\r\n    globalNotice: state => state.globalNotice\r\n  },\r\n  mutations: {\r\n    // 认证相关mutations\r\n    SET_USER(state, user) {\r\n      state.user = user;\r\n      state.isAuthenticated = !!user;\r\n      try {\r\n        if (user) {\r\n          storageManager.setObject('user', user);\r\n        } else {\r\n          storageManager.removeItem('user');\r\n        }\r\n      } catch (error) {\r\n        console.warn('[Store] 保存用户信息失败:', error);\r\n      }\r\n    },\r\n\r\n    SET_TOKEN(state, token) {\r\n      state.token = token;\r\n      try {\r\n        if (token) {\r\n          storageManager.setItem('token', token);\r\n\r\n          // 解析JWT token获取过期时间\r\n          try {\r\n            const payload = JSON.parse(atob(token.split('.')[1]));\r\n            if (payload.exp) {\r\n              const expiry = new Date(payload.exp * 1000).toISOString();\r\n              state.tokenExpiry = expiry;\r\n              storageManager.setItem('tokenExpiry', expiry);\r\n            }\r\n          } catch (e) {\r\n            console.warn('无法解析token过期时间:', e);\r\n          }\r\n        } else {\r\n          storageManager.removeItem('token');\r\n          storageManager.removeItem('tokenExpiry');\r\n          state.tokenExpiry = null;\r\n        }\r\n      } catch (error) {\r\n        console.warn('[Store] 保存token失败:', error);\r\n      }\r\n    },\r\n\r\n    SET_AUTH_LOADING(state, loading) {\r\n      state.authLoading = loading;\r\n    },\r\n\r\n    SET_AUTH_ERROR(state, error) {\r\n      state.authError = error;\r\n    },\r\n\r\n    CLEAR_AUTH(state) {\r\n      state.user = null;\r\n      state.token = '';\r\n      state.tokenExpiry = null;\r\n      state.isAuthenticated = false;\r\n      state.authError = null;\r\n\r\n      // 清除本地存储\r\n      try {\r\n        storageManager.removeItem('user');\r\n        storageManager.removeItem('token');\r\n        storageManager.removeItem('tokenExpiry');\r\n      } catch (error) {\r\n        console.warn('[Store] 清除认证信息失败:', error);\r\n      }\r\n    },\r\n\r\n    SET_CONNECTION_STATUS(state, status) {\r\n      state.connectionStatus = status;\r\n      if (status === 'offline') {\r\n        state.isOfflineMode = true;\r\n      }\r\n    },\r\n\r\n    SET_LAST_SYNC_TIME(state, time) {\r\n      state.lastSyncTime = time;\r\n      try {\r\n        storageManager.setItem('lastSyncTime', time);\r\n      } catch (error) {\r\n        console.warn('[Store] 保存同步时间失败:', error);\r\n      }\r\n    },\r\n\r\n    SET_STORAGE_STATUS(state, status) {\r\n      state.storageStatus = status;\r\n    },\r\n    UPDATE_USER_AVATAR(state, avatarUrl) {\r\n      if (state.user) {\r\n        state.user.avatar_url = avatarUrl;\r\n        // 同时更新存储中的用户信息\r\n        storageManager.setObject('user', state.user);\r\n      }\r\n    },\r\n    SET_CHARACTERS(state, characters) {\r\n      state.characters = characters;\r\n      try {\r\n        storageManager.setObject('characters', characters);\r\n      } catch (error) {\r\n        console.warn('[Store] 保存角色列表失败:', error);\r\n      }\r\n    },\r\n    ADD_CHARACTER(state, character) {\r\n      state.characters.push(character);\r\n      try {\r\n        storageManager.setObject('characters', state.characters);\r\n      } catch (error) {\r\n        console.warn('[Store] 保存新角色失败:', error);\r\n      }\r\n    },\r\n    UPDATE_CHARACTER(state, updatedCharacter) {\r\n      const index = state.characters.findIndex(c => c.id === updatedCharacter.id);\r\n      if (index !== -1) {\r\n        state.characters.splice(index, 1, updatedCharacter);\r\n        try {\r\n          storageManager.setObject('characters', state.characters);\r\n          \r\n          state.localCharacterCache[updatedCharacter.id] = {\r\n            ...updatedCharacter,\r\n            lastUpdated: new Date().toISOString()\r\n          };\r\n          storageManager.setObject('localCharacterCache', state.localCharacterCache);\r\n        } catch (error) {\r\n          console.warn('[Store] 保存角色更新失败:', error);\r\n        }\r\n        \r\n        state.characterSyncStatus[updatedCharacter.id] = {\r\n          lastSync: new Date().toISOString(),\r\n          syncing: false\r\n        };\r\n      }\r\n    },\r\n    UPDATE_CHARACTER_FROM_SERVER(state, characterData) {\r\n      const index = state.characters.findIndex(c => c.id === characterData.id);\r\n      if (index !== -1) {\r\n        const mergedCharacter = {\r\n          ...state.characters[index],\r\n          ...characterData,\r\n          _localData: state.characters[index]._localData || {}\r\n        };\r\n        \r\n        state.characters.splice(index, 1, mergedCharacter);\r\n        \r\n        storageManager.setObject('characters', state.characters);\r\n        \r\n        state.localCharacterCache[characterData.id] = {\r\n          ...mergedCharacter,\r\n          lastUpdated: new Date().toISOString()\r\n        };\r\n        storageManager.setObject('localCharacterCache', state.localCharacterCache);\r\n        \r\n        state.characterSyncStatus[characterData.id] = {\r\n          lastSync: new Date().toISOString(),\r\n          syncing: false\r\n        };\r\n      }\r\n    },\r\n    UPDATE_CHARACTER_ATTRIBUTE(state, { id, field, value }) {\r\n      const index = state.characters.findIndex(c => c.id === id);\r\n      if (index !== -1) {\r\n        const updatedCharacter = { ...state.characters[index] };\r\n        \r\n        if (field.includes('.')) {\r\n          const parts = field.split('.');\r\n          let obj = updatedCharacter;\r\n          for (let i = 0; i < parts.length - 1; i++) {\r\n            if (!obj[parts[i]]) obj[parts[i]] = {};\r\n            obj = obj[parts[i]];\r\n          }\r\n          obj[parts[parts.length - 1]] = value;\r\n        } else {\r\n          updatedCharacter[field] = value;\r\n        }\r\n        \r\n        state.characters.splice(index, 1, updatedCharacter);\r\n        \r\n        storageManager.setObject('characters', state.characters);\r\n        \r\n        state.localCharacterCache[id] = {\r\n          ...updatedCharacter,\r\n          lastUpdated: new Date().toISOString()\r\n        };\r\n        storageManager.setObject('localCharacterCache', state.localCharacterCache);\r\n      }\r\n    },\r\n    SET_CURRENT_CHARACTER(state, characterId) {\r\n      state.currentCharacterId = characterId;\r\n      storageManager.setItem('currentCharacterId', characterId);\r\n    },\r\n    DELETE_CHARACTER(state, characterId) {\r\n      state.characters = state.characters.filter(c => c.id !== characterId);\r\n      storageManager.setObject('characters', state.characters);\r\n      \r\n      if (state.localCharacterCache[characterId]) {\r\n        delete state.localCharacterCache[characterId];\r\n        storageManager.setObject('localCharacterCache', state.localCharacterCache);\r\n      }\r\n      \r\n      if (state.characterSyncStatus[characterId]) {\r\n        delete state.characterSyncStatus[characterId];\r\n      }\r\n    },\r\n    UPDATE_CHARACTER_SYNC_STATUS(state, { characterId, status }) {\r\n      state.characterSyncStatus[characterId] = {\r\n        ...state.characterSyncStatus[characterId],\r\n        ...status\r\n      };\r\n    },\r\n    SET_ROOMS(state, rooms) {\r\n      state.rooms = rooms;\r\n    },\r\n    SET_CURRENT_ROOM(state, roomData) {\r\n      state.currentRoom = roomData;\r\n    },\r\n    CLEAR_CURRENT_ROOM(state) {\r\n      state.currentRoom = null;\r\n      state.messages = [];\r\n    },\r\n    ADD_MESSAGE(state, message) {\r\n      console.log('添加消息到store:', message);\r\n      \r\n      if (!message.timestamp) {\r\n        message.timestamp = new Date().toISOString();\r\n      }\r\n      \r\n      if (message.type === 'chat' && !message.username) {\r\n        message.username = message.user_id === 1 ? 'KP' : '玩家';\r\n      }\r\n      \r\n      message.id = Date.now() + Math.random().toString(36).substr(2, 9);\r\n      \r\n      state.messages.push(message);\r\n      \r\n      if (state.messages.length > 1000) {\r\n        state.messages = state.messages.slice(-500);\r\n      }\r\n      \r\n      const roomId = state.currentRoom ? state.currentRoom.id : 'default';\r\n      const roomMessages = storageManager.getObject(`room_messages_${roomId}`) || [];\r\n      roomMessages.push(message);\r\n      \r\n      if (roomMessages.length > 200) {\r\n        roomMessages.splice(0, roomMessages.length - 200);\r\n      }\r\n      \r\n      storageManager.setObject(`room_messages_${roomId}`, roomMessages);\r\n    },\r\n    SET_MESSAGES(state, messages) {\r\n      state.messages = messages;\r\n      \r\n      const roomId = state.currentRoom ? state.currentRoom.id : 'default';\r\n      storageManager.setObject(`room_messages_${roomId}`, messages.slice(-200));\r\n    },\r\n    TOGGLE_THEME(state) {\r\n      state.theme = state.theme === 'dark' ? 'light' : 'dark';\r\n      storageManager.setItem('theme', state.theme);\r\n    },\r\n    CLEAR_MESSAGES(state) {\r\n      state.messages = [];\r\n      \r\n      const roomId = state.currentRoom ? state.currentRoom.id : 'default';\r\n      storageManager.removeItem(`room_messages_${roomId}`);\r\n    },\r\n    SET_IMPORTANT_MESSAGE(state, message) {\r\n      state.importantMessage = message;\r\n    },\r\n    SET_API_VALID(state, isValid) {\r\n      state.aiSettings.isApiValid = isValid;\r\n    },\r\n    UPDATE_AI_SETTINGS(state, settings) {\r\n      state.aiSettings = {\r\n        ...state.aiSettings,\r\n        ...settings\r\n      };\r\n    },\r\n    UPDATE_MESSAGE(state, { index, message }) {\r\n      if (index >= 0 && index < state.messages.length) {\r\n        state.messages.splice(index, 1, message);\r\n        \r\n        const roomId = state.currentRoom ? state.currentRoom.id : 'default';\r\n        const roomMessages = storageManager.getObject(`room_messages_${roomId}`) || [];\r\n        const msgIndex = roomMessages.findIndex(m => m.id === message.id);\r\n        if (msgIndex !== -1) {\r\n          roomMessages.splice(msgIndex, 1, message);\r\n          storageManager.setObject(`room_messages_${roomId}`, roomMessages);\r\n        }\r\n      }\r\n    },\r\n    REMOVE_LAST_MESSAGE(state) {\r\n      if (state.messages.length > 0) {\r\n        state.messages.pop();\r\n        \r\n        const roomId = state.currentRoom ? state.currentRoom.id : 'default';\r\n        const roomMessages = storageManager.getObject(`room_messages_${roomId}`) || [];\r\n        \r\n        if (roomMessages.length > 0) {\r\n          roomMessages.pop();\r\n          storageManager.setObject(`room_messages_${roomId}`, roomMessages);\r\n        }\r\n      }\r\n    },\r\n    SET_SCENES(state, scenes) {\r\n      state.scenes = scenes;\r\n      storageManager.setObject('scenes', scenes);\r\n    },\r\n    ADD_SCENE(state, scene) {\r\n      state.scenes.push(scene);\r\n      storageManager.setObject('scenes', state.scenes);\r\n    },\r\n    UPDATE_SCENE(state, { index, scene }) {\r\n      if (index >= 0 && index < state.scenes.length) {\r\n        state.scenes.splice(index, 1, scene);\r\n        storageManager.setObject('scenes', state.scenes);\r\n      }\r\n    },\r\n    DELETE_SCENE(state, index) {\r\n      if (index >= 0 && index < state.scenes.length) {\r\n        state.scenes.splice(index, 1);\r\n        storageManager.setObject('scenes', state.scenes);\r\n        \r\n        if (index <= state.currentSceneIndex && state.currentSceneIndex > 0) {\r\n          state.currentSceneIndex--;\r\n          storageManager.setItem('currentSceneIndex', state.currentSceneIndex.toString());\r\n        }\r\n      }\r\n    },\r\n    SET_CURRENT_SCENE_INDEX(state, index) {\r\n      if (index >= 0 && index < state.scenes.length) {\r\n        state.currentSceneIndex = index;\r\n        storageManager.setItem('currentSceneIndex', index.toString());\r\n      }\r\n    },\r\n    SET_NOTES(state, { characterId, notes }) {\r\n      const id = characterId || 'global';\r\n      state.notes[id] = notes;\r\n      storageManager.setObject('notes', state.notes);\r\n    },\r\n    ADD_NOTE(state, { characterId, note }) {\r\n      const id = characterId || 'global';\r\n      if (!state.notes[id]) {\r\n        state.notes[id] = [];\r\n      }\r\n      state.notes[id].unshift(note);\r\n      storageManager.setObject('notes', state.notes);\r\n    },\r\n    UPDATE_NOTE(state, { characterId, index, note }) {\r\n      const id = characterId || 'global';\r\n      if (state.notes[id] && index >= 0 && index < state.notes[id].length) {\r\n        state.notes[id].splice(index, 1, note);\r\n        storageManager.setObject('notes', state.notes);\r\n      }\r\n    },\r\n    DELETE_NOTE(state, { characterId, index }) {\r\n      const id = characterId || 'global';\r\n      if (state.notes[id] && index >= 0 && index < state.notes[id].length) {\r\n        state.notes[id].splice(index, 1);\r\n        storageManager.setObject('notes', state.notes);\r\n      }\r\n    },\r\n    SET_OFFLINE_MODE(state, isOffline) {\r\n      state.isOfflineMode = isOffline;\r\n    },\r\n    SET_SAVES(state, saves) {\r\n      state.saves = saves;\r\n    },\r\n    SET_CURRENT_SAVE(state, save) {\r\n      state.currentSave = save;\r\n    },\r\n    ADD_SAVE(state, save) {\r\n      state.saves.push(save);\r\n    },\r\n    UPDATE_SAVE(state, updatedSave) {\r\n      const index = state.saves.findIndex(save => save.id === updatedSave.id);\r\n      if (index !== -1) {\r\n        state.saves[index] = updatedSave;\r\n      }\r\n    },\r\n    REMOVE_SAVE(state, saveId) {\r\n      state.saves = state.saves.filter(save => save.id !== saveId);\r\n    },\r\n    SET_LOADING_SAVES(state, isLoading) {\r\n      state.loadingSaves = isLoading;\r\n    },\r\n    SET_SAVE_ERROR(state, error) {\r\n      state.saveError = error;\r\n    },\r\n    SET_SAVE_HISTORY(state, history) {\r\n      state.saveHistory = history;\r\n    },\r\n    SET_LOADING_SAVE_HISTORY(state, isLoading) {\r\n      state.loadingSaveHistory = isLoading;\r\n    },\r\n    SET_PREVIEW_MODE(state, isPreview) {\r\n      state.isPreviewMode = isPreview;\r\n    },\r\n    SET_GLOBAL_NOTICE(state, notice) {\r\n      state.globalNotice = notice;\r\n    },\r\n    CLEAR_GLOBAL_NOTICE(state) {\r\n      state.globalNotice = null;\r\n    },\r\n\r\n    // 游戏数据mutations\r\n    SET_OCCUPATIONS(state, occupations) {\r\n      state.occupations = occupations || {};\r\n    },\r\n    SET_SKILLS(state, skills) {\r\n      state.skills = skills || {};\r\n    },\r\n    SET_WEAPONS(state, weapons) {\r\n      state.weapons = weapons || {};\r\n    },\r\n    SET_ARMORS(state, armors) {\r\n      state.armors = armors || {};\r\n    },\r\n\r\n    \r\n    // 更新当前房间的名称\r\n    UPDATE_ROOM_NAME(state, { roomId, name }) {\r\n      if (state.currentRoom && state.currentRoom.id === roomId) {\r\n        state.currentRoom.name = name;\r\n      }\r\n    },\r\n    \r\n    // 更新房间列表中的房间名称\r\n    UPDATE_ROOM_LIST_NAME(state, { roomId, name }) {\r\n      if (state.rooms && state.rooms.length > 0) {\r\n        const roomIndex = state.rooms.findIndex(room => room.id === roomId);\r\n        if (roomIndex !== -1) {\r\n          state.rooms[roomIndex].name = name;\r\n        }\r\n      }\r\n    }\r\n  },\r\n  actions: {\r\n    async register({ commit, dispatch }, userData) {\r\n      commit('SET_AUTH_LOADING', true);\r\n      commit('SET_AUTH_ERROR', null);\r\n\r\n      try {\r\n        console.log('🚀 开始注册用户:', userData.email);\r\n\r\n        // 验证输入数据\r\n        if (!userData.username || !userData.email || !userData.password) {\r\n          throw new Error('请填写完整的注册信息');\r\n        }\r\n\r\n        // 调用注册API\r\n        const response = await axios.post('/api/users/register', userData);\r\n        console.log('✅ API注册成功:', response.data);\r\n\r\n        const { token, user } = response.data;\r\n\r\n        if (!token || !user) {\r\n          throw new Error('服务器返回数据格式错误');\r\n        }\r\n\r\n        // 更新状态\r\n        commit('SET_TOKEN', token);\r\n        commit('SET_USER', user);\r\n        commit('SET_CONNECTION_STATUS', 'online');\r\n        commit('SET_LAST_SYNC_TIME', new Date().toISOString());\r\n\r\n        console.log('✅ 注册成功，用户ID:', user.id);\r\n        return user;\r\n\r\n      } catch (error) {\r\n        console.error('❌ 注册失败:', error);\r\n\r\n        let errorMessage = '注册失败，请稍后重试';\r\n\r\n        if (error.response && error.response.data) {\r\n          const errorDetail = error.response.data.detail;\r\n          if (errorDetail === \"邮箱已被注册\") {\r\n            errorMessage = '邮箱已被注册';\r\n          } else if (errorDetail === \"用户名已被注册\") {\r\n            errorMessage = '用户名已被注册';\r\n          } else if (errorDetail) {\r\n            errorMessage = errorDetail;\r\n          }\r\n        } else if (error.code === 'NETWORK_ERROR' || !error.response) {\r\n          errorMessage = '网络连接失败，请检查网络连接';\r\n          commit('SET_CONNECTION_STATUS', 'offline');\r\n        } else if (error.message) {\r\n          errorMessage = error.message;\r\n        }\r\n\r\n        commit('SET_AUTH_ERROR', errorMessage);\r\n        throw new Error(errorMessage);\r\n\r\n      } finally {\r\n        commit('SET_AUTH_LOADING', false);\r\n      }\r\n    },\r\n    \r\n    async login({ commit, dispatch }, credentials) {\r\n      commit('SET_AUTH_LOADING', true);\r\n      commit('SET_AUTH_ERROR', null);\r\n\r\n      try {\r\n        console.log('🚀 开始用户登录:', credentials.email);\r\n\r\n        // 验证输入数据\r\n        if (!credentials.email || !credentials.password) {\r\n          throw new Error('邮箱和密码不能为空');\r\n        }\r\n\r\n        // 调用登录API\r\n        const response = await axios.post('/api/users/login', credentials);\r\n        console.log('✅ API登录成功:', response.data);\r\n\r\n        const { token, user } = response.data;\r\n\r\n        if (!token || !user) {\r\n          throw new Error('服务器返回数据格式错误');\r\n        }\r\n\r\n        // 更新状态\r\n        commit('SET_TOKEN', token);\r\n        commit('SET_USER', user);\r\n        commit('SET_CONNECTION_STATUS', 'online');\r\n        commit('SET_LAST_SYNC_TIME', new Date().toISOString());\r\n\r\n        // 登录成功后，尝试加载用户数据\r\n        try {\r\n          await dispatch('fetchCharacters');\r\n        } catch (fetchError) {\r\n          console.warn('获取角色数据失败:', fetchError);\r\n          // 不阻止登录流程\r\n        }\r\n\r\n        console.log('✅ 登录成功，用户ID:', user.id);\r\n        return user;\r\n\r\n      } catch (error) {\r\n        console.error('❌ 登录失败:', error);\r\n\r\n        let errorMessage = '登录失败，请稍后重试';\r\n\r\n        if (error.response && error.response.data) {\r\n          const errorDetail = error.response.data.detail;\r\n          if (errorDetail === '邮箱或密码错误') {\r\n            errorMessage = '邮箱或密码错误';\r\n          } else if (errorDetail === '账户已被禁用') {\r\n            errorMessage = '您的账户已被禁用，请联系管理员';\r\n          } else if (errorDetail) {\r\n            errorMessage = errorDetail;\r\n          }\r\n        } else if (error.code === 'NETWORK_ERROR' || !error.response) {\r\n          errorMessage = '网络连接失败，请检查网络连接';\r\n          commit('SET_CONNECTION_STATUS', 'offline');\r\n        } else if (error.message) {\r\n          errorMessage = error.message;\r\n        }\r\n\r\n        commit('SET_AUTH_ERROR', errorMessage);\r\n        throw new Error(errorMessage);\r\n\r\n      } finally {\r\n        commit('SET_AUTH_LOADING', false);\r\n      }\r\n    },\r\n    \r\n    logout({ commit }) {\r\n      console.log('🚪 用户登出');\r\n      commit('CLEAR_AUTH');\r\n\r\n      // 清除所有相关的本地数据\r\n      storageManager.removeItem('characters');\r\n      storageManager.removeItem('currentCharacterId');\r\n      storageManager.removeItem('localCharacterCache');\r\n      storageManager.removeItem('lastSyncTime');\r\n\r\n      // 重置其他状态\r\n      commit('SET_CHARACTERS', []);\r\n      commit('SET_CURRENT_CHARACTER', null);\r\n      commit('CLEAR_CURRENT_ROOM');\r\n    },\r\n\r\n    // 验证token有效性\r\n    async validateToken({ commit, state, dispatch }) {\r\n      if (!state.token) {\r\n        return false;\r\n      }\r\n\r\n      try {\r\n        // 检查token是否过期\r\n        if (state.tokenExpiry) {\r\n          const now = new Date().getTime();\r\n          const expiry = new Date(state.tokenExpiry).getTime();\r\n          if (now >= expiry) {\r\n            console.log('🔒 Token已过期，自动登出');\r\n            dispatch('logout');\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // 尝试获取当前用户信息验证token\r\n        const response = await axios.get('/api/users/me', {\r\n          headers: { Authorization: `Bearer ${state.token}` }\r\n        });\r\n\r\n        if (response.data) {\r\n          commit('SET_USER', response.data);\r\n          commit('SET_CONNECTION_STATUS', 'online');\r\n          return true;\r\n        }\r\n\r\n        return false;\r\n\r\n      } catch (error) {\r\n        console.error('❌ Token验证失败:', error);\r\n\r\n        if (error.response && error.response.status === 401) {\r\n          // Token无效，自动登出\r\n          dispatch('logout');\r\n        } else {\r\n          // 网络错误，设置离线模式\r\n          commit('SET_CONNECTION_STATUS', 'offline');\r\n        }\r\n\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 初始化存储系统\r\n    initializeStorage({ commit }) {\r\n      try {\r\n        // 获取存储状态\r\n        const storageStatus = storageManager.getStatus();\r\n        commit('SET_STORAGE_STATUS', storageStatus);\r\n        \r\n        console.log('[Store] 存储系统初始化完成:', storageStatus);\r\n        \r\n        // 如果存储降级，显示提示\r\n        if (storageStatus.isDegraded) {\r\n          console.warn('[Store] 存储系统处于降级模式:', storageStatus.storageType);\r\n        }\r\n        \r\n        return true;\r\n      } catch (error) {\r\n        console.error('[Store] 存储系统初始化失败:', error);\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 初始化应用状态\r\n    async initializeApp({ commit, dispatch, state }) {\r\n      console.log('🚀 初始化应用状态');\r\n\r\n      try {\r\n        // 首先初始化存储系统\r\n        dispatch('initializeStorage');\r\n        \r\n        // 检查是否有保存的认证信息\r\n        if (state.token && state.user) {\r\n          console.log('📱 发现本地认证信息，验证token');\r\n          const isValid = await dispatch('validateToken');\r\n\r\n          if (isValid) {\r\n            console.log('✅ Token有效，自动登录成功');\r\n\r\n            // 尝试加载用户数据\r\n            try {\r\n              await dispatch('fetchCharacters');\r\n            } catch (error) {\r\n              console.warn('获取角色数据失败:', error);\r\n            }\r\n\r\n            return true;\r\n          }\r\n        }\r\n\r\n        console.log('❌ 无有效认证信息');\r\n        return false;\r\n\r\n      } catch (error) {\r\n        console.error('❌ 应用初始化失败:', error);\r\n        commit('SET_CONNECTION_STATUS', 'offline');\r\n        return false;\r\n      }\r\n    },\r\n\r\n    // 刷新用户数据\r\n    async refreshUserData({ commit, dispatch, state }) {\r\n      if (!state.token) {\r\n        return false;\r\n      }\r\n\r\n      try {\r\n        const isValid = await dispatch('validateToken');\r\n        if (isValid) {\r\n          await dispatch('fetchCharacters');\r\n          commit('SET_LAST_SYNC_TIME', new Date().toISOString());\r\n          return true;\r\n        }\r\n        return false;\r\n      } catch (error) {\r\n        console.error('刷新用户数据失败:', error);\r\n        return false;\r\n      }\r\n    },\r\n    \r\n    async fetchCharacters({ commit, state }) {\r\n      try {\r\n        console.log('🔍 开始获取角色列表，用户ID:', state.user?.id);\r\n\r\n        if (!state.user || !state.user.id) {\r\n          throw new Error('用户未登录或用户ID不存在');\r\n        }\r\n\r\n        const response = await apiService.characters.getUserCharacters(state.user.id);\r\n        console.log('✅ 角色列表获取成功:', response.data);\r\n\r\n        // 后端返回格式: {success: true, data: [角色数组]}\r\n        const characters = response.data.data || response.data;\r\n        console.log('📋 解析后的角色数据:', characters);\r\n\r\n        commit('SET_CHARACTERS', characters);\r\n        return characters;\r\n      } catch (error) {\r\n        console.error('❌ 获取角色列表失败:', error);\r\n        console.error('错误详情:', error.response?.data || error.message);\r\n        throw error;\r\n      }\r\n    },\r\n    \r\n    async updateCharacter({ commit, state }, { id, ...characterData }) {\r\n      try {\r\n        const response = await axios.put(`/api/characters/${id}`, characterData, {\r\n          headers: { Authorization: `Bearer ${state.token}` }\r\n        });\r\n        \r\n        commit('UPDATE_CHARACTER', response.data);\r\n        \r\n        import('@/services/websocket').then(module => {\r\n          const websocketService = module.default;\r\n          if (websocketService.isConnected) {\r\n            const updatedCharacter = state.characters.find(c => c.id === id);\r\n            if (updatedCharacter) {\r\n              websocketService.sendCharacterUpdate(updatedCharacter);\r\n            }\r\n          }\r\n        });\r\n        \r\n        return response.data;\r\n      } catch (error) {\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async deleteCharacter({ commit, state }, characterId) {\r\n      try {\r\n        console.log('🗑️ 删除角色:', characterId);\r\n\r\n        const response = await apiService.characters.deleteCharacter(characterId);\r\n\r\n        if (response.status === 200) {\r\n          commit('DELETE_CHARACTER', characterId);\r\n          console.log('✅ 角色删除成功');\r\n          return true;\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ 删除角色失败:', error);\r\n        console.error('错误详情:', error.response?.data || error.message);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async fetchRooms({ commit, state }) {\r\n      try {\r\n        console.log('正在获取房间列表...');\r\n        const response = await axios.get('/api/rooms/', {\r\n          headers: { Authorization: `Bearer ${state.token}` }\r\n        });\r\n        \r\n        console.log('获取房间成功:', response.data);\r\n        commit('SET_ROOMS', response.data);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('获取房间失败:', error.message, error.response?.status);\r\n        \r\n        if (error.message.includes('Network Error') || error.code === 'ECONNREFUSED') {\r\n          console.log('使用模拟数据');\r\n          const mockRooms = [\r\n            { id: 1, name: '模拟房间1', description: '后端未连接，使用模拟数据', keeper_id: 1, is_private: false },\r\n            { id: 2, name: '模拟房间2', description: '请确保后端服务已启动', keeper_id: 1, is_private: false }\r\n          ];\r\n          commit('SET_ROOMS', mockRooms);\r\n          return mockRooms;\r\n        }\r\n        \r\n        throw error;\r\n      }\r\n    },\r\n    \r\n    async createRoom({ commit, state }, roomData) {\r\n      try {\r\n        const response = await axios.post('/api/rooms/', {\r\n          ...roomData,\r\n          keeper_id: state.user.id\r\n        }, {\r\n          headers: { Authorization: `Bearer ${state.token}` }\r\n        });\r\n        \r\n        const newRoom = response.data;\r\n        commit('SET_ROOMS', [...state.rooms, newRoom]);\r\n        \r\n        return newRoom;\r\n      } catch (error) {\r\n        throw error;\r\n      }\r\n    },\r\n    \r\n    async joinRoom({ commit }, { roomId }) {\r\n      try {\r\n        // 确保roomId是有效的数字\r\n        const numericRoomId = parseInt(roomId);\r\n        \r\n        if (isNaN(numericRoomId)) {\r\n          console.error('无效的房间ID:', roomId);\r\n          throw new Error('无效的房间ID');\r\n        }\r\n        \r\n        console.log(`尝试加入房间，ID: ${numericRoomId}`);\r\n        \r\n        try {\r\n          const response = await apiService.getRoom(numericRoomId);\r\n          if (response.data) {\r\n            // 确保房间数据中的ID是数字类型\r\n            const roomData = {\r\n              ...response.data,\r\n              id: parseInt(response.data.id)\r\n            };\r\n            console.log('从API获取的房间数据:', roomData);\r\n            commit('SET_CURRENT_ROOM', roomData);\r\n            return roomData;\r\n          } else {\r\n            throw new Error('房间不存在');\r\n          }\r\n        } catch (error) {\r\n          console.error('加入房间失败:', error);\r\n          // 创建模拟房间\r\n          const mockRoom = { \r\n            id: numericRoomId, \r\n            name: `测试房间 ${numericRoomId}`, \r\n            description: '这是一个自动创建的房间',\r\n            keeper_id: 1,\r\n            is_private: false,\r\n            is_mock: true\r\n          };\r\n          console.log('创建模拟房间:', mockRoom);\r\n          commit('SET_CURRENT_ROOM', mockRoom);\r\n          return mockRoom; // 返回模拟房间，不抛出错误\r\n        }\r\n      } catch (error) {\r\n        console.error('处理房间ID失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    \r\n    leaveRoom({ commit }) {\r\n      commit('CLEAR_CURRENT_ROOM');\r\n    },\r\n    \r\n    async updateRoomName({ commit, state }, { roomId, name }) {\r\n      try {\r\n        // 如果是当前房间，更新当前房间名称\r\n        if (state.currentRoom && state.currentRoom.id === roomId) {\r\n          commit('UPDATE_ROOM_NAME', { roomId, name });\r\n        }\r\n        \r\n        // 更新房间列表中的房间名称\r\n        if (state.rooms && state.rooms.length > 0) {\r\n          commit('UPDATE_ROOM_LIST_NAME', { roomId, name });\r\n        }\r\n        \r\n        // 保存到本地存储\r\n        try {\r\n          if (window.storageManager) {\r\n            window.storageManager.setItem(`room_name_${roomId}`, name);\r\n          } else {\r\n            localStorage.setItem(`room_name_${roomId}`, name);\r\n          }\r\n        } catch (error) {\r\n          console.warn('[Store] 无法保存房间名称:', error.message);\r\n        }\r\n        \r\n        // 注释掉API调用，因为后端不支持此功能\r\n        // 仅依赖前端存储和WebSocket通信\r\n        /*\r\n        try {\r\n          await apiService.updateRoom(roomId, { name });\r\n        } catch (apiError) {\r\n          console.log('API更新房间名称失败，但不影响前端功能:', apiError.message);\r\n        }\r\n        */\r\n        \r\n        return true;\r\n      } catch (error) {\r\n        console.error('更新房间名称失败:', error);\r\n        // 即使出错，我们仍然保留本地更改\r\n        return false;\r\n      }\r\n    },\r\n    \r\n    toggleTheme({ commit }) {\r\n      commit('TOGGLE_THEME');\r\n    },\r\n    \r\n    addMessage({ commit, rootState }, message) {\r\n      commit('ADD_MESSAGE', message);\r\n      \r\n      emitter.emit('new-message', message);\r\n      \r\n      return message;\r\n    },\r\n    \r\n    clearMessages({ commit }) {\r\n      commit('CLEAR_MESSAGES');\r\n    },\r\n    \r\n    updateMessage({ commit }, { index, message }) {\r\n      commit('UPDATE_MESSAGE', { index, message });\r\n    },\r\n    \r\n    removeLastMessage({ commit }) {\r\n      commit('REMOVE_LAST_MESSAGE');\r\n    },\r\n    \r\n    updateCharacterFromServer({ commit }, characterData) {\r\n      commit('UPDATE_CHARACTER_FROM_SERVER', characterData);\r\n    },\r\n    \r\n    updateCharacterAttribute({ commit, state, dispatch }, { id, field, value }) {\r\n      commit('UPDATE_CHARACTER_ATTRIBUTE', { id, field, value });\r\n      \r\n      import('@/services/websocket').then(module => {\r\n        const websocketService = module.default;\r\n        if (websocketService.isConnected) {\r\n          websocketService.sendCharacterAttributeUpdate(id, field, value);\r\n        }\r\n      });\r\n    },\r\n    \r\n    setCurrentCharacter({ commit, dispatch }, characterId) {\r\n      commit('SET_CURRENT_CHARACTER', characterId);\r\n      \r\n      dispatch('syncCharacterData', characterId);\r\n    },\r\n    \r\n    syncCharacterData({ commit, state }, characterId) {\r\n      commit('UPDATE_CHARACTER_SYNC_STATUS', { \r\n        characterId, \r\n        status: { syncing: true } \r\n      });\r\n      \r\n      import('@/services/websocket').then(module => {\r\n        const websocketService = module.default;\r\n        if (websocketService.isConnected) {\r\n          websocketService.requestCharacterSync(characterId);\r\n        } else {\r\n          const cachedData = state.localCharacterCache[characterId];\r\n          if (cachedData) {\r\n            commit('UPDATE_CHARACTER_FROM_SERVER', cachedData);\r\n          }\r\n          \r\n          commit('UPDATE_CHARACTER_SYNC_STATUS', { \r\n            characterId, \r\n            status: { \r\n              syncing: false,\r\n              lastSync: new Date().toISOString(),\r\n              error: 'WebSocket未连接，使用本地缓存'\r\n            } \r\n          });\r\n        }\r\n      });\r\n    },\r\n    \r\n    loadRoomMessages({ commit, state }) {\r\n      if (!state.currentRoom) return;\r\n      \r\n      const roomId = state.currentRoom.id;\r\n      const storedMessages = storageManager.getItem(`room_messages_${roomId}`);\r\n      \r\n      if (storedMessages) {\r\n        try {\r\n          const messages = JSON.parse(storedMessages);\r\n          commit('SET_MESSAGES', messages);\r\n        } catch (error) {\r\n          console.error('加载房间消息失败:', error);\r\n        }\r\n      }\r\n    },\r\n    \r\n    setScenes({ commit }, scenes) {\r\n      commit('SET_SCENES', scenes);\r\n    },\r\n    \r\n    addScene({ commit }, scene) {\r\n      commit('ADD_SCENE', scene);\r\n    },\r\n    \r\n    updateScene({ commit }, { index, scene }) {\r\n      commit('UPDATE_SCENE', { index, scene });\r\n    },\r\n    \r\n    deleteScene({ commit }, index) {\r\n      commit('DELETE_SCENE', index);\r\n    },\r\n    \r\n    setCurrentSceneIndex({ commit }, index) {\r\n      commit('SET_CURRENT_SCENE_INDEX', index);\r\n    },\r\n    \r\n    setNotes({ commit }, { characterId, notes }) {\r\n      commit('SET_NOTES', { characterId, notes });\r\n    },\r\n    \r\n    addNote({ commit }, { characterId, note }) {\r\n      if (!note.createdAt) {\r\n        note.createdAt = new Date().toISOString();\r\n      }\r\n      commit('ADD_NOTE', { characterId, note });\r\n    },\r\n    \r\n    updateNote({ commit }, { characterId, index, note }) {\r\n      note.updatedAt = new Date().toISOString();\r\n      commit('UPDATE_NOTE', { characterId, index, note });\r\n    },\r\n    \r\n    deleteNote({ commit }, { characterId, index }) {\r\n      commit('DELETE_NOTE', { characterId, index });\r\n    },\r\n    \r\n    loadSceneData({ commit, state }) {\r\n      const scenes = JSON.parse(storageManager.getItem('scenes') || '[]');\r\n      if (scenes.length > 0) {\r\n        commit('SET_SCENES', scenes);\r\n      }\r\n      \r\n      const currentSceneIndex = parseInt(storageManager.getItem('currentSceneIndex') || '0');\r\n      if (currentSceneIndex >= 0 && currentSceneIndex < scenes.length) {\r\n        commit('SET_CURRENT_SCENE_INDEX', currentSceneIndex);\r\n      }\r\n      \r\n      const notes = JSON.parse(storageManager.getItem('notes') || '{}');\r\n      if (notes) {\r\n        Object.keys(notes).forEach(characterId => {\r\n          commit('SET_NOTES', { characterId, notes: notes[characterId] });\r\n        });\r\n      }\r\n    },\r\n    \r\n    setOfflineMode({ commit }, isOffline) {\r\n      commit('SET_OFFLINE_MODE', isOffline);\r\n      \r\n      if (isOffline) {\r\n        console.log('已启用离线模式');\r\n        // 发送系统消息通知用户\r\n        this.dispatch('addMessage', {\r\n          type: 'system',\r\n          content: '已启用离线模式，部分功能可能不可用',\r\n          timestamp: new Date().toISOString()\r\n        });\r\n      }\r\n    },\r\n    \r\n    async fetchSaves({ commit }, { roomId, includeAutoSaves = true }) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.getRoomSaves(roomId, 0, 100, includeAutoSaves);\r\n        commit('SET_SAVES', response.data || []);\r\n        return response.data || [];\r\n      } catch (error) {\r\n        console.error('获取存档列表失败:', error);\r\n        commit('SET_SAVE_ERROR', '获取存档列表失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n    \r\n    async createSave({ commit }, saveData) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.createSave(saveData);\r\n        commit('ADD_SAVE', response.data);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('创建存档失败:', error);\r\n        commit('SET_SAVE_ERROR', '创建存档失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n    \r\n    async updateSave({ commit }, { saveId, updateData }) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.updateSave(saveId, updateData);\r\n        commit('UPDATE_SAVE', response.data);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('更新存档失败:', error);\r\n        commit('SET_SAVE_ERROR', '更新存档失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n    \r\n    async deleteSave({ commit }, saveId) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.deleteSave(saveId);\r\n        if (response.data && response.data.success) {\r\n          commit('REMOVE_SAVE', saveId);\r\n          return true;\r\n        }\r\n        return false;\r\n      } catch (error) {\r\n        console.error('删除存档失败:', error);\r\n        commit('SET_SAVE_ERROR', '删除存档失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n    \r\n    async createAutoSave({ commit }, { roomId, creatorId, saveData, previousAutoSaveId }) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.createAutoSave(\r\n          roomId,\r\n          creatorId,\r\n          saveData,\r\n          previousAutoSaveId\r\n        );\r\n        \r\n        if (previousAutoSaveId) {\r\n          commit('UPDATE_SAVE', response.data);\r\n        } else {\r\n          commit('ADD_SAVE', response.data);\r\n        }\r\n        \r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('创建自动存档失败:', error);\r\n        commit('SET_SAVE_ERROR', '创建自动存档失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n    \r\n    async fetchSaveHistory({ commit }, saveId) {\r\n      commit('SET_LOADING_SAVE_HISTORY', true);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.getSaveHistory(saveId);\r\n        commit('SET_SAVE_HISTORY', response.data || []);\r\n        return response.data || [];\r\n      } catch (error) {\r\n        console.error('获取存档历史记录失败:', error);\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVE_HISTORY', false);\r\n      }\r\n    },\r\n    \r\n    async getSaveHistoryVersion({ commit }, { saveId, version }) {\r\n      try {\r\n        const response = await apiService.gameSaves.getSaveHistoryVersion(saveId, version);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('获取历史版本失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n    \r\n    async restoreVersion({ commit }, { saveId, version }) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        const response = await apiService.gameSaves.restoreVersion(saveId, version);\r\n        commit('UPDATE_SAVE', response.data);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('恢复历史版本失败:', error);\r\n        commit('SET_SAVE_ERROR', '恢复历史版本失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n    \r\n    async loadSave({ commit, dispatch }, save) {\r\n      commit('SET_LOADING_SAVES', true);\r\n      commit('SET_SAVE_ERROR', null);\r\n      \r\n      try {\r\n        // 设置当前存档\r\n        commit('SET_CURRENT_SAVE', save);\r\n        \r\n        // 从存档数据中恢复状态\r\n        const saveData = save.save_data;\r\n        \r\n        // 恢复各种状态\r\n        if (saveData.messages) {\r\n          commit('SET_MESSAGES', saveData.messages);\r\n        }\r\n        \r\n        if (saveData.characters) {\r\n          commit('SET_CHARACTERS', saveData.characters);\r\n        }\r\n        \r\n        if (saveData.scenes) {\r\n          commit('SET_SCENES', saveData.scenes);\r\n          if (saveData.currentScene !== undefined) {\r\n            commit('SET_CURRENT_SCENE_INDEX', saveData.currentScene);\r\n          }\r\n        }\r\n        \r\n        if (saveData.diceHistory) {\r\n          commit('SET_DICE_HISTORY', saveData.diceHistory);\r\n        }\r\n        \r\n        if (saveData.notes) {\r\n          commit('SET_NOTES', saveData.notes);\r\n        }\r\n        \r\n        if (saveData.clues) {\r\n          commit('SET_CLUES', saveData.clues);\r\n        }\r\n        \r\n        if (saveData.aiSettings) {\r\n          commit('SET_AI_SETTINGS', saveData.aiSettings);\r\n        }\r\n        \r\n        return true;\r\n      } catch (error) {\r\n        console.error('加载存档失败:', error);\r\n        commit('SET_SAVE_ERROR', '加载存档失败');\r\n        throw error;\r\n      } finally {\r\n        commit('SET_LOADING_SAVES', false);\r\n      }\r\n    },\r\n\r\n\r\n\r\n    async createCharacter({ commit, dispatch, state }, characterData) {\r\n      try {\r\n        console.log('🎭 创建角色:', characterData.name);\r\n\r\n        // 获取当前用户ID\r\n        const userId = state.user?.id || 1;\r\n        console.log('使用用户ID:', userId);\r\n\r\n        const response = await apiService.characters.createCharacter(characterData, userId);\r\n\r\n        if (response.data) {\r\n          // 添加到角色列表\r\n          commit('ADD_CHARACTER', response.data);\r\n          console.log('✅ 角色创建成功:', response.data.name);\r\n          return response.data;\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ 角色创建失败:', error);\r\n        throw error;\r\n      }\r\n    },\r\n\r\n    async loadUserCharacters({ commit, state }) {\r\n      try {\r\n        if (!state.user || !state.user.id) {\r\n          console.log('⚠️ 用户未登录，跳过加载角色');\r\n          return;\r\n        }\r\n\r\n        const response = await apiService.characters.getUserCharacters(state.user.id);\r\n        if (response.data) {\r\n          commit('SET_CHARACTERS', response.data);\r\n          console.log('✅ 用户角色加载成功:', response.data.length, '个角色');\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ 用户角色加载失败:', error);\r\n      }\r\n    },\r\n\r\n    async calculateSkillPoints({ state }, characterData) {\r\n      try {\r\n        const response = await apiService.characters.calculateSkillPoints(characterData);\r\n        return response.data;\r\n      } catch (error) {\r\n        console.error('❌ 技能点计算失败:', error);\r\n        throw error;\r\n      }\r\n    }\r\n  },\r\n  modules: {\r\n    auth,\r\n    rooms\r\n  },\r\n  plugins: [persistencePlugin]\r\n}) "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,WAAW,QAAQ,MAAM;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,EAAE,IAAIC,MAAM,QAAQ,MAAM;AACnC,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,cAAc,MAAM,iBAAiB;AAC5C,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,IAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,uBAAuB;;AAEtE;AACA,SAASC,kBAAkBA,CAACC,GAAG,EAAuB;EAAA,IAArBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EAClD,IAAI;IACF,OAAOX,cAAc,CAACc,OAAO,CAACL,GAAG,CAAC,IAAIC,YAAY;EACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,2CAAAC,MAAA,CAAkBT,GAAG,qBAAQM,KAAK,CAAC;IAC/C,OAAOL,YAAY;EACrB;AACF;AAEA,SAASS,wBAAwBA,CAACV,GAAG,EAAuB;EAAA,IAArBC,YAAY,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACxD,IAAI;IACF,OAAOX,cAAc,CAACoB,SAAS,CAACX,GAAG,CAAC,IAAIC,YAAY;EACtD,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,iDAAAC,MAAA,CAAmBT,GAAG,qBAAQM,KAAK,CAAC;IAChD,OAAOL,YAAY;EACrB;AACF;AAEA,eAAehB,WAAW,CAAC;EACzB2B,KAAK,EAAE;IACL;IACAC,IAAI,EAAEH,wBAAwB,CAAC,MAAM,EAAE,IAAI,CAAC;IAC5CI,KAAK,EAAEf,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC;IACtCgB,eAAe,EAAE,KAAK;IACtBC,WAAW,EAAE,KAAK;IAClBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAEnB,kBAAkB,CAAC,aAAa,EAAE,IAAI,CAAC;IAEpD;IACAoB,UAAU,EAAET,wBAAwB,CAAC,YAAY,EAAE,EAAE,CAAC;IACtDU,kBAAkB,EAAErB,kBAAkB,CAAC,oBAAoB,EAAE,IAAI,CAAC;IAClEsB,QAAQ,EAAE,EAAE;IAEZ;IACAC,KAAK,EAAE/B,cAAc,CAACc,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM;IAChDkB,UAAU,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE;IACd,CAAC;IAED;IACAC,mBAAmB,EAAE,CAAC,CAAC;IACvBC,mBAAmB,EAAEnB,wBAAwB,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC;IACxEoB,YAAY,EAAE/B,kBAAkB,CAAC,cAAc,EAAE,IAAI,CAAC;IAEtD;IACAgC,MAAM,EAAErB,wBAAwB,CAAC,QAAQ,EAAE,EAAE,CAAC;IAC9CsB,iBAAiB,EAAEC,QAAQ,CAAClC,kBAAkB,CAAC,mBAAmB,EAAE,GAAG,CAAC,CAAC;IACzEmC,KAAK,EAAExB,wBAAwB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;IAE5C;IACAyB,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE,KAAK;IACpBC,gBAAgB,EAAE,QAAQ;IAC1BC,aAAa,EAAE,IAAI;IAAE;;IAErB;IACAC,WAAW,EAAE,CAAC,CAAC;IACfC,MAAM,EAAE,CAAC,CAAC;IACVC,OAAO,EAAE,CAAC,CAAC;IACXC,MAAM,EAAE,CAAC,CAAC;IAEV;IACAC,KAAK,EAAE,EAAE;IACTC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,KAAK;IACnBC,SAAS,EAAE,IAAI;IACfC,WAAW,EAAE,EAAE;IACfC,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDC,OAAO,EAAE;IACP;IACApC,eAAe,EAAE,SAAjBA,eAAeA,CAAEH,KAAK,EAAI;MACxB,IAAI,CAACA,KAAK,CAACE,KAAK,IAAI,CAACF,KAAK,CAACC,IAAI,EAAE,OAAO,KAAK;;MAE7C;MACA,IAAID,KAAK,CAACM,WAAW,EAAE;QACrB,IAAMkC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;QAChC,IAAMC,MAAM,GAAG,IAAIF,IAAI,CAACzC,KAAK,CAACM,WAAW,CAAC,CAACoC,OAAO,CAAC,CAAC;QACpD,IAAIF,GAAG,IAAIG,MAAM,EAAE;UACjB,OAAO,KAAK;QACd;MACF;MAEA,OAAO,IAAI;IACb,CAAC;IAEDC,WAAW,EAAE,SAAbA,WAAWA,CAAE5C,KAAK;MAAA,OAAIA,KAAK,CAACC,IAAI;IAAA;IAChCG,WAAW,EAAE,SAAbA,WAAWA,CAAEJ,KAAK;MAAA,OAAIA,KAAK,CAACI,WAAW;IAAA;IACvCC,SAAS,EAAE,SAAXA,SAASA,CAAEL,KAAK;MAAA,OAAIA,KAAK,CAACK,SAAS;IAAA;IACnCwC,UAAU,EAAE,SAAZA,UAAUA,CAAE7C,KAAK,EAAI;MACnB,IAAI,CAACA,KAAK,CAACE,KAAK,EAAE,OAAO,KAAK;MAC9B,IAAI,CAACF,KAAK,CAACM,WAAW,EAAE,OAAO,IAAI,CAAC,CAAC;;MAErC,IAAMkC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;MAChC,IAAMC,MAAM,GAAG,IAAIF,IAAI,CAACzC,KAAK,CAACM,WAAW,CAAC,CAACoC,OAAO,CAAC,CAAC;MACpD,OAAOF,GAAG,GAAGG,MAAM;IACrB,CAAC;IAED;IACAG,cAAc,EAAE,SAAhBA,cAAcA,CAAE9C,KAAK;MAAA,OAAIA,KAAK,CAACO,UAAU;IAAA;IACzCwC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAE/C,KAAK,EAAI;MACzB,IAAI,CAACA,KAAK,CAACQ,kBAAkB,EAAE,OAAO,IAAI;MAC1C,OAAOR,KAAK,CAACO,UAAU,CAACyC,IAAI,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACC,EAAE,IAAIlD,KAAK,CAACQ,kBAAkB;MAAA,EAAC,IAAI,IAAI;IAC7E,CAAC;IACDQ,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAEhB,KAAK;MAAA,OAAI,UAAAmD,WAAW,EAAI;QAC3C,OAAOnD,KAAK,CAACgB,mBAAmB,CAACmC,WAAW,CAAC,IAAI;UAAEC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE;QAAM,CAAC;MACrF,CAAC;IAAA;IACDC,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAEtD,KAAK;MAAA,OAAI,UAAAmD,WAAW,EAAI;QAC7C,OAAOnD,KAAK,CAACiB,mBAAmB,CAACkC,WAAW,CAAC,IAAI,IAAI;MACvD,CAAC;IAAA;IAED;IACAI,YAAY,EAAE,SAAdA,YAAYA,CAAEvD,KAAK;MAAA,OAAIA,KAAK,CAACS,QAAQ;IAAA;IAErC;IACA+C,SAAS,EAAE,SAAXA,SAASA,CAAExD,KAAK;MAAA,OAAIA,KAAK,CAACmB,MAAM;IAAA;IAChCsC,YAAY,EAAE,SAAdA,YAAYA,CAAEzD,KAAK;MAAA,OAAIA,KAAK,CAACmB,MAAM,CAACnB,KAAK,CAACoB,iBAAiB,CAAC,IAAI,IAAI;IAAA;IACpEsC,cAAc,EAAE,SAAhBA,cAAcA,CAAE1D,KAAK;MAAA,OAAI,UAAAmD,WAAW,EAAI;QACtC,OAAOnD,KAAK,CAACsB,KAAK,CAAC6B,WAAW,CAAC,IAAI,EAAE;MACvC,CAAC;IAAA;IACDQ,WAAW,EAAE,SAAbA,WAAWA,CAAE3D,KAAK;MAAA,OAAIA,KAAK,CAACsB,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE;IAAA;IAEjD;IACAsC,YAAY,EAAE,SAAdA,YAAYA,CAAE5D,KAAK;MAAA,OAAIA,KAAK,CAACU,KAAK;IAAA;IAClCmD,WAAW,EAAE,SAAbA,WAAWA,CAAE7D,KAAK;MAAA,OAAIA,KAAK,CAACU,KAAK,KAAK,MAAM;IAAA;IAC5Cc,aAAa,EAAE,SAAfA,aAAaA,CAAExB,KAAK;MAAA,OAAIA,KAAK,CAACwB,aAAa;IAAA;IAC3CC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAEzB,KAAK;MAAA,OAAIA,KAAK,CAACyB,gBAAgB;IAAA;IACjDP,YAAY,EAAE,SAAdA,YAAYA,CAAElB,KAAK;MAAA,OAAIA,KAAK,CAACkB,YAAY;IAAA;IACzCQ,aAAa,EAAE,SAAfA,aAAaA,CAAE1B,KAAK;MAAA,OAAIA,KAAK,CAAC0B,aAAa;IAAA;IAC3CoC,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAE9D,KAAK;MAAA,IAAA+D,oBAAA;MAAA,OAAI,EAAAA,oBAAA,GAAA/D,KAAK,CAAC0B,aAAa,cAAAqC,oBAAA,uBAAnBA,oBAAA,CAAqBC,UAAU,KAAI,KAAK;IAAA;IAEpE;IACAC,SAAS,EAAE,SAAXA,SAASA,CAAEjE,KAAK;MAAA,OAAIA,KAAK,CAAC+B,KAAK;IAAA;IAC/BmC,eAAe,EAAE,SAAjBA,eAAeA,CAAElE,KAAK;MAAA,OAAIA,KAAK,CAACgC,WAAW;IAAA;IAC3CmC,cAAc,EAAE,SAAhBA,cAAcA,CAAEnE,KAAK;MAAA,OAAIA,KAAK,CAACiC,YAAY;IAAA;IAC3CC,SAAS,EAAE,SAAXA,SAASA,CAAElC,KAAK;MAAA,OAAIA,KAAK,CAACkC,SAAS;IAAA;IACnCC,WAAW,EAAE,SAAbA,WAAWA,CAAEnC,KAAK;MAAA,OAAIA,KAAK,CAACmC,WAAW;IAAA;IACvCiC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAEpE,KAAK;MAAA,OAAIA,KAAK,CAACoC,kBAAkB;IAAA;IACvDC,aAAa,EAAE,SAAfA,aAAaA,CAAErC,KAAK;MAAA,OAAIA,KAAK,CAACqC,aAAa;IAAA;IAC3CC,YAAY,EAAE,SAAdA,YAAYA,CAAEtC,KAAK;MAAA,OAAIA,KAAK,CAACsC,YAAY;IAAA;EAC3C,CAAC;EACD+B,SAAS,EAAE;IACT;IACAC,QAAQ,WAARA,QAAQA,CAACtE,KAAK,EAAEC,IAAI,EAAE;MACpBD,KAAK,CAACC,IAAI,GAAGA,IAAI;MACjBD,KAAK,CAACG,eAAe,GAAG,CAAC,CAACF,IAAI;MAC9B,IAAI;QACF,IAAIA,IAAI,EAAE;UACRtB,cAAc,CAAC4F,SAAS,CAAC,MAAM,EAAEtE,IAAI,CAAC;QACxC,CAAC,MAAM;UACLtB,cAAc,CAAC6F,UAAU,CAAC,MAAM,CAAC;QACnC;MACF,CAAC,CAAC,OAAO9E,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAC1C;IACF,CAAC;IAED+E,SAAS,WAATA,SAASA,CAACzE,KAAK,EAAEE,KAAK,EAAE;MACtBF,KAAK,CAACE,KAAK,GAAGA,KAAK;MACnB,IAAI;QACF,IAAIA,KAAK,EAAE;UACTvB,cAAc,CAAC+F,OAAO,CAAC,OAAO,EAAExE,KAAK,CAAC;;UAEtC;UACA,IAAI;YACF,IAAMyE,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC5E,KAAK,CAAC6E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACrD,IAAIJ,OAAO,CAACK,GAAG,EAAE;cACf,IAAMrC,MAAM,GAAG,IAAIF,IAAI,CAACkC,OAAO,CAACK,GAAG,GAAG,IAAI,CAAC,CAACC,WAAW,CAAC,CAAC;cACzDjF,KAAK,CAACM,WAAW,GAAGqC,MAAM;cAC1BhE,cAAc,CAAC+F,OAAO,CAAC,aAAa,EAAE/B,MAAM,CAAC;YAC/C;UACF,CAAC,CAAC,OAAOuC,CAAC,EAAE;YACVvF,OAAO,CAACC,IAAI,CAAC,gBAAgB,EAAEsF,CAAC,CAAC;UACnC;QACF,CAAC,MAAM;UACLvG,cAAc,CAAC6F,UAAU,CAAC,OAAO,CAAC;UAClC7F,cAAc,CAAC6F,UAAU,CAAC,aAAa,CAAC;UACxCxE,KAAK,CAACM,WAAW,GAAG,IAAI;QAC1B;MACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEF,KAAK,CAAC;MAC3C;IACF,CAAC;IAEDyF,gBAAgB,WAAhBA,gBAAgBA,CAACnF,KAAK,EAAEoF,OAAO,EAAE;MAC/BpF,KAAK,CAACI,WAAW,GAAGgF,OAAO;IAC7B,CAAC;IAEDC,cAAc,WAAdA,cAAcA,CAACrF,KAAK,EAAEN,KAAK,EAAE;MAC3BM,KAAK,CAACK,SAAS,GAAGX,KAAK;IACzB,CAAC;IAED4F,UAAU,WAAVA,UAAUA,CAACtF,KAAK,EAAE;MAChBA,KAAK,CAACC,IAAI,GAAG,IAAI;MACjBD,KAAK,CAACE,KAAK,GAAG,EAAE;MAChBF,KAAK,CAACM,WAAW,GAAG,IAAI;MACxBN,KAAK,CAACG,eAAe,GAAG,KAAK;MAC7BH,KAAK,CAACK,SAAS,GAAG,IAAI;;MAEtB;MACA,IAAI;QACF1B,cAAc,CAAC6F,UAAU,CAAC,MAAM,CAAC;QACjC7F,cAAc,CAAC6F,UAAU,CAAC,OAAO,CAAC;QAClC7F,cAAc,CAAC6F,UAAU,CAAC,aAAa,CAAC;MAC1C,CAAC,CAAC,OAAO9E,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAC1C;IACF,CAAC;IAED6F,qBAAqB,WAArBA,qBAAqBA,CAACvF,KAAK,EAAEwF,MAAM,EAAE;MACnCxF,KAAK,CAACyB,gBAAgB,GAAG+D,MAAM;MAC/B,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxBxF,KAAK,CAACwB,aAAa,GAAG,IAAI;MAC5B;IACF,CAAC;IAEDiE,kBAAkB,WAAlBA,kBAAkBA,CAACzF,KAAK,EAAE0F,IAAI,EAAE;MAC9B1F,KAAK,CAACkB,YAAY,GAAGwE,IAAI;MACzB,IAAI;QACF/G,cAAc,CAAC+F,OAAO,CAAC,cAAc,EAAEgB,IAAI,CAAC;MAC9C,CAAC,CAAC,OAAOhG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAC1C;IACF,CAAC;IAEDiG,kBAAkB,WAAlBA,kBAAkBA,CAAC3F,KAAK,EAAEwF,MAAM,EAAE;MAChCxF,KAAK,CAAC0B,aAAa,GAAG8D,MAAM;IAC9B,CAAC;IACDI,kBAAkB,WAAlBA,kBAAkBA,CAAC5F,KAAK,EAAE6F,SAAS,EAAE;MACnC,IAAI7F,KAAK,CAACC,IAAI,EAAE;QACdD,KAAK,CAACC,IAAI,CAAC6F,UAAU,GAAGD,SAAS;QACjC;QACAlH,cAAc,CAAC4F,SAAS,CAAC,MAAM,EAAEvE,KAAK,CAACC,IAAI,CAAC;MAC9C;IACF,CAAC;IACD8F,cAAc,WAAdA,cAAcA,CAAC/F,KAAK,EAAEO,UAAU,EAAE;MAChCP,KAAK,CAACO,UAAU,GAAGA,UAAU;MAC7B,IAAI;QACF5B,cAAc,CAAC4F,SAAS,CAAC,YAAY,EAAEhE,UAAU,CAAC;MACpD,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEF,KAAK,CAAC;MAC1C;IACF,CAAC;IACDsG,aAAa,WAAbA,aAAaA,CAAChG,KAAK,EAAEiG,SAAS,EAAE;MAC9BjG,KAAK,CAACO,UAAU,CAAC2F,IAAI,CAACD,SAAS,CAAC;MAChC,IAAI;QACFtH,cAAc,CAAC4F,SAAS,CAAC,YAAY,EAAEvE,KAAK,CAACO,UAAU,CAAC;MAC1D,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,kBAAkB,EAAEF,KAAK,CAAC;MACzC;IACF,CAAC;IACDyG,gBAAgB,WAAhBA,gBAAgBA,CAACnG,KAAK,EAAEoG,gBAAgB,EAAE;MACxC,IAAMC,KAAK,GAAGrG,KAAK,CAACO,UAAU,CAAC+F,SAAS,CAAC,UAAArD,CAAC;QAAA,OAAIA,CAAC,CAACC,EAAE,KAAKkD,gBAAgB,CAAClD,EAAE;MAAA,EAAC;MAC3E,IAAImD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBrG,KAAK,CAACO,UAAU,CAACgG,MAAM,CAACF,KAAK,EAAE,CAAC,EAAED,gBAAgB,CAAC;QACnD,IAAI;UACFzH,cAAc,CAAC4F,SAAS,CAAC,YAAY,EAAEvE,KAAK,CAACO,UAAU,CAAC;UAExDP,KAAK,CAACiB,mBAAmB,CAACmF,gBAAgB,CAAClD,EAAE,CAAC,GAAAsD,aAAA,CAAAA,aAAA,KACzCJ,gBAAgB;YACnBK,WAAW,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC;UAAC,EACtC;UACDtG,cAAc,CAAC4F,SAAS,CAAC,qBAAqB,EAAEvE,KAAK,CAACiB,mBAAmB,CAAC;QAC5E,CAAC,CAAC,OAAOvB,KAAK,EAAE;UACdC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEF,KAAK,CAAC;QAC1C;QAEAM,KAAK,CAACgB,mBAAmB,CAACoF,gBAAgB,CAAClD,EAAE,CAAC,GAAG;UAC/CE,QAAQ,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;UAClC5B,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IACDqD,4BAA4B,WAA5BA,4BAA4BA,CAAC1G,KAAK,EAAE2G,aAAa,EAAE;MACjD,IAAMN,KAAK,GAAGrG,KAAK,CAACO,UAAU,CAAC+F,SAAS,CAAC,UAAArD,CAAC;QAAA,OAAIA,CAAC,CAACC,EAAE,KAAKyD,aAAa,CAACzD,EAAE;MAAA,EAAC;MACxE,IAAImD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAMO,eAAe,GAAAJ,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAChBxG,KAAK,CAACO,UAAU,CAAC8F,KAAK,CAAC,GACvBM,aAAa;UAChBE,UAAU,EAAE7G,KAAK,CAACO,UAAU,CAAC8F,KAAK,CAAC,CAACQ,UAAU,IAAI,CAAC;QAAC,EACrD;QAED7G,KAAK,CAACO,UAAU,CAACgG,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEO,eAAe,CAAC;QAElDjI,cAAc,CAAC4F,SAAS,CAAC,YAAY,EAAEvE,KAAK,CAACO,UAAU,CAAC;QAExDP,KAAK,CAACiB,mBAAmB,CAAC0F,aAAa,CAACzD,EAAE,CAAC,GAAAsD,aAAA,CAAAA,aAAA,KACtCI,eAAe;UAClBH,WAAW,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC;QAAC,EACtC;QACDtG,cAAc,CAAC4F,SAAS,CAAC,qBAAqB,EAAEvE,KAAK,CAACiB,mBAAmB,CAAC;QAE1EjB,KAAK,CAACgB,mBAAmB,CAAC2F,aAAa,CAACzD,EAAE,CAAC,GAAG;UAC5CE,QAAQ,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;UAClC5B,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IACDyD,0BAA0B,WAA1BA,0BAA0BA,CAAC9G,KAAK,EAAA+G,IAAA,EAAwB;MAAA,IAApB7D,EAAE,GAAA6D,IAAA,CAAF7D,EAAE;QAAE8D,KAAK,GAAAD,IAAA,CAALC,KAAK;QAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;MAClD,IAAMZ,KAAK,GAAGrG,KAAK,CAACO,UAAU,CAAC+F,SAAS,CAAC,UAAArD,CAAC;QAAA,OAAIA,CAAC,CAACC,EAAE,KAAKA,EAAE;MAAA,EAAC;MAC1D,IAAImD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAMD,gBAAgB,GAAAI,aAAA,KAAQxG,KAAK,CAACO,UAAU,CAAC8F,KAAK,CAAC,CAAE;QAEvD,IAAIW,KAAK,CAACE,QAAQ,CAAC,GAAG,CAAC,EAAE;UACvB,IAAMC,KAAK,GAAGH,KAAK,CAACjC,KAAK,CAAC,GAAG,CAAC;UAC9B,IAAIqC,GAAG,GAAGhB,gBAAgB;UAC1B,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAAC5H,MAAM,GAAG,CAAC,EAAE8H,CAAC,EAAE,EAAE;YACzC,IAAI,CAACD,GAAG,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAED,GAAG,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACtCD,GAAG,GAAGA,GAAG,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC;UACrB;UACAD,GAAG,CAACD,KAAK,CAACA,KAAK,CAAC5H,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG0H,KAAK;QACtC,CAAC,MAAM;UACLb,gBAAgB,CAACY,KAAK,CAAC,GAAGC,KAAK;QACjC;QAEAjH,KAAK,CAACO,UAAU,CAACgG,MAAM,CAACF,KAAK,EAAE,CAAC,EAAED,gBAAgB,CAAC;QAEnDzH,cAAc,CAAC4F,SAAS,CAAC,YAAY,EAAEvE,KAAK,CAACO,UAAU,CAAC;QAExDP,KAAK,CAACiB,mBAAmB,CAACiC,EAAE,CAAC,GAAAsD,aAAA,CAAAA,aAAA,KACxBJ,gBAAgB;UACnBK,WAAW,EAAE,IAAIhE,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC;QAAC,EACtC;QACDtG,cAAc,CAAC4F,SAAS,CAAC,qBAAqB,EAAEvE,KAAK,CAACiB,mBAAmB,CAAC;MAC5E;IACF,CAAC;IACDqG,qBAAqB,WAArBA,qBAAqBA,CAACtH,KAAK,EAAEmD,WAAW,EAAE;MACxCnD,KAAK,CAACQ,kBAAkB,GAAG2C,WAAW;MACtCxE,cAAc,CAAC+F,OAAO,CAAC,oBAAoB,EAAEvB,WAAW,CAAC;IAC3D,CAAC;IACDoE,gBAAgB,WAAhBA,gBAAgBA,CAACvH,KAAK,EAAEmD,WAAW,EAAE;MACnCnD,KAAK,CAACO,UAAU,GAAGP,KAAK,CAACO,UAAU,CAACiH,MAAM,CAAC,UAAAvE,CAAC;QAAA,OAAIA,CAAC,CAACC,EAAE,KAAKC,WAAW;MAAA,EAAC;MACrExE,cAAc,CAAC4F,SAAS,CAAC,YAAY,EAAEvE,KAAK,CAACO,UAAU,CAAC;MAExD,IAAIP,KAAK,CAACiB,mBAAmB,CAACkC,WAAW,CAAC,EAAE;QAC1C,OAAOnD,KAAK,CAACiB,mBAAmB,CAACkC,WAAW,CAAC;QAC7CxE,cAAc,CAAC4F,SAAS,CAAC,qBAAqB,EAAEvE,KAAK,CAACiB,mBAAmB,CAAC;MAC5E;MAEA,IAAIjB,KAAK,CAACgB,mBAAmB,CAACmC,WAAW,CAAC,EAAE;QAC1C,OAAOnD,KAAK,CAACgB,mBAAmB,CAACmC,WAAW,CAAC;MAC/C;IACF,CAAC;IACDsE,4BAA4B,WAA5BA,4BAA4BA,CAACzH,KAAK,EAAA0H,KAAA,EAA2B;MAAA,IAAvBvE,WAAW,GAAAuE,KAAA,CAAXvE,WAAW;QAAEqC,MAAM,GAAAkC,KAAA,CAANlC,MAAM;MACvDxF,KAAK,CAACgB,mBAAmB,CAACmC,WAAW,CAAC,GAAAqD,aAAA,CAAAA,aAAA,KACjCxG,KAAK,CAACgB,mBAAmB,CAACmC,WAAW,CAAC,GACtCqC,MAAM,CACV;IACH,CAAC;IACDmC,SAAS,WAATA,SAASA,CAAC3H,KAAK,EAAElB,KAAK,EAAE;MACtBkB,KAAK,CAAClB,KAAK,GAAGA,KAAK;IACrB,CAAC;IACD8I,gBAAgB,WAAhBA,gBAAgBA,CAAC5H,KAAK,EAAE6H,QAAQ,EAAE;MAChC7H,KAAK,CAAC8H,WAAW,GAAGD,QAAQ;IAC9B,CAAC;IACDE,kBAAkB,WAAlBA,kBAAkBA,CAAC/H,KAAK,EAAE;MACxBA,KAAK,CAAC8H,WAAW,GAAG,IAAI;MACxB9H,KAAK,CAACS,QAAQ,GAAG,EAAE;IACrB,CAAC;IACDuH,WAAW,WAAXA,WAAWA,CAAChI,KAAK,EAAEiI,OAAO,EAAE;MAC1BtI,OAAO,CAACuI,GAAG,CAAC,aAAa,EAAED,OAAO,CAAC;MAEnC,IAAI,CAACA,OAAO,CAACE,SAAS,EAAE;QACtBF,OAAO,CAACE,SAAS,GAAG,IAAI1F,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;MAC9C;MAEA,IAAIgD,OAAO,CAACG,IAAI,KAAK,MAAM,IAAI,CAACH,OAAO,CAACI,QAAQ,EAAE;QAChDJ,OAAO,CAACI,QAAQ,GAAGJ,OAAO,CAACK,OAAO,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;MACxD;MAEAL,OAAO,CAAC/E,EAAE,GAAGT,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+F,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MAEjE1I,KAAK,CAACS,QAAQ,CAACyF,IAAI,CAAC+B,OAAO,CAAC;MAE5B,IAAIjI,KAAK,CAACS,QAAQ,CAAClB,MAAM,GAAG,IAAI,EAAE;QAChCS,KAAK,CAACS,QAAQ,GAAGT,KAAK,CAACS,QAAQ,CAACkI,KAAK,CAAC,CAAC,GAAG,CAAC;MAC7C;MAEA,IAAMC,MAAM,GAAG5I,KAAK,CAAC8H,WAAW,GAAG9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,GAAG,SAAS;MACnE,IAAMK,YAAY,GAAG5E,cAAc,CAACoB,SAAS,kBAAAF,MAAA,CAAkB+I,MAAM,CAAE,CAAC,IAAI,EAAE;MAC9ErF,YAAY,CAAC2C,IAAI,CAAC+B,OAAO,CAAC;MAE1B,IAAI1E,YAAY,CAAChE,MAAM,GAAG,GAAG,EAAE;QAC7BgE,YAAY,CAACgD,MAAM,CAAC,CAAC,EAAEhD,YAAY,CAAChE,MAAM,GAAG,GAAG,CAAC;MACnD;MAEAZ,cAAc,CAAC4F,SAAS,kBAAA1E,MAAA,CAAkB+I,MAAM,GAAIrF,YAAY,CAAC;IACnE,CAAC;IACDsF,YAAY,WAAZA,YAAYA,CAAC7I,KAAK,EAAES,QAAQ,EAAE;MAC5BT,KAAK,CAACS,QAAQ,GAAGA,QAAQ;MAEzB,IAAMmI,MAAM,GAAG5I,KAAK,CAAC8H,WAAW,GAAG9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,GAAG,SAAS;MACnEvE,cAAc,CAAC4F,SAAS,kBAAA1E,MAAA,CAAkB+I,MAAM,GAAInI,QAAQ,CAACkI,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3E,CAAC;IACDG,YAAY,WAAZA,YAAYA,CAAC9I,KAAK,EAAE;MAClBA,KAAK,CAACU,KAAK,GAAGV,KAAK,CAACU,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;MACvD/B,cAAc,CAAC+F,OAAO,CAAC,OAAO,EAAE1E,KAAK,CAACU,KAAK,CAAC;IAC9C,CAAC;IACDqI,cAAc,WAAdA,cAAcA,CAAC/I,KAAK,EAAE;MACpBA,KAAK,CAACS,QAAQ,GAAG,EAAE;MAEnB,IAAMmI,MAAM,GAAG5I,KAAK,CAAC8H,WAAW,GAAG9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,GAAG,SAAS;MACnEvE,cAAc,CAAC6F,UAAU,kBAAA3E,MAAA,CAAkB+I,MAAM,CAAE,CAAC;IACtD,CAAC;IACDI,qBAAqB,WAArBA,qBAAqBA,CAAChJ,KAAK,EAAEiI,OAAO,EAAE;MACpCjI,KAAK,CAACiJ,gBAAgB,GAAGhB,OAAO;IAClC,CAAC;IACDiB,aAAa,WAAbA,aAAaA,CAAClJ,KAAK,EAAEmJ,OAAO,EAAE;MAC5BnJ,KAAK,CAACW,UAAU,CAACI,UAAU,GAAGoI,OAAO;IACvC,CAAC;IACDC,kBAAkB,WAAlBA,kBAAkBA,CAACpJ,KAAK,EAAEqJ,QAAQ,EAAE;MAClCrJ,KAAK,CAACW,UAAU,GAAA6F,aAAA,CAAAA,aAAA,KACXxG,KAAK,CAACW,UAAU,GAChB0I,QAAQ,CACZ;IACH,CAAC;IACDC,cAAc,WAAdA,cAAcA,CAACtJ,KAAK,EAAAuJ,KAAA,EAAsB;MAAA,IAAlBlD,KAAK,GAAAkD,KAAA,CAALlD,KAAK;QAAE4B,OAAO,GAAAsB,KAAA,CAAPtB,OAAO;MACpC,IAAI5B,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrG,KAAK,CAACS,QAAQ,CAAClB,MAAM,EAAE;QAC/CS,KAAK,CAACS,QAAQ,CAAC8F,MAAM,CAACF,KAAK,EAAE,CAAC,EAAE4B,OAAO,CAAC;QAExC,IAAMW,MAAM,GAAG5I,KAAK,CAAC8H,WAAW,GAAG9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,GAAG,SAAS;QACnE,IAAMK,YAAY,GAAG5E,cAAc,CAACoB,SAAS,kBAAAF,MAAA,CAAkB+I,MAAM,CAAE,CAAC,IAAI,EAAE;QAC9E,IAAMY,QAAQ,GAAGjG,YAAY,CAAC+C,SAAS,CAAC,UAAAmD,CAAC;UAAA,OAAIA,CAAC,CAACvG,EAAE,KAAK+E,OAAO,CAAC/E,EAAE;QAAA,EAAC;QACjE,IAAIsG,QAAQ,KAAK,CAAC,CAAC,EAAE;UACnBjG,YAAY,CAACgD,MAAM,CAACiD,QAAQ,EAAE,CAAC,EAAEvB,OAAO,CAAC;UACzCtJ,cAAc,CAAC4F,SAAS,kBAAA1E,MAAA,CAAkB+I,MAAM,GAAIrF,YAAY,CAAC;QACnE;MACF;IACF,CAAC;IACDmG,mBAAmB,WAAnBA,mBAAmBA,CAAC1J,KAAK,EAAE;MACzB,IAAIA,KAAK,CAACS,QAAQ,CAAClB,MAAM,GAAG,CAAC,EAAE;QAC7BS,KAAK,CAACS,QAAQ,CAACkJ,GAAG,CAAC,CAAC;QAEpB,IAAMf,MAAM,GAAG5I,KAAK,CAAC8H,WAAW,GAAG9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,GAAG,SAAS;QACnE,IAAMK,YAAY,GAAG5E,cAAc,CAACoB,SAAS,kBAAAF,MAAA,CAAkB+I,MAAM,CAAE,CAAC,IAAI,EAAE;QAE9E,IAAIrF,YAAY,CAAChE,MAAM,GAAG,CAAC,EAAE;UAC3BgE,YAAY,CAACoG,GAAG,CAAC,CAAC;UAClBhL,cAAc,CAAC4F,SAAS,kBAAA1E,MAAA,CAAkB+I,MAAM,GAAIrF,YAAY,CAAC;QACnE;MACF;IACF,CAAC;IACDqG,UAAU,WAAVA,UAAUA,CAAC5J,KAAK,EAAEmB,MAAM,EAAE;MACxBnB,KAAK,CAACmB,MAAM,GAAGA,MAAM;MACrBxC,cAAc,CAAC4F,SAAS,CAAC,QAAQ,EAAEpD,MAAM,CAAC;IAC5C,CAAC;IACD0I,SAAS,WAATA,SAASA,CAAC7J,KAAK,EAAE8J,KAAK,EAAE;MACtB9J,KAAK,CAACmB,MAAM,CAAC+E,IAAI,CAAC4D,KAAK,CAAC;MACxBnL,cAAc,CAAC4F,SAAS,CAAC,QAAQ,EAAEvE,KAAK,CAACmB,MAAM,CAAC;IAClD,CAAC;IACD4I,YAAY,WAAZA,YAAYA,CAAC/J,KAAK,EAAAgK,KAAA,EAAoB;MAAA,IAAhB3D,KAAK,GAAA2D,KAAA,CAAL3D,KAAK;QAAEyD,KAAK,GAAAE,KAAA,CAALF,KAAK;MAChC,IAAIzD,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrG,KAAK,CAACmB,MAAM,CAAC5B,MAAM,EAAE;QAC7CS,KAAK,CAACmB,MAAM,CAACoF,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEyD,KAAK,CAAC;QACpCnL,cAAc,CAAC4F,SAAS,CAAC,QAAQ,EAAEvE,KAAK,CAACmB,MAAM,CAAC;MAClD;IACF,CAAC;IACD8I,YAAY,WAAZA,YAAYA,CAACjK,KAAK,EAAEqG,KAAK,EAAE;MACzB,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrG,KAAK,CAACmB,MAAM,CAAC5B,MAAM,EAAE;QAC7CS,KAAK,CAACmB,MAAM,CAACoF,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC7B1H,cAAc,CAAC4F,SAAS,CAAC,QAAQ,EAAEvE,KAAK,CAACmB,MAAM,CAAC;QAEhD,IAAIkF,KAAK,IAAIrG,KAAK,CAACoB,iBAAiB,IAAIpB,KAAK,CAACoB,iBAAiB,GAAG,CAAC,EAAE;UACnEpB,KAAK,CAACoB,iBAAiB,EAAE;UACzBzC,cAAc,CAAC+F,OAAO,CAAC,mBAAmB,EAAE1E,KAAK,CAACoB,iBAAiB,CAACqH,QAAQ,CAAC,CAAC,CAAC;QACjF;MACF;IACF,CAAC;IACDyB,uBAAuB,WAAvBA,uBAAuBA,CAAClK,KAAK,EAAEqG,KAAK,EAAE;MACpC,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrG,KAAK,CAACmB,MAAM,CAAC5B,MAAM,EAAE;QAC7CS,KAAK,CAACoB,iBAAiB,GAAGiF,KAAK;QAC/B1H,cAAc,CAAC+F,OAAO,CAAC,mBAAmB,EAAE2B,KAAK,CAACoC,QAAQ,CAAC,CAAC,CAAC;MAC/D;IACF,CAAC;IACD0B,SAAS,WAATA,SAASA,CAACnK,KAAK,EAAAoK,KAAA,EAA0B;MAAA,IAAtBjH,WAAW,GAAAiH,KAAA,CAAXjH,WAAW;QAAE7B,KAAK,GAAA8I,KAAA,CAAL9I,KAAK;MACnC,IAAM4B,EAAE,GAAGC,WAAW,IAAI,QAAQ;MAClCnD,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,GAAG5B,KAAK;MACvB3C,cAAc,CAAC4F,SAAS,CAAC,OAAO,EAAEvE,KAAK,CAACsB,KAAK,CAAC;IAChD,CAAC;IACD+I,QAAQ,WAARA,QAAQA,CAACrK,KAAK,EAAAsK,KAAA,EAAyB;MAAA,IAArBnH,WAAW,GAAAmH,KAAA,CAAXnH,WAAW;QAAEoH,IAAI,GAAAD,KAAA,CAAJC,IAAI;MACjC,IAAMrH,EAAE,GAAGC,WAAW,IAAI,QAAQ;MAClC,IAAI,CAACnD,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,EAAE;QACpBlD,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,GAAG,EAAE;MACtB;MACAlD,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,CAACsH,OAAO,CAACD,IAAI,CAAC;MAC7B5L,cAAc,CAAC4F,SAAS,CAAC,OAAO,EAAEvE,KAAK,CAACsB,KAAK,CAAC;IAChD,CAAC;IACDmJ,WAAW,WAAXA,WAAWA,CAACzK,KAAK,EAAA0K,KAAA,EAAgC;MAAA,IAA5BvH,WAAW,GAAAuH,KAAA,CAAXvH,WAAW;QAAEkD,KAAK,GAAAqE,KAAA,CAALrE,KAAK;QAAEkE,IAAI,GAAAG,KAAA,CAAJH,IAAI;MAC3C,IAAMrH,EAAE,GAAGC,WAAW,IAAI,QAAQ;MAClC,IAAInD,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,IAAImD,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrG,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,CAAC3D,MAAM,EAAE;QACnES,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,CAACqD,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEkE,IAAI,CAAC;QACtC5L,cAAc,CAAC4F,SAAS,CAAC,OAAO,EAAEvE,KAAK,CAACsB,KAAK,CAAC;MAChD;IACF,CAAC;IACDqJ,WAAW,WAAXA,WAAWA,CAAC3K,KAAK,EAAA4K,KAAA,EAA0B;MAAA,IAAtBzH,WAAW,GAAAyH,KAAA,CAAXzH,WAAW;QAAEkD,KAAK,GAAAuE,KAAA,CAALvE,KAAK;MACrC,IAAMnD,EAAE,GAAGC,WAAW,IAAI,QAAQ;MAClC,IAAInD,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,IAAImD,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGrG,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,CAAC3D,MAAM,EAAE;QACnES,KAAK,CAACsB,KAAK,CAAC4B,EAAE,CAAC,CAACqD,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAChC1H,cAAc,CAAC4F,SAAS,CAAC,OAAO,EAAEvE,KAAK,CAACsB,KAAK,CAAC;MAChD;IACF,CAAC;IACDuJ,gBAAgB,WAAhBA,gBAAgBA,CAAC7K,KAAK,EAAE8K,SAAS,EAAE;MACjC9K,KAAK,CAACwB,aAAa,GAAGsJ,SAAS;IACjC,CAAC;IACDC,SAAS,WAATA,SAASA,CAAC/K,KAAK,EAAE+B,KAAK,EAAE;MACtB/B,KAAK,CAAC+B,KAAK,GAAGA,KAAK;IACrB,CAAC;IACDiJ,gBAAgB,WAAhBA,gBAAgBA,CAAChL,KAAK,EAAEiL,IAAI,EAAE;MAC5BjL,KAAK,CAACgC,WAAW,GAAGiJ,IAAI;IAC1B,CAAC;IACDC,QAAQ,WAARA,QAAQA,CAAClL,KAAK,EAAEiL,IAAI,EAAE;MACpBjL,KAAK,CAAC+B,KAAK,CAACmE,IAAI,CAAC+E,IAAI,CAAC;IACxB,CAAC;IACDE,WAAW,WAAXA,WAAWA,CAACnL,KAAK,EAAEoL,WAAW,EAAE;MAC9B,IAAM/E,KAAK,GAAGrG,KAAK,CAAC+B,KAAK,CAACuE,SAAS,CAAC,UAAA2E,IAAI;QAAA,OAAIA,IAAI,CAAC/H,EAAE,KAAKkI,WAAW,CAAClI,EAAE;MAAA,EAAC;MACvE,IAAImD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBrG,KAAK,CAAC+B,KAAK,CAACsE,KAAK,CAAC,GAAG+E,WAAW;MAClC;IACF,CAAC;IACDC,WAAW,WAAXA,WAAWA,CAACrL,KAAK,EAAEsL,MAAM,EAAE;MACzBtL,KAAK,CAAC+B,KAAK,GAAG/B,KAAK,CAAC+B,KAAK,CAACyF,MAAM,CAAC,UAAAyD,IAAI;QAAA,OAAIA,IAAI,CAAC/H,EAAE,KAAKoI,MAAM;MAAA,EAAC;IAC9D,CAAC;IACDC,iBAAiB,WAAjBA,iBAAiBA,CAACvL,KAAK,EAAEwL,SAAS,EAAE;MAClCxL,KAAK,CAACiC,YAAY,GAAGuJ,SAAS;IAChC,CAAC;IACDC,cAAc,WAAdA,cAAcA,CAACzL,KAAK,EAAEN,KAAK,EAAE;MAC3BM,KAAK,CAACkC,SAAS,GAAGxC,KAAK;IACzB,CAAC;IACDgM,gBAAgB,WAAhBA,gBAAgBA,CAAC1L,KAAK,EAAE2L,OAAO,EAAE;MAC/B3L,KAAK,CAACmC,WAAW,GAAGwJ,OAAO;IAC7B,CAAC;IACDC,wBAAwB,WAAxBA,wBAAwBA,CAAC5L,KAAK,EAAEwL,SAAS,EAAE;MACzCxL,KAAK,CAACoC,kBAAkB,GAAGoJ,SAAS;IACtC,CAAC;IACDK,gBAAgB,WAAhBA,gBAAgBA,CAAC7L,KAAK,EAAE8L,SAAS,EAAE;MACjC9L,KAAK,CAACqC,aAAa,GAAGyJ,SAAS;IACjC,CAAC;IACDC,iBAAiB,WAAjBA,iBAAiBA,CAAC/L,KAAK,EAAEgM,MAAM,EAAE;MAC/BhM,KAAK,CAACsC,YAAY,GAAG0J,MAAM;IAC7B,CAAC;IACDC,mBAAmB,WAAnBA,mBAAmBA,CAACjM,KAAK,EAAE;MACzBA,KAAK,CAACsC,YAAY,GAAG,IAAI;IAC3B,CAAC;IAED;IACA4J,eAAe,WAAfA,eAAeA,CAAClM,KAAK,EAAE2B,WAAW,EAAE;MAClC3B,KAAK,CAAC2B,WAAW,GAAGA,WAAW,IAAI,CAAC,CAAC;IACvC,CAAC;IACDwK,UAAU,WAAVA,UAAUA,CAACnM,KAAK,EAAE4B,MAAM,EAAE;MACxB5B,KAAK,CAAC4B,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;IAC7B,CAAC;IACDwK,WAAW,WAAXA,WAAWA,CAACpM,KAAK,EAAE6B,OAAO,EAAE;MAC1B7B,KAAK,CAAC6B,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IAC/B,CAAC;IACDwK,UAAU,WAAVA,UAAUA,CAACrM,KAAK,EAAE8B,MAAM,EAAE;MACxB9B,KAAK,CAAC8B,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;IAC7B,CAAC;IAGD;IACAwK,gBAAgB,WAAhBA,gBAAgBA,CAACtM,KAAK,EAAAuM,KAAA,EAAoB;MAAA,IAAhB3D,MAAM,GAAA2D,KAAA,CAAN3D,MAAM;QAAE4D,IAAI,GAAAD,KAAA,CAAJC,IAAI;MACpC,IAAIxM,KAAK,CAAC8H,WAAW,IAAI9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,KAAK0F,MAAM,EAAE;QACxD5I,KAAK,CAAC8H,WAAW,CAAC0E,IAAI,GAAGA,IAAI;MAC/B;IACF,CAAC;IAED;IACAC,qBAAqB,WAArBA,qBAAqBA,CAACzM,KAAK,EAAA0M,KAAA,EAAoB;MAAA,IAAhB9D,MAAM,GAAA8D,KAAA,CAAN9D,MAAM;QAAE4D,IAAI,GAAAE,KAAA,CAAJF,IAAI;MACzC,IAAIxM,KAAK,CAAClB,KAAK,IAAIkB,KAAK,CAAClB,KAAK,CAACS,MAAM,GAAG,CAAC,EAAE;QACzC,IAAMoN,SAAS,GAAG3M,KAAK,CAAClB,KAAK,CAACwH,SAAS,CAAC,UAAAsG,IAAI;UAAA,OAAIA,IAAI,CAAC1J,EAAE,KAAK0F,MAAM;QAAA,EAAC;QACnE,IAAI+D,SAAS,KAAK,CAAC,CAAC,EAAE;UACpB3M,KAAK,CAAClB,KAAK,CAAC6N,SAAS,CAAC,CAACH,IAAI,GAAGA,IAAI;QACpC;MACF;IACF;EACF,CAAC;EACDK,OAAO,EAAE;IACDC,QAAQ,WAARA,QAAQA,CAAAC,KAAA,EAAuBC,QAAQ,EAAE;MAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA0D,QAAA;QAAA,IAAAC,MAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,cAAA,EAAArN,KAAA,EAAAD,IAAA,EAAAuN,YAAA,EAAAC,WAAA,EAAAC,EAAA;QAAA,OAAAR,YAAA,GAAAS,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAA9BT,MAAM,GAAAL,KAAA,CAANK,MAAM,EAAEC,QAAQ,GAAAN,KAAA,CAARM,QAAQ;cAC/BD,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC;cAChCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAACQ,QAAA,CAAAE,CAAA;cAG7BnO,OAAO,CAACuI,GAAG,CAAC,YAAY,EAAE8E,QAAQ,CAACe,KAAK,CAAC;;cAEzC;cAAA,MACI,CAACf,QAAQ,CAAC3E,QAAQ,IAAI,CAAC2E,QAAQ,CAACe,KAAK,IAAI,CAACf,QAAQ,CAACgB,QAAQ;gBAAAJ,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,MACvD,IAAII,KAAK,CAAC,YAAY,CAAC;YAAA;cAAAL,QAAA,CAAAC,CAAA;cAAA,OAIRvP,KAAK,CAAC4P,IAAI,CAAC,qBAAqB,EAAElB,QAAQ,CAAC;YAAA;cAA5DM,QAAQ,GAAAM,QAAA,CAAAO,CAAA;cACdxO,OAAO,CAACuI,GAAG,CAAC,YAAY,EAAEoF,QAAQ,CAACc,IAAI,CAAC;cAACb,cAAA,GAEjBD,QAAQ,CAACc,IAAI,EAA7BlO,KAAK,GAAAqN,cAAA,CAALrN,KAAK,EAAED,IAAI,GAAAsN,cAAA,CAAJtN,IAAI;cAAA,MAEf,CAACC,KAAK,IAAI,CAACD,IAAI;gBAAA2N,QAAA,CAAAC,CAAA;gBAAA;cAAA;cAAA,MACX,IAAII,KAAK,CAAC,aAAa,CAAC;YAAA;cAGhC;cACAb,MAAM,CAAC,WAAW,EAAElN,KAAK,CAAC;cAC1BkN,MAAM,CAAC,UAAU,EAAEnN,IAAI,CAAC;cACxBmN,MAAM,CAAC,uBAAuB,EAAE,QAAQ,CAAC;cACzCA,MAAM,CAAC,oBAAoB,EAAE,IAAI3K,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC,CAAC;cAEtDtF,OAAO,CAACuI,GAAG,CAAC,cAAc,EAAEjI,IAAI,CAACiD,EAAE,CAAC;cAAC,OAAA0K,QAAA,CAAAS,CAAA,IAC9BpO,IAAI;YAAA;cAAA2N,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAO,CAAA;cAGXxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAAgO,EAAO,CAAC;cAE3BF,YAAY,GAAG,YAAY;cAE/B,IAAIE,EAAA,CAAMJ,QAAQ,IAAII,EAAA,CAAMJ,QAAQ,CAACc,IAAI,EAAE;gBACnCX,WAAW,GAAGC,EAAA,CAAMJ,QAAQ,CAACc,IAAI,CAACE,MAAM;gBAC9C,IAAIb,WAAW,KAAK,QAAQ,EAAE;kBAC5BD,YAAY,GAAG,QAAQ;gBACzB,CAAC,MAAM,IAAIC,WAAW,KAAK,SAAS,EAAE;kBACpCD,YAAY,GAAG,SAAS;gBAC1B,CAAC,MAAM,IAAIC,WAAW,EAAE;kBACtBD,YAAY,GAAGC,WAAW;gBAC5B;cACF,CAAC,MAAM,IAAIC,EAAA,CAAMa,IAAI,KAAK,eAAe,IAAI,CAACb,EAAA,CAAMJ,QAAQ,EAAE;gBAC5DE,YAAY,GAAG,gBAAgB;gBAC/BJ,MAAM,CAAC,uBAAuB,EAAE,SAAS,CAAC;cAC5C,CAAC,MAAM,IAAIM,EAAA,CAAMzF,OAAO,EAAE;gBACxBuF,YAAY,GAAGE,EAAA,CAAMzF,OAAO;cAC9B;cAEAmF,MAAM,CAAC,gBAAgB,EAAEI,YAAY,CAAC;cAAC,MACjC,IAAIS,KAAK,CAACT,YAAY,CAAC;YAAA;cAAAI,QAAA,CAAAE,CAAA;cAG7BV,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;cAAC,OAAAQ,QAAA,CAAAY,CAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAlB,OAAA;MAAA;IAEtC,CAAC;IAEKsB,KAAK,WAALA,KAAKA,CAAAC,MAAA,EAAuBC,WAAW,EAAE;MAAA,OAAA1B,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAmF,SAAA;QAAA,IAAAxB,MAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAuB,eAAA,EAAA3O,KAAA,EAAAD,IAAA,EAAAuN,YAAA,EAAAC,WAAA,EAAAqB,GAAA,EAAAC,GAAA;QAAA,OAAA7B,YAAA,GAAAS,CAAA,WAAAqB,SAAA;UAAA,kBAAAA,SAAA,CAAAnB,CAAA;YAAA;cAAjCT,MAAM,GAAAsB,MAAA,CAANtB,MAAM,EAAEC,QAAQ,GAAAqB,MAAA,CAARrB,QAAQ;cAC5BD,MAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC;cAChCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAAC4B,SAAA,CAAAlB,CAAA;cAG7BnO,OAAO,CAACuI,GAAG,CAAC,YAAY,EAAEyG,WAAW,CAACZ,KAAK,CAAC;;cAE5C;cAAA,MACI,CAACY,WAAW,CAACZ,KAAK,IAAI,CAACY,WAAW,CAACX,QAAQ;gBAAAgB,SAAA,CAAAnB,CAAA;gBAAA;cAAA;cAAA,MACvC,IAAII,KAAK,CAAC,WAAW,CAAC;YAAA;cAAAe,SAAA,CAAAnB,CAAA;cAAA,OAIPvP,KAAK,CAAC4P,IAAI,CAAC,kBAAkB,EAAES,WAAW,CAAC;YAAA;cAA5DrB,QAAQ,GAAA0B,SAAA,CAAAb,CAAA;cACdxO,OAAO,CAACuI,GAAG,CAAC,YAAY,EAAEoF,QAAQ,CAACc,IAAI,CAAC;cAACS,eAAA,GAEjBvB,QAAQ,CAACc,IAAI,EAA7BlO,KAAK,GAAA2O,eAAA,CAAL3O,KAAK,EAAED,IAAI,GAAA4O,eAAA,CAAJ5O,IAAI;cAAA,MAEf,CAACC,KAAK,IAAI,CAACD,IAAI;gBAAA+O,SAAA,CAAAnB,CAAA;gBAAA;cAAA;cAAA,MACX,IAAII,KAAK,CAAC,aAAa,CAAC;YAAA;cAGhC;cACAb,MAAM,CAAC,WAAW,EAAElN,KAAK,CAAC;cAC1BkN,MAAM,CAAC,UAAU,EAAEnN,IAAI,CAAC;cACxBmN,MAAM,CAAC,uBAAuB,EAAE,QAAQ,CAAC;cACzCA,MAAM,CAAC,oBAAoB,EAAE,IAAI3K,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC,CAAC;;cAEtD;cAAA+J,SAAA,CAAAlB,CAAA;cAAAkB,SAAA,CAAAnB,CAAA;cAAA,OAEQR,QAAQ,CAAC,iBAAiB,CAAC;YAAA;cAAA2B,SAAA,CAAAnB,CAAA;cAAA;YAAA;cAAAmB,SAAA,CAAAlB,CAAA;cAAAgB,GAAA,GAAAE,SAAA,CAAAb,CAAA;cAEjCxO,OAAO,CAACC,IAAI,CAAC,WAAW,EAAAkP,GAAY,CAAC;cACrC;YAAA;cAGFnP,OAAO,CAACuI,GAAG,CAAC,cAAc,EAAEjI,IAAI,CAACiD,EAAE,CAAC;cAAC,OAAA8L,SAAA,CAAAX,CAAA,IAC9BpO,IAAI;YAAA;cAAA+O,SAAA,CAAAlB,CAAA;cAAAiB,GAAA,GAAAC,SAAA,CAAAb,CAAA;cAGXxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAAqP,GAAO,CAAC;cAE3BvB,YAAY,GAAG,YAAY;cAE/B,IAAIuB,GAAA,CAAMzB,QAAQ,IAAIyB,GAAA,CAAMzB,QAAQ,CAACc,IAAI,EAAE;gBACnCX,WAAW,GAAGsB,GAAA,CAAMzB,QAAQ,CAACc,IAAI,CAACE,MAAM;gBAC9C,IAAIb,WAAW,KAAK,SAAS,EAAE;kBAC7BD,YAAY,GAAG,SAAS;gBAC1B,CAAC,MAAM,IAAIC,WAAW,KAAK,QAAQ,EAAE;kBACnCD,YAAY,GAAG,iBAAiB;gBAClC,CAAC,MAAM,IAAIC,WAAW,EAAE;kBACtBD,YAAY,GAAGC,WAAW;gBAC5B;cACF,CAAC,MAAM,IAAIsB,GAAA,CAAMR,IAAI,KAAK,eAAe,IAAI,CAACQ,GAAA,CAAMzB,QAAQ,EAAE;gBAC5DE,YAAY,GAAG,gBAAgB;gBAC/BJ,MAAM,CAAC,uBAAuB,EAAE,SAAS,CAAC;cAC5C,CAAC,MAAM,IAAI2B,GAAA,CAAM9G,OAAO,EAAE;gBACxBuF,YAAY,GAAGuB,GAAA,CAAM9G,OAAO;cAC9B;cAEAmF,MAAM,CAAC,gBAAgB,EAAEI,YAAY,CAAC;cAAC,MACjC,IAAIS,KAAK,CAACT,YAAY,CAAC;YAAA;cAAAwB,SAAA,CAAAlB,CAAA;cAG7BV,MAAM,CAAC,kBAAkB,EAAE,KAAK,CAAC;cAAC,OAAA4B,SAAA,CAAAR,CAAA;YAAA;cAAA,OAAAQ,SAAA,CAAAX,CAAA;UAAA;QAAA,GAAAO,QAAA;MAAA;IAEtC,CAAC;IAEDK,MAAM,WAANA,MAAMA,CAAAC,MAAA,EAAa;MAAA,IAAV9B,MAAM,GAAA8B,MAAA,CAAN9B,MAAM;MACbzN,OAAO,CAACuI,GAAG,CAAC,SAAS,CAAC;MACtBkF,MAAM,CAAC,YAAY,CAAC;;MAEpB;MACAzO,cAAc,CAAC6F,UAAU,CAAC,YAAY,CAAC;MACvC7F,cAAc,CAAC6F,UAAU,CAAC,oBAAoB,CAAC;MAC/C7F,cAAc,CAAC6F,UAAU,CAAC,qBAAqB,CAAC;MAChD7F,cAAc,CAAC6F,UAAU,CAAC,cAAc,CAAC;;MAEzC;MACA4I,MAAM,CAAC,gBAAgB,EAAE,EAAE,CAAC;MAC5BA,MAAM,CAAC,uBAAuB,EAAE,IAAI,CAAC;MACrCA,MAAM,CAAC,oBAAoB,CAAC;IAC9B,CAAC;IAED;IACM+B,aAAa,WAAbA,aAAaA,CAAAC,MAAA,EAA8B;MAAA,OAAAnC,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA4F,SAAA;QAAA,IAAAjC,MAAA,EAAApN,KAAA,EAAAqN,QAAA,EAAA7K,GAAA,EAAAG,MAAA,EAAA2K,QAAA,EAAAgC,GAAA;QAAA,OAAApC,YAAA,GAAAS,CAAA,WAAA4B,SAAA;UAAA,kBAAAA,SAAA,CAAA1B,CAAA;YAAA;cAA3BT,MAAM,GAAAgC,MAAA,CAANhC,MAAM,EAAEpN,KAAK,GAAAoP,MAAA,CAALpP,KAAK,EAAEqN,QAAQ,GAAA+B,MAAA,CAAR/B,QAAQ;cAAA,IACtCrN,KAAK,CAACE,KAAK;gBAAAqP,SAAA,CAAA1B,CAAA;gBAAA;cAAA;cAAA,OAAA0B,SAAA,CAAAlB,CAAA,IACP,KAAK;YAAA;cAAAkB,SAAA,CAAAzB,CAAA;cAAA,KAKR9N,KAAK,CAACM,WAAW;gBAAAiP,SAAA,CAAA1B,CAAA;gBAAA;cAAA;cACbrL,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;cAC1BC,MAAM,GAAG,IAAIF,IAAI,CAACzC,KAAK,CAACM,WAAW,CAAC,CAACoC,OAAO,CAAC,CAAC;cAAA,MAChDF,GAAG,IAAIG,MAAM;gBAAA4M,SAAA,CAAA1B,CAAA;gBAAA;cAAA;cACflO,OAAO,CAACuI,GAAG,CAAC,kBAAkB,CAAC;cAC/BmF,QAAQ,CAAC,QAAQ,CAAC;cAAC,OAAAkC,SAAA,CAAAlB,CAAA,IACZ,KAAK;YAAA;cAAAkB,SAAA,CAAA1B,CAAA;cAAA,OAKOvP,KAAK,CAACkR,GAAG,CAAC,eAAe,EAAE;gBAChDC,OAAO,EAAE;kBAAEC,aAAa,YAAA7P,MAAA,CAAYG,KAAK,CAACE,KAAK;gBAAG;cACpD,CAAC,CAAC;YAAA;cAFIoN,QAAQ,GAAAiC,SAAA,CAAApB,CAAA;cAAA,KAIVb,QAAQ,CAACc,IAAI;gBAAAmB,SAAA,CAAA1B,CAAA;gBAAA;cAAA;cACfT,MAAM,CAAC,UAAU,EAAEE,QAAQ,CAACc,IAAI,CAAC;cACjChB,MAAM,CAAC,uBAAuB,EAAE,QAAQ,CAAC;cAAC,OAAAmC,SAAA,CAAAlB,CAAA,IACnC,IAAI;YAAA;cAAA,OAAAkB,SAAA,CAAAlB,CAAA,IAGN,KAAK;YAAA;cAAAkB,SAAA,CAAAzB,CAAA;cAAAwB,GAAA,GAAAC,SAAA,CAAApB,CAAA;cAGZxO,OAAO,CAACD,KAAK,CAAC,cAAc,EAAA4P,GAAO,CAAC;cAEpC,IAAIA,GAAA,CAAMhC,QAAQ,IAAIgC,GAAA,CAAMhC,QAAQ,CAAC9H,MAAM,KAAK,GAAG,EAAE;gBACnD;gBACA6H,QAAQ,CAAC,QAAQ,CAAC;cACpB,CAAC,MAAM;gBACL;gBACAD,MAAM,CAAC,uBAAuB,EAAE,SAAS,CAAC;cAC5C;cAAC,OAAAmC,SAAA,CAAAlB,CAAA,IAEM,KAAK;UAAA;QAAA,GAAAgB,QAAA;MAAA;IAEhB,CAAC;IAED;IACAM,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAa;MAAA,IAAVxC,MAAM,GAAAwC,MAAA,CAANxC,MAAM;MACxB,IAAI;QACF;QACA,IAAM1L,aAAa,GAAG/C,cAAc,CAACkR,SAAS,CAAC,CAAC;QAChDzC,MAAM,CAAC,oBAAoB,EAAE1L,aAAa,CAAC;QAE3C/B,OAAO,CAACuI,GAAG,CAAC,oBAAoB,EAAExG,aAAa,CAAC;;QAEhD;QACA,IAAIA,aAAa,CAACsC,UAAU,EAAE;UAC5BrE,OAAO,CAACC,IAAI,CAAC,qBAAqB,EAAE8B,aAAa,CAACoO,WAAW,CAAC;QAChE;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,OAAOpQ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC1C,OAAO,KAAK;MACd;IACF,CAAC;IAED;IACMqQ,aAAa,WAAbA,aAAaA,CAAAC,MAAA,EAA8B;MAAA,OAAA/C,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAwG,SAAA;QAAA,IAAA7C,MAAA,EAAAC,QAAA,EAAArN,KAAA,EAAAmJ,OAAA,EAAA+G,GAAA,EAAAC,GAAA;QAAA,OAAAjD,YAAA,GAAAS,CAAA,WAAAyC,SAAA;UAAA,kBAAAA,SAAA,CAAAvC,CAAA;YAAA;cAA3BT,MAAM,GAAA4C,MAAA,CAAN5C,MAAM,EAAEC,QAAQ,GAAA2C,MAAA,CAAR3C,QAAQ,EAAErN,KAAK,GAAAgQ,MAAA,CAALhQ,KAAK;cAC3CL,OAAO,CAACuI,GAAG,CAAC,YAAY,CAAC;cAACkI,SAAA,CAAAtC,CAAA;cAGxB;cACAT,QAAQ,CAAC,mBAAmB,CAAC;;cAE7B;cAAA,MACIrN,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACC,IAAI;gBAAAmQ,SAAA,CAAAvC,CAAA;gBAAA;cAAA;cAC3BlO,OAAO,CAACuI,GAAG,CAAC,qBAAqB,CAAC;cAACkI,SAAA,CAAAvC,CAAA;cAAA,OACbR,QAAQ,CAAC,eAAe,CAAC;YAAA;cAAzClE,OAAO,GAAAiH,SAAA,CAAAjC,CAAA;cAAA,KAEThF,OAAO;gBAAAiH,SAAA,CAAAvC,CAAA;gBAAA;cAAA;cACTlO,OAAO,CAACuI,GAAG,CAAC,kBAAkB,CAAC;;cAE/B;cAAAkI,SAAA,CAAAtC,CAAA;cAAAsC,SAAA,CAAAvC,CAAA;cAAA,OAEQR,QAAQ,CAAC,iBAAiB,CAAC;YAAA;cAAA+C,SAAA,CAAAvC,CAAA;cAAA;YAAA;cAAAuC,SAAA,CAAAtC,CAAA;cAAAoC,GAAA,GAAAE,SAAA,CAAAjC,CAAA;cAEjCxO,OAAO,CAACC,IAAI,CAAC,WAAW,EAAAsQ,GAAO,CAAC;YAAC;cAAA,OAAAE,SAAA,CAAA/B,CAAA,IAG5B,IAAI;YAAA;cAIf1O,OAAO,CAACuI,GAAG,CAAC,WAAW,CAAC;cAAC,OAAAkI,SAAA,CAAA/B,CAAA,IAClB,KAAK;YAAA;cAAA+B,SAAA,CAAAtC,CAAA;cAAAqC,GAAA,GAAAC,SAAA,CAAAjC,CAAA;cAGZxO,OAAO,CAACD,KAAK,CAAC,YAAY,EAAAyQ,GAAO,CAAC;cAClC/C,MAAM,CAAC,uBAAuB,EAAE,SAAS,CAAC;cAAC,OAAAgD,SAAA,CAAA/B,CAAA,IACpC,KAAK;UAAA;QAAA,GAAA4B,QAAA;MAAA;IAEhB,CAAC;IAED;IACMI,eAAe,WAAfA,eAAeA,CAAAC,MAAA,EAA8B;MAAA,OAAArD,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA8G,SAAA;QAAA,IAAAnD,MAAA,EAAAC,QAAA,EAAArN,KAAA,EAAAmJ,OAAA,EAAAqH,GAAA;QAAA,OAAAtD,YAAA,GAAAS,CAAA,WAAA8C,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,CAAA;YAAA;cAA3BT,MAAM,GAAAkD,MAAA,CAANlD,MAAM,EAAEC,QAAQ,GAAAiD,MAAA,CAARjD,QAAQ,EAAErN,KAAK,GAAAsQ,MAAA,CAALtQ,KAAK;cAAA,IACxCA,KAAK,CAACE,KAAK;gBAAAuQ,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cAAA,OAAA4C,SAAA,CAAApC,CAAA,IACP,KAAK;YAAA;cAAAoC,SAAA,CAAA3C,CAAA;cAAA2C,SAAA,CAAA5C,CAAA;cAAA,OAIUR,QAAQ,CAAC,eAAe,CAAC;YAAA;cAAzClE,OAAO,GAAAsH,SAAA,CAAAtC,CAAA;cAAA,KACThF,OAAO;gBAAAsH,SAAA,CAAA5C,CAAA;gBAAA;cAAA;cAAA4C,SAAA,CAAA5C,CAAA;cAAA,OACHR,QAAQ,CAAC,iBAAiB,CAAC;YAAA;cACjCD,MAAM,CAAC,oBAAoB,EAAE,IAAI3K,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC,CAAC;cAAC,OAAAwL,SAAA,CAAApC,CAAA,IAChD,IAAI;YAAA;cAAA,OAAAoC,SAAA,CAAApC,CAAA,IAEN,KAAK;YAAA;cAAAoC,SAAA,CAAA3C,CAAA;cAAA0C,GAAA,GAAAC,SAAA,CAAAtC,CAAA;cAEZxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAA8Q,GAAO,CAAC;cAAC,OAAAC,SAAA,CAAApC,CAAA,IAC3B,KAAK;UAAA;QAAA,GAAAkC,QAAA;MAAA;IAEhB,CAAC;IAEKG,eAAe,WAAfA,eAAeA,CAAAC,MAAA,EAAoB;MAAA,OAAA1D,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAmH,SAAA;QAAA,IAAAxD,MAAA,EAAApN,KAAA,EAAA6Q,WAAA,EAAAvD,QAAA,EAAA/M,UAAA,EAAAuQ,eAAA,EAAAC,GAAA;QAAA,OAAA7D,YAAA,GAAAS,CAAA,WAAAqD,SAAA;UAAA,kBAAAA,SAAA,CAAAnD,CAAA;YAAA;cAAjBT,MAAM,GAAAuD,MAAA,CAANvD,MAAM,EAAEpN,KAAK,GAAA2Q,MAAA,CAAL3Q,KAAK;cAAAgR,SAAA,CAAAlD,CAAA;cAEjCnO,OAAO,CAACuI,GAAG,CAAC,mBAAmB,GAAA2I,WAAA,GAAE7Q,KAAK,CAACC,IAAI,cAAA4Q,WAAA,uBAAVA,WAAA,CAAY3N,EAAE,CAAC;cAAC,MAE7C,CAAClD,KAAK,CAACC,IAAI,IAAI,CAACD,KAAK,CAACC,IAAI,CAACiD,EAAE;gBAAA8N,SAAA,CAAAnD,CAAA;gBAAA;cAAA;cAAA,MACzB,IAAII,KAAK,CAAC,eAAe,CAAC;YAAA;cAAA+C,SAAA,CAAAnD,CAAA;cAAA,OAGXnP,UAAU,CAAC6B,UAAU,CAAC0Q,iBAAiB,CAACjR,KAAK,CAACC,IAAI,CAACiD,EAAE,CAAC;YAAA;cAAvEoK,QAAQ,GAAA0D,SAAA,CAAA7C,CAAA;cACdxO,OAAO,CAACuI,GAAG,CAAC,aAAa,EAAEoF,QAAQ,CAACc,IAAI,CAAC;;cAEzC;cACM7N,UAAU,GAAG+M,QAAQ,CAACc,IAAI,CAACA,IAAI,IAAId,QAAQ,CAACc,IAAI;cACtDzO,OAAO,CAACuI,GAAG,CAAC,cAAc,EAAE3H,UAAU,CAAC;cAEvC6M,MAAM,CAAC,gBAAgB,EAAE7M,UAAU,CAAC;cAAC,OAAAyQ,SAAA,CAAA3C,CAAA,IAC9B9N,UAAU;YAAA;cAAAyQ,SAAA,CAAAlD,CAAA;cAAAiD,GAAA,GAAAC,SAAA,CAAA7C,CAAA;cAEjBxO,OAAO,CAACD,KAAK,CAAC,aAAa,EAAAqR,GAAO,CAAC;cACnCpR,OAAO,CAACD,KAAK,CAAC,OAAO,EAAE,EAAAoR,eAAA,GAAAC,GAAA,CAAMzD,QAAQ,cAAAwD,eAAA,uBAAdA,eAAA,CAAgB1C,IAAI,KAAI2C,GAAA,CAAM9I,OAAO,CAAC;cAAC,MAAA8I,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAA3C,CAAA;UAAA;QAAA,GAAAuC,QAAA;MAAA;IAGlE,CAAC;IAEKM,eAAe,WAAfA,eAAeA,CAAAC,MAAA,EAAAC,MAAA,EAA8C;MAAA,OAAAnE,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA4H,SAAA;QAAA,IAAAjE,MAAA,EAAApN,KAAA,EAAAkD,EAAA,EAAAyD,aAAA,EAAA2G,QAAA,EAAAgE,GAAA;QAAA,OAAApE,YAAA,GAAAS,CAAA,WAAA4D,SAAA;UAAA,kBAAAA,SAAA,CAAA1D,CAAA;YAAA;cAA3CT,MAAM,GAAA+D,MAAA,CAAN/D,MAAM,EAAEpN,KAAK,GAAAmR,MAAA,CAALnR,KAAK;cAAMkD,EAAE,GAAAkO,MAAA,CAAFlO,EAAE,EAAKyD,aAAa,GAAA6K,wBAAA,CAAAJ,MAAA,EAAAK,SAAA;cAAAF,SAAA,CAAAzD,CAAA;cAAAyD,SAAA,CAAA1D,CAAA;cAAA,OAEpCvP,KAAK,CAACoT,GAAG,oBAAA7R,MAAA,CAAoBqD,EAAE,GAAIyD,aAAa,EAAE;gBACvE8I,OAAO,EAAE;kBAAEC,aAAa,YAAA7P,MAAA,CAAYG,KAAK,CAACE,KAAK;gBAAG;cACpD,CAAC,CAAC;YAAA;cAFIoN,QAAQ,GAAAiE,SAAA,CAAApD,CAAA;cAIdf,MAAM,CAAC,kBAAkB,EAAEE,QAAQ,CAACc,IAAI,CAAC;cAEzC,MAAM,CAAC,sBAAsB,CAAC,CAACuD,IAAI,CAAC,UAAAC,MAAM,EAAI;gBAC5C,IAAMC,gBAAgB,GAAGD,MAAM,WAAQ;gBACvC,IAAIC,gBAAgB,CAACC,WAAW,EAAE;kBAChC,IAAM1L,gBAAgB,GAAGpG,KAAK,CAACO,UAAU,CAACyC,IAAI,CAAC,UAAAC,CAAC;oBAAA,OAAIA,CAAC,CAACC,EAAE,KAAKA,EAAE;kBAAA,EAAC;kBAChE,IAAIkD,gBAAgB,EAAE;oBACpByL,gBAAgB,CAACE,mBAAmB,CAAC3L,gBAAgB,CAAC;kBACxD;gBACF;cACF,CAAC,CAAC;cAAC,OAAAmL,SAAA,CAAAlD,CAAA,IAEIf,QAAQ,CAACc,IAAI;YAAA;cAAAmD,SAAA,CAAAzD,CAAA;cAAAwD,GAAA,GAAAC,SAAA,CAAApD,CAAA;cAAA,MAAAmD,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAlD,CAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IAIxB,CAAC;IAEKW,eAAe,WAAfA,eAAeA,CAAAC,MAAA,EAAoB9O,WAAW,EAAE;MAAA,OAAA8J,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAyI,SAAA;QAAA,IAAA9E,MAAA,EAAApN,KAAA,EAAAsN,QAAA,EAAA6E,gBAAA,EAAAC,GAAA;QAAA,OAAAlF,YAAA,GAAAS,CAAA,WAAA0E,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,CAAA;YAAA;cAA9BT,MAAM,GAAA6E,MAAA,CAAN7E,MAAM,EAAEpN,KAAK,GAAAiS,MAAA,CAALjS,KAAK;cAAAqS,SAAA,CAAAvE,CAAA;cAEjCnO,OAAO,CAACuI,GAAG,CAAC,WAAW,EAAE/E,WAAW,CAAC;cAACkP,SAAA,CAAAxE,CAAA;cAAA,OAEfnP,UAAU,CAAC6B,UAAU,CAACyR,eAAe,CAAC7O,WAAW,CAAC;YAAA;cAAnEmK,QAAQ,GAAA+E,SAAA,CAAAlE,CAAA;cAAA,MAEVb,QAAQ,CAAC9H,MAAM,KAAK,GAAG;gBAAA6M,SAAA,CAAAxE,CAAA;gBAAA;cAAA;cACzBT,MAAM,CAAC,kBAAkB,EAAEjK,WAAW,CAAC;cACvCxD,OAAO,CAACuI,GAAG,CAAC,UAAU,CAAC;cAAC,OAAAmK,SAAA,CAAAhE,CAAA,IACjB,IAAI;YAAA;cAAAgE,SAAA,CAAAxE,CAAA;cAAA;YAAA;cAAAwE,SAAA,CAAAvE,CAAA;cAAAsE,GAAA,GAAAC,SAAA,CAAAlE,CAAA;cAGbxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAA0S,GAAO,CAAC;cACjCzS,OAAO,CAACD,KAAK,CAAC,OAAO,EAAE,EAAAyS,gBAAA,GAAAC,GAAA,CAAM9E,QAAQ,cAAA6E,gBAAA,uBAAdA,gBAAA,CAAgB/D,IAAI,KAAIgE,GAAA,CAAMnK,OAAO,CAAC;cAAC,MAAAmK,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAhE,CAAA;UAAA;QAAA,GAAA6D,QAAA;MAAA;IAGlE,CAAC;IAEKI,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAoB;MAAA,OAAAtF,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA+I,SAAA;QAAA,IAAApF,MAAA,EAAApN,KAAA,EAAAsN,QAAA,EAAAmF,gBAAA,EAAAC,SAAA,EAAAC,GAAA;QAAA,OAAAzF,YAAA,GAAAS,CAAA,WAAAiF,SAAA;UAAA,kBAAAA,SAAA,CAAA/E,CAAA;YAAA;cAAjBT,MAAM,GAAAmF,MAAA,CAANnF,MAAM,EAAEpN,KAAK,GAAAuS,MAAA,CAALvS,KAAK;cAAA4S,SAAA,CAAA9E,CAAA;cAE5BnO,OAAO,CAACuI,GAAG,CAAC,aAAa,CAAC;cAAC0K,SAAA,CAAA/E,CAAA;cAAA,OACJvP,KAAK,CAACkR,GAAG,CAAC,aAAa,EAAE;gBAC9CC,OAAO,EAAE;kBAAEC,aAAa,YAAA7P,MAAA,CAAYG,KAAK,CAACE,KAAK;gBAAG;cACpD,CAAC,CAAC;YAAA;cAFIoN,QAAQ,GAAAsF,SAAA,CAAAzE,CAAA;cAIdxO,OAAO,CAACuI,GAAG,CAAC,SAAS,EAAEoF,QAAQ,CAACc,IAAI,CAAC;cACrChB,MAAM,CAAC,WAAW,EAAEE,QAAQ,CAACc,IAAI,CAAC;cAAC,OAAAwE,SAAA,CAAAvE,CAAA,IAC5Bf,QAAQ,CAACc,IAAI;YAAA;cAAAwE,SAAA,CAAA9E,CAAA;cAAA6E,GAAA,GAAAC,SAAA,CAAAzE,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEiT,GAAA,CAAM1K,OAAO,GAAAwK,gBAAA,GAAEE,GAAA,CAAMrF,QAAQ,cAAAmF,gBAAA,uBAAdA,gBAAA,CAAgBjN,MAAM,CAAC;cAAC,MAE5DmN,GAAA,CAAM1K,OAAO,CAACf,QAAQ,CAAC,eAAe,CAAC,IAAIyL,GAAA,CAAMpE,IAAI,KAAK,cAAc;gBAAAqE,SAAA,CAAA/E,CAAA;gBAAA;cAAA;cAC1ElO,OAAO,CAACuI,GAAG,CAAC,QAAQ,CAAC;cACfwK,SAAS,GAAG,CAChB;gBAAExP,EAAE,EAAE,CAAC;gBAAEsJ,IAAI,EAAE,OAAO;gBAAEqG,WAAW,EAAE,cAAc;gBAAEC,SAAS,EAAE,CAAC;gBAAEC,UAAU,EAAE;cAAM,CAAC,EACtF;gBAAE7P,EAAE,EAAE,CAAC;gBAAEsJ,IAAI,EAAE,OAAO;gBAAEqG,WAAW,EAAE,YAAY;gBAAEC,SAAS,EAAE,CAAC;gBAAEC,UAAU,EAAE;cAAM,CAAC,CACrF;cACD3F,MAAM,CAAC,WAAW,EAAEsF,SAAS,CAAC;cAAC,OAAAE,SAAA,CAAAvE,CAAA,IACxBqE,SAAS;YAAA;cAAA,MAAAC,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAvE,CAAA;UAAA;QAAA,GAAAmE,QAAA;MAAA;IAKtB,CAAC;IAEKQ,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAoBpL,QAAQ,EAAE;MAAA,OAAAoF,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAyJ,SAAA;QAAA,IAAA9F,MAAA,EAAApN,KAAA,EAAAsN,QAAA,EAAA6F,OAAA,EAAAC,IAAA;QAAA,OAAAlG,YAAA,GAAAS,CAAA,WAAA0F,SAAA;UAAA,kBAAAA,SAAA,CAAAxF,CAAA;YAAA;cAA3BT,MAAM,GAAA6F,MAAA,CAAN7F,MAAM,EAAEpN,KAAK,GAAAiT,MAAA,CAALjT,KAAK;cAAAqT,SAAA,CAAAvF,CAAA;cAAAuF,SAAA,CAAAxF,CAAA;cAAA,OAELvP,KAAK,CAAC4P,IAAI,CAAC,aAAa,EAAA1H,aAAA,CAAAA,aAAA,KAC1CqB,QAAQ;gBACXiL,SAAS,EAAE9S,KAAK,CAACC,IAAI,CAACiD;cAAE,IACvB;gBACDuM,OAAO,EAAE;kBAAEC,aAAa,YAAA7P,MAAA,CAAYG,KAAK,CAACE,KAAK;gBAAG;cACpD,CAAC,CAAC;YAAA;cALIoN,QAAQ,GAAA+F,SAAA,CAAAlF,CAAA;cAORgF,OAAO,GAAG7F,QAAQ,CAACc,IAAI;cAC7BhB,MAAM,CAAC,WAAW,KAAAvN,MAAA,CAAAyT,kBAAA,CAAMtT,KAAK,CAAClB,KAAK,IAAEqU,OAAO,EAAC,CAAC;cAAC,OAAAE,SAAA,CAAAhF,CAAA,IAExC8E,OAAO;YAAA;cAAAE,SAAA,CAAAvF,CAAA;cAAAsF,IAAA,GAAAC,SAAA,CAAAlF,CAAA;cAAA,MAAAiF,IAAA;YAAA;cAAA,OAAAC,SAAA,CAAAhF,CAAA;UAAA;QAAA,GAAA6E,QAAA;MAAA;IAIlB,CAAC;IAEKK,QAAQ,WAARA,QAAQA,CAAAC,MAAA,EAAAC,MAAA,EAAyB;MAAA,OAAAxG,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAiK,SAAA;QAAA,IAAAtG,MAAA,EAAAxE,MAAA,EAAA+K,aAAA,EAAArG,QAAA,EAAAzF,QAAA,EAAA+L,QAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAA5G,YAAA,GAAAS,CAAA,WAAAoG,SAAA;UAAA,kBAAAA,SAAA,CAAAlG,CAAA;YAAA;cAAtBT,MAAM,GAAAoG,MAAA,CAANpG,MAAM;cAAMxE,MAAM,GAAA6K,MAAA,CAAN7K,MAAM;cAAAmL,SAAA,CAAAjG,CAAA;cAE/B;cACM6F,aAAa,GAAGtS,QAAQ,CAACuH,MAAM,CAAC;cAAA,KAElCoL,KAAK,CAACL,aAAa,CAAC;gBAAAI,SAAA,CAAAlG,CAAA;gBAAA;cAAA;cACtBlO,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEkJ,MAAM,CAAC;cAAC,MAC5B,IAAIqF,KAAK,CAAC,SAAS,CAAC;YAAA;cAG5BtO,OAAO,CAACuI,GAAG,kDAAArI,MAAA,CAAe8T,aAAa,CAAE,CAAC;cAACI,SAAA,CAAAjG,CAAA;cAAAiG,SAAA,CAAAlG,CAAA;cAAA,OAGlBnP,UAAU,CAACuV,OAAO,CAACN,aAAa,CAAC;YAAA;cAAlDrG,QAAQ,GAAAyG,SAAA,CAAA5F,CAAA;cAAA,KACVb,QAAQ,CAACc,IAAI;gBAAA2F,SAAA,CAAAlG,CAAA;gBAAA;cAAA;cACf;cACMhG,QAAQ,GAAArB,aAAA,CAAAA,aAAA,KACT8G,QAAQ,CAACc,IAAI;gBAChBlL,EAAE,EAAE7B,QAAQ,CAACiM,QAAQ,CAACc,IAAI,CAAClL,EAAE;cAAC;cAEhCvD,OAAO,CAACuI,GAAG,CAAC,cAAc,EAAEL,QAAQ,CAAC;cACrCuF,MAAM,CAAC,kBAAkB,EAAEvF,QAAQ,CAAC;cAAC,OAAAkM,SAAA,CAAA1F,CAAA,IAC9BxG,QAAQ;YAAA;cAAA,MAET,IAAIoG,KAAK,CAAC,OAAO,CAAC;YAAA;cAAA8F,SAAA,CAAAlG,CAAA;cAAA;YAAA;cAAAkG,SAAA,CAAAjG,CAAA;cAAA+F,IAAA,GAAAE,SAAA,CAAA5F,CAAA;cAG1BxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAAmU,IAAO,CAAC;cAC/B;cACMD,QAAQ,GAAG;gBACf1Q,EAAE,EAAEyQ,aAAa;gBACjBnH,IAAI,8BAAA3M,MAAA,CAAU8T,aAAa,CAAE;gBAC7Bd,WAAW,EAAE,aAAa;gBAC1BC,SAAS,EAAE,CAAC;gBACZC,UAAU,EAAE,KAAK;gBACjBmB,OAAO,EAAE;cACX,CAAC;cACDvU,OAAO,CAACuI,GAAG,CAAC,SAAS,EAAE0L,QAAQ,CAAC;cAChCxG,MAAM,CAAC,kBAAkB,EAAEwG,QAAQ,CAAC;cAAC,OAAAG,SAAA,CAAA1F,CAAA,IAC9BuF,QAAQ;YAAA;cAAAG,SAAA,CAAAlG,CAAA;cAAA;YAAA;cAAAkG,SAAA,CAAAjG,CAAA;cAAAgG,IAAA,GAAAC,SAAA,CAAA5F,CAAA;cAGjBxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAAoU,IAAO,CAAC;cAAC,MAAAA,IAAA;YAAA;cAAA,OAAAC,SAAA,CAAA1F,CAAA;UAAA;QAAA,GAAAqF,QAAA;MAAA;IAGtC,CAAC;IAEDS,SAAS,WAATA,SAASA,CAAAC,MAAA,EAAa;MAAA,IAAVhH,MAAM,GAAAgH,MAAA,CAANhH,MAAM;MAChBA,MAAM,CAAC,oBAAoB,CAAC;IAC9B,CAAC;IAEKiH,cAAc,WAAdA,cAAcA,CAAAC,MAAA,EAAAC,MAAA,EAAsC;MAAA,OAAAtH,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA+K,UAAA;QAAA,IAAApH,MAAA,EAAApN,KAAA,EAAA4I,MAAA,EAAA4D,IAAA,EAAAiI,IAAA;QAAA,OAAAvH,YAAA,GAAAS,CAAA,WAAA+G,UAAA;UAAA,kBAAAA,UAAA,CAAA7G,CAAA;YAAA;cAAnCT,MAAM,GAAAkH,MAAA,CAANlH,MAAM,EAAEpN,KAAK,GAAAsU,MAAA,CAALtU,KAAK;cAAM4I,MAAM,GAAA2L,MAAA,CAAN3L,MAAM,EAAE4D,IAAI,GAAA+H,MAAA,CAAJ/H,IAAI;cAAAkI,UAAA,CAAA5G,CAAA;cAElD;cACA,IAAI9N,KAAK,CAAC8H,WAAW,IAAI9H,KAAK,CAAC8H,WAAW,CAAC5E,EAAE,KAAK0F,MAAM,EAAE;gBACxDwE,MAAM,CAAC,kBAAkB,EAAE;kBAAExE,MAAM,EAANA,MAAM;kBAAE4D,IAAI,EAAJA;gBAAK,CAAC,CAAC;cAC9C;;cAEA;cACA,IAAIxM,KAAK,CAAClB,KAAK,IAAIkB,KAAK,CAAClB,KAAK,CAACS,MAAM,GAAG,CAAC,EAAE;gBACzC6N,MAAM,CAAC,uBAAuB,EAAE;kBAAExE,MAAM,EAANA,MAAM;kBAAE4D,IAAI,EAAJA;gBAAK,CAAC,CAAC;cACnD;;cAEA;cACA,IAAI;gBACF,IAAImI,MAAM,CAAChW,cAAc,EAAE;kBACzBgW,MAAM,CAAChW,cAAc,CAAC+F,OAAO,cAAA7E,MAAA,CAAc+I,MAAM,GAAI4D,IAAI,CAAC;gBAC5D,CAAC,MAAM;kBACLoI,YAAY,CAAClQ,OAAO,cAAA7E,MAAA,CAAc+I,MAAM,GAAI4D,IAAI,CAAC;gBACnD;cACF,CAAC,CAAC,OAAO9M,KAAK,EAAE;gBACdC,OAAO,CAACC,IAAI,CAAC,mBAAmB,EAAEF,KAAK,CAACuI,OAAO,CAAC;cAClD;;cAEA;cACA;cACA;AACR;AACA;AACA;AACA;AACA;AACA;cANQ,OAAAyM,UAAA,CAAArG,CAAA,IAQO,IAAI;YAAA;cAAAqG,UAAA,CAAA5G,CAAA;cAAA2G,IAAA,GAAAC,UAAA,CAAAvG,CAAA;cAEXxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAA+U,IAAO,CAAC;cACjC;cAAA,OAAAC,UAAA,CAAArG,CAAA,IACO,KAAK;UAAA;QAAA,GAAAmG,SAAA;MAAA;IAEhB,CAAC;IAEDK,WAAW,WAAXA,WAAWA,CAAAC,MAAA,EAAa;MAAA,IAAV1H,MAAM,GAAA0H,MAAA,CAAN1H,MAAM;MAClBA,MAAM,CAAC,cAAc,CAAC;IACxB,CAAC;IAED2H,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAwB/M,OAAO,EAAE;MAAA,IAA9BmF,MAAM,GAAA4H,MAAA,CAAN5H,MAAM;QAAE6H,SAAS,GAAAD,MAAA,CAATC,SAAS;MAC5B7H,MAAM,CAAC,aAAa,EAAEnF,OAAO,CAAC;MAE9B1J,OAAO,CAAC2W,IAAI,CAAC,aAAa,EAAEjN,OAAO,CAAC;MAEpC,OAAOA,OAAO;IAChB,CAAC;IAEDkN,aAAa,WAAbA,aAAaA,CAAAC,MAAA,EAAa;MAAA,IAAVhI,MAAM,GAAAgI,MAAA,CAANhI,MAAM;MACpBA,MAAM,CAAC,gBAAgB,CAAC;IAC1B,CAAC;IAEDiI,aAAa,WAAbA,aAAaA,CAAAC,MAAA,EAAAC,MAAA,EAAiC;MAAA,IAA9BnI,MAAM,GAAAkI,MAAA,CAANlI,MAAM;MAAA,IAAM/G,KAAK,GAAAkP,MAAA,CAALlP,KAAK;QAAE4B,OAAO,GAAAsN,MAAA,CAAPtN,OAAO;MACxCmF,MAAM,CAAC,gBAAgB,EAAE;QAAE/G,KAAK,EAALA,KAAK;QAAE4B,OAAO,EAAPA;MAAQ,CAAC,CAAC;IAC9C,CAAC;IAEDuN,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAa;MAAA,IAAVrI,MAAM,GAAAqI,MAAA,CAANrI,MAAM;MACxBA,MAAM,CAAC,qBAAqB,CAAC;IAC/B,CAAC;IAEDsI,yBAAyB,WAAzBA,yBAAyBA,CAAAC,MAAA,EAAahP,aAAa,EAAE;MAAA,IAAzByG,MAAM,GAAAuI,MAAA,CAANvI,MAAM;MAChCA,MAAM,CAAC,8BAA8B,EAAEzG,aAAa,CAAC;IACvD,CAAC;IAEDiP,wBAAwB,WAAxBA,wBAAwBA,CAAAC,MAAA,EAAAC,MAAA,EAAoD;MAAA,IAAjD1I,MAAM,GAAAyI,MAAA,CAANzI,MAAM;QAAEpN,KAAK,GAAA6V,MAAA,CAAL7V,KAAK;QAAEqN,QAAQ,GAAAwI,MAAA,CAARxI,QAAQ;MAAA,IAAMnK,EAAE,GAAA4S,MAAA,CAAF5S,EAAE;QAAE8D,KAAK,GAAA8O,MAAA,CAAL9O,KAAK;QAAEC,KAAK,GAAA6O,MAAA,CAAL7O,KAAK;MACtEmG,MAAM,CAAC,4BAA4B,EAAE;QAAElK,EAAE,EAAFA,EAAE;QAAE8D,KAAK,EAALA,KAAK;QAAEC,KAAK,EAALA;MAAM,CAAC,CAAC;MAE1D,MAAM,CAAC,sBAAsB,CAAC,CAAC0K,IAAI,CAAC,UAAAC,MAAM,EAAI;QAC5C,IAAMC,gBAAgB,GAAGD,MAAM,WAAQ;QACvC,IAAIC,gBAAgB,CAACC,WAAW,EAAE;UAChCD,gBAAgB,CAACkE,4BAA4B,CAAC7S,EAAE,EAAE8D,KAAK,EAAEC,KAAK,CAAC;QACjE;MACF,CAAC,CAAC;IACJ,CAAC;IAED+O,mBAAmB,WAAnBA,mBAAmBA,CAAAC,MAAA,EAAuB9S,WAAW,EAAE;MAAA,IAAjCiK,MAAM,GAAA6I,MAAA,CAAN7I,MAAM;QAAEC,QAAQ,GAAA4I,MAAA,CAAR5I,QAAQ;MACpCD,MAAM,CAAC,uBAAuB,EAAEjK,WAAW,CAAC;MAE5CkK,QAAQ,CAAC,mBAAmB,EAAElK,WAAW,CAAC;IAC5C,CAAC;IAED+S,iBAAiB,WAAjBA,iBAAiBA,CAAAC,MAAA,EAAoBhT,WAAW,EAAE;MAAA,IAA9BiK,MAAM,GAAA+I,MAAA,CAAN/I,MAAM;QAAEpN,KAAK,GAAAmW,MAAA,CAALnW,KAAK;MAC/BoN,MAAM,CAAC,8BAA8B,EAAE;QACrCjK,WAAW,EAAXA,WAAW;QACXqC,MAAM,EAAE;UAAEnC,OAAO,EAAE;QAAK;MAC1B,CAAC,CAAC;MAEF,MAAM,CAAC,sBAAsB,CAAC,CAACsO,IAAI,CAAC,UAAAC,MAAM,EAAI;QAC5C,IAAMC,gBAAgB,GAAGD,MAAM,WAAQ;QACvC,IAAIC,gBAAgB,CAACC,WAAW,EAAE;UAChCD,gBAAgB,CAACuE,oBAAoB,CAACjT,WAAW,CAAC;QACpD,CAAC,MAAM;UACL,IAAMkT,UAAU,GAAGrW,KAAK,CAACiB,mBAAmB,CAACkC,WAAW,CAAC;UACzD,IAAIkT,UAAU,EAAE;YACdjJ,MAAM,CAAC,8BAA8B,EAAEiJ,UAAU,CAAC;UACpD;UAEAjJ,MAAM,CAAC,8BAA8B,EAAE;YACrCjK,WAAW,EAAXA,WAAW;YACXqC,MAAM,EAAE;cACNnC,OAAO,EAAE,KAAK;cACdD,QAAQ,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;cAClCvF,KAAK,EAAE;YACT;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED4W,gBAAgB,WAAhBA,gBAAgBA,CAAAC,MAAA,EAAoB;MAAA,IAAjBnJ,MAAM,GAAAmJ,MAAA,CAANnJ,MAAM;QAAEpN,KAAK,GAAAuW,MAAA,CAALvW,KAAK;MAC9B,IAAI,CAACA,KAAK,CAAC8H,WAAW,EAAE;MAExB,IAAMc,MAAM,GAAG5I,KAAK,CAAC8H,WAAW,CAAC5E,EAAE;MACnC,IAAMsT,cAAc,GAAG7X,cAAc,CAACc,OAAO,kBAAAI,MAAA,CAAkB+I,MAAM,CAAE,CAAC;MAExE,IAAI4N,cAAc,EAAE;QAClB,IAAI;UACF,IAAM/V,QAAQ,GAAGmE,IAAI,CAACC,KAAK,CAAC2R,cAAc,CAAC;UAC3CpJ,MAAM,CAAC,cAAc,EAAE3M,QAAQ,CAAC;QAClC,CAAC,CAAC,OAAOf,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;MACF;IACF,CAAC;IAED+W,SAAS,WAATA,SAASA,CAAAC,MAAA,EAAavV,MAAM,EAAE;MAAA,IAAlBiM,MAAM,GAAAsJ,MAAA,CAANtJ,MAAM;MAChBA,MAAM,CAAC,YAAY,EAAEjM,MAAM,CAAC;IAC9B,CAAC;IAEDwV,QAAQ,WAARA,QAAQA,CAAAC,MAAA,EAAa9M,KAAK,EAAE;MAAA,IAAjBsD,MAAM,GAAAwJ,MAAA,CAANxJ,MAAM;MACfA,MAAM,CAAC,WAAW,EAAEtD,KAAK,CAAC;IAC5B,CAAC;IAED+M,WAAW,WAAXA,WAAWA,CAAAC,MAAA,EAAAC,MAAA,EAA+B;MAAA,IAA5B3J,MAAM,GAAA0J,MAAA,CAAN1J,MAAM;MAAA,IAAM/G,KAAK,GAAA0Q,MAAA,CAAL1Q,KAAK;QAAEyD,KAAK,GAAAiN,MAAA,CAALjN,KAAK;MACpCsD,MAAM,CAAC,cAAc,EAAE;QAAE/G,KAAK,EAALA,KAAK;QAAEyD,KAAK,EAALA;MAAM,CAAC,CAAC;IAC1C,CAAC;IAEDkN,WAAW,WAAXA,WAAWA,CAAAC,MAAA,EAAa5Q,KAAK,EAAE;MAAA,IAAjB+G,MAAM,GAAA6J,MAAA,CAAN7J,MAAM;MAClBA,MAAM,CAAC,cAAc,EAAE/G,KAAK,CAAC;IAC/B,CAAC;IAED6Q,oBAAoB,WAApBA,oBAAoBA,CAAAC,MAAA,EAAa9Q,KAAK,EAAE;MAAA,IAAjB+G,MAAM,GAAA+J,MAAA,CAAN/J,MAAM;MAC3BA,MAAM,CAAC,yBAAyB,EAAE/G,KAAK,CAAC;IAC1C,CAAC;IAED+Q,QAAQ,WAARA,QAAQA,CAAAC,MAAA,EAAAC,MAAA,EAAqC;MAAA,IAAlClK,MAAM,GAAAiK,MAAA,CAANjK,MAAM;MAAA,IAAMjK,WAAW,GAAAmU,MAAA,CAAXnU,WAAW;QAAE7B,KAAK,GAAAgW,MAAA,CAALhW,KAAK;MACvC8L,MAAM,CAAC,WAAW,EAAE;QAAEjK,WAAW,EAAXA,WAAW;QAAE7B,KAAK,EAALA;MAAM,CAAC,CAAC;IAC7C,CAAC;IAEDiW,OAAO,WAAPA,OAAOA,CAAAC,MAAA,EAAAC,MAAA,EAAoC;MAAA,IAAjCrK,MAAM,GAAAoK,MAAA,CAANpK,MAAM;MAAA,IAAMjK,WAAW,GAAAsU,MAAA,CAAXtU,WAAW;QAAEoH,IAAI,GAAAkN,MAAA,CAAJlN,IAAI;MACrC,IAAI,CAACA,IAAI,CAACmN,SAAS,EAAE;QACnBnN,IAAI,CAACmN,SAAS,GAAG,IAAIjV,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;MAC3C;MACAmI,MAAM,CAAC,UAAU,EAAE;QAAEjK,WAAW,EAAXA,WAAW;QAAEoH,IAAI,EAAJA;MAAK,CAAC,CAAC;IAC3C,CAAC;IAEDoN,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAAC,MAAA,EAA2C;MAAA,IAAxCzK,MAAM,GAAAwK,MAAA,CAANxK,MAAM;MAAA,IAAMjK,WAAW,GAAA0U,MAAA,CAAX1U,WAAW;QAAEkD,KAAK,GAAAwR,MAAA,CAALxR,KAAK;QAAEkE,IAAI,GAAAsN,MAAA,CAAJtN,IAAI;MAC/CA,IAAI,CAACuN,SAAS,GAAG,IAAIrV,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC,CAAC;MACzCmI,MAAM,CAAC,aAAa,EAAE;QAAEjK,WAAW,EAAXA,WAAW;QAAEkD,KAAK,EAALA,KAAK;QAAEkE,IAAI,EAAJA;MAAK,CAAC,CAAC;IACrD,CAAC;IAEDwN,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAAC,MAAA,EAAqC;MAAA,IAAlC7K,MAAM,GAAA4K,MAAA,CAAN5K,MAAM;MAAA,IAAMjK,WAAW,GAAA8U,MAAA,CAAX9U,WAAW;QAAEkD,KAAK,GAAA4R,MAAA,CAAL5R,KAAK;MACzC+G,MAAM,CAAC,aAAa,EAAE;QAAEjK,WAAW,EAAXA,WAAW;QAAEkD,KAAK,EAALA;MAAM,CAAC,CAAC;IAC/C,CAAC;IAED6R,aAAa,WAAbA,aAAaA,CAAAC,MAAA,EAAoB;MAAA,IAAjB/K,MAAM,GAAA+K,MAAA,CAAN/K,MAAM;QAAEpN,KAAK,GAAAmY,MAAA,CAALnY,KAAK;MAC3B,IAAMmB,MAAM,GAAGyD,IAAI,CAACC,KAAK,CAAClG,cAAc,CAACc,OAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;MACnE,IAAI0B,MAAM,CAAC5B,MAAM,GAAG,CAAC,EAAE;QACrB6N,MAAM,CAAC,YAAY,EAAEjM,MAAM,CAAC;MAC9B;MAEA,IAAMC,iBAAiB,GAAGC,QAAQ,CAAC1C,cAAc,CAACc,OAAO,CAAC,mBAAmB,CAAC,IAAI,GAAG,CAAC;MACtF,IAAI2B,iBAAiB,IAAI,CAAC,IAAIA,iBAAiB,GAAGD,MAAM,CAAC5B,MAAM,EAAE;QAC/D6N,MAAM,CAAC,yBAAyB,EAAEhM,iBAAiB,CAAC;MACtD;MAEA,IAAME,KAAK,GAAGsD,IAAI,CAACC,KAAK,CAAClG,cAAc,CAACc,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;MACjE,IAAI6B,KAAK,EAAE;QACT8W,MAAM,CAACC,IAAI,CAAC/W,KAAK,CAAC,CAACgX,OAAO,CAAC,UAAAnV,WAAW,EAAI;UACxCiK,MAAM,CAAC,WAAW,EAAE;YAAEjK,WAAW,EAAXA,WAAW;YAAE7B,KAAK,EAAEA,KAAK,CAAC6B,WAAW;UAAE,CAAC,CAAC;QACjE,CAAC,CAAC;MACJ;IACF,CAAC;IAEDoV,cAAc,WAAdA,cAAcA,CAAAC,MAAA,EAAa1N,SAAS,EAAE;MAAA,IAArBsC,MAAM,GAAAoL,MAAA,CAANpL,MAAM;MACrBA,MAAM,CAAC,kBAAkB,EAAEtC,SAAS,CAAC;MAErC,IAAIA,SAAS,EAAE;QACbnL,OAAO,CAACuI,GAAG,CAAC,SAAS,CAAC;QACtB;QACA,IAAI,CAACmF,QAAQ,CAAC,YAAY,EAAE;UAC1BjF,IAAI,EAAE,QAAQ;UACdqQ,OAAO,EAAE,mBAAmB;UAC5BtQ,SAAS,EAAE,IAAI1F,IAAI,CAAC,CAAC,CAACwC,WAAW,CAAC;QACpC,CAAC,CAAC;MACJ;IACF,CAAC;IAEKyT,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAAC,MAAA,EAAkD;MAAA,OAAA3L,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAoP,UAAA;QAAA,IAAAzL,MAAA,EAAAxE,MAAA,EAAAkQ,qBAAA,EAAAC,gBAAA,EAAAzL,QAAA,EAAA0L,IAAA;QAAA,OAAA9L,YAAA,GAAAS,CAAA,WAAAsL,UAAA;UAAA,kBAAAA,UAAA,CAAApL,CAAA;YAAA;cAA/CT,MAAM,GAAAuL,MAAA,CAANvL,MAAM;cAAMxE,MAAM,GAAAgQ,MAAA,CAANhQ,MAAM,EAAAkQ,qBAAA,GAAAF,MAAA,CAAEG,gBAAgB,EAAhBA,gBAAgB,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;cAC5D1L,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAAC6L,UAAA,CAAAnL,CAAA;cAAAmL,UAAA,CAAApL,CAAA;cAAA,OAGNnP,UAAU,CAACuF,SAAS,CAACiV,YAAY,CAACtQ,MAAM,EAAE,CAAC,EAAE,GAAG,EAAEmQ,gBAAgB,CAAC;YAAA;cAApFzL,QAAQ,GAAA2L,UAAA,CAAA9K,CAAA;cACdf,MAAM,CAAC,WAAW,EAAEE,QAAQ,CAACc,IAAI,IAAI,EAAE,CAAC;cAAC,OAAA6K,UAAA,CAAA5K,CAAA,IAClCf,QAAQ,CAACc,IAAI,IAAI,EAAE;YAAA;cAAA6K,UAAA,CAAAnL,CAAA;cAAAkL,IAAA,GAAAC,UAAA,CAAA9K,CAAA;cAE1BxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAAsZ,IAAO,CAAC;cACjC5L,MAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC;cAAC,MAAA4L,IAAA;YAAA;cAAAC,UAAA,CAAAnL,CAAA;cAGrCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAA6L,UAAA,CAAAzK,CAAA;YAAA;cAAA,OAAAyK,UAAA,CAAA5K,CAAA;UAAA;QAAA,GAAAwK,SAAA;MAAA;IAEvC,CAAC;IAEKM,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAaC,QAAQ,EAAE;MAAA,OAAApM,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA6P,UAAA;QAAA,IAAAlM,MAAA,EAAAE,QAAA,EAAAiM,IAAA;QAAA,OAAArM,YAAA,GAAAS,CAAA,WAAA6L,UAAA;UAAA,kBAAAA,UAAA,CAAA3L,CAAA;YAAA;cAApBT,MAAM,GAAAgM,MAAA,CAANhM,MAAM;cACvBA,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAACoM,UAAA,CAAA1L,CAAA;cAAA0L,UAAA,CAAA3L,CAAA;cAAA,OAGNnP,UAAU,CAACuF,SAAS,CAACkV,UAAU,CAACE,QAAQ,CAAC;YAAA;cAA1D/L,QAAQ,GAAAkM,UAAA,CAAArL,CAAA;cACdf,MAAM,CAAC,UAAU,EAAEE,QAAQ,CAACc,IAAI,CAAC;cAAC,OAAAoL,UAAA,CAAAnL,CAAA,IAC3Bf,QAAQ,CAACc,IAAI;YAAA;cAAAoL,UAAA,CAAA1L,CAAA;cAAAyL,IAAA,GAAAC,UAAA,CAAArL,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAA6Z,IAAO,CAAC;cAC/BnM,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;cAAC,MAAAmM,IAAA;YAAA;cAAAC,UAAA,CAAA1L,CAAA;cAGnCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAAoM,UAAA,CAAAhL,CAAA;YAAA;cAAA,OAAAgL,UAAA,CAAAnL,CAAA;UAAA;QAAA,GAAAiL,SAAA;MAAA;IAEvC,CAAC;IAEKG,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAAC,MAAA,EAAqC;MAAA,OAAA1M,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAmQ,UAAA;QAAA,IAAAxM,MAAA,EAAA9B,MAAA,EAAAuO,UAAA,EAAAvM,QAAA,EAAAwM,IAAA;QAAA,OAAA5M,YAAA,GAAAS,CAAA,WAAAoM,UAAA;UAAA,kBAAAA,UAAA,CAAAlM,CAAA;YAAA;cAAlCT,MAAM,GAAAsM,MAAA,CAANtM,MAAM;cAAM9B,MAAM,GAAAqO,MAAA,CAANrO,MAAM,EAAEuO,UAAU,GAAAF,MAAA,CAAVE,UAAU;cAC/CzM,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAAC2M,UAAA,CAAAjM,CAAA;cAAAiM,UAAA,CAAAlM,CAAA;cAAA,OAGNnP,UAAU,CAACuF,SAAS,CAACwV,UAAU,CAACnO,MAAM,EAAEuO,UAAU,CAAC;YAAA;cAApEvM,QAAQ,GAAAyM,UAAA,CAAA5L,CAAA;cACdf,MAAM,CAAC,aAAa,EAAEE,QAAQ,CAACc,IAAI,CAAC;cAAC,OAAA2L,UAAA,CAAA1L,CAAA,IAC9Bf,QAAQ,CAACc,IAAI;YAAA;cAAA2L,UAAA,CAAAjM,CAAA;cAAAgM,IAAA,GAAAC,UAAA,CAAA5L,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAAoa,IAAO,CAAC;cAC/B1M,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;cAAC,MAAA0M,IAAA;YAAA;cAAAC,UAAA,CAAAjM,CAAA;cAGnCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAA2M,UAAA,CAAAvL,CAAA;YAAA;cAAA,OAAAuL,UAAA,CAAA1L,CAAA;UAAA;QAAA,GAAAuL,SAAA;MAAA;IAEvC,CAAC;IAEKI,UAAU,WAAVA,UAAUA,CAAAC,MAAA,EAAa3O,MAAM,EAAE;MAAA,OAAA2B,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAyQ,UAAA;QAAA,IAAA9M,MAAA,EAAAE,QAAA,EAAA6M,IAAA;QAAA,OAAAjN,YAAA,GAAAS,CAAA,WAAAyM,UAAA;UAAA,kBAAAA,UAAA,CAAAvM,CAAA;YAAA;cAAlBT,MAAM,GAAA6M,MAAA,CAAN7M,MAAM;cACvBA,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAACgN,UAAA,CAAAtM,CAAA;cAAAsM,UAAA,CAAAvM,CAAA;cAAA,OAGNnP,UAAU,CAACuF,SAAS,CAAC+V,UAAU,CAAC1O,MAAM,CAAC;YAAA;cAAxDgC,QAAQ,GAAA8M,UAAA,CAAAjM,CAAA;cAAA,MACVb,QAAQ,CAACc,IAAI,IAAId,QAAQ,CAACc,IAAI,CAACiM,OAAO;gBAAAD,UAAA,CAAAvM,CAAA;gBAAA;cAAA;cACxCT,MAAM,CAAC,aAAa,EAAE9B,MAAM,CAAC;cAAC,OAAA8O,UAAA,CAAA/L,CAAA,IACvB,IAAI;YAAA;cAAA,OAAA+L,UAAA,CAAA/L,CAAA,IAEN,KAAK;YAAA;cAAA+L,UAAA,CAAAtM,CAAA;cAAAqM,IAAA,GAAAC,UAAA,CAAAjM,CAAA;cAEZxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAAya,IAAO,CAAC;cAC/B/M,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;cAAC,MAAA+M,IAAA;YAAA;cAAAC,UAAA,CAAAtM,CAAA;cAGnCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAAgN,UAAA,CAAA5L,CAAA;YAAA;cAAA,OAAA4L,UAAA,CAAA/L,CAAA;UAAA;QAAA,GAAA6L,SAAA;MAAA;IAEvC,CAAC;IAEKI,cAAc,WAAdA,cAAcA,CAAAC,MAAA,EAAAC,MAAA,EAAkE;MAAA,OAAAvN,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAgR,UAAA;QAAA,IAAArN,MAAA,EAAAxE,MAAA,EAAA8R,SAAA,EAAArB,QAAA,EAAAsB,kBAAA,EAAArN,QAAA,EAAAsN,IAAA;QAAA,OAAA1N,YAAA,GAAAS,CAAA,WAAAkN,UAAA;UAAA,kBAAAA,UAAA,CAAAhN,CAAA;YAAA;cAA/DT,MAAM,GAAAmN,MAAA,CAANnN,MAAM;cAAMxE,MAAM,GAAA4R,MAAA,CAAN5R,MAAM,EAAE8R,SAAS,GAAAF,MAAA,CAATE,SAAS,EAAErB,QAAQ,GAAAmB,MAAA,CAARnB,QAAQ,EAAEsB,kBAAkB,GAAAH,MAAA,CAAlBG,kBAAkB;cAChFvN,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAACyN,UAAA,CAAA/M,CAAA;cAAA+M,UAAA,CAAAhN,CAAA;cAAA,OAGNnP,UAAU,CAACuF,SAAS,CAACqW,cAAc,CACxD1R,MAAM,EACN8R,SAAS,EACTrB,QAAQ,EACRsB,kBACF,CAAC;YAAA;cALKrN,QAAQ,GAAAuN,UAAA,CAAA1M,CAAA;cAOd,IAAIwM,kBAAkB,EAAE;gBACtBvN,MAAM,CAAC,aAAa,EAAEE,QAAQ,CAACc,IAAI,CAAC;cACtC,CAAC,MAAM;gBACLhB,MAAM,CAAC,UAAU,EAAEE,QAAQ,CAACc,IAAI,CAAC;cACnC;cAAC,OAAAyM,UAAA,CAAAxM,CAAA,IAEMf,QAAQ,CAACc,IAAI;YAAA;cAAAyM,UAAA,CAAA/M,CAAA;cAAA8M,IAAA,GAAAC,UAAA,CAAA1M,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAAkb,IAAO,CAAC;cACjCxN,MAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC;cAAC,MAAAwN,IAAA;YAAA;cAAAC,UAAA,CAAA/M,CAAA;cAGrCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAAyN,UAAA,CAAArM,CAAA;YAAA;cAAA,OAAAqM,UAAA,CAAAxM,CAAA;UAAA;QAAA,GAAAoM,SAAA;MAAA;IAEvC,CAAC;IAEKK,gBAAgB,WAAhBA,gBAAgBA,CAAAC,MAAA,EAAazP,MAAM,EAAE;MAAA,OAAA2B,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAuR,UAAA;QAAA,IAAA5N,MAAA,EAAAE,QAAA,EAAA2N,IAAA;QAAA,OAAA/N,YAAA,GAAAS,CAAA,WAAAuN,UAAA;UAAA,kBAAAA,UAAA,CAAArN,CAAA;YAAA;cAAlBT,MAAM,GAAA2N,MAAA,CAAN3N,MAAM;cAC7BA,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC;cAAC8N,UAAA,CAAApN,CAAA;cAAAoN,UAAA,CAAArN,CAAA;cAAA,OAGhBnP,UAAU,CAACuF,SAAS,CAACkX,cAAc,CAAC7P,MAAM,CAAC;YAAA;cAA5DgC,QAAQ,GAAA4N,UAAA,CAAA/M,CAAA;cACdf,MAAM,CAAC,kBAAkB,EAAEE,QAAQ,CAACc,IAAI,IAAI,EAAE,CAAC;cAAC,OAAA8M,UAAA,CAAA7M,CAAA,IACzCf,QAAQ,CAACc,IAAI,IAAI,EAAE;YAAA;cAAA8M,UAAA,CAAApN,CAAA;cAAAmN,IAAA,GAAAC,UAAA,CAAA/M,CAAA;cAE1BxO,OAAO,CAACD,KAAK,CAAC,aAAa,EAAAub,IAAO,CAAC;cAAC,MAAAA,IAAA;YAAA;cAAAC,UAAA,CAAApN,CAAA;cAGpCV,MAAM,CAAC,0BAA0B,EAAE,KAAK,CAAC;cAAC,OAAA8N,UAAA,CAAA1M,CAAA;YAAA;cAAA,OAAA0M,UAAA,CAAA7M,CAAA;UAAA;QAAA,GAAA2M,SAAA;MAAA;IAE9C,CAAC;IAEKI,qBAAqB,WAArBA,qBAAqBA,CAAAC,MAAA,EAAAC,MAAA,EAAkC;MAAA,OAAArO,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA8R,UAAA;QAAA,IAAAnO,MAAA,EAAA9B,MAAA,EAAAkQ,OAAA,EAAAlO,QAAA,EAAAmO,IAAA;QAAA,OAAAvO,YAAA,GAAAS,CAAA,WAAA+N,UAAA;UAAA,kBAAAA,UAAA,CAAA7N,CAAA;YAAA;cAA/BT,MAAM,GAAAiO,MAAA,CAANjO,MAAM;cAAM9B,MAAM,GAAAgQ,MAAA,CAANhQ,MAAM,EAAEkQ,OAAO,GAAAF,MAAA,CAAPE,OAAO;cAAAE,UAAA,CAAA5N,CAAA;cAAA4N,UAAA,CAAA7N,CAAA;cAAA,OAE9BnP,UAAU,CAACuF,SAAS,CAACmX,qBAAqB,CAAC9P,MAAM,EAAEkQ,OAAO,CAAC;YAAA;cAA5ElO,QAAQ,GAAAoO,UAAA,CAAAvN,CAAA;cAAA,OAAAuN,UAAA,CAAArN,CAAA,IACPf,QAAQ,CAACc,IAAI;YAAA;cAAAsN,UAAA,CAAA5N,CAAA;cAAA2N,IAAA,GAAAC,UAAA,CAAAvN,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAA+b,IAAO,CAAC;cAAC,MAAAA,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAArN,CAAA;UAAA;QAAA,GAAAkN,SAAA;MAAA;IAGtC,CAAC;IAEKI,cAAc,WAAdA,cAAcA,CAAAC,MAAA,EAAAC,MAAA,EAAkC;MAAA,OAAA5O,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAqS,UAAA;QAAA,IAAA1O,MAAA,EAAA9B,MAAA,EAAAkQ,OAAA,EAAAlO,QAAA,EAAAyO,IAAA;QAAA,OAAA7O,YAAA,GAAAS,CAAA,WAAAqO,UAAA;UAAA,kBAAAA,UAAA,CAAAnO,CAAA;YAAA;cAA/BT,MAAM,GAAAwO,MAAA,CAANxO,MAAM;cAAM9B,MAAM,GAAAuQ,MAAA,CAANvQ,MAAM,EAAEkQ,OAAO,GAAAK,MAAA,CAAPL,OAAO;cAChDpO,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAAC4O,UAAA,CAAAlO,CAAA;cAAAkO,UAAA,CAAAnO,CAAA;cAAA,OAGNnP,UAAU,CAACuF,SAAS,CAAC0X,cAAc,CAACrQ,MAAM,EAAEkQ,OAAO,CAAC;YAAA;cAArElO,QAAQ,GAAA0O,UAAA,CAAA7N,CAAA;cACdf,MAAM,CAAC,aAAa,EAAEE,QAAQ,CAACc,IAAI,CAAC;cAAC,OAAA4N,UAAA,CAAA3N,CAAA,IAC9Bf,QAAQ,CAACc,IAAI;YAAA;cAAA4N,UAAA,CAAAlO,CAAA;cAAAiO,IAAA,GAAAC,UAAA,CAAA7N,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAAqc,IAAO,CAAC;cACjC3O,MAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC;cAAC,MAAA2O,IAAA;YAAA;cAAAC,UAAA,CAAAlO,CAAA;cAGrCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAA4O,UAAA,CAAAxN,CAAA;YAAA;cAAA,OAAAwN,UAAA,CAAA3N,CAAA;UAAA;QAAA,GAAAyN,SAAA;MAAA;IAEvC,CAAC;IAEKG,QAAQ,WAARA,QAAQA,CAAAC,MAAA,EAAuBjR,IAAI,EAAE;MAAA,OAAAgC,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA0S,UAAA;QAAA,IAAA/O,MAAA,EAAAC,QAAA,EAAAgM,QAAA,EAAA+C,IAAA;QAAA,OAAAlP,YAAA,GAAAS,CAAA,WAAA0O,UAAA;UAAA,kBAAAA,UAAA,CAAAxO,CAAA;YAAA;cAA1BT,MAAM,GAAA8O,MAAA,CAAN9O,MAAM,EAAEC,QAAQ,GAAA6O,MAAA,CAAR7O,QAAQ;cAC/BD,MAAM,CAAC,mBAAmB,EAAE,IAAI,CAAC;cACjCA,MAAM,CAAC,gBAAgB,EAAE,IAAI,CAAC;cAACiP,UAAA,CAAAvO,CAAA;cAG7B;cACAV,MAAM,CAAC,kBAAkB,EAAEnC,IAAI,CAAC;;cAEhC;cACMoO,QAAQ,GAAGpO,IAAI,CAACqR,SAAS,EAE/B;cACA,IAAIjD,QAAQ,CAAC5Y,QAAQ,EAAE;gBACrB2M,MAAM,CAAC,cAAc,EAAEiM,QAAQ,CAAC5Y,QAAQ,CAAC;cAC3C;cAEA,IAAI4Y,QAAQ,CAAC9Y,UAAU,EAAE;gBACvB6M,MAAM,CAAC,gBAAgB,EAAEiM,QAAQ,CAAC9Y,UAAU,CAAC;cAC/C;cAEA,IAAI8Y,QAAQ,CAAClY,MAAM,EAAE;gBACnBiM,MAAM,CAAC,YAAY,EAAEiM,QAAQ,CAAClY,MAAM,CAAC;gBACrC,IAAIkY,QAAQ,CAAC5V,YAAY,KAAKjE,SAAS,EAAE;kBACvC4N,MAAM,CAAC,yBAAyB,EAAEiM,QAAQ,CAAC5V,YAAY,CAAC;gBAC1D;cACF;cAEA,IAAI4V,QAAQ,CAACkD,WAAW,EAAE;gBACxBnP,MAAM,CAAC,kBAAkB,EAAEiM,QAAQ,CAACkD,WAAW,CAAC;cAClD;cAEA,IAAIlD,QAAQ,CAAC/X,KAAK,EAAE;gBAClB8L,MAAM,CAAC,WAAW,EAAEiM,QAAQ,CAAC/X,KAAK,CAAC;cACrC;cAEA,IAAI+X,QAAQ,CAACmD,KAAK,EAAE;gBAClBpP,MAAM,CAAC,WAAW,EAAEiM,QAAQ,CAACmD,KAAK,CAAC;cACrC;cAEA,IAAInD,QAAQ,CAAC1Y,UAAU,EAAE;gBACvByM,MAAM,CAAC,iBAAiB,EAAEiM,QAAQ,CAAC1Y,UAAU,CAAC;cAChD;cAAC,OAAA0b,UAAA,CAAAhO,CAAA,IAEM,IAAI;YAAA;cAAAgO,UAAA,CAAAvO,CAAA;cAAAsO,IAAA,GAAAC,UAAA,CAAAlO,CAAA;cAEXxO,OAAO,CAACD,KAAK,CAAC,SAAS,EAAA0c,IAAO,CAAC;cAC/BhP,MAAM,CAAC,gBAAgB,EAAE,QAAQ,CAAC;cAAC,MAAAgP,IAAA;YAAA;cAAAC,UAAA,CAAAvO,CAAA;cAGnCV,MAAM,CAAC,mBAAmB,EAAE,KAAK,CAAC;cAAC,OAAAiP,UAAA,CAAA7N,CAAA;YAAA;cAAA,OAAA6N,UAAA,CAAAhO,CAAA;UAAA;QAAA,GAAA8N,SAAA;MAAA;IAEvC,CAAC;IAIKM,eAAe,WAAfA,eAAeA,CAAAC,MAAA,EAA8B/V,aAAa,EAAE;MAAA,OAAAsG,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAkT,UAAA;QAAA,IAAAvP,MAAA,EAAAC,QAAA,EAAArN,KAAA,EAAA4c,YAAA,EAAAC,MAAA,EAAAvP,QAAA,EAAAwP,IAAA;QAAA,OAAA5P,YAAA,GAAAS,CAAA,WAAAoP,UAAA;UAAA,kBAAAA,UAAA,CAAAlP,CAAA;YAAA;cAA1CT,MAAM,GAAAsP,MAAA,CAANtP,MAAM,EAAEC,QAAQ,GAAAqP,MAAA,CAARrP,QAAQ,EAAErN,KAAK,GAAA0c,MAAA,CAAL1c,KAAK;cAAA+c,UAAA,CAAAjP,CAAA;cAE3CnO,OAAO,CAACuI,GAAG,CAAC,UAAU,EAAEvB,aAAa,CAAC6F,IAAI,CAAC;;cAE3C;cACMqQ,MAAM,GAAG,EAAAD,YAAA,GAAA5c,KAAK,CAACC,IAAI,cAAA2c,YAAA,uBAAVA,YAAA,CAAY1Z,EAAE,KAAI,CAAC;cAClCvD,OAAO,CAACuI,GAAG,CAAC,SAAS,EAAE2U,MAAM,CAAC;cAACE,UAAA,CAAAlP,CAAA;cAAA,OAERnP,UAAU,CAAC6B,UAAU,CAACkc,eAAe,CAAC9V,aAAa,EAAEkW,MAAM,CAAC;YAAA;cAA7EvP,QAAQ,GAAAyP,UAAA,CAAA5O,CAAA;cAAA,KAEVb,QAAQ,CAACc,IAAI;gBAAA2O,UAAA,CAAAlP,CAAA;gBAAA;cAAA;cACf;cACAT,MAAM,CAAC,eAAe,EAAEE,QAAQ,CAACc,IAAI,CAAC;cACtCzO,OAAO,CAACuI,GAAG,CAAC,WAAW,EAAEoF,QAAQ,CAACc,IAAI,CAAC5B,IAAI,CAAC;cAAC,OAAAuQ,UAAA,CAAA1O,CAAA,IACtCf,QAAQ,CAACc,IAAI;YAAA;cAAA2O,UAAA,CAAAlP,CAAA;cAAA;YAAA;cAAAkP,UAAA,CAAAjP,CAAA;cAAAgP,IAAA,GAAAC,UAAA,CAAA5O,CAAA;cAGtBxO,OAAO,CAACD,KAAK,CAAC,WAAW,EAAAod,IAAO,CAAC;cAAC,MAAAA,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAA1O,CAAA;UAAA;QAAA,GAAAsO,SAAA;MAAA;IAGtC,CAAC;IAEKK,kBAAkB,WAAlBA,kBAAkBA,CAAAC,MAAA,EAAoB;MAAA,OAAAhQ,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAAyT,UAAA;QAAA,IAAA9P,MAAA,EAAApN,KAAA,EAAAsN,QAAA,EAAA6P,IAAA;QAAA,OAAAjQ,YAAA,GAAAS,CAAA,WAAAyP,UAAA;UAAA,kBAAAA,UAAA,CAAAvP,CAAA;YAAA;cAAjBT,MAAM,GAAA6P,MAAA,CAAN7P,MAAM,EAAEpN,KAAK,GAAAid,MAAA,CAALjd,KAAK;cAAAod,UAAA,CAAAtP,CAAA;cAAA,MAEhC,CAAC9N,KAAK,CAACC,IAAI,IAAI,CAACD,KAAK,CAACC,IAAI,CAACiD,EAAE;gBAAAka,UAAA,CAAAvP,CAAA;gBAAA;cAAA;cAC/BlO,OAAO,CAACuI,GAAG,CAAC,iBAAiB,CAAC;cAAC,OAAAkV,UAAA,CAAA/O,CAAA;YAAA;cAAA+O,UAAA,CAAAvP,CAAA;cAAA,OAIVnP,UAAU,CAAC6B,UAAU,CAAC0Q,iBAAiB,CAACjR,KAAK,CAACC,IAAI,CAACiD,EAAE,CAAC;YAAA;cAAvEoK,QAAQ,GAAA8P,UAAA,CAAAjP,CAAA;cACd,IAAIb,QAAQ,CAACc,IAAI,EAAE;gBACjBhB,MAAM,CAAC,gBAAgB,EAAEE,QAAQ,CAACc,IAAI,CAAC;gBACvCzO,OAAO,CAACuI,GAAG,CAAC,aAAa,EAAEoF,QAAQ,CAACc,IAAI,CAAC7O,MAAM,EAAE,KAAK,CAAC;cACzD;cAAC6d,UAAA,CAAAvP,CAAA;cAAA;YAAA;cAAAuP,UAAA,CAAAtP,CAAA;cAAAqP,IAAA,GAAAC,UAAA,CAAAjP,CAAA;cAEDxO,OAAO,CAACD,KAAK,CAAC,aAAa,EAAAyd,IAAO,CAAC;YAAC;cAAA,OAAAC,UAAA,CAAA/O,CAAA;UAAA;QAAA,GAAA6O,SAAA;MAAA;IAExC,CAAC;IAEKG,oBAAoB,WAApBA,oBAAoBA,CAAAC,MAAA,EAAY3W,aAAa,EAAE;MAAA,OAAAsG,iBAAA,cAAAC,YAAA,GAAAzD,CAAA,UAAA8T,UAAA;QAAA,IAAAvd,KAAA,EAAAsN,QAAA,EAAAkQ,IAAA;QAAA,OAAAtQ,YAAA,GAAAS,CAAA,WAAA8P,UAAA;UAAA,kBAAAA,UAAA,CAAA5P,CAAA;YAAA;cAAxB7N,KAAK,GAAAsd,MAAA,CAALtd,KAAK;cAAAyd,UAAA,CAAA3P,CAAA;cAAA2P,UAAA,CAAA5P,CAAA;cAAA,OAEPnP,UAAU,CAAC6B,UAAU,CAAC8c,oBAAoB,CAAC1W,aAAa,CAAC;YAAA;cAA1E2G,QAAQ,GAAAmQ,UAAA,CAAAtP,CAAA;cAAA,OAAAsP,UAAA,CAAApP,CAAA,IACPf,QAAQ,CAACc,IAAI;YAAA;cAAAqP,UAAA,CAAA3P,CAAA;cAAA0P,IAAA,GAAAC,UAAA,CAAAtP,CAAA;cAEpBxO,OAAO,CAACD,KAAK,CAAC,YAAY,EAAA8d,IAAO,CAAC;cAAC,MAAAA,IAAA;YAAA;cAAA,OAAAC,UAAA,CAAApP,CAAA;UAAA;QAAA,GAAAkP,SAAA;MAAA;IAGvC;EACF,CAAC;EACDG,OAAO,EAAE;IACP7e,IAAI,EAAJA,IAAI;IACJC,KAAK,EAALA;EACF,CAAC;EACD6e,OAAO,EAAE,CAAC/e,iBAAiB;AAC7B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}