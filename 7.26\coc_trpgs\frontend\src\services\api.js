import axios from 'axios';

// 创建带有基础URL的axios实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 安全获取token的函数
function safeGetToken() {
  try {
    if (window.storageManager) {
      return window.storageManager.getItem('token');
    }
    return localStorage.getItem('token');
  } catch (error) {
    console.warn('[API] 无法获取token:', error.message);
    return null;
  }
}

function safeGetAdminToken() {
  try {
    if (window.storageManager) {
      return window.storageManager.getItem('admin_token');
    }
    return localStorage.getItem('admin_token');
  } catch (error) {
    console.warn('[API] 无法获取admin token:', error.message);
    return null;
  }
}

// 请求拦截器，添加认证token
apiClient.interceptors.request.use(config => {
  const token = safeGetToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, error => {
  return Promise.reject(error);
});

// 创建管理员API客户端实例
const adminApiClient = axios.create({
  baseURL: process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// 请求拦截器，添加管理员认证token
adminApiClient.interceptors.request.use(config => {
  const adminToken = safeGetAdminToken();
  if (adminToken) {
    config.headers.Authorization = `Bearer ${adminToken}`;
  }
  return config;
}, error => {
  return Promise.reject(error);
});

// 消息服务
const messagesService = {
  getMessages(roomId, params = {}) {
    return apiClient.get(`/api/rooms/${roomId}/messages`, { params });
  },
  
  sendMessage(roomId, message) {
    return apiClient.post(`/api/rooms/${roomId}/messages`, message);
  }
};

// AI服务
const aiService = {
  sendMessage(data) {
    return apiClient.post('/api/ai/chat', data);
  },
  
  processScenario(data) {
    return apiClient.post('/api/ai/scenario', data);
  },
  
  testConnection(data) {
    return apiClient.post('/api/ai-settings/test-connection', data);
  },
  
  clearMemories() {
    return apiClient.post('/api/ai/clear-memories');
  }
};

// KP服务
const kpService = {
  loadScenario(scenarioPath) {
    return apiClient.post('/api/ai-kp/load-scenario', { scenarioPath });
  },
  
  asyncLoadScenario(scenarioPath) {
    return apiClient.post('/api/ai-kp/async-load-scenario', { scenarioPath });
  },
  
  getDocumentTaskStatus(taskId) {
    return apiClient.get(`/api/ai-kp/document-task-status/${taskId}`);
  },
  
  completeAsyncScenario(taskId) {
    return apiClient.post('/api/ai-kp/complete-async-scenario', { taskId });
  },
  
  processDocument(filePath) {
    return apiClient.post('/api/ai-kp/process-document', { filePath });
  },
  
  getCacheStats() {
    return apiClient.get('/api/ai-kp/cache-stats');
  },
  
  clearExpiredCache() {
    return apiClient.post('/api/ai-kp/clear-expired-cache');
  },
  
  getAvailableScenarios() {
    return apiClient.get('/api/ai-kp/available-scenarios');
  },
  
  startGame(data) {
    return apiClient.post('/api/ai-kp/start-game', data);
  },
  
  advanceScene() {
    return apiClient.post('/api/ai-kp/advance-scene');
  },
  
  processInput(data) {
    return apiClient.post('/api/ai-kp/process-input', data);
  }
};

// 房间服务
const roomsService = {
  getRooms(params = {}) {
    return apiClient.get('/api/rooms/', { params });
  },
  
  getRoom(roomId) {
    return apiClient.get(`/api/rooms/${roomId}/`);
  },
  
  createRoom(data) {
    return apiClient.post('/api/rooms/', data);
  },
  
  updateRoom(roomId, data) {
    return apiClient.put(`/api/rooms/${roomId}/`, data);
  },
  
  deleteRoom(roomId) {
    return apiClient.delete(`/api/rooms/${roomId}/`);
  },
  
  joinRoom(roomId, data) {
    return apiClient.post(`/api/rooms/${roomId}/join/`, data);
  },
  
  leaveRoom(roomId) {
    return apiClient.post(`/api/rooms/${roomId}/leave/`);
  }
};

// 用户服务
const usersService = {
  login(data) {
    return apiClient.post('/api/users/login', data);
  },
  
  register(data) {
    return apiClient.post('/api/users/register', data);
  },
  
  logout() {
    return apiClient.post('/api/users/logout');
  },
  
  getCurrentUser() {
    return apiClient.get('/api/users/me');
  },
  
  getUser(userId) {
    return apiClient.get(`/api/users/${userId}`);
  }
};

// 角色服务
const charactersService = {
  getCharacters(params = {}) {
    return apiClient.get('/api/characters', { params });
  },
  
  getUserCharacters(userId) {
    return apiClient.get(`/api/users/${userId}/characters`);
  },
  
  getCharacter(characterId) {
    return apiClient.get(`/api/characters/${characterId}`);
  },
  
  createCharacter(data, userId = null) {
    // 如果没有传入userId，使用默认值1
    const targetUserId = userId || 1;
    return apiClient.post(`/users/${targetUserId}/characters/`, data);
  },
  
  updateCharacter(characterId, data) {
    return apiClient.put(`/characters/${characterId}`, data);
  },

  deleteCharacter(characterId) {
    return apiClient.delete(`/characters/${characterId}`);
  }
};

// 骰子服务
const diceService = {
  roll(data) {
    return apiClient.post('/api/roll', data);
  },
  
  cocRoll(data) {
    return apiClient.post('/api/coc-roll', data);
  },
  
  sanityCheck(data) {
    return apiClient.post('/api/sanity-check', data);
  }
};

// 游戏存档服务
const savesService = {
  getSaves(roomId, params = {}) {
    return apiClient.get(`/api/rooms/${roomId}/game-saves`, { params });
  },
  
  getSave(saveId) {
    return apiClient.get(`/api/game-saves/${saveId}`);
  },
  
  createSave(data) {
    return apiClient.post('/api/game-saves', data);
  },
  
  updateSave(saveId, data) {
    return apiClient.put(`/api/game-saves/${saveId}`, data);
  },
  
  deleteSave(saveId) {
    return apiClient.delete(`/api/game-saves/${saveId}`);
  },
  
  autoSave(roomId, userId, data, previousAutoSaveId) {
    return apiClient.post('/api/game-saves/auto-save', {
      roomId, 
      creatorId: userId, 
      saveData: data, 
      previousAutoSaveId
    });
  }
};

// 管理员服务
const adminService = {
  login(data) {
    console.log('管理员登录API调用，使用路径: /api/users/login', data);
    return apiClient.post('/api/users/login', data);
  },
  
  getSystemStats() {
    return adminApiClient.get('/admin/system-stats');
  },
  
  getUsers() {
    return adminApiClient.get('/admin/users');
  },
  
  getUserDetails(userId) {
    return adminApiClient.get(`/admin/users/${userId}`);
  },
  
  toggleAdminStatus(userId) {
    return adminApiClient.put(`/admin/users/${userId}/toggle-admin`);
  },
  
  toggleUserActiveStatus(userId) {
    return adminApiClient.put(`/admin/users/${userId}/toggle-active`);
  },
  
  deleteUser(userId) {
    return adminApiClient.delete(`/admin/users/${userId}`);
  },
  
  getRooms() {
    return adminApiClient.get('/admin/rooms');
  },
  
  getRoomDetails(roomId) {
    return adminApiClient.get(`/admin/rooms/${roomId}`);
  },
  
  deleteRoom(roomId) {
    return adminApiClient.delete(`/admin/rooms/${roomId}`);
  },
  
  getOccupations() {
    return apiClient.get('/api/occupations/');
  },

  getSkills() {
    return apiClient.get('/api/skills/');
  },

  getWeapons() {
    return apiClient.get('/rules/weapons');
  },

  getArmors() {
    return apiClient.get('/rules/armors');
  },

  // 角色管理API (已移至charactersService，此处保留兼容性)
  createCharacter(characterData, userId = null) {
    const targetUserId = userId || 1;
    return apiClient.post(`/users/${targetUserId}/characters/`, characterData);
  },

  getCharacter(characterId) {
    return apiClient.get(`/api/characters/${characterId}`);
  },



  // 角色创建辅助API
  calculateSkillPoints(occupationData) {
    return apiClient.post('/api/calculate-skill-points/', occupationData);
  },

  validateAttributes(attributeData) {
    return apiClient.post('/api/characters/validate-attributes', attributeData);
  },

  validateSkills(skillData) {
    return apiClient.post('/api/characters/validate-skills', skillData);
  },

  addOccupation(occupationData) {
    return adminApiClient.post('/admin/occupations', occupationData);
  }
};

// 导出服务
const apiService = {
  messages: messagesService,
  ai: aiService,
  kp: kpService,
  rooms: roomsService,
  users: usersService,
  characters: charactersService,
  dice: diceService,
  saves: savesService,
  admin: adminService
};

export default apiService; 