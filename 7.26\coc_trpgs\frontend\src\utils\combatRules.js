/**
 * COC 7版战斗规则引擎
 * 严格按照《克苏鲁的呼唤》第七版规则书实现
 */

import { diceRoller } from './diceRoller.js'

export class CombatRules {
  /**
   * 计算伤害加值 (严格按规则书)
   * @param {number} str 力量值
   * @param {number} siz 体型值
   * @returns {Object} 伤害加值对象
   */
  static calculateDamageBonus(str, siz) {
    const total = str + siz
    
    if (total < 65) {
      return { 
        bonus: -2, 
        text: '-2',
        maxBonus: -2,
        rollFormula: null
      }
    }
    if (total < 85) {
      return { 
        bonus: -1, 
        text: '-1',
        maxBonus: -1,
        rollFormula: null
      }
    }
    if (total < 125) {
      return { 
        bonus: 0, 
        text: '0',
        maxBonus: 0,
        rollFormula: null
      }
    }
    if (total < 165) {
      return { 
        bonus: '1d4', 
        text: '+1d4',
        maxBonus: 4,
        rollFormula: '1d4'
      }
    }
    if (total < 205) {
      return { 
        bonus: '1d6', 
        text: '+1d6',
        maxBonus: 6,
        rollFormula: '1d6'
      }
    }
    
    // 205以上每80点增加1d6
    const extraDice = Math.floor((total - 205) / 80) + 1
    return { 
      bonus: `${extraDice + 1}d6`, 
      text: `+${extraDice + 1}d6`,
      maxBonus: (extraDice + 1) * 6,
      rollFormula: `${extraDice + 1}d6`
    }
  }

  /**
   * 计算体格 (严格按规则书)
   * @param {number} str 力量值
   * @param {number} siz 体型值
   * @returns {number} 体格值
   */
  static calculateBuild(str, siz) {
    const total = str + siz
    
    if (total < 65) return -2
    if (total < 85) return -1
    if (total < 125) return 0
    if (total < 165) return 1
    if (total < 205) return 2
    
    // 205以上每80点增加1点体格
    return Math.floor((total - 205) / 80) + 3
  }

  /**
   * 计算移动速度 (严格按规则书)
   * @param {number} dex 敏捷值
   * @param {number} con 体质值
   * @param {number} str 力量值
   * @param {number} siz 体型值
   * @returns {number} 移动速度
   */
  static calculateMovementRate(dex, con, str, siz) {
    // 基础移动速度
    let moveRate = 8
    
    // 年龄影响 (这里暂时不考虑，可以后续添加)
    
    // 如果DEX和STR都大于SIZ，移动速度+1
    if (dex > siz && str > siz) {
      moveRate += 1
    }
    
    // 如果DEX和STR都小于SIZ，移动速度-1
    if (dex < siz && str < siz) {
      moveRate -= 1
    }
    
    return Math.max(1, moveRate) // 最小移动速度为1
  }

  /**
   * 判断成功等级 (严格按规则书)
   * @param {number} roll 投骰结果 (1-100)
   * @param {number} skillValue 技能值
   * @returns {string} 成功等级
   */
  static getSuccessLevel(roll, skillValue) {
    // 大成功：投出01
    if (roll === 1) {
      return 'critical'
    }
    
    // 大失败判定
    if (skillValue >= 50) {
      // 技能值≥50时，投出100为大失败
      if (roll === 100) {
        return 'fumble'
      }
    } else {
      // 技能值<50时，投出96-100为大失败
      if (roll >= 96) {
        return 'fumble'
      }
    }
    
    // 失败：投出大于技能值
    if (roll > skillValue) {
      return 'failure'
    }
    
    // 极难成功：投出≤技能值/5
    if (roll <= Math.floor(skillValue / 5)) {
      return 'extreme'
    }
    
    // 困难成功：投出≤技能值/2
    if (roll <= Math.floor(skillValue / 2)) {
      return 'hard'
    }
    
    // 常规成功：投出≤技能值
    return 'regular'
  }

  /**
   * 对抗检定 (严格按规则书)
   * @param {Object} attacker 攻击者数据 {roll, skill, name}
   * @param {Object} defender 防御者数据 {roll, skill, name}
   * @returns {Object} 对抗结果
   */
  static resolveOpposedRoll(attacker, defender) {
    const attackerLevel = this.getSuccessLevel(attacker.roll, attacker.skill)
    const defenderLevel = this.getSuccessLevel(defender.roll, defender.skill)
    
    // 成功等级排序 (从低到高)
    const levels = ['fumble', 'failure', 'regular', 'hard', 'extreme', 'critical']
    const attackerIndex = levels.indexOf(attackerLevel)
    const defenderIndex = levels.indexOf(defenderLevel)
    
    let winner, winnerLevel
    
    if (attackerIndex > defenderIndex) {
      winner = 'attacker'
      winnerLevel = attackerLevel
    } else if (defenderIndex > attackerIndex) {
      winner = 'defender'
      winnerLevel = defenderLevel
    } else {
      // 平手时的处理
      if (attackerLevel === 'fumble') {
        // 双方都大失败，攻击者获胜
        winner = 'attacker'
        winnerLevel = attackerLevel
      } else {
        // 其他情况平手时，攻击者获胜 (近战反击规则)
        winner = 'attacker'
        winnerLevel = attackerLevel
      }
    }
    
    return {
      winner,
      winnerLevel,
      attackerLevel,
      defenderLevel,
      attackerRoll: attacker.roll,
      defenderRoll: defender.roll,
      description: this.getOpposedRollDescription(winner, winnerLevel, attacker.name, defender.name)
    }
  }

  /**
   * 获取对抗检定描述
   */
  static getOpposedRollDescription(winner, level, attackerName, defenderName) {
    const levelDescriptions = {
      critical: '大成功',
      extreme: '极难成功',
      hard: '困难成功',
      regular: '常规成功',
      failure: '失败',
      fumble: '大失败'
    }
    
    const winnerName = winner === 'attacker' ? attackerName : defenderName
    const levelDesc = levelDescriptions[level] || level
    
    return `${winnerName} 以 ${levelDesc} 获胜`
  }

  /**
   * 计算射击难度等级 (严格按规则书)
   * @param {number} distance 距离 (码)
   * @param {Object} weaponRange 武器射程 {base, long, extreme}
   * @returns {string} 难度等级
   */
  static getShootingDifficulty(distance, weaponRange) {
    if (distance <= weaponRange.base) {
      return 'regular' // 基础射程内：常规难度
    }
    if (distance <= weaponRange.base * 2) {
      return 'hard' // 远射程(2倍基础射程)：困难难度
    }
    if (distance <= weaponRange.base * 4) {
      return 'extreme' // 超射程(4倍基础射程)：极难难度
    }
    return 'impossible' // 超出射程：不可能
  }

  /**
   * 应用奖励骰/惩罚骰 (严格按规则书)
   * @param {number} baseRoll 基础投骰
   * @param {number} bonusDice 奖励骰数量 (正数为奖励，负数为惩罚)
   * @returns {number} 最终投骰结果
   */
  static applyBonusPenaltyDice(baseRoll, bonusDice) {
    if (bonusDice === 0) return baseRoll
    
    const tensDigit = Math.floor(baseRoll / 10) * 10
    const onesDigit = baseRoll % 10
    
    // 生成额外的十位数骰子
    const extraTensRolls = []
    for (let i = 0; i < Math.abs(bonusDice); i++) {
      extraTensRolls.push(diceRoller.roll('1d10').total * 10)
    }
    
    let finalTensDigit
    if (bonusDice > 0) {
      // 奖励骰：选择最小的十位数
      finalTensDigit = Math.min(tensDigit, ...extraTensRolls)
    } else {
      // 惩罚骰：选择最大的十位数
      finalTensDigit = Math.max(tensDigit, ...extraTensRolls)
    }
    
    // 处理特殊情况：00 + 0 = 100
    const finalRoll = finalTensDigit + onesDigit
    return finalRoll === 0 ? 100 : finalRoll
  }

  /**
   * 计算武器伤害 (严格按规则书)
   * @param {Object} weapon 武器数据
   * @param {Object} attacker 攻击者数据
   * @param {string} successLevel 成功等级
   * @returns {Object} 伤害结果
   */
  static calculateDamage(weapon, attacker, successLevel) {
    let baseDamage = 0
    let damageBonus = 0
    let totalDamage = 0
    let isMaxDamage = false
    let isPenetrating = false
    
    // 计算基础武器伤害
    if (weapon.damage) {
      const damageRoll = diceRoller.roll(weapon.damage)
      baseDamage = damageRoll.total
    }
    
    // 计算伤害加值
    if (attacker.damageBonus) {
      if (attacker.damageBonus.rollFormula) {
        const bonusRoll = diceRoller.roll(attacker.damageBonus.rollFormula)
        damageBonus = bonusRoll.total
      } else if (typeof attacker.damageBonus.bonus === 'number') {
        damageBonus = attacker.damageBonus.bonus
      }
    }
    
    // 处理极难成功的特殊伤害
    if (successLevel === 'extreme') {
      isMaxDamage = true
      
      if (weapon.impaling) {
        // 贯穿武器：最大伤害 + 额外一次武器伤害骰
        isPenetrating = true
        baseDamage = weapon.maxDamage || this.getMaxDamage(weapon.damage)
        const extraDamage = diceRoller.roll(weapon.damage)
        baseDamage += extraDamage.total
        damageBonus = attacker.damageBonus.maxBonus || 0
      } else {
        // 非贯穿武器：最大伤害
        baseDamage = weapon.maxDamage || this.getMaxDamage(weapon.damage)
        damageBonus = attacker.damageBonus.maxBonus || 0
      }
    }
    
    totalDamage = Math.max(0, baseDamage + damageBonus)
    
    return {
      baseDamage,
      damageBonus,
      totalDamage,
      isMaxDamage,
      isPenetrating,
      successLevel,
      description: this.getDamageDescription(totalDamage, successLevel, isPenetrating)
    }
  }

  /**
   * 获取骰子公式的最大值
   */
  static getMaxDamage(diceFormula) {
    // 解析骰子公式，如 "1d6+2" -> 最大值 8
    const match = diceFormula.match(/(\d+)d(\d+)([+-]\d+)?/)
    if (!match) return 0
    
    const diceCount = parseInt(match[1])
    const diceSides = parseInt(match[2])
    const modifier = parseInt(match[3]) || 0
    
    return (diceCount * diceSides) + modifier
  }

  /**
   * 获取伤害描述
   */
  static getDamageDescription(damage, successLevel, isPenetrating) {
    let desc = `造成 ${damage} 点伤害`
    
    if (successLevel === 'extreme') {
      if (isPenetrating) {
        desc += ' (极难成功 - 贯穿伤害)'
      } else {
        desc += ' (极难成功 - 最大伤害)'
      }
    }
    
    return desc
  }

  /**
   * 处理大失败后果 (严格按规则书)
   * @param {string} actionType 行动类型 ('combat', 'skill')
   * @param {Object} context 上下文信息
   * @returns {Object} 大失败后果
   */
  static handleFumble(actionType, context = {}) {
    const fumbleConsequences = {
      combat: [
        { 
          type: 'weapon_jam', 
          description: '武器卡壳，需要修理检定',
          effect: 'weapon_malfunction'
        },
        { 
          type: 'friendly_fire', 
          description: '误伤友军，对幸运值最低的盟友造成伤害',
          effect: 'damage_ally'
        },
        { 
          type: 'self_injury', 
          description: '自己受伤，受到1d3点伤害',
          effect: 'self_damage',
          damage: '1d3'
        },
        { 
          type: 'weapon_drop', 
          description: '武器脱手，掉落在附近',
          effect: 'drop_weapon'
        },
        { 
          type: 'ammo_spill', 
          description: '弹药散落，损失1d6发子弹',
          effect: 'lose_ammo',
          ammoLoss: '1d6'
        }
      ],
      skill: [
        { 
          type: 'opposite_effect', 
          description: '产生相反的效果',
          effect: 'reverse_outcome'
        },
        { 
          type: 'attract_attention', 
          description: '引起敌人或危险的注意',
          effect: 'draw_attention'
        },
        { 
          type: 'tool_damage', 
          description: '工具损坏或丢失',
          effect: 'break_tool'
        },
        { 
          type: 'time_waste', 
          description: '浪费大量时间，错过机会',
          effect: 'waste_time'
        },
        { 
          type: 'status_effect', 
          description: '获得负面状态效果',
          effect: 'negative_status'
        }
      ]
    }
    
    const consequences = fumbleConsequences[actionType] || fumbleConsequences.skill
    const randomIndex = Math.floor(Math.random() * consequences.length)
    const consequence = consequences[randomIndex]
    
    return {
      ...consequence,
      context,
      severity: 'major' // 大失败总是严重后果
    }
  }

  /**
   * 检查技能检定是否可以孤注一掷
   * 注意：战斗检定不能孤注一掷
   */
  static canPushRoll(actionType) {
    const nonPushableActions = [
      'combat_attack',
      'combat_defense', 
      'firearms',
      'fighting',
      'dodge'
    ]
    
    return !nonPushableActions.includes(actionType)
  }

  /**
   * 获取技能检定的目标值
   */
  static getSkillTarget(skillValue, difficulty = 'regular') {
    switch (difficulty) {
      case 'hard':
        return Math.floor(skillValue / 2)
      case 'extreme':
        return Math.floor(skillValue / 5)
      case 'regular':
      default:
        return skillValue
    }
  }

  // ===== 扩展的对抗检定系统 =====

  /**
   * 近战对抗检定 (攻击 vs 反击/闪避)
   * @param {Object} attacker 攻击者数据
   * @param {Object} defender 防御者数据
   * @param {Object} options 对抗选项
   * @returns {Object} 近战对抗结果
   */
  static resolveMeleeOpposedRoll(attacker, defender, options = {}) {
    const { defenseType = 'dodge', attackWeapon, defenseWeapon } = options
    
    // 攻击者投骰
    const attackSkill = this.getMeleeSkill(attacker, attackWeapon)
    const attackRoll = diceRoller.rollD100()
    const attackLevel = this.getSuccessLevel(attackRoll, attackSkill)
    
    // 防御者投骰
    let defenseSkill = 0
    let defenseRoll = 0
    let defenseLevel = 'failure'
    
    switch (defenseType) {
      case 'dodge':
        defenseSkill = this.getDodgeSkill(defender)
        defenseRoll = diceRoller.rollD100()
        defenseLevel = this.getSuccessLevel(defenseRoll, defenseSkill)
        break
        
      case 'fight_back':
        defenseSkill = this.getMeleeSkill(defender, defenseWeapon)
        defenseRoll = diceRoller.rollD100()
        defenseLevel = this.getSuccessLevel(defenseRoll, defenseSkill)
        break
        
      case 'block':
        defenseSkill = this.getBlockSkill(defender, defenseWeapon)
        defenseRoll = diceRoller.rollD100()
        defenseLevel = this.getSuccessLevel(defenseRoll, defenseSkill)
        break
    }
    
    // 对抗检定
    const opposedResult = this.resolveOpposedRoll(
      { roll: attackRoll, skill: attackSkill, name: attacker.name },
      { roll: defenseRoll, skill: defenseSkill, name: defender.name }
    )
    
    // 计算伤害结果
    let attackDamage = null
    let counterDamage = null
    
    if (opposedResult.winner === 'attacker') {
      // 攻击者获胜，造成伤害
      attackDamage = this.calculateDamage(attackWeapon, attacker, opposedResult.winnerLevel)
    } else if (opposedResult.winner === 'defender' && defenseType === 'fight_back') {
      // 防御者反击成功，造成反击伤害
      counterDamage = this.calculateDamage(defenseWeapon, defender, opposedResult.winnerLevel)
    }
    
    return {
      type: 'melee_opposed',
      attacker: {
        name: attacker.name,
        roll: attackRoll,
        skill: attackSkill,
        level: attackLevel,
        weapon: attackWeapon?.name || '拳头'
      },
      defender: {
        name: defender.name,
        roll: defenseRoll,
        skill: defenseSkill,
        level: defenseLevel,
        defenseType,
        weapon: defenseWeapon?.name || null
      },
      opposedResult,
      attackDamage,
      counterDamage,
      description: this.getMeleeOpposedDescription(opposedResult, defenseType, attackDamage, counterDamage)
    }
  }

  /**
   * 获取近战技能值
   */
  static getMeleeSkill(character, weapon) {
    if (!weapon) {
      return character.fighting || character['fighting_brawl'] || 25
    }
    
    const skillName = weapon.skill || 'fighting'
    return character[skillName] || character.skills?.[skillName] || 25
  }

  /**
   * 获取闪避技能值
   */
  static getDodgeSkill(character) {
    return character.dodge || Math.floor((character.dexterity || character.dex || 50) / 2)
  }

  /**
   * 获取格挡技能值
   */
  static getBlockSkill(character, weapon) {
    if (!weapon || !weapon.canBlock) {
      return 0 // 无法格挡
    }
    
    // 格挡技能通常等于武器技能
    const skillName = weapon.skill || 'fighting'
    return character[skillName] || character.skills?.[skillName] || 25
  }

  /**
   * 获取近战对抗描述
   */
  static getMeleeOpposedDescription(opposedResult, defenseType, attackDamage, counterDamage) {
    const defenseTypeNames = {
      dodge: '闪避',
      fight_back: '反击',
      block: '格挡'
    }
    
    let description = `${opposedResult.description} (${defenseTypeNames[defenseType]})`
    
    if (attackDamage) {
      description += `，造成${attackDamage.totalDamage}点伤害`
    }
    
    if (counterDamage) {
      description += `，反击造成${counterDamage.totalDamage}点伤害`
    }
    
    return description
  }

  /**
   * 多人近战混战对抗
   * @param {Array} participants 参与者列表
   * @param {Object} options 混战选项
   * @returns {Object} 混战结果
   */
  static resolveMeleeBrawl(participants, options = {}) {
    const results = []
    const { allowMultipleTargets = true } = options
    
    // 为每个参与者分配目标
    const assignments = this.assignMeleeTargets(participants, allowMultipleTargets)
    
    // 执行所有对抗检定
    assignments.forEach(assignment => {
      const { attacker, defender, attackWeapon, defenseType, defenseWeapon } = assignment
      
      const result = this.resolveMeleeOpposedRoll(attacker, defender, {
        defenseType,
        attackWeapon,
        defenseWeapon
      })
      
      results.push(result)
    })
    
    return {
      type: 'melee_brawl',
      assignments,
      results,
      summary: this.summarizeBrawlResults(results)
    }
  }

  /**
   * 分配近战目标
   */
  static assignMeleeTargets(participants, allowMultipleTargets) {
    const assignments = []
    const activeParticipants = participants.filter(p => p.status === 'active')
    
    // 简单的目标分配算法
    activeParticipants.forEach(attacker => {
      const enemies = activeParticipants.filter(p => 
        p.faction !== attacker.faction && p.id !== attacker.id
      )
      
      if (enemies.length > 0) {
        // 选择最近的敌人作为目标
        const target = enemies[0] // 简化处理，实际应该考虑距离
        
        assignments.push({
          attacker,
          defender: target,
          attackWeapon: attacker.currentWeapon,
          defenseType: this.chooseBestDefense(target, attacker),
          defenseWeapon: target.currentWeapon
        })
      }
    })
    
    return assignments
  }

  /**
   * 选择最佳防御方式
   */
  static chooseBestDefense(defender, attacker) {
    const dodgeSkill = this.getDodgeSkill(defender)
    const fightBackSkill = this.getMeleeSkill(defender, defender.currentWeapon)
    const blockSkill = this.getBlockSkill(defender, defender.currentWeapon)
    
    // 选择技能值最高的防御方式
    const defenseOptions = [
      { type: 'dodge', skill: dodgeSkill },
      { type: 'fight_back', skill: fightBackSkill },
      { type: 'block', skill: blockSkill }
    ]
    
    const bestDefense = defenseOptions.reduce((best, current) => 
      current.skill > best.skill ? current : best
    )
    
    return bestDefense.type
  }

  /**
   * 总结混战结果
   */
  static summarizeBrawlResults(results) {
    const summary = {
      totalAttacks: results.length,
      successfulAttacks: 0,
      counterAttacks: 0,
      totalDamage: 0,
      casualties: []
    }
    
    results.forEach(result => {
      if (result.attackDamage) {
        summary.successfulAttacks++
        summary.totalDamage += result.attackDamage.totalDamage
      }
      
      if (result.counterDamage) {
        summary.counterAttacks++
        summary.totalDamage += result.counterDamage.totalDamage
      }
    })
    
    return summary
  }

  /**
   * 处理平手情况的特殊规则
   * @param {Object} opposedResult 对抗结果
   * @param {string} combatType 战斗类型
   * @returns {Object} 处理后的结果
   */
  static handleTieBreaker(opposedResult, combatType = 'melee') {
    if (opposedResult.attackerLevel !== opposedResult.defenderLevel) {
      return opposedResult // 不是平手，直接返回
    }
    
    // 平手处理规则
    const tieRules = {
      melee: {
        // 近战中，攻击者获胜 (规则书规定)
        winner: 'attacker',
        reason: '近战平手时攻击者获胜'
      },
      ranged: {
        // 远程攻击中，防御者获胜
        winner: 'defender', 
        reason: '远程攻击平手时防御者获胜'
      },
      grapple: {
        // 擒抱中，力量更高者获胜
        winner: 'strength_comparison',
        reason: '擒抱平手时比较力量值'
      }
    }
    
    const rule = tieRules[combatType] || tieRules.melee
    
    return {
      ...opposedResult,
      winner: rule.winner === 'strength_comparison' ? opposedResult.winner : rule.winner,
      tieBreaker: true,
      tieReason: rule.reason
    }
  }

  /**
   * 计算寡不敌众的奖励骰
   * @param {Object} character 角色
   * @param {Array} enemies 敌人列表
   * @returns {number} 奖励骰数量
   */
  static calculateOutnumberedBonus(character, enemies) {
    const enemyCount = enemies.filter(e => 
      e.status === 'active' && e.faction !== character.faction
    ).length
    
    if (enemyCount <= 1) return 0
    
    // 每多一个敌人，获得1个奖励骰，最多2个
    return Math.min(enemyCount - 1, 2)
  }

  /**
   * 检查是否可以进行反击
   * @param {Object} defender 防御者
   * @param {Object} attacker 攻击者
   * @returns {boolean} 是否可以反击
   */
  static canFightBack(defender, attacker) {
    // 必须有武器或徒手格斗能力
    if (!defender.currentWeapon && !defender.fighting) {
      return false
    }
    
    // 必须在近战范围内
    const distance = this.calculateDistance(defender.position, attacker.position)
    if (distance > 1) {
      return false
    }
    
    // 不能在某些状态下反击
    const disablingConditions = ['stunned', 'unconscious', 'grappled', 'prone']
    if (defender.conditions?.some(c => disablingConditions.includes(c))) {
      return false
    }
    
    return true
  }

  /**
   * 计算两点间距离
   */
  static calculateDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 0
    
    const dx = pos1.x - pos2.x
    const dy = pos1.y - pos2.y
    return Math.sqrt(dx * dx + dy * dy)
  }

  // ===== 射击系统 =====

  /**
   * 执行射击攻击
   * @param {Object} shooter 射击者
   * @param {Object} target 目标
   * @param {Object} weapon 武器
   * @param {Object} options 射击选项
   * @returns {Object} 射击结果
   */
  static performShootingAttack(shooter, target, weapon, options = {}) {
    const {
      distance = 10,
      aiming = false,
      aimingRounds = 0,
      pointBlank = false,
      cover = 'none',
      lighting = 'normal',
      weather = 'clear',
      moving = false,
      burstFire = false,
      fullAuto = false,
      targetSize = 'normal'
    } = options

    // 计算基础技能值
    const baseSkill = this.getFirearmSkill(shooter, weapon)
    
    // 计算射击难度
    const difficulty = this.getShootingDifficulty(distance, weapon.range)
    const targetSkill = this.getSkillTarget(baseSkill, difficulty)
    
    // 计算奖励/惩罚骰
    const bonusDice = this.calculateShootingBonusDice(shooter, target, weapon, options)
    
    // 执行射击投掷
    const shootingRoll = diceRoller.rollD100()
    const finalRoll = this.applyBonusPenaltyDice(shootingRoll, bonusDice)
    const successLevel = this.getSuccessLevel(finalRoll, targetSkill)
    
    // 检查武器故障
    const malfunction = this.checkWeaponMalfunction(weapon, finalRoll)
    
    // 计算命中结果
    let hitResult = {
      hit: false,
      damage: null,
      location: null,
      malfunction: malfunction
    }
    
    if (successLevel !== 'failure' && successLevel !== 'fumble' && !malfunction) {
      hitResult.hit = true
      
      // 计算伤害
      hitResult.damage = this.calculateDamage(weapon, shooter, successLevel)
      
      // 确定命中部位 (如果是瞄准射击)
      if (aiming && aimingRounds > 0) {
        hitResult.location = this.determineHitLocation(options.targetLocation)
      }
    }
    
    // 处理连发和全自动
    if (burstFire || fullAuto) {
      return this.handleAutomaticFire(shooter, target, weapon, options, hitResult)
    }
    
    return {
      type: 'shooting_attack',
      shooter: shooter.name,
      target: target.name,
      weapon: weapon.name,
      distance,
      roll: finalRoll,
      targetSkill,
      successLevel,
      bonusDice,
      difficulty,
      hitResult,
      options,
      description: this.getShootingDescription(hitResult, successLevel, weapon.name)
    }
  }

  /**
   * 获取射击技能值
   */
  static getFirearmSkill(character, weapon) {
    const skillName = weapon.skill || 'firearms'
    return character[skillName] || character.skills?.[skillName] || 20
  }

  /**
   * 计算射击奖励/惩罚骰
   */
  static calculateShootingBonusDice(shooter, target, weapon, options) {
    let bonusDice = 0
    
    // 瞄准奖励
    if (options.aiming) {
      bonusDice += Math.min(options.aimingRounds || 1, 2) // 最多2个奖励骰
    }
    
    // 抵近射击 (5码内)
    if (options.distance <= 5) {
      bonusDice += 1
    }
    
    // 大目标
    if (options.targetSize === 'large') {
      bonusDice += 1
    } else if (options.targetSize === 'huge') {
      bonusDice += 2
    }
    
    // 惩罚骰情况
    
    // 小目标
    if (options.targetSize === 'small') {
      bonusDice -= 1
    } else if (options.targetSize === 'tiny') {
      bonusDice -= 2
    }
    
    // 掩体
    switch (options.cover) {
      case 'light':
        bonusDice -= 1
        break
      case 'heavy':
        bonusDice -= 2
        break
      case 'total':
        return { impossible: true } // 无法射击
    }
    
    // 光照条件
    switch (options.lighting) {
      case 'dim':
        bonusDice -= 1
        break
      case 'dark':
        bonusDice -= 2
        break
      case 'pitch_black':
        bonusDice -= 3
        break
    }
    
    // 天气条件
    switch (options.weather) {
      case 'rain':
        bonusDice -= 1
        break
      case 'heavy_rain':
        bonusDice -= 2
        break
      case 'fog':
        bonusDice -= 2
        break
      case 'storm':
        bonusDice -= 3
        break
    }
    
    // 移动射击
    if (options.moving) {
      bonusDice -= 1
    }
    
    // 目标移动
    if (options.targetMoving) {
      bonusDice -= 1
    }
    
    return bonusDice
  }

  /**
   * 检查武器故障
   */
  static checkWeaponMalfunction(weapon, roll) {
    if (!weapon.malfunction) return false
    
    // 如果投掷结果大于等于故障值，武器故障
    return roll >= weapon.malfunction
  }

  /**
   * 确定命中部位
   */
  static determineHitLocation(targetLocation = null) {
    if (targetLocation) {
      return {
        location: targetLocation,
        modifier: this.getLocationModifier(targetLocation)
      }
    }
    
    // 随机命中部位
    const roll = diceRoller.rollD100()
    
    if (roll <= 10) return { location: 'head', modifier: 2 }
    if (roll <= 20) return { location: 'right_arm', modifier: 1 }
    if (roll <= 30) return { location: 'left_arm', modifier: 1 }
    if (roll <= 70) return { location: 'chest', modifier: 1 }
    if (roll <= 80) return { location: 'abdomen', modifier: 1 }
    if (roll <= 90) return { location: 'right_leg', modifier: 1 }
    return { location: 'left_leg', modifier: 1 }
  }

  /**
   * 获取部位伤害修正
   */
  static getLocationModifier(location) {
    const modifiers = {
      head: 2,
      chest: 1,
      abdomen: 1,
      right_arm: 1,
      left_arm: 1,
      right_leg: 1,
      left_leg: 1
    }
    
    return modifiers[location] || 1
  }

  /**
   * 处理自动射击 (连发/全自动)
   */
  static handleAutomaticFire(shooter, target, weapon, options, baseHitResult) {
    const { burstFire, fullAuto, burstSize = 3, fullAutoRounds = 10 } = options
    
    let shots = 1
    if (burstFire) {
      shots = Math.min(burstSize, weapon.currentAmmo || weapon.ammo)
    } else if (fullAuto) {
      shots = Math.min(fullAutoRounds, weapon.currentAmmo || weapon.ammo)
    }
    
    const results = []
    let totalDamage = 0
    
    // 第一发使用基础结果
    if (baseHitResult.hit) {
      results.push(baseHitResult)
      totalDamage += baseHitResult.damage.totalDamage
    }
    
    // 额外的子弹
    for (let i = 1; i < shots; i++) {
      // 每发额外子弹都有独立的命中检定，但难度递增
      const penaltyDice = Math.floor(i / 2) // 每2发增加1个惩罚骰
      
      const roll = diceRoller.rollD100()
      const finalRoll = this.applyBonusPenaltyDice(roll, options.bonusDice - penaltyDice)
      const successLevel = this.getSuccessLevel(finalRoll, options.targetSkill)
      
      if (successLevel !== 'failure' && successLevel !== 'fumble') {
        const damage = this.calculateDamage(weapon, shooter, successLevel)
        results.push({
          hit: true,
          damage,
          shot: i + 1
        })
        totalDamage += damage.totalDamage
      } else {
        results.push({
          hit: false,
          shot: i + 1,
          roll: finalRoll
        })
      }
    }
    
    return {
      type: fullAuto ? 'full_auto_attack' : 'burst_fire_attack',
      shooter: shooter.name,
      target: target.name,
      weapon: weapon.name,
      shots,
      hits: results.filter(r => r.hit).length,
      totalDamage,
      results,
      description: this.getAutomaticFireDescription(shots, results.filter(r => r.hit).length, totalDamage)
    }
  }

  /**
   * 计算弹幕射击 (对区域射击)
   */
  static performBarrageAttack(shooter, targets, weapon, options = {}) {
    const { area, rounds = 10 } = options
    
    // 弹幕射击对区域内所有目标都有命中机会
    const results = []
    
    targets.forEach(target => {
      // 每个目标都有被命中的概率
      const hitChance = this.calculateBarrageHitChance(target, area, rounds)
      const roll = diceRoller.rollD100()
      
      if (roll <= hitChance) {
        // 命中，计算伤害
        const damage = this.calculateDamage(weapon, shooter, 'regular')
        results.push({
          target: target.name,
          hit: true,
          damage,
          roll
        })
      } else {
        results.push({
          target: target.name,
          hit: false,
          roll
        })
      }
    })
    
    return {
      type: 'barrage_attack',
      shooter: shooter.name,
      weapon: weapon.name,
      rounds,
      area,
      targets: targets.length,
      hits: results.filter(r => r.hit).length,
      results,
      description: this.getBarrageDescription(results)
    }
  }

  /**
   * 计算弹幕命中概率
   */
  static calculateBarrageHitChance(target, area, rounds) {
    // 基础命中率基于区域大小和弹药数量
    let baseChance = Math.min(rounds * 2, 50) // 每发子弹2%基础概率，最高50%
    
    // 目标大小影响
    if (target.size === 'large') baseChance *= 1.5
    if (target.size === 'small') baseChance *= 0.5
    
    // 掩体影响
    if (target.cover === 'light') baseChance *= 0.7
    if (target.cover === 'heavy') baseChance *= 0.3
    
    return Math.min(baseChance, 95) // 最高95%
  }

  /**
   * 获取射击描述
   */
  static getShootingDescription(hitResult, successLevel, weaponName) {
    if (hitResult.malfunction) {
      return `${weaponName} 发生故障！`
    }
    
    if (!hitResult.hit) {
      return `${weaponName} 射击未命中`
    }
    
    let desc = `${weaponName} 命中目标`
    
    if (hitResult.location) {
      desc += ` (${hitResult.location.location})`
    }
    
    desc += `，造成 ${hitResult.damage.totalDamage} 点伤害`
    
    if (successLevel === 'extreme') {
      desc += ' (极难成功)'
    } else if (successLevel === 'critical') {
      desc += ' (大成功)'
    }
    
    return desc
  }

  /**
   * 获取自动射击描述
   */
  static getAutomaticFireDescription(shots, hits, totalDamage) {
    return `射击 ${shots} 发子弹，命中 ${hits} 发，总伤害 ${totalDamage} 点`
  }

  /**
   * 获取弹幕射击描述
   */
  static getBarrageDescription(results) {
    const hits = results.filter(r => r.hit).length
    const totalTargets = results.length
    return `弹幕射击覆盖 ${totalTargets} 个目标，命中 ${hits} 个`
  }

  /**
   * 处理装填弹药
   */
  static reloadWeapon(character, weapon) {
    if (!weapon.ammo || !weapon.reloadTime) {
      return { success: false, reason: '武器无法装填' }
    }
    
    // 检查是否有弹药
    const ammoType = weapon.ammoType || 'generic'
    const availableAmmo = character.inventory?.[ammoType] || 0
    
    if (availableAmmo <= 0) {
      return { success: false, reason: '没有弹药' }
    }
    
    // 计算装填数量
    const currentAmmo = weapon.currentAmmo || 0
    const maxAmmo = weapon.ammo
    const neededAmmo = maxAmmo - currentAmmo
    const reloadAmount = Math.min(neededAmmo, availableAmmo)
    
    // 执行装填
    weapon.currentAmmo = currentAmmo + reloadAmount
    if (character.inventory) {
      character.inventory[ammoType] -= reloadAmount
    }
    
    return {
      success: true,
      reloadAmount,
      currentAmmo: weapon.currentAmmo,
      maxAmmo,
      timeRequired: weapon.reloadTime,
      description: `装填 ${reloadAmount} 发子弹，当前弹药 ${weapon.currentAmmo}/${maxAmmo}`
    }
  }

  /**
   * 修理武器故障
   */
  static repairWeapon(character, weapon) {
    if (!weapon.malfunction) {
      return { success: false, reason: '武器没有故障' }
    }
    
    // 使用机械修理技能
    const repairSkill = character.mechanical_repair || character.skills?.mechanical_repair || 20
    const roll = diceRoller.rollD100()
    const success = roll <= repairSkill
    
    if (success) {
      weapon.malfunctioned = false
      return {
        success: true,
        roll,
        skill: repairSkill,
        description: '成功修理武器故障'
      }
    } else {
      return {
        success: false,
        roll,
        skill: repairSkill,
        description: '修理失败，武器仍然故障'
      }
    }
  }

  // ===== 战技系统 =====

  /**
   * 执行擒抱战技
   * @param {Object} attacker 攻击者
   * @param {Object} defender 防御者
   * @returns {Object} 擒抱结果
   */
  static performGrapple(attacker, defender) {
    // 擒抱使用格斗(擒抱)技能
    const attackerSkill = attacker.fighting_grapple || attacker.fighting || 25
    const defenderSkill = defender.fighting_grapple || defender.fighting || 25
    
    // 体格差异影响
    const attackerBuild = this.calculateBuild(attacker.strength, attacker.size)
    const defenderBuild = this.calculateBuild(defender.strength, defender.size)
    const buildDifference = attackerBuild - defenderBuild
    
    // 计算惩罚骰
    let attackerPenalty = 0
    let defenderPenalty = 0
    
    if (buildDifference > 0) {
      // 攻击者体格更大，防御者受惩罚
      defenderPenalty = Math.min(buildDifference, 2)
    } else if (buildDifference < 0) {
      // 防御者体格更大，攻击者受惩罚
      attackerPenalty = Math.min(Math.abs(buildDifference), 2)
    }
    
    // 投掷检定
    const attackerRoll = diceRoller.rollD100()
    const defenderRoll = diceRoller.rollD100()
    
    const finalAttackerRoll = this.applyBonusPenaltyDice(attackerRoll, -attackerPenalty)
    const finalDefenderRoll = this.applyBonusPenaltyDice(defenderRoll, -defenderPenalty)
    
    const attackerLevel = this.getSuccessLevel(finalAttackerRoll, attackerSkill)
    const defenderLevel = this.getSuccessLevel(finalDefenderRoll, defenderSkill)
    
    // 对抗检定
    const opposedResult = this.resolveOpposedRoll(
      { roll: finalAttackerRoll, skill: attackerSkill, name: attacker.name },
      { roll: finalDefenderRoll, skill: defenderSkill, name: defender.name }
    )
    
    let grappleResult = {
      type: 'grapple',
      success: false,
      effect: null,
      damage: 0
    }
    
    if (opposedResult.winner === 'attacker') {
      grappleResult.success = true
      
      // 根据成功等级决定效果
      switch (opposedResult.winnerLevel) {
        case 'critical':
          grappleResult.effect = 'pin_and_damage'
          grappleResult.damage = diceRoller.roll('1d6').total + attackerBuild
          break
        case 'extreme':
          grappleResult.effect = 'pin'
          break
        case 'hard':
        case 'regular':
          grappleResult.effect = 'grappled'
          break
      }
      
      // 应用状态效果
      if (!defender.conditions) defender.conditions = []
      if (!defender.conditions.includes('grappled')) {
        defender.conditions.push('grappled')
      }
    }
    
    return {
      type: 'grapple_maneuver',
      attacker: {
        name: attacker.name,
        roll: finalAttackerRoll,
        skill: attackerSkill,
        level: attackerLevel,
        build: attackerBuild,
        penalty: attackerPenalty
      },
      defender: {
        name: defender.name,
        roll: finalDefenderRoll,
        skill: defenderSkill,
        level: defenderLevel,
        build: defenderBuild,
        penalty: defenderPenalty
      },
      opposedResult,
      grappleResult,
      description: this.getGrappleDescription(opposedResult, grappleResult)
    }
  }

  /**
   * 执行缴械战技
   * @param {Object} attacker 攻击者
   * @param {Object} defender 防御者
   * @returns {Object} 缴械结果
   */
  static performDisarm(attacker, defender) {
    if (!defender.currentWeapon) {
      return { success: false, reason: '目标没有武器可以缴械' }
    }
    
    // 缴械使用格斗技能，困难难度
    const attackerSkill = this.getSkillTarget(attacker.fighting || 25, 'hard')
    const defenderSkill = defender.fighting || 25
    
    // 体格差异影响
    const buildDifference = this.calculateBuild(attacker.strength, attacker.size) - 
                           this.calculateBuild(defender.strength, defender.size)
    
    let bonusDice = 0
    if (buildDifference > 0) {
      bonusDice = Math.min(buildDifference, 2)
    } else if (buildDifference < 0) {
      bonusDice = Math.max(buildDifference, -2)
    }
    
    // 投掷检定
    const attackerRoll = diceRoller.rollD100()
    const defenderRoll = diceRoller.rollD100()
    
    const finalAttackerRoll = this.applyBonusPenaltyDice(attackerRoll, bonusDice)
    
    const attackerLevel = this.getSuccessLevel(finalAttackerRoll, attackerSkill)
    const defenderLevel = this.getSuccessLevel(defenderRoll, defenderSkill)
    
    // 对抗检定
    const opposedResult = this.resolveOpposedRoll(
      { roll: finalAttackerRoll, skill: attackerSkill, name: attacker.name },
      { roll: defenderRoll, skill: defenderSkill, name: defender.name }
    )
    
    let disarmResult = {
      success: false,
      weaponDropped: null,
      distance: 0
    }
    
    if (opposedResult.winner === 'attacker') {
      disarmResult.success = true
      disarmResult.weaponDropped = defender.currentWeapon
      
      // 武器掉落距离基于成功等级
      switch (opposedResult.winnerLevel) {
        case 'critical':
          disarmResult.distance = diceRoller.roll('2d6').total
          break
        case 'extreme':
          disarmResult.distance = diceRoller.roll('1d6+3').total
          break
        default:
          disarmResult.distance = diceRoller.roll('1d6').total
          break
      }
      
      // 移除武器
      defender.currentWeapon = null
    }
    
    return {
      type: 'disarm_maneuver',
      attacker: {
        name: attacker.name,
        roll: finalAttackerRoll,
        skill: attackerSkill,
        level: attackerLevel
      },
      defender: {
        name: defender.name,
        roll: defenderRoll,
        skill: defenderSkill,
        level: defenderLevel
      },
      opposedResult,
      disarmResult,
      description: this.getDisarmDescription(opposedResult, disarmResult)
    }
  }

  /**
   * 执行撞倒战技
   * @param {Object} attacker 攻击者
   * @param {Object} defender 防御者
   * @returns {Object} 撞倒结果
   */
  static performKnockdown(attacker, defender) {
    // 撞倒使用格斗技能
    const attackerSkill = attacker.fighting || 25
    const defenderSkill = defender.fighting || 25
    
    // 体格和敏捷影响
    const attackerBuild = this.calculateBuild(attacker.strength, attacker.size)
    const defenderBuild = this.calculateBuild(defender.strength, defender.size)
    const buildDifference = attackerBuild - defenderBuild
    
    // 敏捷也影响平衡
    const dexDifference = (attacker.dexterity || 50) - (defender.dexterity || 50)
    
    // 计算修正
    let attackerBonus = 0
    let defenderBonus = 0
    
    if (buildDifference > 0) {
      attackerBonus += Math.min(buildDifference, 2)
    } else if (buildDifference < 0) {
      defenderBonus += Math.min(Math.abs(buildDifference), 2)
    }
    
    if (dexDifference > 20) {
      attackerBonus += 1
    } else if (dexDifference < -20) {
      defenderBonus += 1
    }
    
    // 投掷检定
    const attackerRoll = diceRoller.rollD100()
    const defenderRoll = diceRoller.rollD100()
    
    const finalAttackerRoll = this.applyBonusPenaltyDice(attackerRoll, attackerBonus)
    const finalDefenderRoll = this.applyBonusPenaltyDice(defenderRoll, defenderBonus)
    
    const attackerLevel = this.getSuccessLevel(finalAttackerRoll, attackerSkill)
    const defenderLevel = this.getSuccessLevel(finalDefenderRoll, defenderSkill)
    
    // 对抗检定
    const opposedResult = this.resolveOpposedRoll(
      { roll: finalAttackerRoll, skill: attackerSkill, name: attacker.name },
      { roll: finalDefenderRoll, skill: defenderSkill, name: defender.name }
    )
    
    let knockdownResult = {
      success: false,
      prone: false,
      damage: 0
    }
    
    if (opposedResult.winner === 'attacker') {
      knockdownResult.success = true
      knockdownResult.prone = true
      
      // 根据成功等级决定额外效果
      if (opposedResult.winnerLevel === 'critical' || opposedResult.winnerLevel === 'extreme') {
        // 极难成功或大成功造成额外伤害
        knockdownResult.damage = diceRoller.roll('1d3').total
      }
      
      // 应用倒地状态
      if (!defender.conditions) defender.conditions = []
      if (!defender.conditions.includes('prone')) {
        defender.conditions.push('prone')
      }
    }
    
    return {
      type: 'knockdown_maneuver',
      attacker: {
        name: attacker.name,
        roll: finalAttackerRoll,
        skill: attackerSkill,
        level: attackerLevel,
        build: attackerBuild,
        bonus: attackerBonus
      },
      defender: {
        name: defender.name,
        roll: finalDefenderRoll,
        skill: defenderSkill,
        level: defenderLevel,
        build: defenderBuild,
        bonus: defenderBonus
      },
      opposedResult,
      knockdownResult,
      description: this.getKnockdownDescription(opposedResult, knockdownResult)
    }
  }

  /**
   * 执行推挤战技
   * @param {Object} attacker 攻击者
   * @param {Object} defender 防御者
   * @param {number} distance 推挤距离
   * @returns {Object} 推挤结果
   */
  static performPush(attacker, defender, distance = 1) {
    // 推挤主要依赖力量和体格
    const attackerStr = attacker.strength || 50
    const defenderStr = defender.strength || 50
    
    const attackerBuild = this.calculateBuild(attacker.strength, attacker.size)
    const defenderBuild = this.calculateBuild(defender.strength, defender.size)
    
    // 力量对抗
    const attackerRoll = diceRoller.rollD100()
    const defenderRoll = diceRoller.rollD100()
    
    const attackerLevel = this.getSuccessLevel(attackerRoll, attackerStr)
    const defenderLevel = this.getSuccessLevel(defenderRoll, defenderStr)
    
    // 对抗检定
    const opposedResult = this.resolveOpposedRoll(
      { roll: attackerRoll, skill: attackerStr, name: attacker.name },
      { roll: defenderRoll, skill: defenderStr, name: defender.name }
    )
    
    let pushResult = {
      success: false,
      distance: 0,
      knockdown: false
    }
    
    if (opposedResult.winner === 'attacker') {
      pushResult.success = true
      
      // 推挤距离基于体格差异和成功等级
      let baseDistance = Math.max(1, attackerBuild - defenderBuild + 1)
      
      switch (opposedResult.winnerLevel) {
        case 'critical':
          pushResult.distance = baseDistance * 3
          pushResult.knockdown = true
          break
        case 'extreme':
          pushResult.distance = baseDistance * 2
          break
        default:
          pushResult.distance = baseDistance
          break
      }
      
      // 应用击倒效果
      if (pushResult.knockdown) {
        if (!defender.conditions) defender.conditions = []
        if (!defender.conditions.includes('prone')) {
          defender.conditions.push('prone')
        }
      }
    }
    
    return {
      type: 'push_maneuver',
      attacker: {
        name: attacker.name,
        roll: attackerRoll,
        skill: attackerStr,
        level: attackerLevel,
        build: attackerBuild
      },
      defender: {
        name: defender.name,
        roll: defenderRoll,
        skill: defenderStr,
        level: defenderLevel,
        build: defenderBuild
      },
      opposedResult,
      pushResult,
      description: this.getPushDescription(opposedResult, pushResult)
    }
  }

  /**
   * 挣脱擒抱
   * @param {Object} grappled 被擒抱者
   * @param {Object} grappler 擒抱者
   * @returns {Object} 挣脱结果
   */
  static breakFree(grappled, grappler) {
    // 可以使用力量或敏捷挣脱
    const grappledStr = grappled.strength || 50
    const grappledDex = grappled.dexterity || 50
    const grapplerStr = grappler.strength || 50
    
    // 选择更高的属性
    const grappledSkill = Math.max(grappledStr, grappledDex)
    const grapplerSkill = grapplerStr
    
    // 投掷检定
    const grappledRoll = diceRoller.rollD100()
    const grapplerRoll = diceRoller.rollD100()
    
    const grappledLevel = this.getSuccessLevel(grappledRoll, grappledSkill)
    const grapplerLevel = this.getSuccessLevel(grapplerRoll, grapplerSkill)
    
    // 对抗检定
    const opposedResult = this.resolveOpposedRoll(
      { roll: grappledRoll, skill: grappledSkill, name: grappled.name },
      { roll: grapplerRoll, skill: grapplerSkill, name: grappler.name }
    )
    
    let breakResult = {
      success: false,
      method: grappledStr > grappledDex ? 'strength' : 'dexterity'
    }
    
    if (opposedResult.winner === 'attacker') { // 被擒抱者是"攻击者"
      breakResult.success = true
      
      // 移除擒抱状态
      if (grappled.conditions) {
        grappled.conditions = grappled.conditions.filter(c => c !== 'grappled')
      }
    }
    
    return {
      type: 'break_free',
      grappled: {
        name: grappled.name,
        roll: grappledRoll,
        skill: grappledSkill,
        level: grappledLevel,
        method: breakResult.method
      },
      grappler: {
        name: grappler.name,
        roll: grapplerRoll,
        skill: grapplerSkill,
        level: grapplerLevel
      },
      opposedResult,
      breakResult,
      description: this.getBreakFreeDescription(opposedResult, breakResult)
    }
  }

  /**
   * 获取擒抱描述
   */
  static getGrappleDescription(opposedResult, grappleResult) {
    let desc = opposedResult.description
    
    if (grappleResult.success) {
      switch (grappleResult.effect) {
        case 'pin_and_damage':
          desc += `，成功压制并造成${grappleResult.damage}点伤害`
          break
        case 'pin':
          desc += `，成功压制目标`
          break
        case 'grappled':
          desc += `，成功擒抱目标`
          break
      }
    } else {
      desc += `，擒抱失败`
    }
    
    return desc
  }

  /**
   * 获取缴械描述
   */
  static getDisarmDescription(opposedResult, disarmResult) {
    let desc = opposedResult.description
    
    if (disarmResult.success) {
      desc += `，成功缴械${disarmResult.weaponDropped.name}，武器掉落${disarmResult.distance}米外`
    } else {
      desc += `，缴械失败`
    }
    
    return desc
  }

  /**
   * 获取撞倒描述
   */
  static getKnockdownDescription(opposedResult, knockdownResult) {
    let desc = opposedResult.description
    
    if (knockdownResult.success) {
      desc += `，成功撞倒目标`
      if (knockdownResult.damage > 0) {
        desc += `，造成${knockdownResult.damage}点伤害`
      }
    } else {
      desc += `，撞倒失败`
    }
    
    return desc
  }

  /**
   * 获取推挤描述
   */
  static getPushDescription(opposedResult, pushResult) {
    let desc = opposedResult.description
    
    if (pushResult.success) {
      desc += `，成功推挤目标${pushResult.distance}米`
      if (pushResult.knockdown) {
        desc += `，并将其撞倒`
      }
    } else {
      desc += `，推挤失败`
    }
    
    return desc
  }

  /**
   * 获取挣脱描述
   */
  static getBreakFreeDescription(opposedResult, breakResult) {
    let desc = opposedResult.description
    
    if (breakResult.success) {
      desc += `，成功挣脱擒抱 (使用${breakResult.method === 'strength' ? '力量' : '敏捷'})`
    } else {
      desc += `，挣脱失败`
    }
    
    return desc
  }
}

export default CombatRules