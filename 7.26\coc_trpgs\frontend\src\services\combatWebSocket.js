/**
 * COC 7版战斗系统WebSocket通信协议
 * 处理实时战斗数据同步和消息传递
 */

import { EventEmitter } from 'events'

// 消息类型定义
export const MessageTypes = {
  // 连接管理
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  HEARTBEAT: 'heartbeat',
  
  // 战斗会话管理
  JOIN_COMBAT: 'join_combat',
  LEAVE_COMBAT: 'leave_combat',
  COMBAT_STATE_SYNC: 'combat_state_sync',
  
  // 角色数据同步
  CHARACTER_UPDATE: 'character_update',
  POSITION_UPDATE: 'position_update',
  STATUS_EFFECT_CHANGE: 'status_effect_change',
  INVENTORY_UPDATE: 'inventory_update',
  ATTRIBUTE_CHANGE: 'attribute_change',
  
  // 战斗行动
  ACTION_REQUEST: 'action_request',
  ACTION_RESPONSE: 'action_response',
  ACTION_BROADCAST: 'action_broadcast',
  
  // 回合管理
  ROUND_START: 'round_start',
  ROUND_END: 'round_end',
  TURN_START: 'turn_start',
  TURN_END: 'turn_end',
  INITIATIVE_UPDATE: 'initiative_update',
  
  // KP控制
  KP_COMMAND: 'kp_command',
  KP_RESPONSE: 'kp_response',
  FORCE_COMBAT_MODE: 'force_combat_mode',
  
  // 错误处理
  ERROR: 'error',
  WARNING: 'warning',
  
  // 系统消息
  SYSTEM_MESSAGE: 'system_message',
  CHAT_MESSAGE: 'chat_message'
}

// 连接状态
export const ConnectionStates = {
  DISCONNECTED: 'disconnected',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  RECONNECTING: 'reconnecting',
  ERROR: 'error'
}

export class CombatWebSocket extends EventEmitter {
  constructor(options = {}) {
    super()
    
    this.url = options.url || 'ws://localhost:8765'
    this.roomId = options.roomId
    this.userId = options.userId
    this.token = options.token
    
    this.socket = null
    this.connectionState = ConnectionStates.DISCONNECTED
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.heartbeatInterval = null
    this.heartbeatTimeout = null
    
    // 消息队列
    this.messageQueue = []
    this.pendingRequests = new Map()
    this.requestId = 0
    
    // 配置选项
    this.options = {
      heartbeatInterval: 30000, // 30秒心跳
      heartbeatTimeout: 10000,  // 10秒心跳超时
      messageTimeout: 30000,    // 30秒消息超时
      ...options
    }
  }
  
  /**
   * 连接到WebSocket服务器
   */
  async connect() {
    if (this.connectionState === ConnectionStates.CONNECTED || 
        this.connectionState === ConnectionStates.CONNECTING) {
      return
    }
    
    this.connectionState = ConnectionStates.CONNECTING
    this.emit('stateChange', this.connectionState)
    
    try {
      const wsUrl = `${this.url}/combat/room/${this.roomId}/user/${this.userId}`
      this.socket = new WebSocket(wsUrl)
      
      this.socket.onopen = this.handleOpen.bind(this)
      this.socket.onmessage = this.handleMessage.bind(this)
      this.socket.onclose = this.handleClose.bind(this)
      this.socket.onerror = this.handleError.bind(this)
      
    } catch (error) {
      this.connectionState = ConnectionStates.ERROR
      this.emit('stateChange', this.connectionState)
      this.emit('error', error)
    }
  }
  
  /**
   * 断开连接
   */
  disconnect() {
    this.stopHeartbeat()
    this.clearPendingRequests()
    
    if (this.socket) {
      this.socket.close(1000, 'Client disconnect')
      this.socket = null
    }
    
    this.connectionState = ConnectionStates.DISCONNECTED
    this.emit('stateChange', this.connectionState)
  }
  
  /**
   * 处理连接打开
   */
  handleOpen(event) {
    console.log('WebSocket连接已建立')
    this.connectionState = ConnectionStates.CONNECTED
    this.reconnectAttempts = 0
    this.emit('stateChange', this.connectionState)
    this.emit('connected')
    
    // 发送认证消息
    this.send({
      type: MessageTypes.CONNECT,
      data: {
        roomId: this.roomId,
        userId: this.userId,
        token: this.token,
        timestamp: Date.now()
      }
    })
    
    // 开始心跳
    this.startHeartbeat()
    
    // 发送队列中的消息
    this.flushMessageQueue()
  }
  
  /**
   * 处理接收到的消息
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data)
      this.processMessage(message)
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
      this.emit('error', new Error('Invalid message format'))
    }
  }
  
  /**
   * 处理连接关闭
   */
  handleClose(event) {
    console.log('WebSocket连接已关闭:', event.code, event.reason)
    this.stopHeartbeat()
    this.socket = null
    
    if (event.code !== 1000) { // 非正常关闭
      this.attemptReconnect()
    } else {
      this.connectionState = ConnectionStates.DISCONNECTED
      this.emit('stateChange', this.connectionState)
      this.emit('disconnected', event)
    }
  }
  
  /**
   * 处理连接错误
   */
  handleError(event) {
    console.error('WebSocket错误:', event)
    this.connectionState = ConnectionStates.ERROR
    this.emit('stateChange', this.connectionState)
    this.emit('error', event)
  }
  
  /**
   * 处理消息
   */
  processMessage(message) {
    const { type, requestId, data, error } = message
    
    // 处理响应消息
    if (requestId && this.pendingRequests.has(requestId)) {
      const { resolve, reject, timeout } = this.pendingRequests.get(requestId)
      clearTimeout(timeout)
      this.pendingRequests.delete(requestId)
      
      if (error) {
        reject(new Error(error))
      } else {
        resolve(data)
      }
      return
    }
    
    // 处理不同类型的消息
    switch (type) {
      case MessageTypes.HEARTBEAT:
        this.handleHeartbeat(data)
        break
        
      case MessageTypes.COMBAT_STATE_SYNC:
        this.emit('combatStateSync', data)
        break
        
      case MessageTypes.CHARACTER_UPDATE:
        this.emit('characterUpdate', data)
        break
        
      case MessageTypes.POSITION_UPDATE:
        this.emit('positionUpdate', data)
        break
        
      case MessageTypes.STATUS_EFFECT_CHANGE:
        this.emit('statusEffectChange', data)
        break
        
      case MessageTypes.ACTION_BROADCAST:
        this.emit('actionBroadcast', data)
        break
        
      case MessageTypes.ROUND_START:
        this.emit('roundStart', data)
        break
        
      case MessageTypes.ROUND_END:
        this.emit('roundEnd', data)
        break
        
      case MessageTypes.TURN_START:
        this.emit('turnStart', data)
        break
        
      case MessageTypes.TURN_END:
        this.emit('turnEnd', data)
        break
        
      case MessageTypes.INITIATIVE_UPDATE:
        this.emit('initiativeUpdate', data)
        break
        
      case MessageTypes.FORCE_COMBAT_MODE:
        this.emit('forceCombatMode', data)
        break
        
      case MessageTypes.KP_COMMAND:
        this.emit('kpCommand', data)
        break
        
      case MessageTypes.SYSTEM_MESSAGE:
        this.emit('systemMessage', data)
        break
        
      case MessageTypes.CHAT_MESSAGE:
        this.emit('chatMessage', data)
        break
        
      case MessageTypes.ERROR:
        this.emit('serverError', data)
        break
        
      case MessageTypes.WARNING:
        this.emit('serverWarning', data)
        break
        
      default:
        console.warn('未知消息类型:', type)
        this.emit('unknownMessage', message)
    }
  }
  
  /**
   * 发送消息
   */
  send(message) {
    if (this.connectionState !== ConnectionStates.CONNECTED) {
      // 连接未建立时，将消息加入队列
      this.messageQueue.push(message)
      return Promise.reject(new Error('WebSocket not connected'))
    }
    
    try {
      const messageStr = JSON.stringify(message)
      this.socket.send(messageStr)
      return Promise.resolve()
    } catch (error) {
      return Promise.reject(error)
    }
  }
  
  /**
   * 发送请求并等待响应
   */
  sendRequest(type, data = {}) {
    return new Promise((resolve, reject) => {
      const requestId = ++this.requestId
      
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId)
        reject(new Error('Request timeout'))
      }, this.options.messageTimeout)
      
      // 保存请求信息
      this.pendingRequests.set(requestId, { resolve, reject, timeout })
      
      // 发送请求
      this.send({
        type,
        requestId,
        data,
        timestamp: Date.now()
      }).catch(error => {
        clearTimeout(timeout)
        this.pendingRequests.delete(requestId)
        reject(error)
      })
    })
  }
  
  /**
   * 加入战斗会话
   */
  async joinCombat(sessionId, characterId) {
    return this.sendRequest(MessageTypes.JOIN_COMBAT, {
      sessionId,
      characterId
    })
  }
  
  /**
   * 离开战斗会话
   */
  async leaveCombat(sessionId) {
    return this.sendRequest(MessageTypes.LEAVE_COMBAT, {
      sessionId
    })
  }
  
  /**
   * 同步角色数据
   */
  async syncCharacterData(sessionId, characterId, updateData) {
    return this.sendRequest(MessageTypes.CHARACTER_UPDATE, {
      sessionId,
      characterId,
      data: updateData
    })
  }
  
  /**
   * 更新角色位置
   */
  async updatePosition(sessionId, characterId, position, facing = 0) {
    return this.sendRequest(MessageTypes.POSITION_UPDATE, {
      sessionId,
      characterId,
      position,
      facing
    })
  }
  
  /**
   * 更新状态效果
   */
  async updateStatusEffects(sessionId, characterId, conditions) {
    return this.sendRequest(MessageTypes.STATUS_EFFECT_CHANGE, {
      sessionId,
      characterId,
      conditions
    })
  }
  
  /**
   * 更新背包物品
   */
  async updateInventory(sessionId, characterId, inventory, equippedWeapons = [], equippedArmor = []) {
    return this.sendRequest(MessageTypes.INVENTORY_UPDATE, {
      sessionId,
      characterId,
      inventory,
      equippedWeapons,
      equippedArmor
    })
  }
  
  /**
   * 更新角色属性
   */
  async updateAttributes(sessionId, characterId, attributes) {
    return this.sendRequest(MessageTypes.ATTRIBUTE_CHANGE, {
      sessionId,
      characterId,
      attributes
    })
  }
  
  /**
   * 提交战斗行动
   */
  async submitAction(sessionId, actionData) {
    return this.sendRequest(MessageTypes.ACTION_REQUEST, {
      sessionId,
      ...actionData
    })
  }
  
  /**
   * 发送KP命令
   */
  async sendKPCommand(sessionId, command, data = {}) {
    return this.sendRequest(MessageTypes.KP_COMMAND, {
      sessionId,
      command,
      data
    })
  }
  
  /**
   * 发送聊天消息
   */
  async sendChatMessage(message, type = 'normal') {
    return this.send({
      type: MessageTypes.CHAT_MESSAGE,
      data: {
        message,
        messageType: type,
        userId: this.userId,
        timestamp: Date.now()
      }
    })
  }
  
  /**
   * 开始心跳
   */
  startHeartbeat() {
    this.stopHeartbeat()
    
    this.heartbeatInterval = setInterval(() => {
      if (this.connectionState === ConnectionStates.CONNECTED) {
        this.sendHeartbeat()
      }
    }, this.options.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }
  
  /**
   * 发送心跳
   */
  sendHeartbeat() {
    this.send({
      type: MessageTypes.HEARTBEAT,
      data: { timestamp: Date.now() }
    })
    
    // 设置心跳超时
    this.heartbeatTimeout = setTimeout(() => {
      console.warn('心跳超时，连接可能已断开')
      this.handleClose({ code: 1006, reason: 'Heartbeat timeout' })
    }, this.options.heartbeatTimeout)
  }
  
  /**
   * 处理心跳响应
   */
  handleHeartbeat(data) {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
    
    // 计算延迟
    const latency = Date.now() - data.timestamp
    this.emit('heartbeat', { latency })
  }
  
  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.connectionState = ConnectionStates.ERROR
      this.emit('stateChange', this.connectionState)
      this.emit('reconnectFailed')
      return
    }
    
    this.connectionState = ConnectionStates.RECONNECTING
    this.emit('stateChange', this.connectionState)
    this.emit('reconnecting', this.reconnectAttempts + 1)
    
    setTimeout(() => {
      this.reconnectAttempts++
      this.connect()
    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts)) // 指数退避
  }
  
  /**
   * 清空消息队列
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.send(message).catch(error => {
        console.error('发送队列消息失败:', error)
      })
    }
  }
  
  /**
   * 清理待处理请求
   */
  clearPendingRequests() {
    for (const [requestId, { reject, timeout }] of this.pendingRequests) {
      clearTimeout(timeout)
      reject(new Error('Connection closed'))
    }
    this.pendingRequests.clear()
  }
  
  /**
   * 获取连接状态
   */
  getConnectionState() {
    return this.connectionState
  }
  
  /**
   * 检查是否已连接
   */
  isConnected() {
    return this.connectionState === ConnectionStates.CONNECTED
  }
  
  /**
   * 获取连接统计信息
   */
  getStats() {
    return {
      connectionState: this.connectionState,
      reconnectAttempts: this.reconnectAttempts,
      pendingRequests: this.pendingRequests.size,
      queuedMessages: this.messageQueue.length
    }
  }
}

// 创建全局WebSocket管理器
export class CombatWebSocketManager {
  constructor() {
    this.connections = new Map()
  }
  
  /**
   * 创建或获取WebSocket连接
   */
  getConnection(roomId, userId, options = {}) {
    const key = `${roomId}_${userId}`
    
    if (!this.connections.has(key)) {
      const connection = new CombatWebSocket({
        roomId,
        userId,
        ...options
      })
      
      // 监听连接关闭事件，自动清理
      connection.on('disconnected', () => {
        this.connections.delete(key)
      })
      
      this.connections.set(key, connection)
    }
    
    return this.connections.get(key)
  }
  
  /**
   * 关闭指定连接
   */
  closeConnection(roomId, userId) {
    const key = `${roomId}_${userId}`
    const connection = this.connections.get(key)
    
    if (connection) {
      connection.disconnect()
      this.connections.delete(key)
    }
  }
  
  /**
   * 关闭所有连接
   */
  closeAllConnections() {
    for (const connection of this.connections.values()) {
      connection.disconnect()
    }
    this.connections.clear()
  }
  
  /**
   * 获取所有连接的统计信息
   */
  getAllStats() {
    const stats = {}
    for (const [key, connection] of this.connections) {
      stats[key] = connection.getStats()
    }
    return stats
  }
}

// 创建全局实例
export const combatWebSocketManager = new CombatWebSocketManager()

export default CombatWebSocket