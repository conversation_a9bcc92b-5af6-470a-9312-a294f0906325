{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport CombatLog from '@/components/combat/CombatLog.vue';\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue';\nexport default {\n  name: 'CombatTest',\n  components: {\n    CombatLog: CombatLog,\n    InitiativeTracker: InitiativeTracker\n  },\n  data: function data() {\n    return {\n      combatActive: false,\n      currentRound: 1,\n      currentTurn: 0,\n      testLogs: [{\n        id: 1,\n        type: 'system',\n        message: '战斗开始！',\n        timestamp: new Date(),\n        round: 1\n      }, {\n        id: 2,\n        type: 'attack',\n        message: '侦探约翰对邪教徒发起攻击',\n        timestamp: new Date(),\n        round: 1,\n        participant: {\n          name: '侦探约翰',\n          avatar: '/images/default-avatar.png'\n        },\n        diceRoll: {\n          formula: '1d100',\n          rolls: [45],\n          total: 45\n        },\n        successLevel: 'regular'\n      }, {\n        id: 3,\n        type: 'damage',\n        message: '攻击命中，造成8点伤害',\n        timestamp: new Date(),\n        round: 1,\n        severity: 'critical'\n      }],\n      testInitiative: [{\n        id: 1,\n        name: '侦探约翰',\n        initiative: 85,\n        isPlayer: true,\n        avatar: '/images/default-avatar.png',\n        currentHP: 85,\n        maxHP: 100,\n        hasActed: false\n      }, {\n        id: 2,\n        name: '邪教徒',\n        initiative: 72,\n        isPlayer: false,\n        avatar: '/images/default-enemy.png',\n        currentHP: 42,\n        maxHP: 50,\n        hasActed: true\n      }, {\n        id: 3,\n        name: '记者玛丽',\n        initiative: 68,\n        isPlayer: true,\n        avatar: '/images/default-avatar.png',\n        currentHP: 78,\n        maxHP: 90,\n        hasActed: false\n      }]\n    };\n  },\n  methods: {\n    toggleCombat: function toggleCombat() {\n      this.combatActive = !this.combatActive;\n      if (this.combatActive) {\n        this.addTestLog({\n          type: 'system',\n          message: '战斗开始！',\n          round: this.currentRound\n        });\n      } else {\n        this.addTestLog({\n          type: 'system',\n          message: '战斗结束！'\n        });\n      }\n    },\n    addTestLog: function addTestLog() {\n      var customLog = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      var newLog = customLog || {\n        id: Date.now(),\n        type: 'skill',\n        message: \"\\u6D4B\\u8BD5\\u65E5\\u5FD7 - \".concat(new Date().toLocaleTimeString()),\n        timestamp: new Date(),\n        round: this.currentRound,\n        participant: {\n          name: '测试角色',\n          avatar: '/images/default-avatar.png'\n        }\n      };\n      if (!newLog.id) {\n        newLog.id = Date.now();\n      }\n      if (!newLog.timestamp) {\n        newLog.timestamp = new Date();\n      }\n      this.testLogs.push(newLog);\n    },\n    clearTestLogs: function clearTestLogs() {\n      this.testLogs = [];\n    },\n    handleAutoScroll: function handleAutoScroll(enabled) {\n      console.log('自动滚动:', enabled);\n    },\n    handleParticipantSelect: function handleParticipantSelect(participant) {\n      console.log('选中参与者:', participant);\n    }\n  }\n};", "map": {"version": 3, "names": ["CombatLog", "InitiativeTracker", "name", "components", "data", "combatActive", "currentRound", "currentTurn", "testLogs", "id", "type", "message", "timestamp", "Date", "round", "participant", "avatar", "diceRoll", "formula", "rolls", "total", "successLevel", "severity", "testInitiative", "initiative", "isPlayer", "currentHP", "maxHP", "hasActed", "methods", "toggleCombat", "addTestLog", "customLog", "arguments", "length", "undefined", "newLog", "now", "concat", "toLocaleTimeString", "push", "clearTestLogs", "handleAutoScroll", "enabled", "console", "log", "handleParticipantSelect"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CombatTest.vue"], "sourcesContent": ["<template>\r\n  <div class=\"combat-test\">\r\n    <h1>战斗系统测试页面</h1>\r\n    \r\n    <!-- 测试按钮 -->\r\n    <div class=\"test-controls\">\r\n      <button @click=\"toggleCombat\" class=\"test-btn\">\r\n        {{ combatActive ? '结束战斗' : '开始战斗' }}\r\n      </button>\r\n      <button @click=\"addTestLog\" class=\"test-btn\">\r\n        添加测试日志\r\n      </button>\r\n    </div>\r\n    \r\n    <!-- 战斗日志测试 -->\r\n    <div class=\"combat-log-test\">\r\n      <h2>战斗日志测试</h2>\r\n      <CombatLog \r\n        :combat-logs=\"testLogs\"\r\n        @clear-log=\"clearTestLogs\"\r\n        @toggle-auto-scroll=\"handleAutoScroll\"\r\n      />\r\n    </div>\r\n    \r\n    <!-- 先攻追踪器测试 -->\r\n    <div class=\"initiative-test\" v-if=\"combatActive\">\r\n      <h2>先攻追踪器测试</h2>\r\n      <InitiativeTracker\r\n        :initiative-order=\"testInitiative\"\r\n        :current-round=\"currentRound\"\r\n        :current-turn=\"currentTurn\"\r\n        @participant-selected=\"handleParticipantSelect\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CombatLog from '@/components/combat/CombatLog.vue'\r\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue'\r\n\r\nexport default {\r\n  name: 'CombatTest',\r\n  components: {\r\n    CombatLog,\r\n    InitiativeTracker\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      combatActive: false,\r\n      currentRound: 1,\r\n      currentTurn: 0,\r\n      \r\n      testLogs: [\r\n        {\r\n          id: 1,\r\n          type: 'system',\r\n          message: '战斗开始！',\r\n          timestamp: new Date(),\r\n          round: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          type: 'attack',\r\n          message: '侦探约翰对邪教徒发起攻击',\r\n          timestamp: new Date(),\r\n          round: 1,\r\n          participant: {\r\n            name: '侦探约翰',\r\n            avatar: '/images/default-avatar.png'\r\n          },\r\n          diceRoll: {\r\n            formula: '1d100',\r\n            rolls: [45],\r\n            total: 45\r\n          },\r\n          successLevel: 'regular'\r\n        },\r\n        {\r\n          id: 3,\r\n          type: 'damage',\r\n          message: '攻击命中，造成8点伤害',\r\n          timestamp: new Date(),\r\n          round: 1,\r\n          severity: 'critical'\r\n        }\r\n      ],\r\n      \r\n      testInitiative: [\r\n        {\r\n          id: 1,\r\n          name: '侦探约翰',\r\n          initiative: 85,\r\n          isPlayer: true,\r\n          avatar: '/images/default-avatar.png',\r\n          currentHP: 85,\r\n          maxHP: 100,\r\n          hasActed: false\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '邪教徒',\r\n          initiative: 72,\r\n          isPlayer: false,\r\n          avatar: '/images/default-enemy.png',\r\n          currentHP: 42,\r\n          maxHP: 50,\r\n          hasActed: true\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '记者玛丽',\r\n          initiative: 68,\r\n          isPlayer: true,\r\n          avatar: '/images/default-avatar.png',\r\n          currentHP: 78,\r\n          maxHP: 90,\r\n          hasActed: false\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    toggleCombat() {\r\n      this.combatActive = !this.combatActive\r\n      \r\n      if (this.combatActive) {\r\n        this.addTestLog({\r\n          type: 'system',\r\n          message: '战斗开始！',\r\n          round: this.currentRound\r\n        })\r\n      } else {\r\n        this.addTestLog({\r\n          type: 'system',\r\n          message: '战斗结束！'\r\n        })\r\n      }\r\n    },\r\n    \r\n    addTestLog(customLog = null) {\r\n      const newLog = customLog || {\r\n        id: Date.now(),\r\n        type: 'skill',\r\n        message: `测试日志 - ${new Date().toLocaleTimeString()}`,\r\n        timestamp: new Date(),\r\n        round: this.currentRound,\r\n        participant: {\r\n          name: '测试角色',\r\n          avatar: '/images/default-avatar.png'\r\n        }\r\n      }\r\n      \r\n      if (!newLog.id) {\r\n        newLog.id = Date.now()\r\n      }\r\n      if (!newLog.timestamp) {\r\n        newLog.timestamp = new Date()\r\n      }\r\n      \r\n      this.testLogs.push(newLog)\r\n    },\r\n    \r\n    clearTestLogs() {\r\n      this.testLogs = []\r\n    },\r\n    \r\n    handleAutoScroll(enabled) {\r\n      console.log('自动滚动:', enabled)\r\n    },\r\n    \r\n    handleParticipantSelect(participant) {\r\n      console.log('选中参与者:', participant)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.combat-test {\r\n  padding: 20px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.test-controls {\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.test-btn {\r\n  padding: 10px 20px;\r\n  background: #007bff;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.test-btn:hover {\r\n  background: #0056b3;\r\n}\r\n\r\n.combat-log-test {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.combat-log-test h2,\r\n.initiative-test h2 {\r\n  margin-bottom: 15px;\r\n  color: #333;\r\n}\r\n\r\n.initiative-test {\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 为测试页面调整战斗日志高度 */\r\n.combat-log-test :deep(.combat-log) {\r\n  height: 400px;\r\n}\r\n</style>"], "mappings": ";;;AAsCA,OAAOA,SAAQ,MAAO,mCAAkC;AACxD,OAAOC,iBAAgB,MAAO,2CAA0C;AAExE,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVH,SAAS,EAATA,SAAS;IACTC,iBAAgB,EAAhBA;EACF,CAAC;EAEDG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,KAAK;MACnBC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,CAAC;MAEdC,QAAQ,EAAE,CACR;QACEC,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,OAAO;QAChBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,KAAK,EAAE;MACT,CAAC,EACD;QACEL,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,cAAc;QACvBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;UACXb,IAAI,EAAE,MAAM;UACZc,MAAM,EAAE;QACV,CAAC;QACDC,QAAQ,EAAE;UACRC,OAAO,EAAE,OAAO;UAChBC,KAAK,EAAE,CAAC,EAAE,CAAC;UACXC,KAAK,EAAE;QACT,CAAC;QACDC,YAAY,EAAE;MAChB,CAAC,EACD;QACEZ,EAAE,EAAE,CAAC;QACLC,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,KAAK,EAAE,CAAC;QACRQ,QAAQ,EAAE;MACZ,EACD;MAEDC,cAAc,EAAE,CACd;QACEd,EAAE,EAAE,CAAC;QACLP,IAAI,EAAE,MAAM;QACZsB,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,IAAI;QACdT,MAAM,EAAE,4BAA4B;QACpCU,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,GAAG;QACVC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEnB,EAAE,EAAE,CAAC;QACLP,IAAI,EAAE,KAAK;QACXsB,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,KAAK;QACfT,MAAM,EAAE,2BAA2B;QACnCU,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ,CAAC,EACD;QACEnB,EAAE,EAAE,CAAC;QACLP,IAAI,EAAE,MAAM;QACZsB,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,IAAI;QACdT,MAAM,EAAE,4BAA4B;QACpCU,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE;MACZ;IAEJ;EACF,CAAC;EAEDC,OAAO,EAAE;IACPC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACzB,YAAW,GAAI,CAAC,IAAI,CAACA,YAAW;MAErC,IAAI,IAAI,CAACA,YAAY,EAAE;QACrB,IAAI,CAAC0B,UAAU,CAAC;UACdrB,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE,OAAO;UAChBG,KAAK,EAAE,IAAI,CAACR;QACd,CAAC;MACH,OAAO;QACL,IAAI,CAACyB,UAAU,CAAC;UACdrB,IAAI,EAAE,QAAQ;UACdC,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC;IAEDoB,UAAU,WAAVA,UAAUA,CAAA,EAAmB;MAAA,IAAlBC,SAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAI,IAAI;MACzB,IAAMG,MAAK,GAAIJ,SAAQ,IAAK;QAC1BvB,EAAE,EAAEI,IAAI,CAACwB,GAAG,CAAC,CAAC;QACd3B,IAAI,EAAE,OAAO;QACbC,OAAO,gCAAA2B,MAAA,CAAY,IAAIzB,IAAI,CAAC,CAAC,CAAC0B,kBAAkB,CAAC,CAAC,CAAE;QACpD3B,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,KAAK,EAAE,IAAI,CAACR,YAAY;QACxBS,WAAW,EAAE;UACXb,IAAI,EAAE,MAAM;UACZc,MAAM,EAAE;QACV;MACF;MAEA,IAAI,CAACoB,MAAM,CAAC3B,EAAE,EAAE;QACd2B,MAAM,CAAC3B,EAAC,GAAII,IAAI,CAACwB,GAAG,CAAC;MACvB;MACA,IAAI,CAACD,MAAM,CAACxB,SAAS,EAAE;QACrBwB,MAAM,CAACxB,SAAQ,GAAI,IAAIC,IAAI,CAAC;MAC9B;MAEA,IAAI,CAACL,QAAQ,CAACgC,IAAI,CAACJ,MAAM;IAC3B,CAAC;IAEDK,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAACjC,QAAO,GAAI,EAAC;IACnB,CAAC;IAEDkC,gBAAgB,WAAhBA,gBAAgBA,CAACC,OAAO,EAAE;MACxBC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEF,OAAO;IAC9B,CAAC;IAEDG,uBAAuB,WAAvBA,uBAAuBA,CAAC/B,WAAW,EAAE;MACnC6B,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE9B,WAAW;IACnC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}