{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, resolveComponent as _resolveComponent, createVNode as _createVNode, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"room-header\"\n};\nvar _hoisted_2 = {\n  \"class\": \"room-info\"\n};\nvar _hoisted_3 = {\n  \"class\": \"room-title\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  \"class\": \"combat-indicator\"\n};\nvar _hoisted_5 = {\n  \"class\": \"room-meta\"\n};\nvar _hoisted_6 = {\n  \"class\": \"room-creator\"\n};\nvar _hoisted_7 = {\n  \"class\": \"room-players\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  \"class\": \"combat-round\"\n};\nvar _hoisted_9 = {\n  \"class\": \"room-controls\"\n};\nvar _hoisted_10 = {\n  \"class\": \"left-panel\"\n};\nvar _hoisted_11 = {\n  \"class\": \"panel-header\"\n};\nvar _hoisted_12 = {\n  \"class\": \"panel-content\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  \"class\": \"character-section\"\n};\nvar _hoisted_14 = {\n  \"class\": \"character-list\"\n};\nvar _hoisted_15 = {\n  \"class\": \"character-info\"\n};\nvar _hoisted_16 = {\n  \"class\": \"character-name\"\n};\nvar _hoisted_17 = {\n  \"class\": \"character-status\"\n};\nvar _hoisted_18 = {\n  key: 1,\n  \"class\": \"combat-section\"\n};\nvar _hoisted_19 = {\n  \"class\": \"center-panel\"\n};\nvar _hoisted_20 = {\n  key: 0,\n  \"class\": \"scene-content\"\n};\nvar _hoisted_21 = {\n  \"class\": \"map-placeholder\"\n};\nvar _hoisted_22 = {\n  key: 1,\n  \"class\": \"combat-scene\"\n};\nvar _hoisted_23 = {\n  \"class\": \"right-panel\"\n};\nvar _hoisted_24 = {\n  \"class\": \"panel-header\"\n};\nvar _hoisted_25 = {\n  \"class\": \"panel-controls\"\n};\nvar _hoisted_26 = {\n  \"class\": \"panel-content\"\n};\nvar _hoisted_27 = {\n  key: 0,\n  \"class\": \"chat-section\"\n};\nvar _hoisted_28 = {\n  key: 1,\n  \"class\": \"combat-log-section\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$data$roomData$creat, _$data$combatData, _$data$combatData2, _$data$combatData3, _$data$combatData4, _$data$combatData5, _$data$combatData6, _$data$combatData7, _$data$combatData8;\n  var _component_InitiativeTracker = _resolveComponent(\"InitiativeTracker\");\n  var _component_BattlefieldGrid = _resolveComponent(\"BattlefieldGrid\");\n  var _component_ChatBox = _resolveComponent(\"ChatBox\");\n  var _component_CombatLog = _resolveComponent(\"CombatLog\");\n  var _component_DiceRoller = _resolveComponent(\"DiceRoller\");\n  var _component_ForcedCombatMode = _resolveComponent(\"ForcedCombatMode\");\n  var _component_KeeperCombatPanel = _resolveComponent(\"KeeperCombatPanel\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    \"class\": _normalizeClass([\"game-room\", {\n      'combat-mode': $data.combatActive\n    }])\n  }, [_createCommentVNode(\" 房间头部信息 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[9] || (_cache[9] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-door-open\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"h1\", null, _toDisplayString($data.roomData.name || '游戏房间'), 1 /* TEXT */), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"room-status\", $data.roomData.status])\n  }, [_cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n    \"class\": \"status-dot\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($options.getStatusText()), 1 /* TEXT */)], 2 /* CLASS */), _createCommentVNode(\" 战斗状态指示器 \"), $data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"战斗进行中\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, [_cache[10] || (_cache[10] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-crown\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" KP: \" + _toDisplayString(((_$data$roomData$creat = $data.roomData.creator) === null || _$data$roomData$creat === void 0 ? void 0 : _$data$roomData$creat.username) || '未知'), 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_7, [_cache[11] || (_cache[11] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-users\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" 玩家: \" + _toDisplayString($options.currentPlayers) + \"/\" + _toDisplayString($data.roomData.maxPlayers || 6), 1 /* TEXT */)]), $data.combatActive ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, [_cache[12] || (_cache[12] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-clock\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" 第\" + _toDisplayString(((_$data$combatData = $data.combatData) === null || _$data$combatData === void 0 ? void 0 : _$data$combatData.currentRound) || 1) + \"轮 \", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" KP战斗控制按钮 \"), $options.isKeeper && !$data.combatActive ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.startCombat && $options.startCombat.apply($options, arguments);\n    }),\n    \"class\": \"control-btn combat-btn\",\n    title: \"开始战斗\"\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), $options.isKeeper && $data.combatActive ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.endCombat && $options.endCombat.apply($options, arguments);\n    }),\n    \"class\": \"control-btn combat-btn active\",\n    title: \"结束战斗\"\n  }, _cache[14] || (_cache[14] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-stop\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = function () {\n      return $options.toggleFullscreen && $options.toggleFullscreen.apply($options, arguments);\n    }),\n    \"class\": \"control-btn\",\n    title: \"全屏模式\"\n  }, _cache[15] || (_cache[15] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-expand\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = function () {\n      return $options.leaveRoom && $options.leaveRoom.apply($options, arguments);\n    }),\n    \"class\": \"control-btn leave-btn\",\n    title: \"离开房间\"\n  }, _cache[16] || (_cache[16] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sign-out-alt\"\n  }, null, -1 /* CACHED */)]))])]), _createCommentVNode(\" 游戏主体布局 \"), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"game-layout\", {\n      'combat-layout': $data.combatActive\n    }])\n  }, [_createCommentVNode(\" 左侧面板 \"), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"h3\", null, _toDisplayString($data.combatActive ? '战斗状态' : '角色信息'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_12, [_createCommentVNode(\" 非战斗时：角色列表 \"), !$data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.players, function (player) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: player.id,\n      \"class\": \"character-card\"\n    }, [_cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n      \"class\": \"character-avatar\"\n    }, [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-user-circle\"\n    })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString(player.characterName || player.username), 1 /* TEXT */), _createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"character-role\", {\n        'kp-role': player.isKP\n      }])\n    }, _toDisplayString(player.isKP ? 'KP' : 'PL'), 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"span\", {\n      \"class\": _normalizeClass([\"status-indicator\", player.status || 'online'])\n    }, null, 2 /* CLASS */), _createTextVNode(\" \" + _toDisplayString($options.getPlayerStatusText(player.status)), 1 /* TEXT */)])])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：先攻追踪器 \"), $data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_createVNode(_component_InitiativeTracker, {\n    \"initiative-order\": ((_$data$combatData2 = $data.combatData) === null || _$data$combatData2 === void 0 ? void 0 : _$data$combatData2.initiativeOrder) || [],\n    \"current-round\": ((_$data$combatData3 = $data.combatData) === null || _$data$combatData3 === void 0 ? void 0 : _$data$combatData3.currentRound) || 1,\n    \"current-turn\": ((_$data$combatData4 = $data.combatData) === null || _$data$combatData4 === void 0 ? void 0 : _$data$combatData4.currentTurn) || 0,\n    onNextTurn: $options.handleNextTurn,\n    onPreviousTurn: $options.handlePreviousTurn\n  }, null, 8 /* PROPS */, [\"initiative-order\", \"current-round\", \"current-turn\", \"onNextTurn\", \"onPreviousTurn\"])])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 中央面板 \"), _createElementVNode(\"div\", _hoisted_19, [_createCommentVNode(\" 非战斗时：地图场景 \"), !$data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_cache[19] || (_cache[19] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-map\"\n  }, null, -1 /* CACHED */)), _cache[20] || (_cache[20] = _createElementVNode(\"h3\", null, \"地图系统\", -1 /* CACHED */)), _cache[21] || (_cache[21] = _createElementVNode(\"p\", null, \"地图和战斗场景将在这里显示\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function () {\n      return $options.loadTestMap && $options.loadTestMap.apply($options, arguments);\n    }),\n    \"class\": \"load-map-btn\"\n  }, _cache[18] || (_cache[18] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-upload\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 加载测试地图 \")]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：2D战场 \"), $data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createVNode(_component_BattlefieldGrid, {\n    characters: ((_$data$combatData5 = $data.combatData) === null || _$data$combatData5 === void 0 || (_$data$combatData5 = _$data$combatData5.participants) === null || _$data$combatData5 === void 0 ? void 0 : _$data$combatData5.filter(function (p) {\n      return p.isPlayer;\n    })) || [],\n    monsters: ((_$data$combatData6 = $data.combatData) === null || _$data$combatData6 === void 0 || (_$data$combatData6 = _$data$combatData6.participants) === null || _$data$combatData6 === void 0 ? void 0 : _$data$combatData6.filter(function (p) {\n      return !p.isPlayer;\n    })) || [],\n    \"current-round\": ((_$data$combatData7 = $data.combatData) === null || _$data$combatData7 === void 0 ? void 0 : _$data$combatData7.currentRound) || 1,\n    \"is-keeper\": $options.isKeeper,\n    onCharacterMove: $options.handleCharacterMove,\n    onMonsterMove: $options.handleMonsterMove,\n    onAttack: $options.handleAttack\n  }, null, 8 /* PROPS */, [\"characters\", \"monsters\", \"current-round\", \"is-keeper\", \"onCharacterMove\", \"onMonsterMove\", \"onAttack\"])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 右侧面板 \"), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"h3\", null, _toDisplayString($data.combatActive ? '战斗日志' : '聊天'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = function () {\n      return $options.clearMessages && $options.clearMessages.apply($options, arguments);\n    }),\n    \"class\": \"clear-btn\",\n    title: \"清空\"\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-trash\"\n  }, null, -1 /* CACHED */)]))])]), _createElementVNode(\"div\", _hoisted_26, [_createCommentVNode(\" 非战斗时：聊天系统 \"), !$data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_createVNode(_component_ChatBox, {\n    messages: $data.messages,\n    \"current-user\": _ctx.currentUser,\n    onSendMessage: $options.sendMessage\n  }, null, 8 /* PROPS */, [\"messages\", \"current-user\", \"onSendMessage\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：战斗日志 \"), $data.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_createVNode(_component_CombatLog, {\n    \"combat-logs\": $data.combatLogs,\n    \"current-round\": ((_$data$combatData8 = $data.combatData) === null || _$data$combatData8 === void 0 ? void 0 : _$data$combatData8.currentRound) || 1,\n    onClearLogs: $options.clearCombatLogs\n  }, null, 8 /* PROPS */, [\"combat-logs\", \"current-round\", \"onClearLogs\"])])) : _createCommentVNode(\"v-if\", true)])])], 2 /* CLASS */), _createCommentVNode(\" 浮动组件 \"), $data.showDiceRoller ? (_openBlock(), _createBlock(_component_DiceRoller, {\n    key: 0,\n    onClose: _cache[6] || (_cache[6] = function ($event) {\n      return $data.showDiceRoller = false;\n    }),\n    onRollResult: $options.handleDiceResult\n  }, null, 8 /* PROPS */, [\"onRollResult\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗模式组件 \"), $data.combatActive && !$options.isKeeper ? (_openBlock(), _createBlock(_component_ForcedCombatMode, {\n    key: 1,\n    \"combat-data\": $data.combatData,\n    character: $data.currentPlayerCharacter,\n    onAction: $options.handlePlayerAction\n  }, null, 8 /* PROPS */, [\"combat-data\", \"character\", \"onAction\"])) : _createCommentVNode(\"v-if\", true), $data.combatActive && $options.isKeeper ? (_openBlock(), _createBlock(_component_KeeperCombatPanel, {\n    key: 2,\n    \"combat-data\": $data.combatData,\n    players: $data.players,\n    onUpdateCombat: $options.updateCombatData,\n    onAddMonster: $options.addMonster,\n    onUpdateMonster: $options.updateMonster\n  }, null, 8 /* PROPS */, [\"combat-data\", \"players\", \"onUpdateCombat\", \"onAddMonster\", \"onUpdateMonster\"])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_normalizeClass", "$data", "combatActive", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "roomData", "name", "status", "$options", "getStatusText", "_hoisted_4", "_cache", "_hoisted_5", "_hoisted_6", "_$data$roomData$creat", "creator", "username", "_hoisted_7", "currentPlayers", "maxPlayers", "_hoisted_8", "_$data$combatData", "combatData", "currentRound", "_hoisted_9", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "startCombat", "apply", "arguments", "title", "endCombat", "toggleFullscreen", "leaveRoom", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_Fragment", "_renderList", "players", "player", "key", "id", "_hoisted_15", "_hoisted_16", "<PERSON><PERSON><PERSON>", "isKP", "_hoisted_17", "getPlayerStatusText", "_hoisted_18", "_createVNode", "_component_InitiativeTracker", "_$data$combatData2", "initiativeOrder", "_$data$combatData3", "_$data$combatData4", "currentTurn", "onNextTurn", "handleNextTurn", "onPreviousTurn", "handlePreviousTurn", "_hoisted_19", "_hoisted_20", "_hoisted_21", "loadTestMap", "_hoisted_22", "_component_BattlefieldGrid", "characters", "_$data$combatData5", "participants", "filter", "p", "isPlayer", "monsters", "_$data$combatData6", "_$data$combatData7", "onCharacterMove", "handleCharacterMove", "onMonsterMove", "handleMonsterMove", "onAttack", "handleAttack", "_hoisted_23", "_hoisted_24", "_hoisted_25", "clearMessages", "_hoisted_26", "_hoisted_27", "_component_ChatBox", "messages", "_ctx", "currentUser", "onSendMessage", "sendMessage", "_hoisted_28", "_component_CombatLog", "combatLogs", "_$data$combatData8", "onClearLogs", "clearCombatLogs", "showDiceRoller", "_createBlock", "_component_<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClose", "$event", "onRollResult", "handleDiceResult", "_component_ForcedCombatMode", "character", "currentPlayerCharacter", "onAction", "handlePlayerAction", "_component_KeeperCombatPanel", "onUpdateCombat", "updateCombatData", "onAddMonster", "addMonster", "onUpdateMonster", "updateMonster"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomFixed.vue"], "sourcesContent": ["<template>\r\n  <div class=\"game-room\" :class=\"{ 'combat-mode': combatActive }\">\r\n    <!-- 房间头部信息 -->\r\n    <div class=\"room-header\">\r\n      <div class=\"room-info\">\r\n        <div class=\"room-title\">\r\n          <i class=\"fas fa-door-open\"></i>\r\n          <h1>{{ roomData.name || '游戏房间' }}</h1>\r\n          <div class=\"room-status\" :class=\"roomData.status\">\r\n            <span class=\"status-dot\"></span>\r\n            <span>{{ getStatusText() }}</span>\r\n          </div>\r\n          <!-- 战斗状态指示器 -->\r\n          <div v-if=\"combatActive\" class=\"combat-indicator\">\r\n            <i class=\"fas fa-sword\"></i>\r\n            <span>战斗进行中</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"room-meta\">\r\n          <span class=\"room-creator\">\r\n            <i class=\"fas fa-crown\"></i>\r\n            KP: {{ roomData.creator?.username || '未知' }}\r\n          </span>\r\n          <span class=\"room-players\">\r\n            <i class=\"fas fa-users\"></i>\r\n            玩家: {{ currentPlayers }}/{{ roomData.maxPlayers || 6 }}\r\n          </span>\r\n          <span v-if=\"combatActive\" class=\"combat-round\">\r\n            <i class=\"fas fa-clock\"></i>\r\n            第{{ combatData?.currentRound || 1 }}轮\r\n          </span>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"room-controls\">\r\n        <!-- KP战斗控制按钮 -->\r\n        <button \r\n          v-if=\"isKeeper && !combatActive\" \r\n          @click=\"startCombat\" \r\n          class=\"control-btn combat-btn\"\r\n          title=\"开始战斗\"\r\n        >\r\n          <i class=\"fas fa-sword\"></i>\r\n        </button>\r\n        <button \r\n          v-if=\"isKeeper && combatActive\" \r\n          @click=\"endCombat\" \r\n          class=\"control-btn combat-btn active\"\r\n          title=\"结束战斗\"\r\n        >\r\n          <i class=\"fas fa-stop\"></i>\r\n        </button>\r\n        <button @click=\"toggleFullscreen\" class=\"control-btn\" title=\"全屏模式\">\r\n          <i class=\"fas fa-expand\"></i>\r\n        </button>\r\n        <button @click=\"leaveRoom\" class=\"control-btn leave-btn\" title=\"离开房间\">\r\n          <i class=\"fas fa-sign-out-alt\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 游戏主体布局 -->\r\n    <div class=\"game-layout\" :class=\"{ 'combat-layout': combatActive }\">\r\n      <!-- 左侧面板 -->\r\n      <div class=\"left-panel\">\r\n        <div class=\"panel-header\">\r\n          <h3>{{ combatActive ? '战斗状态' : '角色信息' }}</h3>\r\n        </div>\r\n        \r\n        <div class=\"panel-content\">\r\n          <!-- 非战斗时：角色列表 -->\r\n          <div v-if=\"!combatActive\" class=\"character-section\">\r\n            <div class=\"character-list\">\r\n              <div v-for=\"player in players\" :key=\"player.id\" class=\"character-card\">\r\n                <div class=\"character-avatar\">\r\n                  <i class=\"fas fa-user-circle\"></i>\r\n                </div>\r\n                <div class=\"character-info\">\r\n                  <div class=\"character-name\">{{ player.characterName || player.username }}</div>\r\n                  <div class=\"character-role\" :class=\"{ 'kp-role': player.isKP }\">\r\n                    {{ player.isKP ? 'KP' : 'PL' }}\r\n                  </div>\r\n                  <div class=\"character-status\">\r\n                    <span class=\"status-indicator\" :class=\"player.status || 'online'\"></span>\r\n                    {{ getPlayerStatusText(player.status) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 战斗时：先攻追踪器 -->\r\n          <div v-if=\"combatActive\" class=\"combat-section\">\r\n            <InitiativeTracker\r\n              :initiative-order=\"combatData?.initiativeOrder || []\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              :current-turn=\"combatData?.currentTurn || 0\"\r\n              @next-turn=\"handleNextTurn\"\r\n              @previous-turn=\"handlePreviousTurn\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 中央面板 -->\r\n      <div class=\"center-panel\">\r\n        <!-- 非战斗时：地图场景 -->\r\n        <div v-if=\"!combatActive\" class=\"scene-content\">\r\n          <div class=\"map-placeholder\">\r\n            <i class=\"fas fa-map\"></i>\r\n            <h3>地图系统</h3>\r\n            <p>地图和战斗场景将在这里显示</p>\r\n            <button @click=\"loadTestMap\" class=\"load-map-btn\">\r\n              <i class=\"fas fa-upload\"></i>\r\n              加载测试地图\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 战斗时：2D战场 -->\r\n        <div v-if=\"combatActive\" class=\"combat-scene\">\r\n          <BattlefieldGrid\r\n            :characters=\"combatData?.participants?.filter(p => p.isPlayer) || []\"\r\n            :monsters=\"combatData?.participants?.filter(p => !p.isPlayer) || []\"\r\n            :current-round=\"combatData?.currentRound || 1\"\r\n            :is-keeper=\"isKeeper\"\r\n            @character-move=\"handleCharacterMove\"\r\n            @monster-move=\"handleMonsterMove\"\r\n            @attack=\"handleAttack\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧面板 -->\r\n      <div class=\"right-panel\">\r\n        <div class=\"panel-header\">\r\n          <h3>{{ combatActive ? '战斗日志' : '聊天' }}</h3>\r\n          <div class=\"panel-controls\">\r\n            <button @click=\"clearMessages\" class=\"clear-btn\" title=\"清空\">\r\n              <i class=\"fas fa-trash\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"panel-content\">\r\n          <!-- 非战斗时：聊天系统 -->\r\n          <div v-if=\"!combatActive\" class=\"chat-section\">\r\n            <ChatBox\r\n              :messages=\"messages\"\r\n              :current-user=\"currentUser\"\r\n              @send-message=\"sendMessage\"\r\n            />\r\n          </div>\r\n          \r\n          <!-- 战斗时：战斗日志 -->\r\n          <div v-if=\"combatActive\" class=\"combat-log-section\">\r\n            <CombatLog \r\n              :combat-logs=\"combatLogs\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              @clear-logs=\"clearCombatLogs\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 浮动组件 -->\r\n    <DiceRoller \r\n      v-if=\"showDiceRoller\" \r\n      @close=\"showDiceRoller = false\"\r\n      @roll-result=\"handleDiceResult\"\r\n    />\r\n    \r\n    <!-- 战斗模式组件 -->\r\n    <ForcedCombatMode\r\n      v-if=\"combatActive && !isKeeper\"\r\n      :combat-data=\"combatData\"\r\n      :character=\"currentPlayerCharacter\"\r\n      @action=\"handlePlayerAction\"\r\n    />\r\n    \r\n    <KeeperCombatPanel\r\n      v-if=\"combatActive && isKeeper\"\r\n      :combat-data=\"combatData\"\r\n      :players=\"players\"\r\n      @update-combat=\"updateCombatData\"\r\n      @add-monster=\"addMonster\"\r\n      @update-monster=\"updateMonster\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport ChatBox from '@/components/ChatBox.vue'\r\nimport DiceRoller from '@/components/DiceRoller.vue'\r\nimport CombatLog from '@/components/combat/CombatLog.vue'\r\nimport BattlefieldGrid from '@/components/combat/BattlefieldGrid.vue'\r\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue'\r\nimport KeeperCombatPanel from '@/components/combat/KeeperCombatPanel.vue'\r\nimport ForcedCombatMode from '@/components/combat/ForcedCombatMode.vue'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'GameRoomFixed',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    ChatBox,\r\n    DiceRoller,\r\n    CombatLog,\r\n    BattlefieldGrid,\r\n    InitiativeTracker,\r\n    KeeperCombatPanel,\r\n    ForcedCombatMode\r\n  },\r\n  props: {\r\n    roomId: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      // 房间数据\r\n      roomData: {\r\n        name: '神秘的古宅',\r\n        status: 'active',\r\n        creator: {\r\n          username: 'KP_Master'\r\n        },\r\n        maxPlayers: 6\r\n      },\r\n      \r\n      // 战斗状态\r\n      combatActive: false,\r\n      combatData: null,\r\n      combatLogs: [],\r\n      \r\n      // 玩家数据\r\n      players: [\r\n        {\r\n          id: 1,\r\n          username: 'KP_Master',\r\n          characterName: 'KP',\r\n          isKP: true,\r\n          status: 'online'\r\n        },\r\n        {\r\n          id: 2,\r\n          username: 'Player1',\r\n          characterName: '侦探约翰',\r\n          isKP: false,\r\n          status: 'online'\r\n        },\r\n        {\r\n          id: 3,\r\n          username: 'Player2',\r\n          characterName: '记者玛丽',\r\n          isKP: false,\r\n          status: 'online'\r\n        }\r\n      ],\r\n      \r\n      // 聊天数据\r\n      messages: [\r\n        {\r\n          id: 1,\r\n          username: 'KP_Master',\r\n          content: '欢迎来到游戏房间！',\r\n          timestamp: new Date()\r\n        },\r\n        {\r\n          id: 2,\r\n          username: 'Player1',\r\n          content: '准备好开始冒险了！',\r\n          timestamp: new Date()\r\n        }\r\n      ],\r\n      \r\n      // 组件状态\r\n      showDiceRoller: false,\r\n      currentPlayerCharacter: null,\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    ...mapGetters(['currentUser']),\r\n    \r\n    isKeeper() {\r\n      return this.currentUser?.isKP || false\r\n    },\r\n    \r\n    currentPlayers() {\r\n      return this.players.length\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    getStatusText() {\r\n      const statusMap = {\r\n        'active': '进行中',\r\n        'waiting': '等待中',\r\n        'paused': '已暂停',\r\n        'ended': '已结束'\r\n      }\r\n      return statusMap[this.roomData.status] || '未知'\r\n    },\r\n    \r\n    getPlayerStatusText(status) {\r\n      const statusMap = {\r\n        'online': '在线',\r\n        'away': '离开',\r\n        'offline': '离线'\r\n      }\r\n      return statusMap[status] || '未知'\r\n    },\r\n    \r\n    async startCombat() {\r\n      try {\r\n        this.combatActive = true\r\n        this.combatData = {\r\n          currentRound: 1,\r\n          currentTurn: 0,\r\n          initiativeOrder: this.generateInitiativeOrder(),\r\n          participants: this.generateCombatParticipants()\r\n        }\r\n        \r\n        this.addCombatLog('system', '战斗开始！')\r\n        \r\n        // 这里可以添加WebSocket通知其他玩家\r\n        // await this.notifyPlayersOfCombatStart()\r\n        \r\n      } catch (error) {\r\n        console.error('开始战斗失败:', error)\r\n        this.$store.dispatch('showNotification', {\r\n          type: 'error',\r\n          message: '开始战斗失败，请重试'\r\n        })\r\n      }\r\n    },\r\n    \r\n    async endCombat() {\r\n      try {\r\n        this.combatActive = false\r\n        this.addCombatLog('system', '战斗结束！')\r\n        \r\n        // 重置战斗数据\r\n        this.combatData = null\r\n        \r\n        // 这里可以添加WebSocket通知其他玩家\r\n        // await this.notifyPlayersOfCombatEnd()\r\n        \r\n      } catch (error) {\r\n        console.error('结束战斗失败:', error)\r\n        this.$store.dispatch('showNotification', {\r\n          type: 'error',\r\n          message: '结束战斗失败，请重试'\r\n        })\r\n      }\r\n    },\r\n    \r\n    generateInitiativeOrder() {\r\n      // 生成先攻顺序的示例数据\r\n      return [\r\n        { id: 1, name: '侦探约翰', initiative: 65, isPlayer: true },\r\n        { id: 2, name: '记者玛丽', initiative: 45, isPlayer: true },\r\n        { id: 3, name: '邪教徒', initiative: 40, isPlayer: false }\r\n      ].sort((a, b) => b.initiative - a.initiative)\r\n    },\r\n    \r\n    generateCombatParticipants() {\r\n      // 生成战斗参与者的示例数据\r\n      return [\r\n        {\r\n          id: 1,\r\n          name: '侦探约翰',\r\n          isPlayer: true,\r\n          position: { x: 2, y: 2 },\r\n          hp: 12,\r\n          maxHp: 12,\r\n          mp: 10,\r\n          maxMp: 10\r\n        },\r\n        {\r\n          id: 2,\r\n          name: '记者玛丽',\r\n          isPlayer: true,\r\n          position: { x: 3, y: 2 },\r\n          hp: 10,\r\n          maxHp: 10,\r\n          mp: 12,\r\n          maxMp: 12\r\n        },\r\n        {\r\n          id: 3,\r\n          name: '邪教徒',\r\n          isPlayer: false,\r\n          position: { x: 8, y: 8 },\r\n          hp: 8,\r\n          maxHp: 8,\r\n          mp: 0,\r\n          maxMp: 0\r\n        }\r\n      ]\r\n    },\r\n    \r\n    addCombatLog(type, message, details = null) {\r\n      this.combatLogs.push({\r\n        id: Date.now() + Math.random(),\r\n        type,\r\n        message,\r\n        details,\r\n        timestamp: new Date(),\r\n        round: this.combatData?.currentRound || 1\r\n      })\r\n    },\r\n    \r\n    handleNextTurn() {\r\n      if (this.combatData) {\r\n        const maxTurn = this.combatData.initiativeOrder.length - 1\r\n        if (this.combatData.currentTurn >= maxTurn) {\r\n          this.combatData.currentTurn = 0\r\n          this.combatData.currentRound++\r\n          this.addCombatLog('system', `第${this.combatData.currentRound}轮开始`)\r\n        } else {\r\n          this.combatData.currentTurn++\r\n        }\r\n        \r\n        const currentActor = this.combatData.initiativeOrder[this.combatData.currentTurn]\r\n        this.addCombatLog('turn', `轮到 ${currentActor.name} 行动`)\r\n      }\r\n    },\r\n    \r\n    handlePreviousTurn() {\r\n      if (this.combatData) {\r\n        if (this.combatData.currentTurn <= 0) {\r\n          if (this.combatData.currentRound > 1) {\r\n            this.combatData.currentRound--\r\n            this.combatData.currentTurn = this.combatData.initiativeOrder.length - 1\r\n          }\r\n        } else {\r\n          this.combatData.currentTurn--\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleCharacterMove(characterId, newPosition) {\r\n      const participant = this.combatData?.participants?.find(p => p.id === characterId)\r\n      if (participant) {\r\n        participant.position = newPosition\r\n        this.addCombatLog('move', `${participant.name} 移动到 (${newPosition.x}, ${newPosition.y})`)\r\n      }\r\n    },\r\n    \r\n    handleMonsterMove(monsterId, newPosition) {\r\n      const monster = this.combatData?.participants?.find(p => p.id === monsterId && !p.isPlayer)\r\n      if (monster) {\r\n        monster.position = newPosition\r\n        this.addCombatLog('move', `${monster.name} 移动到 (${newPosition.x}, ${newPosition.y})`)\r\n      }\r\n    },\r\n    \r\n    handleAttack(attackData) {\r\n      this.addCombatLog('attack', `${attackData.attacker} 攻击 ${attackData.target}`, attackData)\r\n    },\r\n    \r\n    handlePlayerAction(action) {\r\n      this.addCombatLog('action', `玩家执行: ${action.type}`, action)\r\n    },\r\n    \r\n    updateCombatData(newData) {\r\n      this.combatData = { ...this.combatData, ...newData }\r\n    },\r\n    \r\n    addMonster(monsterData) {\r\n      if (this.combatData?.participants) {\r\n        this.combatData.participants.push(monsterData)\r\n        this.addCombatLog('system', `${monsterData.name} 加入战斗`)\r\n      }\r\n    },\r\n    \r\n    updateMonster(monsterId, updates) {\r\n      const monster = this.combatData?.participants?.find(p => p.id === monsterId && !p.isPlayer)\r\n      if (monster) {\r\n        Object.assign(monster, updates)\r\n        this.addCombatLog('system', `${monster.name} 状态更新`)\r\n      }\r\n    },\r\n    \r\n    sendMessage(content) {\r\n      const message = {\r\n        id: Date.now(),\r\n        username: this.currentUser?.username || '匿名',\r\n        content,\r\n        timestamp: new Date()\r\n      }\r\n      this.messages.push(message)\r\n      \r\n      // 这里可以添加WebSocket发送消息\r\n      // await this.sendMessageToRoom(message)\r\n    },\r\n    \r\n    handleDiceResult(result) {\r\n      const message = `🎲 ${this.currentUser?.username || '匿名'} 投掷了 ${result.dice}: ${result.results.join(', ')} (总计: ${result.total})`\r\n      this.sendMessage(message)\r\n    },\r\n    \r\n    clearMessages() {\r\n      if (confirm('确定要清空聊天记录吗？')) {\r\n        this.messages = []\r\n      }\r\n    },\r\n    \r\n    clearCombatLogs() {\r\n      if (confirm('确定要清空战斗日志吗？')) {\r\n        this.combatLogs = []\r\n      }\r\n    },\r\n    \r\n    loadTestMap() {\r\n      this.$store.dispatch('showNotification', {\r\n        type: 'info',\r\n        message: '测试地图加载功能暂未实现'\r\n      })\r\n    },\r\n    \r\n    toggleFullscreen() {\r\n      this.isFullscreen = !this.isFullscreen\r\n      if (this.isFullscreen) {\r\n        document.documentElement.requestFullscreen?.()\r\n      } else {\r\n        document.exitFullscreen?.()\r\n      }\r\n    },\r\n    \r\n    leaveRoom() {\r\n      if (confirm('确定要离开房间吗？')) {\r\n        this.$router.push('/')\r\n      }\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    // 初始化房间数据\r\n    this.loadRoomData()\r\n    \r\n    // 设置WebSocket连接\r\n    // this.setupWebSocket()\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    // 清理WebSocket连接\r\n    // this.cleanupWebSocket()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* ===== 游戏房间基础样式 ===== */\r\n.game-room {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode {\r\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\r\n}\r\n\r\n/* ===== 房间头部 ===== */\r\n.room-header {\r\n  background: linear-gradient(135deg, #22c55e 0%, #16a085 100%);\r\n  color: white;\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode .room-header {\r\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\r\n}\r\n\r\n.room-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.room-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.room-title i {\r\n  font-size: 24px;\r\n}\r\n\r\n.room-title h1 {\r\n  margin: 0;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.room-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.status-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background: #10b981;\r\n}\r\n\r\n.room-status.waiting .status-dot {\r\n  background: #f59e0b;\r\n}\r\n\r\n.room-status.paused .status-dot {\r\n  background: #6b7280;\r\n}\r\n\r\n.combat-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  animation: combat-pulse 2s infinite;\r\n}\r\n\r\n@keyframes combat-pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  gap: 16px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.room-meta span {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n.room-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.control-btn {\r\n  padding: 10px 16px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 14px;\r\n}\r\n\r\n.control-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.combat-btn.active {\r\n  background: rgba(239, 68, 68, 0.8);\r\n  animation: combat-btn-pulse 1.5s infinite;\r\n}\r\n\r\n@keyframes combat-btn-pulse {\r\n  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }\r\n  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0); }\r\n}\r\n\r\n.leave-btn:hover {\r\n  background: rgba(239, 68, 68, 0.8);\r\n}\r\n\r\n/* ===== 游戏布局 ===== */\r\n.game-layout {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-layout.combat-layout {\r\n  background: rgba(17, 24, 39, 0.1);\r\n}\r\n\r\n.left-panel,\r\n.right-panel {\r\n  width: 320px;\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode .left-panel,\r\n.game-room.combat-mode .right-panel {\r\n  background: rgba(31, 41, 55, 0.95);\r\n  border-color: #4b5563;\r\n  color: #f3f4f6;\r\n}\r\n\r\n.center-panel {\r\n  flex: 1;\r\n  background: white;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode .center-panel {\r\n  background: rgba(17, 24, 39, 0.9);\r\n}\r\n\r\n.panel-header {\r\n  background: #22c55e;\r\n  color: white;\r\n  padding: 16px 20px;\r\n  font-weight: 600;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.game-room.combat-mode .panel-header {\r\n  background: #dc2626;\r\n}\r\n\r\n.panel-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.clear-btn {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border: none;\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clear-btn:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.panel-content {\r\n  flex: 1;\r\n  padding: 20px;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* ===== 角色列表 ===== */\r\n.character-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.character-card {\r\n  background: #f8fafc;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  border: 1px solid #e2e8f0;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.character-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.game-room.combat-mode .character-card {\r\n  background: rgba(55, 65, 81, 0.8);\r\n  border-color: #6b7280;\r\n}\r\n\r\n.character-avatar {\r\n  font-size: 32px;\r\n  color: #6b7280;\r\n}\r\n\r\n.character-info {\r\n  flex: 1;\r\n}\r\n\r\n.character-name {\r\n  font-weight: 600;\r\n  font-size: 16px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.character-role {\r\n  font-size: 12px;\r\n  color: #6b7280;\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  background: #e5e7eb;\r\n  display: inline-block;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.character-role.kp-role {\r\n  background: #fef3c7;\r\n  color: #92400e;\r\n}\r\n\r\n.character-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 12px;\r\n}\r\n\r\n.status-indicator {\r\n  width: 6px;\r\n  height: 6px;\r\n  border-radius: 50%;\r\n  background: #10b981;\r\n}\r\n\r\n.status-indicator.away {\r\n  background: #f59e0b;\r\n}\r\n\r\n.status-indicator.offline {\r\n  background: #6b7280;\r\n}\r\n\r\n/* ===== 地图占位符 ===== */\r\n.map-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  color: #6b7280;\r\n  text-align: center;\r\n  padding: 40px;\r\n}\r\n\r\n.map-placeholder i {\r\n  font-size: 64px;\r\n  margin-bottom: 20px;\r\n  opacity: 0.5;\r\n}\r\n\r\n.map-placeholder h3 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 24px;\r\n}\r\n\r\n.map-placeholder p {\r\n  margin: 0 0 24px 0;\r\n  font-size: 16px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.load-map-btn {\r\n  background: #22c55e;\r\n  color: white;\r\n  border: none;\r\n  padding: 12px 24px;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.load-map-btn:hover {\r\n  background: #16a085;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n/* ===== 战斗场景 ===== */\r\n.combat-scene {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: #1f2937;\r\n  border-radius: 8px;\r\n  margin: 8px;\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n@media (max-width: 1200px) {\r\n  .left-panel,\r\n  .right-panel {\r\n    width: 280px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .game-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .left-panel,\r\n  .right-panel {\r\n    width: 100%;\r\n    height: 200px;\r\n  }\r\n  \r\n  .room-header {\r\n    padding: 12px 16px;\r\n  }\r\n  \r\n  .room-title h1 {\r\n    font-size: 20px;\r\n  }\r\n  \r\n  .room-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .room-controls {\r\n    flex-direction: column;\r\n  }\r\n}\r\n\r\n/* ===== 动画效果 ===== */\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.slide-enter-active,\r\n.slide-leave-active {\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.slide-enter-from {\r\n  transform: translateX(-100%);\r\n}\r\n\r\n.slide-leave-to {\r\n  transform: translateX(100%);\r\n}\r\n</style>"], "mappings": ";;;;;;;EAGS,SAAM;AAAa;;EACjB,SAAM;AAAW;;EACf,SAAM;AAAY;;;EAQI,SAAM;;;EAK5B,SAAM;AAAW;;EACd,SAAM;AAAc;;EAIpB,SAAM;AAAc;;;EAIA,SAAM;;;EAO/B,SAAM;AAAe;;EA8BrB,SAAM;AAAY;;EAChB,SAAM;AAAc;;EAIpB,SAAM;AAAe;;;EAEE,SAAM;;;EACzB,SAAM;AAAgB;;EAKlB,SAAM;AAAgB;;EACpB,SAAM;AAAgB;;EAItB,SAAM;AAAkB;;;EAUZ,SAAM;;;EAa9B,SAAM;AAAc;;;EAEG,SAAM;;;EACzB,SAAM;AAAiB;;;EAYL,SAAM;;;EAc5B,SAAM;AAAa;;EACjB,SAAM;AAAc;;EAElB,SAAM;AAAgB;;EAOxB,SAAM;AAAe;;;EAEE,SAAM;;;;EASP,SAAM;;;;;;;;;;;uBA1JvCA,mBAAA,CA4LM;IA5LD,SAAKC,eAAA,EAAC,WAAW;MAAA,eAA0BC,KAAA,CAAAC;IAAY;MAC1DC,mBAAA,YAAe,EACfC,mBAAA,CAwDM,OAxDNC,UAwDM,GAvDJD,mBAAA,CA4BM,OA5BNE,UA4BM,GA3BJF,mBAAA,CAYM,OAZNG,UAYM,G,0BAXJH,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,4BAC3BA,mBAAA,CAAsC,YAAAI,gBAAA,CAA/BP,KAAA,CAAAQ,QAAQ,CAACC,IAAI,4BACpBN,mBAAA,CAGM;IAHD,SAAKJ,eAAA,EAAC,aAAa,EAASC,KAAA,CAAAQ,QAAQ,CAACE,MAAM;gCAC9CP,mBAAA,CAAgC;IAA1B,SAAM;EAAY,4BACxBA,mBAAA,CAAkC,cAAAI,gBAAA,CAAzBI,QAAA,CAAAC,aAAa,mB,kBAExBV,mBAAA,aAAgB,EACLF,KAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAGM,OAHNe,UAGM,EAAAC,MAAA,QAAAA,MAAA,OAFJX,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAAkB,cAAZ,OAAK,mB,2CAGfA,mBAAA,CAaM,OAbNY,UAaM,GAZJZ,mBAAA,CAGO,QAHPa,UAGO,G,4BAFLb,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,OACxB,GAAAI,gBAAA,CAAG,EAAAU,qBAAA,GAAAjB,KAAA,CAAAQ,QAAQ,CAACU,OAAO,cAAAD,qBAAA,uBAAhBA,qBAAA,CAAkBE,QAAQ,0B,GAEnChB,mBAAA,CAGO,QAHPiB,UAGO,G,4BAFLjB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,OACxB,GAAAI,gBAAA,CAAGI,QAAA,CAAAU,cAAc,IAAG,GAAC,GAAAd,gBAAA,CAAGP,KAAA,CAAAQ,QAAQ,CAACc,UAAU,sB,GAErCtB,KAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAGO,QAHPyB,UAGO,G,4BAFLpB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,IAC3B,GAAAI,gBAAA,CAAG,EAAAiB,iBAAA,GAAAxB,KAAA,CAAAyB,UAAU,cAAAD,iBAAA,uBAAVA,iBAAA,CAAYE,YAAY,UAAQ,IACtC,gB,4CAIJvB,mBAAA,CAwBM,OAxBNwB,UAwBM,GAvBJzB,mBAAA,cAAiB,EAETS,QAAA,CAAAiB,QAAQ,KAAK5B,KAAA,CAAAC,YAAY,I,cADjCH,mBAAA,CAOS;;IALN+B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAmB,WAAA,IAAAnB,QAAA,CAAAmB,WAAA,CAAAC,KAAA,CAAApB,QAAA,EAAAqB,SAAA,CAAW;IAAA;IACnB,SAAM,wBAAwB;IAC9BC,KAAK,EAAC;kCAEN9B,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,yCAGjBQ,QAAA,CAAAiB,QAAQ,IAAI5B,KAAA,CAAAC,YAAY,I,cADhCH,mBAAA,CAOS;;IALN+B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAuB,SAAA,IAAAvB,QAAA,CAAAuB,SAAA,CAAAH,KAAA,CAAApB,QAAA,EAAAqB,SAAA,CAAS;IAAA;IACjB,SAAM,+BAA+B;IACrCC,KAAK,EAAC;kCAEN9B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,yCAExBA,mBAAA,CAES;IAFA0B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAwB,gBAAA,IAAAxB,QAAA,CAAAwB,gBAAA,CAAAJ,KAAA,CAAApB,QAAA,EAAAqB,SAAA,CAAgB;IAAA;IAAE,SAAM,aAAa;IAACC,KAAK,EAAC;kCAC1D9B,mBAAA,CAA6B;IAA1B,SAAM;EAAe,0B,IAE1BA,mBAAA,CAES;IAFA0B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAAyB,SAAA,IAAAzB,QAAA,CAAAyB,SAAA,CAAAL,KAAA,CAAApB,QAAA,EAAAqB,SAAA,CAAS;IAAA;IAAE,SAAM,uBAAuB;IAACC,KAAK,EAAC;kCAC7D9B,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,0B,QAKpCD,mBAAA,YAAe,EACfC,mBAAA,CAsGM;IAtGD,SAAKJ,eAAA,EAAC,aAAa;MAAA,iBAA4BC,KAAA,CAAAC;IAAY;MAC9DC,mBAAA,UAAa,EACbC,mBAAA,CAsCM,OAtCNkC,WAsCM,GArCJlC,mBAAA,CAEM,OAFNmC,WAEM,GADJnC,mBAAA,CAA6C,YAAAI,gBAAA,CAAtCP,KAAA,CAAAC,YAAY,mC,GAGrBE,mBAAA,CAgCM,OAhCNoC,WAgCM,GA/BJrC,mBAAA,eAAkB,E,CACNF,KAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAkBM,OAlBN0C,WAkBM,GAjBJrC,mBAAA,CAgBM,OAhBNsC,WAgBM,I,kBAfJ3C,mBAAA,CAcM4C,SAAA,QAAAC,WAAA,CAdgB3C,KAAA,CAAA4C,OAAO,YAAjBC,MAAM;yBAAlB/C,mBAAA,CAcM;MAd0BgD,GAAG,EAAED,MAAM,CAACE,EAAE;MAAE,SAAM;oCACpD5C,mBAAA,CAEM;MAFD,SAAM;IAAkB,IAC3BA,mBAAA,CAAkC;MAA/B,SAAM;IAAoB,G,qBAE/BA,mBAAA,CASM,OATN6C,WASM,GARJ7C,mBAAA,CAA+E,OAA/E8C,WAA+E,EAAA1C,gBAAA,CAAhDsC,MAAM,CAACK,aAAa,IAAIL,MAAM,CAAC1B,QAAQ,kBACtEhB,mBAAA,CAEM;MAFD,SAAKJ,eAAA,EAAC,gBAAgB;QAAA,WAAsB8C,MAAM,CAACM;MAAI;wBACvDN,MAAM,CAACM,IAAI,uCAEhBhD,mBAAA,CAGM,OAHNiD,WAGM,GAFJjD,mBAAA,CAAyE;MAAnE,SAAKJ,eAAA,EAAC,kBAAkB,EAAS8C,MAAM,CAACnC,MAAM;8CAAqB,GACzE,GAAAH,gBAAA,CAAGI,QAAA,CAAA0C,mBAAmB,CAACR,MAAM,CAACnC,MAAM,kB;2EAO9CR,mBAAA,eAAkB,EACPF,KAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAQM,OARNwD,WAQM,GAPJC,YAAA,CAMEC,4BAAA;IALC,kBAAgB,EAAE,EAAAC,kBAAA,GAAAzD,KAAA,CAAAyB,UAAU,cAAAgC,kBAAA,uBAAVA,kBAAA,CAAYC,eAAe;IAC7C,eAAa,EAAE,EAAAC,kBAAA,GAAA3D,KAAA,CAAAyB,UAAU,cAAAkC,kBAAA,uBAAVA,kBAAA,CAAYjC,YAAY;IACvC,cAAY,EAAE,EAAAkC,kBAAA,GAAA5D,KAAA,CAAAyB,UAAU,cAAAmC,kBAAA,uBAAVA,kBAAA,CAAYC,WAAW;IACrCC,UAAS,EAAEnD,QAAA,CAAAoD,cAAc;IACzBC,cAAa,EAAErD,QAAA,CAAAsD;6JAMxB/D,mBAAA,UAAa,EACbC,mBAAA,CA0BM,OA1BN+D,WA0BM,GAzBJhE,mBAAA,eAAkB,E,CACNF,KAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAUM,OAVNqE,WAUM,GATJhE,mBAAA,CAQM,OARNiE,WAQM,G,4BAPJjE,mBAAA,CAA0B;IAAvB,SAAM;EAAY,4B,4BACrBA,mBAAA,CAAa,YAAT,MAAI,qB,4BACRA,mBAAA,CAAoB,WAAjB,eAAa,qBAChBA,mBAAA,CAGS;IAHA0B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAA0D,WAAA,IAAA1D,QAAA,CAAA0D,WAAA,CAAAtC,KAAA,CAAApB,QAAA,EAAAqB,SAAA,CAAW;IAAA;IAAE,SAAM;kCACjC7B,mBAAA,CAA6B;IAA1B,SAAM;EAAe,2B,iBAAK,UAE/B,E,6CAIJD,mBAAA,cAAiB,EACNF,KAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAUM,OAVNwE,WAUM,GATJf,YAAA,CAQEgB,0BAAA;IAPCC,UAAU,EAAE,EAAAC,kBAAA,GAAAzE,KAAA,CAAAyB,UAAU,cAAAgD,kBAAA,gBAAAA,kBAAA,GAAVA,kBAAA,CAAYC,YAAY,cAAAD,kBAAA,uBAAxBA,kBAAA,CAA0BE,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,QAAQ;IAAA;IAC5DC,QAAQ,EAAE,EAAAC,kBAAA,GAAA/E,KAAA,CAAAyB,UAAU,cAAAsD,kBAAA,gBAAAA,kBAAA,GAAVA,kBAAA,CAAYL,YAAY,cAAAK,kBAAA,uBAAxBA,kBAAA,CAA0BJ,MAAM,CAAC,UAAAC,CAAC;MAAA,QAAKA,CAAC,CAACC,QAAQ;IAAA;IAC3D,eAAa,EAAE,EAAAG,kBAAA,GAAAhF,KAAA,CAAAyB,UAAU,cAAAuD,kBAAA,uBAAVA,kBAAA,CAAYtD,YAAY;IACvC,WAAS,EAAEf,QAAA,CAAAiB,QAAQ;IACnBqD,eAAc,EAAEtE,QAAA,CAAAuE,mBAAmB;IACnCC,aAAY,EAAExE,QAAA,CAAAyE,iBAAiB;IAC/BC,QAAM,EAAE1E,QAAA,CAAA2E;8KAKfpF,mBAAA,UAAa,EACbC,mBAAA,CA6BM,OA7BNoF,WA6BM,GA5BJpF,mBAAA,CAOM,OAPNqF,WAOM,GANJrF,mBAAA,CAA2C,YAAAI,gBAAA,CAApCP,KAAA,CAAAC,YAAY,kCACnBE,mBAAA,CAIM,OAJNsF,WAIM,GAHJtF,mBAAA,CAES;IAFA0B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEH,QAAA,CAAA+E,aAAA,IAAA/E,QAAA,CAAA+E,aAAA,CAAA3D,KAAA,CAAApB,QAAA,EAAAqB,SAAA,CAAa;IAAA;IAAE,SAAM,WAAW;IAACC,KAAK,EAAC;kCACrD9B,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,QAK7BA,mBAAA,CAkBM,OAlBNwF,WAkBM,GAjBJzF,mBAAA,eAAkB,E,CACNF,KAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAMM,OANN8F,WAMM,GALJrC,YAAA,CAIEsC,kBAAA;IAHCC,QAAQ,EAAE9F,KAAA,CAAA8F,QAAQ;IAClB,cAAY,EAAEC,IAAA,CAAAC,WAAW;IACzBC,aAAY,EAAEtF,QAAA,CAAAuF;iHAInBhG,mBAAA,cAAiB,EACNF,KAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAMM,OANNqG,WAMM,GALJ5C,YAAA,CAIE6C,oBAAA;IAHC,aAAW,EAAEpG,KAAA,CAAAqG,UAAU;IACvB,eAAa,EAAE,EAAAC,kBAAA,GAAAtG,KAAA,CAAAyB,UAAU,cAAA6E,kBAAA,uBAAVA,kBAAA,CAAY5E,YAAY;IACvC6E,WAAU,EAAE5F,QAAA,CAAA6F;wIAOvBtG,mBAAA,UAAa,EAELF,KAAA,CAAAyG,cAAc,I,cADtBC,YAAA,CAIEC,qBAAA;;IAFCC,OAAK,EAAA9F,MAAA,QAAAA,MAAA,gBAAA+F,MAAA;MAAA,OAAE7G,KAAA,CAAAyG,cAAc;IAAA;IACrBK,YAAW,EAAEnG,QAAA,CAAAoG;kFAGhB7G,mBAAA,YAAe,EAEPF,KAAA,CAAAC,YAAY,KAAKU,QAAA,CAAAiB,QAAQ,I,cADjC8E,YAAA,CAKEM,2BAAA;;IAHC,aAAW,EAAEhH,KAAA,CAAAyB,UAAU;IACvBwF,SAAS,EAAEjH,KAAA,CAAAkH,sBAAsB;IACjCC,QAAM,EAAExG,QAAA,CAAAyG;0GAIHpH,KAAA,CAAAC,YAAY,IAAIU,QAAA,CAAAiB,QAAQ,I,cADhC8E,YAAA,CAOEW,4BAAA;;IALC,aAAW,EAAErH,KAAA,CAAAyB,UAAU;IACvBmB,OAAO,EAAE5C,KAAA,CAAA4C,OAAO;IAChB0E,cAAa,EAAE3G,QAAA,CAAA4G,gBAAgB;IAC/BC,YAAW,EAAE7G,QAAA,CAAA8G,UAAU;IACvBC,eAAc,EAAE/G,QAAA,CAAAgH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}