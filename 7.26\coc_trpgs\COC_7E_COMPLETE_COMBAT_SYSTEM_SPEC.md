# COC 7版完整2D战斗系统规格文档

## 系统概述

基于《克苏鲁的呼唤》第七版规则书，设计一个完整的多人2D战斗系统，严格遵循规则书中的所有战斗机制，支持玩家vs玩家、玩家vs怪物、多人团队战斗等多种模式。

## 核心设计原则

1. **严格遵循COC 7版规则** - 所有机制都基于规则书内容，包括投骰机制、大成功大失败、孤注一掷等
2. **强制战斗模式** - KP可触发全屏战斗，玩家无法退出，战斗模块对玩家隐藏
3. **2D卡牌式战斗** - 角色以卡牌形式展示，支持撞击、射击、法术等动画效果
4. **实时多人同步** - 通过WebSocket实现实时战斗状态同步
5. **完整的数据对接** - 与角色卡、MySQL数据库、背包道具系统完全同步
6. **KP专属控制** - 战斗系统只有KP可以操作，玩家只能响应战斗指令

## 第一部分：战斗规则系统

### 1.1 基础属性系统

#### 基础属性 (3d6×5)
- **力量 (STR)**: 影响近战伤害和负重
- **敏捷 (DEX)**: 影响先攻顺序和闪避
- **智力 (INT)**: 影响技能点数和法术学习
- **体质 (CON)**: 影响生命值和抗性检定
- **外貌 (APP)**: 影响社交互动
- **教育 (EDU)**: 影响知识技能
- **体型 (SIZ)**: 影响生命值和伤害加值
- **意志 (POW)**: 影响理智值和魔法点数

#### 派生属性计算
```javascript
生命值 = Math.floor((体质 + 体型) / 10)
理智值 = 意志力
魔法点数 = Math.floor(意志力 / 5)
伤害加值 = calculateDamageBonus(力量 + 体型)
体格 = Math.floor((力量 + 体型) / 10)
移动力 = Math.floor(Math.min(敏捷, 体质) / 10)
```

#### 伤害加值计算表
- 64以下: -2
- 65-84: -1  
- 85-124: 0
- 125-164: +1d4
- 165-204: +1d6
- 205以上: +2d6

### 1.2 战斗轮次系统

#### 先攻顺序 (严格按规则书)
1. **敏捷决定行动顺序**: 敏捷高的角色先行动
2. **射击武器优势**: 准备好枪械的角色敏捷+50计算顺序
3. **相同敏捷处理**: 敏捷相同时，战斗技能高的先行动
4. **延迟行动**: 角色可选择延迟到其他角色行动后

#### 行动类型 (每轮一个主要行动)
- **攻击**: 近战或远程攻击 (使用对应技能投骰)
- **战技**: 缴械、撞倒、擒抱等特殊攻击
- **脱离战斗**: 逃离近战范围
- **施法**: 使用魔法 (消耗魔法点数)
- **其他行动**: 撬锁等需要时间的动作

#### 投骰机制 (严格按规则书)
- **技能检定**: 1d100 ≤ 技能值为成功
- **难度等级**: 常规(技能值)、困难(技能值/2)、极难(技能值/5)
- **大成功**: 投出01，造成最大伤害或贯穿效果
- **大失败**: 投出96-100(技能<50)或100(技能≥50)，产生严重后果
- **不可孤注一掷**: 战斗检定不能使用孤注一掷

### 1.3 战斗技能系统 (完全按规则书)

#### 近战技能 (对抗检定)
- **格斗(斗殴)**: 25% 基础值，包含拳头(1d3+DB)、踢击(1d6+DB)、头锤(1d4+DB)
- **格斗(擒抱)**: 25% 基础值，用于战技和控制
- **格斗(剑)**: 20% 基础值，1d8+DB伤害，可格挡
- **格斗(斧)**: 15% 基础值，1d8+2+DB伤害
- **格斗(矛)**: 20% 基础值，1d8+1+DB伤害，长武器
- **格斗(鞭)**: 05% 基础值，1d3+DB伤害，可缠绕

#### 远程技能 (单独检定)
- **射击(手枪)**: 20% 基础值，1d10伤害，可连射
- **射击(步枪/霰弹枪)**: 25% 基础值，步枪2d6+4，霰弹枪4d6/2d6/1d6
- **射击(冲锋枪)**: 15% 基础值，1d10伤害，全自动射击
- **射击(机枪)**: 10% 基础值，2d6+4伤害，全自动
- **投掷**: 25% 基础值，武器伤害+DB/2，射程=力量/5码
- **射击(弓)**: 15% 基础值，1d6+DB/2伤害

#### 防御选择 (被攻击时选择)
- **反击**: 使用格斗技能对抗攻击者，成功等级高者获胜
- **闪避**: 使用闪避技能(敏捷/2)对抗攻击者，完全避免伤害
- **寻找掩体**: 闪避检定成功，射击者获得惩罚骰，失去下次攻击机会### 1
.4 武器装备系统 (严格按规则书数据)

#### 近战武器数据
```javascript
const meleeWeapons = {
  fist: { 
    name: '拳头', 
    damage: '1d3+DB', 
    skill: 'fighting_brawl', 
    range: 1,
    special: '基础攻击' 
  },
  knife: { 
    name: '小刀', 
    damage: '1d4+DB', 
    skill: 'fighting_brawl', 
    range: 1,
    special: '可投掷' 
  },
  sword: { 
    name: '剑', 
    damage: '1d8+DB', 
    skill: 'sword', 
    range: 1,
    special: '可格挡' 
  },
  axe: { 
    name: '斧头', 
    damage: '1d8+2+DB', 
    skill: 'axe', 
    range: 1,
    special: '高伤害' 
  },
  club: { 
    name: '棍棒', 
    damage: '1d6+DB', 
    skill: 'fighting_brawl', 
    range: 1,
    special: '可格挡' 
  },
  spear: { 
    name: '长矛', 
    damage: '1d8+1+DB', 
    skill: 'spear', 
    range: 2,
    special: '长武器，可投掷' 
  },
  whip: { 
    name: '鞭子', 
    damage: '1d3+DB', 
    skill: 'whip', 
    range: 3,
    special: '可缠绕' 
  }
}
```

#### 远程武器数据
```javascript
const rangedWeapons = {
  pistol: { 
    name: '手枪', 
    damage: '1d10', 
    skill: 'firearms_handgun',
    ranges: { close: 15, medium: 30, long: 60 },
    ammo: 6,
    rof: 1,
    malfunction: 95
  },
  rifle: {
    name: '步枪',
    damage: '2d6+4',
    skill: 'firearms_rifle',
    ranges: { close: 90, medium: 180, long: 360 },
    ammo: 5,
    rof: 1,
    malfunction: 98
  },
  shotgun: {
    name: '霰弹枪',
    damage: '4d6/2d6/1d6',
    skill: 'firearms_shotgun',
    ranges: { close: 10, medium: 20, long: 50 },
    ammo: 2,
    rof: 1,
    malfunction: 100
  },
  submachinegun: {
    name: '冲锋枪',
    damage: '1d10',
    skill: 'firearms_smg',
    ranges: { close: 20, medium: 40, long: 80 },
    ammo: 30,
    rof: '1或全自动',
    malfunction: 96
  },
  bow: {
    name: '弓箭',
    damage: '1d6+DB/2',
    skill: 'firearms_bow',
    ranges: { close: 30, medium: 60, long: 120 },
    ammo: 1,
    rof: 1,
    malfunction: '-'
  }
}
```

#### 护甲系统
```javascript
const armor = {
  leather: { 
    name: '皮甲', 
    protection: 1, 
    coverage: 'body',
    type: 'general' 
  },
  kevlar: { 
    name: '防弹背心', 
    protection: 4, 
    coverage: 'torso', 
    type: 'ballistic' 
  },
  heavy: { 
    name: '重型护甲', 
    protection: 6, 
    coverage: 'body',
    type: 'general' 
  },
  helmet: {
    name: '头盔',
    protection: 1,
    coverage: 'head',
    type: 'general'
  },
  shield: {
    name: '盾牌',
    protection: 1,
    coverage: 'arm',
    type: 'block',
    special: '可用于格挡'
  }
}
```

### 1.5 战斗流程 (严格按规则书)

#### 战斗开始
1. **确定参与者**: KP选择参战角色和怪物
2. **排列行动顺序**: 按敏捷值排序(射击武器+50)
3. **开始战斗轮**: 每轮约10秒游戏时间
4. **轮次管理**: 所有人行动完毕后进入下一轮

#### 近战攻击流程 (对抗检定)
1. **攻击者行动**: 宣布攻击目标，投1d100进行格斗检定
2. **目标选择**: 反击(格斗技能)或闪避(闪避技能)
3. **对抗检定**: 比较成功等级，高者获胜
4. **伤害计算**: 获胜者造成武器伤害+伤害加值
5. **护甲减免**: 从伤害中减去护甲值
6. **生命值扣除**: 最终伤害从生命值中扣除

#### 远程攻击流程 (单独检定)
1. **射击检定**: 根据距离确定难度等级
   - 基础射程内: 常规难度
   - 远射程(2倍): 困难难度  
   - 超射程(4倍): 极难难度
2. **射击调整**: 应用奖励骰/惩罚骰
   - 奖励骰: 瞄准、抵近射击、大目标
   - 惩罚骰: 掩体、高速移动、小目标、连射
3. **命中判定**: 成功则造成武器伤害
4. **护甲减免**: 应用护甲值
5. **弹药消耗**: 扣除使用的弹药

#### 伤害计算 (严格按规则书)
```javascript
function calculateDamage(weapon, attacker, defender, successLevel) {
  let baseDamage = rollDice(weapon.damage)
  let damageBonus = attacker.damageBonus
  
  // 极难成功处理
  if (successLevel === 'extreme') {
    if (weapon.impaling) {
      // 贯穿武器: 最大伤害 + 额外一次伤害骰
      baseDamage = weapon.maxDamage + rollDice(weapon.damage)
      damageBonus = attacker.maxDamageBonus
    } else {
      // 非贯穿武器: 最大伤害
      baseDamage = weapon.maxDamage
      damageBonus = attacker.maxDamageBonus
    }
  }
  
  let totalDamage = baseDamage + damageBonus
  let armorReduction = defender.armor.protection
  let finalDamage = Math.max(0, totalDamage - armorReduction)
  return finalDamage
}
```

### 1.6 特殊战斗规则 (严格按规则书)

#### 战技系统
1. **体格比较**: 体格差距影响成功率
   - 体格小3级以上: 战技无效
   - 体格小2级: 承受2个惩罚骰
   - 体格小1级: 承受1个惩罚骰
2. **战技效果**: 
   - 缴械武器或夺取物品
   - 撞倒或推开敌人
   - 擒抱控制(持续到被挣脱)
   - 造成持续劣势(惩罚骰)

#### 先发制人(突袭)
- **潜行检定**: 攻击者潜行 vs 目标侦查/聆听
- **突袭效果**: 目标无法反击或闪避，攻击获得奖励骰
- **自动成功**: 背后偷袭等情况可自动成功(除非大失败)

#### 寡不敌众
- **多敌劣势**: 角色闪避/反击后，后续攻击者获得奖励骰
- **不适用远程**: 只影响近战攻击
- **多次攻击**: 怪物多次攻击时需相应次数防御后才触发

#### 射击特殊规则
1. **瞄准**: 花费一轮瞄准，下轮获得奖励骰
2. **抵近射击**: 敏捷/5英尺内，获得奖励骰，可被近战攻击
3. **手枪连射**: 每轮2-3发，每发承受惩罚骰
4. **全自动射击**: 
   - 弹幕系统: 每组最多技能值/10发子弹(最少3发)
   - 递增惩罚: 第二组+1惩罚骰，第三组+2惩罚骰
   - 命中计算: 成功时一半子弹命中，极难成功全部命中
5. **武器故障**: 投出≥故障值时武器卡壳或哑火

#### 护甲和掩体
- **护甲值**: 直接从伤害中减去
- **掩体**: 一半以上身体被遮挡，射击者承受惩罚骰
- **材质护甲**: 不同材质对不同攻击类型有效

### 1.7 魔法战斗系统

#### 法术分类
- **攻击法术**: 直接造成伤害
- **防护法术**: 提供保护效果
- **辅助法术**: 增强能力或削弱敌人
- **召唤法术**: 召唤生物协助战斗

#### 施法流程
1. 宣布施法
2. 消耗魔法点数
3. 进行法术检定
4. 产生效果
5. 可能的理智损失

#### 常用战斗法术
```javascript
const combatSpells = {
  shrivelling: {
    name: '枯萎术',
    cost: '1-10 MP',
    damage: '1d6 per 2 MP',
    range: '10m',
    castTime: '1 round',
    sanLoss: '1d4'
  },
  deflectHarm: {
    name: '偏转伤害',
    cost: '3 MP',
    effect: 'Reduce damage by 1d6+2',
    duration: '1 hour',
    sanLoss: '1d3'
  },
  bind: {
    name: '束缚',
    cost: '5 MP',
    effect: 'Target cannot move',
    duration: '1d6 rounds',
    sanLoss: '1d4'
  }
}
```###
 1.8 状态效果系统

#### 生命状态
- **健康**: 生命值 > 50%
- **受伤**: 生命值 25-50%
- **重伤**: 生命值 1-24%
- **昏迷**: 生命值 = 0
- **濒死**: 生命值 < 0

#### 理智状态
- **理智**: 理智值 > 50%
- **不安**: 理智值 25-50%
- **恐慌**: 理智值 1-24%
- **疯狂**: 理智值 = 0

#### 特殊状态
- **中毒**: 持续伤害
- **疾病**: 属性减值
- **恐惧**: 攻击惩罚
- **狂暴**: 攻击奖励，防御惩罚
- **瞄准**: 下轮攻击奖励
- **全防御**: 防御奖励，无法攻击

### 1.9 多人战斗机制

#### 团队协作
- **包围攻击**: 多人攻击同一目标获得奖励
- **协同防御**: 互相掩护提高防御
- **战术配合**: 前排肉盾，后排输出

#### 群体法术
- **范围攻击**: 影响多个目标
- **群体治疗**: 同时治疗多人
- **群体增益**: 提升团队能力

#### 友军误伤
- 近战混战中的误伤判定
- 远程攻击的友军误伤
- 范围法术的友军伤害

### 1.10 AI战斗系统

#### 怪物AI等级
- **野兽级**: 简单攻击，无策略
- **人类级**: 基础战术，会使用掩护
- **精英级**: 高级战术，团队配合
- **神话级**: 超自然能力，复杂策略

#### AI决策树
```javascript
function monsterAI(monster, battlefield) {
  if (monster.hp < monster.maxHp * 0.3) {
    return considerRetreat(monster, battlefield)
  }
  if (hasRangedWeapon(monster) && getDistance(monster, nearestEnemy) > 5) {
    return rangedAttack(monster)
  }
  if (canUseSpecialAbility(monster)) {
    return useSpecialAbility(monster)
  }
  return meleeAttack(monster, nearestEnemy)
}
```

## 第二部分：2D视觉系统设计

### 2.1 战场布局

#### 网格系统
- **六角形网格**: 更真实的距离计算
- **距离单位**: 每格代表1-2米距离
- **地形要素**: 掩体、障碍物、危险区域
- **视线系统**: 遮挡和视野范围

#### 战场尺寸
- **小型战场**: 10x8格 (适合室内战斗)
- **中型战场**: 15x12格 (适合街道战斗)
- **大型战场**: 20x15格 (适合野外战斗)

### 2.2 角色卡片设计

#### 卡片正面显示
- **头像**: 角色头像或怪物图标
- **姓名**: 角色/怪物名称
- **生命值条**: 绿色->黄色->红色渐变
- **理智值条**: 蓝色->紫色->红色渐变
- **状态图标**: 显示当前状态效果

#### 卡片悬停详情
- **完整属性**: 8项基础属性
- **技能列表**: 相关战斗技能
- **装备信息**: 武器和护甲
- **法术列表**: 可用法术

#### 状态指示
- **行动状态**: 
  - 绿色边框: 当前行动者
  - 蓝色边框: 已行动
  - 灰色边框: 未行动
- **生命状态**:
  - 绿色: 健康
  - 黄色: 受伤
  - 红色: 重伤
  - 黑色: 昏迷/死亡

### 2.3 战斗动画

#### 近战攻击动画
- **冲撞效果**: 攻击者卡片向目标移动
- **武器挥舞**: 武器图标围绕卡片旋转
- **命中闪光**: 命中时的白色闪光效果
- **伤害数字**: 飞出的红色伤害数字

#### 远程攻击动画
- **弹道轨迹**: 从攻击者到目标的弹道线
- **枪口火光**: 射击时的黄色闪光
- **弹壳抛出**: 小型弹壳图标飞出
- **命中爆炸**: 命中时的小型爆炸效果

#### 法术效果动画
- **施法光环**: 施法者周围的魔法光环
- **法术轨迹**: 法术能量的飞行轨迹
- **魔法爆炸**: 法术命中的特殊效果
- **状态变化**: 增益/减益的视觉效果

#### 防御动画
- **闪避动作**: 卡片快速左右移动
- **格挡动作**: 盾牌或武器图标出现
- **护甲闪光**: 护甲吸收伤害的闪光

### 2.4 UI界面设计

#### 行动条 (Initiative Tracker)
- **垂直排列**: 显示所有参与者
- **当前指示**: 高亮当前行动者
- **头像显示**: 小型头像和名称
- **状态图标**: 显示各种状态效果

#### 技能面板
- **攻击选项**: 近战/远程攻击按钮
- **防御选项**: 闪避/格挡按钮
- **特殊行动**: 瞄准/移动/施法按钮
- **快捷键**: 键盘快捷键支持

#### 物品栏
- **武器切换**: 主手/副手武器
- **弹药显示**: 当前弹药数量
- **道具使用**: 治疗药品等
- **法术书**: 可用法术列表

#### 状态栏
- **生命值**: 数字和进度条
- **理智值**: 数字和进度条
- **魔法值**: 数字和进度条
- **状态效果**: 图标列表

## 第三部分：技术实现架构

### 3.1 前端组件结构

```
CombatSystem/
├── CombatMain.vue              # 战斗主界面
├── components/
│   ├── CombatField.vue         # 战场网格
│   ├── CharacterCard.vue       # 角色卡片
│   ├── InitiativeTracker.vue   # 先攻追踪器
│   ├── ActionPanel.vue         # 行动面板
│   ├── SkillPanel.vue          # 技能面板
│   ├── InventoryPanel.vue      # 物品栏
│   ├── StatusPanel.vue         # 状态面板
│   ├── CombatLog.vue          # 战斗日志
│   ├── DamageCalculator.vue    # 伤害计算器
│   ├── SpellCaster.vue         # 法术施放器
│   ├── CombatSettings.vue      # 战斗设置
│   └── animations/
│       ├── AttackAnimation.vue  # 攻击动画
│       ├── DefenseAnimation.vue # 防御动画
│       ├── SpellAnimation.vue   # 法术动画
│       └── StatusAnimation.vue  # 状态动画
├── utils/
│   ├── combatRules.js          # 战斗规则
│   ├── diceRoller.js           # 骰子系统
│   ├── damageCalculator.js     # 伤害计算
│   ├── aiSystem.js             # AI系统
│   └── animationManager.js     # 动画管理
└── data/
    ├── weapons.js              # 武器数据
    ├── armor.js                # 护甲数据
    ├── spells.js               # 法术数据
    └── monsters.js             # 怪物数据
```### 3.
2 后端数据结构

#### 战斗实例
```javascript
class CombatInstance {
  constructor(roomId) {
    this.id = generateId()
    this.roomId = roomId
    this.participants = []
    this.currentRound = 1
    this.currentTurn = 0
    this.initiativeOrder = []
    this.battlefield = new Battlefield()
    this.combatLog = []
    this.status = 'preparing' // preparing, active, ended
    this.settings = {
      gridSize: { width: 15, height: 12 },
      turnTimeLimit: 60, // 秒
      autoEndTurn: true
    }
  }
}
```

#### 战斗参与者
```javascript
class CombatParticipant {
  constructor(character, isPlayer = true) {
    this.id = character.id
    this.name = character.name
    this.isPlayer = isPlayer
    this.position = { x: 0, y: 0 }
    this.facing = 0 // 面向方向
    
    // 基础属性
    this.attributes = {
      str: character.str,
      con: character.con,
      siz: character.siz,
      dex: character.dex,
      app: character.app,
      int: character.int,
      pow: character.pow,
      edu: character.edu
    }
    
    // 派生属性
    this.maxHp = Math.floor((character.con + character.siz) / 10)
    this.currentHp = character.currentHp || this.maxHp
    this.maxSan = character.pow
    this.currentSan = character.currentSan || this.maxSan
    this.maxMp = Math.floor(character.pow / 5)
    this.currentMp = character.currentMp || this.maxMp
    this.damageBonus = this.calculateDamageBonus(character.str + character.siz)
    this.build = Math.floor((character.str + character.siz) / 10)
    this.moveRate = Math.floor(Math.min(character.dex, character.con) / 10)
    
    // 技能
    this.skills = character.skills
    
    // 装备
    this.equipment = {
      mainHand: character.mainHandWeapon,
      offHand: character.offHandWeapon,
      armor: character.armor,
      items: character.items || []
    }
    
    // 法术
    this.spells = character.spells || []
    
    // 战斗状态
    this.statusEffects = []
    this.hasActed = false
    this.isAiming = false
    this.aimingTarget = null
    this.isDefending = false
    this.defenseType = null // 'dodge' or 'parry'
    
    // AI相关 (如果是NPC)
    this.aiLevel = character.aiLevel || 'beast'
    this.aiStrategy = character.aiStrategy || 'aggressive'
  }
  
  calculateDamageBonus(strSiz) {
    if (strSiz < 65) return -2
    if (strSiz < 85) return -1
    if (strSiz < 125) return 0
    if (strSiz < 165) return '+1d4'
    if (strSiz < 205) return '+1d6'
    return '+2d6'
  }
}
```

#### 战场系统
```javascript
class Battlefield {
  constructor(width = 15, height = 12) {
    this.width = width
    this.height = height
    this.grid = this.initializeGrid()
    this.terrain = []
    this.obstacles = []
    this.hazards = []
  }
  
  initializeGrid() {
    const grid = []
    for (let x = 0; x < this.width; x++) {
      grid[x] = []
      for (let y = 0; y < this.height; y++) {
        grid[x][y] = {
          x, y,
          occupant: null,
          terrain: 'normal',
          cover: 0, // 0-4 掩护等级
          elevation: 0,
          visibility: true
        }
      }
    }
    return grid
  }
  
  getDistance(pos1, pos2) {
    return Math.sqrt(Math.pow(pos2.x - pos1.x, 2) + Math.pow(pos2.y - pos1.y, 2))
  }
  
  hasLineOfSight(pos1, pos2) {
    // 实现视线检查算法
    return true // 简化实现
  }
  
  getCover(pos1, pos2) {
    // 计算掩护加值
    return 0 // 简化实现
  }
}
```

### 3.3 WebSocket通信协议

#### 消息类型定义
```javascript
const COMBAT_MESSAGES = {
  // 战斗管理
  COMBAT_START: 'combat_start',
  COMBAT_END: 'combat_end',
  COMBAT_PAUSE: 'combat_pause',
  COMBAT_RESUME: 'combat_resume',
  
  // 先攻和回合
  INITIATIVE_ROLL: 'initiative_roll',
  INITIATIVE_ORDER: 'initiative_order',
  TURN_START: 'turn_start',
  TURN_END: 'turn_end',
  ROUND_START: 'round_start',
  ROUND_END: 'round_end',
  
  // 行动
  ACTION_DECLARE: 'action_declare',
  ACTION_EXECUTE: 'action_execute',
  ACTION_RESULT: 'action_result',
  ACTION_CANCEL: 'action_cancel',
  
  // 攻击和防御
  ATTACK_DECLARE: 'attack_declare',
  ATTACK_ROLL: 'attack_roll',
  DEFENSE_DECLARE: 'defense_declare',
  DEFENSE_ROLL: 'defense_roll',
  DAMAGE_ROLL: 'damage_roll',
  DAMAGE_APPLY: 'damage_apply',
  
  // 法术
  SPELL_CAST: 'spell_cast',
  SPELL_EFFECT: 'spell_effect',
  MP_CONSUME: 'mp_consume',
  
  // 状态更新
  HP_UPDATE: 'hp_update',
  SAN_UPDATE: 'san_update',
  MP_UPDATE: 'mp_update',
  STATUS_EFFECT_ADD: 'status_effect_add',
  STATUS_EFFECT_REMOVE: 'status_effect_remove',
  
  // 位置和移动
  POSITION_UPDATE: 'position_update',
  MOVE_ACTION: 'move_action',
  
  // 战斗日志
  COMBAT_LOG: 'combat_log',
  
  // 强制战斗模式
  FORCE_COMBAT_MODE: 'force_combat_mode',
  EXIT_COMBAT_MODE: 'exit_combat_mode'
}
```

#### 消息格式示例
```javascript
// 战斗开始
{
  type: 'combat_start',
  data: {
    combatId: 'combat_123',
    participants: [...],
    battlefield: {...},
    settings: {...}
  }
}

// 攻击行动
{
  type: 'attack_declare',
  data: {
    combatId: 'combat_123',
    attackerId: 'player_1',
    targetId: 'monster_1',
    weapon: 'pistol',
    attackType: 'normal', // normal, aimed, burst
    position: { x: 5, y: 3 }
  }
}

// 伤害应用
{
  type: 'damage_apply',
  data: {
    combatId: 'combat_123',
    targetId: 'monster_1',
    damage: 8,
    damageType: 'physical',
    newHp: 12,
    statusChange: 'injured'
  }
}
```

### 3.4 数据库设计

#### 战斗记录表
```sql
CREATE TABLE combat_sessions (
  id VARCHAR(50) PRIMARY KEY,
  room_id VARCHAR(50) NOT NULL,
  start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMP NULL,
  status ENUM('preparing', 'active', 'paused', 'ended') DEFAULT 'preparing',
  current_round INT DEFAULT 1,
  current_turn INT DEFAULT 0,
  battlefield_data JSON,
  settings JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 战斗参与者表
```sql
CREATE TABLE combat_participants (
  id VARCHAR(50) PRIMARY KEY,
  combat_id VARCHAR(50) NOT NULL,
  character_id VARCHAR(50),
  name VARCHAR(100) NOT NULL,
  is_player BOOLEAN DEFAULT TRUE,
  position_x INT DEFAULT 0,
  position_y INT DEFAULT 0,
  current_hp INT NOT NULL,
  max_hp INT NOT NULL,
  current_san INT NOT NULL,
  max_san INT NOT NULL,
  current_mp INT NOT NULL,
  max_mp INT NOT NULL,
  status_effects JSON,
  equipment JSON,
  initiative_roll INT,
  initiative_order INT,
  has_acted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (combat_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
);
```

#### 战斗日志表
```sql
CREATE TABLE combat_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  combat_id VARCHAR(50) NOT NULL,
  round_number INT NOT NULL,
  turn_number INT NOT NULL,
  action_type VARCHAR(50) NOT NULL,
  actor_id VARCHAR(50),
  target_id VARCHAR(50),
  action_data JSON,
  result_data JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (combat_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
);
```

### 3.5 强制战斗模式实现

#### KP触发战斗
```javascript
// KP界面组件
class KPCombatControls {
  triggerCombat() {
    // 1. 创建战斗实例
    const combatData = {
      participants: this.getSelectedParticipants(),
      battlefield: this.getBattlefieldSettings(),
      settings: this.getCombatSettings()
    }
    
    // 2. 发送强制战斗消息
    websocketService.send({
      type: 'FORCE_COMBAT_MODE',
      data: combatData
    })
    
    // 3. 显示全屏战斗界面
    this.showFullscreenCombat()
  }
}
```

#### 玩家端强制响应
```javascript
// 玩家端处理
websocketService.on('FORCE_COMBAT_MODE', (data) => {
  // 1. 强制进入全屏模式
  document.body.classList.add('combat-mode-forced')
  
  // 2. 隐藏其他界面元素
  this.hideNonCombatElements()
  
  // 3. 显示战斗界面
  this.showCombatInterface(data)
  
  // 4. 禁用退出功能
  this.disableExit()
  
  // 5. 显示强制提示
  this.showForcedModeNotification()
})
```

#### 全屏战斗界面
```vue
<template>
  <div class="forced-combat-overlay" v-if="isForcedCombat">
    <div class="combat-notification">
      <h2>⚔️ 触发战斗 ⚔️</h2>
      <p>KP已启动战斗模式，无法退出</p>
    </div>
    
    <CombatMain 
      :forced="true"
      :combat-data="combatData"
      @combat-end="handleCombatEnd"
    />
    
    <!-- 只有KP可以看到的结束按钮 -->
    <button 
      v-if="isKP" 
      @click="endForcedCombat"
      class="end-combat-btn"
    >
      结束战斗
    </button>
  </div>
</template>
```#
# 第四部分：核心组件实现

### 4.1 战斗主界面组件

```vue
<!-- CombatMain.vue -->
<template>
  <div class="combat-main" :class="{ 'forced-mode': forced }">
    <!-- 战斗头部信息 -->
    <div class="combat-header">
      <div class="combat-info">
        <h3>⚔️ 战斗进行中</h3>
        <div class="round-info">
          <span>第 {{ currentRound }} 轮</span>
          <span class="turn-indicator">{{ currentTurnInfo }}</span>
        </div>
      </div>
      <div class="combat-controls" v-if="isKP">
        <button @click="pauseCombat" class="control-btn">
          {{ isPaused ? '继续' : '暂停' }}
        </button>
        <button @click="endCombat" class="control-btn danger">
          结束战斗
        </button>
      </div>
    </div>

    <!-- 主要战斗区域 -->
    <div class="combat-content">
      <!-- 先攻追踪器 -->
      <div class="initiative-sidebar">
        <InitiativeTracker 
          :participants="participants"
          :current-turn="currentTurn"
          :current-round="currentRound"
          @participant-selected="selectParticipant"
        />
      </div>

      <!-- 战场区域 -->
      <div class="battlefield-container">
        <CombatField 
          :participants="participants"
          :selected-participant="selectedParticipant"
          :current-turn="currentTurn"
          :battlefield="battlefield"
          :grid-size="gridSize"
          @participant-clicked="selectParticipant"
          @position-clicked="handlePositionClick"
          @attack-target="handleAttackTarget"
        />
      </div>

      <!-- 行动面板 -->
      <div class="action-sidebar">
        <ActionPanel 
          v-if="selectedParticipant && canAct"
          :participant="selectedParticipant"
          :available-actions="availableActions"
          :target-participant="targetParticipant"
          @action-selected="handleAction"
          @cancel-action="cancelAction"
        />
        
        <!-- 角色详情面板 -->
        <CharacterDetailsPanel
          v-if="selectedParticipant"
          :participant="selectedParticipant"
          :show-full-details="showFullDetails"
          @toggle-details="showFullDetails = !showFullDetails"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="combat-footer">
      <div class="status-indicators">
        <div class="hp-indicator" v-if="selectedParticipant">
          <span>生命值: {{ selectedParticipant.currentHp }}/{{ selectedParticipant.maxHp }}</span>
          <div class="hp-bar">
            <div class="hp-fill" :style="{ width: hpPercentage + '%' }"></div>
          </div>
        </div>
        <div class="san-indicator" v-if="selectedParticipant">
          <span>理智值: {{ selectedParticipant.currentSan }}/{{ selectedParticipant.maxSan }}</span>
          <div class="san-bar">
            <div class="san-fill" :style="{ width: sanPercentage + '%' }"></div>
          </div>
        </div>
      </div>
      
      <div class="quick-actions">
        <button @click="rollInitiative" v-if="!combatStarted">开始战斗</button>
        <button @click="nextTurn" v-if="isKP && combatStarted">下一回合</button>
        <button @click="toggleCombatLog">{{ showLog ? '隐藏' : '显示' }}日志</button>
      </div>
    </div>

    <!-- 战斗日志 -->
    <CombatLog 
      v-if="showLog"
      :combat-log="combatLog"
      :max-entries="50"
      @clear-log="clearCombatLog"
    />

    <!-- 弹窗组件 -->
    <DamageCalculator
      v-if="showDamageCalculator"
      :attacker="damageCalculator.attacker"
      :defender="damageCalculator.defender"
      :weapon="damageCalculator.weapon"
      :attack-result="damageCalculator.attackResult"
      @damage-calculated="applyDamage"
      @cancel="closeDamageCalculator"
    />

    <SpellCaster
      v-if="showSpellCaster"
      :caster="spellCaster.caster"
      :available-spells="spellCaster.availableSpells"
      :targets="spellCaster.availableTargets"
      @spell-cast="handleSpellCast"
      @cancel="closeSpellCaster"
    />
  </div>
</template>

<script>
import { combatRules } from '@/utils/combatRules'
import { diceRoller } from '@/utils/diceRoller'
import websocketService from '@/services/websocket'

export default {
  name: 'CombatMain',
  props: {
    forced: {
      type: Boolean,
      default: false
    },
    combatData: {
      type: Object,
      required: true
    },
    isKP: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      combatId: null,
      participants: [],
      battlefield: null,
      currentRound: 1,
      currentTurn: 0,
      selectedParticipant: null,
      targetParticipant: null,
      combatStarted: false,
      isPaused: false,
      showLog: false,
      showFullDetails: false,
      combatLog: [],
      
      // 弹窗状态
      showDamageCalculator: false,
      damageCalculator: {
        attacker: null,
        defender: null,
        weapon: null,
        attackResult: null
      },
      showSpellCaster: false,
      spellCaster: {
        caster: null,
        availableSpells: [],
        availableTargets: []
      },
      
      // 当前行动状态
      currentAction: null,
      actionStep: 'select' // select, target, resolve
    }
  },
  
  computed: {
    currentTurnInfo() {
      if (this.participants.length === 0) return ''
      const current = this.participants[this.currentTurn]
      return current ? `${current.name} 的回合` : ''
    },
    
    canAct() {
      if (!this.selectedParticipant) return false
      const currentParticipant = this.participants[this.currentTurn]
      return this.selectedParticipant.id === currentParticipant?.id && !currentParticipant.hasActed
    },
    
    availableActions() {
      if (!this.selectedParticipant) return []
      return combatRules.getAvailableActions(this.selectedParticipant)
    },
    
    hpPercentage() {
      if (!this.selectedParticipant) return 0
      return (this.selectedParticipant.currentHp / this.selectedParticipant.maxHp) * 100
    },
    
    sanPercentage() {
      if (!this.selectedParticipant) return 0
      return (this.selectedParticipant.currentSan / this.selectedParticipant.maxSan) * 100
    },
    
    gridSize() {
      return this.battlefield?.gridSize || { width: 15, height: 12 }
    }
  },
  
  mounted() {
    this.initializeCombat()
    this.setupWebSocketListeners()
  },
  
  beforeUnmount() {
    this.removeWebSocketListeners()
  },
  
  methods: {
    initializeCombat() {
      this.combatId = this.combatData.id
      this.participants = this.combatData.participants
      this.battlefield = this.combatData.battlefield
      this.currentRound = this.combatData.currentRound || 1
      this.currentTurn = this.combatData.currentTurn || 0
    },
    
    // WebSocket 事件处理
    setupWebSocketListeners() {
      websocketService.on('combat_start', this.handleCombatStart)
      websocketService.on('combat_end', this.handleCombatEnd)
      websocketService.on('initiative_order', this.handleInitiativeOrder)
      websocketService.on('turn_start', this.handleTurnStart)
      websocketService.on('combat_action', this.handleCombatAction)
      websocketService.on('damage_apply', this.handleDamageApply)
      websocketService.on('status_update', this.handleStatusUpdate)
      websocketService.on('combat_log', this.handleCombatLog)
    },
    
    removeWebSocketListeners() {
      websocketService.off('combat_start', this.handleCombatStart)
      websocketService.off('combat_end', this.handleCombatEnd)
      websocketService.off('initiative_order', this.handleInitiativeOrder)
      websocketService.off('turn_start', this.handleTurnStart)
      websocketService.off('combat_action', this.handleCombatAction)
      websocketService.off('damage_apply', this.handleDamageApply)
      websocketService.off('status_update', this.handleStatusUpdate)
      websocketService.off('combat_log', this.handleCombatLog)
    },
    
    // 战斗控制方法
    rollInitiative() {
      if (this.isKP) {
        websocketService.send({
          type: 'INITIATIVE_ROLL',
          data: { combatId: this.combatId }
        })
      }
    },
    
    nextTurn() {
      if (this.isKP) {
        websocketService.send({
          type: 'TURN_END',
          data: { combatId: this.combatId }
        })
      }
    },
    
    pauseCombat() {
      if (this.isKP) {
        websocketService.send({
          type: this.isPaused ? 'COMBAT_RESUME' : 'COMBAT_PAUSE',
          data: { combatId: this.combatId }
        })
        this.isPaused = !this.isPaused
      }
    },
    
    endCombat() {
      if (this.isKP) {
        websocketService.send({
          type: 'COMBAT_END',
          data: { combatId: this.combatId }
        })
      }
    },
    
    // 行动处理方法
    handleAction(action) {
      this.currentAction = action
      
      switch (action.id) {
        case 'attack':
          this.actionStep = 'target'
          this.addCombatLogEntry(`${this.selectedParticipant.name} 准备攻击，请选择目标`)
          break
        case 'move':
          this.actionStep = 'target'
          this.addCombatLogEntry(`${this.selectedParticipant.name} 准备移动，请选择目标位置`)
          break
        case 'defend':
          this.executeDefendAction()
          break
        case 'aim':
          this.executeAimAction()
          break
        case 'cast':
          this.openSpellCaster()
          break
        case 'item':
          this.openItemMenu()
          break
      }
    },
    
    executeDefendAction() {
      const participant = this.selectedParticipant
      participant.isDefending = true
      participant.defenseType = 'dodge' // 默认闪避
      participant.hasActed = true
      
      websocketService.send({
        type: 'ACTION_EXECUTE',
        data: {
          combatId: this.combatId,
          actorId: participant.id,
          action: 'defend',
          actionData: { type: 'dodge' }
        }
      })
      
      this.addCombatLogEntry(`${participant.name} 进入防御姿态`)
      this.nextTurn()
    },
    
    executeAimAction() {
      const participant = this.selectedParticipant
      participant.isAiming = true
      participant.hasActed = true
      
      websocketService.send({
        type: 'ACTION_EXECUTE',
        data: {
          combatId: this.combatId,
          actorId: participant.id,
          action: 'aim',
          actionData: {}
        }
      })
      
      this.addCombatLogEntry(`${participant.name} 进行瞄准，下轮攻击将获得奖励`)
      this.nextTurn()
    },
    
    // 其他方法...
    selectParticipant(participant) {
      this.selectedParticipant = participant
    },
    
    handlePositionClick(position) {
      if (this.currentAction?.id === 'move') {
        this.executeMoveAction(position)
      }
    },
    
    handleAttackTarget(target) {
      if (this.currentAction?.id === 'attack') {
        this.executeAttackAction(target)
      }
    },
    
    addCombatLogEntry(message, type = 'action') {
      const entry = {
        id: Date.now(),
        round: this.currentRound,
        turn: this.currentTurn,
        message,
        type,
        timestamp: new Date()
      }
      this.combatLog.unshift(entry)
      
      // 限制日志条目数量
      if (this.combatLog.length > 100) {
        this.combatLog = this.combatLog.slice(0, 100)
      }
    },
    
    toggleCombatLog() {
      this.showLog = !this.showLog
    },
    
    clearCombatLog() {
      this.combatLog = []
    }
  }
}
</script>
```

### 4.2 角色卡片组件

```vue
<!-- CharacterCard.vue -->
<template>
  <div 
    class="character-card"
    :class="cardClasses"
    :style="cardStyle"
    @click="$emit('card-clicked', participant)"
    @contextmenu.prevent="showContextMenu"
  >
    <!-- 角色头像 -->
    <div class="card-avatar">
      <img 
        :src="participant.avatar || defaultAvatar" 
        :alt="participant.name"
        class="avatar-image"
      />
      <div class="status-overlay" v-if="hasStatusEffects">
        <i 
          v-for="effect in visibleStatusEffects" 
          :key="effect.id"
          :class="effect.icon"
          :title="effect.name"
          class="status-icon"
        ></i>
      </div>
    </div>

    <!-- 角色名称 -->
    <div class="card-name">
      <span class="name-text">{{ participant.name }}</span>
      <span class="level-text" v-if="participant.level">Lv.{{ participant.level }}</span>
    </div>

    <!-- 生命值条 -->
    <div class="health-bar">
      <div class="bar-background">
        <div 
          class="bar-fill health-fill" 
          :style="{ width: healthPercentage + '%' }"
        ></div>
      </div>
      <span class="bar-text">{{ participant.currentHp }}/{{ participant.maxHp }}</span>
    </div>

    <!-- 理智值条 -->
    <div class="sanity-bar" v-if="participant.isPlayer">
      <div class="bar-background">
        <div 
          class="bar-fill sanity-fill" 
          :style="{ width: sanityPercentage + '%' }"
        ></div>
      </div>
      <span class="bar-text">{{ participant.currentSan }}/{{ participant.maxSan }}</span>
    </div>

    <!-- 魔法值条 -->
    <div class="magic-bar" v-if="participant.maxMp > 0">
      <div class="bar-background">
        <div 
          class="bar-fill magic-fill" 
          :style="{ width: magicPercentage + '%' }"
        ></div>
      </div>
      <span class="bar-text">{{ participant.currentMp }}/{{ participant.maxMp }}</span>
    </div>

    <!-- 行动状态指示器 -->
    <div class="action-indicator" :class="actionStatus">
      <i :class="actionIcon"></i>
    </div>

    <!-- 武器显示 -->
    <div class="weapon-display" v-if="participant.equipment.mainHand">
      <i :class="weaponIcon" :title="weaponName"></i>
    </div>

    <!-- 护甲显示 -->
    <div class="armor-display" v-if="participant.equipment.armor">
      <span class="armor-value">{{ participant.equipment.armor.protection }}</span>
      <i class="fas fa-shield-alt"></i>
    </div>

    <!-- 悬停详情 -->
    <div class="card-tooltip" v-if="showTooltip">
      <div class="tooltip-section">
        <h4>基础属性</h4>
        <div class="attributes-grid">
          <div class="attr-item" v-for="(value, key) in participant.attributes" :key="key">
            <span class="attr-name">{{ getAttributeName(key) }}</span>
            <span class="attr-value">{{ value }}</span>
          </div>
        </div>
      </div>
      
      <div class="tooltip-section" v-if="participant.equipment">
        <h4>装备</h4>
        <div class="equipment-list">
          <div v-if="participant.equipment.mainHand" class="equipment-item">
            <span>主手: {{ participant.equipment.mainHand.name }}</span>
          </div>
          <div v-if="participant.equipment.armor" class="equipment-item">
            <span>护甲: {{ participant.equipment.armor.name }} ({{ participant.equipment.armor.protection }})</span>
          </div>
        </div>
      </div>
      
      <div class="tooltip-section" v-if="participant.statusEffects.length > 0">
        <h4>状态效果</h4>
        <div class="status-list">
          <div v-for="effect in participant.statusEffects" :key="effect.id" class="status-item">
            <i :class="effect.icon"></i>
            <span>{{ effect.name }}</span>
            <span class="duration" v-if="effect.duration">({{ effect.duration }}轮)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 攻击动画 -->
    <AttackAnimation 
      v-if="showAttackAnimation"
      :attacker="participant"
      :target="attackTarget"
      :weapon="attackWeapon"
      @animation-complete="onAttackAnimationComplete"
    />

    <!-- 受伤动画 -->
    <DamageAnimation
      v-if="showDamageAnimation"
      :damage="lastDamage"
      :damage-type="lastDamageType"
      @animation-complete="onDamageAnimationComplete"
    />
  </div>
</template>

<script>
export default {
  name: 'CharacterCard',
  props: {
    participant: {
      type: Object,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    },
    isCurrentTurn: {
      type: Boolean,
      default: false
    },
    showTooltip: {
      type: Boolean,
      default: false
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    }
  },
  
  data() {
    return {
      showAttackAnimation: false,
      showDamageAnimation: false,
      attackTarget: null,
      attackWeapon: null,
      lastDamage: 0,
      lastDamageType: 'physical'
    }
  },
  
  computed: {
    cardClasses() {
      return {
        'selected': this.isSelected,
        'current-turn': this.isCurrentTurn,
        'has-acted': this.participant.hasActed,
        'is-player': this.participant.isPlayer,
        'is-npc': !this.participant.isPlayer,
        'is-dead': this.participant.currentHp <= 0,
        'is-unconscious': this.participant.currentHp === 0,
        'is-injured': this.participant.currentHp < this.participant.maxHp * 0.5,
        'is-heavily-injured': this.participant.currentHp < this.participant.maxHp * 0.25,
        'is-insane': this.participant.currentSan === 0,
        'is-panicked': this.participant.currentSan < this.participant.maxSan * 0.25
      }
    },
    
    cardStyle() {
      return {
        transform: `translate(${this.position.x * 60}px, ${this.position.y * 60}px)`,
        zIndex: this.isSelected ? 100 : 10
      }
    },
    
    healthPercentage() {
      return Math.max(0, (this.participant.currentHp / this.participant.maxHp) * 100)
    },
    
    sanityPercentage() {
      return Math.max(0, (this.participant.currentSan / this.participant.maxSan) * 100)
    },
    
    magicPercentage() {
      return Math.max(0, (this.participant.currentMp / this.participant.maxMp) * 100)
    },
    
    hasStatusEffects() {
      return this.participant.statusEffects && this.participant.statusEffects.length > 0
    },
    
    visibleStatusEffects() {
      return this.participant.statusEffects?.slice(0, 3) || []
    },
    
    actionStatus() {
      if (this.participant.currentHp <= 0) return 'dead'
      if (this.participant.hasActed) return 'acted'
      if (this.isCurrentTurn) return 'current'
      return 'waiting'
    },
    
    actionIcon() {
      switch (this.actionStatus) {
        case 'dead': return 'fas fa-skull'
        case 'acted': return 'fas fa-check'
        case 'current': return 'fas fa-play'
        default: return 'fas fa-clock'
      }
    },
    
    weaponIcon() {
      const weapon = this.participant.equipment.mainHand
      if (!weapon) return 'fas fa-fist-raised'
      
      switch (weapon.type) {
        case 'sword': return 'fas fa-sword'
        case 'axe': return 'fas fa-axe'
        case 'pistol': return 'fas fa-gun'
        case 'rifle': return 'fas fa-rifle'
        case 'bow': return 'fas fa-bow-arrow'
        default: return 'fas fa-weapon'
      }
    },
    
    weaponName() {
      return this.participant.equipment.mainHand?.name || '拳头'
    },
    
    defaultAvatar() {
      return this.participant.isPlayer ? '/images/default-player.png' : '/images/default-npc.png'
    }
  },
  
  methods: {
    getAttributeName(key) {
      const names = {
        str: '力量',
        con: '体质',
        siz: '体型',
        dex: '敏捷',
        app: '外貌',
        int: '智力',
        pow: '意志',
        edu: '教育'
      }
      return names[key] || key
    },
    
    showContextMenu(event) {
      this.$emit('context-menu', {
        participant: this.participant,
        x: event.clientX,
        y: event.clientY
      })
    },
    
    playAttackAnimation(target, weapon) {
      this.attackTarget = target
      this.attackWeapon = weapon
      this.showAttackAnimation = true
    },
    
    playDamageAnimation(damage, damageType = 'physical') {
      this.lastDamage = damage
      this.lastDamageType = damageType
      this.showDamageAnimation = true
    },
    
    onAttackAnimationComplete() {
      this.showAttackAnimation = false
      this.attackTarget = null
      this.attackWeapon = null
    },
    
    onDamageAnimationComplete() {
      this.showDamageAnimation = false
    }
  }
}
</script>
```### 4.3 战场网格
组件

```vue
<!-- CombatField.vue -->
<template>
  <div class="combat-field" ref="combatField">
    <!-- 网格背景 -->
    <div class="grid-background">
      <div 
        v-for="(row, y) in battlefield.grid" 
        :key="`row-${y}`"
        class="grid-row"
      >
        <div 
          v-for="(cell, x) in row" 
          :key="`cell-${x}-${y}`"
          class="grid-cell"
          :class="getCellClasses(cell)"
          :style="getCellStyle(cell)"
          @click="handleCellClick(cell)"
          @mouseover="handleCellHover(cell)"
          @mouseleave="handleCellLeave(cell)"
        >
          <!-- 地形标记 -->
          <div class="terrain-marker" v-if="cell.terrain !== 'normal'">
            <i :class="getTerrainIcon(cell.terrain)"></i>
          </div>
          
          <!-- 掩护标记 -->
          <div class="cover-marker" v-if="cell.cover > 0">
            <span class="cover-value">{{ cell.cover }}</span>
          </div>
          
          <!-- 危险区域标记 -->
          <div class="hazard-marker" v-if="cell.hazard">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          
          <!-- 网格坐标 -->
          <div class="grid-coordinates" v-if="showCoordinates">
            {{ x }},{{ y }}
          </div>
        </div>
      </div>
    </div>

    <!-- 角色卡片层 -->
    <div class="characters-layer">
      <CharacterCard
        v-for="participant in participants"
        :key="participant.id"
        :participant="participant"
        :position="participant.position"
        :is-selected="selectedParticipant?.id === participant.id"
        :is-current-turn="currentTurnParticipant?.id === participant.id"
        :show-tooltip="hoveredParticipant?.id === participant.id"
        @card-clicked="handleParticipantClick"
        @context-menu="handleParticipantContextMenu"
      />
    </div>

    <!-- 移动路径显示 -->
    <div class="movement-layer" v-if="showMovementPath">
      <svg class="movement-path" :width="fieldWidth" :height="fieldHeight">
        <path
          :d="movementPathData"
          stroke="#00ff00"
          stroke-width="3"
          fill="none"
          stroke-dasharray="5,5"
        />
      </svg>
    </div>

    <!-- 攻击范围显示 -->
    <div class="range-layer" v-if="showAttackRange">
      <div 
        v-for="cell in attackRangeCells" 
        :key="`range-${cell.x}-${cell.y}`"
        class="range-indicator"
        :style="getRangeIndicatorStyle(cell)"
        :class="getRangeIndicatorClass(cell)"
      ></div>
    </div>

    <!-- 视线指示器 -->
    <div class="line-of-sight-layer" v-if="showLineOfSight">
      <svg class="los-lines" :width="fieldWidth" :height="fieldHeight">
        <line
          v-for="line in losLines"
          :key="`los-${line.id}`"
          :x1="line.x1"
          :y1="line.y1"
          :x2="line.x2"
          :y2="line.y2"
          :stroke="line.blocked ? '#ff0000' : '#00ff00'"
          stroke-width="2"
          stroke-dasharray="3,3"
        />
      </svg>
    </div>

    <!-- 特效层 -->
    <div class="effects-layer">
      <!-- 爆炸效果 -->
      <div 
        v-for="explosion in activeExplosions" 
        :key="explosion.id"
        class="explosion-effect"
        :style="getExplosionStyle(explosion)"
      >
        <div class="explosion-ring"></div>
      </div>
      
      <!-- 法术效果 -->
      <div 
        v-for="spell in activeSpellEffects" 
        :key="spell.id"
        class="spell-effect"
        :style="getSpellEffectStyle(spell)"
        :class="spell.type"
      >
        <div class="spell-animation"></div>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="field-toolbar" v-if="isKP">
      <button @click="toggleCoordinates" class="toolbar-btn">
        <i class="fas fa-map-marker-alt"></i>
        坐标
      </button>
      <button @click="toggleGrid" class="toolbar-btn">
        <i class="fas fa-th"></i>
        网格
      </button>
      <button @click="addTerrain" class="toolbar-btn">
        <i class="fas fa-mountain"></i>
        地形
      </button>
      <button @click="addHazard" class="toolbar-btn">
        <i class="fas fa-fire"></i>
        危险
      </button>
      <button @click="clearField" class="toolbar-btn danger">
        <i class="fas fa-trash"></i>
        清空
      </button>
    </div>

    <!-- 右键菜单 -->
    <div 
      v-if="showContextMenu" 
      class="context-menu"
      :style="contextMenuStyle"
      @click.stop
    >
      <div class="menu-item" @click="moveHere" v-if="canMoveToCell">
        <i class="fas fa-walking"></i>
        移动到此处
      </div>
      <div class="menu-item" @click="attackHere" v-if="canAttackCell">
        <i class="fas fa-crosshairs"></i>
        攻击此处
      </div>
      <div class="menu-item" @click="castSpellHere" v-if="canCastSpell">
        <i class="fas fa-magic"></i>
        施法
      </div>
      <div class="menu-separator"></div>
      <div class="menu-item" @click="inspectCell">
        <i class="fas fa-search"></i>
        检查
      </div>
    </div>
  </div>
</template>

<script>
import { battlefieldUtils } from '@/utils/battlefieldUtils'
import { combatRules } from '@/utils/combatRules'

export default {
  name: 'CombatField',
  props: {
    participants: {
      type: Array,
      required: true
    },
    selectedParticipant: {
      type: Object,
      default: null
    },
    currentTurn: {
      type: Number,
      default: 0
    },
    battlefield: {
      type: Object,
      required: true
    },
    gridSize: {
      type: Object,
      default: () => ({ width: 15, height: 12 })
    },
    isKP: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      cellSize: 60, // 每格像素大小
      showCoordinates: false,
      showGrid: true,
      hoveredCell: null,
      hoveredParticipant: null,
      showContextMenu: false,
      contextMenuPosition: { x: 0, y: 0 },
      contextMenuCell: null,
      
      // 显示状态
      showMovementPath: false,
      showAttackRange: false,
      showLineOfSight: false,
      
      // 路径和范围数据
      movementPath: [],
      attackRangeCells: [],
      losLines: [],
      
      // 特效
      activeExplosions: [],
      activeSpellEffects: [],
      
      // 当前行动
      currentAction: null,
      actionTargets: []
    }
  },
  
  computed: {
    fieldWidth() {
      return this.gridSize.width * this.cellSize
    },
    
    fieldHeight() {
      return this.gridSize.height * this.cellSize
    },
    
    currentTurnParticipant() {
      return this.participants[this.currentTurn]
    },
    
    movementPathData() {
      if (this.movementPath.length < 2) return ''
      
      let path = `M ${this.movementPath[0].x * this.cellSize + this.cellSize/2} ${this.movementPath[0].y * this.cellSize + this.cellSize/2}`
      
      for (let i = 1; i < this.movementPath.length; i++) {
        const point = this.movementPath[i]
        path += ` L ${point.x * this.cellSize + this.cellSize/2} ${point.y * this.cellSize + this.cellSize/2}`
      }
      
      return path
    },
    
    contextMenuStyle() {
      return {
        left: this.contextMenuPosition.x + 'px',
        top: this.contextMenuPosition.y + 'px',
        display: this.showContextMenu ? 'block' : 'none'
      }
    },
    
    canMoveToCell() {
      return this.selectedParticipant && 
             this.contextMenuCell && 
             this.currentAction?.type === 'move'
    },
    
    canAttackCell() {
      return this.selectedParticipant && 
             this.contextMenuCell && 
             this.currentAction?.type === 'attack'
    },
    
    canCastSpell() {
      return this.selectedParticipant && 
             this.contextMenuCell && 
             this.selectedParticipant.spells?.length > 0
    }
  },
  
  mounted() {
    this.initializeField()
    document.addEventListener('click', this.hideContextMenu)
  },
  
  beforeUnmount() {
    document.removeEventListener('click', this.hideContextMenu)
  },
  
  methods: {
    initializeField() {
      // 初始化战场
      this.updateParticipantPositions()
    },
    
    updateParticipantPositions() {
      // 更新参与者位置
      this.participants.forEach(participant => {
        if (!participant.position) {
          participant.position = this.findEmptyPosition()
        }
      })
    },
    
    findEmptyPosition() {
      // 寻找空闲位置
      for (let y = 0; y < this.gridSize.height; y++) {
        for (let x = 0; x < this.gridSize.width; x++) {
          if (!this.isPositionOccupied(x, y)) {
            return { x, y }
          }
        }
      }
      return { x: 0, y: 0 }
    },
    
    isPositionOccupied(x, y) {
      return this.participants.some(p => p.position.x === x && p.position.y === y)
    },
    
    // 网格相关方法
    getCellClasses(cell) {
      return {
        'grid-cell': true,
        'hovered': this.hoveredCell === cell,
        'occupied': cell.occupant,
        'terrain-difficult': cell.terrain === 'difficult',
        'terrain-impassable': cell.terrain === 'impassable',
        'has-cover': cell.cover > 0,
        'has-hazard': cell.hazard,
        'in-range': this.isCellInRange(cell),
        'valid-target': this.isValidTarget(cell),
        'movement-path': this.isInMovementPath(cell)
      }
    },
    
    getCellStyle(cell) {
      const style = {
        width: this.cellSize + 'px',
        height: this.cellSize + 'px'
      }
      
      if (cell.elevation !== 0) {
        style.boxShadow = `0 ${cell.elevation * 2}px ${cell.elevation * 4}px rgba(0,0,0,0.3)`
      }
      
      return style
    },
    
    getTerrainIcon(terrain) {
      const icons = {
        difficult: 'fas fa-mountain',
        impassable: 'fas fa-ban',
        water: 'fas fa-water',
        fire: 'fas fa-fire',
        ice: 'fas fa-snowflake'
      }
      return icons[terrain] || 'fas fa-question'
    },
    
    // 事件处理
    handleCellClick(cell) {
      if (this.currentAction) {
        this.executeActionOnCell(cell)
      } else {
        this.$emit('position-clicked', cell)
      }
    },
    
    handleCellHover(cell) {
      this.hoveredCell = cell
      
      if (this.selectedParticipant && this.currentAction) {
        this.updateActionPreview(cell)
      }
    },
    
    handleCellLeave(cell) {
      if (this.hoveredCell === cell) {
        this.hoveredCell = null
      }
    },
    
    handleParticipantClick(participant) {
      this.$emit('participant-clicked', participant)
    },
    
    handleParticipantContextMenu(event) {
      this.showParticipantContextMenu(event.participant, event.x, event.y)
    },
    
    // 右键菜单
    showContextMenu(cell, x, y) {
      this.contextMenuCell = cell
      this.contextMenuPosition = { x, y }
      this.showContextMenu = true
    },
    
    hideContextMenu() {
      this.showContextMenu = false
      this.contextMenuCell = null
    },
    
    // 行动执行
    executeActionOnCell(cell) {
      switch (this.currentAction.type) {
        case 'move':
          this.executeMoveAction(cell)
          break
        case 'attack':
          this.executeAttackAction(cell)
          break
        case 'spell':
          this.executeSpellAction(cell)
          break
      }
    },
    
    executeMoveAction(targetCell) {
      const participant = this.selectedParticipant
      const path = battlefieldUtils.findPath(
        participant.position, 
        { x: targetCell.x, y: targetCell.y },
        this.battlefield
      )
      
      if (path && path.length <= participant.moveRate) {
        this.$emit('move-action', {
          participant,
          path,
          targetPosition: { x: targetCell.x, y: targetCell.y }
        })
      }
    },
    
    executeAttackAction(targetCell) {
      const target = this.getParticipantAtPosition(targetCell.x, targetCell.y)
      if (target) {
        this.$emit('attack-target', {
          attacker: this.selectedParticipant,
          target,
          position: { x: targetCell.x, y: targetCell.y }
        })
      }
    },
    
    // 范围和路径显示
    showMovementRange(participant) {
      this.showMovementPath = true
      this.movementPath = battlefieldUtils.getMovementRange(
        participant.position,
        participant.moveRate,
        this.battlefield
      )
    },
    
    showWeaponRange(participant, weapon) {
      this.showAttackRange = true
      this.attackRangeCells = battlefieldUtils.getWeaponRange(
        participant.position,
        weapon,
        this.battlefield
      )
    },
    
    updateActionPreview(cell) {
      if (this.currentAction.type === 'move') {
        this.previewMovement(cell)
      } else if (this.currentAction.type === 'attack') {
        this.previewAttack(cell)
      }
    },
    
    previewMovement(targetCell) {
      const path = battlefieldUtils.findPath(
        this.selectedParticipant.position,
        { x: targetCell.x, y: targetCell.y },
        this.battlefield
      )
      this.movementPath = path || []
    },
    
    previewAttack(targetCell) {
      const los = battlefieldUtils.checkLineOfSight(
        this.selectedParticipant.position,
        { x: targetCell.x, y: targetCell.y },
        this.battlefield
      )
      
      this.losLines = [{
        id: 'preview',
        x1: this.selectedParticipant.position.x * this.cellSize + this.cellSize/2,
        y1: this.selectedParticipant.position.y * this.cellSize + this.cellSize/2,
        x2: targetCell.x * this.cellSize + this.cellSize/2,
        y2: targetCell.y * this.cellSize + this.cellSize/2,
        blocked: !los.clear
      }]
    },
    
    // 特效方法
    playExplosionEffect(position, radius = 1) {
      const explosion = {
        id: Date.now(),
        x: position.x * this.cellSize,
        y: position.y * this.cellSize,
        radius: radius * this.cellSize,
        duration: 1000
      }
      
      this.activeExplosions.push(explosion)
      
      setTimeout(() => {
        const index = this.activeExplosions.findIndex(e => e.id === explosion.id)
        if (index !== -1) {
          this.activeExplosions.splice(index, 1)
        }
      }, explosion.duration)
    },
    
    playSpellEffect(position, spellType, duration = 2000) {
      const effect = {
        id: Date.now(),
        x: position.x * this.cellSize,
        y: position.y * this.cellSize,
        type: spellType,
        duration
      }
      
      this.activeSpellEffects.push(effect)
      
      setTimeout(() => {
        const index = this.activeSpellEffects.findIndex(e => e.id === effect.id)
        if (index !== -1) {
          this.activeSpellEffects.splice(index, 1)
        }
      }, duration)
    },
    
    // 工具栏方法
    toggleCoordinates() {
      this.showCoordinates = !this.showCoordinates
    },
    
    toggleGrid() {
      this.showGrid = !this.showGrid
    },
    
    addTerrain() {
      // 打开地形编辑器
      this.$emit('open-terrain-editor')
    },
    
    addHazard() {
      // 打开危险区域编辑器
      this.$emit('open-hazard-editor')
    },
    
    clearField() {
      if (confirm('确定要清空战场吗？')) {
        this.$emit('clear-battlefield')
      }
    },
    
    // 辅助方法
    getParticipantAtPosition(x, y) {
      return this.participants.find(p => p.position.x === x && p.position.y === y)
    },
    
    isCellInRange(cell) {
      return this.attackRangeCells.some(c => c.x === cell.x && c.y === cell.y)
    },
    
    isValidTarget(cell) {
      if (!this.selectedParticipant || !this.currentAction) return false
      
      if (this.currentAction.type === 'attack') {
        const target = this.getParticipantAtPosition(cell.x, cell.y)
        return target && target.id !== this.selectedParticipant.id
      }
      
      return false
    },
    
    isInMovementPath(cell) {
      return this.movementPath.some(p => p.x === cell.x && p.y === cell.y)
    },
    
    getRangeIndicatorStyle(cell) {
      return {
        left: cell.x * this.cellSize + 'px',
        top: cell.y * this.cellSize + 'px',
        width: this.cellSize + 'px',
        height: this.cellSize + 'px'
      }
    },
    
    getRangeIndicatorClass(cell) {
      const distance = battlefieldUtils.getDistance(
        this.selectedParticipant.position,
        { x: cell.x, y: cell.y }
      )
      
      if (distance <= 3) return 'close-range'
      if (distance <= 6) return 'medium-range'
      return 'long-range'
    },
    
    getExplosionStyle(explosion) {
      return {
        left: explosion.x + 'px',
        top: explosion.y + 'px',
        width: explosion.radius * 2 + 'px',
        height: explosion.radius * 2 + 'px'
      }
    },
    
    getSpellEffectStyle(effect) {
      return {
        left: effect.x + 'px',
        top: effect.y + 'px'
      }
    }
  }
}
</script>
```

## 第五部分：实现优先级和开发计划

### 5.1 开发阶段划分

#### 第一阶段: 核心战斗系统 (2-3周)
1. **基础框架搭建**
   - 创建战斗主界面组件
   - 实现基础的WebSocket通信
   - 建立数据库表结构

2. **角色卡片系统**
   - 角色卡片显示组件
   - 基础属性展示
   - 生命值/理智值/魔法值显示

3. **简单攻击系统**
   - 基础近战攻击
   - 简单伤害计算
   - 生命值扣除

4. **先攻系统**
   - 敏捷检定
   - 行动顺序排列
   - 回合管理

#### 第二阶段: 完整规则实现 (3-4周)
1. **完整武器系统**
   - 所有武器类型实现
   - 远程武器和弹药系统
   - 武器特殊效果

2. **防御机制**
   - 闪避系统
   - 格挡系统
   - 护甲减伤

3. **特殊攻击**
   - 瞄准攻击
   - 部位攻击
   - 全自动射击

4. **状态效果系统**
   - 各种状态效果
   - 持续时间管理
   - 状态叠加规则

#### 第三阶段: 高级功能 (2-3周)
1. **魔法系统**
   - 法术施放界面
   - 魔法点数消耗
   - 法术效果实现

2. **2D视觉效果**
   - 攻击动画
   - 特效系统
   - 战场环境

3. **AI对手系统**
   - 基础AI决策
   - 不同AI等级
   - 怪物行为模式

4. **团队战斗**
   - 多人协作机制
   - 包围攻击
   - 友军误伤

#### 第四阶段: 优化和扩展 (1-2周)
1. **性能优化**
   - 动画性能优化
   - 网络通信优化
   - 内存使用优化

2. **用户体验**
   - 界面美化
   - 音效添加
   - 操作优化

3. **扩展功能**
   - 战斗回放
   - 自定义规则
   - 战斗统计

### 5.2 技术难点和解决方案

#### 难点1: 实时多人同步
**解决方案**: 
- 使用WebSocket确保实时通信
- 实现乐观锁机制防止冲突
- 添加断线重连和状态恢复

#### 难点2: 复杂的战斗规则
**解决方案**:
- 创建独立的规则引擎模块
- 使用配置文件管理规则参数
- 实现规则验证和错误处理

#### 难点3: 2D动画性能
**解决方案**:
- 使用CSS3动画和GPU加速
- 实现动画队列管理
- 添加动画开关选项

#### 难点4: 数据一致性
**解决方案**:
- 实现数据版本控制
- 添加数据校验机制
- 使用事务确保数据完整性

### 5.3 测试计划

#### 单元测试
- 战斗规则计算测试
- 伤害计算测试
- 状态效果测试

#### 集成测试
- WebSocket通信测试
- 数据库操作测试
- 组件交互测试

#### 用户测试
- 多人战斗测试
- 性能压力测试
- 用户体验测试

### 5.4 部署和维护

#### 部署要求
- Node.js 16+
- MySQL 8.0+
- Redis (用于会话管理)
- Nginx (反向代理)

#### 监控和日志
- 战斗日志记录
- 性能监控
- 错误追踪

#### 维护计划
- 定期数据备份
- 性能优化
- 功能更新

## 结论

这个完整的COC 7版2D战斗系统严格遵循规则书的所有机制，提供了丰富的战斗体验和完善的多人协作功能。通过分阶段的开发计划，可以确保系统的稳定性和可扩展性，为玩家提供真正符合COC规则的战斗体验。

系统的核心优势：
1. **规则准确性** - 严格按照COC 7版规则书实现
2. **多人协作** - 支持实时多人战斗
3. **视觉效果** - 2D卡牌式战斗界面
4. **强制模式** - KP可控制的强制战斗模式
5. **数据同步** - 与角色卡和数据库完全同步
6. **扩展性** - 模块化设计，易于扩展新功能

通过这个系统，玩家可以体验到完整、准确、有趣的COC战斗，KP也能够轻松管理复杂的战斗场景。
#
## 1.10 道具和装备系统 (与背包系统对接)

#### 背包道具使用
- **治疗道具**: 急救包、药品等，需要急救或医学检定
- **工具道具**: 撬锁工具、绳索等，提供技能加值或特殊功能
- **消耗品**: 手电筒电池、弹药等，有使用次数限制
- **特殊道具**: 护身符、神秘物品等，提供特殊效果或属性加值

#### 武器切换系统
- **主手武器**: 当前装备的主要武器，可直接使用
- **副手武器**: 可快速切换的备用武器(如匕首、手枪)
- **收纳武器**: 背包中的其他武器，需要花费一个行动轮装备
- **临时武器**: 现场拾取的物品作为武器使用(椅子、酒瓶等)

#### 弹药管理系统
- **弹药计数**: 精确追踪每种武器的剩余弹药数量
- **装填时间**: 
  - 手枪/步枪: 装填2发需要1轮
  - 换弹夹: 需要1轮
  - 机枪弹链: 需要2轮
- **装填射击**: 同轮装填1发并射击，承受惩罚骰
- **弹药类型**: 普通弹、特殊弹药等不同效果

### 1.11 KP专属界面设计

#### KP战斗控制面板
```vue
<template>
  <div class="kp-combat-panel" v-if="isKP">
    <!-- 战斗触发区域 -->
    <div class="combat-trigger-section">
      <h3>战斗控制</h3>
      <button @click="triggerCombat" class="trigger-combat-btn">
        ⚔️ 触发战斗模式
      </button>
      <button @click="endCombat" class="end-combat-btn" v-if="combatActive">
        🛑 结束战斗
      </button>
    </div>

    <!-- 参与者选择 -->
    <div class="participants-section">
      <h4>选择参战角色</h4>
      <div class="character-selection">
        <div v-for="character in availableCharacters" :key="character.id">
          <input type="checkbox" v-model="selectedParticipants" :value="character.id">
          <label>{{ character.name }} ({{ character.isPlayer ? '玩家' : 'NPC' }})</label>
        </div>
      </div>
    </div>

    <!-- 怪物添加 -->
    <div class="monster-section">
      <h4>添加怪物/NPC</h4>
      <select v-model="selectedMonster">
        <option v-for="monster in monsterLibrary" :key="monster.id" :value="monster">
          {{ monster.name }}
        </option>
      </select>
      <button @click="addMonster">添加到战斗</button>
    </div>

    <!-- 战场设置 -->
    <div class="battlefield-settings">
      <h4>战场设置</h4>
      <label>战场大小:</label>
      <select v-model="battlefieldSize">
        <option value="small">小型 (10x8)</option>
        <option value="medium">中型 (15x12)</option>
        <option value="large">大型 (20x15)</option>
      </select>
      
      <label>环境类型:</label>
      <select v-model="environment">
        <option value="indoor">室内</option>
        <option value="outdoor">室外</option>
        <option value="urban">城市</option>
        <option value="wilderness">荒野</option>
      </select>
    </div>

    <!-- 战斗中控制 -->
    <div class="combat-controls" v-if="combatActive">
      <h4>战斗控制</h4>
      <button @click="nextTurn">下一回合</button>
      <button @click="pauseCombat">{{ isPaused ? '继续' : '暂停' }}</button>
      <button @click="rollInitiative">重新投先攻</button>
      
      <!-- 快速行动 -->
      <div class="quick-actions">
        <h5>快速行动</h5>
        <button @click="quickHeal">快速治疗</button>
        <button @click="addStatusEffect">添加状态</button>
        <button @click="environmentalDamage">环境伤害</button>
      </div>
    </div>
  </div>
</template>
```

#### 玩家强制战斗界面
```vue
<template>
  <div class="forced-combat-overlay" v-if="isForcedCombat">
    <!-- 全屏战斗提示 -->
    <div class="combat-notification-overlay">
      <div class="combat-notification">
        <div class="notification-icon">⚔️</div>
        <h1>触发战斗</h1>
        <p>守秘人已启动战斗模式</p>
        <div class="loading-indicator">
          <div class="spinner"></div>
          <span>准备战斗中...</span>
        </div>
      </div>
    </div>

    <!-- 战斗界面 -->
    <div class="combat-interface" v-if="combatReady">
      <!-- 玩家只能看到自己的行动选项 -->
      <div class="player-action-panel" v-if="isMyTurn">
        <h3>{{ currentCharacter.name }} 的回合</h3>
        <div class="available-actions">
          <button v-for="action in availableActions" 
                  :key="action.id"
                  @click="selectAction(action)"
                  class="action-btn">
            <i :class="action.icon"></i>
            {{ action.name }}
          </button>
        </div>

        <!-- 武器选择 -->
        <div class="weapon-selection" v-if="selectedAction === 'attack'">
          <h4>选择武器</h4>
          <div class="weapon-options">
            <div v-for="weapon in availableWeapons" 
                 :key="weapon.id"
                 @click="selectWeapon(weapon)"
                 class="weapon-option">
              <img :src="weapon.icon" :alt="weapon.name">
              <span>{{ weapon.name }}</span>
              <span class="ammo" v-if="weapon.ammo">{{ weapon.currentAmmo }}/{{ weapon.maxAmmo }}</span>
            </div>
          </div>
        </div>

        <!-- 道具使用 -->
        <div class="item-selection" v-if="selectedAction === 'item'">
          <h4>使用道具</h4>
          <div class="item-grid">
            <div v-for="item in usableItems" 
                 :key="item.id"
                 @click="useItem(item)"
                 class="item-slot">
              <img :src="item.icon" :alt="item.name">
              <span>{{ item.name }}</span>
              <span class="quantity">x{{ item.quantity }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 等待提示 -->
      <div class="waiting-panel" v-else>
        <h3>等待其他玩家行动...</h3>
        <div class="current-turn-info">
          <span>当前回合: {{ currentTurnPlayer }}</span>
        </div>
      </div>

      <!-- 战斗状态显示 -->
      <div class="combat-status">
        <div class="character-status">
          <div class="hp-bar">
            <span>生命值: {{ currentCharacter.currentHp }}/{{ currentCharacter.maxHp }}</span>
            <div class="bar">
              <div class="fill" :style="{ width: hpPercentage + '%' }"></div>
            </div>
          </div>
          <div class="san-bar">
            <span>理智值: {{ currentCharacter.currentSan }}/{{ currentCharacter.maxSan }}</span>
            <div class="bar">
              <div class="fill" :style="{ width: sanPercentage + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 1.12 大成功大失败系统 (严格按规则书)

#### 大成功 (投出01)
- **近战攻击**: 
  - 非贯穿武器: 伤害骰和伤害加值取最大值
  - 贯穿武器: 最大伤害 + 额外一次武器伤害骰
- **远程攻击**: 同近战规则
- **技能检定**: 获得额外的成功效果，由KP决定具体内容
- **特殊效果**: 可能获得意外的有利结果

#### 大失败 (投出96-100或100)
- **判定条件**:
  - 技能值 ≥ 50: 投出100为大失败
  - 技能值 < 50: 投出96-100为大失败
- **战斗大失败后果**:
  - 武器卡壳或损坏
  - 攻击到友军(幸运值最低者)
  - 自己受伤或失去平衡
  - 武器脱手或弹药散落
- **技能大失败后果**:
  - 造成相反的效果
  - 引起敌人注意
  - 损坏工具或装备
  - 浪费大量时间

#### 大失败惩罚示例
```javascript
const fumbleConsequences = {
  combat: [
    { type: 'weapon_jam', description: '武器卡壳，需要修理检定' },
    { type: 'friendly_fire', description: '误伤友军，对幸运值最低的盟友造成伤害' },
    { type: 'self_injury', description: '自己受伤，受到1d3点伤害' },
    { type: 'weapon_drop', description: '武器脱手，掉落在附近' },
    { type: 'ammo_spill', description: '弹药散落，损失1d6发子弹' }
  ],
  skill: [
    { type: 'opposite_effect', description: '产生相反的效果' },
    { type: 'attract_attention', description: '引起敌人或危险的注意' },
    { type: 'tool_damage', description: '工具损坏或丢失' },
    { type: 'time_waste', description: '浪费大量时间，错过机会' },
    { type: 'status_effect', description: '获得负面状态效果' }
  ]
}
```## 第六
部分：完整技术实现

### 6.1 核心战斗规则引擎

```javascript
// combatRules.js - 严格按照COC 7版规则实现
class CombatRules {
  // 计算伤害加值
  static calculateDamageBonus(str, siz) {
    const total = str + siz
    if (total < 65) return { bonus: -2, text: '-2' }
    if (total < 85) return { bonus: -1, text: '-1' }
    if (total < 125) return { bonus: 0, text: '0' }
    if (total < 165) return { bonus: '1d4', text: '+1d4' }
    if (total < 205) return { bonus: '1d6', text: '+1d6' }
    return { bonus: '2d6', text: '+2d6' }
  }
  
  // 计算体格
  static calculateBuild(str, siz) {
    const total = str + siz
    if (total < 65) return -2
    if (total < 85) return -1
    if (total < 125) return 0
    if (total < 165) return 1
    if (total < 205) return 2
    return Math.floor((total - 205) / 80) + 3
  }
  
  // 判断成功等级
  static getSuccessLevel(roll, skillValue) {
    if (roll === 1) return 'critical'
    if (roll === 100 || (skillValue < 50 && roll >= 96)) return 'fumble'
    if (roll > skillValue) return 'failure'
    if (roll <= skillValue / 5) return 'extreme'
    if (roll <= skillValue / 2) return 'hard'
    return 'regular'
  }
  
  // 对抗检定
  static resolveOpposedRoll(attacker, defender) {
    const attackerLevel = this.getSuccessLevel(attacker.roll, attacker.skill)
    const defenderLevel = this.getSuccessLevel(defender.roll, defender.skill)
    
    const levels = ['fumble', 'failure', 'regular', 'hard', 'extreme', 'critical']
    const attackerIndex = levels.indexOf(attackerLevel)
    const defenderIndex = levels.indexOf(defenderLevel)
    
    if (attackerIndex > defenderIndex) {
      return { winner: 'attacker', level: attackerLevel }
    } else if (defenderIndex > attackerIndex) {
      return { winner: 'defender', level: defenderLevel }
    } else {
      // 平手时，攻击者获胜(近战反击时)
      return { winner: 'attacker', level: attackerLevel }
    }
  }
  
  // 计算射击难度
  static getShootingDifficulty(distance, weaponRange) {
    if (distance <= weaponRange.base) return 'regular'
    if (distance <= weaponRange.base * 2) return 'hard'
    if (distance <= weaponRange.base * 4) return 'extreme'
    return 'impossible'
  }
  
  // 计算伤害
  static calculateDamage(weapon, attacker, successLevel) {
    let damage = this.rollDice(weapon.damage)
    let damageBonus = attacker.damageBonus
    
    if (successLevel === 'extreme') {
      if (weapon.impaling) {
        // 贯穿武器：最大伤害 + 额外伤害骰
        damage = weapon.maxDamage + this.rollDice(weapon.damage)
        damageBonus = attacker.maxDamageBonus
      } else {
        // 非贯穿武器：最大伤害
        damage = weapon.maxDamage
        damageBonus = attacker.maxDamageBonus
      }
    }
    
    return damage + damageBonus
  }
  
  // 处理全自动射击
  static processFullAutoFire(shooter, targetCount, bulletsPerBurst) {
    const results = []
    let penaltyDice = 0
    let difficultyIncrease = 0
    
    for (let i = 0; i < targetCount; i++) {
      const roll = this.rollD100()
      const adjustedSkill = shooter.skill - (difficultyIncrease * 20)
      const finalRoll = this.applyPenaltyDice(roll, penaltyDice)
      
      const success = finalRoll <= adjustedSkill
      const hitsCount = success ? Math.floor(bulletsPerBurst / 2) || 1 : 0
      
      results.push({
        burstNumber: i + 1,
        roll: finalRoll,
        success,
        hitsCount,
        bulletsUsed: bulletsPerBurst
      })
      
      // 增加后续射击的难度
      if (penaltyDice < 2) {
        penaltyDice++
      } else {
        difficultyIncrease++
      }
    }
    
    return results
  }
  
  // 处理战技
  static processManeuver(attacker, defender, maneuverType) {
    // 检查体格差异
    const buildDifference = defender.build - attacker.build
    let penaltyDice = 0
    
    if (buildDifference >= 3) {
      return { success: false, reason: '体格差距过大，战技无效' }
    } else if (buildDifference === 2) {
      penaltyDice = 2
    } else if (buildDifference === 1) {
      penaltyDice = 1
    }
    
    // 进行对抗检定
    const attackerRoll = this.applyPenaltyDice(this.rollD100(), penaltyDice)
    const defenderRoll = this.rollD100()
    
    const result = this.resolveOpposedRoll(
      { roll: attackerRoll, skill: attacker.fightingSkill },
      { roll: defenderRoll, skill: defender.fightingSkill }
    )
    
    return {
      success: result.winner === 'attacker',
      level: result.level,
      maneuverType,
      attackerRoll,
      defenderRoll
    }
  }
}
```

### 6.2 战斗状态管理

```javascript
// combatManager.js
class CombatManager {
  constructor(roomId, kpId) {
    this.combat = new CombatInstance(roomId, kpId)
    this.websocket = websocketService
    this.database = databaseService
  }
  
  // KP触发战斗
  async triggerCombat(participants, settings) {
    if (!this.combat.checkKPPermission(this.kpId)) {
      throw new Error('只有KP可以触发战斗')
    }
    
    // 设置参与者
    participants.forEach(p => {
      this.combat.addParticipant(p.character, p.isPlayer)
    })
    
    // 应用设置
    Object.assign(this.combat.settings, settings)
    
    // 保存到数据库
    await this.database.saveCombat(this.combat)
    
    // 强制所有玩家进入战斗模式
    this.websocket.broadcast(this.combat.roomId, {
      type: 'FORCE_COMBAT_MODE',
      data: {
        combatId: this.combat.id,
        participants: this.combat.participants,
        settings: this.combat.settings,
        message: '守秘人已启动战斗模式'
      }
    })
    
    this.combat.status = 'active'
    return this.combat
  }
  
  // 处理玩家行动
  async handlePlayerAction(playerId, action) {
    const participant = this.combat.participants.find(p => p.playerId === playerId)
    if (!participant) {
      throw new Error('玩家不在战斗中')
    }
    
    const currentActor = this.combat.getCurrentActor()
    if (currentActor.id !== participant.id) {
      throw new Error('不是你的回合')
    }
    
    if (participant.hasActed) {
      throw new Error('本轮已经行动过了')
    }
    
    // 处理不同类型的行动
    let result
    switch (action.type) {
      case 'attack':
        result = await this.processAttack(participant, action)
        break
      case 'maneuver':
        result = await this.processManeuver(participant, action)
        break
      case 'move':
        result = await this.processMove(participant, action)
        break
      case 'item':
        result = await this.processItemUse(participant, action)
        break
      case 'spell':
        result = await this.processSpellCast(participant, action)
        break
      default:
        throw new Error('未知的行动类型')
    }
    
    // 标记已行动
    participant.hasActed = true
    
    // 广播行动结果
    this.websocket.broadcast(this.combat.roomId, {
      type: 'COMBAT_ACTION_RESULT',
      data: {
        combatId: this.combat.id,
        actorId: participant.id,
        action,
        result,
        combatState: this.getCombatState()
      }
    })
    
    // 保存状态
    await this.database.updateCombat(this.combat)
    
    return result
  }
  
  // 处理攻击
  async processAttack(attacker, action) {
    const target = this.combat.participants.find(p => p.id === action.targetId)
    if (!target) {
      throw new Error('目标不存在')
    }
    
    const weapon = attacker.equipment.weapons.find(w => w.id === action.weaponId)
    if (!weapon) {
      throw new Error('武器不存在')
    }
    
    // 检查弹药
    if (weapon.ammo !== undefined && weapon.currentAmmo <= 0) {
      throw new Error('弹药不足')
    }
    
    let result
    if (weapon.type === 'melee') {
      result = await this.processMeleeAttack(attacker, target, weapon, action)
    } else {
      result = await this.processRangedAttack(attacker, target, weapon, action)
    }
    
    // 消耗弹药
    if (weapon.ammo !== undefined) {
      weapon.currentAmmo -= (action.bulletsUsed || 1)
    }
    
    return result
  }
  
  // 处理近战攻击
  async processMeleeAttack(attacker, target, weapon, action) {
    // 攻击者投骰
    const attackRoll = diceRoller.roll('1d100')
    const attackSkill = attacker.skills[weapon.skill] || weapon.baseSkill
    const attackSuccess = CombatRules.getSuccessLevel(attackRoll, attackSkill)
    
    // 目标选择防御方式
    const defenseType = action.defenseType || 'dodge' // dodge, parry
    let defenseSkill, defenseRoll
    
    if (defenseType === 'dodge') {
      defenseSkill = target.skills.dodge
      defenseRoll = diceRoller.roll('1d100')
    } else {
      defenseSkill = target.skills[target.equipment.mainWeapon?.skill] || 25
      defenseRoll = diceRoller.roll('1d100')
    }
    
    const defenseSuccess = CombatRules.getSuccessLevel(defenseRoll, defenseSkill)
    
    // 对抗检定
    const contest = CombatRules.resolveOpposedRoll(
      { roll: attackRoll, skill: attackSkill },
      { roll: defenseRoll, skill: defenseSkill }
    )
    
    let damage = 0
    let effects = []
    
    if (contest.winner === 'attacker') {
      // 攻击命中，计算伤害
      damage = CombatRules.calculateDamage(weapon, attacker, contest.level)
      
      // 应用护甲
      const armor = target.equipment.armor
      if (armor && armor.protection > 0) {
        const armorReduction = Math.min(damage, armor.protection)
        damage -= armorReduction
        effects.push(`护甲吸收了 ${armorReduction} 点伤害`)
      }
      
      // 应用伤害
      if (damage > 0) {
        target.currentHp -= damage
        effects.push(`造成 ${damage} 点伤害`)
        
        // 检查重伤状态
        if (target.currentHp <= target.maxHp / 4) {
          effects.push(`${target.name} 进入重伤状态`)
          // 需要体质检定保持清醒
          const conRoll = diceRoller.roll('1d100')
          if (conRoll > target.attributes.constitution) {
            target.statusEffects.push({
              name: '昏迷',
              duration: -1, // 直到治疗
              effect: 'unconscious'
            })
            effects.push(`${target.name} 昏迷了`)
          }
        }
        
        // 检查死亡
        if (target.currentHp <= 0) {
          if (target.currentHp < -target.attributes.constitution) {
            target.statusEffects.push({
              name: '死亡',
              duration: -1,
              effect: 'dead'
            })
            effects.push(`${target.name} 死亡了`)
          } else {
            target.statusEffects.push({
              name: '濒死',
              duration: -1,
              effect: 'dying'
            })
            effects.push(`${target.name} 濒死`)
          }
        }
      }
    } else {
      // 防御成功
      if (defenseType === 'dodge') {
        effects.push(`${target.name} 成功闪避了攻击`)
      } else {
        effects.push(`${target.name} 成功格挡了攻击`)
        // 反击成功时可能造成伤害
        if (contest.winner === 'defender' && defenseType === 'parry') {
          const counterDamage = CombatRules.calculateDamage(
            target.equipment.mainWeapon, 
            target, 
            contest.level
          )
          attacker.currentHp -= counterDamage
          effects.push(`反击造成 ${counterDamage} 点伤害`)
        }
      }
    }
    
    return {
      type: 'melee_attack',
      attacker: attacker.name,
      target: target.name,
      weapon: weapon.name,
      attackRoll,
      attackSuccess,
      defenseRoll,
      defenseSuccess,
      defenseType,
      winner: contest.winner,
      damage,
      effects,
      targetHp: target.currentHp
    }
  }
  
  // KP结束战斗
  async endCombat() {
    if (!this.combat.checkKPPermission(this.kpId)) {
      throw new Error('只有KP可以结束战斗')
    }
    
    this.combat.status = 'ended'
    
    // 同步角色数据到数据库
    for (const participant of this.combat.participants) {
      if (participant.isPlayer) {
        await this.database.updateCharacter(participant.id, {
          currentHp: participant.currentHp,
          currentSan: participant.currentSan,
          currentMp: participant.currentMp,
          statusEffects: participant.statusEffects
        })
      }
    }
    
    // 通知所有玩家退出战斗模式
    this.websocket.broadcast(this.combat.roomId, {
      type: 'EXIT_COMBAT_MODE',
      data: {
        combatId: this.combat.id,
        message: '战斗结束',
        summary: this.generateCombatSummary()
      }
    })
    
    await this.database.updateCombat(this.combat)
    return this.combat
  }
}
```

### 6.3 数据库集成

```sql
-- 战斗会话表
CREATE TABLE combat_sessions (
  id VARCHAR(50) PRIMARY KEY,
  room_id VARCHAR(50) NOT NULL,
  kp_id VARCHAR(50) NOT NULL,
  status ENUM('preparing', 'active', 'paused', 'ended') DEFAULT 'preparing',
  current_round INT DEFAULT 1,
  current_turn INT DEFAULT 0,
  is_forced BOOLEAN DEFAULT TRUE,
  participants_data JSON NOT NULL,
  battlefield_data JSON,
  settings JSON,
  combat_log JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  ended_at TIMESTAMP NULL,
  FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
  FOREIGN KEY (kp_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 战斗行动记录表
CREATE TABLE combat_actions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  combat_id VARCHAR(50) NOT NULL,
  round_number INT NOT NULL,
  turn_number INT NOT NULL,
  actor_id VARCHAR(50) NOT NULL,
  action_type ENUM('attack', 'maneuver', 'move', 'item', 'spell', 'other') NOT NULL,
  action_data JSON NOT NULL,
  result_data JSON NOT NULL,
  dice_rolls JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (combat_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
);

-- 角色状态快照表
CREATE TABLE character_combat_snapshots (
  id INT AUTO_INCREMENT PRIMARY KEY,
  combat_id VARCHAR(50) NOT NULL,
  character_id VARCHAR(50) NOT NULL,
  snapshot_type ENUM('start', 'end', 'death', 'major_change') NOT NULL,
  hp_before INT,
  hp_after INT,
  san_before INT,
  san_after INT,
  mp_before INT,
  mp_after INT,
  status_effects JSON,
  equipment_state JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (combat_id) REFERENCES combat_sessions(id) ON DELETE CASCADE
);
```

### 6.4 前端组件完整实现

```vue
<!-- CombatSystemMain.vue -->
<template>
  <div class="combat-system-main">
    <!-- KP控制面板 -->
    <KPCombatPanel 
      v-if="isKP && !combatActive"
      @trigger-combat="handleTriggerCombat"
      :available-characters="availableCharacters"
      :monster-library="monsterLibrary"
    />
    
    <!-- 强制战斗界面 -->
    <ForcedCombatInterface
      v-if="combatActive"
      :combat-data="combatData"
      :is-kp="isKP"
      :current-character="currentCharacter"
      @player-action="handlePlayerAction"
      @kp-action="handleKPAction"
    />
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'
import websocketService from '@/services/websocket'
import KPCombatPanel from './KPCombatPanel.vue'
import ForcedCombatInterface from './ForcedCombatInterface.vue'

export default {
  name: 'CombatSystemMain',
  components: {
    KPCombatPanel,
    ForcedCombatInterface
  },
  
  data() {
    return {
      combatActive: false,
      combatData: null,
      availableCharacters: [],
      monsterLibrary: [],
      currentCharacter: null
    }
  },
  
  computed: {
    ...mapState(['user', 'currentRoom']),
    isKP() {
      return this.user.id === this.currentRoom.owner_id
    }
  },
  
  mounted() {
    this.setupWebSocketListeners()
    this.loadAvailableCharacters()
    this.loadMonsterLibrary()
  },
  
  methods: {
    setupWebSocketListeners() {
      websocketService.on('FORCE_COMBAT_MODE', this.handleForceCombat)
      websocketService.on('EXIT_COMBAT_MODE', this.handleExitCombat)
      websocketService.on('COMBAT_ACTION_RESULT', this.handleActionResult)
      websocketService.on('COMBAT_STATE_UPDATE', this.handleStateUpdate)
    },
    
    handleForceCombat(data) {
      this.combatActive = true
      this.combatData = data
      this.currentCharacter = this.findPlayerCharacter(data.participants)
      
      // 显示强制战斗提示
      this.$notify({
        title: '⚔️ 触发战斗',
        message: data.message,
        type: 'warning',
        duration: 3000
      })
      
      // 禁用页面其他功能
      document.body.classList.add('combat-mode-forced')
    },
    
    handleExitCombat(data) {
      this.combatActive = false
      this.combatData = null
      this.currentCharacter = null
      
      // 恢复页面功能
      document.body.classList.remove('combat-mode-forced')
      
      this.$notify({
        title: '战斗结束',
        message: data.message,
        type: 'success',
        duration: 3000
      })
    },
    
    async handleTriggerCombat(combatConfig) {
      try {
        await websocketService.send({
          type: 'TRIGGER_COMBAT',
          data: {
            roomId: this.currentRoom.id,
            participants: combatConfig.participants,
            settings: combatConfig.settings
          }
        })
      } catch (error) {
        this.$notify({
          title: '触发战斗失败',
          message: error.message,
          type: 'error'
        })
      }
    },
    
    async handlePlayerAction(action) {
      try {
        await websocketService.send({
          type: 'PLAYER_COMBAT_ACTION',
          data: {
            combatId: this.combatData.combatId,
            playerId: this.user.id,
            action
          }
        })
      } catch (error) {
        this.$notify({
          title: '行动失败',
          message: error.message,
          type: 'error'
        })
      }
    },
    
    async handleKPAction(action) {
      if (!this.isKP) return
      
      try {
        await websocketService.send({
          type: 'KP_COMBAT_ACTION',
          data: {
            combatId: this.combatData.combatId,
            kpId: this.user.id,
            action
          }
        })
      } catch (error) {
        this.$notify({
          title: 'KP操作失败',
          message: error.message,
          type: 'error'
        })
      }
    }
  }
}
</script>

<style scoped>
.combat-system-main {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 强制战斗模式样式 */
:global(.combat-mode-forced) {
  overflow: hidden;
}

:global(.combat-mode-forced .main-content > *:not(.combat-system-main)) {
  display: none !important;
}
</style>
```

这个完整的COC 7版战斗系统严格按照规则书实现，包含了所有核心机制，并且只有KP可以控制战斗系统，玩家只能响应战斗指令。系统与角色卡、背包道具、MySQL数据库完全对接，提供了完整的2D战斗体验。