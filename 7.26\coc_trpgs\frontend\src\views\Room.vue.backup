<template>
  <div class="room-container">
    <!-- 用户身份显示区域 -->
    <div class="user-role-indicator">
      <i class="fas" :class="isRoomOwner ? 'fa-crown' : 'fa-user'"></i>
      <span>{{ isRoomOwner ? '房主' : '玩家' }}</span>
    </div>
    
    <!-- 房间顶部栏，添加双击事件 -->
    <div class="room-header" @dblclick="toggleFullScreen">
      <div class="room-title">
        <h2 v-if="!isEditingRoomName || !isRoomOwner" @click="startEditRoomName" :class="{ 'editable': isRoomOwner }">
          {{ room.name || '加载中...' }}
          <i v-if="isRoomOwner" class="fas fa-edit edit-icon"></i>
        </h2>
        <input 
          v-else
          type="text" 
          v-model="editableRoomName" 
          @blur="saveRoomName"
          @keyup.enter="saveRoomName"
          @keyup.esc="cancelEditRoomName"
          ref="roomNameInput"
          class="room-name-input"
        />
        <span class="room-participants" v-if="onlineUsers.length > 0">
          {{ onlineUsers.length }} 人在线
        </span>
      </div>
      <div class="room-actions">
        <button class="room-action-btn announcement-btn" @click="openAnnouncement" title="公告">
          <i class="fas fa-bullhorn"></i>
          <span class="btn-text">公告</span>
        </button>
        <button class="room-action-btn toggle-btn" @click="toggleToolbar">
          <i :class="isToolbarCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
          <span class="btn-text">{{ isToolbarCollapsed ? '展开' : '收起' }}</span>
        </button>
        <button class="room-action-btn" @click="toggleFullScreen" title="全屏">
          <i class="fas fa-expand"></i>
        </button>
        <button class="room-action-btn" @click="showSettingsModal = true" title="设置">
          <i class="fas fa-cog"></i>
        </button>
      </div>
    </div>
    
    <div class="room-controls">
      <span class="room-participants">👥 {{ onlineUsers.length }} 人在线</span>
      <button @click="toggleDarkMode" class="control-btn theme-btn">
        {{ isDarkMode ? '深色模式' : '浅色模式' }}
      </button>
      
      <!-- 添加剧本模式设置按钮 -->
      <button v-if="currentAIMode === 'script'" @click="openChatBoxSettings" class="control-btn script-btn">
        <i class="fas fa-book-open"></i>
        剧本模式设置
      </button>
      
      <!-- 添加快速存档按钮 -->
      <button v-if="isRoomOwner" @click="quickSave" class="control-btn save-btn">
        <i class="fas fa-save"></i>
        快速存档
      </button>
      
      <!-- 添加快速读档按钮 -->
      <button v-if="isRoomOwner && hasQuickSave" @click="quickLoad" class="control-btn load-btn">
        <i class="fas fa-folder-open"></i>
        快速读档
      </button>
    </div>
    
    <div class="main-content">
      
      <!-- 消息区域 -->
      <div class="message-area">
        <chat-box
          ref="chatBox"
          :messages="messages"
          :currentUser="currentUser"
          :users="users"
          :isLoading="isLoading"
          :roomChatMode="room?.chat_mode || 'normal'"
          @send-message="sendMessage"
          @roll-dice="rollDice"
          @upload-script="uploadScript"
          @view-scenario="handleViewScenario"
        />
      </div>
    </div>
    
    <!-- 浮动面板 -->
    <floating-character-sheet
      v-if="visiblePanels.character"
      :character="selectedCharacter"
      :initial-x="50"
      :initial-y="100"
      @close="togglePanel('character', true)"
      @skill-check="handleSkillCheck"
      @select-character="showCharacterModal = true"
      @update-sanity="handleSanityUpdate"
      @update-character="handleCharacterUpdate"
      @character-sync="handleCharacterSync"
    />
    
    <floating-dice-roller
      v-if="visiblePanels.dice"
      :initial-x="500"
      :initial-y="100"
      :dice-history="diceHistory"
      @close="togglePanel('dice', true)"
      @roll="handleRoll"
      @skill-check="handleSkillCheck"
    />
    
    <floating-notes
      v-if="showNotes"
      :initial-x="300"
      :initial-y="300"
      :character-id="currentCharacterId"
      @close="toggleNotes"
      @note-saved="handleNoteSaved"
    />
    
    <!-- 群聊面板 -->
    <floating-panel
      v-if="visiblePanels.groupChat && currentAIMode === 'script'"
      title="玩家讨论区"
      :initial-x="400"
      :initial-y="200"
      :initial-width="500"
      :initial-height="400"
      :min-width="300"
      :min-height="300"
      @close="togglePanel('groupChat', true)"
    >
      <group-chat :room-id="internalRoomId" :ai-mode="currentAIMode" />
    </floating-panel>
    
    <!-- 地图系统面板 -->
    <floating-panel
      v-if="visiblePanels.map"
      title="地图系统"
      :initial-x="100"
      :initial-y="150"
      :initial-width="600"
      :initial-height="450"
      :min-width="400"
      :min-height="350"
      @close="togglePanel('map', true)"
    >
      <map-system :room-id="internalRoomId" />
    </floating-panel>
    
    <!-- 线索墙面板 -->
    <floating-panel
      v-if="visiblePanels.clueBoard"
      title="线索墙"
      :initial-x="150"
      :initial-y="200"
      :initial-width="700"
      :initial-height="500"
      :min-width="400"
      :min-height="400"
      @close="togglePanel('clueBoard', true)"
    >
      <clue-board :room-id="internalRoomId" />
    </floating-panel>

    <!-- 战斗系统面板 -->
    <floating-panel
      v-if="visiblePanels.combat"
      title="战斗系统"
      :initial-x="100"
      :initial-y="100"
      :initial-width="800"
      :initial-height="600"
      :min-width="600"
      :min-height="500"
      @close="togglePanel('combat', true)"
    >
      <combat-system
        @combat-started="handleCombatStarted"
        @combat-ended="handleCombatEnded"
        @open-skill-check="openSkillCheckForCharacter"
      />
    </floating-panel>

    <!-- 技能检定系统面板 -->
    <floating-panel
      v-if="visiblePanels.skillCheck"
      title="技能检定系统"
      :initial-x="200"
      :initial-y="150"
      :initial-width="700"
      :initial-height="550"
      :min-width="500"
      :min-height="450"
      @close="togglePanel('skillCheck', true)"
    >
      <skill-check-system
        :initial-character="skillCheckCharacter"
        :initial-skill="skillCheckSkill"
        @close="togglePanel('skillCheck', true)"
        @skill-check-result="handleSkillCheckResult"
        @opposed-check-result="handleOpposedCheckResult"
        @skill-growth="handleSkillGrowth"
        @growth-check-result="handleGrowthCheckResult"
        @character-selected="handleSkillCheckCharacterSelected"
      />
    </floating-panel>

    <!-- 装备系统面板 -->
    <floating-panel
      v-if="visiblePanels.equipment"
      title="装备系统"
      :initial-x="150"
      :initial-y="100"
      :initial-width="900"
      :initial-height="650"
      :min-width="700"
      :min-height="500"
      @close="togglePanel('equipment', true)"
    >
      <equipment-system
        @weapon-equipped="handleWeaponEquipped"
        @weapon-unequipped="handleWeaponUnequipped"
        @weapon-used="handleWeaponUsed"
        @armor-equipped="handleArmorEquipped"
        @armor-unequipped="handleArmorUnequipped"
        @item-used="handleItemUsed"
        @item-purchased="handleItemPurchased"
        @item-dropped="handleItemDropped"
        @heal-character="handleHealCharacter"
        @read-book="handleReadBook"
      />
    </floating-panel>

    <!-- 经历包系统面板 -->
    <floating-panel
      v-if="visiblePanels.experiencePack"
      title="经历包系统"
      :initial-x="250"
      :initial-y="150"
      :initial-width="800"
      :initial-height="600"
      :min-width="600"
      :min-height="500"
      @close="togglePanel('experiencePack', true)"
    >
      <experience-pack-system
        @character-updated="handleCharacterUpdate"
      />
    </floating-panel>

    <!-- 法术系统面板 -->
    <floating-panel
      v-if="visiblePanels.spells"
      title="法术系统"
      :initial-x="100"
      :initial-y="120"
      :initial-width="900"
      :initial-height="700"
      :min-width="700"
      :min-height="600"
      @close="togglePanel('spells', true)"
    >
      <spell-system
        @character-updated="handleCharacterUpdate"
        @spell-cast="handleSpellCast"
      />
    </floating-panel>

    <!-- 疯狂症状系统面板 -->
    <floating-panel
      v-if="visiblePanels.madness"
      title="疯狂症状系统"
      :initial-x="150"
      :initial-y="100"
      :initial-width="850"
      :initial-height="650"
      :min-width="650"
      :min-height="550"
      @close="togglePanel('madness', true)"
    >
      <madness-system
        @character-updated="handleCharacterUpdate"
      />
    </floating-panel>

    <!-- 神话典籍图书馆面板 -->
    <floating-panel
      v-if="visiblePanels.library"
      title="神话典籍图书馆"
      :initial-x="200"
      :initial-y="80"
      :initial-width="950"
      :initial-height="750"
      :min-width="750"
      :min-height="650"
      @close="togglePanel('library', true)"
    >
      <mythos-library
        @character-updated="handleCharacterUpdate"
      />
    </floating-panel>
    
    <!-- 音频系统面板 -->
    <floating-panel
      v-if="visiblePanels.audioSystem && isRoomOwner"
      title="音频系统"
      :initial-x="200"
      :initial-y="250"
      :initial-width="600"
      :initial-height="400"
      :min-width="350"
      :min-height="300"
      @close="togglePanel('audioSystem', true)"
    >
      <audio-system :roomId="internalRoomId" :isKP="isRoomOwner" />
    </floating-panel>
    
    <!-- 语音聊天面板 -->
    <floating-panel
      v-if="visiblePanels.voiceChat"
      title="语音聊天"
      :initial-x="250"
      :initial-y="300"
      :initial-width="300"
      :initial-height="400"
      :min-width="250"
      :min-height="300"
      @close="togglePanel('voiceChat', true)"
    >
      <voice-chat 
        :roomId="internalRoomId" 
        :username="$store.getters.currentUser?.username || '玩家'" 
        :isKP="isKP"
        @voice-connected="handleVoiceConnected" 
        @voice-disconnected="handleVoiceDisconnected" 
      />
    </floating-panel>
    
    <div class="room-toolbar" :class="{ 'collapsed': isToolbarCollapsed }">
      <div class="toolbar-buttons">
        <!-- 角色相关工具 -->
        <div class="toolbar-group">
          <button @click="toggleDiceRoller" class="toolbar-btn" :class="{ 'active': showDiceRoller }">
            <i class="fas fa-dice-d20"></i>
            <span class="tooltip">骰子</span>
          </button>

          <button @click="toggleCharacterSheet" class="toolbar-btn" :class="{ 'active': showCharacterSheet }">
            <i class="fas fa-user"></i>
            <span class="tooltip">角色卡</span>
          </button>

          <button @click="toggleSkillCheck" class="toolbar-btn" :class="{ 'active': visiblePanels.skillCheck }">
            <i class="fas fa-cog"></i>
            <span class="tooltip">技能检定</span>
          </button>

          <button @click="toggleNotes" class="toolbar-btn" :class="{ 'active': showNotes }">
            <i class="fas fa-sticky-note"></i>
            <span class="tooltip">笔记</span>
          </button>
        </div>

        <!-- 战斗和装备工具 -->
        <div class="toolbar-group">
          <button @click="toggleCombat" class="toolbar-btn" :class="{ 'active': visiblePanels.combat }">
            <i class="fas fa-sword"></i>
            <span class="tooltip">战斗</span>
          </button>

          <button @click="toggleEquipment" class="toolbar-btn" :class="{ 'active': visiblePanels.equipment }">
            <i class="fas fa-shield-alt"></i>
            <span class="tooltip">装备</span>
          </button>

          <button @click="toggleExperiencePack" class="toolbar-btn" :class="{ 'active': visiblePanels.experiencePack }">
            <i class="fas fa-star"></i>
            <span class="tooltip">经历包</span>
          </button>
        </div>

        <!-- 神话和魔法工具 -->
        <div class="toolbar-group">
          <button @click="toggleSpells" class="toolbar-btn" :class="{ 'active': visiblePanels.spells }">
            <i class="fas fa-magic"></i>
            <span class="tooltip">法术</span>
          </button>

          <button @click="toggleMadness" class="toolbar-btn" :class="{ 'active': visiblePanels.madness }">
            <i class="fas fa-brain"></i>
            <span class="tooltip">疯狂</span>
          </button>

          <button @click="toggleLibrary" class="toolbar-btn" :class="{ 'active': visiblePanels.library }">
            <i class="fas fa-book-open"></i>
            <span class="tooltip">典籍</span>
          </button>
        </div>
        
        <!-- 地图和线索工具 -->
        <div class="toolbar-group">
          <button @click="toggleMap" class="toolbar-btn" :class="{ 'active': visiblePanels.map }">
            <i class="fas fa-map"></i>
            <span class="tooltip">地图</span>
          </button>
          
          <button @click="toggleClueBoard" class="toolbar-btn" :class="{ 'active': visiblePanels.clueBoard }">
            <i class="fas fa-thumbtack"></i>
            <span class="tooltip">线索墙</span>
          </button>
          
          <button 
            v-if="isRoomOwner"
            @click="toggleAudioSystem" 
            class="toolbar-btn"
            :class="{ 'active': visiblePanels.audioSystem }"
          >
            <i class="fas fa-music"></i>
            <span class="tooltip">音频</span>
          </button>
        </div>
        
        <!-- 游戏存档按钮 -->
        <div class="toolbar-group" v-if="isRoomOwner">
          <button 
            @click="toggleSaveOptions" 
            class="toolbar-btn"
            :class="{ 'active': showSaveOptions }"
          >
            <i class="fas fa-save"></i>
            <span class="tooltip">游戏存档</span>
          </button>
        </div>
        
        <!-- 聊天工具 -->
        <div class="toolbar-group">
          <button 
            @click="toggleVoiceChat" 
            class="toolbar-btn"
            :class="{ 'active': visiblePanels.voiceChat }"
          >
            <i class="fas fa-microphone"></i>
            <span class="tooltip">语音</span>
          </button>
          
          <button 
            @click="togglePrivateChat" 
            class="toolbar-btn"
            :class="{ 'active': visiblePanels.privateChat }"
          >
            <i class="fas fa-comments"></i>
            <span class="tooltip">私聊</span>
          </button>
          
          <button 
            v-if="currentAIMode === 'script'"
            @click="toggleGroupChat" 
            class="toolbar-btn"
            :class="{ 'active': visiblePanels.groupChat }"
          >
            <i class="fas fa-users"></i>
            <span class="tooltip">群聊</span>
          </button>
        </div>
      </div>
      
      <!-- 折叠按钮 -->
      <button @click="toggleToolbar" class="collapse-btn">
        <i :class="[
          'fas', 
          isToolbarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'
        ]"></i>
      </button>
    </div>
    
    <!-- 浮动面板 -->
    <floating-panel
      v-if="visiblePanels.privateChat"
      title="私聊"
      :initial-x="windowWidth - 350"
      :initial-y="windowHeight - 450"
      :initial-width="320"
      :initial-height="400"
      :min-width="250"
      :min-height="300"
      @close="togglePanel('privateChat', true)"
    >
      <private-chat-manager :online-users="onlineUsers" />
    </floating-panel>
    
    <!-- 私聊管理器 -->
    <!-- <private-chat-manager :online-users="onlineUsers" :hidden="isToolbarCollapsed" /> -->
    
    <!-- 角色选择模态框 -->
    <modal v-if="showCharacterModal" @close="showCharacterModal = false">
      <template #header>
        <h3>选择角色</h3>
      </template>
      <div class="character-list">
        <div 
          v-for="character in characters" 
          :key="character.id" 
          @click="selectCharacter(character)"
          class="character-item"
        >
          <h4>{{ character.name }}</h4>
          <p>{{ character.occupation }}</p>
        </div>
      </div>
    </modal>
    
    <!-- 设置模态框 -->
    <modal v-if="showSettingsModal" @close="showSettingsModal = false">
      <template #header>
        <h3>房间设置</h3>
      </template>
      <div class="settings-content">
        <div class="setting-group">
          <label>字体大小</label>
          <div class="font-size-controls">
            <button @click="changeFontSize(-1)" class="font-btn">A-</button>
            <span class="current-size">{{ fontSize }}px</span>
            <button @click="changeFontSize(1)" class="font-btn">A+</button>
          </div>
        </div>
        
        <div class="setting-group">
          <label>通知设置</label>
          <div class="toggle-switch">
            <input type="checkbox" id="soundToggle" v-model="playSounds">
            <label for="soundToggle">骰子声音</label>
          </div>
          <div class="toggle-switch">
            <input type="checkbox" id="notifyToggle" v-model="showNotifications">
            <label for="notifyToggle">桌面通知</label>
          </div>
        </div>
        
        <div class="setting-actions">
          <button @click="saveSettings" class="save-settings-btn">保存设置</button>
        </div>
      </div>
    </modal>
    
    <!-- 剧本模式设置已移至ChatBox.vue -->
    
    <!-- AI模型设置面板 -->
    <floating-panel
      v-if="visiblePanels.aiSettings && isRoomOwner"
      title="AI模型设置"
      :initial-x="200"
      :initial-y="150"
      :initial-width="500"
      :initial-height="550"
      @close="togglePanel('aiSettings', true)"
    >
      <a-i-model-settings 
        :room-id="roomId"
        :is-room-owner="isRoomOwner"
        @settings-updated="handleAISettingsUpdated"
      />
    </floating-panel>
    
    <!-- 游戏存档管理面板 -->
    <floating-panel
      v-if="visiblePanels.gameSaves && isRoomOwner"
      title="游戏存档"
      :initial-x="200"
      :initial-y="250"
      :initial-width="500"
      :initial-height="400"
      @close="togglePanel('gameSaves', true)"
    >
      <game-save-manager 
        :room-id="internalRoomId" 
        @save-created="handleSaveCreated" 
        @save-updated="handleSaveUpdated"
        @save-deleted="handleSaveDeleted"
        @save-load="handleSaveLoad" 
        @save-error="handleSaveError" 
        @collect-state="collectGameState"
        @save-auto-created="handleSaveAutoCreated"
        @save-message="handleSaveMessage"
        @save-restored="handleSaveRestored"
        @save-preview="handleSavePreview"
      />
    </floating-panel>
    
    <!-- 剧本查看器面板 -->
    <floating-panel
      v-if="visiblePanels.scenarioViewer && scenarioContent"
      title="剧本查看器"
      :initial-x="300"
      :initial-y="150"
      :initial-width="700"
      :initial-height="500"
      :min-width="400"
      :min-height="350"
      @close="togglePanel('scenarioViewer', true)"
    >
      <scenario-viewer 
        :scenario-content="scenarioContent" 
        :scenario-title="scenarioTitle"
        :file-size="scenarioFileSize"
      />
    </floating-panel>
    
    <!-- 公告展示面板 -->
    <floating-panel
      v-if="visiblePanels.announcement"
      title="公告展示"
      :initial-x="300"
      :initial-y="150"
      :initial-width="700"
      :initial-height="500"
      :min-width="400"
      :min-height="350"
      @close="togglePanel('announcement', true)"
    >
      <announcement-viewer 
        :content="announcementContent" 
        :title="announcementTitle"
        :publish-time="announcementTime"
        :is-editable="isRoomOwner"
        @save="handleAnnouncementSave"
      />
    </floating-panel>
    
    <!-- 在模板中添加预览模式提示 -->
    <div class="preview-mode-indicator" v-if="isPreviewMode">
      <div class="preview-info">
        <i class="fas fa-eye"></i>
        <span>预览模式</span>
      </div>
      <div class="preview-actions">
        <button class="exit-preview-btn" @click="exitPreviewMode">
          <i class="fas fa-times"></i> 退出预览
        </button>
      </div>
    </div>
    
    <!-- 存档选项面板 -->
    <div class="save-options-panel" v-if="showSaveOptions">
      <div class="save-options-content">
        <div class="save-option" @click="quickSave">
          <i class="fas fa-save"></i>
          <span>快速存档</span>
          <small>Ctrl+S</small>
        </div>
        <div class="save-option" @click="quickLoad" :class="{ 'disabled': !quickSaveId }">
          <i class="fas fa-folder-open"></i>
          <span>快速读档</span>
          <small>Ctrl+O</small>
        </div>
        <div class="save-option" @click="toggleGameSaves">
          <i class="fas fa-history"></i>
          <span>存档管理</span>
          <small>Ctrl+H</small>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ChatBox from '@/components/ChatBox.vue';
import Modal from '@/components/Modal.vue';
import FloatingPanel from '@/components/FloatingPanel.vue';
import FloatingCharacterSheet from '@/components/FloatingCharacterSheet.vue';
import FloatingDiceRoller from '@/components/FloatingDiceRoller.vue';
import FloatingNotes from '@/components/FloatingNotes.vue';
import AIModelSettings from '@/components/AIModelSettings.vue';
import MapSystem from '@/components/MapSystem.vue';
import ClueBoard from '@/components/ClueBoard.vue';

import AudioSystem from '@/components/AudioSystem.vue';
import VoiceChat from '@/components/VoiceChat.vue';
import PrivateChatManager from '@/components/PrivateChatManager.vue';
import GroupChat from '@/components/GroupChat.vue';
import GameSaveManager from '@/components/GameSaveManager.vue';
import ScenarioViewer from '@/components/ScenarioViewer.vue';
import AnnouncementViewer from '@/components/AnnouncementViewer.vue';
import apiService from '@/services/api';
import websocketService from '@/services/websocket';
import { storageMixin } from '@/mixins/storageMixin';

export default {
  name: 'Room',
  mixins: [storageMixin],
  components: {
    ChatBox,
    Modal,
    FloatingPanel,
    FloatingCharacterSheet,
    FloatingDiceRoller,
    FloatingNotes,
    AIModelSettings,
    MapSystem,
    ClueBoard,
    CombatSystem: () => import('@/components/CombatSystem.vue'),
    SkillCheckSystem: () => import('@/components/SkillCheckSystem.vue'),
    EquipmentSystem: () => import('@/components/EquipmentSystem.vue'),
    ExperiencePackSystem: () => import('@/components/ExperiencePackSystem.vue'),
    SpellSystem: () => import('@/components/SpellSystem.vue'),
    MadnessSystem: () => import('@/components/MadnessSystem.vue'),
    MythosLibrary: () => import('@/components/MythosLibrary.vue'),

    AudioSystem,
    VoiceChat,
    PrivateChatManager,
    GroupChat,
    GameSaveManager,
    ScenarioViewer,
    AnnouncementViewer
  },
  props: {
    // 不再使用props传递roomId
  },
  data() {
    return {
      room: {},
      messages: [],
      characters: [],
      selectedCharacter: null,
      websocket: null,
      showCharacterModal: false,
      showSettingsModal: false,
      diceHistory: [],
      gameNotes: '',
      onlineUsers: [],
      isTyping: false,
      typingUsername: '',
      typingTimer: null,
      lastTypingTime: 0,
      fontSize: 16,
      playSounds: true,
      showNotifications: false,
      isToolbarCollapsed: false,
      isMobile: false,
      internalRoomId: '', // 使用内部变量存储房间ID
      hasUnsavedChanges: false, // 跟踪是否有未保存的更改
      visiblePanels: {
        character: false,
        dice: false,
        notes: false,
        aiSettings: false,
        map: false,
        clueBoard: false,
        audioSystem: false,
        voiceChat: false,
        privateChat: false, // 私聊面板状态
        groupChat: false,
        gameSaves: false,  // 游戏存档面板状态
        scenarioViewer: false, // 剧本查看器面板状态
        announcement: false, // 公告展示面板状态
        combat: false, // 战斗系统面板状态
        skillCheck: false, // 技能检定系统面板状态
        equipment: false, // 装备系统面板状态
        experiencePack: false, // 经历包系统面板状态
        spells: false, // 法术系统面板状态
        madness: false, // 疯狂症状系统面板状态
        library: false // 神话典籍图书馆面板状态
      },
      currentAIMode: 'normal', // 将在 mounted 中从存储加载

      // 新功能相关状态
      skillCheckCharacter: null, // 技能检定选中的角色
      skillCheckSkill: '', // 技能检定选中的技能
      pendingSceneDescription: null,
      // AI组件状态
      aiStatus: {
          active: false,
          busy: false
      },
      // 房主状态
      isRoomOwner: false,
      // 控制API配置提醒显示
      showConfigAlert: true,
      // 角色同步定时器
      characterSyncTimer: null,
      // 移除默认场景数据
      scenes: [],
      currentSceneIndex: 0,
      currentCharacterId: null,
      showNotes: false,
      // 添加缺失的变量
      isKP: false,
      currentUser: null,
      users: [],
      isLoading: false,
      showDiceRoller: false,
      showCharacterSheet: false,
      // 添加方法引用
      rollDice: null,
      uploadScript: null,
      quickSaveId: null, // 存储快速存档的ID
      isPreviewMode: false,
      tempState: null,
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      showSaveOptions: false,
      // 剧本相关数据
      scenarioContent: '',
      scenarioTitle: '',
      scenarioFileSize: '',
      
      // 公告相关数据
      announcementContent: '',
      announcementTitle: '房间公告',
      announcementTime: new Date().toLocaleString(),
      
      // 房间名称编辑相关
      isEditingRoomName: false,
      editableRoomName: '',
    };
  },
  computed: {
    isDarkMode() {
      return this.$store.getters.isDarkTheme;
    },
    hasQuickSave() {
      // 检查是否有快速存档
      return this.quickSaveId !== null;
    }
  },
  async created() {
    try {
      // 从路由参数获取房间ID
      const routeId = this.$route.params.id;
      
      console.log(`Room组件创建, 路由参数id = ${routeId}`);
      
      // 清除之前的房间数据，确保每次都获取新的房间
      this.$store.commit('CLEAR_CURRENT_ROOM');
      
      // 尝试将路由ID转换为数字
      const numericId = parseInt(routeId);
      
      // 如果转换成功并且是有效数字，使用数字ID；否则使用默认ID 1
      if (!isNaN(numericId)) {
        this.internalRoomId = numericId;
      } else {
        console.error(`无效的房间ID: ${routeId}，使用默认ID 1`);
        this.internalRoomId = 1;
      }
      
      console.log(`Room组件创建, 原始roomId = ${routeId}, 处理后roomId = ${this.internalRoomId}`);
      
      // 初始化用户数据
      this.currentUser = this.$store.getters.currentUser || { username: '玩家' };
      this.isKP = this.currentUser.id === 1; // 假设ID为1的是KP
      
      // 初始化窗口尺寸
      this.updateWindowSize();
      
      // 检查移动设备
      this.checkMobileDevice();
      
      // 加载工具栏折叠状态
      const savedToolbarState = this.safeGetItem(`toolbar_collapsed_${this.internalRoomId}`);
      if (savedToolbarState !== null) {
        this.isToolbarCollapsed = savedToolbarState === 'true';
        console.log('加载工具栏状态:', this.isToolbarCollapsed ? '已折叠' : '已展开');
      } else {
        // 默认不折叠
        this.isToolbarCollapsed = false;
      }
      
      // 首先加载房间数据
      await this.fetchRoomData();
      console.log('房间数据加载完成', this.room);
      
      // 检查当前用户是否是房主
      this.checkRoomOwnership();
      
      // 强制设置为房主（临时解决方案）
      this.isRoomOwner = true;
      
      // 建立WebSocket连接
      this.connectWebSocket();
      
      // 获取用户角色
      await this.fetchUserCharacters();
      console.log('角色数据加载完成', this.characters);
      
      // 获取历史消息 (之后获取，因为需要WebSocket连接)
      setTimeout(() => {
        this.fetchRoomMessages();
      }, 1000);
      
      // 加载用户设置
      this.loadUserSettings();
      
      // 从本地存储加载公告
      this.loadAnnouncementFromLocalStorage();
      
      // 从本地存储加载房间名称
      this.loadRoomNameFromLocalStorage();
      
      // 默认显示公告面板
      this.togglePanel('announcement');
      
      // 默认显示角色卡和骰子面板
      // 注意：这里不需要设置默认值，因为在loadUserSettings中已经处理了
      // 只需要确保同步状态到显示变量
      this.showCharacterSheet = this.visiblePanels.character;
      this.showDiceRoller = this.visiblePanels.dice;
      
      // 设置延时检查系统状态
      setTimeout(() => {
        this.checkSystemStatus();
      }, 2000);
      
      // 检查是否已经关闭过提醒
      if (this.safeGetItem('aiConfigAlertDismissed') === 'true') {
        this.showConfigAlert = false;
      }
      
      // 检查API密钥状态
      this.checkApiKeyStatus();
      
      // 同步选中角色的状态
      this.syncSelectedCharacterStatus();
      
      // 设置定时同步角色状态
      this.setupCharacterSyncTimer();
      
      // 初始化方法引用
      this.rollDice = this.handleRollDice;
      this.uploadScript = this.handleUploadScript;
      
      // 初始化用户列表
      this.users = this.onlineUsers;
      
      // 添加键盘事件监听
      window.addEventListener('keydown', this.handleKeyDown);
      
    } catch (error) {
      console.error('初始化房间失败', error);
      if (error.message === "房间不存在" || error.response?.data?.detail === "房间不存在") {
        // 如果房间不存在，创建一个新房间
        console.log('尝试创建房间', this.internalRoomId);
        const mockRoom = { 
          id: parseInt(this.internalRoomId), 
          name: `测试房间 ${this.internalRoomId}`, 
          description: '这是一个自动创建的房间' 
        };
        this.$store.commit('SET_CURRENT_ROOM', mockRoom);
        this.room = mockRoom;
        // 重新尝试WebSocket连接
        this.connectWebSocket();
        
        // 添加系统消息提示
        this.showSystemMessage('已自动创建测试房间');
        
        // 获取用户角色
        this.fetchUserCharacters().catch(e => console.error('获取角色失败', e));
      } else {
        this.showSystemMessage('加载房间失败，请尝试重新进入');
        setTimeout(() => this.$router.push('/rooms'), 2000);
      }
    }
  },
  mounted() {
    // 注意：房间ID已在created钩子中设置，这里不需要重新设置
    console.log(`Room组件mounted, 当前房间ID = ${this.internalRoomId}`);
    
    // 加载用户设置
    this.loadUserSettings();
    
    // 检查设备类型
    this.detectDevice();
    window.addEventListener('resize', this.detectDevice);
    
    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', this.handleFullScreenChange);
    document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);
    document.addEventListener('mozfullscreenchange', this.handleFullScreenChange);
    document.addEventListener('MSFullscreenChange', this.handleFullScreenChange);
    
    // 尝试预先检测浏览器全屏支持情况
    const elem = document.querySelector('.room-container');
    if (!elem.requestFullscreen && 
        !elem.webkitRequestFullscreen && 
        !elem.mozRequestFullScreen &&
        !elem.msRequestFullscreen) {
      console.warn('当前浏览器可能不完全支持全屏API');
    } else {
      console.log('浏览器支持全屏API');
    }
    
    // 添加键盘快捷键监听
    window.addEventListener('keydown', this.handleKeyboardShortcuts);
  },
  beforeDestroy() {
    // 移除键盘事件监听
    window.removeEventListener('keydown', this.handleKeyDown);
    
    // 清除角色同步定时器
    if (this.characterSyncTimer) {
      clearInterval(this.characterSyncTimer);
    }
    
    // 关闭WebSocket连接
    if (this.websocket) {
      this.websocket.close();
    }
    
    // 断开WebSocket连接
    this.disconnectWebSocket();
    
    // 移除事件监听器
    window.removeEventListener('resize', this.detectDevice);
    
    // 移除全屏状态变化监听器
    document.removeEventListener('fullscreenchange', this.handleFullScreenChange);
    document.removeEventListener('webkitfullscreenchange', this.handleFullScreenChange);
    document.removeEventListener('mozfullscreenchange', this.handleFullScreenChange);
    document.removeEventListener('MSFullscreenChange', this.handleFullScreenChange);
    
    // 如果离开页面时处于全屏状态，尝试退出全屏
    try {
      const isFullScreen = document.fullscreenElement || 
                         document.webkitFullscreenElement || 
                         document.mozFullScreenElement || 
                         document.msFullscreenElement;
      if (isFullScreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    } catch (error) {
      console.error('退出全屏失败:', error);
    }
    
    // 移除键盘快捷键监听
    window.removeEventListener('keydown', this.handleKeyboardShortcuts);
  },
  methods: {
    async fetchRoomData() {
      try {
        const currentRoom = this.$store.getters.currentRoomData;
        
        // 确保房间ID是有效的数字
        const roomId = this.internalRoomId;
        
        console.log(`尝试获取房间数据，房间ID: ${roomId}, 当前房间:`, currentRoom);
        
        // 如果没有当前房间数据，或者当前房间ID与请求的不一致，则获取新的房间数据
        if (!currentRoom || parseInt(currentRoom.id) !== parseInt(roomId)) {
          // 尝试从服务器获取房间信息
          try {
            console.log(`当前房间数据不匹配，从服务器获取房间ID: ${roomId}`);
            
            // 使用store的joinRoom方法，它已经包含了错误处理逻辑
            const room = await this.$store.dispatch('joinRoom', { roomId });
            
            // 确保房间ID是数字类型
            room.id = parseInt(room.id);
            this.room = room;
            
            console.log('获取到的房间数据:', room);
            
            // 如果房间ID与请求的ID不一致，重定向到正确的房间
            if (room.id !== parseInt(roomId)) {
              console.warn(`房间ID不匹配，请求ID: ${roomId}, 返回ID: ${room.id}`);
              // 更新URL但不重新加载页面
              window.history.replaceState(null, '', `/room/${room.id}`);
              this.internalRoomId = room.id;
            }
          } catch (error) {
            console.error('加载房间失败', error);
            throw error;
          }
        } else {
          console.log('使用当前房间数据:', currentRoom);
          this.room = currentRoom;
        }
        
        // 模拟在线用户数据
        this.onlineUsers = [
          { id: 1, username: 'KP' },
          { id: 2, username: this.$store.getters.currentUser?.username || '玩家' }
        ];
      } catch (error) {
        console.error('fetchRoomData失败:', error);
        throw error; // 确保错误被传递出去
      }
    },
    async fetchUserCharacters() {
      try {
        await this.$store.dispatch('fetchCharacters');
        this.characters = this.$store.getters.userCharacters;
        
        // 如果有角色，默认选择第一个
        if (this.characters.length > 0) {
          this.selectedCharacter = this.characters[0];
        }
      } catch (error) {
        console.error('加载角色失败', error);
      }
    },
    async fetchRoomMessages() {
      // 这里简化处理，实际应从API获取历史消息
      // 演示用的假消息
      this.messages = [
        {
          type: 'system',
          content: '欢迎来到房间',
          timestamp: Date.now() - 5000
        },
        {
          type: 'chat',
          user_id: 1,
          username: 'KP',
          content: '你们面前是一扇古老的门，上面刻着奇怪的符号...',
          timestamp: Date.now() - 3000
        }
      ];
    },
    connectWebSocket() {
      try {
        // 获取当前用户ID
        const userId = this.$store.getters.currentUser?.id || 'guest';
        
        // 设置WebSocket事件监听器
        websocketService.onConnect = () => {
          console.log('WebSocket连接成功');
          this.showSystemMessage('已成功连接到房间');
          
          // 连接成功后，请求加入房间
          // 优先使用this.room.id，因为这是已经确认的房间ID
          const roomId = this.room?.id || this.internalRoomId;
          
          console.log(`WebSocket连接成功，加入房间ID: ${roomId}`);
          
          websocketService.sendMessage({
            type: 'join_room',
            room_id: roomId,
            user_id: userId,
            username: this.$store.getters.currentUser?.username || '玩家'
          });
        };
        
        websocketService.onDisconnect = (reason) => {
          console.log('WebSocket连接断开:', reason);
          this.showSystemMessage('与服务器的连接已断开，正在尝试重新连接...');
        };
        
        websocketService.onError = (error) => {
          console.error('WebSocket错误:', error);
          this.showSystemMessage('连接发生错误: ' + error.message);
        };
        
        // 设置自定义事件监听器
        websocketService.setEventListeners({
          'chat_message': this.handleChatMessage,
          'user_joined': this.handleUserJoined,
          'user_left': this.handleUserLeft,
          'typing': this.handleTypingEvent,
          'dice_roll': this.handleDiceRoll,
          'skill_check': this.handleSkillCheck,
          'character_update': this.handleCharacterUpdate,
          'announcement_update': this.handleAnnouncementUpdate,
          'room_name_update': this.handleRoomNameUpdate
        });
        
        // 确保房间ID有效
        // 优先使用this.room.id，因为这是已经确认的房间ID
        const roomId = this.room?.id || this.internalRoomId;
        
        console.log(`尝试连接WebSocket，房间ID: ${roomId}, 用户ID: ${userId}`);
        
        // 连接WebSocket
        websocketService.connect(roomId, userId);
        
        // 添加离开页面前的提示
        window.addEventListener('beforeunload', this.handleBeforeUnload);
      } catch (error) {
        console.error('连接WebSocket失败:', error);
        this.showSystemMessage('连接服务器失败，请检查网络连接或刷新页面重试');
        
        // 在开发环境中，提供更多调试信息
        if (process.env.NODE_ENV === 'development') {
          this.showSystemMessage('开发环境提示: 请确保WebSocket服务器已启动，端口为8084');
          
          // 5秒后显示模拟连接成功的消息，方便开发调试
          setTimeout(() => {
            this.showSystemMessage('开发环境模拟: 已成功连接到房间 (模拟)');
            
            // 添加一些模拟的用户
            this.onlineUsers = [
              { id: 1, username: 'KP' },
              { id: 2, username: this.$store.getters.currentUser?.username || '玩家' },
              { id: 3, username: '模拟玩家1' },
              { id: 4, username: '模拟玩家2' }
            ];
          }, 5000);
        }
      }
    },
    disconnectWebSocket() {
      websocketService.disconnect();
      this.$store.dispatch('leaveRoom');
    },
    handleRoll(rollData) {
        // 添加用户名
        rollData.username = this.$store.getters.currentUser?.username || '玩家';
      
      // 如果没有结果，则在本地生成骰子结果
      if (!rollData.results || !rollData.total) {
        // 生成骰子结果
        const count = parseInt(rollData.count) || 1;
        const faces = parseInt(rollData.faces) || 100;
        const modifier = parseInt(rollData.modifier) || 0;
        
        const results = [];
        for (let i = 0; i < count; i++) {
          results.push(Math.floor(Math.random() * faces) + 1);
        }
        
        const total = results.reduce((sum, val) => sum + val, 0) + modifier;
        
        rollData.results = results;
        rollData.total = total;
      }
      
      // 将骰子结果添加到本地历史记录
      this.diceHistory.unshift({
        description: `${rollData.count}D${rollData.faces}${rollData.modifier > 0 ? '+' + rollData.modifier : ''}`,
        total: rollData.total,
        results: rollData.results
      });
      
      // 限制历史记录长度
      if (this.diceHistory.length > 10) {
        this.diceHistory.pop();
      }
      
      // 创建骰子结果消息
      const diceMessage = {
        type: 'system',
        content: `${rollData.username} 投掷 ${rollData.count}D${rollData.faces}${rollData.modifier > 0 ? '+' + rollData.modifier : ''} = ${rollData.total} [${rollData.results.join(', ')}${rollData.modifier > 0 ? ' + ' + rollData.modifier : ''}]`,
        timestamp: new Date().toISOString(),
        roll_data: {
          count: rollData.count,
          faces: rollData.faces,
          modifier: rollData.modifier,
          results: rollData.results,
          total: rollData.total
        }
      };
      
      // 将结果添加到消息列表
      this.messages.push(diceMessage);
      
      // 使用websocketService发送消息
        websocketService.sendMessage(rollData);
        
        if (this.playSounds) {
          this.playDiceSound();
      }
    },
    handleSkillCheck(skillData) {
      // 添加必要的信息
        skillData.type = 'skill-check';
      if (!skillData.username) {
        skillData.username = this.$store.getters.currentUser?.username || '玩家';
      }
      
      // 如果有角色信息，添加角色相关数据
      if (!skillData.character_name && this.selectedCharacter) {
        skillData.character_name = this.selectedCharacter.name;
        skillData.character_id = this.selectedCharacter.id;
      }
      
      console.log('Room组件处理技能检定:', skillData);
      
      // 使用websocketService发送消息
        websocketService.sendMessage(skillData);
      
      // 播放骰子声音
      if (this.playSounds) {
        this.playDiceSound();
      }
      
      // 在本地执行技能检定并显示结果
      this.performLocalSkillCheck(skillData);
    },
    // 在本地执行技能检定并显示结果
    async performLocalSkillCheck(skillData) {
      try {
        // 生成1-100的随机数
        const diceResult = Math.floor(Math.random() * 100) + 1;
        const skillValue = skillData.skill_value;
        
        // 判定成功等级
        let success = false;
        let level = "失败";
        
        if (diceResult <= skillValue) {
          success = true;
          if (diceResult === 1) {
            level = "大成功";
          } else if (diceResult <= skillValue / 5) {
            level = "极难成功";
          } else if (diceResult <= skillValue / 2) {
            level = "困难成功";
          } else {
            level = "成功";
          }
        } else {
          if (diceResult >= 96 && skillValue < 50) {
            level = "大失败";
          } else if (diceResult === 100) {
            level = "大失败";
          } else {
            level = "失败";
          }
        }
        
        // 创建技能检定结果消息
        const skillName = skillData.skill_name || "技能";
        const characterName = skillData.character_name || this.$store.getters.currentUser?.username || '玩家';
        
        const resultMessage = {
          type: 'system',
          content: `${characterName} 进行 ${skillName} 检定：掷出了 ${diceResult}/${skillValue}，${level}！`,
          timestamp: new Date().toISOString(),
          roll_result: {
            dice: diceResult,
            skill: skillValue,
            success: success,
            level: level
          }
        };
        
        // 将结果添加到消息列表
        this.messages.push(resultMessage);
        
      } catch (error) {
        console.error('本地技能检定失败:', error);
        this.showSystemMessage('技能检定处理失败');
      }
    },
    sendMessage(message) {
      let messageData;
      
      console.log('Room组件收到消息:', message);
      
      // 检查message是否为字符串
      if (typeof message === 'string') {
        if (!message.trim()) return;
        
        messageData = {
          type: 'chat',
          content: message,
          timestamp: new Date().toISOString(),
          character_id: this.selectedCharacter ? this.selectedCharacter.id : null,
          ai_mode: this.currentAIMode
        };
      } else {
        // message已经是一个对象
        messageData = message;
        
        // 确保有时间戳
        if (!messageData.timestamp) {
          messageData.timestamp = new Date().toISOString();
        }
        
        // 添加AI模式信息
        if (!messageData.ai_mode) {
          messageData.ai_mode = this.currentAIMode;
        }
      }
      
      console.log('处理后的消息数据:', messageData);
      
      // 确保消息有用户名
      if (messageData.type === 'chat' && !messageData.username) {
        messageData.username = this.$store.getters.currentUser?.username || '玩家';
        messageData.user_id = this.$store.getters.currentUser?.id || 2; // 确保用户ID不是1 (KP)
      }
      
      // 过滤心跳消息，不显示在UI中
      if (messageData.type === 'heartbeat') {
        console.log('收到心跳消息，不添加到UI:', messageData);
        websocketService.sendMessage(messageData);
        return;
      }
      
      // 先将消息添加到UI，确保即使WebSocket发送失败也能看到消息
      if (messageData.type === 'chat' || messageData.type === 'system') {
        console.log('直接将消息添加到UI:', messageData);
        this.messages.push({...messageData});
        
        // 检查是非AI生成的玩家消息，并且AI KP处于活动状态，则处理该消息
        if (messageData.type === 'chat' && 
            !messageData.ai_generated && 
          this.aiStatus.active) {
          console.log('检测到普通玩家消息，但AI KP功能已移至ChatBox中');
        }
      }
      
      // 检查WebSocket连接状态
      if (!websocketService.isConnected || !websocketService.socket) {
        console.log('WebSocket未连接，尝试重新连接');
        this.showSystemMessage('连接已断开，正在尝试重新连接...');
        this.connectWebSocket();
        
        // 将消息推入队列等待重连
        setTimeout(() => {
          const result = websocketService.sendMessage(messageData);
          console.log('消息发送结果:', result);
        }, 1000);
        return;
      }
      
      console.log('发送消息到WebSocket:', messageData);
      
      // 直接发送消息，不依赖WebSocket的readyState属性
      const sent = websocketService.sendMessage(messageData);
      console.log('消息发送结果:', sent);

      // 如果消息发送成功，清空输入框
      if (typeof message === 'string' && sent) {
        this.messageInput = '';
      }
      
      // 检查是否有待处理的场景描述且玩家回复开始游戏
      if (this.pendingSceneDescription && 
          messageData.type === 'chat' && 
          messageData.content && 
          typeof messageData.content === 'string' &&
          !messageData.ai_generated) {
        
        // 检查玩家消息是否为开始游戏的确认
        const startGameKeywords = ['开始游戏', '开始', '准备好了', '开始冒险', '我准备好了', '出发', '是的'];
        const playerMessage = messageData.content.toLowerCase();
        
        if (startGameKeywords.some(keyword => playerMessage.includes(keyword))) {
          console.log('检测到玩家确认开始游戏');
          
          // 发送玩家的确认消息
          websocketService.sendMessage(messageData);
          
          // 稍后发送场景描述
          setTimeout(() => {
            console.log('发送延迟的场景描述');
            const sceneMessage = {
              type: "chat",
              user_id: 1, // 使用KP的用户ID
              username: "KP",
              content: this.pendingSceneDescription,
              ai_generated: true,
              timestamp: Date.now(),
              ai_mode: this.currentAIMode
            };
            
            // 保存一个标记，表示这是一个重要的AI消息
            this.$store.commit('SET_IMPORTANT_MESSAGE', sceneMessage);
            
            // 直接添加到UI
            this.messages.push({...sceneMessage});
            
            // 发送消息
            websocketService.sendMessage(sceneMessage);
            this.pendingSceneDescription = null; // 清除待处理的场景描述
          }, 1000);
        }
      }
    },
    selectCharacter(character) {
      this.selectedCharacter = character;
      this.showCharacterModal = false;
      
      // 发送系统消息通知角色选择
      const messageData = {
        type: 'system',
        content: `${this.$store.getters.currentUser?.username || '玩家'} 选择了角色: ${character.name}`,
        timestamp: Date.now()
      };
      this.sendMessage(messageData);
      
      // 同步选中角色的状态
      this.syncSelectedCharacterStatus();
      this.updateCurrentCharacterId(character.id);
    },
  
    // 新增功能
    playDiceSound() {
      // 播放骰子声音
      const audio = new Audio('/sounds/');
      audio.volume = 0.5;
      audio.play().catch(err => console.warn('无法播放音效:', err));
    },
    sendTypingStatus(isTyping) {
      // 防止过于频繁发送状态更新
      const now = Date.now();
      if (now - this.lastTypingTime < 2000 && isTyping) {
        return;
      }
      
      // 发送正在输入状态
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        websocketService.sendMessage({
          type: 'typing',
          username: this.$store.getters.currentUser?.username || '玩家',
          isTyping: isTyping
        });
        this.lastTypingTime = now;
      }
    },
    toggleDarkMode() {
      this.$store.dispatch('toggleTheme');
    },
    changeFontSize(change) {
      this.fontSize = Math.max(12, Math.min(24, this.fontSize + change));
      document.documentElement.style.setProperty('--base-font-size', `${this.fontSize}px`);
      this.safeSetItem('fontSize', this.fontSize);
    },
    saveNotes(notes) {
      if (notes !== undefined) {
        this.gameNotes = notes;
      }
            this.safeSetItem(`notes_${this.internalRoomId}`, this.gameNotes);
    },
    saveSettings() {
      this.safeSetItem('fontSize', this.fontSize);
      this.safeSetItem('playSounds', this.playSounds);
      this.safeSetItem('showNotifications', this.showNotifications);
      
      this.showSettingsModal = false;
    },
    loadUserSettings() {
      // 加载AI模式设置
      this.currentAIMode = this.safeGetItem('ai_mode') || 'normal';
      
      // 加载之前保存的笔记
      this.gameNotes = this.safeGetItem(`notes_${this.internalRoomId}`) || '';
      
      // 加载其他设置
      const savedFontSize = this.safeGetItem('fontSize');
      if (savedFontSize) {
        this.fontSize = parseInt(savedFontSize);
        document.documentElement.style.setProperty('--base-font-size', `${this.fontSize}px`);
      }
      
      const playSounds = this.safeGetItem('playSounds');
      if (playSounds) {
        this.playSounds = playSounds === 'true';
      }
      
      const showNotifications = this.safeGetItem('showNotifications');
      if (showNotifications) {
        this.showNotifications = showNotifications === 'true';
      }
      
      // 加载面板可见性设置
      const panelSettings = this.safeGetItem(`panels_${this.internalRoomId}`);
      if (panelSettings) {
        try {
          this.visiblePanels = JSON.parse(panelSettings);
          console.log('已加载面板设置:', this.visiblePanels);
        } catch (e) {
          console.error('解析面板设置失败', e);
        }
      } else {
        // 如果没有保存的设置，设置默认值
        this.visiblePanels.character = true;
        this.visiblePanels.dice = true;
        this.visiblePanels.privateChat = false; // 默认不显示私聊面板
        console.log('使用默认面板设置');
      }
      
      // 单独检查私聊面板设置
      const privateChatSetting = this.safeGetItem(`panel_privateChat_${this.internalRoomId}`);
      if (privateChatSetting) {
        this.visiblePanels.privateChat = privateChatSetting === 'visible';
        console.log('已加载私聊面板设置:', this.visiblePanels.privateChat ? '显示' : '隐藏');
      }
      
      // 加载AI状态设置
      const aiStatusSettings = this.safeGetItem(`ai_status_${this.internalRoomId}`);
      if (aiStatusSettings) {
        try {
          this.aiStatus = JSON.parse(aiStatusSettings);
        } catch (e) {
          console.error('解析AI状态设置失败', e);
        }
      }
      
      // 确保状态与面板可见性一致
      this.aiStatus.active = this.visiblePanels.ai;
      
      // 同步面板状态到其他变量
      this.showCharacterSheet = this.visiblePanels.character;
      this.showDiceRoller = this.visiblePanels.dice;
    },
    sendDesktopNotification(message) {
      // 检查浏览器是否支持通知
      if ("Notification" in window) {
        if (Notification.permission === "granted") {
          new Notification(`${message.username || '用户'} 在 ${this.room.name}`, {
            body: message.content,
            icon: '/favicon.ico'
          });
        } else if (Notification.permission !== "denied") {
          Notification.requestPermission();
        }
      }
    },
    // 通用面板切换方法
    togglePanel(panelName, forceClose = false) {
      if (forceClose) {
        this.visiblePanels[panelName] = false;
      } else {
        this.visiblePanels[panelName] = !this.visiblePanels[panelName];
      }
      
      // 保存面板状态到本地存储
      try {
        // 保存单独的面板状态
        this.safeSetItem(`panel_${panelName}_${this.internalRoomId}`, 
          this.visiblePanels[panelName] ? 'visible' : 'hidden');
          
        // 同时保存整个面板状态对象
        this.safeSetJSON(`panels_${this.internalRoomId}`, this.visiblePanels);
        
        // 同步面板状态到显示变量
        if (panelName === 'character') {
          this.showCharacterSheet = this.visiblePanels.character;
        } else if (panelName === 'dice') {
          this.showDiceRoller = this.visiblePanels.dice;
        }
      } catch (e) {
        console.warn('无法保存面板状态到本地存储:', e);
      }
      
      console.log(`面板 ${panelName} 状态: ${this.visiblePanels[panelName] ? '显示' : '隐藏'}`);
    },
    handleTyping() {
      // 发送正在输入状态
      this.sendTypingStatus(true);
    },
    handleTypingStopped() {
      // 发送停止输入状态
      this.sendTypingStatus(false);
    },
    // 添加显示系统消息的方法
    showSystemMessage(content) {
      this.$store.dispatch('addMessage', {
        type: 'system',
        content: content,
        timestamp: new Date().toISOString()
      });
    },
    handleSanityUpdate(data) {
      // 更新角色的理智值
      if (this.selectedCharacter && this.selectedCharacter.id === data.characterId) {
        // 创建一个新对象，避免直接修改props
        this.selectedCharacter = {
          ...this.selectedCharacter,
          sanity: data.newSanity
        };
        
        // 发送系统消息通知理智值变化
        const messageData = {
          type: 'system',
          content: `${this.selectedCharacter.name} 的理智值变为 ${data.newSanity}`,
          timestamp: Date.now()
        };
        this.sendMessage(messageData);
      }
    },


    // 检查当前用户是否是房主
    checkRoomOwnership() {
      try {
        const currentUser = this.$store.getters.currentUser;
        
        if (!currentUser) {
          console.log('未登录用户，默认不是房主');
          this.isRoomOwner = false;
          return;
        }
        
        if (!this.room) {
          console.log('房间数据不存在，无法检查房主状态');
          this.isRoomOwner = false;
          return;
        }
        
        // 如果是模拟房间，默认当前用户是房主
        if (this.room.is_mock) {
          console.log('模拟房间，当前用户设为房主');
        this.isRoomOwner = true;
          return;
        }
        
        // 检查当前用户是否是房间创建者
        this.isRoomOwner = this.room.keeper_id === currentUser.id;
        console.log('房主检查结果:', this.isRoomOwner ? '是房主' : '不是房主');
      } catch (error) {
        console.error('检查房主状态失败:', error);
        this.isRoomOwner = false;
      }
    },
    // 检查系统组件状态
    checkSystemStatus() {
      console.log('检查系统组件状态...');
      
      // 检查WebSocket连接
      const wsStatus = websocketService.isConnected ? '已连接' : '未连接';
      console.log(`WebSocket状态: ${wsStatus}`);
      
      // 检查服务锁状态
      console.log('服务锁状态:', websocketService.resourceLocks);
      
      // 检查消息队列
      console.log(`消息队列长度: ${websocketService.messageQueue.length}`);
      
      // 检查AI组件状态
      console.log('AI组件状态:', this.aiStatus);
      
      // 检查可见面板状态
      console.log('可见面板:', this.visiblePanels);
      
      if (!websocketService.isConnected) {
        console.log('WebSocket未连接，尝试重连');
        this.showSystemMessage('正在重新建立连接...');
        this.connectWebSocket();
      }
      
      // 确保聊天输入框可用
      this.ensureChatInputEnabled();
      
      // 确认组件正常工作，主动发送一条系统消息
      this.showSystemMessage('系统检查完成，您可以正常发送消息');
    },
    // 确保聊天输入框可用
    ensureChatInputEnabled() {
      console.log('确保聊天输入框可用');
      setTimeout(() => {
        // 查找所有可能的输入框并确保它们可用
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
          textarea.disabled = false;
          console.log('已启用输入框:', textarea);
        });
        
        // 如果有ChatBox引用，尝试直接操作
        if (this.$refs.chatBox && this.$refs.chatBox.$refs.messageTextarea) {
          this.$refs.chatBox.$refs.messageTextarea.disabled = false;
          console.log('已直接启用ChatBox输入框');
        }
      }, 500);
    },
    // 处理AI设置更新
    handleAISettingsUpdated(settings) {
      console.log('AI设置已更新:', settings);
      this.showSystemMessage('AI模型设置已更新');
      
      // 从本地存储加载AI设置并应用
      try {
        const parsedSettings = this.safeGetJSON(`aiSettings_${this.roomId}`);
        if (parsedSettings) {
          console.log('应用本地AI设置:', parsedSettings);
          
          // 如果有AI模式设置，更新当前模式
          if (parsedSettings.ai_mode) {
            this.updateAIMode(parsedSettings.ai_mode);
          }
        }
      } catch (error) {
        console.error('加载本地AI设置失败:', error);
      }
    },
    
    // 更新AI模式
    updateAIMode(mode) {
      if (this.currentAIMode !== mode) {
        console.log(`AI模式从 ${this.currentAIMode} 切换到 ${mode}`);
        this.currentAIMode = mode;
        
        // 保存到本地存储
        this.safeSetItem('ai_mode', mode);
        
        // 通知WebSocket服务
        websocketService.setAIMode(mode);
        
        // 显示系统消息
        this.showSystemMessage(`AI模式已切换到: ${mode === 'normal' ? '普通模式' : '剧本模式'}`);
        
        // 如果切换到剧本模式，自动打开群聊面板
        if (mode === 'script' && !this.visiblePanels.groupChat) {
          setTimeout(() => {
            this.togglePanel('groupChat', false);
          }, 500);
        }
      }
    },
    // 跳转到AI设置
    goToAISettings() {
      this.togglePanel('settings');
      setTimeout(() => {
        // 聚焦到AI设置选项卡，如果有多个选项卡的话
        this.$refs.aiSettings && this.$refs.aiSettings.focus();
      }, 300);
    },
    // 关闭配置提醒
    dismissConfigAlert() {
      this.showConfigAlert = false;
      // 可以保存到本地存储，避免重复显示
      this.safeSetItem('aiConfigAlertDismissed', 'true');
    },
    // 检查API密钥状态
    async checkApiKeyStatus() {
      try {
        const response = await apiService.aiSettings.getSettings(this.roomId);
        if (response.data && response.data.success) {
          const settings = response.data.settings;
          
          // 将设置保存到store
          this.$store.commit('UPDATE_AI_SETTINGS', {
            apiKey: settings.api_key,
            apiUrl: settings.api_url,
            modelName: settings.model_name
          });
          
          // 如果之前通过测试，则标记为有效
          const apiTested = this.safeGetItem(`apiTested_${this.roomId}`);
          if (apiTested === 'true') {
            this.$store.commit('SET_API_VALID', true);
            this.showConfigAlert = false;
          } else {
            this.$store.commit('SET_API_VALID', false);
            this.showConfigAlert = true;
          }
        }
      } catch (error) {
        console.error('检查API密钥状态失败:', error);
      }
    },
    // 同步选中角色的状态
    syncSelectedCharacterStatus() {
      if (!this.selectedCharacter || !this.selectedCharacter.id) return;
      
      // 请求从服务器同步角色状态
      this.$store.dispatch('syncCharacterData', this.selectedCharacter.id)
        .then(() => {
          console.log('角色状态同步完成:', this.selectedCharacter.name);
          this.showSystemMessage(`角色 ${this.selectedCharacter.name} 数据已同步`);
        })
        .catch(error => {
          console.error('角色状态同步失败:', error);
        });
    },
    // 处理角色同步事件
    handleCharacterSync(characterId) {
      console.log(`收到角色同步事件: ${characterId}`);
      
      // 更新角色显示
      if (this.selectedCharacter && this.selectedCharacter.id === characterId) {
        // 从store获取最新的角色数据
        const updatedCharacter = this.$store.getters.userCharacters.find(c => c.id === characterId);
        if (updatedCharacter) {
          this.selectedCharacter = updatedCharacter;
          console.log('角色数据已更新:', updatedCharacter.name);
        }
      }
    },
    // 设置定时同步角色状态
    setupCharacterSyncTimer() {
      // 每隔5分钟同步一次角色状态
      this.characterSyncTimer = setInterval(() => {
        if (this.selectedCharacter && this.selectedCharacter.id) {
          console.log('定时同步角色状态:', this.selectedCharacter.name);
          this.syncSelectedCharacterStatus();
        }
      }, 5 * 60 * 1000); // 5分钟
    },
    // 移除场景相关方法
    
    handleSceneSave(data) {
      // 保存场景数据
      this.scenes[data.sceneIndex] = data.scene;
      
      // 发送场景更新消息
      this.sendSystemMessage(`场景 "${data.scene.name}" 已更新`);
      
      // 持久化场景数据
      this.saveScenesToServer();
    },
    
    saveScenesToServer() {
      // 这里可以添加将场景数据保存到服务器的逻辑
      // 例如通过WebSocket发送或API调用
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({
          type: 'save-scenes',
          scenes: this.scenes
        }));
      }
    },
    
    loadScenesFromServer() {
      // 从服务器加载场景数据
      // 这里可以添加API调用或从WebSocket接收场景数据的逻辑
    },
    handleNoteSaved(note) {
      // 可以在这里处理笔记保存事件
      console.log('笔记已保存:', note);
    },
    updateCurrentCharacterId(characterId) {
      this.currentCharacterId = characterId;
    },
    // 切换骰子面板显示状态
    toggleDiceRoller() {
      this.visiblePanels.dice = !this.visiblePanels.dice;
      this.showDiceRoller = this.visiblePanels.dice;
    },
    // 切换角色卡面板显示状态
    toggleCharacterSheet() {
      this.visiblePanels.character = !this.visiblePanels.character;
      this.showCharacterSheet = this.visiblePanels.character;
    },
    // 切换笔记面板显示状态
    toggleNotes() {
      this.showNotes = !this.showNotes;
    },
    // 切换地图面板显示状态
    toggleMap() {
      this.visiblePanels.map = !this.visiblePanels.map;
    },
    // 切换线索墙面板显示状态
    toggleClueBoard() {
      this.togglePanel('clueBoard');
    },
    // 切换音频系统面板显示状态
    toggleAudioSystem() {
      this.togglePanel('audioSystem');
    },
    // 切换语音聊天面板显示状态
    toggleVoiceChat() {
      this.togglePanel('voiceChat');
    },
    // 切换私聊面板显示状态
    togglePrivateChat() {
      this.togglePanel('privateChat');
      console.log('私聊面板状态:', this.visiblePanels.privateChat ? '显示' : '隐藏');
    },
    // 切换群聊面板显示状态
    toggleGroupChat() {
      this.togglePanel('groupChat');
    },
    // 处理语音聊天连接事件
    handleVoiceConnected() {
      this.showSystemMessage('您已加入语音聊天');
    },
    handleVoiceDisconnected() {
      this.showSystemMessage('您已离开语音聊天');
    },
    // 添加骰子投掷处理方法
    handleRollDice(diceData) {
      if (!diceData) return;
      
      // 创建骰子消息
      const diceMessage = {
        type: 'roll',
        count: diceData.count || 1,
        faces: diceData.faces || 100,
        modifier: diceData.modifier || 0,
        username: this.currentUser?.username || '玩家',
        timestamp: new Date().toISOString()
      };
      
      // 发送到WebSocket
      if (websocketService.isConnected) {
        websocketService.sendMessage(diceMessage);
      } else {
        // 本地模拟骰子结果
        const results = [];
        let total = 0;
        for (let i = 0; i < diceMessage.count; i++) {
          const result = Math.floor(Math.random() * diceMessage.faces) + 1;
          results.push(result);
          total += result;
        }
        total += diceMessage.modifier;
        
        // 显示本地骰子结果
        this.showSystemMessage(`${diceMessage.username} 投掷 ${diceMessage.count}D${diceMessage.faces}${diceMessage.modifier > 0 ? '+' + diceMessage.modifier : ''} = ${total} [${results.join(', ')}${diceMessage.modifier > 0 ? ' + ' + diceMessage.modifier : ''}]`);
      }
    },
    // 添加脚本上传处理方法
    handleUploadScript(file) {
      if (!file) return;
      
      // 显示上传中消息
      this.showSystemMessage('正在上传剧本文件...');
      
      // 模拟上传过程
      setTimeout(() => {
        this.showSystemMessage('剧本文件上传成功！');
      }, 1500);
      
      // 实际项目中，这里应该调用API上传文件
      console.log('上传剧本文件:', file.name);
    },
    toggleToolbar() {
      this.isToolbarCollapsed = !this.isToolbarCollapsed;
      console.log('工具栏状态切换:', this.isToolbarCollapsed ? '已折叠' : '已展开');
      
      // 保存折叠状态到本地存储
      this.safeSetItem(`toolbar_collapsed_${this.roomId}`, this.isToolbarCollapsed);
    },
    checkMobileDevice() {
      this.isMobile = window.innerWidth < 768;
      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');
    },
    // WebSocket事件处理函数
    handleChatMessage(message) {
      console.log('收到聊天消息:', message);
      this.$store.dispatch('addMessage', message);
    },
    
    handleUserJoined(data) {
      console.log('用户加入房间:', data);
      
      // 添加到在线用户列表
      if (!this.onlineUsers.find(user => user.id === data.user_id)) {
        this.onlineUsers.push({
          id: data.user_id,
          username: data.username
        });
      }
      
      // 显示系统消息
      this.showSystemMessage(`${data.username} 加入了房间`);
    },
    
    handleUserLeft(data) {
      console.log('用户离开房间:', data);
      
      // 从在线用户列表中移除
      const index = this.onlineUsers.findIndex(user => user.id === data.user_id);
      if (index !== -1) {
        this.onlineUsers.splice(index, 1);
      }
      
      // 显示系统消息
      this.showSystemMessage(`${data.username} 离开了房间`);
    },
    
    handleTypingEvent(data) {
      if (data.user_id !== this.$store.getters.currentUser?.id) {
        this.isTyping = data.typing;
        this.typingUsername = data.username;
        
        // 设置超时，如果长时间没有收到新的typing事件，则自动清除
        clearTimeout(this.typingTimer);
        if (this.isTyping) {
          this.typingTimer = setTimeout(() => {
            this.isTyping = false;
          }, 5000);
        }
      }
    },
    
    handleDiceRoll(message) {
      console.log('收到骰子消息:', message);
      
      // 创建骰子结果消息
      const diceMessage = {
        type: 'system',
        content: `${message.username} 投掷 ${message.count}D${message.faces}${message.modifier > 0 ? '+' + message.modifier : ''} = ${message.total} [${message.results.join(', ')}${message.modifier > 0 ? ' + ' + message.modifier : ''}]`,
        timestamp: message.timestamp || new Date().toISOString(),
        roll_data: {
          count: message.count,
          faces: message.faces,
          modifier: message.modifier,
          results: message.results,
          total: message.total
        }
      };
      
      // 添加到消息列表
      this.$store.dispatch('addMessage', diceMessage);
      
      // 添加到骰子历史记录
      this.diceHistory.push({
        ...message,
        timestamp: new Date().toISOString()
      });
      
      // 保持历史记录不超过20条
      if (this.diceHistory.length > 20) {
        this.diceHistory.shift();
      }
    },
    
    // 这里已删除重复的handleSkillCheck和handleCharacterUpdate方法
    // 检测设备类型
    detectDevice() {
      this.isMobile = window.innerWidth < 768;
      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');
    },
    // 处理页面关闭前的操作
    handleBeforeUnload(event) {
      this.saveNotes();
      // 提示用户确认离开
      if (this.hasUnsavedChanges) {
        event.preventDefault();
        event.returnValue = '您有未保存的更改，确定要离开吗？';
        return event.returnValue;
      }
    },
    
    // 切换全屏模式
    toggleFullScreen() {
      try {
        // 使用.room-container作为全屏目标元素
        const elem = document.querySelector('.room-container');
        if (!elem) {
          console.error('找不到.room-container元素');
          this.showSystemMessage('全屏切换失败: 找不到目标元素');
          return;
        }
        
        console.log('开始全屏操作，目标元素:', elem);
        
        // 检查当前是否在全屏模式
        const isFullScreen = 
          document.fullscreenElement || 
          document.webkitFullscreenElement || 
          document.mozFullScreenElement || 
          document.msFullscreenElement;
        
        if (!isFullScreen) {
          // 进入全屏模式
          console.log('尝试进入全屏模式');
          
          // 保存请求全屏前的状态
          elem.setAttribute('data-was-fullscreen', 'true');
          
          // 为目标元素添加全屏样式
          elem.classList.add('preparing-fullscreen');
          
          // 尝试所有可能的全屏API
          const requestFullScreen = elem.requestFullscreen || 
                                   elem.webkitRequestFullscreen || 
                                   elem.mozRequestFullScreen || 
                                   elem.msRequestFullscreen;
                                   
          if (requestFullScreen) {
            // 使用apply调用适当的方法
            requestFullScreen.apply(elem).then(() => {
              console.log('全屏请求成功');
              elem.classList.add('in-fullscreen');
              elem.classList.remove('preparing-fullscreen');
              this.showSystemMessage('已进入全屏模式，再次双击顶部栏或按ESC可退出');
            }).catch(err => {
              console.error('全屏请求被拒绝:', err);
              elem.classList.remove('preparing-fullscreen');
              this.showSystemMessage('进入全屏失败: ' + err.message);
            });
          } else {
            // 如果浏览器不支持全屏API，尝试使用CSS模拟全屏
            console.warn('浏览器不支持全屏API，使用CSS模拟全屏');
            elem.classList.add('in-fullscreen');
            elem.classList.remove('preparing-fullscreen');
            document.body.classList.add('fullscreen-mode');
            this.showSystemMessage('已使用模拟全屏模式，再次双击顶部栏可退出');
          }
        } else {
          // 退出全屏模式
          console.log('尝试退出全屏模式');
          
          // 尝试所有可能的退出全屏API
          const exitFullScreen = document.exitFullscreen || 
                               document.webkitExitFullscreen || 
                               document.mozCancelFullScreen || 
                               document.msExitFullscreen;
                               
          if (exitFullScreen) {
            exitFullScreen.apply(document).then(() => {
              console.log('已退出全屏模式');
              elem.classList.remove('in-fullscreen');
              this.showSystemMessage('已退出全屏模式');
            }).catch(err => {
              console.error('退出全屏失败:', err);
              this.showSystemMessage('退出全屏失败: ' + err.message);
            });
          } else {
            // 如果使用CSS模拟全屏，则移除相关样式
            elem.classList.remove('in-fullscreen');
            document.body.classList.remove('fullscreen-mode');
            this.showSystemMessage('已退出全屏模式');
          }
        }
      } catch (error) {
        console.error('全屏切换失败:', error);
        this.showSystemMessage('全屏切换失败: ' + error.message);
        
        // 移除可能残留的全屏状态
        try {
          const elem = document.querySelector('.room-container');
          if (elem) {
            elem.classList.remove('preparing-fullscreen');
            elem.classList.remove('in-fullscreen');
          }
          document.body.classList.remove('fullscreen-mode');
        } catch (e) {
          console.error('清理全屏状态失败:', e);
        }
      }
    },
    // 处理全屏状态变化
    handleFullScreenChange() {
      try {
        const isFullScreen = !!document.fullscreenElement || 
                            !!document.webkitFullscreenElement || 
                            !!document.mozFullScreenElement || 
                            !!document.msFullscreenElement;
        
        const container = document.querySelector('.room-container');
        if (!container) return;
        
        if (isFullScreen) {
          console.log('已进入全屏模式');
          // 确保应用全屏样式
          document.body.classList.add('fullscreen-mode');
          container.classList.add('in-fullscreen');
          
          // 不显示全屏提示
        } else {
          console.log('已退出全屏模式');
          // 移除全屏样式
          document.body.classList.remove('fullscreen-mode');
          container.classList.remove('in-fullscreen');
          
          // 移除可能存在的全屏提示
          const notice = container.querySelector('.fullscreen-notice');
          if (notice) {
            notice.parentNode.removeChild(notice);
          }
        }
      } catch (error) {
        console.error('处理全屏状态变化时出错:', error);
      }
    },
    // 处理剧本模式设置相关方法
    openChatBoxSettings() {
      // 直接调用ChatBox组件的方法（通过引用）
      if (this.$refs.chatBox) {
        this.$refs.chatBox.showAISettings = true;
      } else {
        console.warn('找不到ChatBox组件引用');
      }
    },
    
    handleMemoryCleared() {
      this.showSystemMessage('AI记忆已清除，将重新开始对话');
    },
    
    handleScriptSettingsSaved(settings) {
      this.showSystemMessage('剧本模式设置已保存');
      
      // 如果有API密钥，更新到全局设置
      if (settings.apiKey) {
        this.$store.commit('UPDATE_AI_SETTINGS', {
          apiKey: settings.apiKey
        });
      }
    },
    
    // 打开剧本查看器
    openScenarioViewer(content, title, fileSize) {
      this.scenarioContent = content || '';
      this.scenarioTitle = title || '未命名剧本';
      this.scenarioFileSize = fileSize || '';
      this.togglePanel('scenarioViewer');
    },
    
    handleScriptUploaded(fileInfo) {
      this.showSystemMessage(`剧本文件 ${fileInfo.fileName} 已上传成功`);
    },
    
    // 添加游戏存档相关方法
    toggleGameSaves() {
      this.togglePanel('gameSaves');
    },
    
    // 处理存档创建成功
    handleSaveCreated(save) {
      this.showSystemMessage(`存档 "${save.name}" 创建成功`);
    },
    
    // 处理存档更新成功
    handleSaveUpdated(save) {
      this.showSystemMessage(`存档 "${save.name}" 更新成功`);
    },
    
    // 处理存档删除成功
    handleSaveDeleted() {
      this.showSystemMessage('存档已成功删除');
    },
    
    // 处理自动存档通知
    handleSaveAutoCreated(save) {
      this.showSystemMessage(`自动存档已完成`, 'info', 3000);
    },
    
    // 处理存档消息
    handleSaveMessage(message) {
      this.showSystemMessage(message);
    },
    
    // 处理存档版本恢复
    handleSaveRestored(save) {
      this.showSystemMessage(`存档已恢复到版本 ${save.version}`);
      // 重新加载存档内容
      this.handleSaveLoad(save);
    },
    
    // 处理存档预览
    handleSavePreview(saveData) {
      // 这里可以实现预览功能，比如临时显示存档内容
      this.showSystemMessage('正在预览存档历史版本，加载完成后点击"恢复"可应用此版本');
      
      // 临时存储当前状态
      this.tempState = {
        messages: [...this.messages],
        characters: [...this.characters],
        scenes: [...this.scenes],
        currentSceneIndex: this.currentSceneIndex,
        diceHistory: [...this.diceHistory]
      };
      
      // 临时加载预览内容
      this.messages = saveData.save_data.messages || [];
      this.characters = saveData.save_data.characters || [];
      this.scenes = saveData.save_data.scenes || [];
      this.currentSceneIndex = saveData.save_data.currentScene || 0;
      this.diceHistory = saveData.save_data.diceHistory || [];
      
      // 设置预览模式
      this.isPreviewMode = true;
    },
    
    // 退出预览模式
    exitPreviewMode() {
      if (!this.isPreviewMode || !this.tempState) return;
      
      // 恢复临时存储的状态
      this.messages = this.tempState.messages;
      this.characters = this.tempState.characters;
      this.scenes = this.tempState.scenes;
      this.currentSceneIndex = this.tempState.currentSceneIndex;
      this.diceHistory = this.tempState.diceHistory;
      
      // 重置预览模式
      this.isPreviewMode = false;
      this.tempState = null;
      
      this.showSystemMessage('已退出预览模式');
    },
    
    // 处理存档加载
    async handleSaveLoad(save) {
      try {
        // 使用Vuex加载存档
        await this.$store.dispatch('loadSave', save);
        
        // 更新UI状态
        this.messages = this.$store.state.messages || [];
        this.characters = this.$store.state.characters || [];
        this.scenes = this.$store.state.scenes || [];
        this.currentSceneIndex = this.$store.state.currentSceneIndex || 0;
        this.diceHistory = this.$store.state.diceHistory || [];
        
        // 关闭存档面板
        this.togglePanel('gameSaves', true);
        
        this.showSystemMessage(`存档 "${save.name}" 加载成功`);
      } catch (error) {
        console.error('加载存档失败:', error);
        this.showSystemMessage('加载存档失败');
      }
    },
    
    // 处理存档错误
    handleSaveError(errorMessage) {
      this.showSystemMessage(errorMessage);
    },
    
    // 收集游戏状态用于存档
    collectGameState(customData) {
      // 添加额外的状态数据
      customData.roomName = this.room.name;
      customData.roomDescription = this.room.description;
      customData.onlineUsers = this.onlineUsers.map(user => ({
        id: user.id,
        username: user.username
      }));
      
      // 添加其他需要保存的状态
    },
    
    // 快速存档功能
    async quickSave() {
      try {
        // 获取当前游戏状态
        const gameState = this.collectGameStateForQuickSave();
        
        const saveData = {
          name: `快速存档 - ${new Date().toLocaleString('zh-CN')}`,
          description: '自动创建的快速存档',
          room_id: parseInt(this.internalRoomId),
          creator_id: this.$store.getters.currentUser?.id || 1,
          save_data: gameState,
          thumbnail: null,
          is_auto_save: false // 快速存档不是自动存档
        };
        
        let response;
        
        if (this.quickSaveId) {
          // 更新现有的快速存档
          response = await apiService.gameSaves.updateSave(this.quickSaveId, {
            name: saveData.name,
            description: saveData.description,
            save_data: saveData.save_data
          });
        } else {
          // 创建新的快速存档
          response = await apiService.gameSaves.createSave(saveData);
        }
        
        if (response.data) {
          this.quickSaveId = response.data.id;
          this.showSystemMessage('快速存档成功');
        }
      } catch (error) {
        console.error('快速存档失败:', error);
        this.showSystemMessage('快速存档失败');
      }
    },
    
    // 快速读档功能
    async quickLoad() {
      if (!this.quickSaveId) {
        this.showSystemMessage('没有可用的快速存档');
        return;
      }
      
      try {
        // 获取快速存档数据
        const response = await apiService.gameSaves.getSave(this.quickSaveId);
        
        if (response.data) {
          // 使用Vuex加载存档
          await this.$store.dispatch('loadSave', response.data);
          
          // 更新UI状态
          this.messages = this.$store.state.messages || [];
          this.characters = this.$store.state.characters || [];
          this.scenes = this.$store.state.scenes || [];
          this.currentSceneIndex = this.$store.state.currentSceneIndex || 0;
          this.diceHistory = this.$store.state.diceHistory || [];
          
          this.showSystemMessage('快速读档成功');
        }
      } catch (error) {
        console.error('快速读档失败:', error);
        this.showSystemMessage('快速读档失败');
      }
    },
    
    // 收集游戏状态用于快速存档
    collectGameStateForQuickSave() {
      // 收集当前游戏状态
      const gameState = {
        timestamp: Date.now(),
        messages: this.messages,
        characters: this.characters,
        scenes: this.scenes,
        currentScene: this.currentSceneIndex,
        diceHistory: this.diceHistory,
        notes: this.$store.state.notes || {},
        clues: this.$store.state.clues || [],
        aiSettings: this.$store.state.aiSettings || {},
        customData: {
          roomName: this.room.name,
          roomDescription: this.room.description,
          onlineUsers: this.onlineUsers.map(user => ({
            id: user.id,
            username: user.username
          }))
        }
      };
      
      return gameState;
    },
    // 处理键盘快捷键 - 已移除所有快捷键绑定
    handleKeyDown(event) {
      // 所有快捷键绑定已移除
    },
    // 处理键盘快捷键 - 已移除所有快捷键绑定
    handleKeyboardShortcuts(event) {
      // 所有快捷键绑定已移除
    },
    // 更新窗口尺寸
    updateWindowSize() {
      this.windowWidth = window.innerWidth;
      this.windowHeight = window.innerHeight;
    },
    
    // 切换存档选项面板
    toggleSaveOptions() {
      this.showSaveOptions = !this.showSaveOptions;
      
      // 点击其他区域关闭面板
      if (this.showSaveOptions) {
        setTimeout(() => {
          const closePanel = (e) => {
            const panel = document.querySelector('.save-options-panel');
            const button = document.querySelector('.toolbar-btn.active');
            if (panel && !panel.contains(e.target) && (!button || !button.contains(e.target))) {
              this.showSaveOptions = false;
              document.removeEventListener('click', closePanel);
            }
          };
          document.addEventListener('click', closePanel);
        }, 100);
      }
    },
    // 处理查看剧本事件
    handleViewScenario(data) {
      this.openScenarioViewer(data.content, data.title, data.fileSize);
    },
    
    // 打开公告展示面板
    openAnnouncement() {
      // 如果没有公告内容且是房主，显示默认编辑提示
      if (!this.announcementContent && this.isRoomOwner) {
        this.announcementContent = '在此输入公告内容，玩家可以查看和复制这些信息。\n\n可以放置：\n- 规则提示\n- 背景设定\n- 重要信息\n- 其他需要玩家了解的内容';
      }
      
      this.togglePanel('announcement');
    },
    
    // 处理公告保存
    handleAnnouncementSave(data) {
      this.announcementContent = data.content;
      this.announcementTitle = data.title;
      this.announcementTime = new Date().toLocaleString();
      
      // 保存到本地存储
      this.saveAnnouncementToLocalStorage();
      
      // 使用新的方法发送公告更新消息
      if (websocketService.isConnected) {
        websocketService.sendAnnouncementUpdate(this.internalRoomId, {
          content: data.content,
          title: data.title,
          timestamp: data.timestamp
        });
      }
      
      this.showSystemMessage('公告已更新');
    },
    
    // 保存公告到本地存储
    saveAnnouncementToLocalStorage() {
      try {
        const announcementData = {
          content: this.announcementContent,
          title: this.announcementTitle,
          time: this.announcementTime,
          roomId: this.internalRoomId
        };
        
        this.safeSetJSON(`room_announcement_${this.internalRoomId}`, announcementData);
      } catch (error) {
        console.error('保存公告到本地存储失败:', error);
      }
    },
    
    // 从本地存储加载公告
    loadAnnouncementFromLocalStorage() {
      try {
        const announcementData = this.safeGetJSON(`room_announcement_${this.internalRoomId}`);
        if (announcementData) {
          this.announcementContent = announcementData.content || '';
          this.announcementTitle = announcementData.title || '房间公告';
          this.announcementTime = announcementData.time || new Date().toLocaleString();
        }
      } catch (error) {
        console.error('从本地存储加载公告失败:', error);
      }
    },
    
    // 处理WebSocket接收到的公告更新
    handleAnnouncementUpdate(data) {
      // 只有非房主才接收更新，房主是发送方
      if (!this.isRoomOwner) {
        this.announcementContent = data.content;
        this.announcementTitle = data.title;
        this.announcementTime = new Date(data.timestamp).toLocaleString();
        
        // 保存到本地存储
        this.saveAnnouncementToLocalStorage();
        
        // 显示通知
        this.showSystemMessage('房主更新了公告，点击顶部公告按钮查看');
      }
    },
    
    // 从本地存储加载房间名称
    loadRoomNameFromLocalStorage() {
      try {
        // 确保房间ID是数字
        const roomId = parseInt(this.internalRoomId) || this.room.id;
        
        console.log('尝试从本地存储加载房间名称:', {
          roomId: roomId,
          internalRoomId: this.internalRoomId
        });
        
        const storedName = this.safeGetItem(`room_name_${roomId}`);
        if (storedName) {
          console.log('找到存储的房间名称:', storedName);
          this.room.name = storedName;
          
          // 同步更新到store
          if (this.$store.state.rooms && this.$store.state.rooms.length > 0) {
            this.$store.commit('UPDATE_ROOM_LIST_NAME', { 
              roomId: roomId, 
              name: storedName 
            });
          }
        } else {
          console.log('未找到存储的房间名称');
        }
      } catch (error) {
        console.error('从本地存储加载房间名称失败:', error);
      }
    },
    
    // 开始编辑房间名称
    startEditRoomName() {
      if (!this.isRoomOwner) return;
      
      this.isEditingRoomName = true;
      this.editableRoomName = this.room.name || '';
      
      // 等待DOM更新后聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.roomNameInput) {
          this.$refs.roomNameInput.focus();
        }
      });
    },
    
    // 保存房间名称
    async saveRoomName() {
      if (!this.isRoomOwner) return;
      
      const newName = this.editableRoomName.trim();
      if (!newName) {
        this.cancelEditRoomName();
        return;
      }
      
      // 调试信息
      console.log('保存房间名称:', {
        roomId: this.internalRoomId,
        oldName: this.room.name,
        newName: newName,
        roomObject: this.room
      });
      
      // 更新房间名称
      if (newName !== this.room.name) {
        // 确保房间ID是数字
        const roomId = parseInt(this.internalRoomId) || this.room.id;
        
        // 使用store的action更新房间名称
        await this.$store.dispatch('updateRoomName', {
          roomId: roomId,
          name: newName
        });
        
        // 如果有WebSocket连接，发送房间名称更新
        if (websocketService.isConnected) {
          websocketService.sendMessage({
            type: 'room_name_update',
            roomId: roomId,
            name: newName
          });
        }
        
        this.showSystemMessage('房间名称已更新');
      }
      
      this.isEditingRoomName = false;
    },
    
    // 取消编辑房间名称
    cancelEditRoomName() {
      this.isEditingRoomName = false;
    },
    
    // 处理房间名称更新
    handleRoomNameUpdate(data) {
      if (!this.isRoomOwner) {
        // 确保房间ID是数字
        const roomId = parseInt(this.internalRoomId) || this.room.id;
        
        console.log('接收到房间名称更新:', {
          roomId: roomId,
          receivedData: data
        });
        
        // 使用store的action更新房间名称
        this.$store.dispatch('updateRoomName', {
          roomId: roomId,
          name: data.name
        });
        this.showSystemMessage('房主更新了房间名称');
      }
    },

    // 新功能相关方法
    toggleCombat() {
      this.togglePanel('combat');
    },

    toggleSkillCheck() {
      this.togglePanel('skillCheck');
    },

    toggleEquipment() {
      this.togglePanel('equipment');
    },

    toggleExperiencePack() {
      this.togglePanel('experiencePack');
    },

    toggleSpells() {
      this.togglePanel('spells');
    },

    toggleMadness() {
      this.togglePanel('madness');
    },

    toggleLibrary() {
      this.togglePanel('library');
    },

    // 战斗系统事件处理
    handleCombatStarted() {
      this.showSystemMessage('战斗开始！');
    },

    handleCombatEnded() {
      this.showSystemMessage('战斗结束！');
    },

    openSkillCheckForCharacter(character) {
      this.skillCheckCharacter = character;
      this.skillCheckSkill = '';
      this.togglePanel('skillCheck', false);
    },

    // 技能检定事件处理
    handleSkillCheckResult(result) {
      const message = `${result.characterName} 进行 ${result.skillName} 检定: ${result.roll}/${result.targetValue} ${this.getSuccessLevelText(result.successLevel)}`;
      this.sendMessage(message, 'system');
    },

    handleOpposedCheckResult(result) {
      const message = `对抗检定: ${result.initiator.name}(${result.initiator.roll}) VS ${result.opponent.name}(${result.opponent.roll}) - 胜者: ${result.winner || '平局'}`;
      this.sendMessage(message, 'system');
    },

    handleSkillGrowth(data) {
      const message = `${data.character.name} 的 ${data.skill} 技能成长 +${data.improvement}`;
      this.sendMessage(message, 'system');
    },

    handleGrowthCheckResult(result) {
      const message = `${result.character} 进行 ${result.skill} 成长检定: ${result.roll}/${result.currentValue} ${result.success ? '成功' : '失败'}${result.success ? ` +${result.improvement}` : ''}`;
      this.sendMessage(message, 'system');
    },

    handleSkillCheckCharacterSelected(character) {
      this.skillCheckCharacter = character;
    },

    // 装备系统事件处理
    handleWeaponEquipped(weapon) {
      this.showSystemMessage(`装备了武器: ${weapon.name}`);
    },

    handleWeaponUnequipped(weapon) {
      this.showSystemMessage(`卸下了武器: ${weapon.name}`);
    },

    handleWeaponUsed(weapon) {
      this.showSystemMessage(`使用了武器: ${weapon.name}`);
    },

    handleArmorEquipped(armor) {
      this.showSystemMessage(`装备了防具: ${armor.name}`);
    },

    handleArmorUnequipped(armor) {
      this.showSystemMessage(`卸下了防具: ${armor.name}`);
    },

    handleItemUsed(item) {
      this.showSystemMessage(`使用了物品: ${item.name}`);
    },

    handleItemPurchased(item) {
      this.showSystemMessage(`购买了物品: ${item.name}`);
    },

    handleItemDropped(item) {
      this.showSystemMessage(`丢弃了物品: ${item.name}`);
    },

    handleHealCharacter(data) {
      const message = `${data.character.name} 使用 ${data.source} 恢复了 ${data.amount} 点生命值`;
      this.sendMessage(message, 'system');
    },

    handleReadBook(data) {
      const message = `${data.character.name} 阅读了 ${data.book.name}`;
      this.sendMessage(message, 'system');
    },

    // 法术系统事件处理
    handleSpellCast(data) {
      const message = `${data.caster.name} 施放了 ${data.spell.name}`;
      this.sendMessage(message, 'system');

      // 如果有目标，添加目标信息
      if (data.target && data.target !== 'self') {
        this.sendMessage(`目标: ${data.target}`, 'system');
      }

      // 如果有描述，添加描述
      if (data.description) {
        this.sendMessage(`施法描述: ${data.description}`, 'system');
      }
    },

    // 角色更新处理
    handleCharacterUpdate(character) {
      // 更新角色数据
      this.$emit('character-updated', character);

      // 如果是当前选中的角色，更新本地状态
      if (this.selectedCharacter && this.selectedCharacter.id === character.id) {
        this.selectedCharacter = character;
      }
    },

    // 辅助方法
    getSuccessLevelText(level) {
      const levels = {
        'extreme': '极难成功',
        'hard': '困难成功',
        'normal': '常规成功',
        'failure': '失败'
      };
      return levels[level] || '未知';
    }
  }
};
</script>

<style scoped>
:root {
  --base-font-size: 16px;
}

/* 全屏提示样式 */
.fullscreen-notice {
  position: fixed;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 14px;
  z-index: 10000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
  animation: fadeInOut 3s forwards;
  pointer-events: none;
}

@keyframes fadeInOut {
  0% { opacity: 0; }
  10% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* 准备全屏的过渡样式 */
.preparing-fullscreen {
  transition: all 0.3s ease-out;
}

.room-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  padding: 10px;
  overflow: hidden;
  font-size: var(--base-font-size);
}

.room-header {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(40, 44, 52, 0.8);
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.room-header:hover {
  background-color: rgba(50, 55, 65, 0.9);
}

.room-header:hover::after {
  content: "";
  display: none;
}

.room-container.in-fullscreen .room-header:hover::after {
  content: "";
  display: none;
}

.room-title {
  display: flex;
  align-items: center;
}

.room-actions {
  display: flex;
  gap: 10px;
}

.room-action-btn {
  padding: 5px 10px;
  border-radius: 4px;
  border: none;
  background-color: #444;
  color: #e0e0e0;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: 5px;
  min-width: 80px;
  justify-content: center;
}

.room-action-btn:hover {
  background-color: #555;
}

.ai-settings-btn {
  background-color: #2c3e50;
}

.ai-settings-btn:hover {
  background-color: #34495e;
}

.room-header h2 {
  margin: 0;
  color: #e0e0e0;
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.room-header h2.editable {
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.room-header h2.editable:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.room-header h2 .edit-icon {
  font-size: 0.8em;
  opacity: 0.6;
}

.room-header h2.editable:hover .edit-icon {
  opacity: 1;
}

.room-name-input {
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid #555;
  color: #e0e0e0;
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
  width: 100%;
  max-width: 300px;
}

.room-participants {
  font-size: 0.8rem;
  color: #b0b0b0;
  padding: 3px 6px;
  background: rgba(255,255,255,0.1);
  border-radius: 10px;
}

.room-header p {
  color: #b0b0b0;
  margin: 0 0 8px;
  font-size: 0.9rem;
}

.room-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  min-height: 400px;
  height: calc(100vh - 220px);
}

.online-users {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;
  padding: 6px;
  background: rgba(255,255,255,0.05);
  border-radius: 4px;
}

.user-badge {
  background: #3498db;
  color: white;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
}

.character-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  padding: 15px;
}

.character-item {
  background: #333333;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s;
}

.character-item:hover {
  background: #3a3a3a;
  transform: translateY(-2px);
}

.character-item h4 {
  margin: 0 0 5px;
  color: #e0e0e0;
}

.character-item p {
  margin: 0;
  color: #b0b0b0;
  font-size: 0.9em;
}

/* 设置模态框样式 */
.settings-content {
  padding: 20px;
}

.setting-group {
  margin-bottom: 20px;
}

.setting-group label {
  display: block;
  margin-bottom: 10px;
  color: #e0e0e0;
  font-weight: 500;
}

.font-size-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.font-btn {
  background: #3498db;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 1.2rem;
  cursor: pointer;
}

.current-size {
  font-weight: 500;
  color: #e0e0e0;
}

.toggle-switch {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.toggle-switch input {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.toggle-switch label {
  display: inline;
  margin: 0;
  cursor: pointer;
}

.setting-actions {
  text-align: right;
  margin-top: 20px;
}

.save-settings-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

/* 深色/浅色模式支持 */
:root {
  --text-color: #e0e0e0;
  --bg-color: #2a2a2a;
  --secondary-bg: #333333;
  --border-color: #444;
  --highlight-color: #3498db;
}

.dark-mode .room-container {
  --text-color: #e0e0e0;
  --bg-color: #2a2a2a;
  --secondary-bg: #333333;
  --border-color: #444;
}

/* 媒体查询 - 响应式布局 */
@media (max-width: 768px) {
  .room-header h2 {
    font-size: 1.2rem;
  }
  
  .room-container {
    padding: 5px;
    height: calc(100vh - 60px);
  }
  
  .room-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .room-title {
    margin-bottom: 10px;
    width: 100%;
  }
  
  .room-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .control-btn {
    padding: 5px 6px;
    font-size: 0.8rem;
    min-width: 60px;
  }
  
  .room-controls {
    overflow-x: auto;
    padding-bottom: 5px;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x mandatory;
    white-space: nowrap;
    width: 100%;
  }
  
  .panel-content {
    height: calc(100vh - 180px);
  }
  
  .message-input textarea {
    height: 40px;
  }
  
  .btn-text {
    display: none;
  }
  
  .toggle-btn {
    min-width: 40px;
  }
  
  .room-header:hover::after {
    right: 150px;
  }
}

.room-controls {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.control-btn {
  background: rgba(255,255,255,0.1);
  border: none;
  color: #e0e0e0;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background 0.2s;
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
  min-width: 80px;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255,255,255,0.2);
}

.script-btn {
  background-color: rgba(142, 68, 173, 0.2);
  border: 1px solid #8e44ad;
  display: flex;
  align-items: center;
  gap: 6px;
}

.script-btn:hover {
  background-color: rgba(142, 68, 173, 0.3);
}

.script-btn i {
  font-size: 0.9rem;
}

.ai-config-alert {
  background-color: rgba(255, 193, 7, 0.1);
  border-left: 4px solid #ffc107;
  margin-bottom: 10px;
  border-radius: 4px;
  overflow: hidden;
}

.ai-config-alert .alert-content {
  padding: 10px 15px;
  position: relative;
  font-size: 0.9rem;
}

.ai-config-alert p {
  margin: 5px 0;
}

.ai-config-alert .link-button {
  background: none;
  border: none;
  color: #3498db;
  padding: 0;
  text-decoration: underline;
  cursor: pointer;
}

.ai-config-alert .close-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.ai-config-alert .close-button:hover {
  opacity: 1;
}

/* 添加场景相关样式 */
.main-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.scene-area {
  flex: 1;
  min-height: 300px;
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.message-area {
  flex: 1;
  min-height: 300px;
}

/* 在大屏幕上使用水平布局 */
@media (min-width: 1024px) {
  .main-content {
    flex-direction: row;
    gap: 15px;
  }
  
  .scene-area {
    flex: 1;
    margin-bottom: 0;
  }
  
  .message-area {
    flex: 1;
  }
}

.room-toolbar {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  background-color: rgba(52, 152, 219, 0.95);
  border-radius: 0 10px 10px 0;
  padding: 20px 15px 20px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
  z-index: 9999;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  border-right: 5px solid #2ecc71;
  border-left: none;
}

/* 修改侧边栏样式 */
.room-toolbar {
  background-color: #2980b9;
  border-radius: 0 10px 10px 0;
  padding: 15px;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);
  border-right: 3px solid #3498db;
  width: 85px;
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  z-index: 9999;
  transition: all 0.3s ease;
}

.room-toolbar.collapsed {
  left: -85px;
}

.toolbar-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  transition: all 0.3s ease;
  width: 100%;
  align-items: center;
}

/* 工具组样式 */
.toolbar-group {
  background-color: #2c3e50;
  border-radius: 8px;
  margin-bottom: 10px;
  padding: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 65px;
}

.toolbar-group:last-child {
  border-bottom: none;
}

.collapse-btn {
  background: #1a5276;
  border: 2px solid #ffffff;
  color: #ffffff;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s;
  position: absolute;
  right: -30px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 80px;
  border-radius: 0 5px 5px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);
  animation: pulse-left 2s infinite;
}

@keyframes pulse-left {
  0% {
    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 3px 0 15px rgba(46, 204, 113, 0.7);
  }
  100% {
    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);
  }
}

.collapse-btn:hover {
  background-color: #2980b9;
  transform: translateY(-50%) scale(1.1);
}

.room-toolbar.collapsed .collapse-btn {
  right: 0;
  left: auto;
  border-right: none;
  border-radius: 0;
  animation: none;
}

.toolbar-btn {
  width: 45px;
  height: 45px;
  border-radius: 6px;
  background-color: #2c3e50;
  border: none;
  color: #e0e0e0;
  font-size: 1.2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.toolbar-btn:hover {
  background-color: #2980b9;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.toolbar-btn.active {
  background-color: #16a085;
  box-shadow: 0 0 12px rgba(22, 160, 133, 0.7);
  border-color: #2ecc71;
}

.toolbar-btn:active {
  transform: scale(0.95);
}

.tooltip {
  position: absolute;
  left: 60px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 0.9rem;
  opacity: 0;
  transition: opacity 0.2s, transform 0.2s;
  pointer-events: none;
  white-space: nowrap;
  transform: translateX(-10px);
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 10001;
}

.toolbar-btn:hover .tooltip {
  opacity: 1;
  transform: translateX(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .room-toolbar {
    left: 0;
    right: 0;
    top: auto;
    bottom: 0;
    transform: none;
    width: 100%;
    border-radius: 8px 8px 0 0;
    padding: 10px;
    justify-content: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
    flex-direction: row;
    border-right: none;
    border-left: none;
    border-top: 5px solid #2ecc71;
  }
  
  .room-toolbar.collapsed {
    bottom: -70px;
    left: 0;
    right: 0;
  }
  
  .collapse-btn {
    top: -30px;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    width: 80px;
    height: 30px;
    border-radius: 5px 5px 0 0;
    animation: pulse-mobile 2s infinite;
  }
  
  @keyframes pulse-mobile {
    0% {
      box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);
    }
    50% {
      box-shadow: 0 -3px 15px rgba(46, 204, 113, 0.7);
    }
    100% {
      box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);
    }
  }
  
  .collapse-btn:hover {
    transform: translateX(-50%) scale(1.1);
  }
  
  .room-toolbar.collapsed .collapse-btn {
    top: 0;
    left: 50%;
    right: auto;
    animation: none;
    border-bottom: none;
    border-right: 2px solid #ffffff;
  }
  
  .toolbar-buttons {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .toolbar-group {
    flex-direction: row;
    padding: 0;
    border-bottom: none;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    padding-right: 10px;
    margin-right: 10px;
    width: auto;
    gap: 10px;
  }
  
  .toolbar-group:last-child {
    border-right: none;
    padding-right: 0;
    margin-right: 0;
  }
  
  .toolbar-btn {
    width: 36px;
    height: 36px;
    font-size: 1rem;
    margin: 0;
  }
  
  .tooltip {
    left: 50%;
    transform: translateX(-50%);
    bottom: 45px;
    top: auto;
  }
}

/* 全屏模式样式 */
.fullscreen-mode .room-container,
.room-container.in-fullscreen {
  height: 100vh !important;
  padding: 10px;
  box-sizing: border-box;
  width: 100vw !important;
  max-width: 100vw !important;
  overflow: auto;
  z-index: 9999;
  background-color: var(--bg-color, #2a2a2a);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
}

.fullscreen-mode .room-header,
.room-container.in-fullscreen .room-header {
  background-color: rgba(30, 33, 40, 0.95);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.fullscreen-mode .panel-content,
.room-container.in-fullscreen .panel-content {
  height: calc(100vh - 150px);
}

/* 在全屏模式下特别突出显示双击提示 */
.room-container.in-fullscreen .room-header:hover::after {
  content: "";
  display: none;
}

@keyframes pulse {
  0% { opacity: 0.8; }
  50% { opacity: 1; }
  100% { opacity: 0.8; }
}

.toggle-btn {
  min-width: 80px;
}

.btn-text {
  margin-left: 5px;
}

@media (max-width: 768px) {
  .btn-text {
    display: none;
  }
  
  .toggle-btn {
    min-width: 40px;
  }
  
  .room-header:hover::after {
    right: 150px;
  }
}

.control-btn.save-btn {
  background-color: #4caf50;
  color: white;
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn.save-btn:hover {
  background-color: #45a049;
}

.control-btn.load-btn {
  background-color: #2196f3;
  color: white;
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn.load-btn:hover {
  background-color: #0b7dda;
}

.preview-mode-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgba(76, 175, 80, 0.9);
  color: white;
  padding: 8px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.preview-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.exit-preview-btn {
  background-color: white;
  color: #4caf50;
  border: none;
  border-radius: 4px;
  padding: 4px 10px;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.exit-preview-btn:hover {
  background-color: #f5f5f5;
}

.toolbar-buttons {
  display: flex;
  gap: 10px;
}

.toolbar-btn {
  background-color: #333;
  color: #e0e0e0;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.toolbar-btn:hover {
  background-color: #444;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn i {
  font-size: 1rem;
}

/* 修改按钮样式 */
.toolbar-btn {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: #333;
  border: none;
  color: #e0e0e0;
  font-size: 1.1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  margin: 0;
}

.toolbar-btn:hover {
  background-color: #2980b9;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.toolbar-btn.active {
  background-color: #16a085;
  box-shadow: 0 0 12px rgba(22, 160, 133, 0.7);
  border-color: #2ecc71;
}

/* 存档选项面板样式 */
.save-options-panel {
  position: fixed;
  left: 95px;
  top: 50%;
  transform: translateY(-50%);
  background-color: #2c3e50;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
  z-index: 9998;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 10px;
  width: 180px;
}

.save-options-panel::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 50%;
  transform: translateY(-50%);
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent #2c3e50 transparent transparent;
}

.save-options-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.save-option {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.save-option:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.save-option i {
  font-size: 1.2rem;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.save-option span {
  flex: 1;
  font-size: 0.95rem;
}

.save-option small {
  font-size: 0.75rem;
  opacity: 0.7;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.save-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-role-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #e0e0e0;
  font-size: 0.95rem;
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: rgba(44, 62, 80, 0.9);
  padding: 8px 15px;
  border-radius: 6px;
  font-weight: 600;
  z-index: 1000;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.user-role-indicator:hover {
  background-color: rgba(52, 73, 94, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.5);
}

.user-role-indicator i {
  font-size: 1.3rem;
}

.user-role-indicator i.fa-crown {
  color: #f1c40f;
  text-shadow: 0 0 5px rgba(241, 196, 15, 0.5);
}

.user-role-indicator i.fa-user {
  color: #3498db;
  text-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
}

.room-action-btn.announcement-btn {
  background-color: #4caf50;
  color: white;
}

.room-action-btn.announcement-btn:hover {
  background-color: #388e3c;
}

.room-action-btn.announcement-btn i {
  color: white;
}



</style> 