{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode, withModifiers as _withModifiers, normalizeClass as _normalizeClass } from \"vue\";\nvar _hoisted_1 = [\"transform\"];\nvar _hoisted_2 = [\"r\"];\nvar _hoisted_3 = [\"r\"];\nvar _hoisted_4 = [\"r\"];\nvar _hoisted_5 = {\n  \"class\": \"monster-body\"\n};\nvar _hoisted_6 = {\n  key: 0\n};\nvar _hoisted_7 = [\"x\", \"y\", \"width\", \"height\", \"fill\"];\nvar _hoisted_8 = {\n  key: 1\n};\nvar _hoisted_9 = [\"x\", \"y\", \"width\", \"height\", \"fill\"];\nvar _hoisted_10 = {\n  key: 2\n};\nvar _hoisted_11 = [\"r\", \"fill\"];\nvar _hoisted_12 = [\"font-size\"];\nvar _hoisted_13 = [\"x\", \"y\", \"width\"];\nvar _hoisted_14 = [\"x\", \"y\", \"width\", \"fill\"];\nvar _hoisted_15 = [\"y\"];\nvar _hoisted_16 = [\"transform\"];\nvar _hoisted_17 = {\n  \"text-anchor\": \"middle\",\n  dy: \"3\",\n  \"font-size\": \"8\",\n  fill: \"white\",\n  \"font-weight\": \"bold\"\n};\nvar _hoisted_18 = [\"transform\"];\nvar _hoisted_19 = [\"transform\"];\nvar _hoisted_20 = {\n  \"text-anchor\": \"middle\",\n  dy: \"3\",\n  \"font-size\": \"8\",\n  fill: \"white\"\n};\nvar _hoisted_21 = [\"transform\"];\nvar _hoisted_22 = {\n  key: 0,\n  r: \"4\",\n  fill: \"#9c27b0\",\n  stroke: \"#fff\",\n  \"stroke-width\": \"1\"\n};\nvar _hoisted_23 = {\n  key: 1,\n  \"text-anchor\": \"middle\",\n  dy: \"2\",\n  \"font-size\": \"6\",\n  fill: \"white\"\n};\nvar _hoisted_24 = {\n  key: 2,\n  transform: \"translate(12, 0)\",\n  r: \"4\",\n  fill: \"#ff9800\",\n  stroke: \"#fff\",\n  \"stroke-width\": \"1\"\n};\nvar _hoisted_25 = {\n  key: 3,\n  transform: \"translate(12, 0)\",\n  \"text-anchor\": \"middle\",\n  dy: \"2\",\n  \"font-size\": \"6\",\n  fill: \"white\"\n};\nvar _hoisted_26 = {\n  key: 3,\n  \"class\": \"damage-animation\"\n};\nvar _hoisted_27 = [\"y\", \"fill\"];\nvar _hoisted_28 = [\"values\"];\nvar _hoisted_29 = {\n  key: 4,\n  \"class\": \"attack-animation\"\n};\nvar _hoisted_30 = [\"r\"];\nvar _hoisted_31 = [\"values\"];\nvar _hoisted_32 = [\"r\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 2D战场上的怪物令牌 \"), _createElementVNode(\"g\", {\n    \"class\": _normalizeClass([\"monster-token\", {\n      selected: $props.selected,\n      'can-control': $props.canControl,\n      'boss-monster': $props.monster.isBoss,\n      'minion-monster': $props.monster.isMinion\n    }]),\n    transform: \"translate(\".concat($options.x, \", \").concat($options.y, \")\"),\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return _ctx.$emit('select', $props.monster);\n    }),\n    onContextmenu: _cache[2] || (_cache[2] = _withModifiers(function ($event) {\n      return _ctx.$emit('context-menu', $props.monster, $event);\n    }, [\"prevent\"]))\n  }, [_createCommentVNode(\" 选中光环 \"), $props.selected ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 0,\n    r: $data.tokenRadius + 8,\n    fill: \"none\",\n    stroke: \"#ff6b6b\",\n    \"stroke-width\": \"3\",\n    opacity: \"0.8\"\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"animate\", {\n    attributeName: \"stroke-opacity\",\n    values: \"0.8;0.3;0.8\",\n    dur: \"2s\",\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_2)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" Boss怪物特殊光环 \"), $props.monster.isBoss ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 1,\n    r: $data.tokenRadius + 15,\n    fill: \"none\",\n    stroke: \"#9c27b0\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"8,4\",\n    opacity: \"0.7\"\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    values: \"0;360\",\n    dur: \"4s\",\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 威胁范围指示 \"), $props.showThreatRange ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 2,\n    r: $options.threatRange * $props.gridSize,\n    fill: \"rgba(244, 67, 54, 0.1)\",\n    stroke: \"#f44336\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"3,3\",\n    opacity: \"0.4\"\n  }, null, 8 /* PROPS */, _hoisted_4)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 怪物主体 \"), _createElementVNode(\"g\", _hoisted_5, [_createCommentVNode(\" 背景形状 (根据怪物类型变化) \"), $props.monster.size === 'large' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_6, [_createCommentVNode(\" 大型怪物 - 2x2格子 \"), _createElementVNode(\"rect\", {\n    x: -$data.tokenRadius * 1.5,\n    y: -$data.tokenRadius * 1.5,\n    width: $data.tokenRadius * 3,\n    height: $data.tokenRadius * 3,\n    fill: $options.getMonsterColor(),\n    stroke: \"#fff\",\n    \"stroke-width\": \"2\",\n    rx: \"4\",\n    opacity: \"0.9\"\n  }, null, 8 /* PROPS */, _hoisted_7)])) : $props.monster.size === 'huge' ? (_openBlock(), _createElementBlock(\"g\", _hoisted_8, [_createCommentVNode(\" 巨型怪物 - 3x3格子 \"), _createElementVNode(\"rect\", {\n    x: -$data.tokenRadius * 2,\n    y: -$data.tokenRadius * 2,\n    width: $data.tokenRadius * 4,\n    height: $data.tokenRadius * 4,\n    fill: $options.getMonsterColor(),\n    stroke: \"#fff\",\n    \"stroke-width\": \"3\",\n    rx: \"6\",\n    opacity: \"0.9\"\n  }, null, 8 /* PROPS */, _hoisted_9)])) : (_openBlock(), _createElementBlock(\"g\", _hoisted_10, [_createCommentVNode(\" 普通怪物 - 圆形 \"), _createElementVNode(\"circle\", {\n    r: $data.tokenRadius,\n    fill: $options.getMonsterColor(),\n    stroke: \"#fff\",\n    \"stroke-width\": \"2\",\n    opacity: \"0.9\"\n  }, null, 8 /* PROPS */, _hoisted_11)])), _createCommentVNode(\" 怪物图标 \"), _createElementVNode(\"text\", {\n    \"text-anchor\": \"middle\",\n    dy: \"6\",\n    \"font-size\": $options.getIconSize(),\n    fill: \"white\",\n    \"class\": \"monster-icon\"\n  }, _toDisplayString($options.getMonsterIcon()), 9 /* TEXT, PROPS */, _hoisted_12), _createCommentVNode(\" 生命值条 \"), _createElementVNode(\"rect\", {\n    x: -$data.tokenRadius + 4,\n    y: $data.tokenRadius - 8,\n    width: ($data.tokenRadius - 4) * 2,\n    height: \"4\",\n    fill: \"#333\",\n    opacity: \"0.7\",\n    rx: \"2\"\n  }, null, 8 /* PROPS */, _hoisted_13), _createElementVNode(\"rect\", {\n    x: -$data.tokenRadius + 4,\n    y: $data.tokenRadius - 8,\n    width: $options.getHealthBarWidth(),\n    height: \"4\",\n    fill: $options.getHealthColor(),\n    opacity: \"0.9\",\n    rx: \"2\"\n  }, null, 8 /* PROPS */, _hoisted_14)]), _createCommentVNode(\" 怪物名称和等级 \"), _createElementVNode(\"text\", {\n    y: $options.getNameYPosition(),\n    \"text-anchor\": \"middle\",\n    \"font-size\": \"10\",\n    \"font-weight\": \"bold\",\n    fill: \"#f44336\",\n    \"class\": \"monster-name-text\"\n  }, _toDisplayString($props.monster.name), 9 /* TEXT, PROPS */, _hoisted_15), _createCommentVNode(\" 挑战等级显示 \"), _createElementVNode(\"g\", {\n    \"class\": \"challenge-rating\",\n    transform: \"translate(\".concat($data.tokenRadius - 12, \", \").concat(-$data.tokenRadius + 12, \")\")\n  }, [_cache[5] || (_cache[5] = _createElementVNode(\"circle\", {\n    r: \"10\",\n    fill: \"#ff5722\",\n    stroke: \"#fff\",\n    \"stroke-width\": \"1\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"text\", _hoisted_17, _toDisplayString($props.monster.challengeRating || '?'), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_16), _createCommentVNode(\" 状态效果图标 \"), _createElementVNode(\"g\", {\n    \"class\": \"status-effects\",\n    transform: \"translate(\".concat(-$data.tokenRadius, \", \").concat(-$options.getNameYPosition() - 10, \")\")\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.monster.conditions, function (condition, index) {\n    return _openBlock(), _createElementBlock(\"g\", {\n      key: condition,\n      transform: \"translate(\".concat(index * 14, \", 0)\")\n    }, [_cache[6] || (_cache[6] = _createElementVNode(\"circle\", {\n      r: \"7\",\n      fill: \"#333\",\n      opacity: \"0.8\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"text\", _hoisted_20, _toDisplayString($options.getConditionIcon(condition)), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_19);\n  }), 128 /* KEYED_FRAGMENT */))], 8 /* PROPS */, _hoisted_18), _createCommentVNode(\" 特殊能力指示器 \"), _createElementVNode(\"g\", {\n    \"class\": \"special-abilities\",\n    transform: \"translate(\".concat(-$data.tokenRadius + 8, \", \").concat($data.tokenRadius - 8, \")\")\n  }, [_createCommentVNode(\" 法术能力 \"), $props.monster.hasSpells ? (_openBlock(), _createElementBlock(\"circle\", _hoisted_22)) : _createCommentVNode(\"v-if\", true), $props.monster.hasSpells ? (_openBlock(), _createElementBlock(\"text\", _hoisted_23, \" ✨ \")) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 传奇行动 \"), $props.monster.hasLegendaryActions ? (_openBlock(), _createElementBlock(\"circle\", _hoisted_24)) : _createCommentVNode(\"v-if\", true), $props.monster.hasLegendaryActions ? (_openBlock(), _createElementBlock(\"text\", _hoisted_25, \" ⚡ \")) : _createCommentVNode(\"v-if\", true)], 8 /* PROPS */, _hoisted_21), _createCommentVNode(\" 伤害数字动画 \"), $data.damageAnimation ? (_openBlock(), _createElementBlock(\"g\", _hoisted_26, [_createElementVNode(\"text\", {\n    y: -$options.getNameYPosition() - 20,\n    \"text-anchor\": \"middle\",\n    \"font-size\": \"14\",\n    \"font-weight\": \"bold\",\n    fill: $data.damageAnimation.color,\n    opacity: \"0\"\n  }, [_createTextVNode(_toDisplayString($data.damageAnimation.text) + \" \", 1 /* TEXT */), _createElementVNode(\"animate\", {\n    attributeName: \"y\",\n    values: \"\".concat(-$options.getNameYPosition() - 20, \";\").concat(-$options.getNameYPosition() - 50),\n    dur: \"1.5s\"\n  }, null, 8 /* PROPS */, _hoisted_28), _cache[7] || (_cache[7] = _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;1;0\",\n    dur: \"1.5s\"\n  }, null, -1 /* CACHED */))], 8 /* PROPS */, _hoisted_27)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 攻击动画效果 \"), $data.attackAnimation ? (_openBlock(), _createElementBlock(\"g\", _hoisted_29, [_createElementVNode(\"circle\", {\n    r: $data.tokenRadius,\n    fill: \"none\",\n    stroke: \"#ff4444\",\n    \"stroke-width\": \"4\",\n    opacity: \"0\"\n  }, [_createElementVNode(\"animate\", {\n    attributeName: \"r\",\n    values: \"\".concat($data.tokenRadius, \";\").concat($data.tokenRadius + 20),\n    dur: \"0.5s\"\n  }, null, 8 /* PROPS */, _hoisted_31), _cache[8] || (_cache[8] = _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0.8;0\",\n    dur: \"0.5s\"\n  }, null, -1 /* CACHED */))], 8 /* PROPS */, _hoisted_30)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" KP控制手柄 \"), $props.canControl && $props.selected ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 5,\n    r: $data.tokenRadius + 5,\n    fill: \"transparent\",\n    stroke: \"none\",\n    style: {\n      \"cursor\": \"move\"\n    },\n    onMousedown: _cache[0] || (_cache[0] = function () {\n      return $options.startDrag && $options.startDrag.apply($options, arguments);\n    })\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_32)) : _createCommentVNode(\"v-if\", true)], 42 /* CLASS, PROPS, NEED_HYDRATION */, _hoisted_1)], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["dy", "fill", "r", "stroke", "transform", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "$props", "selected", "canControl", "monster", "isBoss", "isMinion", "concat", "$options", "x", "y", "onClick", "_cache", "$event", "_ctx", "$emit", "onContextmenu", "_withModifiers", "_createElementBlock", "$data", "tokenRadius", "opacity", "attributeName", "values", "dur", "repeatCount", "type", "showThreatRange", "threatRange", "gridSize", "_hoisted_5", "size", "_hoisted_6", "width", "height", "getMonsterColor", "rx", "_hoisted_8", "_hoisted_10", "getIconSize", "getMonsterIcon", "_hoisted_12", "getHealthBarWidth", "getHealthColor", "getNameYPosition", "name", "_hoisted_15", "_hoisted_17", "_toDisplayString", "challengeRating", "_Fragment", "_renderList", "conditions", "condition", "index", "key", "_hoisted_20", "getConditionIcon", "hasSpells", "_hoisted_22", "_hoisted_23", "hasLegendaryActions", "_hoisted_24", "_hoisted_25", "damageAnimation", "_hoisted_26", "color", "text", "attackAnimation", "_hoisted_29", "style", "onMousedown", "startDrag", "apply", "arguments"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\MonsterToken.vue"], "sourcesContent": ["<template>\r\n  <!-- 2D战场上的怪物令牌 -->\r\n  <g \r\n    class=\"monster-token\"\r\n    :class=\"{ \r\n      selected, \r\n      'can-control': canControl,\r\n      'boss-monster': monster.isBoss,\r\n      'minion-monster': monster.isMinion\r\n    }\"\r\n    :transform=\"`translate(${x}, ${y})`\"\r\n    @click=\"$emit('select', monster)\"\r\n    @contextmenu.prevent=\"$emit('context-menu', monster, $event)\"\r\n  >\r\n    <!-- 选中光环 -->\r\n    <circle \r\n      v-if=\"selected\"\r\n      :r=\"tokenRadius + 8\"\r\n      fill=\"none\"\r\n      stroke=\"#ff6b6b\"\r\n      stroke-width=\"3\"\r\n      opacity=\"0.8\"\r\n    >\r\n      <animate \r\n        attributeName=\"stroke-opacity\" \r\n        values=\"0.8;0.3;0.8\" \r\n        dur=\"2s\" \r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- Boss怪物特殊光环 -->\r\n    <circle \r\n      v-if=\"monster.isBoss\"\r\n      :r=\"tokenRadius + 15\"\r\n      fill=\"none\"\r\n      stroke=\"#9c27b0\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"8,4\"\r\n      opacity=\"0.7\"\r\n    >\r\n      <animateTransform\r\n        attributeName=\"transform\"\r\n        type=\"rotate\"\r\n        values=\"0;360\"\r\n        dur=\"4s\"\r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- 威胁范围指示 -->\r\n    <circle \r\n      v-if=\"showThreatRange\"\r\n      :r=\"threatRange * gridSize\"\r\n      fill=\"rgba(244, 67, 54, 0.1)\"\r\n      stroke=\"#f44336\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"3,3\"\r\n      opacity=\"0.4\"\r\n    />\r\n    \r\n    <!-- 怪物主体 -->\r\n    <g class=\"monster-body\">\r\n      <!-- 背景形状 (根据怪物类型变化) -->\r\n      <g v-if=\"monster.size === 'large'\">\r\n        <!-- 大型怪物 - 2x2格子 -->\r\n        <rect \r\n          :x=\"-tokenRadius * 1.5\"\r\n          :y=\"-tokenRadius * 1.5\"\r\n          :width=\"tokenRadius * 3\"\r\n          :height=\"tokenRadius * 3\"\r\n          :fill=\"getMonsterColor()\"\r\n          stroke=\"#fff\"\r\n          stroke-width=\"2\"\r\n          rx=\"4\"\r\n          opacity=\"0.9\"\r\n        />\r\n      </g>\r\n      <g v-else-if=\"monster.size === 'huge'\">\r\n        <!-- 巨型怪物 - 3x3格子 -->\r\n        <rect \r\n          :x=\"-tokenRadius * 2\"\r\n          :y=\"-tokenRadius * 2\"\r\n          :width=\"tokenRadius * 4\"\r\n          :height=\"tokenRadius * 4\"\r\n          :fill=\"getMonsterColor()\"\r\n          stroke=\"#fff\"\r\n          stroke-width=\"3\"\r\n          rx=\"6\"\r\n          opacity=\"0.9\"\r\n        />\r\n      </g>\r\n      <g v-else>\r\n        <!-- 普通怪物 - 圆形 -->\r\n        <circle \r\n          :r=\"tokenRadius\"\r\n          :fill=\"getMonsterColor()\"\r\n          stroke=\"#fff\"\r\n          stroke-width=\"2\"\r\n          opacity=\"0.9\"\r\n        />\r\n      </g>\r\n      \r\n      <!-- 怪物图标 -->\r\n      <text \r\n        text-anchor=\"middle\"\r\n        dy=\"6\"\r\n        :font-size=\"getIconSize()\"\r\n        fill=\"white\"\r\n        class=\"monster-icon\"\r\n      >\r\n        {{ getMonsterIcon() }}\r\n      </text>\r\n      \r\n      <!-- 生命值条 -->\r\n      <rect \r\n        :x=\"-tokenRadius + 4\"\r\n        :y=\"tokenRadius - 8\"\r\n        :width=\"(tokenRadius - 4) * 2\"\r\n        height=\"4\"\r\n        fill=\"#333\"\r\n        opacity=\"0.7\"\r\n        rx=\"2\"\r\n      />\r\n      <rect \r\n        :x=\"-tokenRadius + 4\"\r\n        :y=\"tokenRadius - 8\"\r\n        :width=\"getHealthBarWidth()\"\r\n        height=\"4\"\r\n        :fill=\"getHealthColor()\"\r\n        opacity=\"0.9\"\r\n        rx=\"2\"\r\n      />\r\n    </g>\r\n    \r\n    <!-- 怪物名称和等级 -->\r\n    <text \r\n      :y=\"getNameYPosition()\"\r\n      text-anchor=\"middle\"\r\n      font-size=\"10\"\r\n      font-weight=\"bold\"\r\n      fill=\"#f44336\"\r\n      class=\"monster-name-text\"\r\n    >\r\n      {{ monster.name }}\r\n    </text>\r\n    \r\n    <!-- 挑战等级显示 -->\r\n    <g class=\"challenge-rating\" :transform=\"`translate(${tokenRadius - 12}, ${-tokenRadius + 12})`\">\r\n      <circle r=\"10\" fill=\"#ff5722\" stroke=\"#fff\" stroke-width=\"1\"/>\r\n      <text \r\n        text-anchor=\"middle\"\r\n        dy=\"3\"\r\n        font-size=\"8\"\r\n        fill=\"white\"\r\n        font-weight=\"bold\"\r\n      >\r\n        {{ monster.challengeRating || '?' }}\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 状态效果图标 -->\r\n    <g class=\"status-effects\" :transform=\"`translate(${-tokenRadius}, ${-getNameYPosition() - 10})`\">\r\n      <g \r\n        v-for=\"(condition, index) in monster.conditions\" \r\n        :key=\"condition\"\r\n        :transform=\"`translate(${index * 14}, 0)`\"\r\n      >\r\n        <circle r=\"7\" fill=\"#333\" opacity=\"0.8\"/>\r\n        <text \r\n          text-anchor=\"middle\" \r\n          dy=\"3\" \r\n          font-size=\"8\" \r\n          fill=\"white\"\r\n        >\r\n          {{ getConditionIcon(condition) }}\r\n        </text>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 特殊能力指示器 -->\r\n    <g class=\"special-abilities\" :transform=\"`translate(${-tokenRadius + 8}, ${tokenRadius - 8})`\">\r\n      <!-- 法术能力 -->\r\n      <circle \r\n        v-if=\"monster.hasSpells\"\r\n        r=\"4\"\r\n        fill=\"#9c27b0\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"monster.hasSpells\"\r\n        text-anchor=\"middle\"\r\n        dy=\"2\"\r\n        font-size=\"6\"\r\n        fill=\"white\"\r\n      >\r\n        ✨\r\n      </text>\r\n      \r\n      <!-- 传奇行动 -->\r\n      <circle \r\n        v-if=\"monster.hasLegendaryActions\"\r\n        :transform=\"`translate(12, 0)`\"\r\n        r=\"4\"\r\n        fill=\"#ff9800\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"monster.hasLegendaryActions\"\r\n        :transform=\"`translate(12, 0)`\"\r\n        text-anchor=\"middle\"\r\n        dy=\"2\"\r\n        font-size=\"6\"\r\n        fill=\"white\"\r\n      >\r\n        ⚡\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 伤害数字动画 -->\r\n    <g v-if=\"damageAnimation\" class=\"damage-animation\">\r\n      <text \r\n        :y=\"-getNameYPosition() - 20\"\r\n        text-anchor=\"middle\"\r\n        font-size=\"14\"\r\n        font-weight=\"bold\"\r\n        :fill=\"damageAnimation.color\"\r\n        opacity=\"0\"\r\n      >\r\n        {{ damageAnimation.text }}\r\n        <animate \r\n          attributeName=\"y\" \r\n          :values=\"`${-getNameYPosition() - 20};${-getNameYPosition() - 50}`\"\r\n          dur=\"1.5s\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0\"\r\n          dur=\"1.5s\"\r\n        />\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 攻击动画效果 -->\r\n    <g v-if=\"attackAnimation\" class=\"attack-animation\">\r\n      <circle \r\n        :r=\"tokenRadius\"\r\n        fill=\"none\"\r\n        stroke=\"#ff4444\"\r\n        stroke-width=\"4\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"r\" \r\n          :values=\"`${tokenRadius};${tokenRadius + 20}`\"\r\n          dur=\"0.5s\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0.8;0\"\r\n          dur=\"0.5s\"\r\n        />\r\n      </circle>\r\n    </g>\r\n    \r\n    <!-- KP控制手柄 -->\r\n    <circle \r\n      v-if=\"canControl && selected\"\r\n      :r=\"tokenRadius + 5\"\r\n      fill=\"transparent\"\r\n      stroke=\"none\"\r\n      style=\"cursor: move\"\r\n      @mousedown=\"startDrag\"\r\n    />\r\n  </g>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MonsterToken',\r\n  props: {\r\n    monster: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    },\r\n    zoom: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    selected: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canControl: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showThreatRange: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      tokenRadius: 20,\r\n      isDragging: false,\r\n      dragStart: null,\r\n      damageAnimation: null,\r\n      attackAnimation: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    x() {\r\n      return (this.monster.position?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    y() {\r\n      return (this.monster.position?.y || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    threatRange() {\r\n      // 根据怪物武器和能力计算威胁范围\r\n      return this.monster.reach || 1.5\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 获取怪物颜色\r\n     */\r\n    getMonsterColor() {\r\n      const colorMap = {\r\n        'beast': '#8bc34a',      // 野兽 - 绿色\r\n        'humanoid': '#ff9800',   // 人形 - 橙色\r\n        'undead': '#9c27b0',     // 不死 - 紫色\r\n        'fiend': '#f44336',      // 恶魔 - 红色\r\n        'celestial': '#ffeb3b',  // 天界 - 黄色\r\n        'dragon': '#e91e63',     // 龙类 - 粉红\r\n        'aberration': '#3f51b5', // 异怪 - 蓝色\r\n        'construct': '#607d8b',  // 构装 - 灰色\r\n        'elemental': '#00bcd4',  // 元素 - 青色\r\n        'fey': '#4caf50',        // 精类 - 浅绿\r\n        'giant': '#795548',      // 巨人 - 棕色\r\n        'monstrosity': '#ff5722', // 怪物 - 深橙\r\n        'ooze': '#9e9e9e',       // 软泥 - 灰色\r\n        'plant': '#689f38'       // 植物 - 深绿\r\n      }\r\n      \r\n      return colorMap[this.monster.type] || '#666666'\r\n    },\r\n    \r\n    /**\r\n     * 获取怪物图标\r\n     */\r\n    getMonsterIcon() {\r\n      const iconMap = {\r\n        'beast': '🐺',\r\n        'humanoid': '👤',\r\n        'undead': '💀',\r\n        'fiend': '👹',\r\n        'celestial': '👼',\r\n        'dragon': '🐉',\r\n        'aberration': '👁️',\r\n        'construct': '🤖',\r\n        'elemental': '🌪️',\r\n        'fey': '🧚',\r\n        'giant': '🗿',\r\n        'monstrosity': '👾',\r\n        'ooze': '🟢',\r\n        'plant': '🌿'\r\n      }\r\n      \r\n      return iconMap[this.monster.type] || '❓'\r\n    },\r\n    \r\n    /**\r\n     * 获取图标大小\r\n     */\r\n    getIconSize() {\r\n      if (this.monster.size === 'huge') return 32\r\n      if (this.monster.size === 'large') return 24\r\n      return 16\r\n    },\r\n    \r\n    /**\r\n     * 获取名称Y位置\r\n     */\r\n    getNameYPosition() {\r\n      if (this.monster.size === 'huge') return this.tokenRadius * 2 + 15\r\n      if (this.monster.size === 'large') return this.tokenRadius * 1.5 + 12\r\n      return this.tokenRadius + 15\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值颜色\r\n     */\r\n    getHealthColor() {\r\n      const healthPercent = this.getHealthPercentage()\r\n      \r\n      if (healthPercent > 75) return '#4caf50'\r\n      if (healthPercent > 50) return '#ff9800'\r\n      if (healthPercent > 25) return '#f44336'\r\n      return '#9c27b0'\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值百分比\r\n     */\r\n    getHealthPercentage() {\r\n      const current = this.monster.currentHP || this.monster.hitPoints\r\n      const max = this.monster.maxHP || this.monster.hitPoints\r\n      return max > 0 ? (current / max) * 100 : 0\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值条宽度\r\n     */\r\n    getHealthBarWidth() {\r\n      const maxWidth = (this.tokenRadius - 4) * 2\r\n      const healthPercent = this.getHealthPercentage() / 100\r\n      return maxWidth * healthPercent\r\n    },\r\n    \r\n    /**\r\n     * 获取状态效果图标\r\n     */\r\n    getConditionIcon(condition) {\r\n      const iconMap = {\r\n        'unconscious': '💤',\r\n        'dying': '💀',\r\n        'prone': '⬇️',\r\n        'stunned': '😵',\r\n        'restrained': '🔒',\r\n        'frightened': '😨',\r\n        'poisoned': '☠️',\r\n        'bleeding': '🩸',\r\n        'burning': '🔥',\r\n        'frozen': '❄️',\r\n        'paralyzed': '⚡',\r\n        'blinded': '👁️',\r\n        'deafened': '👂',\r\n        'charmed': '💖',\r\n        'confused': '❓',\r\n        'enraged': '😡',\r\n        'invisible': '👻'\r\n      }\r\n      \r\n      return iconMap[condition] || '?'\r\n    },\r\n    \r\n    /**\r\n     * 开始拖拽 (仅KP可用)\r\n     */\r\n    startDrag(event) {\r\n      if (!this.canControl) return\r\n      \r\n      this.isDragging = true\r\n      this.dragStart = {\r\n        x: event.clientX,\r\n        y: event.clientY,\r\n        monsterX: this.monster.position.x,\r\n        monsterY: this.monster.position.y\r\n      }\r\n      \r\n      document.addEventListener('mousemove', this.handleDrag)\r\n      document.addEventListener('mouseup', this.endDrag)\r\n      \r\n      event.stopPropagation()\r\n    },\r\n    \r\n    /**\r\n     * 处理拖拽\r\n     */\r\n    handleDrag(event) {\r\n      if (!this.isDragging || !this.dragStart) return\r\n      \r\n      const deltaX = event.clientX - this.dragStart.x\r\n      const deltaY = event.clientY - this.dragStart.y\r\n      \r\n      const gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom))\r\n      const gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom))\r\n      \r\n      const newX = this.dragStart.monsterX + gridDeltaX\r\n      const newY = this.dragStart.monsterY + gridDeltaY\r\n      \r\n      this.$emit('move', this.monster, { x: newX, y: newY })\r\n    },\r\n    \r\n    /**\r\n     * 结束拖拽\r\n     */\r\n    endDrag() {\r\n      this.isDragging = false\r\n      this.dragStart = null\r\n      \r\n      document.removeEventListener('mousemove', this.handleDrag)\r\n      document.removeEventListener('mouseup', this.endDrag)\r\n    },\r\n    \r\n    /**\r\n     * 播放伤害动画\r\n     */\r\n    playDamageAnimation(damage, type = 'normal') {\r\n      const colors = {\r\n        normal: '#f44336',\r\n        critical: '#e91e63',\r\n        healing: '#4caf50',\r\n        miss: '#9e9e9e'\r\n      }\r\n      \r\n      const texts = {\r\n        normal: `-${damage}`,\r\n        critical: `CRIT! -${damage}`,\r\n        healing: `+${damage}`,\r\n        miss: 'MISS'\r\n      }\r\n      \r\n      this.damageAnimation = {\r\n        text: texts[type] || `-${damage}`,\r\n        color: colors[type] || '#f44336'\r\n      }\r\n      \r\n      setTimeout(() => {\r\n        this.damageAnimation = null\r\n      }, 1500)\r\n    },\r\n    \r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAttackAnimation() {\r\n      this.attackAnimation = true\r\n      \r\n      setTimeout(() => {\r\n        this.attackAnimation = false\r\n      }, 500)\r\n    },\r\n    \r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    playDeathAnimation() {\r\n      // 添加死亡动画效果\r\n      this.$el.style.transition = 'all 1s ease-out'\r\n      this.$el.style.opacity = '0.3'\r\n      this.$el.style.transform += ' scale(0.8)'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.monster-token {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.monster-token:hover {\r\n  filter: brightness(1.1);\r\n}\r\n\r\n.monster-token.selected {\r\n  filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.8));\r\n}\r\n\r\n.monster-token.boss-monster {\r\n  filter: drop-shadow(0 0 12px rgba(156, 39, 176, 0.9));\r\n}\r\n\r\n.monster-token.can-control {\r\n  cursor: move;\r\n}\r\n\r\n.monster-name-text {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.monster-icon {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.challenge-rating circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.status-effects circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.special-abilities circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.damage-animation text {\r\n  font-family: 'Arial Black', sans-serif;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n/* 大型怪物样式 */\r\n.monster-token.large-monster .monster-body rect {\r\n  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n/* 巨型怪物样式 */\r\n.monster-token.huge-monster .monster-body rect {\r\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));\r\n}\r\n\r\n/* Boss怪物特殊效果 */\r\n.monster-token.boss-monster .monster-body {\r\n  animation: bossGlow 3s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes bossGlow {\r\n  from { filter: brightness(1); }\r\n  to { filter: brightness(1.2); }\r\n}\r\n\r\n/* 小怪特殊效果 */\r\n.monster-token.minion-monster {\r\n  opacity: 0.8;\r\n  transform: scale(0.9);\r\n}\r\n</style>"], "mappings": ";;;;;;;;EA8DO,SAAM;AAAc;;;;;;;;;;;;;;;;;;;EAyFnB,aAAW,EAAC,QAAQ;EACpBA,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,GAAG;EACbC,IAAI,EAAC,OAAO;EACZ,aAAW,EAAC;;;;;EAeV,aAAW,EAAC,QAAQ;EACpBD,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,GAAG;EACbC,IAAI,EAAC;;;;;EAYPC,CAAC,EAAC,GAAG;EACLD,IAAI,EAAC,SAAS;EACdE,MAAM,EAAC,MAAM;EACb,cAAY,EAAC;;;;EAIb,aAAW,EAAC,QAAQ;EACpBH,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,GAAG;EACbC,IAAI,EAAC;;;;EAQJG,SAAS,oBAAoB;EAC9BF,CAAC,EAAC,GAAG;EACLD,IAAI,EAAC,SAAS;EACdE,MAAM,EAAC,MAAM;EACb,cAAY,EAAC;;;;EAIZC,SAAS,oBAAoB;EAC9B,aAAW,EAAC,QAAQ;EACpBJ,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,GAAG;EACbC,IAAI,EAAC;;;;EAOiB,SAAM;;;;;;EAwBN,SAAM;;;;;;6DArPlCI,mBAAA,gBAAmB,EACnBC,mBAAA,CAkRI;IAjRF,SAAKC,eAAA,EAAC,eAAe;gBACHC,MAAA,CAAAC,QAAQ;qBAAyBD,MAAA,CAAAE,UAAU;sBAAyBF,MAAA,CAAAG,OAAO,CAACC,MAAM;wBAA2BJ,MAAA,CAAAG,OAAO,CAACE;;IAMtIT,SAAS,eAAAU,MAAA,CAAeC,QAAA,CAAAC,CAAC,QAAAF,MAAA,CAAKC,QAAA,CAAAE,CAAC;IAC/BC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,IAAA,CAAAC,KAAK,WAAWd,MAAA,CAAAG,OAAO;IAAA;IAC9BY,aAAW,EAAAJ,MAAA,QAAAA,MAAA,MAAAK,cAAA,WAAAJ,MAAA;MAAA,OAAUC,IAAA,CAAAC,KAAK,iBAAiBd,MAAA,CAAAG,OAAO,EAAES,MAAM;IAAA;MAE3Df,mBAAA,UAAa,EAELG,MAAA,CAAAC,QAAQ,I,cADhBgB,mBAAA,CAcS;;IAZNvB,CAAC,EAAEwB,KAAA,CAAAC,WAAW;IACf1B,IAAI,EAAC,MAAM;IACXE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChByB,OAAO,EAAC;gCAERtB,mBAAA,CAKE;IAJAuB,aAAa,EAAC,gBAAgB;IAC9BC,MAAM,EAAC,aAAa;IACpBC,GAAG,EAAC,IAAI;IACRC,WAAW,EAAC;gGAIhB3B,mBAAA,gBAAmB,EAEXG,MAAA,CAAAG,OAAO,CAACC,MAAM,I,cADtBa,mBAAA,CAgBS;;IAdNvB,CAAC,EAAEwB,KAAA,CAAAC,WAAW;IACf1B,IAAI,EAAC,MAAM;IACXE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtByB,OAAO,EAAC;gCAERtB,mBAAA,CAME;IALAuB,aAAa,EAAC,WAAW;IACzBI,IAAI,EAAC,QAAQ;IACbH,MAAM,EAAC,OAAO;IACdC,GAAG,EAAC,IAAI;IACRC,WAAW,EAAC;gGAIhB3B,mBAAA,YAAe,EAEPG,MAAA,CAAA0B,eAAe,I,cADvBT,mBAAA,CAQE;;IANCvB,CAAC,EAAEa,QAAA,CAAAoB,WAAW,GAAG3B,MAAA,CAAA4B,QAAQ;IAC1BnC,IAAI,EAAC,wBAAwB;IAC7BE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtByB,OAAO,EAAC;4EAGVvB,mBAAA,UAAa,EACbC,mBAAA,CAuEI,KAvEJ+B,UAuEI,GAtEFhC,mBAAA,qBAAwB,EACfG,MAAA,CAAAG,OAAO,CAAC2B,IAAI,gB,cAArBb,mBAAA,CAaI,KAAAc,UAAA,GAZFlC,mBAAA,kBAAqB,EACrBC,mBAAA,CAUE;IATCU,CAAC,GAAGU,KAAA,CAAAC,WAAW;IACfV,CAAC,GAAGS,KAAA,CAAAC,WAAW;IACfa,KAAK,EAAEd,KAAA,CAAAC,WAAW;IAClBc,MAAM,EAAEf,KAAA,CAAAC,WAAW;IACnB1B,IAAI,EAAEc,QAAA,CAAA2B,eAAe;IACtBvC,MAAM,EAAC,MAAM;IACb,cAAY,EAAC,GAAG;IAChBwC,EAAE,EAAC,GAAG;IACNf,OAAO,EAAC;2CAGEpB,MAAA,CAAAG,OAAO,CAAC2B,IAAI,e,cAA1Bb,mBAAA,CAaI,KAAAmB,UAAA,GAZFvC,mBAAA,kBAAqB,EACrBC,mBAAA,CAUE;IATCU,CAAC,GAAGU,KAAA,CAAAC,WAAW;IACfV,CAAC,GAAGS,KAAA,CAAAC,WAAW;IACfa,KAAK,EAAEd,KAAA,CAAAC,WAAW;IAClBc,MAAM,EAAEf,KAAA,CAAAC,WAAW;IACnB1B,IAAI,EAAEc,QAAA,CAAA2B,eAAe;IACtBvC,MAAM,EAAC,MAAM;IACb,cAAY,EAAC,GAAG;IAChBwC,EAAE,EAAC,GAAG;IACNf,OAAO,EAAC;0DAGZH,mBAAA,CASI,KAAAoB,WAAA,GARFxC,mBAAA,eAAkB,EAClBC,mBAAA,CAME;IALCJ,CAAC,EAAEwB,KAAA,CAAAC,WAAW;IACd1B,IAAI,EAAEc,QAAA,CAAA2B,eAAe;IACtBvC,MAAM,EAAC,MAAM;IACb,cAAY,EAAC,GAAG;IAChByB,OAAO,EAAC;2CAIZvB,mBAAA,UAAa,EACbC,mBAAA,CAQO;IAPL,aAAW,EAAC,QAAQ;IACpBN,EAAE,EAAC,GAAG;IACL,WAAS,EAAEe,QAAA,CAAA+B,WAAW;IACvB7C,IAAI,EAAC,OAAO;IACZ,SAAM;sBAEHc,QAAA,CAAAgC,cAAc,0BAAAC,WAAA,GAGnB3C,mBAAA,UAAa,EACbC,mBAAA,CAQE;IAPCU,CAAC,GAAGU,KAAA,CAAAC,WAAW;IACfV,CAAC,EAAES,KAAA,CAAAC,WAAW;IACda,KAAK,GAAGd,KAAA,CAAAC,WAAW;IACpBc,MAAM,EAAC,GAAG;IACVxC,IAAI,EAAC,MAAM;IACX2B,OAAO,EAAC,KAAK;IACbe,EAAE,EAAC;wCAELrC,mBAAA,CAQE;IAPCU,CAAC,GAAGU,KAAA,CAAAC,WAAW;IACfV,CAAC,EAAES,KAAA,CAAAC,WAAW;IACda,KAAK,EAAEzB,QAAA,CAAAkC,iBAAiB;IACzBR,MAAM,EAAC,GAAG;IACTxC,IAAI,EAAEc,QAAA,CAAAmC,cAAc;IACrBtB,OAAO,EAAC,KAAK;IACbe,EAAE,EAAC;0CAIPtC,mBAAA,aAAgB,EAChBC,mBAAA,CASO;IARJW,CAAC,EAAEF,QAAA,CAAAoC,gBAAgB;IACpB,aAAW,EAAC,QAAQ;IACpB,WAAS,EAAC,IAAI;IACd,aAAW,EAAC,MAAM;IAClBlD,IAAI,EAAC,SAAS;IACd,SAAM;sBAEHO,MAAA,CAAAG,OAAO,CAACyC,IAAI,wBAAAC,WAAA,GAGjBhD,mBAAA,YAAe,EACfC,mBAAA,CAWI;IAXD,SAAM,kBAAkB;IAAEF,SAAS,eAAAU,MAAA,CAAeY,KAAA,CAAAC,WAAW,aAAAb,MAAA,EAAWY,KAAA,CAAAC,WAAW;gCACpFrB,mBAAA,CAA8D;IAAtDJ,CAAC,EAAC,IAAI;IAACD,IAAI,EAAC,SAAS;IAACE,MAAM,EAAC,MAAM;IAAC,cAAY,EAAC;8BACzDG,mBAAA,CAQO,QARPgD,WAQO,EAAAC,gBAAA,CADF/C,MAAA,CAAAG,OAAO,CAAC6C,eAAe,wB,+BAI9BnD,mBAAA,YAAe,EACfC,mBAAA,CAgBI;IAhBD,SAAM,gBAAgB;IAAEF,SAAS,eAAAU,MAAA,EAAgBY,KAAA,CAAAC,WAAW,QAAAb,MAAA,EAAMC,QAAA,CAAAoC,gBAAgB;yBACnF1B,mBAAA,CAcIgC,SAAA,QAAAC,WAAA,CAb2BlD,MAAA,CAAAG,OAAO,CAACgD,UAAU,YAAvCC,SAAS,EAAEC,KAAK;yBAD1BpC,mBAAA,CAcI;MAZDqC,GAAG,EAAEF,SAAS;MACdxD,SAAS,eAAAU,MAAA,CAAe+C,KAAK;kCAE9BvD,mBAAA,CAAyC;MAAjCJ,CAAC,EAAC,GAAG;MAACD,IAAI,EAAC,MAAM;MAAC2B,OAAO,EAAC;gCAClCtB,mBAAA,CAOO,QAPPyD,WAOO,EAAAR,gBAAA,CADFxC,QAAA,CAAAiD,gBAAgB,CAACJ,SAAS,kB;gEAKnCvD,mBAAA,aAAgB,EAChBC,mBAAA,CAsCI;IAtCD,SAAM,mBAAmB;IAAEF,SAAS,eAAAU,MAAA,EAAgBY,KAAA,CAAAC,WAAW,YAAAb,MAAA,CAASY,KAAA,CAAAC,WAAW;MACpFtB,mBAAA,UAAa,EAELG,MAAA,CAAAG,OAAO,CAACsD,SAAS,I,cADzBxC,mBAAA,CAME,UANFyC,WAME,K,mCAEM1D,MAAA,CAAAG,OAAO,CAACsD,SAAS,I,cADzBxC,mBAAA,CAQO,QARP0C,WAQO,EAFN,KAED,K,mCAEA9D,mBAAA,UAAa,EAELG,MAAA,CAAAG,OAAO,CAACyD,mBAAmB,I,cADnC3C,mBAAA,CAOE,UAPF4C,WAOE,K,mCAEM7D,MAAA,CAAAG,OAAO,CAACyD,mBAAmB,I,cADnC3C,mBAAA,CASO,QATP6C,WASO,EAFN,KAED,K,iEAGFjE,mBAAA,YAAe,EACNqB,KAAA,CAAA6C,eAAe,I,cAAxB9C,mBAAA,CAqBI,KArBJ+C,WAqBI,GApBFlE,mBAAA,CAmBO;IAlBJW,CAAC,GAAGF,QAAA,CAAAoC,gBAAgB;IACrB,aAAW,EAAC,QAAQ;IACpB,WAAS,EAAC,IAAI;IACd,aAAW,EAAC,MAAM;IACjBlD,IAAI,EAAEyB,KAAA,CAAA6C,eAAe,CAACE,KAAK;IAC5B7C,OAAO,EAAC;wCAELF,KAAA,CAAA6C,eAAe,CAACG,IAAI,IAAG,GAC1B,iBAAApE,mBAAA,CAIE;IAHAuB,aAAa,EAAC,GAAG;IAChBC,MAAM,KAAAhB,MAAA,EAAMC,QAAA,CAAAoC,gBAAgB,cAAArC,MAAA,EAAYC,QAAA,CAAAoC,gBAAgB;IACzDpB,GAAG,EAAC;kEAENzB,mBAAA,CAIE;IAHAuB,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IAChBC,GAAG,EAAC;mGAKV1B,mBAAA,YAAe,EACNqB,KAAA,CAAAiD,eAAe,I,cAAxBlD,mBAAA,CAmBI,KAnBJmD,WAmBI,GAlBFtE,mBAAA,CAiBS;IAhBNJ,CAAC,EAAEwB,KAAA,CAAAC,WAAW;IACf1B,IAAI,EAAC,MAAM;IACXE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChByB,OAAO,EAAC;MAERtB,mBAAA,CAIE;IAHAuB,aAAa,EAAC,GAAG;IAChBC,MAAM,KAAAhB,MAAA,CAAKY,KAAA,CAAAC,WAAW,OAAAb,MAAA,CAAIY,KAAA,CAAAC,WAAW;IACtCI,GAAG,EAAC;kEAENzB,mBAAA,CAIE;IAHAuB,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,OAAO;IACdC,GAAG,EAAC;mGAKV1B,mBAAA,YAAe,EAEPG,MAAA,CAAAE,UAAU,IAAIF,MAAA,CAAAC,QAAQ,I,cAD9BgB,mBAAA,CAOE;;IALCvB,CAAC,EAAEwB,KAAA,CAAAC,WAAW;IACf1B,IAAI,EAAC,aAAa;IAClBE,MAAM,EAAC,MAAM;IACb0E,KAAoB,EAApB;MAAA;IAAA,CAAoB;IACnBC,WAAS,EAAA3D,MAAA,QAAAA,MAAA;MAAA,OAAEJ,QAAA,CAAAgE,SAAA,IAAAhE,QAAA,CAAAgE,SAAA,CAAAC,KAAA,CAAAjE,QAAA,EAAAkE,SAAA,CAAS;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}