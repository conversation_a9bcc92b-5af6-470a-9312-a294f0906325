<template>
  <div class="combat-log">
    <!-- 日志头部 -->
    <div class="log-header">
      <div class="header-left">
        <i class="fas fa-scroll"></i>
        <h3>战斗日志</h3>
      </div>
      <div class="header-right">
        <button @click="toggleAutoScroll" class="auto-scroll-btn" :class="{ active: autoScroll }">
          <i class="fas fa-arrow-down"></i>
        </button>
        <button @click="clearLog" class="clear-btn" title="清空日志">
          <i class="fas fa-trash"></i>
        </button>
        <button @click="exportLog" class="export-btn" title="导出日志">
          <i class="fas fa-download"></i>
        </button>
      </div>
    </div>

    <!-- 过滤器 -->
    <div class="log-filters">
      <div class="filter-group">
        <button 
          v-for="filter in logFilters" 
          :key="filter.type"
          @click="toggleFilter(filter.type)"
          class="filter-btn"
          :class="{ active: activeFilters.includes(filter.type) }"
        >
          <i :class="filter.icon"></i>
          <span>{{ filter.name }}</span>
        </button>
      </div>
      
      <div class="search-box">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="搜索日志..."
          class="search-input"
        >
        <i class="fas fa-search search-icon"></i>
      </div>
    </div>

    <!-- 日志内容 -->
    <div class="log-content" ref="logContent">
      <div 
        v-for="(entry, index) in filteredLogs" 
        :key="entry.id || index"
        class="log-entry"
        :class="[entry.type, entry.severity || 'normal']"
      >
        <!-- 时间戳 -->
        <div class="log-timestamp">
          {{ formatTime(entry.timestamp) }}
        </div>
        
        <!-- 轮次信息 -->
        <div class="log-round" v-if="entry.round">
          第{{ entry.round }}轮
        </div>
        
        <!-- 日志图标 -->
        <div class="log-icon">
          <i :class="getLogIcon(entry.type)"></i>
        </div>
        
        <!-- 日志内容 -->
        <div class="log-message">
          <div class="message-text" v-html="formatMessage(entry.message)"></div>
          
          <!-- 详细信息 -->
          <div class="message-details" v-if="entry.details">
            <div 
              v-for="(detail, key) in entry.details" 
              :key="key"
              class="detail-item"
            >
              <span class="detail-label">{{ formatDetailLabel(key) }}:</span>
              <span class="detail-value">{{ formatDetailValue(detail) }}</span>
            </div>
          </div>
          
          <!-- 骰子结果 -->
          <div class="dice-result" v-if="entry.diceRoll">
            <div class="dice-formula">{{ entry.diceRoll.formula }}</div>
            <div class="dice-rolls">
              <span 
                v-for="(roll, i) in entry.diceRoll.rolls" 
                :key="i"
                class="dice-roll"
                :class="getDiceRollClass(roll, entry.diceRoll)"
              >
                {{ roll }}
              </span>
            </div>
            <div class="dice-total">总计: {{ entry.diceRoll.total }}</div>
          </div>
          
          <!-- 成功等级 -->
          <div class="success-level" v-if="entry.successLevel">
            <span class="success-badge" :class="entry.successLevel">
              {{ getSuccessLevelText(entry.successLevel) }}
            </span>
          </div>
        </div>
        
        <!-- 参与者头像 -->
        <div class="log-participant" v-if="entry.participant">
          <img 
            :src="entry.participant.avatar || '/default-avatar.png'" 
            :alt="entry.participant.name"
            class="participant-avatar"
          >
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredLogs.length === 0" class="empty-state">
        <i class="fas fa-inbox"></i>
        <p>{{ searchQuery ? '没有找到匹配的日志' : '暂无战斗日志' }}</p>
      </div>
    </div>

    <!-- 底部状态 -->
    <div class="log-footer">
      <div class="log-stats">
        <span>总计: {{ combatLogs.length }} 条</span>
        <span>显示: {{ filteredLogs.length }} 条</span>
      </div>
      <div class="log-actions">
        <button @click="scrollToTop" class="scroll-btn">
          <i class="fas fa-arrow-up"></i>
          顶部
        </button>
        <button @click="scrollToBottom" class="scroll-btn">
          <i class="fas fa-arrow-down"></i>
          底部
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CombatLog',
  props: {
    combatLogs: {
      type: Array,
      default: () => []
    },
    autoScroll: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      // 过滤器
      activeFilters: ['all'],
      searchQuery: '',
      
      // 日志过滤器配置
      logFilters: [
        { type: 'all', name: '全部', icon: 'fas fa-list' },
        { type: 'attack', name: '攻击', icon: 'fas fa-sword' },
        { type: 'damage', name: '伤害', icon: 'fas fa-heart-broken' },
        { type: 'heal', name: '治疗', icon: 'fas fa-heart' },
        { type: 'move', name: '移动', icon: 'fas fa-walking' },
        { type: 'skill', name: '技能', icon: 'fas fa-dice-d20' },
        { type: 'status', name: '状态', icon: 'fas fa-magic' },
        { type: 'system', name: '系统', icon: 'fas fa-cog' }
      ],
      
      // 自动滚动状态
      autoScrollEnabled: true
    }
  },
  
  computed: {
    // 过滤后的日志
    filteredLogs() {
      let logs = this.combatLogs
      
      // 类型过滤
      if (!this.activeFilters.includes('all')) {
        logs = logs.filter(log => this.activeFilters.includes(log.type))
      }
      
      // 搜索过滤
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase()
        logs = logs.filter(log => 
          log.message.toLowerCase().includes(query) ||
          log.participant?.name.toLowerCase().includes(query)
        )
      }
      
      return logs
    }
  },
  
  watch: {
    // 监听日志变化，自动滚动到底部
    combatLogs: {
      handler() {
        if (this.autoScrollEnabled) {
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      },
      deep: true
    }
  },
  
  methods: {
    // 切换过滤器
    toggleFilter(filterType) {
      if (filterType === 'all') {
        this.activeFilters = ['all']
      } else {
        const index = this.activeFilters.indexOf(filterType)
        if (index > -1) {
          this.activeFilters.splice(index, 1)
          if (this.activeFilters.length === 0) {
            this.activeFilters = ['all']
          }
        } else {
          this.activeFilters = this.activeFilters.filter(f => f !== 'all')
          this.activeFilters.push(filterType)
        }
      }
    },
    
    // 切换自动滚动
    toggleAutoScroll() {
      this.autoScrollEnabled = !this.autoScrollEnabled
      this.$emit('toggle-auto-scroll', this.autoScrollEnabled)
    },
    
    // 清空日志
    clearLog() {
      this.$emit('clear-log')
    },
    
    // 导出日志
    exportLog() {
      const logText = this.combatLogs.map(entry => {
        const time = this.formatTime(entry.timestamp)
        const round = entry.round ? `[第${entry.round}轮] ` : ''
        return `${time} ${round}${entry.message}`
      }).join('\n')
      
      const blob = new Blob([logText], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `combat-log-${new Date().toISOString().slice(0, 10)}.txt`
      a.click()
      URL.revokeObjectURL(url)
    },
    
    // 滚动到顶部
    scrollToTop() {
      const content = this.$refs.logContent
      if (content) {
        content.scrollTop = 0
      }
    },
    
    // 滚动到底部
    scrollToBottom() {
      const content = this.$refs.logContent
      if (content) {
        content.scrollTop = content.scrollHeight
      }
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    
    // 格式化消息
    formatMessage(message) {
      // 高亮关键词
      return message
        .replace(/(\d+)点伤害/g, '<span class="damage-highlight">$1点伤害</span>')
        .replace(/(\d+)点治疗/g, '<span class="heal-highlight">$1点治疗</span>')
        .replace(/(大成功|极难成功|困难成功|常规成功|失败|大失败)/g, '<span class="success-highlight">$1</span>')
        .replace(/投掷(\d+)/g, '<span class="roll-highlight">投掷$1</span>')
    },
    
    // 获取日志图标
    getLogIcon(type) {
      const icons = {
        attack: 'fas fa-sword',
        damage: 'fas fa-heart-broken',
        heal: 'fas fa-heart',
        move: 'fas fa-walking',
        skill: 'fas fa-dice-d20',
        status: 'fas fa-magic',
        system: 'fas fa-cog',
        round_start: 'fas fa-play',
        round_end: 'fas fa-stop',
        combat_start: 'fas fa-flag',
        combat_end: 'fas fa-flag-checkered'
      }
      return icons[type] || 'fas fa-info-circle'
    },
    
    // 格式化详细信息标签
    formatDetailLabel(key) {
      const labels = {
        attacker: '攻击者',
        target: '目标',
        weapon: '武器',
        damage: '伤害',
        skill: '技能',
        roll: '投掷',
        modifier: '修正',
        result: '结果'
      }
      return labels[key] || key
    },
    
    // 格式化详细信息值
    formatDetailValue(value) {
      if (typeof value === 'object') {
        return JSON.stringify(value)
      }
      return String(value)
    },
    
    // 获取骰子投掷样式类
    getDiceRollClass(roll, diceRoll) {
      if (diceRoll.formula.includes('d100')) {
        if (roll === 1) return 'critical-success'
        if (roll >= 96) return 'critical-failure'
      } else if (diceRoll.formula.includes('d20')) {
        if (roll === 20) return 'critical-success'
        if (roll === 1) return 'critical-failure'
      }
      return 'normal'
    },
    
    // 获取成功等级文本
    getSuccessLevelText(level) {
      const texts = {
        critical: '大成功',
        extreme: '极难成功',
        hard: '困难成功',
        regular: '常规成功',
        failure: '失败',
        fumble: '大失败'
      }
      return texts[level] || level
    }
  }
}
</script>

<style scoped>
.combat-log {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #0f3460;
  border-radius: 12px;
  padding: 16px;
  color: #e94560;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 日志头部 */
.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #0f3460;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left h3 {
  margin: 0;
  color: #e94560;
  font-size: 1.1rem;
  font-weight: bold;
}

.header-right {
  display: flex;
  gap: 6px;
}

.auto-scroll-btn,
.clear-btn,
.export-btn {
  width: 28px;
  height: 28px;
  background: rgba(15, 52, 96, 0.8);
  border: 1px solid #0f3460;
  border-radius: 4px;
  color: #e94560;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.3s ease;
}

.auto-scroll-btn:hover,
.clear-btn:hover,
.export-btn:hover {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
}

.auto-scroll-btn.active {
  background: rgba(233, 69, 96, 0.3);
  border-color: #e94560;
}

/* 过滤器 */
.log-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  gap: 12px;
}

.filter-group {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 4px 8px;
  background: rgba(15, 52, 96, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #bdc3c7;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: rgba(15, 52, 96, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
}

.filter-btn.active {
  background: rgba(233, 69, 96, 0.3);
  border-color: #e94560;
  color: #e94560;
}

.search-box {
  position: relative;
  min-width: 150px;
}

.search-input {
  width: 100%;
  padding: 6px 30px 6px 10px;
  background: rgba(15, 52, 96, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  color: #ecf0f1;
  font-size: 0.9rem;
}

.search-input::placeholder {
  color: #bdc3c7;
}

.search-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: #bdc3c7;
  font-size: 0.8rem;
}

/* 日志内容 */
.log-content {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 12px;
  padding-right: 8px;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  margin-bottom: 6px;
  background: rgba(15, 52, 96, 0.2);
  border-left: 3px solid transparent;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.log-entry:hover {
  background: rgba(15, 52, 96, 0.3);
}

/* 日志类型样式 */
.log-entry.attack { border-left-color: #e74c3c; }
.log-entry.damage { border-left-color: #c0392b; }
.log-entry.heal { border-left-color: #27ae60; }
.log-entry.move { border-left-color: #3498db; }
.log-entry.skill { border-left-color: #9b59b6; }
.log-entry.status { border-left-color: #f39c12; }
.log-entry.system { border-left-color: #95a5a6; }

/* 严重程度样式 */
.log-entry.critical {
  background: rgba(231, 76, 60, 0.1);
  border-left-color: #e74c3c;
}

.log-entry.warning {
  background: rgba(243, 156, 18, 0.1);
  border-left-color: #f39c12;
}

.log-entry.success {
  background: rgba(39, 174, 96, 0.1);
  border-left-color: #27ae60;
}

.log-timestamp {
  font-size: 0.7rem;
  color: #7f8c8d;
  min-width: 60px;
  text-align: right;
}

.log-round {
  font-size: 0.7rem;
  color: #e94560;
  background: rgba(233, 69, 96, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 50px;
  text-align: center;
}

.log-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #e94560;
  font-size: 0.9rem;
}

.log-message {
  flex: 1;
  font-size: 0.9rem;
  line-height: 1.4;
}

.message-text {
  color: #ecf0f1;
  margin-bottom: 4px;
}

.message-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 4px;
}

.detail-item {
  font-size: 0.8rem;
  color: #bdc3c7;
}

.detail-label {
  font-weight: bold;
}

.detail-value {
  color: #ecf0f1;
}

/* 骰子结果 */
.dice-result {
  margin-top: 6px;
  padding: 6px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dice-formula {
  font-size: 0.8rem;
  color: #bdc3c7;
  margin-bottom: 4px;
}

.dice-rolls {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
}

.dice-roll {
  padding: 2px 6px;
  background: rgba(52, 73, 94, 0.8);
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  color: #ecf0f1;
}

.dice-roll.critical-success {
  background: #27ae60;
  color: white;
}

.dice-roll.critical-failure {
  background: #e74c3c;
  color: white;
}

.dice-total {
  font-size: 0.8rem;
  font-weight: bold;
  color: #e94560;
}

/* 成功等级 */
.success-level {
  margin-top: 4px;
}

.success-badge {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
}

.success-badge.critical { background: #27ae60; color: white; }
.success-badge.extreme { background: #2ecc71; color: white; }
.success-badge.hard { background: #3498db; color: white; }
.success-badge.regular { background: #95a5a6; color: white; }
.success-badge.failure { background: #e67e22; color: white; }
.success-badge.fumble { background: #e74c3c; color: white; }

.log-participant {
  width: 24px;
  height: 24px;
}

.participant-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 高亮样式 */
.damage-highlight {
  color: #e74c3c;
  font-weight: bold;
}

.heal-highlight {
  color: #27ae60;
  font-weight: bold;
}

.success-highlight {
  color: #f39c12;
  font-weight: bold;
}

.roll-highlight {
  color: #3498db;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #7f8c8d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 16px;
}

/* 底部状态 */
.log-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.log-stats {
  display: flex;
  gap: 12px;
  font-size: 0.8rem;
  color: #bdc3c7;
}

.log-actions {
  display: flex;
  gap: 6px;
}

.scroll-btn {
  padding: 4px 8px;
  background: rgba(15, 52, 96, 0.8);
  border: 1px solid #0f3460;
  border-radius: 4px;
  color: #e94560;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
}

.scroll-btn:hover {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track {
  background: rgba(15, 52, 96, 0.3);
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb {
  background: rgba(233, 69, 96, 0.6);
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: rgba(233, 69, 96, 0.8);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .combat-log {
    padding: 12px;
  }
  
  .log-filters {
    flex-direction: column;
    gap: 8px;
  }
  
  .filter-group {
    justify-content: center;
  }
  
  .log-entry {
    padding: 6px;
    gap: 6px;
  }
  
  .log-timestamp {
    min-width: 50px;
    font-size: 0.6rem;
  }
  
  .log-round {
    min-width: 40px;
    font-size: 0.6rem;
  }
  
  .message-text {
    font-size: 0.8rem;
  }
  
  .log-footer {
    flex-direction: column;
    gap: 8px;
  }
}
</style>