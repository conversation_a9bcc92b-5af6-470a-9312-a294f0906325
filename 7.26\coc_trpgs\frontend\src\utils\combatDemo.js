/**
 * COC 7版战斗系统演示
 * 展示完整的战斗流程
 */

import { combatSystem } from './combatSystem.js'

console.log('=== COC 7版战斗系统演示 ===\n')

// 创建测试角色
const investigator = {
  id: 'inv1',
  name: '调查员约翰',
  faction: 'investigators',
  
  // 基础属性
  strength: 70,
  dexterity: 60,
  constitution: 65,
  size: 65,
  
  // 技能
  fighting: 60,
  firearms: 50,
  dodge: 30,
  
  // 生命值和理智值
  hitPoints: 13,
  sanity: 65,
  
  // 装备
  currentWeapon: {
    name: '.38左轮手枪',
    damage: '1d10',
    skill: 'firearms',
    type: 'firearm',
    range: { base: 15, long: 30, extreme: 60 },
    impaling: false,
    ammo: 6,
    currentAmmo: 6
  },
  
  armor: 0
}

const cultist = {
  id: 'cult1',
  name: '邪教徒',
  faction: 'enemies',
  
  // 基础属性
  strength: 60,
  dexterity: 50,
  constitution: 55,
  size: 60,
  
  // 技能
  fighting: 50,
  firearms: 40,
  dodge: 25,
  
  // 生命值和理智值
  hitPoints: 11,
  sanity: 30,
  
  // 装备
  currentWeapon: {
    name: '匕首',
    damage: '1d4+2',
    skill: 'fighting',
    type: 'melee',
    impaling: true
  },
  
  armor: 0
}

// 开始战斗
console.log('🎲 开始战斗！')
const combat = combatSystem.startCombat([investigator, cultist], {
  location: '废弃仓库',
  lighting: 'dim',
  cover: false
})

console.log(`战斗ID: ${combat.id}`)
console.log(`环境: ${combat.environment.location}`)

// 显示先攻顺序
console.log('\n📋 先攻顺序:')
combatSystem.participants.forEach((p, index) => {
  console.log(`${index + 1}. ${p.name} (先攻: ${p.initiative})`)
})

// 模拟几轮战斗
let round = 1
let maxRounds = 5

while (round <= maxRounds && combatSystem.currentCombat.status === 'active') {
  console.log(`\n=== 第${round}轮 ===`)
  
  for (let turn = 0; turn < combatSystem.participants.length; turn++) {
    const currentParticipant = combatSystem.getCurrentParticipant()
    
    if (!currentParticipant || currentParticipant.status !== 'active') {
      combatSystem.nextTurn()
      continue
    }
    
    console.log(`\n🎯 ${currentParticipant.name} 的回合`)
    console.log(`生命值: ${currentParticipant.currentHP}/${currentParticipant.hitPoints}`)
    
    // 寻找目标
    const enemies = combatSystem.participants.filter(p => 
      p.faction !== currentParticipant.faction && p.status === 'active'
    )
    
    if (enemies.length === 0) {
      console.log('没有敌人，战斗结束！')
      combatSystem.endCombat('no_enemies')
      break
    }
    
    const target = enemies[0]
    console.log(`目标: ${target.name}`)
    
    // 执行攻击
    try {
      const attackOptions = {
        distance: currentParticipant.currentWeapon.type === 'firearm' ? 10 : 1,
        aiming: Math.random() > 0.5, // 50%概率瞄准
        defenseType: 'dodge'
      }
      
      console.log(`使用武器: ${currentParticipant.currentWeapon.name}`)
      if (attackOptions.aiming) {
        console.log('📍 瞄准攻击 (+1奖励骰)')
      }
      
      const result = combatSystem.performAttack(
        currentParticipant.id,
        target.id,
        attackOptions
      )
      
      // 显示攻击结果
      console.log(`攻击投掷: ${result.attackRoll} (${result.attackLevel})`)
      console.log(`防御投掷: ${result.defenseRoll} (${result.defenseLevel})`)
      console.log(`结果: ${result.opposedResult.description}`)
      
      if (result.damage > 0) {
        console.log(`💥 造成 ${result.damage} 点伤害！`)
        console.log(`${target.name} 剩余生命值: ${target.currentHP}/${target.hitPoints}`)
        
        if (target.currentHP <= 0) {
          console.log(`💀 ${target.name} 倒下了！`)
        }
      } else {
        console.log(`🛡️ 攻击被成功防御！`)
      }
      
    } catch (error) {
      console.log(`❌ 攻击失败: ${error.message}`)
    }
    
    // 检查战斗是否结束
    const activeCombatants = combatSystem.participants.filter(p => p.status === 'active')
    const factions = [...new Set(activeCombatants.map(p => p.faction))]
    
    if (factions.length <= 1) {
      const winner = factions[0] || 'none'
      console.log(`\n🏆 战斗结束！获胜方: ${winner}`)
      combatSystem.endCombat('victory')
      break
    }
    
    combatSystem.nextTurn()
  }
  
  if (combatSystem.currentCombat.status !== 'active') {
    break
  }
  
  round++
}

// 显示战斗总结
console.log('\n=== 战斗总结 ===')
const status = combatSystem.getCombatStatus()

console.log(`战斗时长: ${round} 轮`)
console.log(`总行动数: ${status.log.filter(e => e.type === 'attack').length}`)

console.log('\n参与者最终状态:')
status.participants.forEach(p => {
  const healthPercent = Math.round((p.currentHP / (p.hitPoints || p.hp)) * 100)
  console.log(`${p.name}: ${p.currentHP}/${p.hitPoints}HP (${healthPercent}%) - ${p.status}`)
})

console.log('\n战斗日志:')
status.log.forEach((entry, index) => {
  const time = new Date(entry.timestamp).toLocaleTimeString()
  switch (entry.type) {
    case 'combat_start':
      console.log(`${index + 1}. [${time}] 战斗开始`)
      break
    case 'attack':
      const data = entry.data
      console.log(`${index + 1}. [${time}] ${data.attacker} 攻击 ${data.defender} (${data.opposedResult.description})`)
      break
    case 'casualty':
      console.log(`${index + 1}. [${time}] ${entry.data.name} 倒下`)
      break
    case 'new_round':
      console.log(`${index + 1}. [${time}] 第${entry.data.round}轮开始`)
      break
    case 'combat_end':
      console.log(`${index + 1}. [${time}] 战斗结束 (${entry.data.reason})`)
      break
  }
})

console.log('\n=== 演示完成 ===')
console.log('COC 7版战斗系统核心功能已实现并验证！')