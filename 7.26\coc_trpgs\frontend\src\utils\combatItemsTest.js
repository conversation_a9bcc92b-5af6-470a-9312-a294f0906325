/**
 * COC 7版战斗道具系统测试
 * 验证道具使用和背包对接功能
 */

import CombatItems from './combatItems.js'

console.log('=== COC 7版战斗道具系统测试 ===\n')

// 创建测试角色
const medic = {
  id: 'medic1',
  name: '医生',
  
  // 技能
  first_aid: 80,
  medicine: 70,
  locksmith: 30,
  psychology: 60,
  
  // 生命值和理智值
  hitPoints: 12,
  currentHP: 6, // 受伤状态
  sanity: 65,
  currentSAN: 50, // 理智受损
  
  // 状态
  conditions: ['bleeding', 'injured'],
  
  // 背包道具
  inventory: {
    first_aid_kit: { quantity: 2, uses: 3 },
    medical_bag: { quantity: 1, uses: 5 },
    morphine: { quantity: 1, uses: 1 },
    bandage: { quantity: 3, uses: 1 },
    lockpicks: { quantity: 1, uses: -1 },
    flashlight: { quantity: 1, uses: 8 },
    holy_water: { quantity: 1, uses: 1 },
    stimulant: { quantity: 2, uses: 1 },
    alcohol: { quantity: 1, uses: 3 },
    lucky_charm: { quantity: 1, uses: 3 }
  }
}

const patient = {
  id: 'patient1',
  name: '伤患',
  hitPoints: 10,
  currentHP: 3, // 重伤
  sanity: 50,
  currentSAN: 45,
  conditions: ['bleeding', 'major_wound', 'poisoned']
}

const evilCreature = {
  id: 'evil1',
  name: '邪恶生物',
  type: 'evil',
  hitPoints: 15,
  currentHP: 15
}

// 测试1: 治疗道具使用
console.log('=== 测试1: 治疗道具使用 ===')

console.log(`${medic.name} 初始状态:`)
console.log(`  生命值: ${medic.currentHP}/${medic.hitPoints}`)
console.log(`  状态: ${medic.conditions.join(', ')}`)

console.log(`\n${patient.name} 初始状态:`)
console.log(`  生命值: ${patient.currentHP}/${patient.hitPoints}`)
console.log(`  状态: ${patient.conditions.join(', ')}`)

// 使用急救包治疗自己
console.log(`\n1.1 ${medic.name} 使用急救包治疗自己:`)
const selfHeal = CombatItems.useHealingItem(medic, medic, 'first_aid_kit')
console.log(`  ${selfHeal.description}`)
console.log(`  技能检定: ${selfHeal.result.skillRoll} vs ${selfHeal.result.skillValue} (${selfHeal.result.successLevel})`)
if (selfHeal.result.success) {
  console.log(`  治疗后生命值: ${medic.currentHP}/${medic.hitPoints}`)
  console.log(`  状态: ${medic.conditions.join(', ') || '无'}`)
}

// 使用医疗包治疗伤患
console.log(`\n1.2 ${medic.name} 使用医疗包治疗${patient.name}:`)
const patientHeal = CombatItems.useHealingItem(medic, patient, 'medical_bag')
console.log(`  ${patientHeal.description}`)
console.log(`  技能检定: ${patientHeal.result.skillRoll} vs ${patientHeal.result.skillValue} (${patientHeal.result.successLevel})`)
if (patientHeal.result.success) {
  console.log(`  治疗后生命值: ${patient.currentHP}/${patient.hitPoints}`)
  console.log(`  状态: ${patient.conditions.join(', ') || '无'}`)
}

// 使用吗啡
console.log(`\n1.3 ${medic.name} 使用吗啡:`)
const morphineUse = CombatItems.useHealingItem(medic, medic, 'morphine')
console.log(`  ${morphineUse.description}`)
if (morphineUse.result.success) {
  console.log(`  效果: ${morphineUse.result.effectsApplied.join(', ')}`)
}

// 测试2: 工具道具使用
console.log('\n\n=== 测试2: 工具道具使用 ===')

// 使用撬锁工具
console.log(`\n2.1 ${medic.name} 使用撬锁工具:`)
const lockpickUse = CombatItems.useToolItem(medic, 'lockpicks', 'locksmith')
console.log(`  ${lockpickUse.description}`)
if (lockpickUse.result.success) {
  console.log(`  技能加值: +${lockpickUse.result.skillBonus}`)
  console.log(`  特殊效果: ${lockpickUse.result.specialEffects.join(', ') || '无'}`)
}

// 使用手电筒
console.log(`\n2.2 ${medic.name} 使用手电筒:`)
const flashlightUse = CombatItems.useToolItem(medic, 'flashlight', 'spot_hidden')
console.log(`  ${flashlightUse.description}`)
if (flashlightUse.result.success) {
  console.log(`  特殊效果: ${flashlightUse.result.specialEffects.join(', ')}`)
}

// 测试3: 特殊道具使用
console.log('\n\n=== 测试3: 特殊道具使用 ===')

// 使用圣水对付邪恶生物
console.log(`\n3.1 ${medic.name} 对${evilCreature.name}使用圣水:`)
console.log(`  ${evilCreature.name} 使用前生命值: ${evilCreature.currentHP}/${evilCreature.hitPoints}`)

const holyWaterUse = CombatItems.useSpecialItem(medic, 'holy_water', evilCreature)
console.log(`  ${holyWaterUse.description}`)
if (holyWaterUse.result.success) {
  console.log(`  效果: ${holyWaterUse.result.effects.join(', ')}`)
  console.log(`  ${evilCreature.name} 使用后生命值: ${evilCreature.currentHP}/${evilCreature.hitPoints}`)
}

// 使用幸运符
console.log(`\n3.2 ${medic.name} 使用幸运符:`)
const luckyCharmUse = CombatItems.useSpecialItem(medic, 'lucky_charm')
console.log(`  ${luckyCharmUse.description}`)
if (luckyCharmUse.result.success) {
  console.log(`  效果: ${luckyCharmUse.result.effects.join(', ')}`)
}

// 测试4: 消耗品使用
console.log('\n\n=== 测试4: 消耗品使用 ===')

console.log(`${medic.name} 使用前状态:`)
console.log(`  理智值: ${medic.currentSAN}/${medic.sanity}`)

// 使用兴奋剂
console.log(`\n4.1 ${medic.name} 使用兴奋剂:`)
const stimulantUse = CombatItems.useConsumableItem(medic, 'stimulant')
console.log(`  ${stimulantUse.description}`)
if (stimulantUse.result.success) {
  console.log(`  效果: ${stimulantUse.result.effects.join(', ')}`)
  console.log(`  持续时间: ${stimulantUse.result.duration}轮`)
  console.log(`  临时加值: ${JSON.stringify(medic.tempBonuses || {})}`)
}

// 使用烈酒
console.log(`\n4.2 ${medic.name} 使用烈酒:`)
const alcoholUse = CombatItems.useConsumableItem(medic, 'alcohol')
console.log(`  ${alcoholUse.description}`)
if (alcoholUse.result.success) {
  console.log(`  效果: ${alcoholUse.result.effects.join(', ')}`)
  console.log(`  使用后理智值: ${medic.currentSAN}/${medic.sanity}`)
  console.log(`  临时惩罚: ${JSON.stringify(medic.tempPenalties || {})}`)
}

// 测试5: 道具库存管理
console.log('\n\n=== 测试5: 道具库存管理 ===')

console.log(`${medic.name} 的可用道具:`)
const availableItems = CombatItems.getAvailableItems(medic)
availableItems.forEach(item => {
  console.log(`  ${item.name}: 数量${item.quantity}, 使用次数${item.uses === -1 ? '无限' : item.uses}`)
})

// 测试特定分类的道具
console.log(`\n治疗道具:`)
const healingItems = CombatItems.getAvailableItems(medic, 'healing')
healingItems.forEach(item => {
  console.log(`  ${item.name}: ${item.description}`)
})

// 测试6: 道具使用条件检查
console.log('\n\n=== 测试6: 道具使用条件检查 ===')

const itemsToCheck = ['first_aid_kit', 'medical_bag', 'lockpicks', 'nonexistent_item']

itemsToCheck.forEach(itemId => {
  const condition = CombatItems.checkItemUsageConditions(medic, itemId, { inCombat: true })
  console.log(`${itemId}:`)
  console.log(`  可使用: ${condition.canUse}`)
  if (condition.reason) console.log(`  原因: ${condition.reason}`)
  if (condition.warning) console.log(`  警告: ${condition.warning}`)
  if (condition.timeRequired) console.log(`  需要时间: ${condition.timeRequired}回合`)
})

// 测试7: 道具耗尽情况
console.log('\n\n=== 测试7: 道具耗尽测试 ===')

// 连续使用绷带直到用完
console.log(`${medic.name} 连续使用绷带:`)
let bandageCount = 0
while (medic.inventory.bandage && medic.inventory.bandage.quantity > 0) {
  bandageCount++
  const bandageUse = CombatItems.useHealingItem(medic, medic, 'bandage')
  console.log(`  第${bandageCount}次: ${bandageUse.description}`)
  
  if (bandageUse.success === false && bandageUse.reason === '没有该道具') {
    console.log(`  绷带已全部用完`)
    break
  }
}

// 检查库存变化
console.log(`\n使用后的绷带库存: ${medic.inventory.bandage?.quantity || 0}`)

// 测试8: 道具效果持续时间
console.log('\n\n=== 测试8: 道具效果持续时间 ===')

console.log(`当前临时效果:`)
console.log(`  临时加值: ${JSON.stringify(medic.tempBonuses || {})}`)
console.log(`  临时惩罚: ${JSON.stringify(medic.tempPenalties || {})}`)
console.log(`  状态效果: ${medic.conditions.join(', ') || '无'}`)

// 测试9: 错误处理
console.log('\n\n=== 测试9: 错误处理 ===')

// 尝试使用不存在的道具
const invalidUse = CombatItems.useHealingItem(medic, medic, 'nonexistent_item')
console.log(`使用不存在道具: ${invalidUse.success ? '成功' : '失败'} - ${invalidUse.reason || invalidUse.description}`)

// 尝试使用已用完的道具
const emptyUse = CombatItems.useHealingItem(medic, medic, 'morphine')
console.log(`使用已用完道具: ${emptyUse.success ? '成功' : '失败'} - ${emptyUse.reason || emptyUse.description}`)

// 测试10: 道具数据库完整性
console.log('\n\n=== 测试10: 道具数据库完整性 ===')

let totalItems = 0
Object.entries(CombatItems.itemDatabase).forEach(([category, items]) => {
  const itemCount = Object.keys(items).length
  totalItems += itemCount
  console.log(`${category}: ${itemCount} 种道具`)
})

console.log(`总计: ${totalItems} 种道具`)

// 验证每个道具都有必要的属性
console.log(`\n道具数据完整性检查:`)
let validItems = 0
let invalidItems = 0

Object.entries(CombatItems.itemDatabase).forEach(([category, items]) => {
  Object.entries(items).forEach(([itemId, item]) => {
    const hasRequiredFields = item.id && item.name && item.type && item.description
    if (hasRequiredFields) {
      validItems++
    } else {
      invalidItems++
      console.log(`  ❌ ${itemId}: 缺少必要字段`)
    }
  })
})

console.log(`有效道具: ${validItems}`)
console.log(`无效道具: ${invalidItems}`)

console.log('\n=== 战斗道具系统测试完成 ===')
console.log('所有道具功能已验证，与背包系统对接完成！')