/**
 * 骰子系统 - 支持COC 7版战斗系统
 * 严格按照《克苏鲁的呼唤》第七版规则书实现
 */

export class DiceRoller {
  /**
   * 投掷骰子
   * @param {string} formula 骰子公式，如 "1d100", "2d6+3", "1d4"
   * @returns {Object} 投掷结果
   */
  static roll(formula) {
    if (!formula || typeof formula !== 'string') {
      throw new Error('Invalid dice formula')
    }

    // 解析骰子公式
    const parsed = this.parseFormula(formula.toLowerCase().trim())
    if (!parsed) {
      throw new Error(`Cannot parse dice formula: ${formula}`)
    }

    const { count, sides, modifier } = parsed
    const results = []
    
    // 投掷每个骰子
    for (let i = 0; i < count; i++) {
      const roll = Math.floor(Math.random() * sides) + 1
      results.push(roll)
    }
    
    // 计算总和
    const diceTotal = results.reduce((sum, roll) => sum + roll, 0)
    const total = diceTotal + modifier
    
    return {
      formula,
      count,
      sides,
      modifier,
      results,
      diceTotal,
      total,
      timestamp: Date.now()
    }
  }

  /**
   * 投掷1d100 (COC标准检定骰)
   * @returns {number} 1-100的随机数
   */
  static rollD100() {
    return Math.floor(Math.random() * 100) + 1
  }

  /**
   * 投掷1d10 (十面骰)
   * @returns {number} 1-10的随机数
   */
  static rollD10() {
    return Math.floor(Math.random() * 10) + 1
  }

  /**
   * 投掷1d6 (六面骰)
   * @returns {number} 1-6的随机数
   */
  static rollD6() {
    return Math.floor(Math.random() * 6) + 1
  }

  /**
   * 投掷1d4 (四面骰)
   * @returns {number} 1-4的随机数
   */
  static rollD4() {
    return Math.floor(Math.random() * 4) + 1
  }

  /**
   * 投掷1d3 (三面骰，实际是1d6/2向上取整)
   * @returns {number} 1-3的随机数
   */
  static rollD3() {
    return Math.ceil(Math.random() * 3)
  }

  /**
   * 投掷奖励骰/惩罚骰 (COC 7版特殊机制)
   * @param {number} bonusDice 奖励骰数量 (正数为奖励，负数为惩罚)
   * @returns {Object} 包含所有十位数骰子结果的对象
   */
  static rollBonusPenaltyDice(bonusDice) {
    const tensRolls = []
    
    // 基础十位数骰子 (0-9, 0表示00)
    const baseTens = Math.floor(Math.random() * 10)
    tensRolls.push(baseTens)
    
    // 额外的十位数骰子
    for (let i = 0; i < Math.abs(bonusDice); i++) {
      const extraTens = Math.floor(Math.random() * 10)
      tensRolls.push(extraTens)
    }
    
    // 个位数骰子 (1-10, 10表示0)
    let ones = Math.floor(Math.random() * 10) + 1
    if (ones === 10) ones = 0
    
    // 选择最终的十位数
    let finalTens
    if (bonusDice > 0) {
      // 奖励骰：选择最小的十位数
      finalTens = Math.min(...tensRolls)
    } else if (bonusDice < 0) {
      // 惩罚骰：选择最大的十位数
      finalTens = Math.max(...tensRolls)
    } else {
      // 无奖励惩罚骰
      finalTens = baseTens
    }
    
    // 计算最终结果
    const finalResult = finalTens * 10 + ones
    const displayResult = finalResult === 0 ? 100 : finalResult
    
    return {
      tensRolls,
      ones,
      finalTens,
      finalResult: displayResult,
      bonusDice,
      description: this.getBonusPenaltyDescription(bonusDice, tensRolls, ones, displayResult)
    }
  }

  /**
   * 解析骰子公式
   * @param {string} formula 骰子公式
   * @returns {Object|null} 解析结果
   */
  static parseFormula(formula) {
    // 移除所有空格
    formula = formula.replace(/\s/g, '')
    
    // 匹配各种骰子公式格式
    const patterns = [
      // 完整格式: 2d6+3, 1d100-2
      /^(\d+)d(\d+)([+-]\d+)?$/,
      // 简化格式: d20, d6+1
      /^d(\d+)([+-]\d+)?$/,
      // 只有修正值: +3, -2
      /^([+-]\d+)$/
    ]
    
    for (const pattern of patterns) {
      const match = formula.match(pattern)
      if (match) {
        if (pattern === patterns[0]) {
          // 完整格式
          return {
            count: parseInt(match[1]),
            sides: parseInt(match[2]),
            modifier: parseInt(match[3]) || 0
          }
        } else if (pattern === patterns[1]) {
          // 简化格式 (默认1个骰子)
          return {
            count: 1,
            sides: parseInt(match[1]),
            modifier: parseInt(match[2]) || 0
          }
        } else if (pattern === patterns[2]) {
          // 只有修正值 (0个骰子)
          return {
            count: 0,
            sides: 0,
            modifier: parseInt(match[1])
          }
        }
      }
    }
    
    return null
  }

  /**
   * 获取奖励/惩罚骰描述
   */
  static getBonusPenaltyDescription(bonusDice, tensRolls, ones, finalResult) {
    if (bonusDice === 0) {
      return `投掷结果: ${finalResult}`
    }
    
    const tensDisplay = tensRolls.map(t => t === 0 ? '00' : t + '0').join(', ')
    const onesDisplay = ones === 0 ? '0' : ones.toString()
    
    if (bonusDice > 0) {
      return `奖励骰 (${Math.abs(bonusDice)}个): 十位数[${tensDisplay}] + 个位数[${onesDisplay}] = ${finalResult} (选择最小十位数)`
    } else {
      return `惩罚骰 (${Math.abs(bonusDice)}个): 十位数[${tensDisplay}] + 个位数[${onesDisplay}] = ${finalResult} (选择最大十位数)`
    }
  }

  /**
   * 批量投掷相同骰子
   * @param {number} count 骰子数量
   * @param {number} sides 骰子面数
   * @returns {Object} 批量投掷结果
   */
  static rollMultiple(count, sides) {
    const results = []
    for (let i = 0; i < count; i++) {
      results.push(Math.floor(Math.random() * sides) + 1)
    }
    
    return {
      count,
      sides,
      results,
      total: results.reduce((sum, roll) => sum + roll, 0),
      average: results.reduce((sum, roll) => sum + roll, 0) / count,
      min: Math.min(...results),
      max: Math.max(...results),
      timestamp: Date.now()
    }
  }

  /**
   * 投掷伤害骰 (支持贯穿武器的额外伤害)
   * @param {string} damageFormula 伤害公式
   * @param {boolean} isImpaling 是否为贯穿武器
   * @param {boolean} isMaxDamage 是否为最大伤害 (极难成功)
   * @returns {Object} 伤害投掷结果
   */
  static rollDamage(damageFormula, isImpaling = false, isMaxDamage = false) {
    const baseRoll = this.roll(damageFormula)
    let totalDamage = baseRoll.total
    let extraDamage = 0
    let description = `基础伤害: ${baseRoll.total}`
    
    if (isMaxDamage) {
      // 极难成功：使用最大伤害
      const maxDamage = this.getMaxDamage(damageFormula)
      totalDamage = maxDamage
      description = `最大伤害: ${maxDamage}`
      
      if (isImpaling) {
        // 贯穿武器：额外投掷一次伤害骰
        const extraRoll = this.roll(damageFormula)
        extraDamage = extraRoll.total
        totalDamage += extraDamage
        description += ` + 贯穿额外伤害: ${extraDamage} = ${totalDamage}`
      }
    }
    
    return {
      baseRoll,
      extraDamage,
      totalDamage,
      isMaxDamage,
      isImpaling,
      description,
      timestamp: Date.now()
    }
  }

  /**
   * 计算骰子公式的最大可能值
   * @param {string} formula 骰子公式
   * @returns {number} 最大值
   */
  static getMaxDamage(formula) {
    const parsed = this.parseFormula(formula)
    if (!parsed) return 0
    
    const { count, sides, modifier } = parsed
    return (count * sides) + modifier
  }

  /**
   * 计算骰子公式的平均值
   * @param {string} formula 骰子公式
   * @returns {number} 平均值
   */
  static getAverageDamage(formula) {
    const parsed = this.parseFormula(formula)
    if (!parsed) return 0
    
    const { count, sides, modifier } = parsed
    return (count * (sides + 1) / 2) + modifier
  }

  /**
   * 验证骰子公式是否有效
   * @param {string} formula 骰子公式
   * @returns {boolean} 是否有效
   */
  static isValidFormula(formula) {
    return this.parseFormula(formula) !== null
  }

  /**
   * 生成随机种子 (用于可重现的随机序列)
   * @param {number} seed 种子值
   */
  static setSeed(seed) {
    // 简单的线性同余生成器
    this._seed = seed
    this._random = () => {
      this._seed = (this._seed * 9301 + 49297) % 233280
      return this._seed / 233280
    }
  }

  /**
   * 重置为系统随机数生成器
   */
  static resetSeed() {
    this._seed = null
    this._random = null
  }

  /**
   * 获取随机数 (支持种子)
   * @returns {number} 0-1之间的随机数
   */
  static getRandom() {
    return this._random ? this._random() : Math.random()
  }
}

// 创建默认实例
export const diceRoller = DiceRoller

export default DiceRoller