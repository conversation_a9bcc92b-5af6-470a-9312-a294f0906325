<template>
  <!-- 玩家战斗响应界面 -->
  <div class="player-combat-interface" v-if="isPlayerTurn && combatActive">
    <!-- 界面头部 -->
    <div class="interface-header">
      <div class="turn-indicator">
        <i class="fas fa-user-clock"></i>
        <h3>轮到你行动</h3>
        <div class="timer" v-if="turnTimer > 0">
          <i class="fas fa-hourglass-half"></i>
          <span>{{ turnTimer }}秒</span>
        </div>
      </div>
      
      <div class="character-status">
        <div class="character-avatar">
          <img :src="character.avatar || '/default-avatar.png'" :alt="character.name">
          <div class="health-indicator" :class="healthStatus">
            <div class="health-bar" :style="{ width: healthPercentage + '%' }"></div>
          </div>
        </div>
        <div class="character-info">
          <h4>{{ character.name }}</h4>
          <div class="status-bars">
            <div class="stat-bar">
              <label>生命:</label>
              <span class="stat-value">{{ character.currentHP || 0 }}/{{ character.hitPoints || character.hp }}</span>
            </div>
            <div class="stat-bar">
              <label>理智:</label>
              <span class="stat-value">{{ character.currentSAN || 0 }}/{{ character.sanity }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 行动选择面板 -->
    <div class="action-selection-panel">
      <div class="action-categories">
        <!-- 攻击行动 -->
        <div class="action-category" :class="{ active: selectedCategory === 'attack' }">
          <button 
            @click="selectCategory('attack')" 
            class="category-btn"
            :disabled="!canAttack"
          >
            <i class="fas fa-sword"></i>
            <span>攻击</span>
          </button>
          
          <div class="action-options" v-if="selectedCategory === 'attack'">
            <!-- 近战攻击 -->
            <div class="action-group" v-if="hasMeleeWeapon">
              <h5>近战攻击</h5>
              <div class="weapon-options">
                <button 
                  v-for="weapon in meleeWeapons" 
                  :key="weapon.id"
                  @click="selectAction('melee_attack', weapon)"
                  class="weapon-btn"
                  :class="{ selected: selectedWeapon?.id === weapon.id }"
                >
                  <div class="weapon-info">
                    <span class="weapon-name">{{ weapon.name }}</span>
                    <span class="weapon-damage">{{ weapon.damage }}</span>
                    <span class="weapon-skill">{{ getSkillName(weapon.skill) }}</span>
                  </div>
                </button>
              </div>
            </div>

            <!-- 远程攻击 -->
            <div class="action-group" v-if="hasRangedWeapon">
              <h5>远程攻击</h5>
              <div class="weapon-options">
                <button 
                  v-for="weapon in rangedWeapons" 
                  :key="weapon.id"
                  @click="selectAction('ranged_attack', weapon)"
                  class="weapon-btn"
                  :class="{ selected: selectedWeapon?.id === weapon.id }"
                >
                  <div class="weapon-info">
                    <span class="weapon-name">{{ weapon.name }}</span>
                    <span class="weapon-damage">{{ weapon.damage }}</span>
                    <span class="weapon-ammo">{{ weapon.currentAmmo || 0 }}/{{ weapon.ammo || 0 }}</span>
                  </div>
                </button>
              </div>
            </div>

            <!-- 战技 -->
            <div class="action-group">
              <h5>战技</h5>
              <div class="maneuver-options">
                <button 
                  @click="selectAction('disarm')"
                  class="maneuver-btn"
                  :disabled="!canUseManeuver('disarm')"
                >
                  <i class="fas fa-hand-paper"></i>
                  <span>缴械</span>
                </button>
                <button 
                  @click="selectAction('knockdown')"
                  class="maneuver-btn"
                  :disabled="!canUseManeuver('knockdown')"
                >
                  <i class="fas fa-running"></i>
                  <span>撞倒</span>
                </button>
                <button 
                  @click="selectAction('grapple')"
                  class="maneuver-btn"
                  :disabled="!canUseManeuver('grapple')"
                >
                  <i class="fas fa-hand-rock"></i>
                  <span>擒抱</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 防御行动 -->
        <div class="action-category" :class="{ active: selectedCategory === 'defense' }">
          <button 
            @click="selectCategory('defense')" 
            class="category-btn"
          >
            <i class="fas fa-shield-alt"></i>
            <span>防御</span>
          </button>
          
          <div class="action-options" v-if="selectedCategory === 'defense'">
            <div class="action-group">
              <button 
                @click="selectAction('dodge')"
                class="defense-btn"
              >
                <i class="fas fa-running"></i>
                <div class="action-info">
                  <span class="action-name">闪避</span>
                  <span class="skill-value">{{ character.dodge || 0 }}%</span>
                </div>
              </button>
              
              <button 
                @click="selectAction('block')"
                class="defense-btn"
                :disabled="!canBlock"
              >
                <i class="fas fa-shield"></i>
                <div class="action-info">
                  <span class="action-name">格挡</span>
                  <span class="skill-value">{{ getBlockSkill() }}%</span>
                </div>
              </button>
              
              <button 
                @click="selectAction('parry')"
                class="defense-btn"
                :disabled="!canParry"
              >
                <i class="fas fa-sword"></i>
                <div class="action-info">
                  <span class="action-name">招架</span>
                  <span class="skill-value">{{ getParrySkill() }}%</span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- 移动行动 -->
        <div class="action-category" :class="{ active: selectedCategory === 'movement' }">
          <button 
            @click="selectCategory('movement')" 
            class="category-btn"
          >
            <i class="fas fa-walking"></i>
            <span>移动</span>
          </button>
          
          <div class="action-options" v-if="selectedCategory === 'movement'">
            <div class="action-group">
              <button 
                @click="selectAction('move')"
                class="movement-btn"
              >
                <i class="fas fa-arrows-alt"></i>
                <div class="action-info">
                  <span class="action-name">移动</span>
                  <span class="move-distance">{{ character.movementRate || 8 }}米</span>
                </div>
              </button>
              
              <button 
                @click="selectAction('run')"
                class="movement-btn"
              >
                <i class="fas fa-running"></i>
                <div class="action-info">
                  <span class="action-name">奔跑</span>
                  <span class="move-distance">{{ (character.movementRate || 8) * 5 }}米</span>
                </div>
              </button>
              
              <button 
                @click="selectAction('retreat')"
                class="movement-btn"
              >
                <i class="fas fa-backward"></i>
                <div class="action-info">
                  <span class="action-name">脱离战斗</span>
                  <span class="skill-value">需要检定</span>
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- 道具使用 -->
        <div class="action-category" :class="{ active: selectedCategory === 'items' }">
          <button 
            @click="selectCategory('items')" 
            class="category-btn"
            :disabled="!hasUsableItems"
          >
            <i class="fas fa-briefcase-medical"></i>
            <span>道具</span>
          </button>
          
          <div class="action-options" v-if="selectedCategory === 'items'">
            <div class="action-group">
              <h5>治疗道具</h5>
              <div class="item-options">
                <button 
                  v-for="item in healingItems" 
                  :key="item.id"
                  @click="selectAction('use_item', item)"
                  class="item-btn"
                >
                  <div class="item-info">
                    <span class="item-name">{{ item.name }}</span>
                    <span class="item-uses">{{ item.uses }}/{{ item.maxUses }}</span>
                  </div>
                </button>
              </div>
            </div>
            
            <div class="action-group" v-if="consumableItems.length > 0">
              <h5>消耗品</h5>
              <div class="item-options">
                <button 
                  v-for="item in consumableItems" 
                  :key="item.id"
                  @click="selectAction('use_item', item)"
                  class="item-btn"
                >
                  <div class="item-info">
                    <span class="item-name">{{ item.name }}</span>
                    <span class="item-uses">{{ item.uses }}</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他行动 -->
        <div class="action-category" :class="{ active: selectedCategory === 'other' }">
          <button 
            @click="selectCategory('other')" 
            class="category-btn"
          >
            <i class="fas fa-ellipsis-h"></i>
            <span>其他</span>
          </button>
          
          <div class="action-options" v-if="selectedCategory === 'other'">
            <div class="action-group">
              <button 
                @click="selectAction('aim')"
                class="other-btn"
                :disabled="!hasRangedWeapon"
              >
                <i class="fas fa-crosshairs"></i>
                <span>瞄准</span>
              </button>
              
              <button 
                @click="selectAction('reload')"
                class="other-btn"
                :disabled="!needsReload"
              >
                <i class="fas fa-redo"></i>
                <span>装填</span>
              </button>
              
              <button 
                @click="selectAction('wait')"
                class="other-btn"
              >
                <i class="fas fa-pause"></i>
                <span>等待</span>
              </button>
              
              <button 
                @click="selectAction('delay')"
                class="other-btn"
              >
                <i class="fas fa-clock"></i>
                <span>延迟行动</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 目标选择提示 -->
    <div class="target-selection" v-if="selectedAction && needsTarget">
      <div class="selection-prompt">
        <i class="fas fa-crosshairs"></i>
        <span>请选择目标</span>
        <button @click="cancelAction" class="btn-cancel">
          <i class="fas fa-times"></i>
          取消
        </button>
      </div>
      
      <div class="available-targets">
        <button 
          v-for="target in availableTargets" 
          :key="target.id"
          @click="selectTarget(target)"
          class="target-btn"
          :class="{ selected: selectedTarget?.id === target.id }"
        >
          <div class="target-info">
            <img :src="target.avatar || '/default-avatar.png'" :alt="target.name">
            <span class="target-name">{{ target.name }}</span>
            <span class="target-distance">{{ getDistanceToTarget(target) }}米</span>
          </div>
        </button>
      </div>
    </div>

    <!-- 行动确认 -->
    <div class="action-confirmation" v-if="selectedAction && (!needsTarget || selectedTarget)">
      <div class="confirmation-panel">
        <div class="action-summary">
          <h4>确认行动</h4>
          <div class="summary-details">
            <div class="action-type">
              <span class="label">行动:</span>
              <span class="value">{{ getActionName(selectedAction) }}</span>
            </div>
            <div class="action-weapon" v-if="selectedWeapon">
              <span class="label">武器:</span>
              <span class="value">{{ selectedWeapon.name }}</span>
            </div>
            <div class="action-target" v-if="selectedTarget">
              <span class="label">目标:</span>
              <span class="value">{{ selectedTarget.name }}</span>
            </div>
            <div class="action-skill" v-if="requiredSkill">
              <span class="label">技能:</span>
              <span class="value">{{ getSkillName(requiredSkill) }} ({{ getSkillValue(requiredSkill) }}%)</span>
            </div>
          </div>
        </div>
        
        <div class="confirmation-actions">
          <button 
            @click="confirmAction" 
            class="btn-confirm"
            :disabled="isProcessing"
          >
            <i class="fas fa-check"></i>
            {{ isProcessing ? '处理中...' : '确认' }}
          </button>
          <button 
            @click="cancelAction" 
            class="btn-cancel"
            :disabled="isProcessing"
          >
            <i class="fas fa-times"></i>
            取消
          </button>
        </div>
      </div>
    </div>

    <!-- 等待其他玩家提示 -->
    <div class="waiting-overlay" v-if="isWaitingForOthers">
      <div class="waiting-content">
        <div class="spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <h3>等待其他玩家行动</h3>
        <p>{{ waitingMessage }}</p>
        
        <div class="other-players-status">
          <div 
            v-for="player in otherPlayers" 
            :key="player.id"
            class="player-status"
            :class="{ completed: player.actionCompleted }"
          >
            <img :src="player.avatar || '/default-avatar.png'" :alt="player.name">
            <span class="player-name">{{ player.name }}</span>
            <i class="fas fa-check" v-if="player.actionCompleted"></i>
            <i class="fas fa-clock" v-else></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CombatItems from '@/utils/combatItems.js'
import WeaponSystem from '@/utils/weaponSystem.js'

export default {
  name: 'PlayerCombatInterface',
  props: {
    character: {
      type: Object,
      required: true
    },
    combatActive: {
      type: Boolean,
      default: false
    },
    isPlayerTurn: {
      type: Boolean,
      default: false
    },
    availableTargets: {
      type: Array,
      default: () => []
    },
    otherPlayers: {
      type: Array,
      default: () => []
    },
    turnTimer: {
      type: Number,
      default: 0
    }
  },
  
  data() {
    return {
      selectedCategory: null,
      selectedAction: null,
      selectedWeapon: null,
      selectedTarget: null,
      selectedItem: null,
      isProcessing: false,
      isWaitingForOthers: false,
      waitingMessage: ''
    }
  },
  
  computed: {
    healthPercentage() {
      const current = this.character.currentHP || 0
      const max = this.character.hitPoints || this.character.hp || 1
      return Math.max(0, (current / max) * 100)
    },
    
    healthStatus() {
      const percentage = this.healthPercentage
      if (percentage > 75) return 'healthy'
      if (percentage > 50) return 'injured'
      if (percentage > 25) return 'wounded'
      return 'critical'
    },
    
    meleeWeapons() {
      return this.getWeaponsByType('melee')
    },
    
    rangedWeapons() {
      return this.getWeaponsByType('ranged')
    },
    
    hasMeleeWeapon() {
      return this.meleeWeapons.length > 0
    },
    
    hasRangedWeapon() {
      return this.rangedWeapons.length > 0
    },
    
    healingItems() {
      return CombatItems.getAvailableItems(this.character, 'healing')
    },
    
    consumableItems() {
      return CombatItems.getAvailableItems(this.character, 'consumables')
    },
    
    hasUsableItems() {
      return this.healingItems.length > 0 || this.consumableItems.length > 0
    },
    
    canAttack() {
      return this.hasMeleeWeapon || this.hasRangedWeapon
    },
    
    canBlock() {
      return this.meleeWeapons.some(w => w.canBlock)
    },
    
    canParry() {
      return this.meleeWeapons.some(w => w.canParry)
    },
    
    needsTarget() {
      return ['melee_attack', 'ranged_attack', 'disarm', 'knockdown', 'grapple'].includes(this.selectedAction)
    },
    
    needsReload() {
      return this.rangedWeapons.some(w => (w.currentAmmo || 0) < (w.ammo || 0))
    },
    
    requiredSkill() {
      if (this.selectedWeapon) {
        return this.selectedWeapon.skill
      }
      if (this.selectedAction === 'dodge') {
        return 'dodge'
      }
      return null
    }
  },
  
  methods: {
    selectCategory(category) {
      this.selectedCategory = this.selectedCategory === category ? null : category
      this.resetSelection()
    },
    
    selectAction(action, data = null) {
      this.selectedAction = action
      if (data) {
        if (data.type === 'melee' || data.type === 'ranged') {
          this.selectedWeapon = data
        } else {
          this.selectedItem = data
        }
      }
    },
    
    selectTarget(target) {
      this.selectedTarget = target
    },
    
    cancelAction() {
      this.resetSelection()
    },
    
    resetSelection() {
      this.selectedAction = null
      this.selectedWeapon = null
      this.selectedTarget = null
      this.selectedItem = null
    },
    
    async confirmAction() {
      if (!this.selectedAction) return
      
      this.isProcessing = true
      
      try {
        const actionData = {
          type: this.selectedAction,
          character: this.character,
          weapon: this.selectedWeapon,
          target: this.selectedTarget,
          item: this.selectedItem
        }
        
        // 发送行动到服务器
        await this.$emit('action-selected', actionData)
        
        // 重置选择
        this.resetSelection()
        
        // 显示等待状态
        this.isWaitingForOthers = true
        this.waitingMessage = '等待KP处理行动结果...'
        
      } catch (error) {
        console.error('提交行动失败:', error)
        this.$emit('error', '提交行动失败，请重试')
      } finally {
        this.isProcessing = false
      }
    },
    
    getWeaponsByType(type) {
      if (!this.character.weapons) return []
      return this.character.weapons.filter(w => w.type === type)
    },
    
    getSkillName(skillKey) {
      const skillNames = {
        'fighting_brawl': '格斗(斗殴)',
        'firearms_handgun': '射击(手枪)',
        'firearms_rifle': '射击(步枪)',
        'firearms_shotgun': '射击(霰弹枪)',
        'sword': '剑术',
        'dodge': '闪避'
      }
      return skillNames[skillKey] || skillKey
    },
    
    getSkillValue(skillKey) {
      return this.character[skillKey] || this.character.skills?.[skillKey] || 0
    },
    
    getBlockSkill() {
      const weapon = this.meleeWeapons.find(w => w.canBlock)
      return weapon ? this.getSkillValue(weapon.skill) : 0
    },
    
    getParrySkill() {
      const weapon = this.meleeWeapons.find(w => w.canParry)
      return weapon ? this.getSkillValue(weapon.skill) : 0
    },
    
    canUseManeuver(maneuver) {
      // 检查是否可以使用特定战技
      return true // 简化实现
    },
    
    getActionName(action) {
      const actionNames = {
        'melee_attack': '近战攻击',
        'ranged_attack': '远程攻击',
        'dodge': '闪避',
        'block': '格挡',
        'parry': '招架',
        'move': '移动',
        'run': '奔跑',
        'retreat': '脱离战斗',
        'disarm': '缴械',
        'knockdown': '撞倒',
        'grapple': '擒抱',
        'use_item': '使用道具',
        'aim': '瞄准',
        'reload': '装填',
        'wait': '等待',
        'delay': '延迟行动'
      }
      return actionNames[action] || action
    },
    
    getDistanceToTarget(target) {
      // 计算到目标的距离
      return Math.floor(Math.random() * 20) + 1 // 简化实现
    }
  },
  
  watch: {
    isPlayerTurn(newVal) {
      if (newVal) {
        this.isWaitingForOthers = false
        this.resetSelection()
      }
    }
  }
}
</script>

<style scoped>
.player-combat-interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  color: white;
}

.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  border-bottom: 2px solid #e74c3c;
}

.turn-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.turn-indicator h3 {
  margin: 0;
  color: #e74c3c;
  font-size: 1.5rem;
}

.timer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(231, 76, 60, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid #e74c3c;
}

.character-status {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.character-avatar {
  position: relative;
  width: 60px;
  height: 60px;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #3498db;
}

.health-indicator {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 8px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.health-bar {
  height: 100%;
  transition: width 0.3s ease;
}

.health-indicator.healthy .health-bar { background: #27ae60; }
.health-indicator.injured .health-bar { background: #f39c12; }
.health-indicator.wounded .health-bar { background: #e67e22; }
.health-indicator.critical .health-bar { background: #e74c3c; }

.character-info h4 {
  margin: 0 0 0.5rem 0;
  color: #ecf0f1;
}

.status-bars {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-bar {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.stat-bar .label {
  color: #bdc3c7;
}

.stat-bar .value {
  color: #ecf0f1;
  font-weight: bold;
}

.action-selection-panel {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.action-categories {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.action-category {
  background: rgba(52, 73, 94, 0.8);
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.action-category.active {
  border-color: #3498db;
  background: rgba(52, 73, 94, 1);
}

.category-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: none;
  border: none;
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: background 0.3s ease;
}

.category-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
}

.category-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-options {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-group {
  margin-bottom: 1.5rem;
}

.action-group h5 {
  margin: 0 0 1rem 0;
  color: #3498db;
  font-size: 1rem;
}

.weapon-options,
.maneuver-options,
.item-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.weapon-btn,
.defense-btn,
.movement-btn,
.maneuver-btn,
.item-btn,
.other-btn {
  padding: 1rem;
  background: rgba(44, 62, 80, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.weapon-btn:hover,
.defense-btn:hover,
.movement-btn:hover,
.maneuver-btn:hover,
.item-btn:hover,
.other-btn:hover {
  border-color: #3498db;
  background: rgba(52, 152, 219, 0.2);
}

.weapon-btn.selected {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.2);
}

.weapon-info,
.action-info,
.item-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.weapon-name,
.action-name,
.item-name {
  font-weight: bold;
  font-size: 1rem;
}

.weapon-damage,
.weapon-skill,
.weapon-ammo,
.skill-value,
.move-distance,
.item-uses {
  font-size: 0.9rem;
  color: #bdc3c7;
}

.target-selection {
  padding: 1rem 2rem;
  background: rgba(44, 62, 80, 0.9);
  border-top: 2px solid #e74c3c;
}

.selection-prompt {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.selection-prompt span {
  font-size: 1.1rem;
  font-weight: bold;
  color: #e74c3c;
}

.available-targets {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.target-btn {
  padding: 1rem;
  background: rgba(52, 73, 94, 0.8);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.target-btn:hover {
  border-color: #3498db;
}

.target-btn.selected {
  border-color: #e74c3c;
  background: rgba(231, 76, 60, 0.2);
}

.target-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.target-info img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.action-confirmation {
  padding: 1rem 2rem;
  background: rgba(39, 174, 96, 0.1);
  border-top: 2px solid #27ae60;
}

.confirmation-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.action-summary h4 {
  margin: 0 0 1rem 0;
  color: #27ae60;
}

.summary-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.summary-details > div {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-details .label {
  font-size: 0.9rem;
  color: #bdc3c7;
}

.summary-details .value {
  font-weight: bold;
  color: #ecf0f1;
}

.confirmation-actions {
  display: flex;
  gap: 1rem;
}

.btn-confirm,
.btn-cancel {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-confirm {
  background: #27ae60;
  color: white;
}

.btn-confirm:hover:not(:disabled) {
  background: #2ecc71;
}

.btn-cancel {
  background: #e74c3c;
  color: white;
}

.btn-cancel:hover:not(:disabled) {
  background: #c0392b;
}

.btn-confirm:disabled,
.btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.waiting-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.waiting-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.spinner {
  font-size: 3rem;
  color: #3498db;
  margin-bottom: 1rem;
}

.waiting-content h3 {
  margin: 0 0 1rem 0;
  color: #ecf0f1;
}

.waiting-content p {
  color: #bdc3c7;
  margin-bottom: 2rem;
}

.other-players-status {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.player-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(52, 73, 94, 0.8);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.player-status.completed {
  border-color: #27ae60;
  background: rgba(39, 174, 96, 0.2);
}

.player-status img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.player-name {
  font-size: 0.9rem;
  color: #ecf0f1;
}

.player-status i {
  font-size: 0.8rem;
}

.player-status.completed i {
  color: #27ae60;
}

.player-status:not(.completed) i {
  color: #f39c12;
}
</style>