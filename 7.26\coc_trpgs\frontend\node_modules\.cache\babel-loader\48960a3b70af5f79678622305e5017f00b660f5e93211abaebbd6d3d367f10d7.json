{"ast": null, "code": "import \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/web.timers.js\";\nexport default {\n  name: 'MonsterToken',\n  props: {\n    monster: {\n      type: Object,\n      required: true\n    },\n    gridSize: {\n      type: Number,\n      \"default\": 40\n    },\n    zoom: {\n      type: Number,\n      \"default\": 1\n    },\n    selected: {\n      type: <PERSON><PERSON><PERSON>,\n      \"default\": false\n    },\n    canControl: {\n      type: <PERSON><PERSON>an,\n      \"default\": false\n    },\n    showThreatRange: {\n      type: <PERSON>olean,\n      \"default\": false\n    }\n  },\n  data: function data() {\n    return {\n      tokenRadius: 20,\n      isDragging: false,\n      dragStart: null,\n      damageAnimation: null,\n      attackAnimation: false\n    };\n  },\n  computed: {\n    x: function x() {\n      var _this$monster$positio;\n      return (((_this$monster$positio = this.monster.position) === null || _this$monster$positio === void 0 ? void 0 : _this$monster$positio.x) || 0) * this.gridSize * this.zoom;\n    },\n    y: function y() {\n      var _this$monster$positio2;\n      return (((_this$monster$positio2 = this.monster.position) === null || _this$monster$positio2 === void 0 ? void 0 : _this$monster$positio2.y) || 0) * this.gridSize * this.zoom;\n    },\n    threatRange: function threatRange() {\n      // 根据怪物武器和能力计算威胁范围\n      return this.monster.reach || 1.5;\n    }\n  },\n  methods: {\n    /**\r\n     * 获取怪物颜色\r\n     */\n    getMonsterColor: function getMonsterColor() {\n      var colorMap = {\n        'beast': '#8bc34a',\n        // 野兽 - 绿色\n        'humanoid': '#ff9800',\n        // 人形 - 橙色\n        'undead': '#9c27b0',\n        // 不死 - 紫色\n        'fiend': '#f44336',\n        // 恶魔 - 红色\n        'celestial': '#ffeb3b',\n        // 天界 - 黄色\n        'dragon': '#e91e63',\n        // 龙类 - 粉红\n        'aberration': '#3f51b5',\n        // 异怪 - 蓝色\n        'construct': '#607d8b',\n        // 构装 - 灰色\n        'elemental': '#00bcd4',\n        // 元素 - 青色\n        'fey': '#4caf50',\n        // 精类 - 浅绿\n        'giant': '#795548',\n        // 巨人 - 棕色\n        'monstrosity': '#ff5722',\n        // 怪物 - 深橙\n        'ooze': '#9e9e9e',\n        // 软泥 - 灰色\n        'plant': '#689f38' // 植物 - 深绿\n      };\n      return colorMap[this.monster.type] || '#666666';\n    },\n    /**\r\n     * 获取怪物图标\r\n     */\n    getMonsterIcon: function getMonsterIcon() {\n      var iconMap = {\n        'beast': '🐺',\n        'humanoid': '👤',\n        'undead': '💀',\n        'fiend': '👹',\n        'celestial': '👼',\n        'dragon': '🐉',\n        'aberration': '👁️',\n        'construct': '🤖',\n        'elemental': '🌪️',\n        'fey': '🧚',\n        'giant': '🗿',\n        'monstrosity': '👾',\n        'ooze': '🟢',\n        'plant': '🌿'\n      };\n      return iconMap[this.monster.type] || '❓';\n    },\n    /**\r\n     * 获取图标大小\r\n     */\n    getIconSize: function getIconSize() {\n      if (this.monster.size === 'huge') return 32;\n      if (this.monster.size === 'large') return 24;\n      return 16;\n    },\n    /**\r\n     * 获取名称Y位置\r\n     */\n    getNameYPosition: function getNameYPosition() {\n      if (this.monster.size === 'huge') return this.tokenRadius * 2 + 15;\n      if (this.monster.size === 'large') return this.tokenRadius * 1.5 + 12;\n      return this.tokenRadius + 15;\n    },\n    /**\r\n     * 获取生命值颜色\r\n     */\n    getHealthColor: function getHealthColor() {\n      var healthPercent = this.getHealthPercentage();\n      if (healthPercent > 75) return '#4caf50';\n      if (healthPercent > 50) return '#ff9800';\n      if (healthPercent > 25) return '#f44336';\n      return '#9c27b0';\n    },\n    /**\r\n     * 获取生命值百分比\r\n     */\n    getHealthPercentage: function getHealthPercentage() {\n      var current = this.monster.currentHP || this.monster.hitPoints;\n      var max = this.monster.maxHP || this.monster.hitPoints;\n      return max > 0 ? current / max * 100 : 0;\n    },\n    /**\r\n     * 获取生命值条宽度\r\n     */\n    getHealthBarWidth: function getHealthBarWidth() {\n      var maxWidth = (this.tokenRadius - 4) * 2;\n      var healthPercent = this.getHealthPercentage() / 100;\n      return maxWidth * healthPercent;\n    },\n    /**\r\n     * 获取状态效果图标\r\n     */\n    getConditionIcon: function getConditionIcon(condition) {\n      var iconMap = {\n        'unconscious': '💤',\n        'dying': '💀',\n        'prone': '⬇️',\n        'stunned': '😵',\n        'restrained': '🔒',\n        'frightened': '😨',\n        'poisoned': '☠️',\n        'bleeding': '🩸',\n        'burning': '🔥',\n        'frozen': '❄️',\n        'paralyzed': '⚡',\n        'blinded': '👁️',\n        'deafened': '👂',\n        'charmed': '💖',\n        'confused': '❓',\n        'enraged': '😡',\n        'invisible': '👻'\n      };\n      return iconMap[condition] || '?';\n    },\n    /**\r\n     * 开始拖拽 (仅KP可用)\r\n     */\n    startDrag: function startDrag(event) {\n      if (!this.canControl) return;\n      this.isDragging = true;\n      this.dragStart = {\n        x: event.clientX,\n        y: event.clientY,\n        monsterX: this.monster.position.x,\n        monsterY: this.monster.position.y\n      };\n      document.addEventListener('mousemove', this.handleDrag);\n      document.addEventListener('mouseup', this.endDrag);\n      event.stopPropagation();\n    },\n    /**\r\n     * 处理拖拽\r\n     */\n    handleDrag: function handleDrag(event) {\n      if (!this.isDragging || !this.dragStart) return;\n      var deltaX = event.clientX - this.dragStart.x;\n      var deltaY = event.clientY - this.dragStart.y;\n      var gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom));\n      var gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom));\n      var newX = this.dragStart.monsterX + gridDeltaX;\n      var newY = this.dragStart.monsterY + gridDeltaY;\n      this.$emit('move', this.monster, {\n        x: newX,\n        y: newY\n      });\n    },\n    /**\r\n     * 结束拖拽\r\n     */\n    endDrag: function endDrag() {\n      this.isDragging = false;\n      this.dragStart = null;\n      document.removeEventListener('mousemove', this.handleDrag);\n      document.removeEventListener('mouseup', this.endDrag);\n    },\n    /**\r\n     * 播放伤害动画\r\n     */\n    playDamageAnimation: function playDamageAnimation(damage) {\n      var _this = this;\n      var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';\n      var colors = {\n        normal: '#f44336',\n        critical: '#e91e63',\n        healing: '#4caf50',\n        miss: '#9e9e9e'\n      };\n      var texts = {\n        normal: \"-\".concat(damage),\n        critical: \"CRIT! -\".concat(damage),\n        healing: \"+\".concat(damage),\n        miss: 'MISS'\n      };\n      this.damageAnimation = {\n        text: texts[type] || \"-\".concat(damage),\n        color: colors[type] || '#f44336'\n      };\n      setTimeout(function () {\n        _this.damageAnimation = null;\n      }, 1500);\n    },\n    /**\r\n     * 播放攻击动画\r\n     */\n    playAttackAnimation: function playAttackAnimation() {\n      var _this2 = this;\n      this.attackAnimation = true;\n      setTimeout(function () {\n        _this2.attackAnimation = false;\n      }, 500);\n    },\n    /**\r\n     * 播放死亡动画\r\n     */\n    playDeathAnimation: function playDeathAnimation() {\n      // 添加死亡动画效果\n      this.$el.style.transition = 'all 1s ease-out';\n      this.$el.style.opacity = '0.3';\n      this.$el.style.transform += ' scale(0.8)';\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "monster", "type", "Object", "required", "gridSize", "Number", "zoom", "selected", "Boolean", "canControl", "showThreatRange", "data", "tokenRadius", "isDragging", "dragStart", "damageAnimation", "attackAnimation", "computed", "x", "_this$monster$positio", "position", "y", "_this$monster$positio2", "threatRange", "reach", "methods", "getMonsterColor", "colorMap", "getMonsterIcon", "iconMap", "getIconSize", "size", "getNameYPosition", "getHealthColor", "healthPercent", "getHealthPercentage", "current", "currentHP", "hitPoints", "max", "maxHP", "getHealthBarWidth", "max<PERSON><PERSON><PERSON>", "getConditionIcon", "condition", "startDrag", "event", "clientX", "clientY", "monsterX", "monsterY", "document", "addEventListener", "handleDrag", "endDrag", "stopPropagation", "deltaX", "deltaY", "gridDeltaX", "Math", "round", "gridDeltaY", "newX", "newY", "$emit", "removeEventListener", "playDamageAnimation", "damage", "_this", "arguments", "length", "undefined", "colors", "normal", "critical", "healing", "miss", "texts", "concat", "text", "color", "setTimeout", "playAttackAnimation", "_this2", "playDeathAnimation", "$el", "style", "transition", "opacity", "transform"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\MonsterToken.vue"], "sourcesContent": ["<template>\r\n  <!-- 2D战场上的怪物令牌 -->\r\n  <g \r\n    class=\"monster-token\"\r\n    :class=\"{ \r\n      selected, \r\n      'can-control': canControl,\r\n      'boss-monster': monster.isBoss,\r\n      'minion-monster': monster.isMinion\r\n    }\"\r\n    :transform=\"`translate(${x}, ${y})`\"\r\n    @click=\"$emit('select', monster)\"\r\n    @contextmenu.prevent=\"$emit('context-menu', monster, $event)\"\r\n  >\r\n    <!-- 选中光环 -->\r\n    <circle \r\n      v-if=\"selected\"\r\n      :r=\"tokenRadius + 8\"\r\n      fill=\"none\"\r\n      stroke=\"#ff6b6b\"\r\n      stroke-width=\"3\"\r\n      opacity=\"0.8\"\r\n    >\r\n      <animate \r\n        attributeName=\"stroke-opacity\" \r\n        values=\"0.8;0.3;0.8\" \r\n        dur=\"2s\" \r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- Boss怪物特殊光环 -->\r\n    <circle \r\n      v-if=\"monster.isBoss\"\r\n      :r=\"tokenRadius + 15\"\r\n      fill=\"none\"\r\n      stroke=\"#9c27b0\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"8,4\"\r\n      opacity=\"0.7\"\r\n    >\r\n      <animateTransform\r\n        attributeName=\"transform\"\r\n        type=\"rotate\"\r\n        values=\"0;360\"\r\n        dur=\"4s\"\r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- 威胁范围指示 -->\r\n    <circle \r\n      v-if=\"showThreatRange\"\r\n      :r=\"threatRange * gridSize\"\r\n      fill=\"rgba(244, 67, 54, 0.1)\"\r\n      stroke=\"#f44336\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"3,3\"\r\n      opacity=\"0.4\"\r\n    />\r\n    \r\n    <!-- 怪物主体 -->\r\n    <g class=\"monster-body\">\r\n      <!-- 背景形状 (根据怪物类型变化) -->\r\n      <g v-if=\"monster.size === 'large'\">\r\n        <!-- 大型怪物 - 2x2格子 -->\r\n        <rect \r\n          :x=\"-tokenRadius * 1.5\"\r\n          :y=\"-tokenRadius * 1.5\"\r\n          :width=\"tokenRadius * 3\"\r\n          :height=\"tokenRadius * 3\"\r\n          :fill=\"getMonsterColor()\"\r\n          stroke=\"#fff\"\r\n          stroke-width=\"2\"\r\n          rx=\"4\"\r\n          opacity=\"0.9\"\r\n        />\r\n      </g>\r\n      <g v-else-if=\"monster.size === 'huge'\">\r\n        <!-- 巨型怪物 - 3x3格子 -->\r\n        <rect \r\n          :x=\"-tokenRadius * 2\"\r\n          :y=\"-tokenRadius * 2\"\r\n          :width=\"tokenRadius * 4\"\r\n          :height=\"tokenRadius * 4\"\r\n          :fill=\"getMonsterColor()\"\r\n          stroke=\"#fff\"\r\n          stroke-width=\"3\"\r\n          rx=\"6\"\r\n          opacity=\"0.9\"\r\n        />\r\n      </g>\r\n      <g v-else>\r\n        <!-- 普通怪物 - 圆形 -->\r\n        <circle \r\n          :r=\"tokenRadius\"\r\n          :fill=\"getMonsterColor()\"\r\n          stroke=\"#fff\"\r\n          stroke-width=\"2\"\r\n          opacity=\"0.9\"\r\n        />\r\n      </g>\r\n      \r\n      <!-- 怪物图标 -->\r\n      <text \r\n        text-anchor=\"middle\"\r\n        dy=\"6\"\r\n        :font-size=\"getIconSize()\"\r\n        fill=\"white\"\r\n        class=\"monster-icon\"\r\n      >\r\n        {{ getMonsterIcon() }}\r\n      </text>\r\n      \r\n      <!-- 生命值条 -->\r\n      <rect \r\n        :x=\"-tokenRadius + 4\"\r\n        :y=\"tokenRadius - 8\"\r\n        :width=\"(tokenRadius - 4) * 2\"\r\n        height=\"4\"\r\n        fill=\"#333\"\r\n        opacity=\"0.7\"\r\n        rx=\"2\"\r\n      />\r\n      <rect \r\n        :x=\"-tokenRadius + 4\"\r\n        :y=\"tokenRadius - 8\"\r\n        :width=\"getHealthBarWidth()\"\r\n        height=\"4\"\r\n        :fill=\"getHealthColor()\"\r\n        opacity=\"0.9\"\r\n        rx=\"2\"\r\n      />\r\n    </g>\r\n    \r\n    <!-- 怪物名称和等级 -->\r\n    <text \r\n      :y=\"getNameYPosition()\"\r\n      text-anchor=\"middle\"\r\n      font-size=\"10\"\r\n      font-weight=\"bold\"\r\n      fill=\"#f44336\"\r\n      class=\"monster-name-text\"\r\n    >\r\n      {{ monster.name }}\r\n    </text>\r\n    \r\n    <!-- 挑战等级显示 -->\r\n    <g class=\"challenge-rating\" :transform=\"`translate(${tokenRadius - 12}, ${-tokenRadius + 12})`\">\r\n      <circle r=\"10\" fill=\"#ff5722\" stroke=\"#fff\" stroke-width=\"1\"/>\r\n      <text \r\n        text-anchor=\"middle\"\r\n        dy=\"3\"\r\n        font-size=\"8\"\r\n        fill=\"white\"\r\n        font-weight=\"bold\"\r\n      >\r\n        {{ monster.challengeRating || '?' }}\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 状态效果图标 -->\r\n    <g class=\"status-effects\" :transform=\"`translate(${-tokenRadius}, ${-getNameYPosition() - 10})`\">\r\n      <g \r\n        v-for=\"(condition, index) in monster.conditions\" \r\n        :key=\"condition\"\r\n        :transform=\"`translate(${index * 14}, 0)`\"\r\n      >\r\n        <circle r=\"7\" fill=\"#333\" opacity=\"0.8\"/>\r\n        <text \r\n          text-anchor=\"middle\" \r\n          dy=\"3\" \r\n          font-size=\"8\" \r\n          fill=\"white\"\r\n        >\r\n          {{ getConditionIcon(condition) }}\r\n        </text>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 特殊能力指示器 -->\r\n    <g class=\"special-abilities\" :transform=\"`translate(${-tokenRadius + 8}, ${tokenRadius - 8})`\">\r\n      <!-- 法术能力 -->\r\n      <circle \r\n        v-if=\"monster.hasSpells\"\r\n        r=\"4\"\r\n        fill=\"#9c27b0\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"monster.hasSpells\"\r\n        text-anchor=\"middle\"\r\n        dy=\"2\"\r\n        font-size=\"6\"\r\n        fill=\"white\"\r\n      >\r\n        ✨\r\n      </text>\r\n      \r\n      <!-- 传奇行动 -->\r\n      <circle \r\n        v-if=\"monster.hasLegendaryActions\"\r\n        :transform=\"`translate(12, 0)`\"\r\n        r=\"4\"\r\n        fill=\"#ff9800\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"monster.hasLegendaryActions\"\r\n        :transform=\"`translate(12, 0)`\"\r\n        text-anchor=\"middle\"\r\n        dy=\"2\"\r\n        font-size=\"6\"\r\n        fill=\"white\"\r\n      >\r\n        ⚡\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 伤害数字动画 -->\r\n    <g v-if=\"damageAnimation\" class=\"damage-animation\">\r\n      <text \r\n        :y=\"-getNameYPosition() - 20\"\r\n        text-anchor=\"middle\"\r\n        font-size=\"14\"\r\n        font-weight=\"bold\"\r\n        :fill=\"damageAnimation.color\"\r\n        opacity=\"0\"\r\n      >\r\n        {{ damageAnimation.text }}\r\n        <animate \r\n          attributeName=\"y\" \r\n          :values=\"`${-getNameYPosition() - 20};${-getNameYPosition() - 50}`\"\r\n          dur=\"1.5s\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0\"\r\n          dur=\"1.5s\"\r\n        />\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 攻击动画效果 -->\r\n    <g v-if=\"attackAnimation\" class=\"attack-animation\">\r\n      <circle \r\n        :r=\"tokenRadius\"\r\n        fill=\"none\"\r\n        stroke=\"#ff4444\"\r\n        stroke-width=\"4\"\r\n        opacity=\"0\"\r\n      >\r\n        <animate \r\n          attributeName=\"r\" \r\n          :values=\"`${tokenRadius};${tokenRadius + 20}`\"\r\n          dur=\"0.5s\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0.8;0\"\r\n          dur=\"0.5s\"\r\n        />\r\n      </circle>\r\n    </g>\r\n    \r\n    <!-- KP控制手柄 -->\r\n    <circle \r\n      v-if=\"canControl && selected\"\r\n      :r=\"tokenRadius + 5\"\r\n      fill=\"transparent\"\r\n      stroke=\"none\"\r\n      style=\"cursor: move\"\r\n      @mousedown=\"startDrag\"\r\n    />\r\n  </g>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MonsterToken',\r\n  props: {\r\n    monster: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    },\r\n    zoom: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    selected: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canControl: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showThreatRange: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      tokenRadius: 20,\r\n      isDragging: false,\r\n      dragStart: null,\r\n      damageAnimation: null,\r\n      attackAnimation: false\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    x() {\r\n      return (this.monster.position?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    y() {\r\n      return (this.monster.position?.y || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    threatRange() {\r\n      // 根据怪物武器和能力计算威胁范围\r\n      return this.monster.reach || 1.5\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 获取怪物颜色\r\n     */\r\n    getMonsterColor() {\r\n      const colorMap = {\r\n        'beast': '#8bc34a',      // 野兽 - 绿色\r\n        'humanoid': '#ff9800',   // 人形 - 橙色\r\n        'undead': '#9c27b0',     // 不死 - 紫色\r\n        'fiend': '#f44336',      // 恶魔 - 红色\r\n        'celestial': '#ffeb3b',  // 天界 - 黄色\r\n        'dragon': '#e91e63',     // 龙类 - 粉红\r\n        'aberration': '#3f51b5', // 异怪 - 蓝色\r\n        'construct': '#607d8b',  // 构装 - 灰色\r\n        'elemental': '#00bcd4',  // 元素 - 青色\r\n        'fey': '#4caf50',        // 精类 - 浅绿\r\n        'giant': '#795548',      // 巨人 - 棕色\r\n        'monstrosity': '#ff5722', // 怪物 - 深橙\r\n        'ooze': '#9e9e9e',       // 软泥 - 灰色\r\n        'plant': '#689f38'       // 植物 - 深绿\r\n      }\r\n      \r\n      return colorMap[this.monster.type] || '#666666'\r\n    },\r\n    \r\n    /**\r\n     * 获取怪物图标\r\n     */\r\n    getMonsterIcon() {\r\n      const iconMap = {\r\n        'beast': '🐺',\r\n        'humanoid': '👤',\r\n        'undead': '💀',\r\n        'fiend': '👹',\r\n        'celestial': '👼',\r\n        'dragon': '🐉',\r\n        'aberration': '👁️',\r\n        'construct': '🤖',\r\n        'elemental': '🌪️',\r\n        'fey': '🧚',\r\n        'giant': '🗿',\r\n        'monstrosity': '👾',\r\n        'ooze': '🟢',\r\n        'plant': '🌿'\r\n      }\r\n      \r\n      return iconMap[this.monster.type] || '❓'\r\n    },\r\n    \r\n    /**\r\n     * 获取图标大小\r\n     */\r\n    getIconSize() {\r\n      if (this.monster.size === 'huge') return 32\r\n      if (this.monster.size === 'large') return 24\r\n      return 16\r\n    },\r\n    \r\n    /**\r\n     * 获取名称Y位置\r\n     */\r\n    getNameYPosition() {\r\n      if (this.monster.size === 'huge') return this.tokenRadius * 2 + 15\r\n      if (this.monster.size === 'large') return this.tokenRadius * 1.5 + 12\r\n      return this.tokenRadius + 15\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值颜色\r\n     */\r\n    getHealthColor() {\r\n      const healthPercent = this.getHealthPercentage()\r\n      \r\n      if (healthPercent > 75) return '#4caf50'\r\n      if (healthPercent > 50) return '#ff9800'\r\n      if (healthPercent > 25) return '#f44336'\r\n      return '#9c27b0'\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值百分比\r\n     */\r\n    getHealthPercentage() {\r\n      const current = this.monster.currentHP || this.monster.hitPoints\r\n      const max = this.monster.maxHP || this.monster.hitPoints\r\n      return max > 0 ? (current / max) * 100 : 0\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值条宽度\r\n     */\r\n    getHealthBarWidth() {\r\n      const maxWidth = (this.tokenRadius - 4) * 2\r\n      const healthPercent = this.getHealthPercentage() / 100\r\n      return maxWidth * healthPercent\r\n    },\r\n    \r\n    /**\r\n     * 获取状态效果图标\r\n     */\r\n    getConditionIcon(condition) {\r\n      const iconMap = {\r\n        'unconscious': '💤',\r\n        'dying': '💀',\r\n        'prone': '⬇️',\r\n        'stunned': '😵',\r\n        'restrained': '🔒',\r\n        'frightened': '😨',\r\n        'poisoned': '☠️',\r\n        'bleeding': '🩸',\r\n        'burning': '🔥',\r\n        'frozen': '❄️',\r\n        'paralyzed': '⚡',\r\n        'blinded': '👁️',\r\n        'deafened': '👂',\r\n        'charmed': '💖',\r\n        'confused': '❓',\r\n        'enraged': '😡',\r\n        'invisible': '👻'\r\n      }\r\n      \r\n      return iconMap[condition] || '?'\r\n    },\r\n    \r\n    /**\r\n     * 开始拖拽 (仅KP可用)\r\n     */\r\n    startDrag(event) {\r\n      if (!this.canControl) return\r\n      \r\n      this.isDragging = true\r\n      this.dragStart = {\r\n        x: event.clientX,\r\n        y: event.clientY,\r\n        monsterX: this.monster.position.x,\r\n        monsterY: this.monster.position.y\r\n      }\r\n      \r\n      document.addEventListener('mousemove', this.handleDrag)\r\n      document.addEventListener('mouseup', this.endDrag)\r\n      \r\n      event.stopPropagation()\r\n    },\r\n    \r\n    /**\r\n     * 处理拖拽\r\n     */\r\n    handleDrag(event) {\r\n      if (!this.isDragging || !this.dragStart) return\r\n      \r\n      const deltaX = event.clientX - this.dragStart.x\r\n      const deltaY = event.clientY - this.dragStart.y\r\n      \r\n      const gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom))\r\n      const gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom))\r\n      \r\n      const newX = this.dragStart.monsterX + gridDeltaX\r\n      const newY = this.dragStart.monsterY + gridDeltaY\r\n      \r\n      this.$emit('move', this.monster, { x: newX, y: newY })\r\n    },\r\n    \r\n    /**\r\n     * 结束拖拽\r\n     */\r\n    endDrag() {\r\n      this.isDragging = false\r\n      this.dragStart = null\r\n      \r\n      document.removeEventListener('mousemove', this.handleDrag)\r\n      document.removeEventListener('mouseup', this.endDrag)\r\n    },\r\n    \r\n    /**\r\n     * 播放伤害动画\r\n     */\r\n    playDamageAnimation(damage, type = 'normal') {\r\n      const colors = {\r\n        normal: '#f44336',\r\n        critical: '#e91e63',\r\n        healing: '#4caf50',\r\n        miss: '#9e9e9e'\r\n      }\r\n      \r\n      const texts = {\r\n        normal: `-${damage}`,\r\n        critical: `CRIT! -${damage}`,\r\n        healing: `+${damage}`,\r\n        miss: 'MISS'\r\n      }\r\n      \r\n      this.damageAnimation = {\r\n        text: texts[type] || `-${damage}`,\r\n        color: colors[type] || '#f44336'\r\n      }\r\n      \r\n      setTimeout(() => {\r\n        this.damageAnimation = null\r\n      }, 1500)\r\n    },\r\n    \r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAttackAnimation() {\r\n      this.attackAnimation = true\r\n      \r\n      setTimeout(() => {\r\n        this.attackAnimation = false\r\n      }, 500)\r\n    },\r\n    \r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    playDeathAnimation() {\r\n      // 添加死亡动画效果\r\n      this.$el.style.transition = 'all 1s ease-out'\r\n      this.$el.style.opacity = '0.3'\r\n      this.$el.style.transform += ' scale(0.8)'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.monster-token {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.monster-token:hover {\r\n  filter: brightness(1.1);\r\n}\r\n\r\n.monster-token.selected {\r\n  filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.8));\r\n}\r\n\r\n.monster-token.boss-monster {\r\n  filter: drop-shadow(0 0 12px rgba(156, 39, 176, 0.9));\r\n}\r\n\r\n.monster-token.can-control {\r\n  cursor: move;\r\n}\r\n\r\n.monster-name-text {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.monster-icon {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.challenge-rating circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.status-effects circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.special-abilities circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.damage-animation text {\r\n  font-family: 'Arial Black', sans-serif;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n/* 大型怪物样式 */\r\n.monster-token.large-monster .monster-body rect {\r\n  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n/* 巨型怪物样式 */\r\n.monster-token.huge-monster .monster-body rect {\r\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));\r\n}\r\n\r\n/* Boss怪物特殊效果 */\r\n.monster-token.boss-monster .monster-body {\r\n  animation: bossGlow 3s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes bossGlow {\r\n  from { filter: brightness(1); }\r\n  to { filter: brightness(1.2); }\r\n}\r\n\r\n/* 小怪特殊效果 */\r\n.monster-token.minion-monster {\r\n  opacity: 0.8;\r\n  transform: scale(0.9);\r\n}\r\n</style>"], "mappings": ";;AAwRA,eAAe;EACbA,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE;IACLC,OAAO,EAAE;MACPC,IAAI,EAAEC,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC;IACDC,QAAQ,EAAE;MACRH,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDC,IAAI,EAAE;MACJL,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDC,UAAU,EAAE;MACVR,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDE,eAAe,EAAE;MACfT,IAAI,EAAEO,OAAO;MACb,WAAS;IACX;EACF,CAAC;EAEDG,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,IAAI;MACfC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE;IACnB;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,CAAC,WAADA,CAACA,CAAA,EAAG;MAAA,IAAAC,qBAAA;MACF,OAAO,CAAC,EAAAA,qBAAA,OAAI,CAACnB,OAAO,CAACoB,QAAQ,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuBD,CAAA,KAAK,CAAC,IAAI,IAAI,CAACd,QAAO,GAAI,IAAI,CAACE,IAAG;IACnE,CAAC;IAEDe,CAAC,WAADA,CAACA,CAAA,EAAG;MAAA,IAAAC,sBAAA;MACF,OAAO,CAAC,EAAAA,sBAAA,OAAI,CAACtB,OAAO,CAACoB,QAAQ,cAAAE,sBAAA,uBAArBA,sBAAA,CAAuBD,CAAA,KAAK,CAAC,IAAI,IAAI,CAACjB,QAAO,GAAI,IAAI,CAACE,IAAG;IACnE,CAAC;IAEDiB,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ;MACA,OAAO,IAAI,CAACvB,OAAO,CAACwB,KAAI,IAAK,GAAE;IACjC;EACF,CAAC;EAEDC,OAAO,EAAE;IACP;;;IAGAC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAMC,QAAO,GAAI;QACf,OAAO,EAAE,SAAS;QAAO;QACzB,UAAU,EAAE,SAAS;QAAI;QACzB,QAAQ,EAAE,SAAS;QAAM;QACzB,OAAO,EAAE,SAAS;QAAO;QACzB,WAAW,EAAE,SAAS;QAAG;QACzB,QAAQ,EAAE,SAAS;QAAM;QACzB,YAAY,EAAE,SAAS;QAAE;QACzB,WAAW,EAAE,SAAS;QAAG;QACzB,WAAW,EAAE,SAAS;QAAG;QACzB,KAAK,EAAE,SAAS;QAAS;QACzB,OAAO,EAAE,SAAS;QAAO;QACzB,aAAa,EAAE,SAAS;QAAE;QAC1B,MAAM,EAAE,SAAS;QAAQ;QACzB,OAAO,EAAE,SAAQ,CAAQ;MAC3B;MAEA,OAAOA,QAAQ,CAAC,IAAI,CAAC3B,OAAO,CAACC,IAAI,KAAK,SAAQ;IAChD,CAAC;IAED;;;IAGA2B,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAMC,OAAM,GAAI;QACd,OAAO,EAAE,IAAI;QACb,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;QACd,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,IAAI;QACjB,QAAQ,EAAE,IAAI;QACd,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,IAAI;QACjB,WAAW,EAAE,KAAK;QAClB,KAAK,EAAE,IAAI;QACX,OAAO,EAAE,IAAI;QACb,aAAa,EAAE,IAAI;QACnB,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE;MACX;MAEA,OAAOA,OAAO,CAAC,IAAI,CAAC7B,OAAO,CAACC,IAAI,KAAK,GAAE;IACzC,CAAC;IAED;;;IAGA6B,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,IAAI,CAAC9B,OAAO,CAAC+B,IAAG,KAAM,MAAM,EAAE,OAAO,EAAC;MAC1C,IAAI,IAAI,CAAC/B,OAAO,CAAC+B,IAAG,KAAM,OAAO,EAAE,OAAO,EAAC;MAC3C,OAAO,EAAC;IACV,CAAC;IAED;;;IAGAC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,IAAI,CAAChC,OAAO,CAAC+B,IAAG,KAAM,MAAM,EAAE,OAAO,IAAI,CAACnB,WAAU,GAAI,IAAI,EAAC;MACjE,IAAI,IAAI,CAACZ,OAAO,CAAC+B,IAAG,KAAM,OAAO,EAAE,OAAO,IAAI,CAACnB,WAAU,GAAI,GAAE,GAAI,EAAC;MACpE,OAAO,IAAI,CAACA,WAAU,GAAI,EAAC;IAC7B,CAAC;IAED;;;IAGAqB,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAMC,aAAY,GAAI,IAAI,CAACC,mBAAmB,CAAC;MAE/C,IAAID,aAAY,GAAI,EAAE,EAAE,OAAO,SAAQ;MACvC,IAAIA,aAAY,GAAI,EAAE,EAAE,OAAO,SAAQ;MACvC,IAAIA,aAAY,GAAI,EAAE,EAAE,OAAO,SAAQ;MACvC,OAAO,SAAQ;IACjB,CAAC;IAED;;;IAGAC,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,IAAMC,OAAM,GAAI,IAAI,CAACpC,OAAO,CAACqC,SAAQ,IAAK,IAAI,CAACrC,OAAO,CAACsC,SAAQ;MAC/D,IAAMC,GAAE,GAAI,IAAI,CAACvC,OAAO,CAACwC,KAAI,IAAK,IAAI,CAACxC,OAAO,CAACsC,SAAQ;MACvD,OAAOC,GAAE,GAAI,IAAKH,OAAM,GAAIG,GAAG,GAAI,GAAE,GAAI;IAC3C,CAAC;IAED;;;IAGAE,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAMC,QAAO,GAAI,CAAC,IAAI,CAAC9B,WAAU,GAAI,CAAC,IAAI;MAC1C,IAAMsB,aAAY,GAAI,IAAI,CAACC,mBAAmB,CAAC,IAAI,GAAE;MACrD,OAAOO,QAAO,GAAIR,aAAY;IAChC,CAAC;IAED;;;IAGAS,gBAAgB,WAAhBA,gBAAgBA,CAACC,SAAS,EAAE;MAC1B,IAAMf,OAAM,GAAI;QACd,aAAa,EAAE,IAAI;QACnB,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,IAAI;QACf,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,IAAI;QAClB,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,GAAG;QAChB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,IAAI;QAChB,SAAS,EAAE,IAAI;QACf,UAAU,EAAE,GAAG;QACf,SAAS,EAAE,IAAI;QACf,WAAW,EAAE;MACf;MAEA,OAAOA,OAAO,CAACe,SAAS,KAAK,GAAE;IACjC,CAAC;IAED;;;IAGAC,SAAS,WAATA,SAASA,CAACC,KAAK,EAAE;MACf,IAAI,CAAC,IAAI,CAACrC,UAAU,EAAE;MAEtB,IAAI,CAACI,UAAS,GAAI,IAAG;MACrB,IAAI,CAACC,SAAQ,GAAI;QACfI,CAAC,EAAE4B,KAAK,CAACC,OAAO;QAChB1B,CAAC,EAAEyB,KAAK,CAACE,OAAO;QAChBC,QAAQ,EAAE,IAAI,CAACjD,OAAO,CAACoB,QAAQ,CAACF,CAAC;QACjCgC,QAAQ,EAAE,IAAI,CAAClD,OAAO,CAACoB,QAAQ,CAACC;MAClC;MAEA8B,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACC,UAAU;MACtDF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACE,OAAO;MAEjDR,KAAK,CAACS,eAAe,CAAC;IACxB,CAAC;IAED;;;IAGAF,UAAU,WAAVA,UAAUA,CAACP,KAAK,EAAE;MAChB,IAAI,CAAC,IAAI,CAACjC,UAAS,IAAK,CAAC,IAAI,CAACC,SAAS,EAAE;MAEzC,IAAM0C,MAAK,GAAIV,KAAK,CAACC,OAAM,GAAI,IAAI,CAACjC,SAAS,CAACI,CAAA;MAC9C,IAAMuC,MAAK,GAAIX,KAAK,CAACE,OAAM,GAAI,IAAI,CAAClC,SAAS,CAACO,CAAA;MAE9C,IAAMqC,UAAS,GAAIC,IAAI,CAACC,KAAK,CAACJ,MAAK,IAAK,IAAI,CAACpD,QAAO,GAAI,IAAI,CAACE,IAAI,CAAC;MAClE,IAAMuD,UAAS,GAAIF,IAAI,CAACC,KAAK,CAACH,MAAK,IAAK,IAAI,CAACrD,QAAO,GAAI,IAAI,CAACE,IAAI,CAAC;MAElE,IAAMwD,IAAG,GAAI,IAAI,CAAChD,SAAS,CAACmC,QAAO,GAAIS,UAAS;MAChD,IAAMK,IAAG,GAAI,IAAI,CAACjD,SAAS,CAACoC,QAAO,GAAIW,UAAS;MAEhD,IAAI,CAACG,KAAK,CAAC,MAAM,EAAE,IAAI,CAAChE,OAAO,EAAE;QAAEkB,CAAC,EAAE4C,IAAI;QAAEzC,CAAC,EAAE0C;MAAK,CAAC;IACvD,CAAC;IAED;;;IAGAT,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,IAAI,CAACzC,UAAS,GAAI,KAAI;MACtB,IAAI,CAACC,SAAQ,GAAI,IAAG;MAEpBqC,QAAQ,CAACc,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACZ,UAAU;MACzDF,QAAQ,CAACc,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACX,OAAO;IACtD,CAAC;IAED;;;IAGAY,mBAAmB,WAAnBA,mBAAmBA,CAACC,MAAM,EAAmB;MAAA,IAAAC,KAAA;MAAA,IAAjBnE,IAAG,GAAAoE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAI,QAAQ;MACzC,IAAMG,MAAK,GAAI;QACbC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,SAAS;QAClBC,IAAI,EAAE;MACR;MAEA,IAAMC,KAAI,GAAI;QACZJ,MAAM,MAAAK,MAAA,CAAMX,MAAM,CAAE;QACpBO,QAAQ,YAAAI,MAAA,CAAYX,MAAM,CAAE;QAC5BQ,OAAO,MAAAG,MAAA,CAAMX,MAAM,CAAE;QACrBS,IAAI,EAAE;MACR;MAEA,IAAI,CAAC7D,eAAc,GAAI;QACrBgE,IAAI,EAAEF,KAAK,CAAC5E,IAAI,SAAA6E,MAAA,CAASX,MAAM,CAAE;QACjCa,KAAK,EAAER,MAAM,CAACvE,IAAI,KAAK;MACzB;MAEAgF,UAAU,CAAC,YAAM;QACfb,KAAI,CAACrD,eAAc,GAAI,IAAG;MAC5B,CAAC,EAAE,IAAI;IACT,CAAC;IAED;;;IAGAmE,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACpB,IAAI,CAACnE,eAAc,GAAI,IAAG;MAE1BiE,UAAU,CAAC,YAAM;QACfE,MAAI,CAACnE,eAAc,GAAI,KAAI;MAC7B,CAAC,EAAE,GAAG;IACR,CAAC;IAED;;;IAGAoE,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB;MACA,IAAI,CAACC,GAAG,CAACC,KAAK,CAACC,UAAS,GAAI,iBAAgB;MAC5C,IAAI,CAACF,GAAG,CAACC,KAAK,CAACE,OAAM,GAAI,KAAI;MAC7B,IAAI,CAACH,GAAG,CAACC,KAAK,CAACG,SAAQ,IAAK,aAAY;IAC1C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}