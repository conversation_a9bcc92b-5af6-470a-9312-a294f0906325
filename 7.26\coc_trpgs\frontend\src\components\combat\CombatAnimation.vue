<template>
  <!-- 战斗动画效果组件 -->
  <g class="combat-animation" :class="animation.type">
    <!-- 近战攻击动画 -->
    <g v-if="animation.type === 'melee_attack'">
      <!-- 攻击轨迹 -->
      <path 
        :d="getMeleeAttackPath()"
        fill="none"
        stroke="#ff4444"
        stroke-width="4"
        stroke-linecap="round"
        opacity="0"
      >
        <animate 
          attributeName="opacity" 
          values="0;1;0"
          :dur="duration + 'ms'"
        />
        <animate 
          attributeName="stroke-dasharray" 
          :values="`0,${getPathLength()};${getPathLength()},0`"
          :dur="duration + 'ms'"
        />
      </path>
      
      <!-- 冲击波效果 -->
      <circle 
        :cx="toX"
        :cy="toY"
        r="0"
        fill="none"
        stroke="#ff6666"
        stroke-width="2"
        opacity="0.8"
      >
        <animate 
          attributeName="r" 
          values="0;30;0"
          :dur="duration * 0.3 + 'ms'"
          :begin="duration * 0.7 + 'ms'"
        />
        <animate 
          attributeName="opacity" 
          values="0.8;0"
          :dur="duration * 0.3 + 'ms'"
          :begin="duration * 0.7 + 'ms'"
        />
      </circle>
    </g>
    
    <!-- 远程攻击动画 -->
    <g v-if="animation.type === 'ranged_attack'">
      <!-- 弹道轨迹 -->
      <circle 
        :cx="fromX"
        :cy="fromY"
        r="3"
        :fill="getProjectileColor()"
        opacity="0"
      >
        <animateMotion 
          :dur="duration + 'ms'"
          :path="getProjectilePath()"
        />
        <animate 
          attributeName="opacity" 
          values="0;1;1;0"
          :dur="duration + 'ms'"
        />
      </circle>
      
      <!-- 轨迹线 -->
      <line 
        :x1="fromX"
        :y1="fromY"
        :x2="toX"
        :y2="toY"
        stroke="#ffaa00"
        stroke-width="2"
        stroke-dasharray="5,5"
        opacity="0"
      >
        <animate 
          attributeName="opacity" 
          values="0;0.6;0"
          :dur="duration + 'ms'"
        />
      </line>
      
      <!-- 命中效果 -->
      <g :transform="`translate(${toX}, ${toY})`">
        <circle 
          r="0"
          fill="none"
          stroke="#ffaa00"
          stroke-width="3"
          opacity="0"
        >
          <animate 
            attributeName="r" 
            values="0;25"
            :dur="duration * 0.2 + 'ms'"
            :begin="duration * 0.8 + 'ms'"
          />
          <animate 
            attributeName="opacity" 
            values="0;1;0"
            :dur="duration * 0.2 + 'ms'"
            :begin="duration * 0.8 + 'ms'"
          />
        </circle>
        
        <!-- 火花效果 -->
        <g v-for="i in 8" :key="i">
          <line 
            x1="0" y1="0"
            :x2="Math.cos(i * Math.PI / 4) * 15"
            :y2="Math.sin(i * Math.PI / 4) * 15"
            stroke="#ffdd00"
            stroke-width="2"
            opacity="0"
          >
            <animate 
              attributeName="opacity" 
              values="0;1;0"
              :dur="duration * 0.3 + 'ms'"
              :begin="duration * 0.8 + 'ms'"
            />
          </line>
        </g>
      </g>
    </g>
    
    <!-- 法术攻击动画 -->
    <g v-if="animation.type === 'spell_attack'">
      <!-- 魔法光球 -->
      <circle 
        :cx="fromX"
        :cy="fromY"
        r="8"
        :fill="getSpellColor()"
        opacity="0"
      >
        <animateMotion 
          :dur="duration + 'ms'"
          :path="getSpellPath()"
        />
        <animate 
          attributeName="opacity" 
          values="0;1;1;0.5"
          :dur="duration + 'ms'"
        />
        <animate 
          attributeName="r" 
          values="8;12;8"
          :dur="500 + 'ms'"
          repeatCount="indefinite"
        />
      </circle>
      
      <!-- 魔法粒子效果 -->
      <g v-for="i in 12" :key="i">
        <circle 
          :cx="fromX + Math.cos(i * Math.PI / 6) * 20"
          :cy="fromY + Math.sin(i * Math.PI / 6) * 20"
          r="2"
          :fill="getSpellColor()"
          opacity="0"
        >
          <animateMotion 
            :dur="duration + 'ms'"
            :path="getParticlePath(i)"
          />
          <animate 
            attributeName="opacity" 
            values="0;0.8;0"
            :dur="duration + 'ms'"
          />
        </circle>
      </g>
      
      <!-- 爆炸效果 -->
      <g :transform="`translate(${toX}, ${toY})`">
        <circle 
          r="0"
          :fill="getSpellColor()"
          opacity="0"
        >
          <animate 
            attributeName="r" 
            values="0;40;60"
            :dur="duration * 0.4 + 'ms'"
            :begin="duration * 0.8 + 'ms'"
          />
          <animate 
            attributeName="opacity" 
            values="0;0.8;0"
            :dur="duration * 0.4 + 'ms'"
            :begin="duration * 0.8 + 'ms'"
          />
        </circle>
      </g>
    </g>
    
    <!-- 移动动画 -->
    <g v-if="animation.type === 'movement'">
      <!-- 移动轨迹 -->
      <path 
        :d="getMovementPath()"
        fill="none"
        stroke="#4ecdc4"
        stroke-width="3"
        stroke-dasharray="8,4"
        opacity="0"
      >
        <animate 
          attributeName="opacity" 
          values="0;0.8;0"
          :dur="duration + 'ms'"
        />
        <animate 
          attributeName="stroke-dashoffset" 
          values="0;-100"
          :dur="duration + 'ms'"
        />
      </path>
      
      <!-- 移动粒子 -->
      <g v-for="i in 6" :key="i">
        <circle 
          :cx="fromX"
          :cy="fromY"
          r="3"
          fill="#4ecdc4"
          opacity="0"
        >
          <animateMotion 
            :dur="duration + i * 100 + 'ms'"
            :path="getMovementPath()"
          />
          <animate 
            attributeName="opacity" 
            values="0;0.6;0"
            :dur="duration + i * 100 + 'ms'"
          />
        </circle>
      </g>
    </g>
    
    <!-- 治疗动画 -->
    <g v-if="animation.type === 'healing'">
      <!-- 治疗光环 -->
      <circle 
        :cx="toX"
        :cy="toY"
        r="0"
        fill="none"
        stroke="#4caf50"
        stroke-width="3"
        opacity="0"
      >
        <animate 
          attributeName="r" 
          values="0;50;0"
          :dur="duration + 'ms'"
        />
        <animate 
          attributeName="opacity" 
          values="0;0.8;0"
          :dur="duration + 'ms'"
        />
      </circle>
      
      <!-- 治疗粒子 -->
      <g v-for="i in 16" :key="i">
        <circle 
          :cx="toX + Math.cos(i * Math.PI / 8) * 60"
          :cy="toY + Math.sin(i * Math.PI / 8) * 60"
          r="4"
          fill="#4caf50"
          opacity="0"
        >
          <animateMotion 
            :dur="duration + 'ms'"
            :path="`M 0,0 L ${-Math.cos(i * Math.PI / 8) * 60},${-Math.sin(i * Math.PI / 8) * 60}`"
          />
          <animate 
            attributeName="opacity" 
            values="0;1;0"
            :dur="duration + 'ms'"
          />
        </circle>
      </g>
      
      <!-- 十字光效 -->
      <g :transform="`translate(${toX}, ${toY})`">
        <line x1="-20" y1="0" x2="20" y2="0" stroke="#66bb6a" stroke-width="4" opacity="0">
          <animate attributeName="opacity" values="0;1;0" :dur="duration * 0.5 + 'ms'" :begin="duration * 0.3 + 'ms'"/>
        </line>
        <line x1="0" y1="-20" x2="0" y2="20" stroke="#66bb6a" stroke-width="4" opacity="0">
          <animate attributeName="opacity" values="0;1;0" :dur="duration * 0.5 + 'ms'" :begin="duration * 0.3 + 'ms'"/>
        </line>
      </g>
    </g>
    
    <!-- 状态效果动画 -->
    <g v-if="animation.type === 'status_effect'">
      <!-- 毒素效果 -->
      <g v-if="animation.effect === 'poison'">
        <circle 
          :cx="toX"
          :cy="toY"
          r="25"
          fill="none"
          stroke="#8bc34a"
          stroke-width="2"
          opacity="0"
        >
          <animate attributeName="opacity" values="0;0.6;0" :dur="duration + 'ms'"/>
          <animate attributeName="r" values="25;35;25" :dur="1000 + 'ms'" repeatCount="indefinite"/>
        </circle>
        
        <!-- 毒气泡 -->
        <g v-for="i in 8" :key="i">
          <circle 
            :cx="toX + Math.cos(i * Math.PI / 4) * 20"
            :cy="toY + Math.sin(i * Math.PI / 4) * 20"
            r="3"
            fill="#689f38"
            opacity="0"
          >
            <animate attributeName="opacity" values="0;0.8;0" :dur="duration + i * 200 + 'ms'"/>
            <animate attributeName="cy" :values="`${toY + Math.sin(i * Math.PI / 4) * 20};${toY + Math.sin(i * Math.PI / 4) * 20 - 30}`" :dur="duration + i * 200 + 'ms'"/>
          </circle>
        </g>
      </g>
      
      <!-- 燃烧效果 -->
      <g v-if="animation.effect === 'burning'">
        <g v-for="i in 12" :key="i">
          <path 
            :d="`M ${toX + Math.cos(i * Math.PI / 6) * 15} ${toY + Math.sin(i * Math.PI / 6) * 15} Q ${toX + Math.cos(i * Math.PI / 6) * 10} ${toY + Math.sin(i * Math.PI / 6) * 10 - 20} ${toX + Math.cos(i * Math.PI / 6) * 5} ${toY + Math.sin(i * Math.PI / 6) * 5 - 30}`"
            fill="none"
            stroke="#ff5722"
            stroke-width="2"
            opacity="0"
          >
            <animate attributeName="opacity" values="0;1;0" :dur="duration + i * 100 + 'ms'"/>
          </path>
        </g>
      </g>
    </g>
    
    <!-- 死亡动画 -->
    <g v-if="animation.type === 'death'">
      <!-- 灵魂上升 -->
      <circle 
        :cx="toX"
        :cy="toY"
        r="8"
        fill="#e0e0e0"
        opacity="0"
      >
        <animate attributeName="cy" :values="`${toY};${toY - 100}`" :dur="duration + 'ms'"/>
        <animate attributeName="opacity" values="0;0.8;0" :dur="duration + 'ms'"/>
        <animate attributeName="r" values="8;12;8" :dur="1000 + 'ms'" repeatCount="indefinite"/>
      </circle>
      
      <!-- 光芒效果 -->
      <g :transform="`translate(${toX}, ${toY})`">
        <g v-for="i in 8" :key="i">
          <line 
            x1="0" y1="0"
            :x2="Math.cos(i * Math.PI / 4) * 40"
            :y2="Math.sin(i * Math.PI / 4) * 40"
            stroke="#ffffff"
            stroke-width="2"
            opacity="0"
          >
            <animate attributeName="opacity" values="0;0.8;0" :dur="duration * 0.5 + 'ms'"/>
          </line>
        </g>
      </g>
    </g>
  </g>
</template>

<script>
export default {
  name: 'CombatAnimation',
  props: {
    animation: {
      type: Object,
      required: true
    },
    gridSize: {
      type: Number,
      default: 40
    },
    zoom: {
      type: Number,
      default: 1
    }
  },
  
  data() {
    return {
      startTime: Date.now()
    }
  },
  
  computed: {
    duration() {
      return this.animation.duration || 1000
    },
    
    fromX() {
      return (this.animation.from?.x || 0) * this.gridSize * this.zoom
    },
    
    fromY() {
      return (this.animation.from?.y || 0) * this.gridSize * this.zoom
    },
    
    toX() {
      return (this.animation.to?.x || 0) * this.gridSize * this.zoom
    },
    
    toY() {
      return (this.animation.to?.y || 0) * this.gridSize * this.zoom
    }
  },
  
  mounted() {
    // 动画完成后触发回调
    setTimeout(() => {
      this.$emit('complete', this.animation.id)
    }, this.duration)
  },
  
  methods: {
    /**
     * 获取近战攻击路径
     */
    getMeleeAttackPath() {
      const midX = (this.fromX + this.toX) / 2
      const midY = (this.fromY + this.toY) / 2
      
      // 创建弧形攻击轨迹
      return `M ${this.fromX} ${this.fromY} Q ${midX + 20} ${midY - 20} ${this.toX} ${this.toY}`
    },
    
    /**
     * 获取弹道路径
     */
    getProjectilePath() {
      const midX = (this.fromX + this.toX) / 2
      const midY = (this.fromY + this.toY) / 2 - 30 // 抛物线效果
      
      return `M ${this.fromX} ${this.fromY} Q ${midX} ${midY} ${this.toX} ${this.toY}`
    },
    
    /**
     * 获取法术路径
     */
    getSpellPath() {
      // 法术通常是直线飞行
      return `M ${this.fromX} ${this.fromY} L ${this.toX} ${this.toY}`
    },
    
    /**
     * 获取粒子路径
     */
    getParticlePath(index) {
      const angle = (index * Math.PI * 2) / 12
      const offsetX = Math.cos(angle) * 10
      const offsetY = Math.sin(angle) * 10
      
      return `M ${this.fromX + offsetX} ${this.fromY + offsetY} L ${this.toX + offsetX} ${this.toY + offsetY}`
    },
    
    /**
     * 获取移动路径
     */
    getMovementPath() {
      return `M ${this.fromX} ${this.fromY} L ${this.toX} ${this.toY}`
    },
    
    /**
     * 获取路径长度
     */
    getPathLength() {
      const dx = this.toX - this.fromX
      const dy = this.toY - this.fromY
      return Math.sqrt(dx * dx + dy * dy)
    },
    
    /**
     * 获取弹道颜色
     */
    getProjectileColor() {
      const weapon = this.animation.weapon
      if (!weapon) return '#ffaa00'
      
      const colorMap = {
        'arrow': '#8d6e63',
        'bullet': '#607d8b',
        'bolt': '#795548',
        'stone': '#9e9e9e',
        'dart': '#546e7a'
      }
      
      return colorMap[weapon.projectileType] || '#ffaa00'
    },
    
    /**
     * 获取法术颜色
     */
    getSpellColor() {
      const spell = this.animation.spell
      if (!spell) return '#9c27b0'
      
      const colorMap = {
        'fire': '#f44336',
        'ice': '#2196f3',
        'lightning': '#ffeb3b',
        'acid': '#4caf50',
        'necrotic': '#9c27b0',
        'radiant': '#fff176',
        'force': '#e91e63',
        'psychic': '#673ab7'
      }
      
      return colorMap[spell.damageType] || '#9c27b0'
    }
  }
}
</script>

<style scoped>
.combat-animation {
  pointer-events: none;
}

/* 近战攻击动画样式 */
.combat-animation.melee_attack path {
  filter: drop-shadow(0 0 4px rgba(255, 68, 68, 0.6));
}

/* 远程攻击动画样式 */
.combat-animation.ranged_attack circle {
  filter: drop-shadow(0 0 3px rgba(255, 170, 0, 0.8));
}

/* 法术攻击动画样式 */
.combat-animation.spell_attack circle {
  filter: drop-shadow(0 0 6px rgba(156, 39, 176, 0.8));
}

/* 移动动画样式 */
.combat-animation.movement path {
  filter: drop-shadow(0 0 2px rgba(78, 205, 196, 0.6));
}

/* 治疗动画样式 */
.combat-animation.healing circle {
  filter: drop-shadow(0 0 4px rgba(76, 175, 80, 0.8));
}

/* 状态效果动画样式 */
.combat-animation.status_effect circle {
  filter: drop-shadow(0 0 3px rgba(139, 195, 74, 0.6));
}

/* 死亡动画样式 */
.combat-animation.death circle {
  filter: drop-shadow(0 0 8px rgba(224, 224, 224, 0.9));
}
</style>