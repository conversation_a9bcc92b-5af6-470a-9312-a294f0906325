import { createRouter, createWebHistory } from 'vue-router'
import Home from '@/views/Home.vue'
import Login from '@/views/Login.vue'
import Register from '@/views/Register.vue'
import CharacterManager from '@/views/CharacterManager.vue'
// import Room from '@/views/Room.vue' // 已替换为GameRoomFixed
import CharacterCreator from '@/views/ListenerCardCreator.vue'
import CharacterGrowth from '@/views/CharacterGrowth.vue'
import CreateRoom from '@/views/CreateRoom.vue'
import store from '@/store'




const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { guestOnly: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { guestOnly: true }
  },
  {
    path: '/characters',
    name: 'CharacterManager',
    component: CharacterManager,
    meta: { requiresAuth: true }
  },
  {
    path: '/create-character',
    name: 'CharacterCreator',
    component: CharacterCreator,
    meta: { requiresAuth: true }
  },
  {
    path: '/create-listener-card',
    redirect: '/create-character'
  },
  {
    path: '/character-growth',
    name: 'CharacterGrowth',
    component: CharacterGrowth,
    meta: { requiresAuth: true }
  },

  {
    path: '/create-room',
    name: 'CreateRoom',
    component: CreateRoom,
    meta: { requiresAuth: true }
  },
  {
    path: '/room/:id',
    name: 'Room',
    component: () => import('@/views/GameRoomFixed.vue'),
    meta: { requiresAuth: true },
    props: route => ({ roomId: route.params.id })
  },

  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/Settings.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/combat-test',
    name: 'CombatTest',
    component: () => import('@/views/CombatTest.vue'),
    meta: { requiresAuth: true }
  },

]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.isAuthenticated
  

  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
  } else if (to.meta.guestOnly && isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router 