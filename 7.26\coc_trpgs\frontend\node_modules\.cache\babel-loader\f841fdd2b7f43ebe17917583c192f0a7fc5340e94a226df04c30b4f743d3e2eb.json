{"ast": null, "code": "import \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nexport default {\n  name: 'InitiativeTracker',\n  props: {\n    initiativeOrder: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    currentRound: {\n      type: Number,\n      \"default\": 1\n    },\n    currentTurn: {\n      type: Number,\n      \"default\": 0\n    },\n    isKeeper: {\n      type: <PERSON><PERSON>an,\n      \"default\": false\n    },\n    canReroll: {\n      type: <PERSON><PERSON><PERSON>,\n      \"default\": true\n    }\n  },\n  computed: {\n    // 已行动的参与者数量\n    actedCount: function actedCount() {\n      return this.initiativeOrder.filter(function (p) {\n        return p.hasActed;\n      }).length;\n    },\n    // 存活的参与者数量\n    aliveCount: function aliveCount() {\n      return this.initiativeOrder.filter(function (p) {\n        return (p.currentHP || 0) > 0;\n      }).length;\n    }\n  },\n  watch: {\n    currentTurn: function currentTurn(newTurn) {\n      var _this = this;\n      // 自动滚动到当前回合\n      this.$nextTick(function () {\n        _this.scrollToCurrentTurn(newTurn);\n      });\n    }\n  },\n  methods: {\n    // 选择参与者\n    selectParticipant: function selectParticipant(participant, index) {\n      this.$emit('participant-selected', participant, index);\n    },\n    // 获取生命值百分比\n    getHealthPercentage: function getHealthPercentage(participant) {\n      var current = participant.currentHP || 0;\n      var max = participant.maxHP || participant.hitPoints || 1;\n      return Math.max(0, Math.min(100, current / max * 100));\n    },\n    // 获取生命值样式类\n    getHealthClass: function getHealthClass(participant) {\n      var percentage = this.getHealthPercentage(participant);\n      if (percentage > 75) return 'healthy';\n      if (percentage > 50) return 'injured';\n      if (percentage > 25) return 'wounded';\n      return 'critical';\n    },\n    // 获取先攻修正值显示\n    getInitiativeModifier: function getInitiativeModifier(participant) {\n      if (participant.initiativeModifier) {\n        return participant.initiativeModifier > 0 ? \"+\".concat(participant.initiativeModifier) : \"\".concat(participant.initiativeModifier);\n      }\n      return null;\n    },\n    // 获取状态效果名称\n    getStatusEffectName: function getStatusEffectName(effect) {\n      var names = {\n        bleeding: '流血',\n        poisoned: '中毒',\n        stunned: '眩晕',\n        frightened: '恐惧',\n        blessed: '祝福',\n        cursed: '诅咒',\n        prone: '倒地',\n        grappled: '被擒抱',\n        unconscious: '昏迷'\n      };\n      return names[effect] || effect;\n    },\n    // 获取状态效果图标\n    getStatusEffectIcon: function getStatusEffectIcon(effect) {\n      var icons = {\n        bleeding: '🩸',\n        poisoned: '☠️',\n        stunned: '😵',\n        frightened: '😨',\n        blessed: '✨',\n        cursed: '💀',\n        prone: '⬇️',\n        grappled: '🤝',\n        unconscious: '😴'\n      };\n      return icons[effect] || '❓';\n    },\n    // 获取武器图标\n    getWeaponIcon: function getWeaponIcon(weapon) {\n      if (!weapon) return 'fas fa-fist-raised';\n      var icons = {\n        melee: 'fas fa-sword',\n        ranged: 'fas fa-crosshairs',\n        firearm: 'fas fa-gun',\n        thrown: 'fas fa-hand-paper'\n      };\n      return icons[weapon.type] || 'fas fa-fist-raised';\n    },\n    // 标记为已行动\n    markAsActed: function markAsActed(participant) {\n      this.$emit('mark-acted', participant);\n    },\n    // 标记为未行动\n    markAsNotActed: function markAsNotActed(participant) {\n      this.$emit('mark-not-acted', participant);\n    },\n    // 延迟行动\n    delayAction: function delayAction(participant, index) {\n      this.$emit('delay-action', participant, index);\n    },\n    // 移除参与者\n    removeParticipant: function removeParticipant(participant) {\n      this.$emit('remove-participant', participant);\n    },\n    // 上一个回合\n    previousTurn: function previousTurn() {\n      this.$emit('previous-turn');\n    },\n    // 下一个回合\n    nextTurn: function nextTurn() {\n      this.$emit('next-turn');\n    },\n    // 结束本轮\n    endRound: function endRound() {\n      this.$emit('end-round');\n    },\n    // 重新投掷先攻\n    rerollInitiative: function rerollInitiative() {\n      this.$emit('reroll-initiative');\n    },\n    // 重置先攻\n    resetInitiative: function resetInitiative() {\n      this.$emit('reset-initiative');\n    },\n    // 滚动到当前回合\n    scrollToCurrentTurn: function scrollToCurrentTurn(turnIndex) {\n      var list = this.$refs.initiativeList;\n      if (!list) return;\n      var items = list.querySelectorAll('.initiative-item');\n      var currentItem = items[turnIndex];\n      if (currentItem) {\n        currentItem.scrollIntoView({\n          behavior: 'smooth',\n          block: 'nearest'\n        });\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "initiativeOrder", "type", "Array", "default", "currentRound", "Number", "currentTurn", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "can<PERSON><PERSON><PERSON>", "computed", "actedCount", "filter", "p", "hasActed", "length", "aliveCount", "currentHP", "watch", "newTurn", "_this", "$nextTick", "scrollToCurrentTurn", "methods", "selectParticipant", "participant", "index", "$emit", "getHealthPercentage", "current", "max", "maxHP", "hitPoints", "Math", "min", "getHealthClass", "percentage", "getInitiativeModifier", "initiativeModifier", "concat", "getStatusEffectName", "effect", "names", "bleeding", "poisoned", "stunned", "frightened", "blessed", "cursed", "prone", "grappled", "unconscious", "getStatusEffectIcon", "icons", "getWeaponIcon", "weapon", "melee", "ranged", "firearm", "thrown", "markAsActed", "markAsNotActed", "delayAction", "removeParticipant", "previousTurn", "nextTurn", "endRound", "rerollInitiative", "resetInitiative", "turnIndex", "list", "$refs", "initiativeList", "items", "querySelectorAll", "currentItem", "scrollIntoView", "behavior", "block"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\InitiativeTracker.vue"], "sourcesContent": ["<template>\r\n  <div class=\"initiative-tracker\">\r\n    <!-- 追踪器头部 -->\r\n    <div class=\"tracker-header\">\r\n      <div class=\"header-left\">\r\n        <i class=\"fas fa-list-ol\"></i>\r\n        <h3>先攻顺序</h3>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"round-info\">\r\n          <span class=\"round-label\">第</span>\r\n          <span class=\"round-number\">{{ currentRound }}</span>\r\n          <span class=\"round-label\">轮</span>\r\n        </div>\r\n        <button \r\n          v-if=\"canReroll\" \r\n          @click=\"rerollInitiative\" \r\n          class=\"reroll-btn\"\r\n          title=\"重新投掷先攻\"\r\n        >\r\n          <i class=\"fas fa-dice\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 先攻列表 -->\r\n    <div class=\"initiative-list\" ref=\"initiativeList\">\r\n      <div \r\n        v-for=\"(participant, index) in initiativeOrder\"\r\n        :key=\"participant.id\"\r\n        class=\"initiative-item\"\r\n        :class=\"{ \r\n          current: index === currentTurn,\r\n          acted: participant.hasActed,\r\n          player: participant.isPlayer,\r\n          enemy: !participant.isPlayer,\r\n          unconscious: participant.currentHP <= 0,\r\n          delayed: participant.delayedAction\r\n        }\"\r\n        @click=\"selectParticipant(participant, index)\"\r\n      >\r\n        <!-- 先攻值 -->\r\n        <div class=\"initiative-value\">\r\n          <div class=\"initiative-number\">{{ participant.initiative }}</div>\r\n          <div class=\"initiative-modifier\" v-if=\"getInitiativeModifier(participant)\">\r\n            {{ getInitiativeModifier(participant) }}\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 参与者信息 -->\r\n        <div class=\"participant-info\">\r\n          <div class=\"participant-avatar\">\r\n            <img \r\n              :src=\"participant.avatar || '/default-avatar.png'\" \r\n              :alt=\"participant.name\"\r\n              class=\"avatar-image\"\r\n            >\r\n            <!-- 当前回合指示器 -->\r\n            <div v-if=\"index === currentTurn\" class=\"current-indicator\">\r\n              <i class=\"fas fa-play\"></i>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"participant-details\">\r\n            <div class=\"participant-name\">{{ participant.name }}</div>\r\n            <div class=\"participant-type\">\r\n              {{ participant.isPlayer ? '玩家' : (participant.type || 'NPC') }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 状态信息 -->\r\n        <div class=\"participant-status\">\r\n          <!-- 生命值指示器 -->\r\n          <div class=\"health-indicator\">\r\n            <div class=\"health-bar\">\r\n              <div \r\n                class=\"health-fill\" \r\n                :class=\"getHealthClass(participant)\"\r\n                :style=\"{ width: getHealthPercentage(participant) + '%' }\"\r\n              ></div>\r\n            </div>\r\n            <div class=\"health-text\">\r\n              {{ participant.currentHP || 0 }}/{{ participant.maxHP || participant.hitPoints || 0 }}\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 状态效果 -->\r\n          <div class=\"status-effects\" v-if=\"participant.conditions && participant.conditions.length > 0\">\r\n            <div \r\n              v-for=\"effect in participant.conditions.slice(0, 3)\" \r\n              :key=\"effect\"\r\n              class=\"status-effect\"\r\n              :class=\"effect\"\r\n              :title=\"getStatusEffectName(effect)\"\r\n            >\r\n              {{ getStatusEffectIcon(effect) }}\r\n            </div>\r\n            <div \r\n              v-if=\"participant.conditions.length > 3\"\r\n              class=\"status-effect more\"\r\n              :title=\"`还有${participant.conditions.length - 3}个状态效果`\"\r\n            >\r\n              +{{ participant.conditions.length - 3 }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 行动控制 (仅KP可见) -->\r\n        <div class=\"action-controls\" v-if=\"isKeeper\">\r\n          <button \r\n            v-if=\"!participant.hasActed && index === currentTurn\"\r\n            @click.stop=\"markAsActed(participant)\"\r\n            class=\"control-btn acted-btn\"\r\n            title=\"标记为已行动\"\r\n          >\r\n            <i class=\"fas fa-check\"></i>\r\n          </button>\r\n          \r\n          <button \r\n            v-if=\"participant.hasActed\"\r\n            @click.stop=\"markAsNotActed(participant)\"\r\n            class=\"control-btn unacted-btn\"\r\n            title=\"标记为未行动\"\r\n          >\r\n            <i class=\"fas fa-undo\"></i>\r\n          </button>\r\n          \r\n          <button \r\n            @click.stop=\"delayAction(participant, index)\"\r\n            class=\"control-btn delay-btn\"\r\n            title=\"延迟行动\"\r\n            :disabled=\"participant.delayedAction\"\r\n          >\r\n            <i class=\"fas fa-clock\"></i>\r\n          </button>\r\n          \r\n          <button \r\n            @click.stop=\"removeParticipant(participant)\"\r\n            class=\"control-btn remove-btn\"\r\n            title=\"移除参与者\"\r\n          >\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n\r\n        <!-- 武器信息 -->\r\n        <div class=\"weapon-info\" v-if=\"participant.currentWeapon\">\r\n          <i :class=\"getWeaponIcon(participant.currentWeapon)\"></i>\r\n          <span class=\"weapon-name\">{{ participant.currentWeapon.name }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 回合控制 (仅KP可见) -->\r\n    <div class=\"round-controls\" v-if=\"isKeeper\">\r\n      <div class=\"control-group\">\r\n        <button @click=\"previousTurn\" class=\"control-btn\" :disabled=\"currentTurn === 0\">\r\n          <i class=\"fas fa-step-backward\"></i>\r\n          <span>上一个</span>\r\n        </button>\r\n        \r\n        <button @click=\"nextTurn\" class=\"control-btn primary\">\r\n          <i class=\"fas fa-step-forward\"></i>\r\n          <span>下一个</span>\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"control-group\">\r\n        <button @click=\"endRound\" class=\"control-btn warning\">\r\n          <i class=\"fas fa-refresh\"></i>\r\n          <span>结束本轮</span>\r\n        </button>\r\n        \r\n        <button @click=\"resetInitiative\" class=\"control-btn danger\">\r\n          <i class=\"fas fa-redo\"></i>\r\n          <span>重置先攻</span>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 统计信息 -->\r\n    <div class=\"tracker-stats\">\r\n      <div class=\"stat-item\">\r\n        <span class=\"stat-label\">参与者:</span>\r\n        <span class=\"stat-value\">{{ initiativeOrder.length }}</span>\r\n      </div>\r\n      <div class=\"stat-item\">\r\n        <span class=\"stat-label\">已行动:</span>\r\n        <span class=\"stat-value\">{{ actedCount }}/{{ initiativeOrder.length }}</span>\r\n      </div>\r\n      <div class=\"stat-item\">\r\n        <span class=\"stat-label\">存活:</span>\r\n        <span class=\"stat-value\">{{ aliveCount }}/{{ initiativeOrder.length }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'InitiativeTracker',\r\n  props: {\r\n    initiativeOrder: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    currentRound: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    currentTurn: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    isKeeper: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canReroll: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 已行动的参与者数量\r\n    actedCount() {\r\n      return this.initiativeOrder.filter(p => p.hasActed).length\r\n    },\r\n    \r\n    // 存活的参与者数量\r\n    aliveCount() {\r\n      return this.initiativeOrder.filter(p => (p.currentHP || 0) > 0).length\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    currentTurn(newTurn) {\r\n      // 自动滚动到当前回合\r\n      this.$nextTick(() => {\r\n        this.scrollToCurrentTurn(newTurn)\r\n      })\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 选择参与者\r\n    selectParticipant(participant, index) {\r\n      this.$emit('participant-selected', participant, index)\r\n    },\r\n    \r\n    // 获取生命值百分比\r\n    getHealthPercentage(participant) {\r\n      const current = participant.currentHP || 0\r\n      const max = participant.maxHP || participant.hitPoints || 1\r\n      return Math.max(0, Math.min(100, (current / max) * 100))\r\n    },\r\n    \r\n    // 获取生命值样式类\r\n    getHealthClass(participant) {\r\n      const percentage = this.getHealthPercentage(participant)\r\n      if (percentage > 75) return 'healthy'\r\n      if (percentage > 50) return 'injured'\r\n      if (percentage > 25) return 'wounded'\r\n      return 'critical'\r\n    },\r\n    \r\n    // 获取先攻修正值显示\r\n    getInitiativeModifier(participant) {\r\n      if (participant.initiativeModifier) {\r\n        return participant.initiativeModifier > 0 \r\n          ? `+${participant.initiativeModifier}` \r\n          : `${participant.initiativeModifier}`\r\n      }\r\n      return null\r\n    },\r\n    \r\n    // 获取状态效果名称\r\n    getStatusEffectName(effect) {\r\n      const names = {\r\n        bleeding: '流血',\r\n        poisoned: '中毒',\r\n        stunned: '眩晕',\r\n        frightened: '恐惧',\r\n        blessed: '祝福',\r\n        cursed: '诅咒',\r\n        prone: '倒地',\r\n        grappled: '被擒抱',\r\n        unconscious: '昏迷'\r\n      }\r\n      return names[effect] || effect\r\n    },\r\n    \r\n    // 获取状态效果图标\r\n    getStatusEffectIcon(effect) {\r\n      const icons = {\r\n        bleeding: '🩸',\r\n        poisoned: '☠️',\r\n        stunned: '😵',\r\n        frightened: '😨',\r\n        blessed: '✨',\r\n        cursed: '💀',\r\n        prone: '⬇️',\r\n        grappled: '🤝',\r\n        unconscious: '😴'\r\n      }\r\n      return icons[effect] || '❓'\r\n    },\r\n    \r\n    // 获取武器图标\r\n    getWeaponIcon(weapon) {\r\n      if (!weapon) return 'fas fa-fist-raised'\r\n      \r\n      const icons = {\r\n        melee: 'fas fa-sword',\r\n        ranged: 'fas fa-crosshairs',\r\n        firearm: 'fas fa-gun',\r\n        thrown: 'fas fa-hand-paper'\r\n      }\r\n      \r\n      return icons[weapon.type] || 'fas fa-fist-raised'\r\n    },\r\n    \r\n    // 标记为已行动\r\n    markAsActed(participant) {\r\n      this.$emit('mark-acted', participant)\r\n    },\r\n    \r\n    // 标记为未行动\r\n    markAsNotActed(participant) {\r\n      this.$emit('mark-not-acted', participant)\r\n    },\r\n    \r\n    // 延迟行动\r\n    delayAction(participant, index) {\r\n      this.$emit('delay-action', participant, index)\r\n    },\r\n    \r\n    // 移除参与者\r\n    removeParticipant(participant) {\r\n      this.$emit('remove-participant', participant)\r\n    },\r\n    \r\n    // 上一个回合\r\n    previousTurn() {\r\n      this.$emit('previous-turn')\r\n    },\r\n    \r\n    // 下一个回合\r\n    nextTurn() {\r\n      this.$emit('next-turn')\r\n    },\r\n    \r\n    // 结束本轮\r\n    endRound() {\r\n      this.$emit('end-round')\r\n    },\r\n    \r\n    // 重新投掷先攻\r\n    rerollInitiative() {\r\n      this.$emit('reroll-initiative')\r\n    },\r\n    \r\n    // 重置先攻\r\n    resetInitiative() {\r\n      this.$emit('reset-initiative')\r\n    },\r\n    \r\n    // 滚动到当前回合\r\n    scrollToCurrentTurn(turnIndex) {\r\n      const list = this.$refs.initiativeList\r\n      if (!list) return\r\n      \r\n      const items = list.querySelectorAll('.initiative-item')\r\n      const currentItem = items[turnIndex]\r\n      \r\n      if (currentItem) {\r\n        currentItem.scrollIntoView({\r\n          behavior: 'smooth',\r\n          block: 'nearest'\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.initiative-tracker {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 2px solid #0f3460;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  color: #e94560;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  max-height: 600px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 追踪器头部 */\r\n.tracker-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 2px solid #0f3460;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left h3 {\r\n  margin: 0;\r\n  color: #e94560;\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.round-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  padding: 6px 12px;\r\n  border-radius: 20px;\r\n  border: 1px solid #0f3460;\r\n}\r\n\r\n.round-label {\r\n  font-size: 0.9rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.round-number {\r\n  font-size: 1.2rem;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.reroll-btn {\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 6px;\r\n  color: #e94560;\r\n  padding: 6px 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.reroll-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n/* 先攻列表 */\r\n.initiative-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 16px;\r\n  max-height: 400px;\r\n}\r\n\r\n.initiative-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  margin-bottom: 8px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 2px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.initiative-item:hover {\r\n  background: rgba(15, 52, 96, 0.5);\r\n  border-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.initiative-item.current {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n  box-shadow: 0 0 12px rgba(233, 69, 96, 0.3);\r\n}\r\n\r\n.initiative-item.acted {\r\n  opacity: 0.7;\r\n  background: rgba(39, 174, 96, 0.2);\r\n  border-color: rgba(39, 174, 96, 0.5);\r\n}\r\n\r\n.initiative-item.player {\r\n  border-left: 4px solid #3498db;\r\n}\r\n\r\n.initiative-item.enemy {\r\n  border-left: 4px solid #e74c3c;\r\n}\r\n\r\n.initiative-item.unconscious {\r\n  opacity: 0.5;\r\n  filter: grayscale(50%);\r\n}\r\n\r\n.initiative-item.delayed {\r\n  border-style: dashed;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 先攻值 */\r\n.initiative-value {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-width: 50px;\r\n}\r\n\r\n.initiative-number {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #e94560, #f27121);\r\n  color: white;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-weight: bold;\r\n  font-size: 1.1rem;\r\n  box-shadow: 0 2px 8px rgba(233, 69, 96, 0.3);\r\n}\r\n\r\n.initiative-modifier {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n  margin-top: 2px;\r\n}\r\n\r\n/* 参与者信息 */\r\n.participant-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n}\r\n\r\n.participant-avatar {\r\n  position: relative;\r\n  width: 40px;\r\n  height: 40px;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.current-indicator {\r\n  position: absolute;\r\n  top: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  background: #27ae60;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: white;\r\n  font-size: 8px;\r\n  animation: current-pulse 1s infinite;\r\n}\r\n\r\n@keyframes current-pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.2); }\r\n}\r\n\r\n.participant-details {\r\n  flex: 1;\r\n}\r\n\r\n.participant-name {\r\n  font-weight: bold;\r\n  color: #ecf0f1;\r\n  font-size: 1rem;\r\n  margin-bottom: 2px;\r\n}\r\n\r\n.participant-type {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n/* 状态信息 */\r\n.participant-status {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 4px;\r\n  min-width: 80px;\r\n}\r\n\r\n.health-indicator {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 2px;\r\n}\r\n\r\n.health-bar {\r\n  width: 60px;\r\n  height: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.health-fill {\r\n  height: 100%;\r\n  border-radius: 4px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.health-fill.healthy { background: #27ae60; }\r\n.health-fill.injured { background: #f39c12; }\r\n.health-fill.wounded { background: #e67e22; }\r\n.health-fill.critical { background: #e74c3c; }\r\n\r\n.health-text {\r\n  font-size: 0.8rem;\r\n  color: #ecf0f1;\r\n  font-weight: bold;\r\n}\r\n\r\n.status-effects {\r\n  display: flex;\r\n  gap: 2px;\r\n  flex-wrap: wrap;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.status-effect {\r\n  width: 16px;\r\n  height: 16px;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.status-effect.more {\r\n  background: rgba(233, 69, 96, 0.8);\r\n  color: white;\r\n  font-size: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 行动控制 */\r\n.action-controls {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.control-btn {\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 10px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.acted-btn {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.unacted-btn {\r\n  background: #f39c12;\r\n  color: white;\r\n}\r\n\r\n.delay-btn {\r\n  background: #3498db;\r\n  color: white;\r\n}\r\n\r\n.remove-btn {\r\n  background: #e74c3c;\r\n  color: white;\r\n}\r\n\r\n.control-btn:hover:not(:disabled) {\r\n  transform: scale(1.1);\r\n}\r\n\r\n.control-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n/* 武器信息 */\r\n.weapon-info {\r\n  position: absolute;\r\n  bottom: 2px;\r\n  right: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 0.7rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.weapon-name {\r\n  max-width: 60px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 回合控制 */\r\n.round-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 12px;\r\n  margin-bottom: 12px;\r\n  padding-top: 12px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.control-group {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.round-controls .control-btn {\r\n  padding: 8px 12px;\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 6px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  font-size: 0.9rem;\r\n  height: auto;\r\n  width: auto;\r\n}\r\n\r\n.round-controls .control-btn:hover:not(:disabled) {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.round-controls .control-btn.primary {\r\n  background: linear-gradient(135deg, #e94560, #f27121);\r\n  color: white;\r\n  border-color: #e94560;\r\n}\r\n\r\n.round-controls .control-btn.warning {\r\n  background: rgba(243, 156, 18, 0.2);\r\n  border-color: #f39c12;\r\n  color: #f39c12;\r\n}\r\n\r\n.round-controls .control-btn.danger {\r\n  background: rgba(231, 76, 60, 0.2);\r\n  border-color: #e74c3c;\r\n  color: #e74c3c;\r\n}\r\n\r\n/* 统计信息 */\r\n.tracker-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 8px 12px;\r\n  background: rgba(15, 52, 96, 0.2);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 2px;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 1rem;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .initiative-tracker {\r\n    padding: 12px;\r\n    max-height: 400px;\r\n  }\r\n  \r\n  .initiative-item {\r\n    padding: 8px;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .initiative-number {\r\n    width: 32px;\r\n    height: 32px;\r\n    font-size: 1rem;\r\n  }\r\n  \r\n  .participant-avatar {\r\n    width: 32px;\r\n    height: 32px;\r\n  }\r\n  \r\n  .participant-name {\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .health-bar {\r\n    width: 50px;\r\n    height: 6px;\r\n  }\r\n  \r\n  .round-controls {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .control-group {\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;AAwMA,eAAe;EACbA,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE;IACLC,eAAe,EAAE;MACfC,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDC,YAAY,EAAE;MACZH,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDC,WAAW,EAAE;MACXL,IAAI,EAAEI,MAAM;MACZ,WAAS;IACX,CAAC;IACDE,QAAQ,EAAE;MACRN,IAAI,EAAEO,OAAO;MACb,WAAS;IACX,CAAC;IACDC,SAAS,EAAE;MACTR,IAAI,EAAEO,OAAO;MACb,WAAS;IACX;EACF,CAAC;EAEDE,QAAQ,EAAE;IACR;IACAC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACX,eAAe,CAACY,MAAM,CAAC,UAAAC,CAAA;QAAA,OAAKA,CAAC,CAACC,QAAQ;MAAA,EAAC,CAACC,MAAK;IAC3D,CAAC;IAED;IACAC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAAChB,eAAe,CAACY,MAAM,CAAC,UAAAC,CAAA;QAAA,OAAK,CAACA,CAAC,CAACI,SAAQ,IAAK,CAAC,IAAI,CAAC;MAAA,EAAC,CAACF,MAAK;IACvE;EACF,CAAC;EAEDG,KAAK,EAAE;IACLZ,WAAW,WAAXA,WAAWA,CAACa,OAAO,EAAE;MAAA,IAAAC,KAAA;MACnB;MACA,IAAI,CAACC,SAAS,CAAC,YAAM;QACnBD,KAAI,CAACE,mBAAmB,CAACH,OAAO;MAClC,CAAC;IACH;EACF,CAAC;EAEDI,OAAO,EAAE;IACP;IACAC,iBAAiB,WAAjBA,iBAAiBA,CAACC,WAAW,EAAEC,KAAK,EAAE;MACpC,IAAI,CAACC,KAAK,CAAC,sBAAsB,EAAEF,WAAW,EAAEC,KAAK;IACvD,CAAC;IAED;IACAE,mBAAmB,WAAnBA,mBAAmBA,CAACH,WAAW,EAAE;MAC/B,IAAMI,OAAM,GAAIJ,WAAW,CAACR,SAAQ,IAAK;MACzC,IAAMa,GAAE,GAAIL,WAAW,CAACM,KAAI,IAAKN,WAAW,CAACO,SAAQ,IAAK;MAC1D,OAAOC,IAAI,CAACH,GAAG,CAAC,CAAC,EAAEG,IAAI,CAACC,GAAG,CAAC,GAAG,EAAGL,OAAM,GAAIC,GAAG,GAAI,GAAG,CAAC;IACzD,CAAC;IAED;IACAK,cAAc,WAAdA,cAAcA,CAACV,WAAW,EAAE;MAC1B,IAAMW,UAAS,GAAI,IAAI,CAACR,mBAAmB,CAACH,WAAW;MACvD,IAAIW,UAAS,GAAI,EAAE,EAAE,OAAO,SAAQ;MACpC,IAAIA,UAAS,GAAI,EAAE,EAAE,OAAO,SAAQ;MACpC,IAAIA,UAAS,GAAI,EAAE,EAAE,OAAO,SAAQ;MACpC,OAAO,UAAS;IAClB,CAAC;IAED;IACAC,qBAAqB,WAArBA,qBAAqBA,CAACZ,WAAW,EAAE;MACjC,IAAIA,WAAW,CAACa,kBAAkB,EAAE;QAClC,OAAOb,WAAW,CAACa,kBAAiB,GAAI,QAAAC,MAAA,CAChCd,WAAW,CAACa,kBAAkB,OAAAC,MAAA,CAC/Bd,WAAW,CAACa,kBAAkB,CAAC;MACxC;MACA,OAAO,IAAG;IACZ,CAAC;IAED;IACAE,mBAAmB,WAAnBA,mBAAmBA,CAACC,MAAM,EAAE;MAC1B,IAAMC,KAAI,GAAI;QACZC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE;MACf;MACA,OAAOT,KAAK,CAACD,MAAM,KAAKA,MAAK;IAC/B,CAAC;IAED;IACAW,mBAAmB,WAAnBA,mBAAmBA,CAACX,MAAM,EAAE;MAC1B,IAAMY,KAAI,GAAI;QACZV,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,GAAG;QACZC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,IAAI;QACdC,WAAW,EAAE;MACf;MACA,OAAOE,KAAK,CAACZ,MAAM,KAAK,GAAE;IAC5B,CAAC;IAED;IACAa,aAAa,WAAbA,aAAaA,CAACC,MAAM,EAAE;MACpB,IAAI,CAACA,MAAM,EAAE,OAAO,oBAAmB;MAEvC,IAAMF,KAAI,GAAI;QACZG,KAAK,EAAE,cAAc;QACrBC,MAAM,EAAE,mBAAmB;QAC3BC,OAAO,EAAE,YAAY;QACrBC,MAAM,EAAE;MACV;MAEA,OAAON,KAAK,CAACE,MAAM,CAACtD,IAAI,KAAK,oBAAmB;IAClD,CAAC;IAED;IACA2D,WAAW,WAAXA,WAAWA,CAACnC,WAAW,EAAE;MACvB,IAAI,CAACE,KAAK,CAAC,YAAY,EAAEF,WAAW;IACtC,CAAC;IAED;IACAoC,cAAc,WAAdA,cAAcA,CAACpC,WAAW,EAAE;MAC1B,IAAI,CAACE,KAAK,CAAC,gBAAgB,EAAEF,WAAW;IAC1C,CAAC;IAED;IACAqC,WAAW,WAAXA,WAAWA,CAACrC,WAAW,EAAEC,KAAK,EAAE;MAC9B,IAAI,CAACC,KAAK,CAAC,cAAc,EAAEF,WAAW,EAAEC,KAAK;IAC/C,CAAC;IAED;IACAqC,iBAAiB,WAAjBA,iBAAiBA,CAACtC,WAAW,EAAE;MAC7B,IAAI,CAACE,KAAK,CAAC,oBAAoB,EAAEF,WAAW;IAC9C,CAAC;IAED;IACAuC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACrC,KAAK,CAAC,eAAe;IAC5B,CAAC;IAED;IACAsC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,IAAI,CAACtC,KAAK,CAAC,WAAW;IACxB,CAAC;IAED;IACAuC,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,IAAI,CAACvC,KAAK,CAAC,WAAW;IACxB,CAAC;IAED;IACAwC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACxC,KAAK,CAAC,mBAAmB;IAChC,CAAC;IAED;IACAyC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAACzC,KAAK,CAAC,kBAAkB;IAC/B,CAAC;IAED;IACAL,mBAAmB,WAAnBA,mBAAmBA,CAAC+C,SAAS,EAAE;MAC7B,IAAMC,IAAG,GAAI,IAAI,CAACC,KAAK,CAACC,cAAa;MACrC,IAAI,CAACF,IAAI,EAAE;MAEX,IAAMG,KAAI,GAAIH,IAAI,CAACI,gBAAgB,CAAC,kBAAkB;MACtD,IAAMC,WAAU,GAAIF,KAAK,CAACJ,SAAS;MAEnC,IAAIM,WAAW,EAAE;QACfA,WAAW,CAACC,cAAc,CAAC;UACzBC,QAAQ,EAAE,QAAQ;UAClBC,KAAK,EAAE;QACT,CAAC;MACH;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}