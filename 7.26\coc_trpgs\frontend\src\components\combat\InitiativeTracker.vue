<template>
  <div class="initiative-tracker">
    <!-- 追踪器头部 -->
    <div class="tracker-header">
      <div class="header-left">
        <i class="fas fa-list-ol"></i>
        <h3>先攻顺序</h3>
      </div>
      <div class="header-right">
        <div class="round-info">
          <span class="round-label">第</span>
          <span class="round-number">{{ currentRound }}</span>
          <span class="round-label">轮</span>
        </div>
        <button 
          v-if="canReroll" 
          @click="rerollInitiative" 
          class="reroll-btn"
          title="重新投掷先攻"
        >
          <i class="fas fa-dice"></i>
        </button>
      </div>
    </div>

    <!-- 先攻列表 -->
    <div class="initiative-list" ref="initiativeList">
      <div 
        v-for="(participant, index) in initiativeOrder"
        :key="participant.id"
        class="initiative-item"
        :class="{ 
          current: index === currentTurn,
          acted: participant.hasActed,
          player: participant.isPlayer,
          enemy: !participant.isPlayer,
          unconscious: participant.currentHP <= 0,
          delayed: participant.delayedAction
        }"
        @click="selectParticipant(participant, index)"
      >
        <!-- 先攻值 -->
        <div class="initiative-value">
          <div class="initiative-number">{{ participant.initiative }}</div>
          <div class="initiative-modifier" v-if="getInitiativeModifier(participant)">
            {{ getInitiativeModifier(participant) }}
          </div>
        </div>

        <!-- 参与者信息 -->
        <div class="participant-info">
          <div class="participant-avatar">
            <img 
              :src="participant.avatar || '/default-avatar.png'" 
              :alt="participant.name"
              class="avatar-image"
            >
            <!-- 当前回合指示器 -->
            <div v-if="index === currentTurn" class="current-indicator">
              <i class="fas fa-play"></i>
            </div>
          </div>
          
          <div class="participant-details">
            <div class="participant-name">{{ participant.name }}</div>
            <div class="participant-type">
              {{ participant.isPlayer ? '玩家' : (participant.type || 'NPC') }}
            </div>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="participant-status">
          <!-- 生命值指示器 -->
          <div class="health-indicator">
            <div class="health-bar">
              <div 
                class="health-fill" 
                :class="getHealthClass(participant)"
                :style="{ width: getHealthPercentage(participant) + '%' }"
              ></div>
            </div>
            <div class="health-text">
              {{ participant.currentHP || 0 }}/{{ participant.maxHP || participant.hitPoints || 0 }}
            </div>
          </div>

          <!-- 状态效果 -->
          <div class="status-effects" v-if="participant.conditions && participant.conditions.length > 0">
            <div 
              v-for="effect in participant.conditions.slice(0, 3)" 
              :key="effect"
              class="status-effect"
              :class="effect"
              :title="getStatusEffectName(effect)"
            >
              {{ getStatusEffectIcon(effect) }}
            </div>
            <div 
              v-if="participant.conditions.length > 3"
              class="status-effect more"
              :title="`还有${participant.conditions.length - 3}个状态效果`"
            >
              +{{ participant.conditions.length - 3 }}
            </div>
          </div>
        </div>

        <!-- 行动控制 (仅KP可见) -->
        <div class="action-controls" v-if="isKeeper">
          <button 
            v-if="!participant.hasActed && index === currentTurn"
            @click.stop="markAsActed(participant)"
            class="control-btn acted-btn"
            title="标记为已行动"
          >
            <i class="fas fa-check"></i>
          </button>
          
          <button 
            v-if="participant.hasActed"
            @click.stop="markAsNotActed(participant)"
            class="control-btn unacted-btn"
            title="标记为未行动"
          >
            <i class="fas fa-undo"></i>
          </button>
          
          <button 
            @click.stop="delayAction(participant, index)"
            class="control-btn delay-btn"
            title="延迟行动"
            :disabled="participant.delayedAction"
          >
            <i class="fas fa-clock"></i>
          </button>
          
          <button 
            @click.stop="removeParticipant(participant)"
            class="control-btn remove-btn"
            title="移除参与者"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>

        <!-- 武器信息 -->
        <div class="weapon-info" v-if="participant.currentWeapon">
          <i :class="getWeaponIcon(participant.currentWeapon)"></i>
          <span class="weapon-name">{{ participant.currentWeapon.name }}</span>
        </div>
      </div>
    </div>

    <!-- 回合控制 (仅KP可见) -->
    <div class="round-controls" v-if="isKeeper">
      <div class="control-group">
        <button @click="previousTurn" class="control-btn" :disabled="currentTurn === 0">
          <i class="fas fa-step-backward"></i>
          <span>上一个</span>
        </button>
        
        <button @click="nextTurn" class="control-btn primary">
          <i class="fas fa-step-forward"></i>
          <span>下一个</span>
        </button>
      </div>
      
      <div class="control-group">
        <button @click="endRound" class="control-btn warning">
          <i class="fas fa-refresh"></i>
          <span>结束本轮</span>
        </button>
        
        <button @click="resetInitiative" class="control-btn danger">
          <i class="fas fa-redo"></i>
          <span>重置先攻</span>
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="tracker-stats">
      <div class="stat-item">
        <span class="stat-label">参与者:</span>
        <span class="stat-value">{{ initiativeOrder.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已行动:</span>
        <span class="stat-value">{{ actedCount }}/{{ initiativeOrder.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">存活:</span>
        <span class="stat-value">{{ aliveCount }}/{{ initiativeOrder.length }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InitiativeTracker',
  props: {
    initiativeOrder: {
      type: Array,
      default: () => []
    },
    currentRound: {
      type: Number,
      default: 1
    },
    currentTurn: {
      type: Number,
      default: 0
    },
    isKeeper: {
      type: Boolean,
      default: false
    },
    canReroll: {
      type: Boolean,
      default: true
    }
  },
  
  computed: {
    // 已行动的参与者数量
    actedCount() {
      return this.initiativeOrder.filter(p => p.hasActed).length
    },
    
    // 存活的参与者数量
    aliveCount() {
      return this.initiativeOrder.filter(p => (p.currentHP || 0) > 0).length
    }
  },
  
  watch: {
    currentTurn(newTurn) {
      // 自动滚动到当前回合
      this.$nextTick(() => {
        this.scrollToCurrentTurn(newTurn)
      })
    }
  },
  
  methods: {
    // 选择参与者
    selectParticipant(participant, index) {
      this.$emit('participant-selected', participant, index)
    },
    
    // 获取生命值百分比
    getHealthPercentage(participant) {
      const current = participant.currentHP || 0
      const max = participant.maxHP || participant.hitPoints || 1
      return Math.max(0, Math.min(100, (current / max) * 100))
    },
    
    // 获取生命值样式类
    getHealthClass(participant) {
      const percentage = this.getHealthPercentage(participant)
      if (percentage > 75) return 'healthy'
      if (percentage > 50) return 'injured'
      if (percentage > 25) return 'wounded'
      return 'critical'
    },
    
    // 获取先攻修正值显示
    getInitiativeModifier(participant) {
      if (participant.initiativeModifier) {
        return participant.initiativeModifier > 0 
          ? `+${participant.initiativeModifier}` 
          : `${participant.initiativeModifier}`
      }
      return null
    },
    
    // 获取状态效果名称
    getStatusEffectName(effect) {
      const names = {
        bleeding: '流血',
        poisoned: '中毒',
        stunned: '眩晕',
        frightened: '恐惧',
        blessed: '祝福',
        cursed: '诅咒',
        prone: '倒地',
        grappled: '被擒抱',
        unconscious: '昏迷'
      }
      return names[effect] || effect
    },
    
    // 获取状态效果图标
    getStatusEffectIcon(effect) {
      const icons = {
        bleeding: '🩸',
        poisoned: '☠️',
        stunned: '😵',
        frightened: '😨',
        blessed: '✨',
        cursed: '💀',
        prone: '⬇️',
        grappled: '🤝',
        unconscious: '😴'
      }
      return icons[effect] || '❓'
    },
    
    // 获取武器图标
    getWeaponIcon(weapon) {
      if (!weapon) return 'fas fa-fist-raised'
      
      const icons = {
        melee: 'fas fa-sword',
        ranged: 'fas fa-crosshairs',
        firearm: 'fas fa-gun',
        thrown: 'fas fa-hand-paper'
      }
      
      return icons[weapon.type] || 'fas fa-fist-raised'
    },
    
    // 标记为已行动
    markAsActed(participant) {
      this.$emit('mark-acted', participant)
    },
    
    // 标记为未行动
    markAsNotActed(participant) {
      this.$emit('mark-not-acted', participant)
    },
    
    // 延迟行动
    delayAction(participant, index) {
      this.$emit('delay-action', participant, index)
    },
    
    // 移除参与者
    removeParticipant(participant) {
      this.$emit('remove-participant', participant)
    },
    
    // 上一个回合
    previousTurn() {
      this.$emit('previous-turn')
    },
    
    // 下一个回合
    nextTurn() {
      this.$emit('next-turn')
    },
    
    // 结束本轮
    endRound() {
      this.$emit('end-round')
    },
    
    // 重新投掷先攻
    rerollInitiative() {
      this.$emit('reroll-initiative')
    },
    
    // 重置先攻
    resetInitiative() {
      this.$emit('reset-initiative')
    },
    
    // 滚动到当前回合
    scrollToCurrentTurn(turnIndex) {
      const list = this.$refs.initiativeList
      if (!list) return
      
      const items = list.querySelectorAll('.initiative-item')
      const currentItem = items[turnIndex]
      
      if (currentItem) {
        currentItem.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest'
        })
      }
    }
  }
}
</script>

<style scoped>
.initiative-tracker {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #0f3460;
  border-radius: 12px;
  padding: 16px;
  color: #e94560;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  max-height: 600px;
  display: flex;
  flex-direction: column;
}

/* 追踪器头部 */
.tracker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #0f3460;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-left h3 {
  margin: 0;
  color: #e94560;
  font-size: 1.2rem;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.round-info {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(15, 52, 96, 0.3);
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid #0f3460;
}

.round-label {
  font-size: 0.9rem;
  color: #bdc3c7;
}

.round-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: #e94560;
  min-width: 20px;
  text-align: center;
}

.reroll-btn {
  background: rgba(15, 52, 96, 0.8);
  border: 1px solid #0f3460;
  border-radius: 6px;
  color: #e94560;
  padding: 6px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reroll-btn:hover {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
}

/* 先攻列表 */
.initiative-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  max-height: 400px;
}

.initiative-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: rgba(15, 52, 96, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.initiative-item:hover {
  background: rgba(15, 52, 96, 0.5);
  border-color: rgba(255, 255, 255, 0.2);
}

.initiative-item.current {
  background: rgba(233, 69, 96, 0.3);
  border-color: #e94560;
  box-shadow: 0 0 12px rgba(233, 69, 96, 0.3);
}

.initiative-item.acted {
  opacity: 0.7;
  background: rgba(39, 174, 96, 0.2);
  border-color: rgba(39, 174, 96, 0.5);
}

.initiative-item.player {
  border-left: 4px solid #3498db;
}

.initiative-item.enemy {
  border-left: 4px solid #e74c3c;
}

.initiative-item.unconscious {
  opacity: 0.5;
  filter: grayscale(50%);
}

.initiative-item.delayed {
  border-style: dashed;
  opacity: 0.8;
}

/* 先攻值 */
.initiative-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
}

.initiative-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #e94560, #f27121);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  box-shadow: 0 2px 8px rgba(233, 69, 96, 0.3);
}

.initiative-modifier {
  font-size: 0.8rem;
  color: #bdc3c7;
  margin-top: 2px;
}

/* 参与者信息 */
.participant-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.participant-avatar {
  position: relative;
  width: 40px;
  height: 40px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.current-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: #27ae60;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 8px;
  animation: current-pulse 1s infinite;
}

@keyframes current-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.participant-details {
  flex: 1;
}

.participant-name {
  font-weight: bold;
  color: #ecf0f1;
  font-size: 1rem;
  margin-bottom: 2px;
}

.participant-type {
  font-size: 0.8rem;
  color: #bdc3c7;
}

/* 状态信息 */
.participant-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  min-width: 80px;
}

.health-indicator {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.health-bar {
  width: 60px;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.health-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.health-fill.healthy { background: #27ae60; }
.health-fill.injured { background: #f39c12; }
.health-fill.wounded { background: #e67e22; }
.health-fill.critical { background: #e74c3c; }

.health-text {
  font-size: 0.8rem;
  color: #ecf0f1;
  font-weight: bold;
}

.status-effects {
  display: flex;
  gap: 2px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.status-effect {
  width: 16px;
  height: 16px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-effect.more {
  background: rgba(233, 69, 96, 0.8);
  color: white;
  font-size: 8px;
  font-weight: bold;
}

/* 行动控制 */
.action-controls {
  display: flex;
  gap: 4px;
}

.control-btn {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.3s ease;
}

.acted-btn {
  background: #27ae60;
  color: white;
}

.unacted-btn {
  background: #f39c12;
  color: white;
}

.delay-btn {
  background: #3498db;
  color: white;
}

.remove-btn {
  background: #e74c3c;
  color: white;
}

.control-btn:hover:not(:disabled) {
  transform: scale(1.1);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 武器信息 */
.weapon-info {
  position: absolute;
  bottom: 2px;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.7rem;
  color: #bdc3c7;
}

.weapon-name {
  max-width: 60px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 回合控制 */
.round-controls {
  display: flex;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.control-group {
  display: flex;
  gap: 8px;
}

.round-controls .control-btn {
  padding: 8px 12px;
  background: rgba(15, 52, 96, 0.8);
  border: 1px solid #0f3460;
  border-radius: 6px;
  color: #e94560;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  height: auto;
  width: auto;
}

.round-controls .control-btn:hover:not(:disabled) {
  background: rgba(233, 69, 96, 0.2);
  border-color: #e94560;
  transform: translateY(-1px);
}

.round-controls .control-btn.primary {
  background: linear-gradient(135deg, #e94560, #f27121);
  color: white;
  border-color: #e94560;
}

.round-controls .control-btn.warning {
  background: rgba(243, 156, 18, 0.2);
  border-color: #f39c12;
  color: #f39c12;
}

.round-controls .control-btn.danger {
  background: rgba(231, 76, 60, 0.2);
  border-color: #e74c3c;
  color: #e74c3c;
}

/* 统计信息 */
.tracker-stats {
  display: flex;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(15, 52, 96, 0.2);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.stat-label {
  font-size: 0.8rem;
  color: #bdc3c7;
}

.stat-value {
  font-size: 1rem;
  font-weight: bold;
  color: #e94560;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .initiative-tracker {
    padding: 12px;
    max-height: 400px;
  }
  
  .initiative-item {
    padding: 8px;
    gap: 8px;
  }
  
  .initiative-number {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
  
  .participant-avatar {
    width: 32px;
    height: 32px;
  }
  
  .participant-name {
    font-size: 0.9rem;
  }
  
  .health-bar {
    width: 50px;
    height: 6px;
  }
  
  .round-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .control-group {
    justify-content: center;
  }
}
</style>