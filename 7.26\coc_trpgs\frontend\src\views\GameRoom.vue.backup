<template>
  <div class="game-room">
    <!-- 房间头部信息 -->
    <div class="room-header">
      <div class="room-info">
        <div class="room-title">
          <i class="fas fa-door-open"></i>
          <h1>{{ roomData.name || '游戏房间' }}</h1>
          <div class="room-status" :class="roomData.status">
            <span class="status-dot"></span>
            <span>{{ getStatusText() }}</span>
          </div>
        </div>
        <div class="room-meta">
          <span class="room-creator">
            <i class="fas fa-crown"></i>
            KP: {{ roomData.creator?.username || '未知' }}
          </span>
          <span class="room-players">
            <i class="fas fa-users"></i>
            玩家: {{ currentPlayers }}/{{ roomData.maxPlayers || 6 }}
          </span>
        </div>
      </div>
      
      <div class="room-controls">
        <button @click="toggleFullscreen" class="control-btn" title="全屏模式">
          <i class="fas fa-expand"></i>
        </button>
        <button @click="toggleSettings" class="control-btn" title="房间设置">
          <i class="fas fa-cog"></i>
        </button>
        <button @click="leaveRoom" class="control-btn leave-btn" title="离开房间">
          <i class="fas fa-sign-out-alt"></i>
        </button>
      </div>
    </div>

    <!-- 三栏布局主体 -->
    <div class="game-layout">
      <!-- 左侧面板：角色信息 -->
      <div class="left-panel" :class="{ 'collapsed': leftPanelCollapsed }">
        <div class="panel-header">
          <div class="panel-title">
            <i class="fas fa-users"></i>
            <span>角色信息</span>
          </div>
          <button @click="toggleLeftPanel" class="panel-toggle">
            <i :class="leftPanelCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
          </button>
        </div>
        
        <div class="panel-content" v-show="!leftPanelCollapsed">
          <!-- 角色列表 -->
          <div class="character-list">
            <div v-for="player in players" :key="player.id" class="character-card">
              <div class="character-avatar">
                <img :src="player.avatar || defaultAvatar" :alt="player.username" />
                <div class="character-status" :class="player.status"></div>
              </div>
              <div class="character-info">
                <div class="character-name">{{ player.characterName || player.username }}</div>
                <div class="character-role" :class="{ 'kp': player.isKP }">
                  {{ player.isKP ? 'KP' : 'PL' }}
                </div>
                <div class="character-stats" v-if="player.stats">
                  <div class="stat-item">
                    <span class="stat-label">HP:</span>
                    <div class="stat-bar">
                      <div class="stat-fill hp" :style="{ width: getStatPercentage(player.stats.hp, player.stats.maxHp) + '%' }"></div>
                    </div>
                    <span class="stat-value">{{ player.stats.hp }}/{{ player.stats.maxHp }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">SAN:</span>
                    <div class="stat-bar">
                      <div class="stat-fill san" :style="{ width: getStatPercentage(player.stats.san, player.stats.maxSan) + '%' }"></div>
                    </div>
                    <span class="stat-value">{{ player.stats.san }}/{{ player.stats.maxSan }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 快速操作 -->
          <div class="quick-actions">
            <button @click="openCharacterSheet" class="action-btn">
              <i class="fas fa-id-card"></i>
              <span>角色卡</span>
            </button>
            <button @click="openDiceRoller" class="action-btn">
              <i class="fas fa-dice"></i>
              <span>投骰</span>
            </button>
            <button @click="openInventory" class="action-btn">
              <i class="fas fa-backpack"></i>
              <span>背包</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 中央面板：游戏场景 -->
      <div class="center-panel">
        <div class="scene-tabs">
          <button 
            v-for="tab in sceneTabs" 
            :key="tab.id"
            @click="activeSceneTab = tab.id"
            class="scene-tab"
            :class="{ 'active': activeSceneTab === tab.id }"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.name }}</span>
            <span v-if="tab.badge" class="tab-badge">{{ tab.badge }}</span>
          </button>
        </div>
        
        <div class="scene-content">
          <!-- 地图场景 -->
          <div v-show="activeSceneTab === 'map'" class="scene-panel map-scene">
            <div class="map-container">
              <div class="map-placeholder">
                <i class="fas fa-map"></i>
                <h3>地图系统</h3>
                <p>地图和战斗场景将在这里显示</p>
                <button class="upload-map-btn">
                  <i class="fas fa-upload"></i>
                  <span>上传地图</span>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 线索墙 -->
          <div v-show="activeSceneTab === 'clues'" class="scene-panel clues-scene">
            <div class="clues-header">
              <h3>线索墙</h3>
              <button v-if="isKP" @click="addClue" class="add-btn">
                <i class="fas fa-plus"></i>
                <span>添加线索</span>
              </button>
            </div>
            <div class="clues-grid">
              <div v-for="clue in clues" :key="clue.id" class="clue-card">
                <div class="clue-header">
                  <h4 class="clue-title">{{ clue.title }}</h4>
                  <span class="clue-type" :class="clue.type">{{ clue.type }}</span>
                </div>
                <p class="clue-content">{{ clue.content }}</p>
                <div class="clue-footer">
                  <span class="clue-discoverer">发现者：{{ clue.discoverer }}</span>
                  <span class="clue-time">{{ formatTime(clue.createdAt) }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 笔记区域 -->
          <div v-show="activeSceneTab === 'notes'" class="scene-panel notes-scene">
            <div class="notes-header">
              <h3>笔记本</h3>
              <div class="notes-controls">
                <button @click="addNote" class="add-btn">
                  <i class="fas fa-plus"></i>
                  <span>新建笔记</span>
                </button>
                <select v-model="noteFilter" class="note-filter">
                  <option value="all">全部笔记</option>
                  <option value="personal">个人笔记</option>
                  <option value="shared">共享笔记</option>
                </select>
              </div>
            </div>
            <div class="notes-list">
              <div v-for="note in filteredNotes" :key="note.id" class="note-card">
                <div class="note-header">
                  <h4 class="note-title">{{ note.title }}</h4>
                  <div class="note-meta">
                    <span class="note-author">{{ note.author }}</span>
                    <span class="note-time">{{ formatTime(note.updatedAt) }}</span>
                  </div>
                </div>
                <div class="note-content">{{ note.content }}</div>
                <div class="note-actions">
                  <button @click="editNote(note)" class="note-action-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="shareNote(note)" class="note-action-btn">
                    <i class="fas fa-share"></i>
                  </button>
                  <button @click="deleteNote(note)" class="note-action-btn delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧面板：聊天系统 -->
      <div class="right-panel" :class="{ 'collapsed': rightPanelCollapsed }">
        <div class="panel-header">
          <div class="panel-title">
            <i class="fas fa-comments"></i>
            <span>聊天</span>
          </div>
          <div class="chat-controls">
            <button 
              @click="chatMode = 'ic'" 
              class="chat-mode-btn" 
              :class="{ 'active': chatMode === 'ic' }"
            >
              IC
            </button>
            <button 
              @click="chatMode = 'ooc'" 
              class="chat-mode-btn" 
              :class="{ 'active': chatMode === 'ooc' }"
            >
              OOC
            </button>
          </div>
          <button @click="toggleRightPanel" class="panel-toggle">
            <i :class="rightPanelCollapsed ? 'fas fa-chevron-left' : 'fas fa-chevron-right'"></i>
          </button>
        </div>
        
        <div class="panel-content" v-show="!rightPanelCollapsed">
          <ChatBox 
            :messages="messages"
            :current-user="currentUser"
            :chat-mode="chatMode"
            @send-message="sendMessage"
            @roll-dice="handleDiceRoll"
          />
        </div>
      </div>
    </div>

    <!-- 底部工具栏 -->
    <div class="bottom-toolbar">
      <div class="toolbar-left">
        <div class="audio-controls">
          <button @click="toggleMute" class="audio-btn" :class="{ 'muted': isMuted }">
            <i :class="isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
          </button>
          <button @click="toggleDeafen" class="audio-btn" :class="{ 'deafened': isDeafened }">
            <i :class="isDeafened ? 'fas fa-volume-mute' : 'fas fa-volume-up'"></i>
          </button>
          <div class="volume-control">
            <input type="range" v-model="volume" min="0" max="100" class="volume-slider" />
          </div>
        </div>
      </div>
      
      <div class="toolbar-center">
        <button @click="quickRoll" class="toolbar-btn primary">
          <i class="fas fa-dice-d20"></i>
          <span>快速投骰</span>
        </button>
        <button @click="openDiceRoller" class="toolbar-btn">
          <i class="fas fa-dice"></i>
          <span>骰子面板</span>
        </button>
      </div>
      
      <div class="toolbar-right">
        <div class="connection-status">
          <div class="status-indicator" :class="connectionStatus">
            <i :class="getConnectionIcon()"></i>
          </div>
          <span class="status-text">{{ getConnectionText() }}</span>
        </div>
      </div>
    </div>

    <!-- 浮动组件 -->
    <DiceRoller 
      v-if="showDiceRoller" 
      @close="showDiceRoller = false"
      @roll="handleDiceRoll"
      @skill-check="handleSkillCheck"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChatBox from '@/components/ChatBox.vue'
import DiceRoller from '@/components/DiceRoller.vue'
import { storageMixin } from '@/mixins/storageMixin'

export default {
  name: 'GameRoom',
  mixins: [storageMixin],
  components: {
    ChatBox,
    DiceRoller
  },
  props: {
    roomId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // 房间数据
      roomData: {
        name: '神秘的古宅',
        status: 'active',
        creator: { username: 'KP_Master' },
        maxPlayers: 6
      },
      
      // 面板状态
      leftPanelCollapsed: false,
      rightPanelCollapsed: false,
      activeSceneTab: 'map',
      
      // 聊天相关
      chatMode: 'ic', // ic (in-character) or ooc (out-of-character)
      messages: [],
      
      // 玩家数据
      players: [
        {
          id: 1,
          username: 'KP_Master',
          characterName: 'KP',
          isKP: true,
          status: 'online',
          avatar: null
        },
        {
          id: 2,
          username: 'Player1',
          characterName: '侦探约翰',
          isKP: false,
          status: 'online',
          avatar: null,
          stats: {
            hp: 85,
            maxHp: 100,
            san: 72,
            maxSan: 80
          }
        }
      ],
      
      // 场景数据
      sceneTabs: [
        { id: 'map', name: '地图', icon: 'fas fa-map', badge: null },
        { id: 'clues', name: '线索', icon: 'fas fa-search', badge: 3 },
        { id: 'notes', name: '笔记', icon: 'fas fa-sticky-note', badge: null }
      ],
      
      clues: [
        {
          id: 1,
          title: '神秘的日记',
          content: '在废弃的房屋中发现了一本日记，记录着奇怪的仪式...',
          type: 'important',
          discoverer: 'Player1',
          createdAt: new Date()
        }
      ],
      
      notes: [
        {
          id: 1,
          title: '调查笔记',
          content: '今天的调查发现了一些线索...',
          author: 'Player1',
          shared: false,
          updatedAt: new Date()
        }
      ],
      
      noteFilter: 'all',
      
      // 音频控制
      isMuted: false,
      isDeafened: false,
      volume: 50,
      
      // 连接状态
      connectionStatus: 'connected', // connected, connecting, disconnected
      
      // 组件状态
      showDiceRoller: false,
      defaultAvatar: '/images/default-avatar.png'
    }
  },
  computed: {
    ...mapGetters(['currentUser']),
    
    currentPlayers() {
      return this.players.length
    },
    
    isKP() {
      return this.currentUser?.isKP || false
    },
    
    filteredNotes() {
      if (this.noteFilter === 'all') return this.notes
      if (this.noteFilter === 'personal') return this.notes.filter(note => note.author === this.currentUser?.username)
      if (this.noteFilter === 'shared') return this.notes.filter(note => note.shared)
      return this.notes
    }
  },
  created() {
    this.initializeRoom()
    this.setupWebSocket()
    this.restorePanelStates()
  },
  beforeUnmount() {
    this.savePanelStates()
    this.cleanupWebSocket()
  },
  methods: {
    // 初始化房间
    initializeRoom() {
      // 模拟加载房间数据
      this.messages = [
        {
          id: 1,
          username: 'KP_Master',
          content: '欢迎来到游戏房间！',
          type: 'system',
          timestamp: new Date(),
          avatar: null
        }
      ]
    },
    
    // WebSocket 相关
    setupWebSocket() {
      console.log('设置WebSocket连接')
    },
    
    cleanupWebSocket() {
      console.log('清理WebSocket连接')
    },
    
    // 面板控制
    toggleLeftPanel() {
      this.leftPanelCollapsed = !this.leftPanelCollapsed
    },
    
    toggleRightPanel() {
      this.rightPanelCollapsed = !this.rightPanelCollapsed
    },
    
    // 房间控制
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    
    toggleSettings() {
      console.log('打开设置')
    },
    
    leaveRoom() {
      if (confirm('确定要离开房间吗？')) {
        this.$router.push('/')
      }
    },
    
    // 音频控制
    toggleMute() {
      this.isMuted = !this.isMuted
    },
    
    toggleDeafen() {
      this.isDeafened = !this.isDeafened
    },
    
    // 快速操作
    openCharacterSheet() {
      console.log('打开角色卡')
    },
    
    openDiceRoller() {
      this.showDiceRoller = true
    },
    
    openInventory() {
      console.log('打开背包')
    },
    
    quickRoll() {
      const result = Math.floor(Math.random() * 100) + 1
      this.handleDiceRoll({
        total: result,
        results: [result],
        description: '1D100',
        timestamp: Date.now()
      })
    },
    
    // 聊天相关
    sendMessage(message) {
      const newMessage = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名用户',
        content: message,
        type: this.chatMode,
        timestamp: new Date(),
        avatar: this.currentUser?.avatar
      }
      
      this.messages.push(newMessage)
    },
    
    handleDiceRoll(rollResult) {
      const message = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名用户',
        content: `投掷 ${rollResult.description}: ${rollResult.total}`,
        type: 'dice',
        timestamp: new Date(),
        avatar: this.currentUser?.avatar,
        rollData: rollResult
      }
      
      this.messages.push(message)
    },
    
    handleSkillCheck(checkResult) {
      const message = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名用户',
        content: `技能检定: ${checkResult.diceResult}/${checkResult.effectiveSkill} - ${checkResult.level}`,
        type: 'skill-check',
        timestamp: new Date(),
        avatar: this.currentUser?.avatar,
        checkData: checkResult
      }
      
      this.messages.push(message)
    },
    
    // 线索和笔记管理
    addClue() {
      console.log('添加线索')
    },
    
    addNote() {
      console.log('添加笔记')
    },
    
    editNote(note) {
      console.log('编辑笔记:', note)
    },
    
    shareNote(note) {
      console.log('分享笔记:', note)
    },
    
    deleteNote(note) {
      console.log('删除笔记:', note)
    },
    
    // 工具函数
    getStatusText() {
      const statusMap = {
        'active': '进行中',
        'waiting': '等待中',
        'paused': '已暂停',
        'ended': '已结束'
      }
      return statusMap[this.roomData.status] || '未知'
    },
    
    getStatPercentage(current, max) {
      return max > 0 ? Math.round((current / max) * 100) : 0
    },
    
    getConnectionIcon() {
      const iconMap = {
        'connected': 'fas fa-wifi',
        'connecting': 'fas fa-spinner fa-spin',
        'disconnected': 'fas fa-wifi-slash'
      }
      return iconMap[this.connectionStatus] || 'fas fa-question'
    },
    
    getConnectionText() {
      const textMap = {
        'connected': '已连接',
        'connecting': '连接中',
        'disconnected': '已断开'
      }
      return textMap[this.connectionStatus] || '未知'
    },
    
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },
    
    // 状态保存和恢复
    savePanelStates() {
      const states = {
        leftPanelCollapsed: this.leftPanelCollapsed,
        rightPanelCollapsed: this.rightPanelCollapsed,
        activeSceneTab: this.activeSceneTab,
        chatMode: this.chatMode
      }
      this.safeSetJSON(`gameroom_${this.roomId}_panels`, states)
    },
    
    restorePanelStates() {
      const states = this.safeGetJSON(`gameroom_${this.roomId}_panels`)
      if (states) {
        try {
          this.leftPanelCollapsed = states.leftPanelCollapsed || false
          this.rightPanelCollapsed = states.rightPanelCollapsed || false
          this.activeSceneTab = states.activeSceneTab || 'map'
          this.chatMode = states.chatMode || 'ic'
        } catch (error) {
          console.warn('恢复面板状态失败:', error)
        }
      }
    }
  }
}
</script><style 
scoped>
/* ===== 游戏房间基础样式 ===== */
.game-room {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--success-50) 100%);
  position: relative;
  overflow: hidden;
}

.game-room::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="game-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="%2322c55e" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23game-pattern)"/></svg>') repeat;
  pointer-events: none;
  z-index: 1;
}

.game-room > * {
  position: relative;
  z-index: 2;
}

/* ===== 房间头部 ===== */
.room-header {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-lg);
  border-bottom: 2px solid var(--success-400);
  position: relative;
  overflow: hidden;
}

.room-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  animation: header-shine 3s ease-in-out infinite;
}

@keyframes header-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.room-info {
  flex: 1;
}

.room-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-2);
}

.room-title i {
  font-size: var(--font-size-xl);
  color: var(--success-200);
  animation: door-glow 2s ease-in-out infinite alternate;
}

@keyframes door-glow {
  from { opacity: 0.8; }
  to { opacity: 1; }
}

.room-title h1 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.room-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background: rgba(255, 255, 255, 0.15);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  backdrop-filter: blur(5px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  animation: status-pulse 2s ease-in-out infinite;
}

.room-status.active .status-dot {
  background: var(--success-300);
}

.room-status.waiting .status-dot {
  background: var(--warning-300);
}

.room-status.paused .status-dot {
  background: var(--error-300);
}

@keyframes status-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.room-meta {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  opacity: 0.9;
}

.room-creator,
.room-players {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.room-creator i {
  color: var(--warning-300);
}

.room-players i {
  color: var(--success-200);
}.
room-controls {
  display: flex;
  gap: var(--spacing-2);
}

.control-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  backdrop-filter: blur(5px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.control-btn.leave-btn:hover {
  background: rgba(239, 68, 68, 0.8);
  border-color: rgba(239, 68, 68, 0.6);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* ===== 游戏布局 ===== */
.game-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* ===== 面板通用样式 ===== */
.left-panel,
.right-panel {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  border: 1px solid var(--success-200);
  display: flex;
  flex-direction: column;
  transition: all var(--transition-fast);
  backdrop-filter: blur(10px);
}

.left-panel {
  width: 300px;
  border-right: 2px solid var(--success-200);
}

.right-panel {
  width: 350px;
  border-left: 2px solid var(--success-200);
}

.left-panel.collapsed {
  width: 60px;
}

.right-panel.collapsed {
  width: 60px;
}

.panel-header {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  color: var(--text-inverse);
  padding: var(--spacing-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-sm);
  position: relative;
}

.panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-300) 100%);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-base);
}

.panel-title i {
  font-size: var(--font-size-lg);
}

.panel-toggle {
  width: 28px;
  height: 28px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.panel-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.panel-content {
  flex: 1;
  padding: var(--spacing-4);
  overflow-y: auto;
  background: var(--bg-primary);
}

.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: var(--success-100);
  border-radius: var(--radius-full);
}

.panel-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--success-400) 0%, var(--success-500) 100%);
  border-radius: var(--radius-full);
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--success-500) 0%, var(--success-600) 100%);
}/* 
===== 角色列表 ===== */
.character-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-4);
}

.character-card {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-3);
  display: flex;
  gap: var(--spacing-3);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.character-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
  transition: left var(--transition-fast);
}

.character-card:hover {
  border-color: var(--success-300);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.character-card:hover::before {
  left: 100%;
}

.character-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 2px solid var(--success-300);
  flex-shrink: 0;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-status {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-primary);
}

.character-status.online {
  background: var(--success-500);
}

.character-status.away {
  background: var(--warning-500);
}

.character-status.offline {
  background: var(--error-500);
}

.character-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.character-name {
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.character-role {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  background: var(--success-100);
  align-self: flex-start;
}

.character-role.kp {
  background: var(--warning-100);
  color: var(--warning-700);
}

.character-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  margin-top: var(--spacing-1);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
}

.stat-label {
  min-width: 30px;
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
}

.stat-bar {
  flex: 1;
  height: 6px;
  background: var(--success-100);
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 1px solid var(--success-200);
}

.stat-fill {
  height: 100%;
  border-radius: var(--radius-full);
  transition: width var(--transition-fast);
}

.stat-fill.hp {
  background: linear-gradient(90deg, var(--success-500) 0%, var(--success-600) 100%);
}

.stat-fill.san {
  background: linear-gradient(90deg, var(--info-500) 0%, var(--info-600) 100%);
}

.stat-value {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  min-width: 40px;
  text-align: right;
}

/* ===== 快速操作 ===== */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-2);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-3);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-xs);
  text-align: center;
}

.action-btn:hover {
  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);
  color: var(--success-700);
  border-color: var(--success-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn i {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-1);
}

.action-btn span {
  font-weight: var(--font-weight-medium);
}/* 
===== 中央面板 ===== */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
  border-left: 1px solid var(--success-200);
  border-right: 1px solid var(--success-200);
}

.scene-tabs {
  display: flex;
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  border-bottom: 2px solid var(--success-200);
  padding: var(--spacing-2);
  gap: var(--spacing-1);
}

.scene-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
}

.scene-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
  transition: left var(--transition-fast);
}

.scene-tab:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
}

.scene-tab:hover::before {
  left: 100%;
}

.scene-tab.active {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  color: var(--text-inverse);
  border-color: var(--success-500);
  box-shadow: var(--shadow-md);
}

.scene-tab i {
  font-size: var(--font-size-base);
}

.tab-badge {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  min-width: 20px;
  text-align: center;
}

.scene-tab:not(.active) .tab-badge {
  background: var(--success-200);
  color: var(--success-700);
}

.scene-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-5);
}

.scene-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* ===== 地图场景 ===== */
.map-scene {
  align-items: center;
  justify-content: center;
}

.map-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.map-placeholder {
  text-align: center;
  color: var(--text-muted);
  padding: var(--spacing-8);
  border: 2px dashed var(--success-300);
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--success-50) 0%, rgba(255, 255, 255, 0.5) 100%);
}

.map-placeholder i {
  font-size: var(--font-size-4xl);
  color: var(--success-400);
  margin-bottom: var(--spacing-4);
}

.map-placeholder h3 {
  margin: 0 0 var(--spacing-2) 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
}

.map-placeholder p {
  margin: 0 0 var(--spacing-4) 0;
  color: var(--text-secondary);
}

.upload-map-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: none;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
  cursor: pointer;
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.upload-map-btn:hover {
  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== 线索场景 ===== */
.clues-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--success-200);
}

.clues-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--success-300);
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);
  color: var(--success-700);
  cursor: pointer;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.add-btn:hover {
  background: linear-gradient(135deg, var(--success-200) 0%, var(--success-300) 100%);
  border-color: var(--success-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.clues-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-4);
}

.clue-card {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.clue-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 100%);
}

.clue-card:hover {
  border-color: var(--success-300);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.clue-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
}

.clue-title {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
}

.clue-type {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.clue-type.important {
  background: var(--error-100);
  color: var(--error-700);
}

.clue-type.normal {
  background: var(--success-100);
  color: var(--success-700);
}

.clue-content {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-3);
}

.clue-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.clue-discoverer {
  font-weight: var(--font-weight-medium);
}/
* ===== 笔记场景 ===== */
.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-3);
  border-bottom: 2px solid var(--success-200);
}

.notes-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.notes-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.note-filter {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.note-filter:focus {
  outline: none;
  border-color: var(--success-400);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.note-card {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.note-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 100%);
}

.note-card:hover {
  border-color: var(--success-300);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
}

.note-title {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
}

.note-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.note-author {
  font-weight: var(--font-weight-medium);
}

.note-content {
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--spacing-3);
}

.note-actions {
  display: flex;
  gap: var(--spacing-2);
  justify-content: flex-end;
}

.note-action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--success-200);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
}

.note-action-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
  transform: scale(1.1);
}

.note-action-btn.delete:hover {
  background: var(--error-50);
  color: var(--error-600);
  border-color: var(--error-300);
}

/* ===== 聊天控制 ===== */
.chat-controls {
  display: flex;
  gap: var(--spacing-1);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-1);
}

.chat-mode-btn {
  padding: var(--spacing-1) var(--spacing-3);
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.chat-mode-btn.active {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
}

.chat-mode-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-inverse);
}

/* ===== 底部工具栏 ===== */
.bottom-toolbar {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--success-50) 100%);
  border-top: 2px solid var(--success-200);
  padding: var(--spacing-3) var(--spacing-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--shadow-lg);
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.audio-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
}

.audio-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
  transform: scale(1.1);
}

.audio-btn.muted,
.audio-btn.deafened {
  background: var(--error-50);
  color: var(--error-600);
  border-color: var(--error-300);
}

.volume-control {
  margin-left: var(--spacing-2);
}

.volume-slider {
  width: 80px;
  height: 4px;
  border-radius: var(--radius-full);
  background: var(--success-200);
  outline: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  background: var(--success-500);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.toolbar-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.toolbar-btn.primary {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
  border-color: var(--success-500);
}

.toolbar-btn.primary:hover {
  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);
  color: var(--text-inverse);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--bg-primary);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xs);
}

.status-indicator.connected {
  background: var(--success-500);
  color: var(--text-inverse);
}

.status-indicator.connecting {
  background: var(--warning-500);
  color: var(--text-inverse);
}

.status-indicator.disconnected {
  background: var(--error-500);
  color: var(--text-inverse);
}

.status-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}/*
 ===== 响应式设计 ===== */
@media (max-width: 1024px) {
  .left-panel {
    width: 250px;
  }
  
  .right-panel {
    width: 300px;
  }
  
  .room-header {
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .room-title h1 {
    font-size: var(--font-size-xl);
  }
  
  .scene-content {
    padding: var(--spacing-4);
  }
}

@media (max-width: 768px) {
  .game-layout {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
    border: none;
    border-top: 1px solid var(--success-200);
    border-bottom: 1px solid var(--success-200);
  }
  
  .left-panel {
    order: 2;
  }
  
  .center-panel {
    order: 1;
    flex: 1;
    border: none;
  }
  
  .right-panel {
    order: 3;
  }
  
  .room-header {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }
  
  .room-info {
    order: 1;
  }
  
  .room-controls {
    order: 2;
    justify-content: center;
  }
  
  .room-meta {
    justify-content: center;
  }
  
  .scene-tabs {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .scene-tab {
    flex: 1;
    min-width: 80px;
    justify-content: center;
  }
  
  .scene-tab span:not(.tab-badge) {
    display: none;
  }
  
  .clues-grid {
    grid-template-columns: 1fr;
  }
  
  .bottom-toolbar {
    flex-direction: column;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
  }
  
  .toolbar-left,
  .toolbar-center,
  .toolbar-right {
    width: 100%;
    justify-content: center;
  }
  
  .audio-controls {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .room-header {
    padding: var(--spacing-2);
  }
  
  .room-title {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-2);
  }
  
  .room-title h1 {
    font-size: var(--font-size-lg);
  }
  
  .room-meta {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .control-btn {
    width: 36px;
    height: 36px;
  }
  
  .panel-content {
    padding: var(--spacing-3);
  }
  
  .character-card {
    padding: var(--spacing-2);
  }
  
  .character-avatar {
    width: 40px;
    height: 40px;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .scene-content {
    padding: var(--spacing-3);
  }
  
  .scene-tab {
    padding: var(--spacing-2);
  }
  
  .map-placeholder {
    padding: var(--spacing-4);
  }
  
  .map-placeholder i {
    font-size: var(--font-size-2xl);
  }
  
  .clue-card,
  .note-card {
    padding: var(--spacing-3);
  }
  
  .toolbar-btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
  }
  
  .toolbar-btn span {
    display: none;
  }
  
  .volume-control {
    display: none;
  }
}

/* ===== 可访问性增强 ===== */
.game-room button:focus {
  outline: 2px solid var(--success-500);
  outline-offset: 2px;
}

.game-room input:focus,
.game-room select:focus {
  outline: 2px solid var(--success-500);
  outline-offset: 2px;
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
  .game-room {
    border: 2px solid var(--success-600);
  }
  
  .room-header,
  .panel-header {
    border-bottom: 3px solid var(--success-400);
  }
  
  .left-panel,
  .right-panel,
  .center-panel {
    border-width: 2px;
  }
  
  .character-card,
  .clue-card,
  .note-card {
    border-width: 2px;
  }
  
  .control-btn,
  .panel-toggle,
  .action-btn,
  .scene-tab,
  .toolbar-btn,
  .audio-btn {
    border-width: 2px;
  }
}

/* ===== 减少动画模式 ===== */
@media (prefers-reduced-motion: reduce) {
  .game-room *,
  .game-room *::before,
  .game-room *::after {
    animation: none !important;
    transition: none !important;
  }
  
  .control-btn:hover,
  .action-btn:hover,
  .scene-tab:hover,
  .toolbar-btn:hover,
  .character-card:hover,
  .clue-card:hover,
  .note-card:hover {
    transform: none !important;
  }
}

/* ===== 打印样式 ===== */
@media print {
  .game-room {
    height: auto;
    background: white !important;
  }
  
  .room-header {
    background: var(--gray-100) !important;
    color: var(--text-primary) !important;
    page-break-after: avoid;
  }
  
  .room-controls,
  .panel-toggle,
  .bottom-toolbar {
    display: none !important;
  }
  
  .game-layout {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100% !important;
    height: auto !important;
    page-break-inside: avoid;
  }
  
  .center-panel {
    width: 100% !important;
    page-break-inside: avoid;
  }
  
  .scene-tabs {
    display: none !important;
  }
  
  .scene-content {
    padding: var(--spacing-3) !important;
  }
  
  .character-card,
  .clue-card,
  .note-card {
    page-break-inside: avoid;
    margin-bottom: var(--spacing-3);
  }
}
</style>