/**
 * COC 7版对抗检定系统测试
 * 验证近战对抗检定的完整功能
 */

import CombatRules from './combatRules.js'
import { diceRoller } from './diceRoller.js'

console.log('=== COC 7版对抗检定系统测试 ===\n')

// 创建测试角色
const fighter = {
  id: 'fighter1',
  name: '战士',
  faction: 'heroes',
  
  // 基础属性
  strength: 80,
  dexterity: 70,
  constitution: 75,
  size: 70,
  
  // 技能
  fighting: 70,
  dodge: 35,
  sword: 60,
  
  // 装备
  currentWeapon: {
    name: '长剑',
    damage: '1d8+1',
    skill: 'sword',
    canBlock: true,
    impaling: false
  },
  
  // 伤害加值
  damageBonus: CombatRules.calculateDamageBonus(80, 70),
  
  position: { x: 0, y: 0 },
  status: 'active'
}

const rogue = {
  id: 'rogue1',
  name: '盗贼',
  faction: 'villains',
  
  // 基础属性
  strength: 60,
  dexterity: 85,
  constitution: 65,
  size: 60,
  
  // 技能
  fighting: 50,
  dodge: 42,
  knife: 65,
  
  // 装备
  currentWeapon: {
    name: '匕首',
    damage: '1d4+2',
    skill: 'knife',
    canBlock: false,
    impaling: true
  },
  
  // 伤害加值
  damageBonus: CombatRules.calculateDamageBonus(60, 60),
  
  position: { x: 1, y: 0 },
  status: 'active'
}

// 测试1: 基础近战对抗 (攻击 vs 闪避)
console.log('=== 测试1: 攻击 vs 闪避 ===')

for (let i = 0; i < 3; i++) {
  console.log(`\n回合 ${i + 1}:`)
  
  const result = CombatRules.resolveMeleeOpposedRoll(fighter, rogue, {
    defenseType: 'dodge',
    attackWeapon: fighter.currentWeapon,
    defenseWeapon: null
  })
  
  console.log(`攻击者: ${result.attacker.name} 使用 ${result.attacker.weapon}`)
  console.log(`  投掷: ${result.attacker.roll} vs 技能 ${result.attacker.skill} (${result.attacker.level})`)
  
  console.log(`防御者: ${result.defender.name} 选择 ${result.defender.defenseType}`)
  console.log(`  投掷: ${result.defender.roll} vs 技能 ${result.defender.skill} (${result.defender.level})`)
  
  console.log(`结果: ${result.description}`)
  
  if (result.attackDamage) {
    console.log(`💥 伤害: ${result.attackDamage.totalDamage} 点`)
  }
}

// 测试2: 近战反击
console.log('\n\n=== 测试2: 攻击 vs 反击 ===')

for (let i = 0; i < 3; i++) {
  console.log(`\n回合 ${i + 1}:`)
  
  const result = CombatRules.resolveMeleeOpposedRoll(fighter, rogue, {
    defenseType: 'fight_back',
    attackWeapon: fighter.currentWeapon,
    defenseWeapon: rogue.currentWeapon
  })
  
  console.log(`攻击者: ${result.attacker.name} 使用 ${result.attacker.weapon}`)
  console.log(`  投掷: ${result.attacker.roll} vs 技能 ${result.attacker.skill} (${result.attacker.level})`)
  
  console.log(`防御者: ${result.defender.name} 使用 ${result.defender.weapon} 反击`)
  console.log(`  投掷: ${result.defender.roll} vs 技能 ${result.defender.skill} (${result.defender.level})`)
  
  console.log(`结果: ${result.description}`)
  
  if (result.attackDamage) {
    console.log(`💥 攻击伤害: ${result.attackDamage.totalDamage} 点`)
  }
  
  if (result.counterDamage) {
    console.log(`⚔️ 反击伤害: ${result.counterDamage.totalDamage} 点`)
  }
}

// 测试3: 格挡防御
console.log('\n\n=== 测试3: 攻击 vs 格挡 ===')

// 给盗贼一个可以格挡的武器
const blockingRogue = {
  ...rogue,
  currentWeapon: {
    name: '短剑',
    damage: '1d6+1',
    skill: 'sword',
    canBlock: true,
    impaling: false
  },
  sword: 45,
  damageBonus: CombatRules.calculateDamageBonus(60, 60)
}

for (let i = 0; i < 3; i++) {
  console.log(`\n回合 ${i + 1}:`)
  
  const result = CombatRules.resolveMeleeOpposedRoll(fighter, blockingRogue, {
    defenseType: 'block',
    attackWeapon: fighter.currentWeapon,
    defenseWeapon: blockingRogue.currentWeapon
  })
  
  console.log(`攻击者: ${result.attacker.name} 使用 ${result.attacker.weapon}`)
  console.log(`  投掷: ${result.attacker.roll} vs 技能 ${result.attacker.skill} (${result.attacker.level})`)
  
  console.log(`防御者: ${result.defender.name} 使用 ${result.defender.weapon} 格挡`)
  console.log(`  投掷: ${result.defender.roll} vs 技能 ${result.defender.skill} (${result.defender.level})`)
  
  console.log(`结果: ${result.description}`)
  
  if (result.attackDamage) {
    console.log(`💥 伤害: ${result.attackDamage.totalDamage} 点`)
  }
}

// 测试4: 最佳防御选择
console.log('\n\n=== 测试4: 最佳防御选择 ===')

const testDefender = {
  ...rogue,
  fighting: 40,
  dodge: 30,
  sword: 50,
  currentWeapon: {
    name: '短剑',
    skill: 'sword',
    canBlock: true
  }
}

const bestDefense = CombatRules.chooseBestDefense(testDefender, fighter)
console.log(`防御者技能值:`)
console.log(`  格斗: ${CombatRules.getMeleeSkill(testDefender, testDefender.currentWeapon)}`)
console.log(`  闪避: ${CombatRules.getDodgeSkill(testDefender)}`)
console.log(`  格挡: ${CombatRules.getBlockSkill(testDefender, testDefender.currentWeapon)}`)
console.log(`最佳防御方式: ${bestDefense}`)

// 测试5: 寡不敌众奖励骰
console.log('\n\n=== 测试5: 寡不敌众奖励骰 ===')

const enemies = [
  { status: 'active', faction: 'enemies' },
  { status: 'active', faction: 'enemies' },
  { status: 'active', faction: 'enemies' }
]

const hero = { faction: 'heroes' }

const bonusDice = CombatRules.calculateOutnumberedBonus(hero, enemies)
console.log(`英雄面对 ${enemies.length} 个敌人`)
console.log(`获得奖励骰: ${bonusDice} 个`)

// 测试6: 反击条件检查
console.log('\n\n=== 测试6: 反击条件检查 ===')

const testCases = [
  {
    name: '正常情况',
    defender: { 
      currentWeapon: { name: '剑' }, 
      position: { x: 0, y: 0 },
      conditions: []
    },
    attacker: { position: { x: 1, y: 0 } }
  },
  {
    name: '无武器',
    defender: { 
      currentWeapon: null, 
      fighting: 50,
      position: { x: 0, y: 0 },
      conditions: []
    },
    attacker: { position: { x: 1, y: 0 } }
  },
  {
    name: '距离太远',
    defender: { 
      currentWeapon: { name: '剑' }, 
      position: { x: 0, y: 0 },
      conditions: []
    },
    attacker: { position: { x: 5, y: 0 } }
  },
  {
    name: '被眩晕',
    defender: { 
      currentWeapon: { name: '剑' }, 
      position: { x: 0, y: 0 },
      conditions: ['stunned']
    },
    attacker: { position: { x: 1, y: 0 } }
  }
]

testCases.forEach(testCase => {
  const canFightBack = CombatRules.canFightBack(testCase.defender, testCase.attacker)
  console.log(`${testCase.name}: ${canFightBack ? '可以反击' : '无法反击'}`)
})

// 测试7: 多人混战
console.log('\n\n=== 测试7: 多人混战 ===')

const brawlParticipants = [
  {
    id: 'hero1',
    name: '英雄1',
    faction: 'heroes',
    status: 'active',
    currentWeapon: { name: '剑', damage: '1d8' },
    fighting: 60,
    damageBonus: CombatRules.calculateDamageBonus(70, 65)
  },
  {
    id: 'hero2', 
    name: '英雄2',
    faction: 'heroes',
    status: 'active',
    currentWeapon: { name: '斧', damage: '1d8+2' },
    fighting: 55,
    damageBonus: CombatRules.calculateDamageBonus(75, 70)
  },
  {
    id: 'villain1',
    name: '恶棍1',
    faction: 'villains',
    status: 'active',
    currentWeapon: { name: '匕首', damage: '1d4+2' },
    fighting: 50,
    damageBonus: CombatRules.calculateDamageBonus(60, 60)
  },
  {
    id: 'villain2',
    name: '恶棍2', 
    faction: 'villains',
    status: 'active',
    currentWeapon: { name: '棍棒', damage: '1d6' },
    fighting: 45,
    damageBonus: CombatRules.calculateDamageBonus(65, 65)
  }
]

const brawlResult = CombatRules.resolveMeleeBrawl(brawlParticipants)

console.log(`混战参与者: ${brawlParticipants.length} 人`)
console.log(`攻击次数: ${brawlResult.summary.totalAttacks}`)
console.log(`成功攻击: ${brawlResult.summary.successfulAttacks}`)
console.log(`反击次数: ${brawlResult.summary.counterAttacks}`)
console.log(`总伤害: ${brawlResult.summary.totalDamage}`)

console.log('\n混战详情:')
brawlResult.results.forEach((result, index) => {
  console.log(`${index + 1}. ${result.description}`)
})

console.log('\n=== 对抗检定系统测试完成 ===')
console.log('所有近战对抗检定功能已验证，符合COC 7版规则书要求！')