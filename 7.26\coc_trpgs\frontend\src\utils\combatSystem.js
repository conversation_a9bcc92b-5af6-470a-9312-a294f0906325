/**
 * COC 7版完整战斗系统
 * 整合所有战斗相关功能的主入口
 */

import CombatRules from './combatRules.js'
import { diceRoller } from './diceRoller.js'

export class CombatSystem {
  constructor() {
    this.currentCombat = null
    this.participants = []
    this.currentTurn = 0
    this.roundNumber = 1
    this.combatLog = []
  }

  /**
   * 开始新的战斗
   * @param {Array} participants 参与者列表
   * @param {Object} environment 环境设置
   */
  startCombat(participants, environment = {}) {
    this.currentCombat = {
      id: Date.now(),
      startTime: new Date(),
      environment,
      status: 'active'
    }
    
    this.participants = participants.map(p => ({
      ...p,
      initiative: this.calculateInitiative(p),
      currentHP: p.hitPoints || p.hp,
      currentSAN: p.sanity || p.san,
      status: 'active',
      conditions: [],
      actions: [],
      // 计算伤害加值
      damageBonus: CombatRules.calculateDamageBonus(p.strength, p.size)
    }))
    
    // 按先攻顺序排序
    this.participants.sort((a, b) => b.initiative - a.initiative)
    
    this.currentTurn = 0
    this.roundNumber = 1
    
    this.logEvent('combat_start', {
      participants: this.participants.length,
      environment
    })
    
    return this.currentCombat
  }

  /**
   * 计算先攻值 (严格按COC 7版规则)
   * @param {Object} character 角色数据
   * @returns {number} 先攻值
   */
  calculateInitiative(character) {
    // 基础先攻值 = 敏捷值
    let initiative = character.dexterity || character.dex || 50
    
    // 射击武器先攻优势 (+50)
    if (character.currentWeapon && character.currentWeapon.type === 'firearm') {
      initiative += 50
    }
    
    // 特殊情况修正
    if (character.conditions) {
      // 受伤状态影响先攻
      if (character.conditions.includes('major_wound')) {
        initiative -= 20
      }
      if (character.conditions.includes('injured')) {
        initiative -= 10
      }
      
      // 某些状态完全失去先攻
      if (character.conditions.includes('unconscious') || 
          character.conditions.includes('stunned')) {
        initiative = 0
      }
    }
    
    // 添加随机因素 (1d10)
    initiative += diceRoller.rollD10()
    
    return Math.max(0, initiative)
  }

  /**
   * 延迟行动
   * @param {string} participantId 参与者ID
   * @param {number} newInitiative 新的先攻值
   */
  delayAction(participantId, newInitiative = null) {
    const participant = this.getParticipant(participantId)
    if (!participant) return false
    
    // 如果没有指定新先攻值，则设为当前最低先攻值-1
    if (newInitiative === null) {
      const minInitiative = Math.min(...this.participants.map(p => p.initiative))
      newInitiative = minInitiative - 1
    }
    
    participant.initiative = newInitiative
    participant.hasDelayed = true
    
    // 重新排序参与者
    this.participants.sort((a, b) => b.initiative - a.initiative)
    
    // 更新当前回合索引
    this.currentTurn = this.participants.findIndex(p => p.id === participantId)
    
    this.logEvent('delay_action', {
      participant: participant.name,
      newInitiative
    })
    
    return true
  }

  /**
   * 准备行动 (全防御)
   * @param {string} participantId 参与者ID
   */
  readyAction(participantId) {
    const participant = this.getParticipant(participantId)
    if (!participant) return false
    
    participant.isReady = true
    participant.readyAction = null // 将在触发条件满足时设置
    
    this.logEvent('ready_action', {
      participant: participant.name
    })
    
    return true
  }

  /**
   * 触发准备的行动
   * @param {string} participantId 参与者ID
   * @param {Object} action 行动内容
   */
  triggerReadyAction(participantId, action) {
    const participant = this.getParticipant(participantId)
    if (!participant || !participant.isReady) return false
    
    participant.isReady = false
    participant.readyAction = action
    
    this.logEvent('trigger_ready_action', {
      participant: participant.name,
      action: action.type
    })
    
    return true
  }

  /**
   * 全防御行动
   * @param {string} participantId 参与者ID
   */
  fullDefense(participantId) {
    const participant = this.getParticipant(participantId)
    if (!participant) return false
    
    participant.isFullDefense = true
    participant.defenseBonus = 20 // 全防御获得+20防御加值
    
    this.logEvent('full_defense', {
      participant: participant.name
    })
    
    return true
  }

  /**
   * 检查回合结束条件
   */
  checkRoundEnd() {
    // 检查是否所有活跃参与者都已行动
    const activeParticipants = this.participants.filter(p => p.status === 'active')
    const actedParticipants = activeParticipants.filter(p => p.hasActed)
    
    return actedParticipants.length >= activeParticipants.length
  }

  /**
   * 开始新回合
   */
  startNewRound() {
    this.roundNumber++
    this.currentTurn = 0
    
    // 重置所有参与者的回合状态
    this.participants.forEach(participant => {
      participant.hasActed = false
      participant.hasDelayed = false
      participant.isFullDefense = false
      participant.defenseBonus = 0
      participant.isReady = false
      participant.readyAction = null
      
      // 处理持续效果
      this.processContinuousEffects(participant)
    })
    
    this.logEvent('new_round', {
      round: this.roundNumber
    })
  }

  /**
   * 处理持续效果
   * @param {Object} participant 参与者
   */
  processContinuousEffects(participant) {
    if (!participant.conditions) return
    
    // 处理流血效果
    if (participant.conditions.includes('bleeding')) {
      const bleedDamage = 1 // 每回合失去1点生命值
      participant.currentHP = Math.max(0, participant.currentHP - bleedDamage)
      
      this.logEvent('bleeding_damage', {
        participant: participant.name,
        damage: bleedDamage,
        remainingHP: participant.currentHP
      })
    }
    
    // 处理中毒效果
    if (participant.conditions.includes('poisoned')) {
      const conCheck = diceRoller.rollD100()
      const constitution = participant.constitution || participant.con || 50
      
      if (conCheck > constitution) {
        const poisonDamage = diceRoller.roll('1d3').total
        participant.currentHP = Math.max(0, participant.currentHP - poisonDamage)
        
        this.logEvent('poison_damage', {
          participant: participant.name,
          roll: conCheck,
          constitution,
          damage: poisonDamage,
          remainingHP: participant.currentHP
        })
      }
    }
    
    // 移除临时状态效果
    const temporaryEffects = ['stunned', 'dazed']
    participant.conditions = participant.conditions.filter(
      condition => !temporaryEffects.includes(condition)
    )
  }

  /**
   * 执行攻击行动
   * @param {string} attackerId 攻击者ID
   * @param {string} defenderId 防御者ID
   * @param {Object} attackOptions 攻击选项
   */
  performAttack(attackerId, defenderId, attackOptions = {}) {
    const attacker = this.getParticipant(attackerId)
    const defender = this.getParticipant(defenderId)
    
    if (!attacker || !defender) {
      throw new Error('Invalid attacker or defender')
    }

    const weapon = attacker.currentWeapon || this.getDefaultWeapon()
    const attackSkill = this.getAttackSkill(attacker, weapon)
    
    // 计算攻击难度和修正
    const difficulty = this.calculateAttackDifficulty(attacker, defender, weapon, attackOptions)
    const bonusDice = this.calculateBonusDice(attacker, defender, weapon, attackOptions)
    
    // 攻击者投骰
    const attackRoll = diceRoller.rollD100()
    const finalAttackRoll = CombatRules.applyBonusPenaltyDice(attackRoll, bonusDice)
    const attackLevel = CombatRules.getSuccessLevel(finalAttackRoll, attackSkill)
    
    // 防御者响应
    const defenseResult = this.performDefense(defender, attackLevel, attackOptions)
    
    // 对抗检定
    const opposedResult = CombatRules.resolveOpposedRoll(
      { roll: finalAttackRoll, skill: attackSkill, name: attacker.name },
      { roll: defenseResult.roll, skill: defenseResult.skill, name: defender.name }
    )
    
    let damage = 0
    let damageResult = null
    
    // 如果攻击成功
    if (opposedResult.winner === 'attacker') {
      damageResult = CombatRules.calculateDamage(weapon, attacker, opposedResult.winnerLevel)
      damage = this.applyDamage(defender, damageResult.totalDamage, weapon)
    }
    
    // 记录战斗结果
    const combatResult = {
      type: 'attack',
      attacker: attacker.name,
      defender: defender.name,
      weapon: weapon.name,
      attackRoll: finalAttackRoll,
      attackLevel,
      defenseRoll: defenseResult.roll,
      defenseLevel: defenseResult.level,
      opposedResult,
      damage,
      damageResult,
      timestamp: Date.now()
    }
    
    this.logEvent('attack', combatResult)
    
    // 检查是否有人倒下
    this.checkCasualties()
    
    return combatResult
  }

  /**
   * 执行防御行动
   * @param {Object} defender 防御者
   * @param {string} attackLevel 攻击成功等级
   * @param {Object} options 防御选项
   */
  performDefense(defender, attackLevel, options = {}) {
    const defenseType = options.defenseType || 'dodge' // dodge, fight_back, block
    let defenseSkill = 0
    
    switch (defenseType) {
      case 'dodge':
        defenseSkill = defender.dodge || Math.floor(defender.dexterity / 2)
        break
      case 'fight_back':
        defenseSkill = this.getAttackSkill(defender, defender.currentWeapon)
        break
      case 'block':
        defenseSkill = defender.block || 0
        break
    }
    
    const defenseRoll = diceRoller.rollD100()
    const defenseLevel = CombatRules.getSuccessLevel(defenseRoll, defenseSkill)
    
    return {
      type: defenseType,
      roll: defenseRoll,
      skill: defenseSkill,
      level: defenseLevel
    }
  }

  /**
   * 应用伤害
   * @param {Object} target 目标
   * @param {number} damage 伤害值
   * @param {Object} weapon 武器
   */
  applyDamage(target, damage, weapon) {
    // 计算护甲减伤
    const armor = target.armor || 0
    const finalDamage = Math.max(0, damage - armor)
    
    // 扣除生命值
    target.currentHP = Math.max(0, target.currentHP - finalDamage)
    
    // 检查生命状态
    const healthStatus = this.getHealthStatus(target)
    if (healthStatus !== 'healthy') {
      target.conditions = target.conditions || []
      if (!target.conditions.includes(healthStatus)) {
        target.conditions.push(healthStatus)
      }
    }
    
    return finalDamage
  }

  /**
   * 获取生命状态
   * @param {Object} character 角色
   * @returns {string} 生命状态
   */
  getHealthStatus(character) {
    const maxHP = character.hitPoints || character.hp
    const currentHP = character.currentHP
    const percentage = currentHP / maxHP
    
    if (currentHP <= 0) {
      return 'dying'
    } else if (percentage <= 0.2) {
      return 'major_wound'
    } else if (percentage <= 0.5) {
      return 'injured'
    }
    
    return 'healthy'
  }

  /**
   * 计算攻击技能值
   * @param {Object} character 角色
   * @param {Object} weapon 武器
   */
  getAttackSkill(character, weapon) {
    if (!weapon) return 25 // 默认格斗技能
    
    const skillName = weapon.skill || 'fighting'
    return character[skillName] || character.skills?.[skillName] || 25
  }

  /**
   * 计算攻击难度
   * @param {Object} attacker 攻击者
   * @param {Object} defender 防御者
   * @param {Object} weapon 武器
   * @param {Object} options 选项
   */
  calculateAttackDifficulty(attacker, defender, weapon, options) {
    let difficulty = 'regular'
    
    // 射击距离影响
    if (weapon.type === 'firearm' && options.distance) {
      difficulty = CombatRules.getShootingDifficulty(options.distance, weapon.range)
    }
    
    // 环境因素
    if (options.lighting === 'dark') {
      difficulty = this.increaseDifficulty(difficulty)
    }
    
    if (options.cover) {
      difficulty = this.increaseDifficulty(difficulty)
    }
    
    return difficulty
  }

  /**
   * 计算奖励/惩罚骰
   * @param {Object} attacker 攻击者
   * @param {Object} defender 防御者
   * @param {Object} weapon 武器
   * @param {Object} options 选项
   */
  calculateBonusDice(attacker, defender, weapon, options) {
    let bonusDice = 0
    
    // 瞄准奖励
    if (options.aiming) {
      bonusDice += 1
    }
    
    // 抵近射击奖励
    if (weapon.type === 'firearm' && options.distance <= 5) {
      bonusDice += 1
    }
    
    // 寡不敌众惩罚
    const enemyCount = this.getEnemyCount(attacker)
    if (enemyCount > 1) {
      bonusDice -= Math.min(enemyCount - 1, 2)
    }
    
    return bonusDice
  }

  /**
   * 获取默认武器 (拳头)
   */
  getDefaultWeapon() {
    return {
      name: '拳头',
      damage: '1d3',
      skill: 'fighting',
      type: 'melee',
      impaling: false
    }
  }

  /**
   * 增加难度等级
   */
  increaseDifficulty(currentDifficulty) {
    const levels = ['regular', 'hard', 'extreme', 'impossible']
    const currentIndex = levels.indexOf(currentDifficulty)
    return levels[Math.min(currentIndex + 1, levels.length - 1)]
  }

  /**
   * 获取参与者
   */
  getParticipant(id) {
    return this.participants.find(p => p.id === id)
  }

  /**
   * 获取敌人数量
   */
  getEnemyCount(character) {
    return this.participants.filter(p => 
      p.faction !== character.faction && p.status === 'active'
    ).length
  }

  /**
   * 检查伤亡
   */
  checkCasualties() {
    this.participants.forEach(p => {
      if (p.currentHP <= 0 && p.status === 'active') {
        p.status = 'down'
        this.logEvent('casualty', { name: p.name, status: 'down' })
      }
    })
  }

  /**
   * 下一回合
   */
  nextTurn() {
    this.currentTurn++
    
    if (this.currentTurn >= this.participants.length) {
      this.currentTurn = 0
      this.roundNumber++
      this.logEvent('new_round', { round: this.roundNumber })
    }
    
    return this.getCurrentParticipant()
  }

  /**
   * 获取当前行动者
   */
  getCurrentParticipant() {
    return this.participants[this.currentTurn]
  }

  /**
   * 记录事件
   */
  logEvent(type, data) {
    this.combatLog.push({
      type,
      data,
      timestamp: Date.now(),
      round: this.roundNumber,
      turn: this.currentTurn
    })
  }

  /**
   * 结束战斗
   */
  endCombat(reason = 'completed') {
    if (this.currentCombat) {
      this.currentCombat.status = 'ended'
      this.currentCombat.endTime = new Date()
      this.currentCombat.reason = reason
      
      this.logEvent('combat_end', { reason })
    }
  }

  /**
   * 获取战斗状态
   */
  getCombatStatus() {
    return {
      combat: this.currentCombat,
      participants: this.participants,
      currentTurn: this.currentTurn,
      roundNumber: this.roundNumber,
      currentParticipant: this.getCurrentParticipant(),
      log: this.combatLog
    }
  }
}

// 导出单例实例
export const combatSystem = new CombatSystem()

export default CombatSystem