{"ast": null, "code": "import \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/web.timers.js\";\nimport BattlefieldGrid from './BattlefieldGrid.vue';\nexport default {\n  name: 'ForcedCombatMode',\n  components: {\n    BattlefieldGrid: BattlefieldGrid\n  },\n  props: {\n    isActive: {\n      type: Boolean,\n      \"default\": false\n    },\n    isKeeper: {\n      type: Boolean,\n      \"default\": false\n    },\n    combatData: {\n      type: Object,\n      \"default\": function _default() {\n        return {};\n      }\n    }\n  },\n  data: function data() {\n    return {\n      // 战斗状态\n      participants: [],\n      currentTurn: 0,\n      currentRound: 1,\n      selectedParticipant: null,\n      targetParticipant: null,\n      // 玩家操作\n      selectedAction: null,\n      actionData: null,\n      // 界面状态\n      showResult: false,\n      resultData: {},\n      showReconnect: false,\n      reconnectProgress: 0,\n      reconnectAttempts: 0,\n      maxReconnectAttempts: 5,\n      // 连接状态\n      connectionStatus: 'connected',\n      // 战场配置\n      battlefieldSize: {\n        width: 20,\n        height: 15\n      }\n    };\n  },\n  computed: {\n    currentParticipant: function currentParticipant() {\n      return this.participants[this.currentTurn] || null;\n    },\n    isPlayerTurn: function isPlayerTurn() {\n      var _this$currentParticip, _this$currentParticip2, _this$$store$state$au;\n      return ((_this$currentParticip = this.currentParticipant) === null || _this$currentParticip === void 0 ? void 0 : _this$currentParticip.isPlayer) && ((_this$currentParticip2 = this.currentParticipant) === null || _this$currentParticip2 === void 0 ? void 0 : _this$currentParticip2.playerId) === ((_this$$store$state$au = this.$store.state.auth.user) === null || _this$$store$state$au === void 0 ? void 0 : _this$$store$state$au.id);\n    },\n    currentTurnInfo: function currentTurnInfo() {\n      if (!this.currentParticipant) return '等待开始';\n      return \"\".concat(this.currentParticipant.name, \" \\u7684\\u56DE\\u5408\");\n    },\n    connectionText: function connectionText() {\n      var texts = {\n        connected: '已连接',\n        connecting: '连接中',\n        disconnected: '已断开',\n        error: '连接错误'\n      };\n      return texts[this.connectionStatus] || '未知状态';\n    }\n  },\n  methods: {\n    // 参与者操作\n    selectParticipant: function selectParticipant(participant) {\n      this.selectedParticipant = participant;\n      this.$emit('participant-selected', participant);\n    },\n    handleParticipantClick: function handleParticipantClick(participant) {\n      if (this.selectedAction === 'attack') {\n        this.targetParticipant = participant;\n      } else {\n        this.selectParticipant(participant);\n      }\n    },\n    handlePositionClick: function handlePositionClick(position) {\n      if (this.selectedAction === 'move') {\n        this.actionData = {\n          targetPosition: position\n        };\n      }\n    },\n    handleParticipantMove: function handleParticipantMove(participant, newPosition) {\n      this.$emit('participant-moved', participant, newPosition);\n    },\n    // 行动选择\n    selectAction: function selectAction(actionType) {\n      this.selectedAction = actionType;\n      this.actionData = null;\n      this.targetParticipant = null;\n\n      // 根据行动类型设置界面状态\n      switch (actionType) {\n        case 'attack':\n          this.showMessage('请选择攻击目标');\n          break;\n        case 'move':\n          this.showMessage('请选择移动位置');\n          break;\n        case 'defend':\n          this.actionData = {\n            type: 'full_defense'\n          };\n          break;\n        case 'item':\n          this.showItemSelection();\n          break;\n        case 'maneuver':\n          this.showManeuverSelection();\n          break;\n      }\n    },\n    confirmAction: function confirmAction() {\n      if (!this.selectedAction) return;\n      var actionData = {\n        type: this.selectedAction,\n        participant: this.currentParticipant,\n        target: this.targetParticipant,\n        data: this.actionData\n      };\n      this.$emit('action-confirmed', actionData);\n      this.resetActionState();\n    },\n    cancelAction: function cancelAction() {\n      this.resetActionState();\n    },\n    resetActionState: function resetActionState() {\n      this.selectedAction = null;\n      this.actionData = null;\n      this.targetParticipant = null;\n    },\n    // KP控制\n    nextTurn: function nextTurn() {\n      this.$emit('next-turn');\n    },\n    pauseCombat: function pauseCombat() {\n      this.$emit('pause-combat');\n    },\n    endCombat: function endCombat() {\n      this.$emit('end-combat');\n    },\n    // 界面辅助\n    getHealthPercentage: function getHealthPercentage(participant) {\n      if (!participant.maxHP) return 0;\n      return Math.max(0, participant.currentHP / participant.maxHP * 100);\n    },\n    getSanityPercentage: function getSanityPercentage(participant) {\n      if (!participant.maxSAN) return 0;\n      return Math.max(0, participant.currentSAN / participant.maxSAN * 100);\n    },\n    getStatusEffectName: function getStatusEffectName(effect) {\n      var names = {\n        bleeding: '流血',\n        poisoned: '中毒',\n        stunned: '眩晕',\n        frightened: '恐惧',\n        blessed: '祝福',\n        cursed: '诅咒',\n        prone: '倒地',\n        grappled: '被擒抱',\n        unconscious: '昏迷'\n      };\n      return names[effect] || effect;\n    },\n    showMessage: function showMessage(message) {\n      // 显示临时消息\n      this.$emit('show-message', message);\n    },\n    showItemSelection: function showItemSelection() {\n      // 显示道具选择界面\n      this.$emit('show-item-selection');\n    },\n    showManeuverSelection: function showManeuverSelection() {\n      // 显示战技选择界面\n      this.$emit('show-maneuver-selection');\n    },\n    // 结果处理\n    showCombatResult: function showCombatResult(resultData) {\n      this.resultData = resultData;\n      this.showResult = true;\n    },\n    closeResult: function closeResult() {\n      this.showResult = false;\n      this.resultData = {};\n    },\n    // 连接管理\n    handleConnectionLost: function handleConnectionLost() {\n      this.connectionStatus = 'disconnected';\n      this.showReconnect = true;\n      this.startReconnectAttempts();\n    },\n    startReconnectAttempts: function startReconnectAttempts() {\n      var _this = this;\n      this.reconnectAttempts = 0;\n      this.reconnectProgress = 0;\n      var _attemptReconnect = function attemptReconnect() {\n        if (_this.reconnectAttempts >= _this.maxReconnectAttempts) {\n          _this.handleReconnectFailed();\n          return;\n        }\n        _this.reconnectAttempts++;\n        _this.reconnectProgress = _this.reconnectAttempts / _this.maxReconnectAttempts * 100;\n\n        // 尝试重连\n        _this.$emit('reconnect-attempt');\n        setTimeout(function () {\n          if (_this.connectionStatus !== 'connected') {\n            _attemptReconnect();\n          } else {\n            _this.handleReconnectSuccess();\n          }\n        }, 2000);\n      };\n      _attemptReconnect();\n    },\n    handleReconnectSuccess: function handleReconnectSuccess() {\n      this.showReconnect = false;\n      this.connectionStatus = 'connected';\n      this.showMessage('重新连接成功');\n    },\n    handleReconnectFailed: function handleReconnectFailed() {\n      this.showReconnect = false;\n      this.showCombatResult({\n        title: '连接失败',\n        message: '无法重新连接到战斗服务器，战斗将被强制结束。',\n        details: [{\n          label: '错误代码',\n          value: 'CONNECTION_TIMEOUT'\n        }, {\n          label: '尝试次数',\n          value: this.maxReconnectAttempts\n        }]\n      });\n    }\n  },\n  watch: {\n    combatData: {\n      handler: function handler(newData) {\n        if (newData) {\n          this.participants = newData.participants || [];\n          this.currentTurn = newData.currentTurn || 0;\n          this.currentRound = newData.currentRound || 1;\n          this.battlefieldSize = newData.battlefieldSize || {\n            width: 20,\n            height: 15\n          };\n        }\n      },\n      deep: true,\n      immediate: true\n    },\n    isActive: function isActive(newVal) {\n      if (newVal) {\n        // 进入强制战斗模式\n        document.body.style.overflow = 'hidden';\n        this.$emit('combat-mode-entered');\n      } else {\n        // 退出强制战斗模式\n        document.body.style.overflow = '';\n        this.$emit('combat-mode-exited');\n      }\n    }\n  },\n  beforeUnmount: function beforeUnmount() {\n    // 清理样式\n    document.body.style.overflow = '';\n  }\n};", "map": {"version": 3, "names": ["BattlefieldGrid", "name", "components", "props", "isActive", "type", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "combatData", "Object", "default", "data", "participants", "currentTurn", "currentRound", "selectedParticipant", "targetParticipant", "selectedAction", "actionData", "showResult", "resultData", "showReconnect", "reconnectProgress", "reconnectAttempts", "maxReconnectAttempts", "connectionStatus", "battlefieldSize", "width", "height", "computed", "currentParticipant", "isPlayerTurn", "_this$currentParticip", "_this$currentParticip2", "_this$$store$state$au", "isPlayer", "playerId", "$store", "state", "auth", "user", "id", "currentTurnInfo", "concat", "connectionText", "texts", "connected", "connecting", "disconnected", "error", "methods", "selectParticipant", "participant", "$emit", "handleParticipantClick", "handlePositionClick", "position", "targetPosition", "handleParticipantMove", "newPosition", "selectAction", "actionType", "showMessage", "showItemSelection", "showManeuverSelection", "confirmAction", "target", "resetActionState", "cancelAction", "nextTurn", "pauseCombat", "endCombat", "getHealthPercentage", "maxHP", "Math", "max", "currentHP", "getSanityPercentage", "maxSAN", "currentSAN", "getStatusEffectName", "effect", "names", "bleeding", "poisoned", "stunned", "frightened", "blessed", "cursed", "prone", "grappled", "unconscious", "message", "showCombatResult", "closeResult", "handleConnectionLost", "startReconnectAttempts", "_this", "attemptReconnect", "handleReconnectFailed", "setTimeout", "handleReconnectSuccess", "title", "details", "label", "value", "watch", "handler", "newData", "deep", "immediate", "newVal", "document", "body", "style", "overflow", "beforeUnmount"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\ForcedCombatMode.vue"], "sourcesContent": ["<template>\r\n  <!-- 强制战斗模式全屏覆盖 -->\r\n  <div class=\"forced-combat-overlay\" v-if=\"isActive\">\r\n    <!-- 背景遮罩 -->\r\n    <div class=\"combat-backdrop\"></div>\r\n    \r\n    <!-- 战斗界面容器 -->\r\n    <div class=\"combat-container\">\r\n      <!-- 顶部状态栏 -->\r\n      <div class=\"combat-status-bar\">\r\n        <div class=\"status-left\">\r\n          <div class=\"combat-indicator\">\r\n            <i class=\"fas fa-sword\"></i>\r\n            <span class=\"combat-text\">战斗模式</span>\r\n            <div class=\"pulse-ring\"></div>\r\n          </div>\r\n          <div class=\"round-info\">\r\n            <span class=\"round-number\">第 {{ currentRound }} 轮</span>\r\n            <span class=\"turn-info\">{{ currentTurnInfo }}</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"status-right\">\r\n          <div class=\"connection-status\" :class=\"connectionStatus\">\r\n            <i class=\"fas fa-wifi\"></i>\r\n            <span>{{ connectionText }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 主战斗区域 -->\r\n      <div class=\"combat-main-area\">\r\n        <!-- 战场视图 -->\r\n        <div class=\"battlefield-container\">\r\n          <BattlefieldGrid\r\n            :characters=\"participants?.filter(p => p.isPlayer) || []\"\r\n            :monsters=\"participants?.filter(p => !p.isPlayer) || []\"\r\n            :selected-character=\"selectedParticipant?.isPlayer ? selectedParticipant : null\"\r\n            :selected-monster=\"!selectedParticipant?.isPlayer ? selectedParticipant : null\"\r\n            :current-turn=\"currentTurn\"\r\n            :target-participant=\"targetParticipant\"\r\n            :battlefield-size=\"battlefieldSize\"\r\n            @participant-clicked=\"handleParticipantClick\"\r\n            @position-clicked=\"handlePositionClick\"\r\n            @participant-moved=\"handleParticipantMove\"\r\n          />\r\n        </div>\r\n\r\n        <!-- 右侧信息面板 -->\r\n        <div class=\"info-panel\">\r\n          <!-- 当前角色信息 -->\r\n          <div class=\"current-character-panel\" v-if=\"currentParticipant\">\r\n            <div class=\"panel-header\">\r\n              <h3>{{ currentParticipant.name }}</h3>\r\n              <div class=\"character-type\" :class=\"{ player: currentParticipant.isPlayer }\">\r\n                {{ currentParticipant.isPlayer ? '玩家' : 'NPC' }}\r\n              </div>\r\n            </div>\r\n            \r\n            <div class=\"character-stats\">\r\n              <div class=\"stat-bar\">\r\n                <label>生命值</label>\r\n                <div class=\"progress-bar health\">\r\n                  <div \r\n                    class=\"progress-fill\" \r\n                    :style=\"{ width: `${getHealthPercentage(currentParticipant)}%` }\"\r\n                  ></div>\r\n                  <span class=\"progress-text\">\r\n                    {{ currentParticipant.currentHP }}/{{ currentParticipant.maxHP }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div class=\"stat-bar\" v-if=\"currentParticipant.currentSAN\">\r\n                <label>理智值</label>\r\n                <div class=\"progress-bar sanity\">\r\n                  <div \r\n                    class=\"progress-fill\" \r\n                    :style=\"{ width: `${getSanityPercentage(currentParticipant)}%` }\"\r\n                  ></div>\r\n                  <span class=\"progress-text\">\r\n                    {{ currentParticipant.currentSAN }}/{{ currentParticipant.maxSAN }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div class=\"status-effects\" v-if=\"currentParticipant.conditions?.length\">\r\n                <label>状态效果</label>\r\n                <div class=\"effects-list\">\r\n                  <span \r\n                    v-for=\"effect in currentParticipant.conditions\" \r\n                    :key=\"effect\"\r\n                    class=\"effect-badge\"\r\n                    :class=\"effect\"\r\n                  >\r\n                    {{ getStatusEffectName(effect) }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- 先攻顺序 -->\r\n          <div class=\"initiative-panel\">\r\n            <div class=\"panel-header\">\r\n              <h3>先攻顺序</h3>\r\n            </div>\r\n            <div class=\"initiative-list\">\r\n              <div \r\n                v-for=\"(participant, index) in participants\" \r\n                :key=\"participant.id\"\r\n                class=\"initiative-item\"\r\n                :class=\"{ \r\n                  current: index === currentTurn,\r\n                  player: participant.isPlayer,\r\n                  selected: selectedParticipant?.id === participant.id\r\n                }\"\r\n                @click=\"selectParticipant(participant)\"\r\n              >\r\n                <div class=\"initiative-number\">{{ participant.initiative }}</div>\r\n                <div class=\"participant-info\">\r\n                  <span class=\"participant-name\">{{ participant.name }}</span>\r\n                  <div class=\"participant-health\">\r\n                    {{ participant.currentHP }}/{{ participant.maxHP }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"turn-indicator\" v-if=\"index === currentTurn\">\r\n                  <i class=\"fas fa-play\"></i>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 底部操作栏 -->\r\n      <div class=\"combat-action-bar\">\r\n        <!-- 玩家操作区 (仅当前回合玩家可见) -->\r\n        <div class=\"player-actions\" v-if=\"isPlayerTurn && !isKeeper\">\r\n          <div class=\"action-section\">\r\n            <h4>选择行动</h4>\r\n            <div class=\"action-buttons\">\r\n              <button \r\n                @click=\"selectAction('attack')\" \r\n                class=\"action-btn attack\"\r\n                :class=\"{ active: selectedAction === 'attack' }\"\r\n              >\r\n                <i class=\"fas fa-sword\"></i>\r\n                <span>攻击</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('defend')\" \r\n                class=\"action-btn defend\"\r\n                :class=\"{ active: selectedAction === 'defend' }\"\r\n              >\r\n                <i class=\"fas fa-shield\"></i>\r\n                <span>防御</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('move')\" \r\n                class=\"action-btn move\"\r\n                :class=\"{ active: selectedAction === 'move' }\"\r\n              >\r\n                <i class=\"fas fa-running\"></i>\r\n                <span>移动</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('item')\" \r\n                class=\"action-btn item\"\r\n                :class=\"{ active: selectedAction === 'item' }\"\r\n              >\r\n                <i class=\"fas fa-backpack\"></i>\r\n                <span>道具</span>\r\n              </button>\r\n              <button \r\n                @click=\"selectAction('maneuver')\" \r\n                class=\"action-btn maneuver\"\r\n                :class=\"{ active: selectedAction === 'maneuver' }\"\r\n              >\r\n                <i class=\"fas fa-fist-raised\"></i>\r\n                <span>战技</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 确认操作 -->\r\n          <div class=\"action-confirm\" v-if=\"selectedAction\">\r\n            <button @click=\"confirmAction\" class=\"btn-confirm\">\r\n              <i class=\"fas fa-check\"></i>\r\n              确认行动\r\n            </button>\r\n            <button @click=\"cancelAction\" class=\"btn-cancel\">\r\n              <i class=\"fas fa-times\"></i>\r\n              取消\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 等待提示 (非当前回合玩家) -->\r\n        <div class=\"waiting-indicator\" v-else-if=\"!isKeeper\">\r\n          <div class=\"waiting-content\">\r\n            <div class=\"spinner\"></div>\r\n            <div class=\"waiting-text\">\r\n              <h4>等待其他玩家行动</h4>\r\n              <p>当前回合: {{ currentParticipant?.name || '未知' }}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- KP控制区 -->\r\n        <div class=\"keeper-controls\" v-if=\"isKeeper\">\r\n          <div class=\"control-section\">\r\n            <h4>KP控制</h4>\r\n            <div class=\"keeper-buttons\">\r\n              <button @click=\"nextTurn\" class=\"keeper-btn\">\r\n                <i class=\"fas fa-forward\"></i>\r\n                下一回合\r\n              </button>\r\n              <button @click=\"pauseCombat\" class=\"keeper-btn\">\r\n                <i class=\"fas fa-pause\"></i>\r\n                暂停战斗\r\n              </button>\r\n              <button @click=\"endCombat\" class=\"keeper-btn danger\">\r\n                <i class=\"fas fa-stop\"></i>\r\n                结束战斗\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战斗结果弹窗 -->\r\n    <div class=\"combat-result-modal\" v-if=\"showResult\">\r\n      <div class=\"result-content\">\r\n        <div class=\"result-header\">\r\n          <h2>{{ resultData.title }}</h2>\r\n        </div>\r\n        <div class=\"result-body\">\r\n          <p>{{ resultData.message }}</p>\r\n          <div class=\"result-details\" v-if=\"resultData.details\">\r\n            <div v-for=\"detail in resultData.details\" :key=\"detail.label\">\r\n              <strong>{{ detail.label }}:</strong> {{ detail.value }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"result-actions\">\r\n          <button @click=\"closeResult\" class=\"btn-primary\">确定</button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 断线重连提示 -->\r\n    <div class=\"reconnect-overlay\" v-if=\"showReconnect\">\r\n      <div class=\"reconnect-content\">\r\n        <div class=\"reconnect-icon\">\r\n          <i class=\"fas fa-wifi\"></i>\r\n        </div>\r\n        <h3>连接中断</h3>\r\n        <p>正在尝试重新连接到战斗服务器...</p>\r\n        <div class=\"reconnect-progress\">\r\n          <div class=\"progress-bar\">\r\n            <div class=\"progress-fill\" :style=\"{ width: `${reconnectProgress}%` }\"></div>\r\n          </div>\r\n          <span>{{ reconnectAttempts }}/{{ maxReconnectAttempts }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport BattlefieldGrid from './BattlefieldGrid.vue'\r\n\r\nexport default {\r\n  name: 'ForcedCombatMode',\r\n  components: {\r\n    BattlefieldGrid\r\n  },\r\n  props: {\r\n    isActive: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    isKeeper: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    combatData: {\r\n      type: Object,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 战斗状态\r\n      participants: [],\r\n      currentTurn: 0,\r\n      currentRound: 1,\r\n      selectedParticipant: null,\r\n      targetParticipant: null,\r\n      \r\n      // 玩家操作\r\n      selectedAction: null,\r\n      actionData: null,\r\n      \r\n      // 界面状态\r\n      showResult: false,\r\n      resultData: {},\r\n      showReconnect: false,\r\n      reconnectProgress: 0,\r\n      reconnectAttempts: 0,\r\n      maxReconnectAttempts: 5,\r\n      \r\n      // 连接状态\r\n      connectionStatus: 'connected',\r\n      \r\n      // 战场配置\r\n      battlefieldSize: { width: 20, height: 15 }\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    currentParticipant() {\r\n      return this.participants[this.currentTurn] || null\r\n    },\r\n    \r\n    isPlayerTurn() {\r\n      return this.currentParticipant?.isPlayer && \r\n             this.currentParticipant?.playerId === this.$store.state.auth.user?.id\r\n    },\r\n    \r\n    currentTurnInfo() {\r\n      if (!this.currentParticipant) return '等待开始'\r\n      return `${this.currentParticipant.name} 的回合`\r\n    },\r\n    \r\n    connectionText() {\r\n      const texts = {\r\n        connected: '已连接',\r\n        connecting: '连接中',\r\n        disconnected: '已断开',\r\n        error: '连接错误'\r\n      }\r\n      return texts[this.connectionStatus] || '未知状态'\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 参与者操作\r\n    selectParticipant(participant) {\r\n      this.selectedParticipant = participant\r\n      this.$emit('participant-selected', participant)\r\n    },\r\n    \r\n    handleParticipantClick(participant) {\r\n      if (this.selectedAction === 'attack') {\r\n        this.targetParticipant = participant\r\n      } else {\r\n        this.selectParticipant(participant)\r\n      }\r\n    },\r\n    \r\n    handlePositionClick(position) {\r\n      if (this.selectedAction === 'move') {\r\n        this.actionData = { targetPosition: position }\r\n      }\r\n    },\r\n    \r\n    handleParticipantMove(participant, newPosition) {\r\n      this.$emit('participant-moved', participant, newPosition)\r\n    },\r\n    \r\n    // 行动选择\r\n    selectAction(actionType) {\r\n      this.selectedAction = actionType\r\n      this.actionData = null\r\n      this.targetParticipant = null\r\n      \r\n      // 根据行动类型设置界面状态\r\n      switch (actionType) {\r\n        case 'attack':\r\n          this.showMessage('请选择攻击目标')\r\n          break\r\n        case 'move':\r\n          this.showMessage('请选择移动位置')\r\n          break\r\n        case 'defend':\r\n          this.actionData = { type: 'full_defense' }\r\n          break\r\n        case 'item':\r\n          this.showItemSelection()\r\n          break\r\n        case 'maneuver':\r\n          this.showManeuverSelection()\r\n          break\r\n      }\r\n    },\r\n    \r\n    confirmAction() {\r\n      if (!this.selectedAction) return\r\n      \r\n      const actionData = {\r\n        type: this.selectedAction,\r\n        participant: this.currentParticipant,\r\n        target: this.targetParticipant,\r\n        data: this.actionData\r\n      }\r\n      \r\n      this.$emit('action-confirmed', actionData)\r\n      this.resetActionState()\r\n    },\r\n    \r\n    cancelAction() {\r\n      this.resetActionState()\r\n    },\r\n    \r\n    resetActionState() {\r\n      this.selectedAction = null\r\n      this.actionData = null\r\n      this.targetParticipant = null\r\n    },\r\n    \r\n    // KP控制\r\n    nextTurn() {\r\n      this.$emit('next-turn')\r\n    },\r\n    \r\n    pauseCombat() {\r\n      this.$emit('pause-combat')\r\n    },\r\n    \r\n    endCombat() {\r\n      this.$emit('end-combat')\r\n    },\r\n    \r\n    // 界面辅助\r\n    getHealthPercentage(participant) {\r\n      if (!participant.maxHP) return 0\r\n      return Math.max(0, (participant.currentHP / participant.maxHP) * 100)\r\n    },\r\n    \r\n    getSanityPercentage(participant) {\r\n      if (!participant.maxSAN) return 0\r\n      return Math.max(0, (participant.currentSAN / participant.maxSAN) * 100)\r\n    },\r\n    \r\n    getStatusEffectName(effect) {\r\n      const names = {\r\n        bleeding: '流血',\r\n        poisoned: '中毒',\r\n        stunned: '眩晕',\r\n        frightened: '恐惧',\r\n        blessed: '祝福',\r\n        cursed: '诅咒',\r\n        prone: '倒地',\r\n        grappled: '被擒抱',\r\n        unconscious: '昏迷'\r\n      }\r\n      return names[effect] || effect\r\n    },\r\n    \r\n    showMessage(message) {\r\n      // 显示临时消息\r\n      this.$emit('show-message', message)\r\n    },\r\n    \r\n    showItemSelection() {\r\n      // 显示道具选择界面\r\n      this.$emit('show-item-selection')\r\n    },\r\n    \r\n    showManeuverSelection() {\r\n      // 显示战技选择界面\r\n      this.$emit('show-maneuver-selection')\r\n    },\r\n    \r\n    // 结果处理\r\n    showCombatResult(resultData) {\r\n      this.resultData = resultData\r\n      this.showResult = true\r\n    },\r\n    \r\n    closeResult() {\r\n      this.showResult = false\r\n      this.resultData = {}\r\n    },\r\n    \r\n    // 连接管理\r\n    handleConnectionLost() {\r\n      this.connectionStatus = 'disconnected'\r\n      this.showReconnect = true\r\n      this.startReconnectAttempts()\r\n    },\r\n    \r\n    startReconnectAttempts() {\r\n      this.reconnectAttempts = 0\r\n      this.reconnectProgress = 0\r\n      \r\n      const attemptReconnect = () => {\r\n        if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n          this.handleReconnectFailed()\r\n          return\r\n        }\r\n        \r\n        this.reconnectAttempts++\r\n        this.reconnectProgress = (this.reconnectAttempts / this.maxReconnectAttempts) * 100\r\n        \r\n        // 尝试重连\r\n        this.$emit('reconnect-attempt')\r\n        \r\n        setTimeout(() => {\r\n          if (this.connectionStatus !== 'connected') {\r\n            attemptReconnect()\r\n          } else {\r\n            this.handleReconnectSuccess()\r\n          }\r\n        }, 2000)\r\n      }\r\n      \r\n      attemptReconnect()\r\n    },\r\n    \r\n    handleReconnectSuccess() {\r\n      this.showReconnect = false\r\n      this.connectionStatus = 'connected'\r\n      this.showMessage('重新连接成功')\r\n    },\r\n    \r\n    handleReconnectFailed() {\r\n      this.showReconnect = false\r\n      this.showCombatResult({\r\n        title: '连接失败',\r\n        message: '无法重新连接到战斗服务器，战斗将被强制结束。',\r\n        details: [\r\n          { label: '错误代码', value: 'CONNECTION_TIMEOUT' },\r\n          { label: '尝试次数', value: this.maxReconnectAttempts }\r\n        ]\r\n      })\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    combatData: {\r\n      handler(newData) {\r\n        if (newData) {\r\n          this.participants = newData.participants || []\r\n          this.currentTurn = newData.currentTurn || 0\r\n          this.currentRound = newData.currentRound || 1\r\n          this.battlefieldSize = newData.battlefieldSize || { width: 20, height: 15 }\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    },\r\n    \r\n    isActive(newVal) {\r\n      if (newVal) {\r\n        // 进入强制战斗模式\r\n        document.body.style.overflow = 'hidden'\r\n        this.$emit('combat-mode-entered')\r\n      } else {\r\n        // 退出强制战斗模式\r\n        document.body.style.overflow = ''\r\n        this.$emit('combat-mode-exited')\r\n      }\r\n    }\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    // 清理样式\r\n    document.body.style.overflow = ''\r\n  }\r\n}\r\n</script>"], "mappings": ";;AAgRA,OAAOA,eAAc,MAAO,uBAAsB;AAElD,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,UAAU,EAAE;IACVF,eAAc,EAAdA;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,QAAQ,EAAE;MACRC,IAAI,EAAEC,OAAO;MACb,WAAS;IACX,CAAC;IACDC,QAAQ,EAAE;MACRF,IAAI,EAAEC,OAAO;MACb,WAAS;IACX,CAAC;IACDE,UAAU,EAAE;MACVH,IAAI,EAAEI,MAAM;MACZ,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAS,CAAC,CAAC;MAAA;IACpB;EACF,CAAC;EACDC,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE,CAAC;MACdC,YAAY,EAAE,CAAC;MACfC,mBAAmB,EAAE,IAAI;MACzBC,iBAAiB,EAAE,IAAI;MAEvB;MACAC,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE,IAAI;MAEhB;MACAC,UAAU,EAAE,KAAK;MACjBC,UAAU,EAAE,CAAC,CAAC;MACdC,aAAa,EAAE,KAAK;MACpBC,iBAAiB,EAAE,CAAC;MACpBC,iBAAiB,EAAE,CAAC;MACpBC,oBAAoB,EAAE,CAAC;MAEvB;MACAC,gBAAgB,EAAE,WAAW;MAE7B;MACAC,eAAe,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG;IAC3C;EACF,CAAC;EAEDC,QAAQ,EAAE;IACRC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAAClB,YAAY,CAAC,IAAI,CAACC,WAAW,KAAK,IAAG;IACnD,CAAC;IAEDkB,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;MACb,OAAO,EAAAF,qBAAA,OAAI,CAACF,kBAAkB,cAAAE,qBAAA,uBAAvBA,qBAAA,CAAyBG,QAAO,KAChC,EAAAF,sBAAA,OAAI,CAACH,kBAAkB,cAAAG,sBAAA,uBAAvBA,sBAAA,CAAyBG,QAAO,QAAAF,qBAAA,GAAM,IAAI,CAACG,MAAM,CAACC,KAAK,CAACC,IAAI,CAACC,IAAI,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BO,EAAC;IAC7E,CAAC;IAEDC,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAACZ,kBAAkB,EAAE,OAAO,MAAK;MAC1C,UAAAa,MAAA,CAAU,IAAI,CAACb,kBAAkB,CAAC7B,IAAI;IACxC,CAAC;IAED2C,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAMC,KAAI,GAAI;QACZC,SAAS,EAAE,KAAK;QAChBC,UAAU,EAAE,KAAK;QACjBC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE;MACT;MACA,OAAOJ,KAAK,CAAC,IAAI,CAACpB,gBAAgB,KAAK,MAAK;IAC9C;EACF,CAAC;EAEDyB,OAAO,EAAE;IACP;IACAC,iBAAiB,WAAjBA,iBAAiBA,CAACC,WAAW,EAAE;MAC7B,IAAI,CAACrC,mBAAkB,GAAIqC,WAAU;MACrC,IAAI,CAACC,KAAK,CAAC,sBAAsB,EAAED,WAAW;IAChD,CAAC;IAEDE,sBAAsB,WAAtBA,sBAAsBA,CAACF,WAAW,EAAE;MAClC,IAAI,IAAI,CAACnC,cAAa,KAAM,QAAQ,EAAE;QACpC,IAAI,CAACD,iBAAgB,GAAIoC,WAAU;MACrC,OAAO;QACL,IAAI,CAACD,iBAAiB,CAACC,WAAW;MACpC;IACF,CAAC;IAEDG,mBAAmB,WAAnBA,mBAAmBA,CAACC,QAAQ,EAAE;MAC5B,IAAI,IAAI,CAACvC,cAAa,KAAM,MAAM,EAAE;QAClC,IAAI,CAACC,UAAS,GAAI;UAAEuC,cAAc,EAAED;QAAS;MAC/C;IACF,CAAC;IAEDE,qBAAqB,WAArBA,qBAAqBA,CAACN,WAAW,EAAEO,WAAW,EAAE;MAC9C,IAAI,CAACN,KAAK,CAAC,mBAAmB,EAAED,WAAW,EAAEO,WAAW;IAC1D,CAAC;IAED;IACAC,YAAY,WAAZA,YAAYA,CAACC,UAAU,EAAE;MACvB,IAAI,CAAC5C,cAAa,GAAI4C,UAAS;MAC/B,IAAI,CAAC3C,UAAS,GAAI,IAAG;MACrB,IAAI,CAACF,iBAAgB,GAAI,IAAG;;MAE5B;MACA,QAAQ6C,UAAU;QAChB,KAAK,QAAQ;UACX,IAAI,CAACC,WAAW,CAAC,SAAS;UAC1B;QACF,KAAK,MAAM;UACT,IAAI,CAACA,WAAW,CAAC,SAAS;UAC1B;QACF,KAAK,QAAQ;UACX,IAAI,CAAC5C,UAAS,GAAI;YAAEb,IAAI,EAAE;UAAe;UACzC;QACF,KAAK,MAAM;UACT,IAAI,CAAC0D,iBAAiB,CAAC;UACvB;QACF,KAAK,UAAU;UACb,IAAI,CAACC,qBAAqB,CAAC;UAC3B;MACJ;IACF,CAAC;IAEDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAAChD,cAAc,EAAE;MAE1B,IAAMC,UAAS,GAAI;QACjBb,IAAI,EAAE,IAAI,CAACY,cAAc;QACzBmC,WAAW,EAAE,IAAI,CAACtB,kBAAkB;QACpCoC,MAAM,EAAE,IAAI,CAAClD,iBAAiB;QAC9BL,IAAI,EAAE,IAAI,CAACO;MACb;MAEA,IAAI,CAACmC,KAAK,CAAC,kBAAkB,EAAEnC,UAAU;MACzC,IAAI,CAACiD,gBAAgB,CAAC;IACxB,CAAC;IAEDC,YAAY,WAAZA,YAAYA,CAAA,EAAG;MACb,IAAI,CAACD,gBAAgB,CAAC;IACxB,CAAC;IAEDA,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAClD,cAAa,GAAI,IAAG;MACzB,IAAI,CAACC,UAAS,GAAI,IAAG;MACrB,IAAI,CAACF,iBAAgB,GAAI,IAAG;IAC9B,CAAC;IAED;IACAqD,QAAQ,WAARA,QAAQA,CAAA,EAAG;MACT,IAAI,CAAChB,KAAK,CAAC,WAAW;IACxB,CAAC;IAEDiB,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACjB,KAAK,CAAC,cAAc;IAC3B,CAAC;IAEDkB,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAAClB,KAAK,CAAC,YAAY;IACzB,CAAC;IAED;IACAmB,mBAAmB,WAAnBA,mBAAmBA,CAACpB,WAAW,EAAE;MAC/B,IAAI,CAACA,WAAW,CAACqB,KAAK,EAAE,OAAO;MAC/B,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGvB,WAAW,CAACwB,SAAQ,GAAIxB,WAAW,CAACqB,KAAK,GAAI,GAAG;IACtE,CAAC;IAEDI,mBAAmB,WAAnBA,mBAAmBA,CAACzB,WAAW,EAAE;MAC/B,IAAI,CAACA,WAAW,CAAC0B,MAAM,EAAE,OAAO;MAChC,OAAOJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGvB,WAAW,CAAC2B,UAAS,GAAI3B,WAAW,CAAC0B,MAAM,GAAI,GAAG;IACxE,CAAC;IAEDE,mBAAmB,WAAnBA,mBAAmBA,CAACC,MAAM,EAAE;MAC1B,IAAMC,KAAI,GAAI;QACZC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,IAAI;QACZC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE,KAAK;QACfC,WAAW,EAAE;MACf;MACA,OAAOT,KAAK,CAACD,MAAM,KAAKA,MAAK;IAC/B,CAAC;IAEDnB,WAAW,WAAXA,WAAWA,CAAC8B,OAAO,EAAE;MACnB;MACA,IAAI,CAACvC,KAAK,CAAC,cAAc,EAAEuC,OAAO;IACpC,CAAC;IAED7B,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB;MACA,IAAI,CAACV,KAAK,CAAC,qBAAqB;IAClC,CAAC;IAEDW,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB;MACA,IAAI,CAACX,KAAK,CAAC,yBAAyB;IACtC,CAAC;IAED;IACAwC,gBAAgB,WAAhBA,gBAAgBA,CAACzE,UAAU,EAAE;MAC3B,IAAI,CAACA,UAAS,GAAIA,UAAS;MAC3B,IAAI,CAACD,UAAS,GAAI,IAAG;IACvB,CAAC;IAED2E,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAAC3E,UAAS,GAAI,KAAI;MACtB,IAAI,CAACC,UAAS,GAAI,CAAC;IACrB,CAAC;IAED;IACA2E,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrB,IAAI,CAACtE,gBAAe,GAAI,cAAa;MACrC,IAAI,CAACJ,aAAY,GAAI,IAAG;MACxB,IAAI,CAAC2E,sBAAsB,CAAC;IAC9B,CAAC;IAEDA,sBAAsB,WAAtBA,sBAAsBA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACvB,IAAI,CAAC1E,iBAAgB,GAAI;MACzB,IAAI,CAACD,iBAAgB,GAAI;MAEzB,IAAM4E,iBAAe,GAAI,SAAnBA,gBAAeA,CAAA,EAAU;QAC7B,IAAID,KAAI,CAAC1E,iBAAgB,IAAK0E,KAAI,CAACzE,oBAAoB,EAAE;UACvDyE,KAAI,CAACE,qBAAqB,CAAC;UAC3B;QACF;QAEAF,KAAI,CAAC1E,iBAAiB,EAAC;QACvB0E,KAAI,CAAC3E,iBAAgB,GAAK2E,KAAI,CAAC1E,iBAAgB,GAAI0E,KAAI,CAACzE,oBAAoB,GAAI,GAAE;;QAElF;QACAyE,KAAI,CAAC5C,KAAK,CAAC,mBAAmB;QAE9B+C,UAAU,CAAC,YAAM;UACf,IAAIH,KAAI,CAACxE,gBAAe,KAAM,WAAW,EAAE;YACzCyE,iBAAgB,CAAC;UACnB,OAAO;YACLD,KAAI,CAACI,sBAAsB,CAAC;UAC9B;QACF,CAAC,EAAE,IAAI;MACT;MAEAH,iBAAgB,CAAC;IACnB,CAAC;IAEDG,sBAAsB,WAAtBA,sBAAsBA,CAAA,EAAG;MACvB,IAAI,CAAChF,aAAY,GAAI,KAAI;MACzB,IAAI,CAACI,gBAAe,GAAI,WAAU;MAClC,IAAI,CAACqC,WAAW,CAAC,QAAQ;IAC3B,CAAC;IAEDqC,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAAC9E,aAAY,GAAI,KAAI;MACzB,IAAI,CAACwE,gBAAgB,CAAC;QACpBS,KAAK,EAAE,MAAM;QACbV,OAAO,EAAE,wBAAwB;QACjCW,OAAO,EAAE,CACP;UAAEC,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAqB,CAAC,EAC9C;UAAED,KAAK,EAAE,MAAM;UAAEC,KAAK,EAAE,IAAI,CAACjF;QAAqB;MAEtD,CAAC;IACH;EACF,CAAC;EAEDkF,KAAK,EAAE;IACLlG,UAAU,EAAE;MACVmG,OAAO,WAAPA,OAAOA,CAACC,OAAO,EAAE;QACf,IAAIA,OAAO,EAAE;UACX,IAAI,CAAChG,YAAW,GAAIgG,OAAO,CAAChG,YAAW,IAAK,EAAC;UAC7C,IAAI,CAACC,WAAU,GAAI+F,OAAO,CAAC/F,WAAU,IAAK;UAC1C,IAAI,CAACC,YAAW,GAAI8F,OAAO,CAAC9F,YAAW,IAAK;UAC5C,IAAI,CAACY,eAAc,GAAIkF,OAAO,CAAClF,eAAc,IAAK;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG;QAC5E;MACF,CAAC;MACDiF,IAAI,EAAE,IAAI;MACVC,SAAS,EAAE;IACb,CAAC;IAED1G,QAAQ,WAARA,QAAQA,CAAC2G,MAAM,EAAE;MACf,IAAIA,MAAM,EAAE;QACV;QACAC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAO,GAAI,QAAO;QACtC,IAAI,CAAC9D,KAAK,CAAC,qBAAqB;MAClC,OAAO;QACL;QACA2D,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAO,GAAI,EAAC;QAChC,IAAI,CAAC9D,KAAK,CAAC,oBAAoB;MACjC;IACF;EACF,CAAC;EAED+D,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd;IACAJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAO,GAAI,EAAC;EAClC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}