import { createStore } from 'vuex'
import axios from 'axios'
import { emitter } from '../main.js'
import { v4 as uuidv4 } from 'uuid'
import apiService from '@/services/api'
import storageManager from '@/utils/storage'
import persistencePlugin from './plugins/persistencePlugin'
import auth from './modules/auth'
import rooms from './modules/rooms'

const API_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000'

// 安全地从存储中获取数据的辅助函数
function safeGetFromStorage(key, defaultValue = null) {
  try {
    return storageManager.getItem(key) || defaultValue
  } catch (error) {
    console.warn(`[Store] 获取存储项 ${key} 失败:`, error)
    return defaultValue
  }
}

function safeGetObjectFromStorage(key, defaultValue = null) {
  try {
    return storageManager.getObject(key) || defaultValue
  } catch (error) {
    console.warn(`[Store] 获取存储对象 ${key} 失败:`, error)
    return defaultValue
  }
}

export default createStore({
  state: {
    // 用户认证状态
    user: safeGetObjectFromStorage('user', null),
    token: safeGetFromStorage('token', ''),
    isAuthenticated: false,
    authLoading: false,
    authError: null,
    tokenExpiry: safeGetFromStorage('tokenExpiry', null),

    // 角色数据
    characters: safeGetObjectFromStorage('characters', []),
    currentCharacterId: safeGetFromStorage('currentCharacterId', null),
    messages: [],

    // 应用设置
    theme: storageManager.getItem('theme') || 'dark',
    aiSettings: {
      apiKey: '',
      apiUrl: '',
      modelName: '',
      isApiValid: false
    },

    // 同步状态
    characterSyncStatus: {},
    localCharacterCache: safeGetObjectFromStorage('localCharacterCache', {}),
    lastSyncTime: safeGetFromStorage('lastSyncTime', null),

    // 场景和笔记
    scenes: safeGetObjectFromStorage('scenes', []),
    currentSceneIndex: parseInt(safeGetFromStorage('currentSceneIndex', '0')),
    notes: safeGetObjectFromStorage('notes', {}),

    // 系统状态
    apiValid: true,
    isOfflineMode: false,
    connectionStatus: 'online',
    storageStatus: null, // 添加存储状态跟踪

    // 游戏数据 (保留基本结构，供其他功能使用)
    occupations: {},
    skills: {},
    weapons: {},
    armors: {},

    // 存档系统
    saves: [],
    currentSave: null,
    loadingSaves: false,
    saveError: null,
    saveHistory: [],
    loadingSaveHistory: false,
    isPreviewMode: false,
    globalNotice: null
  },
  getters: {
    // 认证相关
    isAuthenticated: state => {
      if (!state.token || !state.user) return false;

      // 检查token是否过期
      if (state.tokenExpiry) {
        const now = new Date().getTime();
        const expiry = new Date(state.tokenExpiry).getTime();
        if (now >= expiry) {
          return false;
        }
      }

      return true;
    },

    currentUser: state => state.user,
    authLoading: state => state.authLoading,
    authError: state => state.authError,
    tokenValid: state => {
      if (!state.token) return false;
      if (!state.tokenExpiry) return true; // 如果没有过期时间，假设有效

      const now = new Date().getTime();
      const expiry = new Date(state.tokenExpiry).getTime();
      return now < expiry;
    },

    // 角色相关
    userCharacters: state => state.characters,
    currentCharacter: state => {
      if (!state.currentCharacterId) return null;
      return state.characters.find(c => c.id == state.currentCharacterId) || null;
    },
    characterSyncStatus: state => characterId => {
      return state.characterSyncStatus[characterId] || { lastSync: null, syncing: false };
    },
    getLocalCharacterData: state => characterId => {
      return state.localCharacterCache[characterId] || null;
    },

    // 消息相关
    roomMessages: state => state.messages,

    // 场景和笔记
    allScenes: state => state.scenes,
    currentScene: state => state.scenes[state.currentSceneIndex] || null,
    characterNotes: state => characterId => {
      return state.notes[characterId] || [];
    },
    globalNotes: state => state.notes['global'] || [],

    // 系统状态
    currentTheme: state => state.theme,
    isDarkTheme: state => state.theme === 'dark',
    isOfflineMode: state => state.isOfflineMode,
    connectionStatus: state => state.connectionStatus,
    lastSyncTime: state => state.lastSyncTime,
    storageStatus: state => state.storageStatus,
    isStorageDegraded: state => state.storageStatus?.isDegraded || false,

    // 存档相关
    gameSaves: state => state.saves,
    currentGameSave: state => state.currentSave,
    isLoadingSaves: state => state.loadingSaves,
    saveError: state => state.saveError,
    saveHistory: state => state.saveHistory,
    isLoadingSaveHistory: state => state.loadingSaveHistory,
    isPreviewMode: state => state.isPreviewMode,
    globalNotice: state => state.globalNotice
  },
  mutations: {
    // 认证相关mutations
    SET_USER(state, user) {
      state.user = user;
      state.isAuthenticated = !!user;
      try {
        if (user) {
          storageManager.setObject('user', user);
        } else {
          storageManager.removeItem('user');
        }
      } catch (error) {
        console.warn('[Store] 保存用户信息失败:', error);
      }
    },

    SET_TOKEN(state, token) {
      state.token = token;
      try {
        if (token) {
          storageManager.setItem('token', token);

          // 解析JWT token获取过期时间
          try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            if (payload.exp) {
              const expiry = new Date(payload.exp * 1000).toISOString();
              state.tokenExpiry = expiry;
              storageManager.setItem('tokenExpiry', expiry);
            }
          } catch (e) {
            console.warn('无法解析token过期时间:', e);
          }
        } else {
          storageManager.removeItem('token');
          storageManager.removeItem('tokenExpiry');
          state.tokenExpiry = null;
        }
      } catch (error) {
        console.warn('[Store] 保存token失败:', error);
      }
    },

    SET_AUTH_LOADING(state, loading) {
      state.authLoading = loading;
    },

    SET_AUTH_ERROR(state, error) {
      state.authError = error;
    },

    CLEAR_AUTH(state) {
      state.user = null;
      state.token = '';
      state.tokenExpiry = null;
      state.isAuthenticated = false;
      state.authError = null;

      // 清除本地存储
      try {
        storageManager.removeItem('user');
        storageManager.removeItem('token');
        storageManager.removeItem('tokenExpiry');
      } catch (error) {
        console.warn('[Store] 清除认证信息失败:', error);
      }
    },

    SET_CONNECTION_STATUS(state, status) {
      state.connectionStatus = status;
      if (status === 'offline') {
        state.isOfflineMode = true;
      }
    },

    SET_LAST_SYNC_TIME(state, time) {
      state.lastSyncTime = time;
      try {
        storageManager.setItem('lastSyncTime', time);
      } catch (error) {
        console.warn('[Store] 保存同步时间失败:', error);
      }
    },

    SET_STORAGE_STATUS(state, status) {
      state.storageStatus = status;
    },
    UPDATE_USER_AVATAR(state, avatarUrl) {
      if (state.user) {
        state.user.avatar_url = avatarUrl;
        // 同时更新存储中的用户信息
        storageManager.setObject('user', state.user);
      }
    },
    SET_CHARACTERS(state, characters) {
      state.characters = characters;
      try {
        storageManager.setObject('characters', characters);
      } catch (error) {
        console.warn('[Store] 保存角色列表失败:', error);
      }
    },
    ADD_CHARACTER(state, character) {
      state.characters.push(character);
      try {
        storageManager.setObject('characters', state.characters);
      } catch (error) {
        console.warn('[Store] 保存新角色失败:', error);
      }
    },
    UPDATE_CHARACTER(state, updatedCharacter) {
      const index = state.characters.findIndex(c => c.id === updatedCharacter.id);
      if (index !== -1) {
        state.characters.splice(index, 1, updatedCharacter);
        try {
          storageManager.setObject('characters', state.characters);
          
          state.localCharacterCache[updatedCharacter.id] = {
            ...updatedCharacter,
            lastUpdated: new Date().toISOString()
          };
          storageManager.setObject('localCharacterCache', state.localCharacterCache);
        } catch (error) {
          console.warn('[Store] 保存角色更新失败:', error);
        }
        
        state.characterSyncStatus[updatedCharacter.id] = {
          lastSync: new Date().toISOString(),
          syncing: false
        };
      }
    },
    UPDATE_CHARACTER_FROM_SERVER(state, characterData) {
      const index = state.characters.findIndex(c => c.id === characterData.id);
      if (index !== -1) {
        const mergedCharacter = {
          ...state.characters[index],
          ...characterData,
          _localData: state.characters[index]._localData || {}
        };
        
        state.characters.splice(index, 1, mergedCharacter);
        
        storageManager.setObject('characters', state.characters);
        
        state.localCharacterCache[characterData.id] = {
          ...mergedCharacter,
          lastUpdated: new Date().toISOString()
        };
        storageManager.setObject('localCharacterCache', state.localCharacterCache);
        
        state.characterSyncStatus[characterData.id] = {
          lastSync: new Date().toISOString(),
          syncing: false
        };
      }
    },
    UPDATE_CHARACTER_ATTRIBUTE(state, { id, field, value }) {
      const index = state.characters.findIndex(c => c.id === id);
      if (index !== -1) {
        const updatedCharacter = { ...state.characters[index] };
        
        if (field.includes('.')) {
          const parts = field.split('.');
          let obj = updatedCharacter;
          for (let i = 0; i < parts.length - 1; i++) {
            if (!obj[parts[i]]) obj[parts[i]] = {};
            obj = obj[parts[i]];
          }
          obj[parts[parts.length - 1]] = value;
        } else {
          updatedCharacter[field] = value;
        }
        
        state.characters.splice(index, 1, updatedCharacter);
        
        storageManager.setObject('characters', state.characters);
        
        state.localCharacterCache[id] = {
          ...updatedCharacter,
          lastUpdated: new Date().toISOString()
        };
        storageManager.setObject('localCharacterCache', state.localCharacterCache);
      }
    },
    SET_CURRENT_CHARACTER(state, characterId) {
      state.currentCharacterId = characterId;
      storageManager.setItem('currentCharacterId', characterId);
    },
    DELETE_CHARACTER(state, characterId) {
      state.characters = state.characters.filter(c => c.id !== characterId);
      storageManager.setObject('characters', state.characters);
      
      if (state.localCharacterCache[characterId]) {
        delete state.localCharacterCache[characterId];
        storageManager.setObject('localCharacterCache', state.localCharacterCache);
      }
      
      if (state.characterSyncStatus[characterId]) {
        delete state.characterSyncStatus[characterId];
      }
    },
    UPDATE_CHARACTER_SYNC_STATUS(state, { characterId, status }) {
      state.characterSyncStatus[characterId] = {
        ...state.characterSyncStatus[characterId],
        ...status
      };
    },
    SET_ROOMS(state, rooms) {
      state.rooms = rooms;
    },
    SET_CURRENT_ROOM(state, roomData) {
      state.currentRoom = roomData;
    },
    CLEAR_CURRENT_ROOM(state) {
      state.currentRoom = null;
      state.messages = [];
    },
    ADD_MESSAGE(state, message) {
      console.log('添加消息到store:', message);
      
      if (!message.timestamp) {
        message.timestamp = new Date().toISOString();
      }
      
      if (message.type === 'chat' && !message.username) {
        message.username = message.user_id === 1 ? 'KP' : '玩家';
      }
      
      message.id = Date.now() + Math.random().toString(36).substr(2, 9);
      
      state.messages.push(message);
      
      if (state.messages.length > 1000) {
        state.messages = state.messages.slice(-500);
      }
      
      const roomId = state.currentRoom ? state.currentRoom.id : 'default';
      const roomMessages = storageManager.getObject(`room_messages_${roomId}`) || [];
      roomMessages.push(message);
      
      if (roomMessages.length > 200) {
        roomMessages.splice(0, roomMessages.length - 200);
      }
      
      storageManager.setObject(`room_messages_${roomId}`, roomMessages);
    },
    SET_MESSAGES(state, messages) {
      state.messages = messages;
      
      const roomId = state.currentRoom ? state.currentRoom.id : 'default';
      storageManager.setObject(`room_messages_${roomId}`, messages.slice(-200));
    },
    TOGGLE_THEME(state) {
      state.theme = state.theme === 'dark' ? 'light' : 'dark';
      storageManager.setItem('theme', state.theme);
    },
    CLEAR_MESSAGES(state) {
      state.messages = [];
      
      const roomId = state.currentRoom ? state.currentRoom.id : 'default';
      storageManager.removeItem(`room_messages_${roomId}`);
    },
    SET_IMPORTANT_MESSAGE(state, message) {
      state.importantMessage = message;
    },
    SET_API_VALID(state, isValid) {
      state.aiSettings.isApiValid = isValid;
    },
    UPDATE_AI_SETTINGS(state, settings) {
      state.aiSettings = {
        ...state.aiSettings,
        ...settings
      };
    },
    UPDATE_MESSAGE(state, { index, message }) {
      if (index >= 0 && index < state.messages.length) {
        state.messages.splice(index, 1, message);
        
        const roomId = state.currentRoom ? state.currentRoom.id : 'default';
        const roomMessages = storageManager.getObject(`room_messages_${roomId}`) || [];
        const msgIndex = roomMessages.findIndex(m => m.id === message.id);
        if (msgIndex !== -1) {
          roomMessages.splice(msgIndex, 1, message);
          storageManager.setObject(`room_messages_${roomId}`, roomMessages);
        }
      }
    },
    REMOVE_LAST_MESSAGE(state) {
      if (state.messages.length > 0) {
        state.messages.pop();
        
        const roomId = state.currentRoom ? state.currentRoom.id : 'default';
        const roomMessages = storageManager.getObject(`room_messages_${roomId}`) || [];
        
        if (roomMessages.length > 0) {
          roomMessages.pop();
          storageManager.setObject(`room_messages_${roomId}`, roomMessages);
        }
      }
    },
    SET_SCENES(state, scenes) {
      state.scenes = scenes;
      storageManager.setObject('scenes', scenes);
    },
    ADD_SCENE(state, scene) {
      state.scenes.push(scene);
      storageManager.setObject('scenes', state.scenes);
    },
    UPDATE_SCENE(state, { index, scene }) {
      if (index >= 0 && index < state.scenes.length) {
        state.scenes.splice(index, 1, scene);
        storageManager.setObject('scenes', state.scenes);
      }
    },
    DELETE_SCENE(state, index) {
      if (index >= 0 && index < state.scenes.length) {
        state.scenes.splice(index, 1);
        storageManager.setObject('scenes', state.scenes);
        
        if (index <= state.currentSceneIndex && state.currentSceneIndex > 0) {
          state.currentSceneIndex--;
          storageManager.setItem('currentSceneIndex', state.currentSceneIndex.toString());
        }
      }
    },
    SET_CURRENT_SCENE_INDEX(state, index) {
      if (index >= 0 && index < state.scenes.length) {
        state.currentSceneIndex = index;
        storageManager.setItem('currentSceneIndex', index.toString());
      }
    },
    SET_NOTES(state, { characterId, notes }) {
      const id = characterId || 'global';
      state.notes[id] = notes;
      storageManager.setObject('notes', state.notes);
    },
    ADD_NOTE(state, { characterId, note }) {
      const id = characterId || 'global';
      if (!state.notes[id]) {
        state.notes[id] = [];
      }
      state.notes[id].unshift(note);
      storageManager.setObject('notes', state.notes);
    },
    UPDATE_NOTE(state, { characterId, index, note }) {
      const id = characterId || 'global';
      if (state.notes[id] && index >= 0 && index < state.notes[id].length) {
        state.notes[id].splice(index, 1, note);
        storageManager.setObject('notes', state.notes);
      }
    },
    DELETE_NOTE(state, { characterId, index }) {
      const id = characterId || 'global';
      if (state.notes[id] && index >= 0 && index < state.notes[id].length) {
        state.notes[id].splice(index, 1);
        storageManager.setObject('notes', state.notes);
      }
    },
    SET_OFFLINE_MODE(state, isOffline) {
      state.isOfflineMode = isOffline;
    },
    SET_SAVES(state, saves) {
      state.saves = saves;
    },
    SET_CURRENT_SAVE(state, save) {
      state.currentSave = save;
    },
    ADD_SAVE(state, save) {
      state.saves.push(save);
    },
    UPDATE_SAVE(state, updatedSave) {
      const index = state.saves.findIndex(save => save.id === updatedSave.id);
      if (index !== -1) {
        state.saves[index] = updatedSave;
      }
    },
    REMOVE_SAVE(state, saveId) {
      state.saves = state.saves.filter(save => save.id !== saveId);
    },
    SET_LOADING_SAVES(state, isLoading) {
      state.loadingSaves = isLoading;
    },
    SET_SAVE_ERROR(state, error) {
      state.saveError = error;
    },
    SET_SAVE_HISTORY(state, history) {
      state.saveHistory = history;
    },
    SET_LOADING_SAVE_HISTORY(state, isLoading) {
      state.loadingSaveHistory = isLoading;
    },
    SET_PREVIEW_MODE(state, isPreview) {
      state.isPreviewMode = isPreview;
    },
    SET_GLOBAL_NOTICE(state, notice) {
      state.globalNotice = notice;
    },
    CLEAR_GLOBAL_NOTICE(state) {
      state.globalNotice = null;
    },

    // 游戏数据mutations
    SET_OCCUPATIONS(state, occupations) {
      state.occupations = occupations || {};
    },
    SET_SKILLS(state, skills) {
      state.skills = skills || {};
    },
    SET_WEAPONS(state, weapons) {
      state.weapons = weapons || {};
    },
    SET_ARMORS(state, armors) {
      state.armors = armors || {};
    },

    
    // 更新当前房间的名称
    UPDATE_ROOM_NAME(state, { roomId, name }) {
      if (state.currentRoom && state.currentRoom.id === roomId) {
        state.currentRoom.name = name;
      }
    },
    
    // 更新房间列表中的房间名称
    UPDATE_ROOM_LIST_NAME(state, { roomId, name }) {
      if (state.rooms && state.rooms.length > 0) {
        const roomIndex = state.rooms.findIndex(room => room.id === roomId);
        if (roomIndex !== -1) {
          state.rooms[roomIndex].name = name;
        }
      }
    }
  },
  actions: {
    async register({ commit, dispatch }, userData) {
      commit('SET_AUTH_LOADING', true);
      commit('SET_AUTH_ERROR', null);

      try {
        console.log('🚀 开始注册用户:', userData.email);

        // 验证输入数据
        if (!userData.username || !userData.email || !userData.password) {
          throw new Error('请填写完整的注册信息');
        }

        // 调用注册API
        const response = await axios.post('/api/users/register', userData);
        console.log('✅ API注册成功:', response.data);

        const { token, user } = response.data;

        if (!token || !user) {
          throw new Error('服务器返回数据格式错误');
        }

        // 更新状态
        commit('SET_TOKEN', token);
        commit('SET_USER', user);
        commit('SET_CONNECTION_STATUS', 'online');
        commit('SET_LAST_SYNC_TIME', new Date().toISOString());

        console.log('✅ 注册成功，用户ID:', user.id);
        return user;

      } catch (error) {
        console.error('❌ 注册失败:', error);

        let errorMessage = '注册失败，请稍后重试';

        if (error.response && error.response.data) {
          const errorDetail = error.response.data.detail;
          if (errorDetail === "邮箱已被注册") {
            errorMessage = '邮箱已被注册';
          } else if (errorDetail === "用户名已被注册") {
            errorMessage = '用户名已被注册';
          } else if (errorDetail) {
            errorMessage = errorDetail;
          }
        } else if (error.code === 'NETWORK_ERROR' || !error.response) {
          errorMessage = '网络连接失败，请检查网络连接';
          commit('SET_CONNECTION_STATUS', 'offline');
        } else if (error.message) {
          errorMessage = error.message;
        }

        commit('SET_AUTH_ERROR', errorMessage);
        throw new Error(errorMessage);

      } finally {
        commit('SET_AUTH_LOADING', false);
      }
    },
    
    async login({ commit, dispatch }, credentials) {
      commit('SET_AUTH_LOADING', true);
      commit('SET_AUTH_ERROR', null);

      try {
        console.log('🚀 开始用户登录:', credentials.email);

        // 验证输入数据
        if (!credentials.email || !credentials.password) {
          throw new Error('邮箱和密码不能为空');
        }

        // 调用登录API
        const response = await axios.post('/api/users/login', credentials);
        console.log('✅ API登录成功:', response.data);

        const { token, user } = response.data;

        if (!token || !user) {
          throw new Error('服务器返回数据格式错误');
        }

        // 更新状态
        commit('SET_TOKEN', token);
        commit('SET_USER', user);
        commit('SET_CONNECTION_STATUS', 'online');
        commit('SET_LAST_SYNC_TIME', new Date().toISOString());

        // 登录成功后，尝试加载用户数据
        try {
          await dispatch('fetchCharacters');
        } catch (fetchError) {
          console.warn('获取角色数据失败:', fetchError);
          // 不阻止登录流程
        }

        console.log('✅ 登录成功，用户ID:', user.id);
        return user;

      } catch (error) {
        console.error('❌ 登录失败:', error);

        let errorMessage = '登录失败，请稍后重试';

        if (error.response && error.response.data) {
          const errorDetail = error.response.data.detail;
          if (errorDetail === '邮箱或密码错误') {
            errorMessage = '邮箱或密码错误';
          } else if (errorDetail === '账户已被禁用') {
            errorMessage = '您的账户已被禁用，请联系管理员';
          } else if (errorDetail) {
            errorMessage = errorDetail;
          }
        } else if (error.code === 'NETWORK_ERROR' || !error.response) {
          errorMessage = '网络连接失败，请检查网络连接';
          commit('SET_CONNECTION_STATUS', 'offline');
        } else if (error.message) {
          errorMessage = error.message;
        }

        commit('SET_AUTH_ERROR', errorMessage);
        throw new Error(errorMessage);

      } finally {
        commit('SET_AUTH_LOADING', false);
      }
    },
    
    logout({ commit }) {
      console.log('🚪 用户登出');
      commit('CLEAR_AUTH');

      // 清除所有相关的本地数据
      storageManager.removeItem('characters');
      storageManager.removeItem('currentCharacterId');
      storageManager.removeItem('localCharacterCache');
      storageManager.removeItem('lastSyncTime');

      // 重置其他状态
      commit('SET_CHARACTERS', []);
      commit('SET_CURRENT_CHARACTER', null);
      commit('CLEAR_CURRENT_ROOM');
    },

    // 验证token有效性
    async validateToken({ commit, state, dispatch }) {
      if (!state.token) {
        return false;
      }

      try {
        // 检查token是否过期
        if (state.tokenExpiry) {
          const now = new Date().getTime();
          const expiry = new Date(state.tokenExpiry).getTime();
          if (now >= expiry) {
            console.log('🔒 Token已过期，自动登出');
            dispatch('logout');
            return false;
          }
        }

        // 尝试获取当前用户信息验证token
        const response = await axios.get('/api/users/me', {
          headers: { Authorization: `Bearer ${state.token}` }
        });

        if (response.data) {
          commit('SET_USER', response.data);
          commit('SET_CONNECTION_STATUS', 'online');
          return true;
        }

        return false;

      } catch (error) {
        console.error('❌ Token验证失败:', error);

        if (error.response && error.response.status === 401) {
          // Token无效，自动登出
          dispatch('logout');
        } else {
          // 网络错误，设置离线模式
          commit('SET_CONNECTION_STATUS', 'offline');
        }

        return false;
      }
    },

    // 初始化存储系统
    initializeStorage({ commit }) {
      try {
        // 获取存储状态
        const storageStatus = storageManager.getStatus();
        commit('SET_STORAGE_STATUS', storageStatus);
        
        console.log('[Store] 存储系统初始化完成:', storageStatus);
        
        // 如果存储降级，显示提示
        if (storageStatus.isDegraded) {
          console.warn('[Store] 存储系统处于降级模式:', storageStatus.storageType);
        }
        
        return true;
      } catch (error) {
        console.error('[Store] 存储系统初始化失败:', error);
        return false;
      }
    },

    // 初始化应用状态
    async initializeApp({ commit, dispatch, state }) {
      console.log('🚀 初始化应用状态');

      try {
        // 首先初始化存储系统
        dispatch('initializeStorage');
        
        // 检查是否有保存的认证信息
        if (state.token && state.user) {
          console.log('📱 发现本地认证信息，验证token');
          const isValid = await dispatch('validateToken');

          if (isValid) {
            console.log('✅ Token有效，自动登录成功');

            // 尝试加载用户数据
            try {
              await dispatch('fetchCharacters');
            } catch (error) {
              console.warn('获取角色数据失败:', error);
            }

            return true;
          }
        }

        console.log('❌ 无有效认证信息');
        return false;

      } catch (error) {
        console.error('❌ 应用初始化失败:', error);
        commit('SET_CONNECTION_STATUS', 'offline');
        return false;
      }
    },

    // 刷新用户数据
    async refreshUserData({ commit, dispatch, state }) {
      if (!state.token) {
        return false;
      }

      try {
        const isValid = await dispatch('validateToken');
        if (isValid) {
          await dispatch('fetchCharacters');
          commit('SET_LAST_SYNC_TIME', new Date().toISOString());
          return true;
        }
        return false;
      } catch (error) {
        console.error('刷新用户数据失败:', error);
        return false;
      }
    },
    
    async fetchCharacters({ commit, state }) {
      try {
        console.log('🔍 开始获取角色列表，用户ID:', state.user?.id);

        if (!state.user || !state.user.id) {
          throw new Error('用户未登录或用户ID不存在');
        }

        const response = await apiService.characters.getUserCharacters(state.user.id);
        console.log('✅ 角色列表获取成功:', response.data);

        // 后端返回格式: {success: true, data: [角色数组]}
        const characters = response.data.data || response.data;
        console.log('📋 解析后的角色数据:', characters);

        commit('SET_CHARACTERS', characters);
        return characters;
      } catch (error) {
        console.error('❌ 获取角色列表失败:', error);
        console.error('错误详情:', error.response?.data || error.message);
        throw error;
      }
    },
    
    async updateCharacter({ commit, state }, { id, ...characterData }) {
      try {
        const response = await axios.put(`/api/characters/${id}`, characterData, {
          headers: { Authorization: `Bearer ${state.token}` }
        });
        
        commit('UPDATE_CHARACTER', response.data);
        
        import('@/services/websocket').then(module => {
          const websocketService = module.default;
          if (websocketService.isConnected) {
            const updatedCharacter = state.characters.find(c => c.id === id);
            if (updatedCharacter) {
              websocketService.sendCharacterUpdate(updatedCharacter);
            }
          }
        });
        
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    async deleteCharacter({ commit, state }, characterId) {
      try {
        console.log('🗑️ 删除角色:', characterId);

        const response = await apiService.characters.deleteCharacter(characterId);

        if (response.status === 200) {
          commit('DELETE_CHARACTER', characterId);
          console.log('✅ 角色删除成功');
          return true;
        }
      } catch (error) {
        console.error('❌ 删除角色失败:', error);
        console.error('错误详情:', error.response?.data || error.message);
        throw error;
      }
    },

    async fetchRooms({ commit, state }) {
      try {
        console.log('正在获取房间列表...');
        const response = await axios.get('/api/rooms/', {
          headers: { Authorization: `Bearer ${state.token}` }
        });
        
        console.log('获取房间成功:', response.data);
        commit('SET_ROOMS', response.data);
        return response.data;
      } catch (error) {
        console.error('获取房间失败:', error.message, error.response?.status);
        
        if (error.message.includes('Network Error') || error.code === 'ECONNREFUSED') {
          console.log('使用模拟数据');
          const mockRooms = [
            { id: 1, name: '模拟房间1', description: '后端未连接，使用模拟数据', keeper_id: 1, is_private: false },
            { id: 2, name: '模拟房间2', description: '请确保后端服务已启动', keeper_id: 1, is_private: false }
          ];
          commit('SET_ROOMS', mockRooms);
          return mockRooms;
        }
        
        throw error;
      }
    },
    
    async createRoom({ commit, state }, roomData) {
      try {
        const response = await axios.post('/api/rooms/', {
          ...roomData,
          keeper_id: state.user.id
        }, {
          headers: { Authorization: `Bearer ${state.token}` }
        });
        
        const newRoom = response.data;
        commit('SET_ROOMS', [...state.rooms, newRoom]);
        
        return newRoom;
      } catch (error) {
        throw error;
      }
    },
    
    async joinRoom({ commit }, { roomId }) {
      try {
        // 确保roomId是有效的数字
        const numericRoomId = parseInt(roomId);
        
        if (isNaN(numericRoomId)) {
          console.error('无效的房间ID:', roomId);
          throw new Error('无效的房间ID');
        }
        
        console.log(`尝试加入房间，ID: ${numericRoomId}`);
        
        try {
          const response = await apiService.getRoom(numericRoomId);
          if (response.data) {
            // 确保房间数据中的ID是数字类型
            const roomData = {
              ...response.data,
              id: parseInt(response.data.id)
            };
            console.log('从API获取的房间数据:', roomData);
            commit('SET_CURRENT_ROOM', roomData);
            return roomData;
          } else {
            throw new Error('房间不存在');
          }
        } catch (error) {
          console.error('加入房间失败:', error);
          // 创建模拟房间
          const mockRoom = { 
            id: numericRoomId, 
            name: `测试房间 ${numericRoomId}`, 
            description: '这是一个自动创建的房间',
            keeper_id: 1,
            is_private: false,
            is_mock: true
          };
          console.log('创建模拟房间:', mockRoom);
          commit('SET_CURRENT_ROOM', mockRoom);
          return mockRoom; // 返回模拟房间，不抛出错误
        }
      } catch (error) {
        console.error('处理房间ID失败:', error);
        throw error;
      }
    },
    
    leaveRoom({ commit }) {
      commit('CLEAR_CURRENT_ROOM');
    },
    
    async updateRoomName({ commit, state }, { roomId, name }) {
      try {
        // 如果是当前房间，更新当前房间名称
        if (state.currentRoom && state.currentRoom.id === roomId) {
          commit('UPDATE_ROOM_NAME', { roomId, name });
        }
        
        // 更新房间列表中的房间名称
        if (state.rooms && state.rooms.length > 0) {
          commit('UPDATE_ROOM_LIST_NAME', { roomId, name });
        }
        
        // 保存到本地存储
        try {
          if (window.storageManager) {
            window.storageManager.setItem(`room_name_${roomId}`, name);
          } else {
            localStorage.setItem(`room_name_${roomId}`, name);
          }
        } catch (error) {
          console.warn('[Store] 无法保存房间名称:', error.message);
        }
        
        // 注释掉API调用，因为后端不支持此功能
        // 仅依赖前端存储和WebSocket通信
        /*
        try {
          await apiService.updateRoom(roomId, { name });
        } catch (apiError) {
          console.log('API更新房间名称失败，但不影响前端功能:', apiError.message);
        }
        */
        
        return true;
      } catch (error) {
        console.error('更新房间名称失败:', error);
        // 即使出错，我们仍然保留本地更改
        return false;
      }
    },
    
    toggleTheme({ commit }) {
      commit('TOGGLE_THEME');
    },
    
    addMessage({ commit, rootState }, message) {
      commit('ADD_MESSAGE', message);
      
      emitter.emit('new-message', message);
      
      return message;
    },
    
    clearMessages({ commit }) {
      commit('CLEAR_MESSAGES');
    },
    
    updateMessage({ commit }, { index, message }) {
      commit('UPDATE_MESSAGE', { index, message });
    },
    
    removeLastMessage({ commit }) {
      commit('REMOVE_LAST_MESSAGE');
    },
    
    updateCharacterFromServer({ commit }, characterData) {
      commit('UPDATE_CHARACTER_FROM_SERVER', characterData);
    },
    
    updateCharacterAttribute({ commit, state, dispatch }, { id, field, value }) {
      commit('UPDATE_CHARACTER_ATTRIBUTE', { id, field, value });
      
      import('@/services/websocket').then(module => {
        const websocketService = module.default;
        if (websocketService.isConnected) {
          websocketService.sendCharacterAttributeUpdate(id, field, value);
        }
      });
    },
    
    setCurrentCharacter({ commit, dispatch }, characterId) {
      commit('SET_CURRENT_CHARACTER', characterId);
      
      dispatch('syncCharacterData', characterId);
    },
    
    syncCharacterData({ commit, state }, characterId) {
      commit('UPDATE_CHARACTER_SYNC_STATUS', { 
        characterId, 
        status: { syncing: true } 
      });
      
      import('@/services/websocket').then(module => {
        const websocketService = module.default;
        if (websocketService.isConnected) {
          websocketService.requestCharacterSync(characterId);
        } else {
          const cachedData = state.localCharacterCache[characterId];
          if (cachedData) {
            commit('UPDATE_CHARACTER_FROM_SERVER', cachedData);
          }
          
          commit('UPDATE_CHARACTER_SYNC_STATUS', { 
            characterId, 
            status: { 
              syncing: false,
              lastSync: new Date().toISOString(),
              error: 'WebSocket未连接，使用本地缓存'
            } 
          });
        }
      });
    },
    
    loadRoomMessages({ commit, state }) {
      if (!state.currentRoom) return;
      
      const roomId = state.currentRoom.id;
      const storedMessages = storageManager.getItem(`room_messages_${roomId}`);
      
      if (storedMessages) {
        try {
          const messages = JSON.parse(storedMessages);
          commit('SET_MESSAGES', messages);
        } catch (error) {
          console.error('加载房间消息失败:', error);
        }
      }
    },
    
    setScenes({ commit }, scenes) {
      commit('SET_SCENES', scenes);
    },
    
    addScene({ commit }, scene) {
      commit('ADD_SCENE', scene);
    },
    
    updateScene({ commit }, { index, scene }) {
      commit('UPDATE_SCENE', { index, scene });
    },
    
    deleteScene({ commit }, index) {
      commit('DELETE_SCENE', index);
    },
    
    setCurrentSceneIndex({ commit }, index) {
      commit('SET_CURRENT_SCENE_INDEX', index);
    },
    
    setNotes({ commit }, { characterId, notes }) {
      commit('SET_NOTES', { characterId, notes });
    },
    
    addNote({ commit }, { characterId, note }) {
      if (!note.createdAt) {
        note.createdAt = new Date().toISOString();
      }
      commit('ADD_NOTE', { characterId, note });
    },
    
    updateNote({ commit }, { characterId, index, note }) {
      note.updatedAt = new Date().toISOString();
      commit('UPDATE_NOTE', { characterId, index, note });
    },
    
    deleteNote({ commit }, { characterId, index }) {
      commit('DELETE_NOTE', { characterId, index });
    },
    
    loadSceneData({ commit, state }) {
      const scenes = JSON.parse(storageManager.getItem('scenes') || '[]');
      if (scenes.length > 0) {
        commit('SET_SCENES', scenes);
      }
      
      const currentSceneIndex = parseInt(storageManager.getItem('currentSceneIndex') || '0');
      if (currentSceneIndex >= 0 && currentSceneIndex < scenes.length) {
        commit('SET_CURRENT_SCENE_INDEX', currentSceneIndex);
      }
      
      const notes = JSON.parse(storageManager.getItem('notes') || '{}');
      if (notes) {
        Object.keys(notes).forEach(characterId => {
          commit('SET_NOTES', { characterId, notes: notes[characterId] });
        });
      }
    },
    
    setOfflineMode({ commit }, isOffline) {
      commit('SET_OFFLINE_MODE', isOffline);
      
      if (isOffline) {
        console.log('已启用离线模式');
        // 发送系统消息通知用户
        this.dispatch('addMessage', {
          type: 'system',
          content: '已启用离线模式，部分功能可能不可用',
          timestamp: new Date().toISOString()
        });
      }
    },
    
    async fetchSaves({ commit }, { roomId, includeAutoSaves = true }) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        const response = await apiService.gameSaves.getRoomSaves(roomId, 0, 100, includeAutoSaves);
        commit('SET_SAVES', response.data || []);
        return response.data || [];
      } catch (error) {
        console.error('获取存档列表失败:', error);
        commit('SET_SAVE_ERROR', '获取存档列表失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },
    
    async createSave({ commit }, saveData) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        const response = await apiService.gameSaves.createSave(saveData);
        commit('ADD_SAVE', response.data);
        return response.data;
      } catch (error) {
        console.error('创建存档失败:', error);
        commit('SET_SAVE_ERROR', '创建存档失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },
    
    async updateSave({ commit }, { saveId, updateData }) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        const response = await apiService.gameSaves.updateSave(saveId, updateData);
        commit('UPDATE_SAVE', response.data);
        return response.data;
      } catch (error) {
        console.error('更新存档失败:', error);
        commit('SET_SAVE_ERROR', '更新存档失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },
    
    async deleteSave({ commit }, saveId) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        const response = await apiService.gameSaves.deleteSave(saveId);
        if (response.data && response.data.success) {
          commit('REMOVE_SAVE', saveId);
          return true;
        }
        return false;
      } catch (error) {
        console.error('删除存档失败:', error);
        commit('SET_SAVE_ERROR', '删除存档失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },
    
    async createAutoSave({ commit }, { roomId, creatorId, saveData, previousAutoSaveId }) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        const response = await apiService.gameSaves.createAutoSave(
          roomId,
          creatorId,
          saveData,
          previousAutoSaveId
        );
        
        if (previousAutoSaveId) {
          commit('UPDATE_SAVE', response.data);
        } else {
          commit('ADD_SAVE', response.data);
        }
        
        return response.data;
      } catch (error) {
        console.error('创建自动存档失败:', error);
        commit('SET_SAVE_ERROR', '创建自动存档失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },
    
    async fetchSaveHistory({ commit }, saveId) {
      commit('SET_LOADING_SAVE_HISTORY', true);
      
      try {
        const response = await apiService.gameSaves.getSaveHistory(saveId);
        commit('SET_SAVE_HISTORY', response.data || []);
        return response.data || [];
      } catch (error) {
        console.error('获取存档历史记录失败:', error);
        throw error;
      } finally {
        commit('SET_LOADING_SAVE_HISTORY', false);
      }
    },
    
    async getSaveHistoryVersion({ commit }, { saveId, version }) {
      try {
        const response = await apiService.gameSaves.getSaveHistoryVersion(saveId, version);
        return response.data;
      } catch (error) {
        console.error('获取历史版本失败:', error);
        throw error;
      }
    },
    
    async restoreVersion({ commit }, { saveId, version }) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        const response = await apiService.gameSaves.restoreVersion(saveId, version);
        commit('UPDATE_SAVE', response.data);
        return response.data;
      } catch (error) {
        console.error('恢复历史版本失败:', error);
        commit('SET_SAVE_ERROR', '恢复历史版本失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },
    
    async loadSave({ commit, dispatch }, save) {
      commit('SET_LOADING_SAVES', true);
      commit('SET_SAVE_ERROR', null);
      
      try {
        // 设置当前存档
        commit('SET_CURRENT_SAVE', save);
        
        // 从存档数据中恢复状态
        const saveData = save.save_data;
        
        // 恢复各种状态
        if (saveData.messages) {
          commit('SET_MESSAGES', saveData.messages);
        }
        
        if (saveData.characters) {
          commit('SET_CHARACTERS', saveData.characters);
        }
        
        if (saveData.scenes) {
          commit('SET_SCENES', saveData.scenes);
          if (saveData.currentScene !== undefined) {
            commit('SET_CURRENT_SCENE_INDEX', saveData.currentScene);
          }
        }
        
        if (saveData.diceHistory) {
          commit('SET_DICE_HISTORY', saveData.diceHistory);
        }
        
        if (saveData.notes) {
          commit('SET_NOTES', saveData.notes);
        }
        
        if (saveData.clues) {
          commit('SET_CLUES', saveData.clues);
        }
        
        if (saveData.aiSettings) {
          commit('SET_AI_SETTINGS', saveData.aiSettings);
        }
        
        return true;
      } catch (error) {
        console.error('加载存档失败:', error);
        commit('SET_SAVE_ERROR', '加载存档失败');
        throw error;
      } finally {
        commit('SET_LOADING_SAVES', false);
      }
    },



    async createCharacter({ commit, dispatch, state }, characterData) {
      try {
        console.log('🎭 创建角色:', characterData.name);

        // 获取当前用户ID
        const userId = state.user?.id || 1;
        console.log('使用用户ID:', userId);

        const response = await apiService.characters.createCharacter(characterData, userId);

        if (response.data) {
          // 添加到角色列表
          commit('ADD_CHARACTER', response.data);
          console.log('✅ 角色创建成功:', response.data.name);
          return response.data;
        }
      } catch (error) {
        console.error('❌ 角色创建失败:', error);
        throw error;
      }
    },

    async loadUserCharacters({ commit, state }) {
      try {
        if (!state.user || !state.user.id) {
          console.log('⚠️ 用户未登录，跳过加载角色');
          return;
        }

        const response = await apiService.characters.getUserCharacters(state.user.id);
        if (response.data) {
          commit('SET_CHARACTERS', response.data);
          console.log('✅ 用户角色加载成功:', response.data.length, '个角色');
        }
      } catch (error) {
        console.error('❌ 用户角色加载失败:', error);
      }
    },

    async calculateSkillPoints({ state }, characterData) {
      try {
        const response = await apiService.characters.calculateSkillPoints(characterData);
        return response.data;
      } catch (error) {
        console.error('❌ 技能点计算失败:', error);
        throw error;
      }
    }
  },
  modules: {
    auth,
    rooms
  },
  plugins: [persistencePlugin]
}) 