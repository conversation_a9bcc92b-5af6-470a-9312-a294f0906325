/**
 * COC 7版战斗道具系统
 * 与背包系统对接，处理战斗中的道具使用
 */

import CombatRules from './combatRules.js'
import { diceRoller } from './diceRoller.js'

export class CombatItems {
  /**
   * 道具数据库
   */
  static itemDatabase = {
    // 治疗道具
    healing: {
      first_aid_kit: {
        id: 'first_aid_kit',
        name: '急救包',
        type: 'healing',
        category: 'medical',
        uses: 3,
        maxUses: 3,
        skill: 'first_aid',
        healAmount: '1d3',
        timeRequired: 1, // 回合数
        description: '基础急救包，可以治疗轻伤',
        effects: {
          stopBleeding: true,
          healHP: '1d3',
          skillBonus: 0
        }
      },
      medical_bag: {
        id: 'medical_bag',
        name: '医疗包',
        type: 'healing',
        category: 'medical',
        uses: 5,
        maxUses: 5,
        skill: 'medicine',
        healAmount: '1d4+1',
        timeRequired: 2,
        description: '专业医疗包，治疗效果更好',
        effects: {
          stopBleeding: true,
          healHP: '1d4+1',
          skillBonus: 10,
          removePoisoned: true
        }
      },
      morphine: {
        id: 'morphine',
        name: '吗啡',
        type: 'healing',
        category: 'drug',
        uses: 1,
        maxUses: 1,
        skill: 'medicine',
        timeRequired: 1,
        description: '强效止痛药，可以暂时忽略伤势影响',
        effects: {
          ignorePain: 3, // 持续3轮
          healHP: 0,
          skillBonus: 0,
          sideEffects: ['drowsy']
        }
      },
      bandage: {
        id: 'bandage',
        name: '绷带',
        type: 'healing',
        category: 'medical',
        uses: 1,
        maxUses: 1,
        skill: 'first_aid',
        timeRequired: 1,
        description: '简单绷带，主要用于止血',
        effects: {
          stopBleeding: true,
          healHP: 1,
          skillBonus: 0
        }
      }
    },

    // 工具道具
    tools: {
      lockpicks: {
        id: 'lockpicks',
        name: '撬锁工具',
        type: 'tool',
        category: 'thief',
        uses: -1, // 无限使用
        skill: 'locksmith',
        timeRequired: 0,
        description: '专业撬锁工具',
        effects: {
          skillBonus: 20,
          targetSkills: ['locksmith']
        }
      },
      crowbar: {
        id: 'crowbar',
        name: '撬棍',
        type: 'tool',
        category: 'general',
        uses: -1,
        skill: 'strength',
        timeRequired: 0,
        description: '可以用作武器或工具',
        effects: {
          skillBonus: 15,
          targetSkills: ['strength'],
          canUseAsWeapon: true,
          weaponStats: {
            name: '撬棍',
            damage: '1d6+2',
            skill: 'fighting'
          }
        }
      },
      flashlight: {
        id: 'flashlight',
        name: '手电筒',
        type: 'tool',
        category: 'general',
        uses: 10, // 电池使用次数
        maxUses: 10,
        timeRequired: 0,
        description: '照明工具，可以减少黑暗环境的惩罚',
        effects: {
          lightRadius: 5,
          reduceDarknessPenalty: 1,
          batteryLife: 10
        }
      },
      rope: {
        id: 'rope',
        name: '绳索',
        type: 'tool',
        category: 'general',
        uses: -1,
        skill: 'climb',
        timeRequired: 0,
        description: '50英尺长的绳索',
        effects: {
          skillBonus: 10,
          targetSkills: ['climb'],
          length: 50,
          canBindTarget: true
        }
      }
    },

    // 特殊道具
    special: {
      holy_water: {
        id: 'holy_water',
        name: '圣水',
        type: 'special',
        category: 'religious',
        uses: 1,
        maxUses: 1,
        timeRequired: 1,
        description: '对邪恶生物有特殊效果',
        effects: {
          damageToEvil: '1d6',
          protectionFromEvil: 2,
          duration: 5
        }
      },
      silver_cross: {
        id: 'silver_cross',
        name: '银十字架',
        type: 'special',
        category: 'religious',
        uses: -1,
        timeRequired: 0,
        description: '可以驱退某些邪恶生物',
        effects: {
          repelEvil: true,
          protectionRadius: 2,
          faithRequired: true
        }
      },
      occult_book: {
        id: 'occult_book',
        name: '神秘学书籍',
        type: 'special',
        category: 'knowledge',
        uses: -1,
        timeRequired: 0,
        description: '包含神秘知识的书籍',
        effects: {
          skillBonus: 15,
          targetSkills: ['occult', 'cthulhu_mythos'],
          sanityRisk: '1d4'
        }
      },
      lucky_charm: {
        id: 'lucky_charm',
        name: '幸运符',
        type: 'special',
        category: 'mystical',
        uses: 3,
        maxUses: 3,
        timeRequired: 0,
        description: '可以重投一次骰子',
        effects: {
          rerollOnce: true,
          luckBonus: 5
        }
      }
    },

    // 消耗品
    consumables: {
      stimulant: {
        id: 'stimulant',
        name: '兴奋剂',
        type: 'consumable',
        category: 'drug',
        uses: 1,
        maxUses: 1,
        timeRequired: 1,
        description: '暂时提升身体能力',
        effects: {
          attributeBonus: {
            dexterity: 10,
            strength: 5
          },
          duration: 5,
          sideEffects: ['exhausted_after']
        }
      },
      alcohol: {
        id: 'alcohol',
        name: '烈酒',
        type: 'consumable',
        category: 'drug',
        uses: 3,
        maxUses: 3,
        timeRequired: 1,
        description: '可以暂时缓解恐惧，但影响判断',
        effects: {
          sanityRestore: '1d3',
          attributePenalty: {
            intelligence: 5,
            dexterity: 5
          },
          duration: 3
        }
      },
      smelling_salts: {
        id: 'smelling_salts',
        name: '嗅盐',
        type: 'consumable',
        category: 'medical',
        uses: 3,
        maxUses: 3,
        timeRequired: 1,
        description: '可以唤醒昏迷的角色',
        effects: {
          wakeUnconscious: true,
          removeStunned: true
        }
      }
    }
  }

  /**
   * 使用治疗道具
   * @param {Object} character 使用者
   * @param {Object} target 目标 (可以是自己)
   * @param {string} itemId 道具ID
   * @returns {Object} 使用结果
   */
  static useHealingItem(character, target, itemId) {
    const item = this.getItemFromInventory(character, itemId)
    if (!item) {
      return { success: false, reason: '没有该道具' }
    }

    if (item.uses <= 0) {
      return { success: false, reason: '道具已用完' }
    }

    // 检查技能要求
    const requiredSkill = item.skill
    const skillValue = character[requiredSkill] || character.skills?.[requiredSkill] || 0

    // 进行技能检定
    const roll = diceRoller.rollD100()
    const successLevel = CombatRules.getSuccessLevel(roll, skillValue + item.effects.skillBonus)

    let healingResult = {
      success: false,
      healAmount: 0,
      effectsApplied: [],
      skillRoll: roll,
      skillValue: skillValue + item.effects.skillBonus,
      successLevel
    }

    if (successLevel !== 'failure' && successLevel !== 'fumble') {
      healingResult.success = true

      // 计算治疗量
      if (item.effects.healHP) {
        if (typeof item.effects.healHP === 'string') {
          const healRoll = diceRoller.roll(item.effects.healHP)
          healingResult.healAmount = healRoll.total
        } else {
          healingResult.healAmount = item.effects.healHP
        }

        // 根据成功等级调整治疗量
        if (successLevel === 'extreme' || successLevel === 'critical') {
          healingResult.healAmount = Math.ceil(healingResult.healAmount * 1.5)
        }

        // 应用治疗
        target.currentHP = Math.min(target.hitPoints || target.hp, 
                                   (target.currentHP || 0) + healingResult.healAmount)
      }

      // 应用特殊效果
      if (item.effects.stopBleeding && target.conditions) {
        target.conditions = target.conditions.filter(c => c !== 'bleeding')
        healingResult.effectsApplied.push('止血')
      }

      if (item.effects.removePoisoned && target.conditions) {
        target.conditions = target.conditions.filter(c => c !== 'poisoned')
        healingResult.effectsApplied.push('解毒')
      }

      if (item.effects.ignorePain) {
        if (!target.conditions) target.conditions = []
        target.conditions.push(`ignore_pain_${item.effects.ignorePain}`)
        healingResult.effectsApplied.push(`忽略疼痛${item.effects.ignorePain}轮`)
      }

      // 消耗道具
      item.uses--
      if (item.uses <= 0) {
        this.removeItemFromInventory(character, itemId)
      }
    }

    return {
      type: 'healing_item_use',
      character: character.name,
      target: target.name,
      item: item.name,
      result: healingResult,
      description: this.getHealingDescription(healingResult, item.name)
    }
  }

  /**
   * 使用工具道具
   * @param {Object} character 使用者
   * @param {string} itemId 道具ID
   * @param {string} targetSkill 目标技能
   * @returns {Object} 使用结果
   */
  static useToolItem(character, itemId, targetSkill) {
    const item = this.getItemFromInventory(character, itemId)
    if (!item) {
      return { success: false, reason: '没有该道具' }
    }

    if (item.uses === 0) {
      return { success: false, reason: '道具已损坏' }
    }

    // 检查是否适用于目标技能
    if (item.effects.targetSkills && !item.effects.targetSkills.includes(targetSkill)) {
      return { success: false, reason: '该工具不适用于此技能' }
    }

    let toolResult = {
      success: true,
      skillBonus: item.effects.skillBonus || 0,
      specialEffects: []
    }

    // 应用特殊效果
    if (item.effects.lightRadius) {
      toolResult.specialEffects.push(`照明半径${item.effects.lightRadius}米`)
    }

    if (item.effects.reduceDarknessPenalty) {
      toolResult.specialEffects.push(`减少黑暗惩罚${item.effects.reduceDarknessPenalty}个骰子`)
    }

    // 消耗使用次数 (如果有限制)
    if (item.uses > 0) {
      item.uses--
      if (item.uses <= 0) {
        this.removeItemFromInventory(character, itemId)
        toolResult.specialEffects.push('工具已损坏')
      }
    }

    return {
      type: 'tool_item_use',
      character: character.name,
      item: item.name,
      targetSkill,
      result: toolResult,
      description: this.getToolDescription(toolResult, item.name, targetSkill)
    }
  }

  /**
   * 使用特殊道具
   * @param {Object} character 使用者
   * @param {string} itemId 道具ID
   * @param {Object} target 目标 (可选)
   * @returns {Object} 使用结果
   */
  static useSpecialItem(character, itemId, target = null) {
    const item = this.getItemFromInventory(character, itemId)
    if (!item) {
      return { success: false, reason: '没有该道具' }
    }

    if (item.uses === 0) {
      return { success: false, reason: '道具已用完' }
    }

    let specialResult = {
      success: true,
      effects: [],
      sanityLoss: 0
    }

    // 处理不同类型的特殊道具
    switch (item.id) {
      case 'holy_water':
        if (target && target.type === 'evil') {
          const damage = diceRoller.roll(item.effects.damageToEvil).total
          target.currentHP = Math.max(0, (target.currentHP || 0) - damage)
          specialResult.effects.push(`对邪恶生物造成${damage}点伤害`)
        }
        break

      case 'silver_cross':
        if (item.effects.faithRequired) {
          // 需要信仰检定
          const faithRoll = diceRoller.rollD100()
          const faithSkill = character.faith || character.psychology || 50
          if (faithRoll <= faithSkill) {
            specialResult.effects.push('成功驱退邪恶生物')
          } else {
            specialResult.success = false
            specialResult.effects.push('信仰不足，无法发挥效果')
          }
        }
        break

      case 'occult_book':
        // 阅读神秘书籍可能造成理智损失
        if (item.effects.sanityRisk) {
          const sanityLoss = diceRoller.roll(item.effects.sanityRisk).total
          character.currentSAN = Math.max(0, (character.currentSAN || character.sanity) - sanityLoss)
          specialResult.sanityLoss = sanityLoss
          specialResult.effects.push(`获得神秘知识，失去${sanityLoss}点理智`)
        }
        break

      case 'lucky_charm':
        specialResult.effects.push('可以重投一次骰子')
        break
    }

    // 消耗道具
    if (item.uses > 0) {
      item.uses--
      if (item.uses <= 0) {
        this.removeItemFromInventory(character, itemId)
      }
    }

    return {
      type: 'special_item_use',
      character: character.name,
      item: item.name,
      target: target?.name || null,
      result: specialResult,
      description: this.getSpecialDescription(specialResult, item.name)
    }
  }

  /**
   * 使用消耗品
   * @param {Object} character 使用者
   * @param {string} itemId 道具ID
   * @returns {Object} 使用结果
   */
  static useConsumableItem(character, itemId) {
    const item = this.getItemFromInventory(character, itemId)
    if (!item) {
      return { success: false, reason: '没有该道具' }
    }

    if (item.uses <= 0) {
      return { success: false, reason: '道具已用完' }
    }

    let consumableResult = {
      success: true,
      effects: [],
      duration: item.effects.duration || 0
    }

    // 应用属性加值
    if (item.effects.attributeBonus) {
      Object.entries(item.effects.attributeBonus).forEach(([attr, bonus]) => {
        if (!character.tempBonuses) character.tempBonuses = {}
        character.tempBonuses[attr] = (character.tempBonuses[attr] || 0) + bonus
        consumableResult.effects.push(`${attr}+${bonus}`)
      })
    }

    // 应用属性惩罚
    if (item.effects.attributePenalty) {
      Object.entries(item.effects.attributePenalty).forEach(([attr, penalty]) => {
        if (!character.tempPenalties) character.tempPenalties = {}
        character.tempPenalties[attr] = (character.tempPenalties[attr] || 0) + penalty
        consumableResult.effects.push(`${attr}-${penalty}`)
      })
    }

    // 恢复理智
    if (item.effects.sanityRestore) {
      const sanityRestore = typeof item.effects.sanityRestore === 'string' 
        ? diceRoller.roll(item.effects.sanityRestore).total 
        : item.effects.sanityRestore
      
      character.currentSAN = Math.min(character.sanity || character.maxSAN, 
                                     (character.currentSAN || 0) + sanityRestore)
      consumableResult.effects.push(`恢复${sanityRestore}点理智`)
    }

    // 移除状态效果
    if (item.effects.wakeUnconscious && character.conditions) {
      character.conditions = character.conditions.filter(c => c !== 'unconscious')
      consumableResult.effects.push('唤醒昏迷')
    }

    if (item.effects.removeStunned && character.conditions) {
      character.conditions = character.conditions.filter(c => c !== 'stunned')
      consumableResult.effects.push('移除眩晕')
    }

    // 消耗道具
    item.uses--
    if (item.uses <= 0) {
      this.removeItemFromInventory(character, itemId)
    }

    return {
      type: 'consumable_item_use',
      character: character.name,
      item: item.name,
      result: consumableResult,
      description: this.getConsumableDescription(consumableResult, item.name)
    }
  }

  /**
   * 从背包获取道具
   * @param {Object} character 角色
   * @param {string} itemId 道具ID
   * @returns {Object|null} 道具对象
   */
  static getItemFromInventory(character, itemId) {
    if (!character.inventory) return null
    
    // 在各个分类中查找道具
    for (const category of Object.values(this.itemDatabase)) {
      if (category[itemId]) {
        // 检查角色是否拥有该道具
        const inventoryItem = character.inventory[itemId]
        if (inventoryItem && inventoryItem.quantity > 0) {
          return {
            ...category[itemId],
            uses: inventoryItem.uses || category[itemId].uses,
            quantity: inventoryItem.quantity
          }
        }
      }
    }
    
    return null
  }

  /**
   * 从背包移除道具
   * @param {Object} character 角色
   * @param {string} itemId 道具ID
   */
  static removeItemFromInventory(character, itemId) {
    if (character.inventory && character.inventory[itemId]) {
      character.inventory[itemId].quantity--
      if (character.inventory[itemId].quantity <= 0) {
        delete character.inventory[itemId]
      }
    }
  }

  /**
   * 获取治疗描述
   */
  static getHealingDescription(result, itemName) {
    if (!result.success) {
      return `使用${itemName}失败 (投掷${result.skillRoll})`
    }

    let desc = `使用${itemName}成功`
    if (result.healAmount > 0) {
      desc += `，恢复${result.healAmount}点生命值`
    }
    if (result.effectsApplied.length > 0) {
      desc += `，${result.effectsApplied.join('、')}`
    }
    return desc
  }

  /**
   * 获取工具描述
   */
  static getToolDescription(result, itemName, targetSkill) {
    let desc = `使用${itemName}`
    if (result.skillBonus > 0) {
      desc += `，${targetSkill}技能获得+${result.skillBonus}加值`
    }
    if (result.specialEffects.length > 0) {
      desc += `，${result.specialEffects.join('、')}`
    }
    return desc
  }

  /**
   * 获取特殊道具描述
   */
  static getSpecialDescription(result, itemName) {
    let desc = `使用${itemName}`
    if (result.effects.length > 0) {
      desc += `，${result.effects.join('、')}`
    }
    if (result.sanityLoss > 0) {
      desc += ` (理智损失${result.sanityLoss})`
    }
    return desc
  }

  /**
   * 获取消耗品描述
   */
  static getConsumableDescription(result, itemName) {
    let desc = `使用${itemName}`
    if (result.effects.length > 0) {
      desc += `，${result.effects.join('、')}`
    }
    if (result.duration > 0) {
      desc += ` (持续${result.duration}轮)`
    }
    return desc
  }

  /**
   * 获取角色可用的道具列表
   * @param {Object} character 角色
   * @param {string} category 道具分类 (可选)
   * @returns {Array} 可用道具列表
   */
  static getAvailableItems(character, category = null) {
    const availableItems = []
    
    if (!character.inventory) return availableItems

    for (const [categoryName, items] of Object.entries(this.itemDatabase)) {
      if (category && categoryName !== category) continue
      
      for (const [itemId, itemData] of Object.entries(items)) {
        const inventoryItem = character.inventory[itemId]
        if (inventoryItem && inventoryItem.quantity > 0) {
          availableItems.push({
            ...itemData,
            uses: inventoryItem.uses || itemData.uses,
            quantity: inventoryItem.quantity
          })
        }
      }
    }
    
    return availableItems
  }

  /**
   * 检查道具使用条件
   * @param {Object} character 角色
   * @param {string} itemId 道具ID
   * @param {Object} context 使用上下文
   * @returns {Object} 检查结果
   */
  static checkItemUsageConditions(character, itemId, context = {}) {
    const item = this.getItemFromInventory(character, itemId)
    if (!item) {
      return { canUse: false, reason: '没有该道具' }
    }

    if (item.uses === 0) {
      return { canUse: false, reason: '道具已用完' }
    }

    // 检查时间要求
    if (item.timeRequired > 0 && context.inCombat) {
      return { 
        canUse: true, 
        warning: `使用该道具需要${item.timeRequired}个回合`,
        timeRequired: item.timeRequired
      }
    }

    // 检查技能要求
    if (item.skill) {
      const skillValue = character[item.skill] || character.skills?.[item.skill] || 0
      if (skillValue === 0) {
        return { 
          canUse: true, 
          warning: `缺乏${item.skill}技能，成功率较低`
        }
      }
    }

    return { canUse: true }
  }
}

export default CombatItems