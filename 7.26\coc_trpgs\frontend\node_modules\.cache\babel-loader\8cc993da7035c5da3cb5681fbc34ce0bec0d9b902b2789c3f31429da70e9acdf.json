{"ast": null, "code": "import { normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, Transition as _Transition, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"notice-content\"\n};\nvar _hoisted_2 = {\n  \"class\": \"notice-message\"\n};\nvar _hoisted_3 = {\n  \"class\": \"notice-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createBlock(_Transition, {\n    name: \"notice-fade\"\n  }, {\n    \"default\": _withCtx(function () {\n      return [_ctx.notice ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        \"class\": _normalizeClass([\"global-notice\", $options.noticeClass])\n      }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"i\", {\n        \"class\": _normalizeClass($options.noticeIcon)\n      }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_2, _toDisplayString(_ctx.notice.message), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_3, [_ctx.notice.action ? (_openBlock(), _createElementBlock(\"button\", {\n        key: 0,\n        \"class\": \"notice-action-btn\",\n        onClick: _cache[0] || (_cache[0] = function () {\n          return $options.handleAction && $options.handleAction.apply($options, arguments);\n        })\n      }, _toDisplayString(_ctx.notice.action.text), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n        \"class\": \"notice-close-btn\",\n        onClick: _cache[1] || (_cache[1] = function () {\n          return $options.clearNotice && $options.clearNotice.apply($options, arguments);\n        })\n      }, _cache[2] || (_cache[2] = [_createElementVNode(\"i\", {\n        \"class\": \"fas fa-times\"\n      }, null, -1 /* CACHED */)]))])], 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["_createBlock", "_Transition", "name", "_ctx", "notice", "_createElementBlock", "_normalizeClass", "$options", "noticeClass", "_createElementVNode", "_hoisted_1", "noticeIcon", "_hoisted_2", "_toDisplayString", "message", "_hoisted_3", "action", "onClick", "_cache", "handleAction", "apply", "arguments", "text", "clearNotice"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\GlobalNotice.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"notice-fade\">\r\n    <div \r\n      v-if=\"notice\" \r\n      class=\"global-notice\" \r\n      :class=\"noticeClass\"\r\n    >\r\n      <div class=\"notice-content\">\r\n        <i :class=\"noticeIcon\"></i>\r\n        <span class=\"notice-message\">{{ notice.message }}</span>\r\n      </div>\r\n      <div class=\"notice-actions\">\r\n        <button \r\n          v-if=\"notice.action\" \r\n          class=\"notice-action-btn\"\r\n          @click=\"handleAction\"\r\n        >\r\n          {{ notice.action.text }}\r\n        </button>\r\n        <button \r\n          class=\"notice-close-btn\"\r\n          @click=\"clearNotice\"\r\n        >\r\n          <i class=\"fas fa-times\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n</template>\r\n\r\n<script>\r\nimport { mapState } from 'vuex';\r\n\r\nexport default {\r\n  name: 'GlobalNotice',\r\n  \r\n  computed: {\r\n    ...mapState({\r\n      notice: state => state.globalNotice\r\n    }),\r\n    \r\n    noticeClass() {\r\n      if (!this.notice) return '';\r\n      \r\n      switch (this.notice.type) {\r\n        case 'error':\r\n          return 'notice-error';\r\n        case 'warning':\r\n          return 'notice-warning';\r\n        case 'success':\r\n          return 'notice-success';\r\n        case 'preview':\r\n          return 'notice-preview';\r\n        default:\r\n          return 'notice-info';\r\n      }\r\n    },\r\n    \r\n    noticeIcon() {\r\n      if (!this.notice) return '';\r\n      \r\n      switch (this.notice.type) {\r\n        case 'error':\r\n          return 'fas fa-exclamation-circle';\r\n        case 'warning':\r\n          return 'fas fa-exclamation-triangle';\r\n        case 'success':\r\n          return 'fas fa-check-circle';\r\n        case 'preview':\r\n          return 'fas fa-eye';\r\n        default:\r\n          return 'fas fa-info-circle';\r\n      }\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    clearNotice() {\r\n      this.$store.commit('CLEAR_GLOBAL_NOTICE');\r\n    },\r\n    \r\n    handleAction() {\r\n      if (this.notice && this.notice.action && typeof this.notice.action.callback === 'function') {\r\n        this.notice.action.callback();\r\n      }\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    notice(newVal) {\r\n      if (newVal && newVal.timeout > 0) {\r\n        setTimeout(() => {\r\n          if (this.notice && this.notice === newVal) {\r\n            this.clearNotice();\r\n          }\r\n        }, newVal.timeout);\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.global-notice {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  z-index: 9999;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 20px;\r\n  background-color: #1890ff;\r\n  color: white;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.notice-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.notice-message {\r\n  font-size: 14px;\r\n}\r\n\r\n.notice-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.notice-action-btn {\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n  border: none;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.notice-action-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.notice-close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  cursor: pointer;\r\n  padding: 4px;\r\n  font-size: 16px;\r\n  transition: color 0.3s;\r\n}\r\n\r\n.notice-close-btn:hover {\r\n  color: white;\r\n}\r\n\r\n/* Notice types */\r\n.notice-error {\r\n  background-color: #ff4d4f;\r\n}\r\n\r\n.notice-warning {\r\n  background-color: #faad14;\r\n}\r\n\r\n.notice-success {\r\n  background-color: #52c41a;\r\n}\r\n\r\n.notice-info {\r\n  background-color: #1890ff;\r\n}\r\n\r\n.notice-preview {\r\n  background-color: #722ed1;\r\n}\r\n\r\n/* Transition */\r\n.notice-fade-enter-active,\r\n.notice-fade-leave-active {\r\n  transition: transform 0.3s, opacity 0.3s;\r\n}\r\n\r\n.notice-fade-enter,\r\n.notice-fade-leave-to {\r\n  transform: translateY(-100%);\r\n  opacity: 0;\r\n}\r\n</style> "], "mappings": ";;EAOW,SAAM;AAAgB;;EAEnB,SAAM;AAAgB;;EAEzB,SAAM;AAAgB;;uBAV/BA,YAAA,CA0BaC,WAAA;IA1BDC,IAAI,EAAC;EAAa;wBAC7B;MAAA,OAuBQ,CAtBCC,IAAA,CAAAC,MAAM,I,cADdC,mBAAA,CAwBM;;QAtBJ,SAAKC,eAAA,EAAC,eAAe,EACbC,QAAA,CAAAC,WAAW;UAEnBC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAA2B;QAAvB,SAAKH,eAAA,CAAEC,QAAA,CAAAI,UAAU;+BACrBF,mBAAA,CAAwD,QAAxDG,UAAwD,EAAAC,gBAAA,CAAxBV,IAAA,CAAAC,MAAM,CAACU,OAAO,iB,GAEhDL,mBAAA,CAcM,OAdNM,UAcM,GAZIZ,IAAA,CAAAC,MAAM,CAACY,MAAM,I,cADrBX,mBAAA,CAMS;;QAJP,SAAM,mBAAmB;QACxBY,OAAK,EAAAC,MAAA,QAAAA,MAAA;UAAA,OAAEX,QAAA,CAAAY,YAAA,IAAAZ,QAAA,CAAAY,YAAA,CAAAC,KAAA,CAAAb,QAAA,EAAAc,SAAA,CAAY;QAAA;0BAEjBlB,IAAA,CAAAC,MAAM,CAACY,MAAM,CAACM,IAAI,oB,mCAEvBb,mBAAA,CAKS;QAJP,SAAM,kBAAkB;QACvBQ,OAAK,EAAAC,MAAA,QAAAA,MAAA;UAAA,OAAEX,QAAA,CAAAgB,WAAA,IAAAhB,QAAA,CAAAgB,WAAA,CAAAH,KAAA,CAAAb,QAAA,EAAAc,SAAA,CAAW;QAAA;oCAEnBZ,mBAAA,CAA4B;QAAzB,SAAM;MAAc,0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}