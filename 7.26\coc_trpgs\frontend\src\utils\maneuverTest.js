/**
 * COC 7版战技系统测试
 * 验证各种战技的完整功能
 */

import CombatRules from './combatRules.js'

console.log('=== COC 7版战技系统测试 ===\n')

// 创建测试角色
const strongFighter = {
  id: 'strong1',
  name: '强壮战士',
  strength: 85,
  dexterity: 60,
  size: 75,
  fighting: 70,
  fighting_grapple: 65,
  currentWeapon: {
    name: '战锤',
    damage: '1d8+2'
  },
  conditions: []
}

const agileFighter = {
  id: 'agile1',
  name: '敏捷战士',
  strength: 60,
  dexterity: 85,
  size: 60,
  fighting: 65,
  fighting_grapple: 60,
  currentWeapon: {
    name: '长剑',
    damage: '1d8+1'
  },
  conditions: []
}

const averageFighter = {
  id: 'average1',
  name: '普通战士',
  strength: 65,
  dexterity: 65,
  size: 65,
  fighting: 55,
  fighting_grapple: 50,
  currentWeapon: {
    name: '短剑',
    damage: '1d6+1'
  },
  conditions: []
}

// 显示角色信息
console.log('=== 测试角色信息 ===')
const fighters = [strongFighter, agileFighter, averageFighter]

fighters.forEach(fighter => {
  const build = CombatRules.calculateBuild(fighter.strength, fighter.size)
  console.log(`${fighter.name}:`)
  console.log(`  力量: ${fighter.strength}, 敏捷: ${fighter.dexterity}, 体型: ${fighter.size}`)
  console.log(`  体格: ${build}`)
  console.log(`  格斗技能: ${fighter.fighting}`)
  console.log(`  擒抱技能: ${fighter.fighting_grapple}`)
  console.log()
})

// 测试1: 擒抱战技
console.log('=== 测试1: 擒抱战技 ===')

for (let i = 0; i < 3; i++) {
  console.log(`\n擒抱测试 ${i + 1}: ${strongFighter.name} vs ${agileFighter.name}`)
  
  const grappleResult = CombatRules.performGrapple(strongFighter, agileFighter)
  
  console.log(`攻击者: ${grappleResult.attacker.name}`)
  console.log(`  投掷: ${grappleResult.attacker.roll} vs 技能 ${grappleResult.attacker.skill} (${grappleResult.attacker.level})`)
  console.log(`  体格: ${grappleResult.attacker.build}, 惩罚骰: ${grappleResult.attacker.penalty}`)
  
  console.log(`防御者: ${grappleResult.defender.name}`)
  console.log(`  投掷: ${grappleResult.defender.roll} vs 技能 ${grappleResult.defender.skill} (${grappleResult.defender.level})`)
  console.log(`  体格: ${grappleResult.defender.build}, 惩罚骰: ${grappleResult.defender.penalty}`)
  
  console.log(`结果: ${grappleResult.description}`)
  
  if (grappleResult.grappleResult.success) {
    console.log(`  效果: ${grappleResult.grappleResult.effect}`)
    if (grappleResult.grappleResult.damage > 0) {
      console.log(`  💥 伤害: ${grappleResult.grappleResult.damage} 点`)
    }
    console.log(`  ${agileFighter.name} 状态: ${agileFighter.conditions.join(', ') || '无'}`)
  }
  
  // 重置状态
  agileFighter.conditions = []
}

// 测试2: 缴械战技
console.log('\n\n=== 测试2: 缴械战技 ===')

for (let i = 0; i < 3; i++) {
  console.log(`\n缴械测试 ${i + 1}: ${strongFighter.name} vs ${averageFighter.name}`)
  
  // 确保目标有武器
  if (!averageFighter.currentWeapon) {
    averageFighter.currentWeapon = { name: '短剑', damage: '1d6+1' }
  }
  
  const disarmResult = CombatRules.performDisarm(strongFighter, averageFighter)
  
  console.log(`攻击者: ${disarmResult.attacker.name}`)
  console.log(`  投掷: ${disarmResult.attacker.roll} vs 技能 ${disarmResult.attacker.skill} (${disarmResult.attacker.level})`)
  
  console.log(`防御者: ${disarmResult.defender.name}`)
  console.log(`  投掷: ${disarmResult.defender.roll} vs 技能 ${disarmResult.defender.skill} (${disarmResult.defender.level})`)
  
  console.log(`结果: ${disarmResult.description}`)
  
  if (disarmResult.disarmResult.success) {
    console.log(`  武器掉落: ${disarmResult.disarmResult.weaponDropped.name}`)
    console.log(`  掉落距离: ${disarmResult.disarmResult.distance} 米`)
    console.log(`  ${averageFighter.name} 当前武器: ${averageFighter.currentWeapon ? averageFighter.currentWeapon.name : '无'}`)
  }
  
  // 重置武器
  averageFighter.currentWeapon = { name: '短剑', damage: '1d6+1' }
}

// 测试3: 撞倒战技
console.log('\n\n=== 测试3: 撞倒战技 ===')

for (let i = 0; i < 3; i++) {
  console.log(`\n撞倒测试 ${i + 1}: ${strongFighter.name} vs ${agileFighter.name}`)
  
  const knockdownResult = CombatRules.performKnockdown(strongFighter, agileFighter)
  
  console.log(`攻击者: ${knockdownResult.attacker.name}`)
  console.log(`  投掷: ${knockdownResult.attacker.roll} vs 技能 ${knockdownResult.attacker.skill} (${knockdownResult.attacker.level})`)
  console.log(`  体格: ${knockdownResult.attacker.build}, 奖励骰: ${knockdownResult.attacker.bonus}`)
  
  console.log(`防御者: ${knockdownResult.defender.name}`)
  console.log(`  投掷: ${knockdownResult.defender.roll} vs 技能 ${knockdownResult.defender.skill} (${knockdownResult.defender.level})`)
  console.log(`  体格: ${knockdownResult.defender.build}, 奖励骰: ${knockdownResult.defender.bonus}`)
  
  console.log(`结果: ${knockdownResult.description}`)
  
  if (knockdownResult.knockdownResult.success) {
    console.log(`  倒地: ${knockdownResult.knockdownResult.prone}`)
    if (knockdownResult.knockdownResult.damage > 0) {
      console.log(`  💥 伤害: ${knockdownResult.knockdownResult.damage} 点`)
    }
    console.log(`  ${agileFighter.name} 状态: ${agileFighter.conditions.join(', ') || '无'}`)
  }
  
  // 重置状态
  agileFighter.conditions = []
}

// 测试4: 推挤战技
console.log('\n\n=== 测试4: 推挤战技 ===')

for (let i = 0; i < 3; i++) {
  console.log(`\n推挤测试 ${i + 1}: ${strongFighter.name} vs ${averageFighter.name}`)
  
  const pushResult = CombatRules.performPush(strongFighter, averageFighter)
  
  console.log(`攻击者: ${pushResult.attacker.name}`)
  console.log(`  投掷: ${pushResult.attacker.roll} vs 力量 ${pushResult.attacker.skill} (${pushResult.attacker.level})`)
  console.log(`  体格: ${pushResult.attacker.build}`)
  
  console.log(`防御者: ${pushResult.defender.name}`)
  console.log(`  投掷: ${pushResult.defender.roll} vs 力量 ${pushResult.defender.skill} (${pushResult.defender.level})`)
  console.log(`  体格: ${pushResult.defender.build}`)
  
  console.log(`结果: ${pushResult.description}`)
  
  if (pushResult.pushResult.success) {
    console.log(`  推挤距离: ${pushResult.pushResult.distance} 米`)
    console.log(`  击倒: ${pushResult.pushResult.knockdown}`)
    if (pushResult.pushResult.knockdown) {
      console.log(`  ${averageFighter.name} 状态: ${averageFighter.conditions.join(', ') || '无'}`)
    }
  }
  
  // 重置状态
  averageFighter.conditions = []
}

// 测试5: 挣脱擒抱
console.log('\n\n=== 测试5: 挣脱擒抱 ===')

// 先让敏捷战士被擒抱
agileFighter.conditions = ['grappled']

for (let i = 0; i < 3; i++) {
  console.log(`\n挣脱测试 ${i + 1}: ${agileFighter.name} 尝试挣脱 ${strongFighter.name} 的擒抱`)
  
  const breakResult = CombatRules.breakFree(agileFighter, strongFighter)
  
  console.log(`被擒抱者: ${breakResult.grappled.name}`)
  console.log(`  投掷: ${breakResult.grappled.roll} vs ${breakResult.grappled.method === 'strength' ? '力量' : '敏捷'} ${breakResult.grappled.skill} (${breakResult.grappled.level})`)
  console.log(`  使用方法: ${breakResult.grappled.method === 'strength' ? '力量' : '敏捷'}`)
  
  console.log(`擒抱者: ${breakResult.grappler.name}`)
  console.log(`  投掷: ${breakResult.grappler.roll} vs 力量 ${breakResult.grappler.skill} (${breakResult.grappler.level})`)
  
  console.log(`结果: ${breakResult.description}`)
  
  if (breakResult.breakResult.success) {
    console.log(`  ${agileFighter.name} 状态: ${agileFighter.conditions.join(', ') || '无'}`)
  } else {
    console.log(`  ${agileFighter.name} 仍被擒抱`)
  }
  
  // 重置状态以便下次测试
  agileFighter.conditions = ['grappled']
}

// 测试6: 体格差异影响
console.log('\n\n=== 测试6: 体格差异影响 ===')

const giantFighter = {
  id: 'giant',
  name: '巨人战士',
  strength: 100,
  dexterity: 40,
  size: 100,
  fighting: 60,
  fighting_grapple: 65,
  conditions: []
}

const smallFighter = {
  id: 'small',
  name: '小个子战士',
  strength: 40,
  dexterity: 80,
  size: 40,
  fighting: 70,
  fighting_grapple: 65,
  conditions: []
}

const giantBuild = CombatRules.calculateBuild(giantFighter.strength, giantFighter.size)
const smallBuild = CombatRules.calculateBuild(smallFighter.strength, smallFighter.size)

console.log(`${giantFighter.name}: 体格 ${giantBuild}`)
console.log(`${smallFighter.name}: 体格 ${smallBuild}`)
console.log(`体格差异: ${giantBuild - smallBuild}`)

console.log(`\n巨人擒抱小个子:`)
const giantGrapple = CombatRules.performGrapple(giantFighter, smallFighter)
console.log(`  巨人惩罚骰: ${giantGrapple.attacker.penalty}`)
console.log(`  小个子惩罚骰: ${giantGrapple.defender.penalty}`)
console.log(`  结果: ${giantGrapple.description}`)

// 测试7: 连续战技
console.log('\n\n=== 测试7: 连续战技组合 ===')

console.log(`场景: ${strongFighter.name} 对 ${averageFighter.name} 进行连续战技`)

// 重置状态
averageFighter.conditions = []
averageFighter.currentWeapon = { name: '短剑', damage: '1d6+1' }

// 1. 先尝试缴械
console.log(`\n1. 缴械尝试:`)
const comboDisarm = CombatRules.performDisarm(strongFighter, averageFighter)
console.log(`   ${comboDisarm.description}`)

if (comboDisarm.disarmResult.success) {
  // 2. 缴械成功后尝试擒抱
  console.log(`\n2. 缴械成功，尝试擒抱:`)
  const comboGrapple = CombatRules.performGrapple(strongFighter, averageFighter)
  console.log(`   ${comboGrapple.description}`)
  
  if (comboGrapple.grappleResult.success) {
    // 3. 擒抱成功后尝试撞倒
    console.log(`\n3. 擒抱成功，尝试撞倒:`)
    const comboKnockdown = CombatRules.performKnockdown(strongFighter, averageFighter)
    console.log(`   ${comboKnockdown.description}`)
    
    console.log(`\n最终状态:`)
    console.log(`   ${averageFighter.name} 武器: ${averageFighter.currentWeapon ? averageFighter.currentWeapon.name : '无'}`)
    console.log(`   ${averageFighter.name} 状态: ${averageFighter.conditions.join(', ') || '无'}`)
  }
} else {
  console.log(`   缴械失败，无法进行后续战技`)
}

// 测试8: 战技失败后果
console.log('\n\n=== 测试8: 战技失败分析 ===')

let successCount = { grapple: 0, disarm: 0, knockdown: 0, push: 0 }
let totalTests = 10

console.log(`进行 ${totalTests} 次各种战技测试，统计成功率...`)

for (let i = 0; i < totalTests; i++) {
  // 重置状态
  averageFighter.conditions = []
  averageFighter.currentWeapon = { name: '短剑', damage: '1d6+1' }
  
  // 测试各种战技
  const grapple = CombatRules.performGrapple(strongFighter, averageFighter)
  if (grapple.grappleResult.success) successCount.grapple++
  
  const disarm = CombatRules.performDisarm(strongFighter, averageFighter)
  if (disarm.disarmResult.success) successCount.disarm++
  
  const knockdown = CombatRules.performKnockdown(strongFighter, averageFighter)
  if (knockdown.knockdownResult.success) successCount.knockdown++
  
  const push = CombatRules.performPush(strongFighter, averageFighter)
  if (push.pushResult.success) successCount.push++
}

console.log(`\n战技成功率统计 (${strongFighter.name} vs ${averageFighter.name}):`)
console.log(`  擒抱: ${successCount.grapple}/${totalTests} (${(successCount.grapple/totalTests*100).toFixed(1)}%)`)
console.log(`  缴械: ${successCount.disarm}/${totalTests} (${(successCount.disarm/totalTests*100).toFixed(1)}%)`)
console.log(`  撞倒: ${successCount.knockdown}/${totalTests} (${(successCount.knockdown/totalTests*100).toFixed(1)}%)`)
console.log(`  推挤: ${successCount.push}/${totalTests} (${(successCount.push/totalTests*100).toFixed(1)}%)`)

console.log('\n=== 战技系统测试完成 ===')
console.log('所有战技功能已验证，符合COC 7版规则书要求！')