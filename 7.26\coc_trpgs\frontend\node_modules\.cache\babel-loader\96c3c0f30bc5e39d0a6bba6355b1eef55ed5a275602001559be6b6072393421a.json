{"ast": null, "code": "import \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, vModelText as _vModelText, withDirectives as _withDirectives, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"combat-log\"\n};\nvar _hoisted_2 = {\n  \"class\": \"log-header\"\n};\nvar _hoisted_3 = {\n  \"class\": \"header-right\"\n};\nvar _hoisted_4 = {\n  \"class\": \"log-filters\"\n};\nvar _hoisted_5 = {\n  \"class\": \"filter-group\"\n};\nvar _hoisted_6 = [\"onClick\"];\nvar _hoisted_7 = {\n  \"class\": \"search-box\"\n};\nvar _hoisted_8 = {\n  \"class\": \"log-content\",\n  ref: \"logContent\"\n};\nvar _hoisted_9 = {\n  \"class\": \"log-timestamp\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  \"class\": \"log-round\"\n};\nvar _hoisted_11 = {\n  \"class\": \"log-icon\"\n};\nvar _hoisted_12 = {\n  \"class\": \"log-message\"\n};\nvar _hoisted_13 = [\"innerHTML\"];\nvar _hoisted_14 = {\n  key: 0,\n  \"class\": \"message-details\"\n};\nvar _hoisted_15 = {\n  \"class\": \"detail-label\"\n};\nvar _hoisted_16 = {\n  \"class\": \"detail-value\"\n};\nvar _hoisted_17 = {\n  key: 1,\n  \"class\": \"dice-result\"\n};\nvar _hoisted_18 = {\n  \"class\": \"dice-formula\"\n};\nvar _hoisted_19 = {\n  \"class\": \"dice-rolls\"\n};\nvar _hoisted_20 = {\n  \"class\": \"dice-total\"\n};\nvar _hoisted_21 = {\n  key: 2,\n  \"class\": \"success-level\"\n};\nvar _hoisted_22 = {\n  key: 1,\n  \"class\": \"log-participant\"\n};\nvar _hoisted_23 = [\"src\", \"alt\"];\nvar _hoisted_24 = {\n  key: 0,\n  \"class\": \"empty-state\"\n};\nvar _hoisted_25 = {\n  \"class\": \"log-footer\"\n};\nvar _hoisted_26 = {\n  \"class\": \"log-stats\"\n};\nvar _hoisted_27 = {\n  \"class\": \"log-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 日志头部 \"), _createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n    \"class\": \"header-left\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-scroll\"\n  }), _createElementVNode(\"h3\", null, \"战斗日志\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.toggleAutoScroll && $options.toggleAutoScroll.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"auto-scroll-btn\", {\n      active: $props.autoScroll\n    }])\n  }, _cache[6] || (_cache[6] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-arrow-down\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.clearLog && $options.clearLog.apply($options, arguments);\n    }),\n    \"class\": \"clear-btn\",\n    title: \"清空日志\"\n  }, _cache[7] || (_cache[7] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-trash\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = function () {\n      return $options.exportLog && $options.exportLog.apply($options, arguments);\n    }),\n    \"class\": \"export-btn\",\n    title: \"导出日志\"\n  }, _cache[8] || (_cache[8] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-download\"\n  }, null, -1 /* CACHED */)]))])]), _createCommentVNode(\" 过滤器 \"), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.logFilters, function (filter) {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: filter.type,\n      onClick: function onClick($event) {\n        return $options.toggleFilter(filter.type);\n      },\n      \"class\": _normalizeClass([\"filter-btn\", {\n        active: $data.activeFilters.includes(filter.type)\n      }])\n    }, [_createElementVNode(\"i\", {\n      \"class\": _normalizeClass(filter.icon)\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(filter.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_6);\n  }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_7, [_withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $data.searchQuery = $event;\n    }),\n    type: \"text\",\n    placeholder: \"搜索日志...\",\n    \"class\": \"search-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.searchQuery]]), _cache[10] || (_cache[10] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-search search-icon\"\n  }, null, -1 /* CACHED */))])]), _createCommentVNode(\" 日志内容 \"), _createElementVNode(\"div\", _hoisted_8, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredLogs, function (entry, index) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: entry.id || index,\n      \"class\": _normalizeClass([\"log-entry\", [entry.type, entry.severity || 'normal']])\n    }, [_createCommentVNode(\" 时间戳 \"), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($options.formatTime(entry.timestamp)), 1 /* TEXT */), _createCommentVNode(\" 轮次信息 \"), entry.round ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, \" 第\" + _toDisplayString(entry.round) + \"轮 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 日志图标 \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"i\", {\n      \"class\": _normalizeClass($options.getLogIcon(entry.type))\n    }, null, 2 /* CLASS */)]), _createCommentVNode(\" 日志内容 \"), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"div\", {\n      \"class\": \"message-text\",\n      innerHTML: $options.formatMessage(entry.message)\n    }, null, 8 /* PROPS */, _hoisted_13), _createCommentVNode(\" 详细信息 \"), entry.details ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(entry.details, function (detail, key) {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: key,\n        \"class\": \"detail-item\"\n      }, [_createElementVNode(\"span\", _hoisted_15, _toDisplayString($options.formatDetailLabel(key)) + \":\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_16, _toDisplayString($options.formatDetailValue(detail)), 1 /* TEXT */)]);\n    }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 骰子结果 \"), entry.diceRoll ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString(entry.diceRoll.formula), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(entry.diceRoll.rolls, function (roll, i) {\n      return _openBlock(), _createElementBlock(\"span\", {\n        key: i,\n        \"class\": _normalizeClass([\"dice-roll\", $options.getDiceRollClass(roll, entry.diceRoll)])\n      }, _toDisplayString(roll), 3 /* TEXT, CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))]), _createElementVNode(\"div\", _hoisted_20, \"总计: \" + _toDisplayString(entry.diceRoll.total), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 成功等级 \"), entry.successLevel ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createElementVNode(\"span\", {\n      \"class\": _normalizeClass([\"success-badge\", entry.successLevel])\n    }, _toDisplayString($options.getSuccessLevelText(entry.successLevel)), 3 /* TEXT, CLASS */)])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 参与者头像 \"), entry.participant ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createElementVNode(\"img\", {\n      src: entry.participant.avatar || '/default-avatar.png',\n      alt: entry.participant.name,\n      \"class\": \"participant-avatar\"\n    }, null, 8 /* PROPS */, _hoisted_23)])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n  }), 128 /* KEYED_FRAGMENT */)), _createCommentVNode(\" 空状态 \"), $options.filteredLogs.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [_cache[11] || (_cache[11] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-inbox\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"p\", null, _toDisplayString($data.searchQuery ? '没有找到匹配的日志' : '暂无战斗日志'), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), _createCommentVNode(\" 底部状态 \"), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"span\", null, \"总计: \" + _toDisplayString($props.combatLogs.length) + \" 条\", 1 /* TEXT */), _createElementVNode(\"span\", null, \"显示: \" + _toDisplayString($options.filteredLogs.length) + \" 条\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_27, [_createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function () {\n      return $options.scrollToTop && $options.scrollToTop.apply($options, arguments);\n    }),\n    \"class\": \"scroll-btn\"\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-arrow-up\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 顶部 \")])), _createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = function () {\n      return $options.scrollToBottom && $options.scrollToBottom.apply($options, arguments);\n    }),\n    \"class\": \"scroll-btn\"\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-arrow-down\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 底部 \")]))])])]);\n}", "map": {"version": 3, "names": ["ref", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "onClick", "_cache", "$options", "toggleAutoScroll", "apply", "arguments", "_normalizeClass", "active", "$props", "autoScroll", "clearLog", "title", "exportLog", "_hoisted_4", "_hoisted_5", "_Fragment", "_renderList", "$data", "logFilters", "filter", "key", "type", "$event", "toggleFilter", "activeFilters", "includes", "icon", "_toDisplayString", "name", "_hoisted_7", "searchQuery", "placeholder", "_hoisted_8", "filteredLogs", "entry", "index", "id", "severity", "_hoisted_9", "formatTime", "timestamp", "round", "_hoisted_10", "_hoisted_11", "getLogIcon", "_hoisted_12", "innerHTML", "formatMessage", "message", "details", "_hoisted_14", "detail", "_hoisted_15", "formatDetailLabel", "_hoisted_16", "formatDetailValue", "diceRoll", "_hoisted_17", "_hoisted_18", "formula", "_hoisted_19", "rolls", "roll", "i", "getDiceRollClass", "_hoisted_20", "total", "successLevel", "_hoisted_21", "getSuccessLevelText", "participant", "_hoisted_22", "src", "avatar", "alt", "length", "_hoisted_24", "_hoisted_25", "_hoisted_26", "combatLogs", "_hoisted_27", "scrollToTop", "scrollToBottom"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CombatLog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"combat-log\">\r\n    <!-- 日志头部 -->\r\n    <div class=\"log-header\">\r\n      <div class=\"header-left\">\r\n        <i class=\"fas fa-scroll\"></i>\r\n        <h3>战斗日志</h3>\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <button @click=\"toggleAutoScroll\" class=\"auto-scroll-btn\" :class=\"{ active: autoScroll }\">\r\n          <i class=\"fas fa-arrow-down\"></i>\r\n        </button>\r\n        <button @click=\"clearLog\" class=\"clear-btn\" title=\"清空日志\">\r\n          <i class=\"fas fa-trash\"></i>\r\n        </button>\r\n        <button @click=\"exportLog\" class=\"export-btn\" title=\"导出日志\">\r\n          <i class=\"fas fa-download\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 过滤器 -->\r\n    <div class=\"log-filters\">\r\n      <div class=\"filter-group\">\r\n        <button \r\n          v-for=\"filter in logFilters\" \r\n          :key=\"filter.type\"\r\n          @click=\"toggleFilter(filter.type)\"\r\n          class=\"filter-btn\"\r\n          :class=\"{ active: activeFilters.includes(filter.type) }\"\r\n        >\r\n          <i :class=\"filter.icon\"></i>\r\n          <span>{{ filter.name }}</span>\r\n        </button>\r\n      </div>\r\n      \r\n      <div class=\"search-box\">\r\n        <input \r\n          v-model=\"searchQuery\" \r\n          type=\"text\" \r\n          placeholder=\"搜索日志...\"\r\n          class=\"search-input\"\r\n        >\r\n        <i class=\"fas fa-search search-icon\"></i>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 日志内容 -->\r\n    <div class=\"log-content\" ref=\"logContent\">\r\n      <div \r\n        v-for=\"(entry, index) in filteredLogs\" \r\n        :key=\"entry.id || index\"\r\n        class=\"log-entry\"\r\n        :class=\"[entry.type, entry.severity || 'normal']\"\r\n      >\r\n        <!-- 时间戳 -->\r\n        <div class=\"log-timestamp\">\r\n          {{ formatTime(entry.timestamp) }}\r\n        </div>\r\n        \r\n        <!-- 轮次信息 -->\r\n        <div class=\"log-round\" v-if=\"entry.round\">\r\n          第{{ entry.round }}轮\r\n        </div>\r\n        \r\n        <!-- 日志图标 -->\r\n        <div class=\"log-icon\">\r\n          <i :class=\"getLogIcon(entry.type)\"></i>\r\n        </div>\r\n        \r\n        <!-- 日志内容 -->\r\n        <div class=\"log-message\">\r\n          <div class=\"message-text\" v-html=\"formatMessage(entry.message)\"></div>\r\n          \r\n          <!-- 详细信息 -->\r\n          <div class=\"message-details\" v-if=\"entry.details\">\r\n            <div \r\n              v-for=\"(detail, key) in entry.details\" \r\n              :key=\"key\"\r\n              class=\"detail-item\"\r\n            >\r\n              <span class=\"detail-label\">{{ formatDetailLabel(key) }}:</span>\r\n              <span class=\"detail-value\">{{ formatDetailValue(detail) }}</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 骰子结果 -->\r\n          <div class=\"dice-result\" v-if=\"entry.diceRoll\">\r\n            <div class=\"dice-formula\">{{ entry.diceRoll.formula }}</div>\r\n            <div class=\"dice-rolls\">\r\n              <span \r\n                v-for=\"(roll, i) in entry.diceRoll.rolls\" \r\n                :key=\"i\"\r\n                class=\"dice-roll\"\r\n                :class=\"getDiceRollClass(roll, entry.diceRoll)\"\r\n              >\r\n                {{ roll }}\r\n              </span>\r\n            </div>\r\n            <div class=\"dice-total\">总计: {{ entry.diceRoll.total }}</div>\r\n          </div>\r\n          \r\n          <!-- 成功等级 -->\r\n          <div class=\"success-level\" v-if=\"entry.successLevel\">\r\n            <span class=\"success-badge\" :class=\"entry.successLevel\">\r\n              {{ getSuccessLevelText(entry.successLevel) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 参与者头像 -->\r\n        <div class=\"log-participant\" v-if=\"entry.participant\">\r\n          <img \r\n            :src=\"entry.participant.avatar || '/default-avatar.png'\" \r\n            :alt=\"entry.participant.name\"\r\n            class=\"participant-avatar\"\r\n          >\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 空状态 -->\r\n      <div v-if=\"filteredLogs.length === 0\" class=\"empty-state\">\r\n        <i class=\"fas fa-inbox\"></i>\r\n        <p>{{ searchQuery ? '没有找到匹配的日志' : '暂无战斗日志' }}</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 底部状态 -->\r\n    <div class=\"log-footer\">\r\n      <div class=\"log-stats\">\r\n        <span>总计: {{ combatLogs.length }} 条</span>\r\n        <span>显示: {{ filteredLogs.length }} 条</span>\r\n      </div>\r\n      <div class=\"log-actions\">\r\n        <button @click=\"scrollToTop\" class=\"scroll-btn\">\r\n          <i class=\"fas fa-arrow-up\"></i>\r\n          顶部\r\n        </button>\r\n        <button @click=\"scrollToBottom\" class=\"scroll-btn\">\r\n          <i class=\"fas fa-arrow-down\"></i>\r\n          底部\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CombatLog',\r\n  props: {\r\n    combatLogs: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    autoScroll: {\r\n      type: Boolean,\r\n      default: true\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      // 过滤器\r\n      activeFilters: ['all'],\r\n      searchQuery: '',\r\n      \r\n      // 日志过滤器配置\r\n      logFilters: [\r\n        { type: 'all', name: '全部', icon: 'fas fa-list' },\r\n        { type: 'attack', name: '攻击', icon: 'fas fa-sword' },\r\n        { type: 'damage', name: '伤害', icon: 'fas fa-heart-broken' },\r\n        { type: 'heal', name: '治疗', icon: 'fas fa-heart' },\r\n        { type: 'move', name: '移动', icon: 'fas fa-walking' },\r\n        { type: 'skill', name: '技能', icon: 'fas fa-dice-d20' },\r\n        { type: 'status', name: '状态', icon: 'fas fa-magic' },\r\n        { type: 'system', name: '系统', icon: 'fas fa-cog' }\r\n      ],\r\n      \r\n      // 自动滚动状态\r\n      autoScrollEnabled: true\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 过滤后的日志\r\n    filteredLogs() {\r\n      let logs = this.combatLogs\r\n      \r\n      // 类型过滤\r\n      if (!this.activeFilters.includes('all')) {\r\n        logs = logs.filter(log => this.activeFilters.includes(log.type))\r\n      }\r\n      \r\n      // 搜索过滤\r\n      if (this.searchQuery.trim()) {\r\n        const query = this.searchQuery.toLowerCase()\r\n        logs = logs.filter(log => \r\n          log.message.toLowerCase().includes(query) ||\r\n          log.participant?.name.toLowerCase().includes(query)\r\n        )\r\n      }\r\n      \r\n      return logs\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    // 监听日志变化，自动滚动到底部\r\n    combatLogs: {\r\n      handler() {\r\n        if (this.autoScrollEnabled) {\r\n          this.$nextTick(() => {\r\n            this.scrollToBottom()\r\n          })\r\n        }\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    // 切换过滤器\r\n    toggleFilter(filterType) {\r\n      if (filterType === 'all') {\r\n        this.activeFilters = ['all']\r\n      } else {\r\n        const index = this.activeFilters.indexOf(filterType)\r\n        if (index > -1) {\r\n          this.activeFilters.splice(index, 1)\r\n          if (this.activeFilters.length === 0) {\r\n            this.activeFilters = ['all']\r\n          }\r\n        } else {\r\n          this.activeFilters = this.activeFilters.filter(f => f !== 'all')\r\n          this.activeFilters.push(filterType)\r\n        }\r\n      }\r\n    },\r\n    \r\n    // 切换自动滚动\r\n    toggleAutoScroll() {\r\n      this.autoScrollEnabled = !this.autoScrollEnabled\r\n      this.$emit('toggle-auto-scroll', this.autoScrollEnabled)\r\n    },\r\n    \r\n    // 清空日志\r\n    clearLog() {\r\n      this.$emit('clear-log')\r\n    },\r\n    \r\n    // 导出日志\r\n    exportLog() {\r\n      const logText = this.combatLogs.map(entry => {\r\n        const time = this.formatTime(entry.timestamp)\r\n        const round = entry.round ? `[第${entry.round}轮] ` : ''\r\n        return `${time} ${round}${entry.message}`\r\n      }).join('\\n')\r\n      \r\n      const blob = new Blob([logText], { type: 'text/plain' })\r\n      const url = URL.createObjectURL(blob)\r\n      const a = document.createElement('a')\r\n      a.href = url\r\n      a.download = `combat-log-${new Date().toISOString().slice(0, 10)}.txt`\r\n      a.click()\r\n      URL.revokeObjectURL(url)\r\n    },\r\n    \r\n    // 滚动到顶部\r\n    scrollToTop() {\r\n      const content = this.$refs.logContent\r\n      if (content) {\r\n        content.scrollTop = 0\r\n      }\r\n    },\r\n    \r\n    // 滚动到底部\r\n    scrollToBottom() {\r\n      const content = this.$refs.logContent\r\n      if (content) {\r\n        content.scrollTop = content.scrollHeight\r\n      }\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp)\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    \r\n    // 格式化消息\r\n    formatMessage(message) {\r\n      // 高亮关键词\r\n      return message\r\n        .replace(/(\\d+)点伤害/g, '<span class=\"damage-highlight\">$1点伤害</span>')\r\n        .replace(/(\\d+)点治疗/g, '<span class=\"heal-highlight\">$1点治疗</span>')\r\n        .replace(/(大成功|极难成功|困难成功|常规成功|失败|大失败)/g, '<span class=\"success-highlight\">$1</span>')\r\n        .replace(/投掷(\\d+)/g, '<span class=\"roll-highlight\">投掷$1</span>')\r\n    },\r\n    \r\n    // 获取日志图标\r\n    getLogIcon(type) {\r\n      const icons = {\r\n        attack: 'fas fa-sword',\r\n        damage: 'fas fa-heart-broken',\r\n        heal: 'fas fa-heart',\r\n        move: 'fas fa-walking',\r\n        skill: 'fas fa-dice-d20',\r\n        status: 'fas fa-magic',\r\n        system: 'fas fa-cog',\r\n        round_start: 'fas fa-play',\r\n        round_end: 'fas fa-stop',\r\n        combat_start: 'fas fa-flag',\r\n        combat_end: 'fas fa-flag-checkered'\r\n      }\r\n      return icons[type] || 'fas fa-info-circle'\r\n    },\r\n    \r\n    // 格式化详细信息标签\r\n    formatDetailLabel(key) {\r\n      const labels = {\r\n        attacker: '攻击者',\r\n        target: '目标',\r\n        weapon: '武器',\r\n        damage: '伤害',\r\n        skill: '技能',\r\n        roll: '投掷',\r\n        modifier: '修正',\r\n        result: '结果'\r\n      }\r\n      return labels[key] || key\r\n    },\r\n    \r\n    // 格式化详细信息值\r\n    formatDetailValue(value) {\r\n      if (typeof value === 'object') {\r\n        return JSON.stringify(value)\r\n      }\r\n      return String(value)\r\n    },\r\n    \r\n    // 获取骰子投掷样式类\r\n    getDiceRollClass(roll, diceRoll) {\r\n      if (diceRoll.formula.includes('d100')) {\r\n        if (roll === 1) return 'critical-success'\r\n        if (roll >= 96) return 'critical-failure'\r\n      } else if (diceRoll.formula.includes('d20')) {\r\n        if (roll === 20) return 'critical-success'\r\n        if (roll === 1) return 'critical-failure'\r\n      }\r\n      return 'normal'\r\n    },\r\n    \r\n    // 获取成功等级文本\r\n    getSuccessLevelText(level) {\r\n      const texts = {\r\n        critical: '大成功',\r\n        extreme: '极难成功',\r\n        hard: '困难成功',\r\n        regular: '常规成功',\r\n        failure: '失败',\r\n        fumble: '大失败'\r\n      }\r\n      return texts[level] || level\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.combat-log {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 2px solid #0f3460;\r\n  border-radius: 12px;\r\n  padding: 16px;\r\n  color: #e94560;\r\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 日志头部 */\r\n.log-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  padding-bottom: 8px;\r\n  border-bottom: 2px solid #0f3460;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left h3 {\r\n  margin: 0;\r\n  color: #e94560;\r\n  font-size: 1.1rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  gap: 6px;\r\n}\r\n\r\n.auto-scroll-btn,\r\n.clear-btn,\r\n.export-btn {\r\n  width: 28px;\r\n  height: 28px;\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 4px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.auto-scroll-btn:hover,\r\n.clear-btn:hover,\r\n.export-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n.auto-scroll-btn.active {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n}\r\n\r\n/* 过滤器 */\r\n.log-filters {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12px;\r\n  gap: 12px;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  gap: 4px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-btn {\r\n  padding: 4px 8px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n  color: #bdc3c7;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.filter-btn:hover {\r\n  background: rgba(15, 52, 96, 0.5);\r\n  border-color: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.filter-btn.active {\r\n  background: rgba(233, 69, 96, 0.3);\r\n  border-color: #e94560;\r\n  color: #e94560;\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  min-width: 150px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  padding: 6px 30px 6px 10px;\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n  color: #ecf0f1;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.search-input::placeholder {\r\n  color: #bdc3c7;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  right: 8px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: #bdc3c7;\r\n  font-size: 0.8rem;\r\n}\r\n\r\n/* 日志内容 */\r\n.log-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  margin-bottom: 12px;\r\n  padding-right: 8px;\r\n}\r\n\r\n.log-entry {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n  padding: 8px;\r\n  margin-bottom: 6px;\r\n  background: rgba(15, 52, 96, 0.2);\r\n  border-left: 3px solid transparent;\r\n  border-radius: 6px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.log-entry:hover {\r\n  background: rgba(15, 52, 96, 0.3);\r\n}\r\n\r\n/* 日志类型样式 */\r\n.log-entry.attack { border-left-color: #e74c3c; }\r\n.log-entry.damage { border-left-color: #c0392b; }\r\n.log-entry.heal { border-left-color: #27ae60; }\r\n.log-entry.move { border-left-color: #3498db; }\r\n.log-entry.skill { border-left-color: #9b59b6; }\r\n.log-entry.status { border-left-color: #f39c12; }\r\n.log-entry.system { border-left-color: #95a5a6; }\r\n\r\n/* 严重程度样式 */\r\n.log-entry.critical {\r\n  background: rgba(231, 76, 60, 0.1);\r\n  border-left-color: #e74c3c;\r\n}\r\n\r\n.log-entry.warning {\r\n  background: rgba(243, 156, 18, 0.1);\r\n  border-left-color: #f39c12;\r\n}\r\n\r\n.log-entry.success {\r\n  background: rgba(39, 174, 96, 0.1);\r\n  border-left-color: #27ae60;\r\n}\r\n\r\n.log-timestamp {\r\n  font-size: 0.7rem;\r\n  color: #7f8c8d;\r\n  min-width: 60px;\r\n  text-align: right;\r\n}\r\n\r\n.log-round {\r\n  font-size: 0.7rem;\r\n  color: #e94560;\r\n  background: rgba(233, 69, 96, 0.2);\r\n  padding: 2px 6px;\r\n  border-radius: 10px;\r\n  min-width: 50px;\r\n  text-align: center;\r\n}\r\n\r\n.log-icon {\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #e94560;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.log-message {\r\n  flex: 1;\r\n  font-size: 0.9rem;\r\n  line-height: 1.4;\r\n}\r\n\r\n.message-text {\r\n  color: #ecf0f1;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.message-details {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.detail-item {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.detail-label {\r\n  font-weight: bold;\r\n}\r\n\r\n.detail-value {\r\n  color: #ecf0f1;\r\n}\r\n\r\n/* 骰子结果 */\r\n.dice-result {\r\n  margin-top: 6px;\r\n  padding: 6px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.dice-formula {\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.dice-rolls {\r\n  display: flex;\r\n  gap: 4px;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.dice-roll {\r\n  padding: 2px 6px;\r\n  background: rgba(52, 73, 94, 0.8);\r\n  border-radius: 4px;\r\n  font-size: 0.8rem;\r\n  font-weight: bold;\r\n  color: #ecf0f1;\r\n}\r\n\r\n.dice-roll.critical-success {\r\n  background: #27ae60;\r\n  color: white;\r\n}\r\n\r\n.dice-roll.critical-failure {\r\n  background: #e74c3c;\r\n  color: white;\r\n}\r\n\r\n.dice-total {\r\n  font-size: 0.8rem;\r\n  font-weight: bold;\r\n  color: #e94560;\r\n}\r\n\r\n/* 成功等级 */\r\n.success-level {\r\n  margin-top: 4px;\r\n}\r\n\r\n.success-badge {\r\n  padding: 2px 8px;\r\n  border-radius: 10px;\r\n  font-size: 0.7rem;\r\n  font-weight: bold;\r\n}\r\n\r\n.success-badge.critical { background: #27ae60; color: white; }\r\n.success-badge.extreme { background: #2ecc71; color: white; }\r\n.success-badge.hard { background: #3498db; color: white; }\r\n.success-badge.regular { background: #95a5a6; color: white; }\r\n.success-badge.failure { background: #e67e22; color: white; }\r\n.success-badge.fumble { background: #e74c3c; color: white; }\r\n\r\n.log-participant {\r\n  width: 24px;\r\n  height: 24px;\r\n}\r\n\r\n.participant-avatar {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 高亮样式 */\r\n.damage-highlight {\r\n  color: #e74c3c;\r\n  font-weight: bold;\r\n}\r\n\r\n.heal-highlight {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n}\r\n\r\n.success-highlight {\r\n  color: #f39c12;\r\n  font-weight: bold;\r\n}\r\n\r\n.roll-highlight {\r\n  color: #3498db;\r\n  font-weight: bold;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 200px;\r\n  color: #7f8c8d;\r\n}\r\n\r\n.empty-state i {\r\n  font-size: 3rem;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n/* 底部状态 */\r\n.log-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: 8px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.log-stats {\r\n  display: flex;\r\n  gap: 12px;\r\n  font-size: 0.8rem;\r\n  color: #bdc3c7;\r\n}\r\n\r\n.log-actions {\r\n  display: flex;\r\n  gap: 6px;\r\n}\r\n\r\n.scroll-btn {\r\n  padding: 4px 8px;\r\n  background: rgba(15, 52, 96, 0.8);\r\n  border: 1px solid #0f3460;\r\n  border-radius: 4px;\r\n  color: #e94560;\r\n  cursor: pointer;\r\n  font-size: 0.8rem;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.scroll-btn:hover {\r\n  background: rgba(233, 69, 96, 0.2);\r\n  border-color: #e94560;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.log-content::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.log-content::-webkit-scrollbar-track {\r\n  background: rgba(15, 52, 96, 0.3);\r\n  border-radius: 3px;\r\n}\r\n\r\n.log-content::-webkit-scrollbar-thumb {\r\n  background: rgba(233, 69, 96, 0.6);\r\n  border-radius: 3px;\r\n}\r\n\r\n.log-content::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(233, 69, 96, 0.8);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .combat-log {\r\n    padding: 12px;\r\n  }\r\n  \r\n  .log-filters {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .filter-group {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .log-entry {\r\n    padding: 6px;\r\n    gap: 6px;\r\n  }\r\n  \r\n  .log-timestamp {\r\n    min-width: 50px;\r\n    font-size: 0.6rem;\r\n  }\r\n  \r\n  .log-round {\r\n    min-width: 40px;\r\n    font-size: 0.6rem;\r\n  }\r\n  \r\n  .message-text {\r\n    font-size: 0.8rem;\r\n  }\r\n  \r\n  .log-footer {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;EACO,SAAM;AAAY;;EAEhB,SAAM;AAAY;;EAKhB,SAAM;AAAc;;EActB,SAAM;AAAa;;EACjB,SAAM;AAAc;;;EAapB,SAAM;AAAY;;EAYpB,SAAM,aAAa;EAACA,GAAG,EAAC;;;EAQpB,SAAM;AAAe;;;EAKrB,SAAM;;;EAKN,SAAM;AAAU;;EAKhB,SAAM;AAAa;;;;EAIjB,SAAM;;;EAMD,SAAM;AAAc;;EACpB,SAAM;AAAc;;;EAKzB,SAAM;;;EACJ,SAAM;AAAc;;EACpB,SAAM;AAAY;;EAUlB,SAAM;AAAY;;;EAIpB,SAAM;;;;EAQR,SAAM;;;;;EAUyB,SAAM;;;EAOzC,SAAM;AAAY;;EAChB,SAAM;AAAW;;EAIjB,SAAM;AAAa;;uBApI5BC,mBAAA,CA+IM,OA/INC,UA+IM,GA9IJC,mBAAA,UAAa,EACbC,mBAAA,CAgBM,OAhBNC,UAgBM,G,0BAfJD,mBAAA,CAGM;IAHD,SAAM;EAAa,IACtBA,mBAAA,CAA6B;IAA1B,SAAM;EAAe,IACxBA,mBAAA,CAAa,YAAT,MAAI,E,qBAEVA,mBAAA,CAUM,OAVNE,UAUM,GATJF,mBAAA,CAES;IAFAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;IAAE,SAAKC,eAAA,EAAC,iBAAiB;MAAAC,MAAA,EAAmBC,MAAA,CAAAC;IAAU;gCACpFZ,mBAAA,CAAiC;IAA9B,SAAM;EAAmB,0B,mBAE9BA,mBAAA,CAES;IAFAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAQ,QAAA,IAAAR,QAAA,CAAAQ,QAAA,CAAAN,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAQ;IAAA;IAAE,SAAM,WAAW;IAACM,KAAK,EAAC;gCAChDd,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,IAEzBA,mBAAA,CAES;IAFAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAU,SAAA,IAAAV,QAAA,CAAAU,SAAA,CAAAR,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;IAAE,SAAM,YAAY;IAACM,KAAK,EAAC;gCAClDd,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,0B,QAKhCD,mBAAA,SAAY,EACZC,mBAAA,CAuBM,OAvBNgB,UAuBM,GAtBJhB,mBAAA,CAWM,OAXNiB,UAWM,I,kBAVJpB,mBAAA,CASSqB,SAAA,QAAAC,WAAA,CARUC,KAAA,CAAAC,UAAU,YAApBC,MAAM;yBADfzB,mBAAA,CASS;MAPN0B,GAAG,EAAED,MAAM,CAACE,IAAI;MAChBrB,OAAK,WAALA,OAAKA,CAAAsB,MAAA;QAAA,OAAEpB,QAAA,CAAAqB,YAAY,CAACJ,MAAM,CAACE,IAAI;MAAA;MAChC,SAAKf,eAAA,EAAC,YAAY;QAAAC,MAAA,EACAU,KAAA,CAAAO,aAAa,CAACC,QAAQ,CAACN,MAAM,CAACE,IAAI;MAAA;QAEpDxB,mBAAA,CAA4B;MAAxB,SAAKS,eAAA,CAAEa,MAAM,CAACO,IAAI;6BACtB7B,mBAAA,CAA8B,cAAA8B,gBAAA,CAArBR,MAAM,CAACS,IAAI,iB;oCAIxB/B,mBAAA,CAQM,OARNgC,UAQM,G,gBAPJhC,mBAAA,CAKC;;aAJUoB,KAAA,CAAAa,WAAW,GAAAR,MAAA;IAAA;IACpBD,IAAI,EAAC,MAAM;IACXU,WAAW,EAAC,SAAS;IACrB,SAAM;iDAHGd,KAAA,CAAAa,WAAW,E,+BAKtBjC,mBAAA,CAAyC;IAAtC,SAAM;EAA2B,2B,KAIxCD,mBAAA,UAAa,EACbC,mBAAA,CA6EM,OA7ENmC,UA6EM,I,kBA5EJtC,mBAAA,CAqEMqB,SAAA,QAAAC,WAAA,CApEqBd,QAAA,CAAA+B,YAAY,YAA7BC,KAAK,EAAEC,KAAK;yBADtBzC,mBAAA,CAqEM;MAnEH0B,GAAG,EAAEc,KAAK,CAACE,EAAE,IAAID,KAAK;MACvB,SAAK7B,eAAA,EAAC,WAAW,GACR4B,KAAK,CAACb,IAAI,EAAEa,KAAK,CAACG,QAAQ;QAEnCzC,mBAAA,SAAY,EACZC,mBAAA,CAEM,OAFNyC,UAEM,EAAAX,gBAAA,CADDzB,QAAA,CAAAqC,UAAU,CAACL,KAAK,CAACM,SAAS,mBAG/B5C,mBAAA,UAAa,EACgBsC,KAAK,CAACO,KAAK,I,cAAxC/C,mBAAA,CAEM,OAFNgD,WAEM,EAFoC,IACvC,GAAAf,gBAAA,CAAGO,KAAK,CAACO,KAAK,IAAG,IACpB,mB,mCAEA7C,mBAAA,UAAa,EACbC,mBAAA,CAEM,OAFN8C,WAEM,GADJ9C,mBAAA,CAAuC;MAAnC,SAAKS,eAAA,CAAEJ,QAAA,CAAA0C,UAAU,CAACV,KAAK,CAACb,IAAI;+BAGlCzB,mBAAA,UAAa,EACbC,mBAAA,CAqCM,OArCNgD,WAqCM,GApCJhD,mBAAA,CAAsE;MAAjE,SAAM,cAAc;MAACiD,SAAqC,EAA7B5C,QAAA,CAAA6C,aAAa,CAACb,KAAK,CAACc,OAAO;0CAE7DpD,mBAAA,UAAa,EACsBsC,KAAK,CAACe,OAAO,I,cAAhDvD,mBAAA,CASM,OATNwD,WASM,I,kBARJxD,mBAAA,CAOMqB,SAAA,QAAAC,WAAA,CANoBkB,KAAK,CAACe,OAAO,YAA7BE,MAAM,EAAE/B,GAAG;2BADrB1B,mBAAA,CAOM;QALH0B,GAAG,EAAEA,GAAG;QACT,SAAM;UAENvB,mBAAA,CAA+D,QAA/DuD,WAA+D,EAAAzB,gBAAA,CAAjCzB,QAAA,CAAAmD,iBAAiB,CAACjC,GAAG,KAAI,GAAC,iBACxDvB,mBAAA,CAAiE,QAAjEyD,WAAiE,EAAA3B,gBAAA,CAAnCzB,QAAA,CAAAqD,iBAAiB,CAACJ,MAAM,kB;2EAI1DvD,mBAAA,UAAa,EACkBsC,KAAK,CAACsB,QAAQ,I,cAA7C9D,mBAAA,CAaM,OAbN+D,WAaM,GAZJ5D,mBAAA,CAA4D,OAA5D6D,WAA4D,EAAA/B,gBAAA,CAA/BO,KAAK,CAACsB,QAAQ,CAACG,OAAO,kBACnD9D,mBAAA,CASM,OATN+D,WASM,I,kBARJlE,mBAAA,CAOOqB,SAAA,QAAAC,WAAA,CANekB,KAAK,CAACsB,QAAQ,CAACK,KAAK,YAAhCC,IAAI,EAAEC,CAAC;2BADjBrE,mBAAA,CAOO;QALJ0B,GAAG,EAAE2C,CAAC;QACP,SAAKzD,eAAA,EAAC,WAAW,EACTJ,QAAA,CAAA8D,gBAAgB,CAACF,IAAI,EAAE5B,KAAK,CAACsB,QAAQ;0BAE1CM,IAAI;sCAGXjE,mBAAA,CAA4D,OAA5DoE,WAA4D,EAApC,MAAI,GAAAtC,gBAAA,CAAGO,KAAK,CAACsB,QAAQ,CAACU,KAAK,iB,wCAGrDtE,mBAAA,UAAa,EACoBsC,KAAK,CAACiC,YAAY,I,cAAnDzE,mBAAA,CAIM,OAJN0E,WAIM,GAHJvE,mBAAA,CAEO;MAFD,SAAKS,eAAA,EAAC,eAAe,EAAS4B,KAAK,CAACiC,YAAY;wBACjDjE,QAAA,CAAAmE,mBAAmB,CAACnC,KAAK,CAACiC,YAAY,yB,0CAK/CvE,mBAAA,WAAc,EACqBsC,KAAK,CAACoC,WAAW,I,cAApD5E,mBAAA,CAMM,OANN6E,WAMM,GALJ1E,mBAAA,CAIC;MAHE2E,GAAG,EAAEtC,KAAK,CAACoC,WAAW,CAACG,MAAM;MAC7BC,GAAG,EAAExC,KAAK,CAACoC,WAAW,CAAC1C,IAAI;MAC5B,SAAM;;kCAKZhC,mBAAA,SAAY,EACDM,QAAA,CAAA+B,YAAY,CAAC0C,MAAM,U,cAA9BjF,mBAAA,CAGM,OAHNkF,WAGM,G,4BAFJ/E,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4BACvBA,mBAAA,CAAiD,WAAA8B,gBAAA,CAA3CV,KAAA,CAAAa,WAAW,0C,gEAIrBlC,mBAAA,UAAa,EACbC,mBAAA,CAeM,OAfNgF,WAeM,GAdJhF,mBAAA,CAGM,OAHNiF,WAGM,GAFJjF,mBAAA,CAA0C,cAApC,MAAI,GAAA8B,gBAAA,CAAGnB,MAAA,CAAAuE,UAAU,CAACJ,MAAM,IAAG,IAAE,iBACnC9E,mBAAA,CAA4C,cAAtC,MAAI,GAAA8B,gBAAA,CAAGzB,QAAA,CAAA+B,YAAY,CAAC0C,MAAM,IAAG,IAAE,gB,GAEvC9E,mBAAA,CASM,OATNmF,WASM,GARJnF,mBAAA,CAGS;IAHAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA+E,WAAA,IAAA/E,QAAA,CAAA+E,WAAA,CAAA7E,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAW;IAAA;IAAE,SAAM;kCACjCR,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2B,iBAAK,MAEjC,E,IACAA,mBAAA,CAGS;IAHAG,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAgF,cAAA,IAAAhF,QAAA,CAAAgF,cAAA,CAAA9E,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAc;IAAA;IAAE,SAAM;kCACpCR,mBAAA,CAAiC;IAA9B,SAAM;EAAmB,2B,iBAAK,MAEnC,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}