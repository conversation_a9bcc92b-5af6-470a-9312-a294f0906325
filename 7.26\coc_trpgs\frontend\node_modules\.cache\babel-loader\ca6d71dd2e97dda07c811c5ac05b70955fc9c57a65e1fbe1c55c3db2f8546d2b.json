{"ast": null, "code": "import \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, vModelText as _vModelText, withKeys as _withKeys, withDirectives as _withDirectives, resolveComponent as _resolveComponent, createVNode as _createVNode, createBlock as _createBlock, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, vModelCheckbox as _vModelCheckbox } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"room-container\"\n};\nvar _hoisted_2 = {\n  \"class\": \"user-role-indicator\"\n};\nvar _hoisted_3 = {\n  \"class\": \"room-title\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  \"class\": \"fas fa-edit edit-icon\"\n};\nvar _hoisted_5 = {\n  key: 2,\n  \"class\": \"room-participants\"\n};\nvar _hoisted_6 = {\n  \"class\": \"room-actions\"\n};\nvar _hoisted_7 = {\n  \"class\": \"btn-text\"\n};\nvar _hoisted_8 = {\n  \"class\": \"room-controls\"\n};\nvar _hoisted_9 = {\n  \"class\": \"room-participants\"\n};\nvar _hoisted_10 = {\n  \"class\": \"main-content\"\n};\nvar _hoisted_11 = {\n  \"class\": \"message-area\"\n};\nvar _hoisted_12 = {\n  \"class\": \"toolbar-buttons\"\n};\nvar _hoisted_13 = {\n  \"class\": \"toolbar-group\"\n};\nvar _hoisted_14 = {\n  \"class\": \"toolbar-group\"\n};\nvar _hoisted_15 = {\n  \"class\": \"toolbar-group\"\n};\nvar _hoisted_16 = {\n  \"class\": \"toolbar-group\"\n};\nvar _hoisted_17 = {\n  key: 0,\n  \"class\": \"toolbar-group\"\n};\nvar _hoisted_18 = {\n  \"class\": \"toolbar-group\"\n};\nvar _hoisted_19 = {\n  \"class\": \"character-list\"\n};\nvar _hoisted_20 = [\"onClick\"];\nvar _hoisted_21 = {\n  \"class\": \"settings-content\"\n};\nvar _hoisted_22 = {\n  \"class\": \"setting-group\"\n};\nvar _hoisted_23 = {\n  \"class\": \"font-size-controls\"\n};\nvar _hoisted_24 = {\n  \"class\": \"current-size\"\n};\nvar _hoisted_25 = {\n  \"class\": \"setting-group\"\n};\nvar _hoisted_26 = {\n  \"class\": \"toggle-switch\"\n};\nvar _hoisted_27 = {\n  \"class\": \"toggle-switch\"\n};\nvar _hoisted_28 = {\n  \"class\": \"setting-actions\"\n};\nvar _hoisted_29 = {\n  key: 22,\n  \"class\": \"preview-mode-indicator\"\n};\nvar _hoisted_30 = {\n  \"class\": \"preview-actions\"\n};\nvar _hoisted_31 = {\n  key: 23,\n  \"class\": \"save-options-panel\"\n};\nvar _hoisted_32 = {\n  \"class\": \"save-options-content\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$data$room;\n  var _component_chat_box = _resolveComponent(\"chat-box\");\n  var _component_floating_character_sheet = _resolveComponent(\"floating-character-sheet\");\n  var _component_floating_dice_roller = _resolveComponent(\"floating-dice-roller\");\n  var _component_floating_notes = _resolveComponent(\"floating-notes\");\n  var _component_group_chat = _resolveComponent(\"group-chat\");\n  var _component_floating_panel = _resolveComponent(\"floating-panel\");\n  var _component_map_system = _resolveComponent(\"map-system\");\n  var _component_clue_board = _resolveComponent(\"clue-board\");\n  var _component_combat_system = _resolveComponent(\"combat-system\");\n  var _component_skill_check_system = _resolveComponent(\"skill-check-system\");\n  var _component_equipment_system = _resolveComponent(\"equipment-system\");\n  var _component_experience_pack_system = _resolveComponent(\"experience-pack-system\");\n  var _component_spell_system = _resolveComponent(\"spell-system\");\n  var _component_madness_system = _resolveComponent(\"madness-system\");\n  var _component_mythos_library = _resolveComponent(\"mythos-library\");\n  var _component_audio_system = _resolveComponent(\"audio-system\");\n  var _component_voice_chat = _resolveComponent(\"voice-chat\");\n  var _component_private_chat_manager = _resolveComponent(\"private-chat-manager\");\n  var _component_modal = _resolveComponent(\"modal\");\n  var _component_a_i_model_settings = _resolveComponent(\"a-i-model-settings\");\n  var _component_game_save_manager = _resolveComponent(\"game-save-manager\");\n  var _component_scenario_viewer = _resolveComponent(\"scenario-viewer\");\n  var _component_announcement_viewer = _resolveComponent(\"announcement-viewer\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 用户身份显示区域 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass([\"fas\", $data.isRoomOwner ? 'fa-crown' : 'fa-user'])\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString($data.isRoomOwner ? '房主' : '玩家'), 1 /* TEXT */)]), _createCommentVNode(\" 房间顶部栏，添加双击事件 \"), _createElementVNode(\"div\", {\n    \"class\": \"room-header\",\n    onDblclick: _cache[9] || (_cache[9] = function () {\n      return $options.toggleFullScreen && $options.toggleFullScreen.apply($options, arguments);\n    })\n  }, [_createElementVNode(\"div\", _hoisted_3, [!$data.isEditingRoomName || !$data.isRoomOwner ? (_openBlock(), _createElementBlock(\"h2\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.startEditRoomName && $options.startEditRoomName.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass({\n      'editable': $data.isRoomOwner\n    })\n  }, [_createTextVNode(_toDisplayString($data.room.name || '加载中...') + \" \", 1 /* TEXT */), $data.isRoomOwner ? (_openBlock(), _createElementBlock(\"i\", _hoisted_4)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)) : _withDirectives((_openBlock(), _createElementBlock(\"input\", {\n    key: 1,\n    type: \"text\",\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $data.editableRoomName = $event;\n    }),\n    onBlur: _cache[2] || (_cache[2] = function () {\n      return $options.saveRoomName && $options.saveRoomName.apply($options, arguments);\n    }),\n    onKeyup: [_cache[3] || (_cache[3] = _withKeys(function () {\n      return $options.saveRoomName && $options.saveRoomName.apply($options, arguments);\n    }, [\"enter\"])), _cache[4] || (_cache[4] = _withKeys(function () {\n      return $options.cancelEditRoomName && $options.cancelEditRoomName.apply($options, arguments);\n    }, [\"esc\"]))],\n    ref: \"roomNameInput\",\n    \"class\": \"room-name-input\"\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */)), [[_vModelText, $data.editableRoomName]]), $data.onlineUsers.length > 0 ? (_openBlock(), _createElementBlock(\"span\", _hoisted_5, _toDisplayString($data.onlineUsers.length) + \" 人在线 \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"button\", {\n    \"class\": \"room-action-btn announcement-btn\",\n    onClick: _cache[5] || (_cache[5] = function () {\n      return $options.openAnnouncement && $options.openAnnouncement.apply($options, arguments);\n    }),\n    title: \"公告\"\n  }, _cache[64] || (_cache[64] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-bullhorn\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"btn-text\"\n  }, \"公告\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    \"class\": \"room-action-btn toggle-btn\",\n    onClick: _cache[6] || (_cache[6] = function () {\n      return $options.toggleToolbar && $options.toggleToolbar.apply($options, arguments);\n    })\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass($data.isToolbarCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left')\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_7, _toDisplayString($data.isToolbarCollapsed ? '展开' : '收起'), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    \"class\": \"room-action-btn\",\n    onClick: _cache[7] || (_cache[7] = function () {\n      return $options.toggleFullScreen && $options.toggleFullScreen.apply($options, arguments);\n    }),\n    title: \"全屏\"\n  }, _cache[65] || (_cache[65] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-expand\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    \"class\": \"room-action-btn\",\n    onClick: _cache[8] || (_cache[8] = function ($event) {\n      return $data.showSettingsModal = true;\n    }),\n    title: \"设置\"\n  }, _cache[66] || (_cache[66] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-cog\"\n  }, null, -1 /* CACHED */)]))])], 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"span\", _hoisted_9, \"👥 \" + _toDisplayString($data.onlineUsers.length) + \" 人在线\", 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = function () {\n      return $options.toggleDarkMode && $options.toggleDarkMode.apply($options, arguments);\n    }),\n    \"class\": \"control-btn theme-btn\"\n  }, _toDisplayString($options.isDarkMode ? '深色模式' : '浅色模式'), 1 /* TEXT */), _createCommentVNode(\" 添加剧本模式设置按钮 \"), $data.currentAIMode === 'script' ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[11] || (_cache[11] = function () {\n      return $options.openChatBoxSettings && $options.openChatBoxSettings.apply($options, arguments);\n    }),\n    \"class\": \"control-btn script-btn\"\n  }, _cache[67] || (_cache[67] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-book-open\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 剧本模式设置 \")]))) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 添加快速存档按钮 \"), $data.isRoomOwner ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    onClick: _cache[12] || (_cache[12] = function () {\n      return $options.quickSave && $options.quickSave.apply($options, arguments);\n    }),\n    \"class\": \"control-btn save-btn\"\n  }, _cache[68] || (_cache[68] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-save\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 快速存档 \")]))) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 添加快速读档按钮 \"), $data.isRoomOwner && $options.hasQuickSave ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 2,\n    onClick: _cache[13] || (_cache[13] = function () {\n      return $options.quickLoad && $options.quickLoad.apply($options, arguments);\n    }),\n    \"class\": \"control-btn load-btn\"\n  }, _cache[69] || (_cache[69] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-folder-open\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 快速读档 \")]))) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_10, [_createCommentVNode(\" 消息区域 \"), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_chat_box, {\n    ref: \"chatBox\",\n    messages: $data.messages,\n    currentUser: $data.currentUser,\n    users: $data.users,\n    isLoading: $data.isLoading,\n    roomChatMode: ((_$data$room = $data.room) === null || _$data$room === void 0 ? void 0 : _$data$room.chat_mode) || 'normal',\n    onSendMessage: $options.sendMessage,\n    onRollDice: $data.rollDice,\n    onUploadScript: $data.uploadScript,\n    onViewScenario: $options.handleViewScenario\n  }, null, 8 /* PROPS */, [\"messages\", \"currentUser\", \"users\", \"isLoading\", \"roomChatMode\", \"onSendMessage\", \"onRollDice\", \"onUploadScript\", \"onViewScenario\"])])]), _createCommentVNode(\" 浮动面板 \"), $data.visiblePanels.character ? (_openBlock(), _createBlock(_component_floating_character_sheet, {\n    key: 0,\n    character: $data.selectedCharacter,\n    \"initial-x\": 50,\n    \"initial-y\": 100,\n    onClose: _cache[14] || (_cache[14] = function ($event) {\n      return $options.togglePanel('character', true);\n    }),\n    onSkillCheck: $options.handleSkillCheck,\n    onSelectCharacter: _cache[15] || (_cache[15] = function ($event) {\n      return $data.showCharacterModal = true;\n    }),\n    onUpdateSanity: $options.handleSanityUpdate,\n    onUpdateCharacter: $options.handleCharacterUpdate,\n    onCharacterSync: $options.handleCharacterSync\n  }, null, 8 /* PROPS */, [\"character\", \"onSkillCheck\", \"onUpdateSanity\", \"onUpdateCharacter\", \"onCharacterSync\"])) : _createCommentVNode(\"v-if\", true), $data.visiblePanels.dice ? (_openBlock(), _createBlock(_component_floating_dice_roller, {\n    key: 1,\n    \"initial-x\": 500,\n    \"initial-y\": 100,\n    \"dice-history\": $data.diceHistory,\n    onClose: _cache[16] || (_cache[16] = function ($event) {\n      return $options.togglePanel('dice', true);\n    }),\n    onRoll: $options.handleRoll,\n    onSkillCheck: $options.handleSkillCheck\n  }, null, 8 /* PROPS */, [\"dice-history\", \"onRoll\", \"onSkillCheck\"])) : _createCommentVNode(\"v-if\", true), $data.showNotes ? (_openBlock(), _createBlock(_component_floating_notes, {\n    key: 2,\n    \"initial-x\": 300,\n    \"initial-y\": 300,\n    \"character-id\": $data.currentCharacterId,\n    onClose: $options.toggleNotes,\n    onNoteSaved: $options.handleNoteSaved\n  }, null, 8 /* PROPS */, [\"character-id\", \"onClose\", \"onNoteSaved\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 群聊面板 \"), $data.visiblePanels.groupChat && $data.currentAIMode === 'script' ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 3,\n    title: \"玩家讨论区\",\n    \"initial-x\": 400,\n    \"initial-y\": 200,\n    \"initial-width\": 500,\n    \"initial-height\": 400,\n    \"min-width\": 300,\n    \"min-height\": 300,\n    onClose: _cache[17] || (_cache[17] = function ($event) {\n      return $options.togglePanel('groupChat', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_group_chat, {\n        \"room-id\": $data.internalRoomId,\n        \"ai-mode\": $data.currentAIMode\n      }, null, 8 /* PROPS */, [\"room-id\", \"ai-mode\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 地图系统面板 \"), $data.visiblePanels.map ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 4,\n    title: \"地图系统\",\n    \"initial-x\": 100,\n    \"initial-y\": 150,\n    \"initial-width\": 600,\n    \"initial-height\": 450,\n    \"min-width\": 400,\n    \"min-height\": 350,\n    onClose: _cache[18] || (_cache[18] = function ($event) {\n      return $options.togglePanel('map', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_map_system, {\n        \"room-id\": $data.internalRoomId\n      }, null, 8 /* PROPS */, [\"room-id\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 线索墙面板 \"), $data.visiblePanels.clueBoard ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 5,\n    title: \"线索墙\",\n    \"initial-x\": 150,\n    \"initial-y\": 200,\n    \"initial-width\": 700,\n    \"initial-height\": 500,\n    \"min-width\": 400,\n    \"min-height\": 400,\n    onClose: _cache[19] || (_cache[19] = function ($event) {\n      return $options.togglePanel('clueBoard', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_clue_board, {\n        \"room-id\": $data.internalRoomId\n      }, null, 8 /* PROPS */, [\"room-id\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗系统面板 \"), $data.visiblePanels.combat ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 6,\n    title: \"战斗系统\",\n    \"initial-x\": 100,\n    \"initial-y\": 100,\n    \"initial-width\": 800,\n    \"initial-height\": 600,\n    \"min-width\": 600,\n    \"min-height\": 500,\n    onClose: _cache[20] || (_cache[20] = function ($event) {\n      return $options.togglePanel('combat', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_combat_system, {\n        onCombatStarted: $options.handleCombatStarted,\n        onCombatEnded: $options.handleCombatEnded,\n        onOpenSkillCheck: $options.openSkillCheckForCharacter\n      }, null, 8 /* PROPS */, [\"onCombatStarted\", \"onCombatEnded\", \"onOpenSkillCheck\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 技能检定系统面板 \"), $data.visiblePanels.skillCheck ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 7,\n    title: \"技能检定系统\",\n    \"initial-x\": 200,\n    \"initial-y\": 150,\n    \"initial-width\": 700,\n    \"initial-height\": 550,\n    \"min-width\": 500,\n    \"min-height\": 450,\n    onClose: _cache[22] || (_cache[22] = function ($event) {\n      return $options.togglePanel('skillCheck', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_skill_check_system, {\n        \"initial-character\": $data.skillCheckCharacter,\n        \"initial-skill\": $data.skillCheckSkill,\n        onClose: _cache[21] || (_cache[21] = function ($event) {\n          return $options.togglePanel('skillCheck', true);\n        }),\n        onSkillCheckResult: $options.handleSkillCheckResult,\n        onOpposedCheckResult: $options.handleOpposedCheckResult,\n        onSkillGrowth: $options.handleSkillGrowth,\n        onGrowthCheckResult: $options.handleGrowthCheckResult,\n        onCharacterSelected: $options.handleSkillCheckCharacterSelected\n      }, null, 8 /* PROPS */, [\"initial-character\", \"initial-skill\", \"onSkillCheckResult\", \"onOpposedCheckResult\", \"onSkillGrowth\", \"onGrowthCheckResult\", \"onCharacterSelected\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 装备系统面板 \"), $data.visiblePanels.equipment ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 8,\n    title: \"装备系统\",\n    \"initial-x\": 150,\n    \"initial-y\": 100,\n    \"initial-width\": 900,\n    \"initial-height\": 650,\n    \"min-width\": 700,\n    \"min-height\": 500,\n    onClose: _cache[23] || (_cache[23] = function ($event) {\n      return $options.togglePanel('equipment', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_equipment_system, {\n        onWeaponEquipped: $options.handleWeaponEquipped,\n        onWeaponUnequipped: $options.handleWeaponUnequipped,\n        onWeaponUsed: $options.handleWeaponUsed,\n        onArmorEquipped: $options.handleArmorEquipped,\n        onArmorUnequipped: $options.handleArmorUnequipped,\n        onItemUsed: $options.handleItemUsed,\n        onItemPurchased: $options.handleItemPurchased,\n        onItemDropped: $options.handleItemDropped,\n        onHealCharacter: $options.handleHealCharacter,\n        onReadBook: $options.handleReadBook\n      }, null, 8 /* PROPS */, [\"onWeaponEquipped\", \"onWeaponUnequipped\", \"onWeaponUsed\", \"onArmorEquipped\", \"onArmorUnequipped\", \"onItemUsed\", \"onItemPurchased\", \"onItemDropped\", \"onHealCharacter\", \"onReadBook\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 经历包系统面板 \"), $data.visiblePanels.experiencePack ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 9,\n    title: \"经历包系统\",\n    \"initial-x\": 250,\n    \"initial-y\": 150,\n    \"initial-width\": 800,\n    \"initial-height\": 600,\n    \"min-width\": 600,\n    \"min-height\": 500,\n    onClose: _cache[24] || (_cache[24] = function ($event) {\n      return $options.togglePanel('experiencePack', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_experience_pack_system, {\n        onCharacterUpdated: $options.handleCharacterUpdate\n      }, null, 8 /* PROPS */, [\"onCharacterUpdated\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 法术系统面板 \"), $data.visiblePanels.spells ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 10,\n    title: \"法术系统\",\n    \"initial-x\": 100,\n    \"initial-y\": 120,\n    \"initial-width\": 900,\n    \"initial-height\": 700,\n    \"min-width\": 700,\n    \"min-height\": 600,\n    onClose: _cache[25] || (_cache[25] = function ($event) {\n      return $options.togglePanel('spells', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_spell_system, {\n        onCharacterUpdated: $options.handleCharacterUpdate,\n        onSpellCast: $options.handleSpellCast\n      }, null, 8 /* PROPS */, [\"onCharacterUpdated\", \"onSpellCast\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 疯狂症状系统面板 \"), $data.visiblePanels.madness ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 11,\n    title: \"疯狂症状系统\",\n    \"initial-x\": 150,\n    \"initial-y\": 100,\n    \"initial-width\": 850,\n    \"initial-height\": 650,\n    \"min-width\": 650,\n    \"min-height\": 550,\n    onClose: _cache[26] || (_cache[26] = function ($event) {\n      return $options.togglePanel('madness', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_madness_system, {\n        onCharacterUpdated: $options.handleCharacterUpdate\n      }, null, 8 /* PROPS */, [\"onCharacterUpdated\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 神话典籍图书馆面板 \"), $data.visiblePanels.library ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 12,\n    title: \"神话典籍图书馆\",\n    \"initial-x\": 200,\n    \"initial-y\": 80,\n    \"initial-width\": 950,\n    \"initial-height\": 750,\n    \"min-width\": 750,\n    \"min-height\": 650,\n    onClose: _cache[27] || (_cache[27] = function ($event) {\n      return $options.togglePanel('library', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_mythos_library, {\n        onCharacterUpdated: $options.handleCharacterUpdate\n      }, null, 8 /* PROPS */, [\"onCharacterUpdated\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 音频系统面板 \"), $data.visiblePanels.audioSystem && $data.isRoomOwner ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 13,\n    title: \"音频系统\",\n    \"initial-x\": 200,\n    \"initial-y\": 250,\n    \"initial-width\": 600,\n    \"initial-height\": 400,\n    \"min-width\": 350,\n    \"min-height\": 300,\n    onClose: _cache[28] || (_cache[28] = function ($event) {\n      return $options.togglePanel('audioSystem', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_audio_system, {\n        roomId: $data.internalRoomId,\n        isKP: $data.isRoomOwner\n      }, null, 8 /* PROPS */, [\"roomId\", \"isKP\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 语音聊天面板 \"), $data.visiblePanels.voiceChat ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 14,\n    title: \"语音聊天\",\n    \"initial-x\": 250,\n    \"initial-y\": 300,\n    \"initial-width\": 300,\n    \"initial-height\": 400,\n    \"min-width\": 250,\n    \"min-height\": 300,\n    onClose: _cache[29] || (_cache[29] = function ($event) {\n      return $options.togglePanel('voiceChat', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      var _ctx$$store$getters$c;\n      return [_createVNode(_component_voice_chat, {\n        roomId: $data.internalRoomId,\n        username: ((_ctx$$store$getters$c = _ctx.$store.getters.currentUser) === null || _ctx$$store$getters$c === void 0 ? void 0 : _ctx$$store$getters$c.username) || '玩家',\n        isKP: $data.isKP,\n        onVoiceConnected: $options.handleVoiceConnected,\n        onVoiceDisconnected: $options.handleVoiceDisconnected\n      }, null, 8 /* PROPS */, [\"roomId\", \"username\", \"isKP\", \"onVoiceConnected\", \"onVoiceDisconnected\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"room-toolbar\", {\n      'collapsed': $data.isToolbarCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_12, [_createCommentVNode(\" 角色相关工具 \"), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"button\", {\n    onClick: _cache[30] || (_cache[30] = function () {\n      return $options.toggleDiceRoller && $options.toggleDiceRoller.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.showDiceRoller\n    }])\n  }, _cache[70] || (_cache[70] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-dice-d20\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"骰子\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[31] || (_cache[31] = function () {\n      return $options.toggleCharacterSheet && $options.toggleCharacterSheet.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.showCharacterSheet\n    }])\n  }, _cache[71] || (_cache[71] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-user\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"角色卡\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[32] || (_cache[32] = function () {\n      return $options.toggleSkillCheck && $options.toggleSkillCheck.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.skillCheck\n    }])\n  }, _cache[72] || (_cache[72] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-cog\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"技能检定\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[33] || (_cache[33] = function () {\n      return $options.toggleNotes && $options.toggleNotes.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.showNotes\n    }])\n  }, _cache[73] || (_cache[73] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sticky-note\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"笔记\", -1 /* CACHED */)]), 2 /* CLASS */)]), _createCommentVNode(\" 战斗和装备工具 \"), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"button\", {\n    onClick: _cache[34] || (_cache[34] = function () {\n      return $options.toggleCombat && $options.toggleCombat.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.combat\n    }])\n  }, _cache[74] || (_cache[74] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"战斗\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[35] || (_cache[35] = function () {\n      return $options.toggleEquipment && $options.toggleEquipment.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.equipment\n    }])\n  }, _cache[75] || (_cache[75] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-shield-alt\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"装备\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[36] || (_cache[36] = function () {\n      return $options.toggleExperiencePack && $options.toggleExperiencePack.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.experiencePack\n    }])\n  }, _cache[76] || (_cache[76] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-star\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"经历包\", -1 /* CACHED */)]), 2 /* CLASS */)]), _createCommentVNode(\" 神话和魔法工具 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"button\", {\n    onClick: _cache[37] || (_cache[37] = function () {\n      return $options.toggleSpells && $options.toggleSpells.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.spells\n    }])\n  }, _cache[77] || (_cache[77] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-magic\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"法术\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[38] || (_cache[38] = function () {\n      return $options.toggleMadness && $options.toggleMadness.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.madness\n    }])\n  }, _cache[78] || (_cache[78] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-brain\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"疯狂\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[39] || (_cache[39] = function () {\n      return $options.toggleLibrary && $options.toggleLibrary.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.library\n    }])\n  }, _cache[79] || (_cache[79] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-book-open\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"典籍\", -1 /* CACHED */)]), 2 /* CLASS */)]), _createCommentVNode(\" 地图和线索工具 \"), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"button\", {\n    onClick: _cache[40] || (_cache[40] = function () {\n      return $options.toggleMap && $options.toggleMap.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.map\n    }])\n  }, _cache[80] || (_cache[80] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-map\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"地图\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[41] || (_cache[41] = function () {\n      return $options.toggleClueBoard && $options.toggleClueBoard.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.clueBoard\n    }])\n  }, _cache[81] || (_cache[81] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-thumbtack\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"线索墙\", -1 /* CACHED */)]), 2 /* CLASS */), $data.isRoomOwner ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[42] || (_cache[42] = function () {\n      return $options.toggleAudioSystem && $options.toggleAudioSystem.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.audioSystem\n    }])\n  }, _cache[82] || (_cache[82] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-music\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"音频\", -1 /* CACHED */)]), 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 游戏存档按钮 \"), $data.isRoomOwner ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"button\", {\n    onClick: _cache[43] || (_cache[43] = function () {\n      return $options.toggleSaveOptions && $options.toggleSaveOptions.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.showSaveOptions\n    }])\n  }, _cache[83] || (_cache[83] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-save\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"游戏存档\", -1 /* CACHED */)]), 2 /* CLASS */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 聊天工具 \"), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"button\", {\n    onClick: _cache[44] || (_cache[44] = function () {\n      return $options.toggleVoiceChat && $options.toggleVoiceChat.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.voiceChat\n    }])\n  }, _cache[84] || (_cache[84] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-microphone\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"语音\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[45] || (_cache[45] = function () {\n      return $options.togglePrivateChat && $options.togglePrivateChat.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.privateChat\n    }])\n  }, _cache[85] || (_cache[85] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-comments\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"私聊\", -1 /* CACHED */)]), 2 /* CLASS */), $data.currentAIMode === 'script' ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[46] || (_cache[46] = function () {\n      return $options.toggleGroupChat && $options.toggleGroupChat.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"toolbar-btn\", {\n      'active': $data.visiblePanels.groupChat\n    }])\n  }, _cache[86] || (_cache[86] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-users\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", {\n    \"class\": \"tooltip\"\n  }, \"群聊\", -1 /* CACHED */)]), 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 折叠按钮 \"), _createElementVNode(\"button\", {\n    onClick: _cache[47] || (_cache[47] = function () {\n      return $options.toggleToolbar && $options.toggleToolbar.apply($options, arguments);\n    }),\n    \"class\": \"collapse-btn\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(['fas', $data.isToolbarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'])\n  }, null, 2 /* CLASS */)])], 2 /* CLASS */), _createCommentVNode(\" 浮动面板 \"), $data.visiblePanels.privateChat ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 15,\n    title: \"私聊\",\n    \"initial-x\": $data.windowWidth - 350,\n    \"initial-y\": $data.windowHeight - 450,\n    \"initial-width\": 320,\n    \"initial-height\": 400,\n    \"min-width\": 250,\n    \"min-height\": 300,\n    onClose: _cache[48] || (_cache[48] = function ($event) {\n      return $options.togglePanel('privateChat', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_private_chat_manager, {\n        \"online-users\": $data.onlineUsers\n      }, null, 8 /* PROPS */, [\"online-users\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"initial-x\", \"initial-y\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 私聊管理器 \"), _createCommentVNode(\" <private-chat-manager :online-users=\\\"onlineUsers\\\" :hidden=\\\"isToolbarCollapsed\\\" /> \"), _createCommentVNode(\" 角色选择模态框 \"), $data.showCharacterModal ? (_openBlock(), _createBlock(_component_modal, {\n    key: 16,\n    onClose: _cache[49] || (_cache[49] = function ($event) {\n      return $data.showCharacterModal = false;\n    })\n  }, {\n    header: _withCtx(function () {\n      return _cache[87] || (_cache[87] = [_createElementVNode(\"h3\", null, \"选择角色\", -1 /* CACHED */)]);\n    }),\n    \"default\": _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.characters, function (character) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: character.id,\n          onClick: function onClick($event) {\n            return $options.selectCharacter(character);\n          },\n          \"class\": \"character-item\"\n        }, [_createElementVNode(\"h4\", null, _toDisplayString(character.name), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(character.occupation), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_20);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 设置模态框 \"), $data.showSettingsModal ? (_openBlock(), _createBlock(_component_modal, {\n    key: 17,\n    onClose: _cache[55] || (_cache[55] = function ($event) {\n      return $data.showSettingsModal = false;\n    })\n  }, {\n    header: _withCtx(function () {\n      return _cache[88] || (_cache[88] = [_createElementVNode(\"h3\", null, \"房间设置\", -1 /* CACHED */)]);\n    }),\n    \"default\": _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_cache[89] || (_cache[89] = _createElementVNode(\"label\", null, \"字体大小\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"button\", {\n        onClick: _cache[50] || (_cache[50] = function ($event) {\n          return $options.changeFontSize(-1);\n        }),\n        \"class\": \"font-btn\"\n      }, \"A-\"), _createElementVNode(\"span\", _hoisted_24, _toDisplayString($data.fontSize) + \"px\", 1 /* TEXT */), _createElementVNode(\"button\", {\n        onClick: _cache[51] || (_cache[51] = function ($event) {\n          return $options.changeFontSize(1);\n        }),\n        \"class\": \"font-btn\"\n      }, \"A+\")])]), _createElementVNode(\"div\", _hoisted_25, [_cache[92] || (_cache[92] = _createElementVNode(\"label\", null, \"通知设置\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_26, [_withDirectives(_createElementVNode(\"input\", {\n        type: \"checkbox\",\n        id: \"soundToggle\",\n        \"onUpdate:modelValue\": _cache[52] || (_cache[52] = function ($event) {\n          return $data.playSounds = $event;\n        })\n      }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.playSounds]]), _cache[90] || (_cache[90] = _createElementVNode(\"label\", {\n        \"for\": \"soundToggle\"\n      }, \"骰子声音\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_27, [_withDirectives(_createElementVNode(\"input\", {\n        type: \"checkbox\",\n        id: \"notifyToggle\",\n        \"onUpdate:modelValue\": _cache[53] || (_cache[53] = function ($event) {\n          return $data.showNotifications = $event;\n        })\n      }, null, 512 /* NEED_PATCH */), [[_vModelCheckbox, $data.showNotifications]]), _cache[91] || (_cache[91] = _createElementVNode(\"label\", {\n        \"for\": \"notifyToggle\"\n      }, \"桌面通知\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"button\", {\n        onClick: _cache[54] || (_cache[54] = function () {\n          return $options.saveSettings && $options.saveSettings.apply($options, arguments);\n        }),\n        \"class\": \"save-settings-btn\"\n      }, \"保存设置\")])])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 剧本模式设置已移至ChatBox.vue \"), _createCommentVNode(\" AI模型设置面板 \"), $data.visiblePanels.aiSettings && $data.isRoomOwner ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 18,\n    title: \"AI模型设置\",\n    \"initial-x\": 200,\n    \"initial-y\": 150,\n    \"initial-width\": 500,\n    \"initial-height\": 550,\n    onClose: _cache[56] || (_cache[56] = function ($event) {\n      return $options.togglePanel('aiSettings', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_a_i_model_settings, {\n        \"room-id\": _ctx.roomId,\n        \"is-room-owner\": $data.isRoomOwner,\n        onSettingsUpdated: $options.handleAISettingsUpdated\n      }, null, 8 /* PROPS */, [\"room-id\", \"is-room-owner\", \"onSettingsUpdated\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 游戏存档管理面板 \"), $data.visiblePanels.gameSaves && $data.isRoomOwner ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 19,\n    title: \"游戏存档\",\n    \"initial-x\": 200,\n    \"initial-y\": 250,\n    \"initial-width\": 500,\n    \"initial-height\": 400,\n    onClose: _cache[57] || (_cache[57] = function ($event) {\n      return $options.togglePanel('gameSaves', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_game_save_manager, {\n        \"room-id\": $data.internalRoomId,\n        onSaveCreated: $options.handleSaveCreated,\n        onSaveUpdated: $options.handleSaveUpdated,\n        onSaveDeleted: $options.handleSaveDeleted,\n        onSaveLoad: $options.handleSaveLoad,\n        onSaveError: $options.handleSaveError,\n        onCollectState: $options.collectGameState,\n        onSaveAutoCreated: $options.handleSaveAutoCreated,\n        onSaveMessage: $options.handleSaveMessage,\n        onSaveRestored: $options.handleSaveRestored,\n        onSavePreview: $options.handleSavePreview\n      }, null, 8 /* PROPS */, [\"room-id\", \"onSaveCreated\", \"onSaveUpdated\", \"onSaveDeleted\", \"onSaveLoad\", \"onSaveError\", \"onCollectState\", \"onSaveAutoCreated\", \"onSaveMessage\", \"onSaveRestored\", \"onSavePreview\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 剧本查看器面板 \"), $data.visiblePanels.scenarioViewer && $data.scenarioContent ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 20,\n    title: \"剧本查看器\",\n    \"initial-x\": 300,\n    \"initial-y\": 150,\n    \"initial-width\": 700,\n    \"initial-height\": 500,\n    \"min-width\": 400,\n    \"min-height\": 350,\n    onClose: _cache[58] || (_cache[58] = function ($event) {\n      return $options.togglePanel('scenarioViewer', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_scenario_viewer, {\n        \"scenario-content\": $data.scenarioContent,\n        \"scenario-title\": $data.scenarioTitle,\n        \"file-size\": $data.scenarioFileSize\n      }, null, 8 /* PROPS */, [\"scenario-content\", \"scenario-title\", \"file-size\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 公告展示面板 \"), $data.visiblePanels.announcement ? (_openBlock(), _createBlock(_component_floating_panel, {\n    key: 21,\n    title: \"公告展示\",\n    \"initial-x\": 300,\n    \"initial-y\": 150,\n    \"initial-width\": 700,\n    \"initial-height\": 500,\n    \"min-width\": 400,\n    \"min-height\": 350,\n    onClose: _cache[59] || (_cache[59] = function ($event) {\n      return $options.togglePanel('announcement', true);\n    })\n  }, {\n    \"default\": _withCtx(function () {\n      return [_createVNode(_component_announcement_viewer, {\n        content: $data.announcementContent,\n        title: $data.announcementTitle,\n        \"publish-time\": $data.announcementTime,\n        \"is-editable\": $data.isRoomOwner,\n        onSave: $options.handleAnnouncementSave\n      }, null, 8 /* PROPS */, [\"content\", \"title\", \"publish-time\", \"is-editable\", \"onSave\"])];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 在模板中添加预览模式提示 \"), $data.isPreviewMode ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_cache[94] || (_cache[94] = _createElementVNode(\"div\", {\n    \"class\": \"preview-info\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-eye\"\n  }), _createElementVNode(\"span\", null, \"预览模式\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"button\", {\n    \"class\": \"exit-preview-btn\",\n    onClick: _cache[60] || (_cache[60] = function () {\n      return $options.exitPreviewMode && $options.exitPreviewMode.apply($options, arguments);\n    })\n  }, _cache[93] || (_cache[93] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-times\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 退出预览 \")]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 存档选项面板 \"), $data.showSaveOptions ? (_openBlock(), _createElementBlock(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", {\n    \"class\": \"save-option\",\n    onClick: _cache[61] || (_cache[61] = function () {\n      return $options.quickSave && $options.quickSave.apply($options, arguments);\n    })\n  }, _cache[95] || (_cache[95] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-save\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"快速存档\", -1 /* CACHED */), _createElementVNode(\"small\", null, \"Ctrl+S\", -1 /* CACHED */)])), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"save-option\", {\n      'disabled': !$data.quickSaveId\n    }]),\n    onClick: _cache[62] || (_cache[62] = function () {\n      return $options.quickLoad && $options.quickLoad.apply($options, arguments);\n    })\n  }, _cache[96] || (_cache[96] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-folder-open\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"快速读档\", -1 /* CACHED */), _createElementVNode(\"small\", null, \"Ctrl+O\", -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"div\", {\n    \"class\": \"save-option\",\n    onClick: _cache[63] || (_cache[63] = function () {\n      return $options.toggleGameSaves && $options.toggleGameSaves.apply($options, arguments);\n    })\n  }, _cache[97] || (_cache[97] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-history\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"存档管理\", -1 /* CACHED */), _createElementVNode(\"small\", null, \"Ctrl+H\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_normalizeClass", "$data", "isRoomOwner", "_toDisplayString", "onDblclick", "_cache", "$options", "toggleFullScreen", "apply", "arguments", "_hoisted_3", "isEditingRoomName", "onClick", "startEditRoomName", "room", "name", "_hoisted_4", "type", "editableRoomName", "$event", "onBlur", "saveRoomName", "onKeyup", "cancelEditRoomName", "ref", "onlineUsers", "length", "_hoisted_5", "_hoisted_6", "openAnnouncement", "title", "toggleToolbar", "isToolbarCollapsed", "_hoisted_7", "showSettingsModal", "_hoisted_8", "_hoisted_9", "toggleDarkMode", "isDarkMode", "currentAIMode", "openChatBoxSettings", "quickSave", "hasQuickSave", "quickLoad", "_hoisted_10", "_hoisted_11", "_createVNode", "_component_chat_box", "messages", "currentUser", "users", "isLoading", "roomChatMode", "_$data$room", "chat_mode", "onSendMessage", "sendMessage", "onRollDice", "rollDice", "onUploadScript", "uploadScript", "onViewScenario", "handleViewScenario", "visiblePanels", "character", "_createBlock", "_component_floating_character_sheet", "<PERSON><PERSON><PERSON><PERSON>", "onClose", "togglePanel", "onSkillCheck", "handleSkillCheck", "onSelectCharacter", "showCharacterModal", "onUpdateSanity", "handleSanityUpdate", "onUpdateCharacter", "handleCharacterUpdate", "onCharacterSync", "handleCharacterSync", "dice", "_component_floating_dice_roller", "diceHistory", "onRoll", "handleRoll", "showNotes", "_component_floating_notes", "currentCharacterId", "toggleNotes", "onNoteSaved", "handleNoteSaved", "groupChat", "_component_floating_panel", "_component_group_chat", "internalRoomId", "map", "_component_map_system", "clueBoard", "_component_clue_board", "combat", "_component_combat_system", "onCombatStarted", "handleCombatStarted", "onCombatEnded", "handleCombatEnded", "onOpenSkillCheck", "openSkillCheckForCharacter", "<PERSON><PERSON><PERSON><PERSON>", "_component_skill_check_system", "skillCheckCharacter", "skillCheckSkill", "onSkillCheckResult", "handleSkillCheckResult", "onOpposedCheckResult", "handleOpposedCheckResult", "onSkillGrowth", "handleSkillGrowth", "onGrowthCheckResult", "handleGrowthCheckResult", "onCharacterSelected", "handleSkillCheckCharacterSelected", "equipment", "_component_equipment_system", "onWeaponEquipped", "handleWeaponEquipped", "onWeaponUnequipped", "handleWeaponUnequipped", "onWeaponUsed", "handleWeaponUsed", "onArmorEquipped", "handleArmorEquipped", "onArmorUnequipped", "handleArmorUnequipped", "onItemUsed", "handleItemUsed", "onItemPurchased", "handleItemPurchased", "onItemDropped", "handleItemDropped", "onHealCharacter", "handleHealCharacter", "onReadBook", "handleReadBook", "experiencePack", "_component_experience_pack_system", "onCharacterUpdated", "spells", "_component_spell_system", "onSpellCast", "handleSpellCast", "madness", "_component_madness_system", "library", "_component_mythos_library", "audioSystem", "_component_audio_system", "roomId", "isKP", "voiceChat", "_ctx$$store$getters$c", "_component_voice_chat", "username", "_ctx", "$store", "getters", "onVoiceConnected", "handleVoiceConnected", "onVoiceDisconnected", "handleVoiceDisconnected", "_hoisted_12", "_hoisted_13", "toggle<PERSON>ice<PERSON>oller", "showDiceRoller", "toggleCharacterSheet", "showCharacterSheet", "toggleS<PERSON><PERSON>he<PERSON>", "_hoisted_14", "toggleCombat", "toggleEquipment", "toggleExperiencePack", "_hoisted_15", "toggleSpells", "toggleMadness", "toggleLibrary", "_hoisted_16", "toggleMap", "toggleClueBoard", "toggleAudioSystem", "_hoisted_17", "toggleSaveOptions", "showSaveOptions", "_hoisted_18", "toggleVoiceChat", "togglePrivateChat", "privateChat", "toggleGroupChat", "windowWidth", "windowHeight", "_component_private_chat_manager", "_component_modal", "header", "_withCtx", "_hoisted_19", "_Fragment", "_renderList", "characters", "key", "id", "selectCharacter", "occupation", "_hoisted_21", "_hoisted_22", "_hoisted_23", "changeFontSize", "_hoisted_24", "fontSize", "_hoisted_25", "_hoisted_26", "playSounds", "_hoisted_27", "showNotifications", "_hoisted_28", "saveSettings", "aiSettings", "_component_a_i_model_settings", "onSettingsUpdated", "handleAISettingsUpdated", "gameSaves", "_component_game_save_manager", "onSaveCreated", "handleSaveCreated", "onSaveUpdated", "handleSaveUpdated", "onSaveDeleted", "handleSaveDeleted", "onSaveLoad", "handleSaveLoad", "onSaveError", "handleSaveError", "onCollectState", "collectGameState", "onSaveAutoCreated", "handleSaveAutoCreated", "onSaveMessage", "handleSaveMessage", "onSaveRestored", "handleSaveRestored", "onSavePreview", "handleSavePreview", "<PERSON><PERSON><PERSON><PERSON>", "scenarioContent", "_component_scenario_viewer", "scenarioTitle", "scenarioFileSize", "announcement", "_component_announcement_viewer", "content", "announcementContent", "announcementTitle", "announcementTime", "onSave", "handleAnnouncementSave", "isPreviewMode", "_hoisted_29", "_hoisted_30", "exitPreviewMode", "_hoisted_31", "_hoisted_32", "quickSaveId", "toggleGameSaves"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Room.vue"], "sourcesContent": ["<template>\n  <div class=\"room-container\">\n    <!-- 用户身份显示区域 -->\n    <div class=\"user-role-indicator\">\n      <i class=\"fas\" :class=\"isRoomOwner ? 'fa-crown' : 'fa-user'\"></i>\n      <span>{{ isRoomOwner ? '房主' : '玩家' }}</span>\n    </div>\n    \n    <!-- 房间顶部栏，添加双击事件 -->\n    <div class=\"room-header\" @dblclick=\"toggleFullScreen\">\n      <div class=\"room-title\">\n        <h2 v-if=\"!isEditingRoomName || !isRoomOwner\" @click=\"startEditRoomName\" :class=\"{ 'editable': isRoomOwner }\">\n          {{ room.name || '加载中...' }}\n          <i v-if=\"isRoomOwner\" class=\"fas fa-edit edit-icon\"></i>\n        </h2>\n        <input \n          v-else\n          type=\"text\" \n          v-model=\"editableRoomName\" \n          @blur=\"saveRoomName\"\n          @keyup.enter=\"saveRoomName\"\n          @keyup.esc=\"cancelEditRoomName\"\n          ref=\"roomNameInput\"\n          class=\"room-name-input\"\n        />\n        <span class=\"room-participants\" v-if=\"onlineUsers.length > 0\">\n          {{ onlineUsers.length }} 人在线\n        </span>\n      </div>\n      <div class=\"room-actions\">\n        <button class=\"room-action-btn announcement-btn\" @click=\"openAnnouncement\" title=\"公告\">\n          <i class=\"fas fa-bullhorn\"></i>\n          <span class=\"btn-text\">公告</span>\n        </button>\n        <button class=\"room-action-btn toggle-btn\" @click=\"toggleToolbar\">\n          <i :class=\"isToolbarCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'\"></i>\n          <span class=\"btn-text\">{{ isToolbarCollapsed ? '展开' : '收起' }}</span>\n        </button>\n        <button class=\"room-action-btn\" @click=\"toggleFullScreen\" title=\"全屏\">\n          <i class=\"fas fa-expand\"></i>\n        </button>\n        <button class=\"room-action-btn\" @click=\"showSettingsModal = true\" title=\"设置\">\n          <i class=\"fas fa-cog\"></i>\n        </button>\n      </div>\n    </div>\n    \n    <div class=\"room-controls\">\n      <span class=\"room-participants\">👥 {{ onlineUsers.length }} 人在线</span>\n      <button @click=\"toggleDarkMode\" class=\"control-btn theme-btn\">\n        {{ isDarkMode ? '深色模式' : '浅色模式' }}\n      </button>\n      \n      <!-- 添加剧本模式设置按钮 -->\n      <button v-if=\"currentAIMode === 'script'\" @click=\"openChatBoxSettings\" class=\"control-btn script-btn\">\n        <i class=\"fas fa-book-open\"></i>\n        剧本模式设置\n      </button>\n      \n      <!-- 添加快速存档按钮 -->\n      <button v-if=\"isRoomOwner\" @click=\"quickSave\" class=\"control-btn save-btn\">\n        <i class=\"fas fa-save\"></i>\n        快速存档\n      </button>\n      \n      <!-- 添加快速读档按钮 -->\n      <button v-if=\"isRoomOwner && hasQuickSave\" @click=\"quickLoad\" class=\"control-btn load-btn\">\n        <i class=\"fas fa-folder-open\"></i>\n        快速读档\n      </button>\n    </div>\n    \n    <div class=\"main-content\">\n      \n      <!-- 消息区域 -->\n      <div class=\"message-area\">\n        <chat-box\n          ref=\"chatBox\"\n          :messages=\"messages\"\n          :currentUser=\"currentUser\"\n          :users=\"users\"\n          :isLoading=\"isLoading\"\n          :roomChatMode=\"room?.chat_mode || 'normal'\"\n          @send-message=\"sendMessage\"\n          @roll-dice=\"rollDice\"\n          @upload-script=\"uploadScript\"\n          @view-scenario=\"handleViewScenario\"\n        />\n      </div>\n    </div>\n    \n    <!-- 浮动面板 -->\n    <floating-character-sheet\n      v-if=\"visiblePanels.character\"\n      :character=\"selectedCharacter\"\n      :initial-x=\"50\"\n      :initial-y=\"100\"\n      @close=\"togglePanel('character', true)\"\n      @skill-check=\"handleSkillCheck\"\n      @select-character=\"showCharacterModal = true\"\n      @update-sanity=\"handleSanityUpdate\"\n      @update-character=\"handleCharacterUpdate\"\n      @character-sync=\"handleCharacterSync\"\n    />\n    \n    <floating-dice-roller\n      v-if=\"visiblePanels.dice\"\n      :initial-x=\"500\"\n      :initial-y=\"100\"\n      :dice-history=\"diceHistory\"\n      @close=\"togglePanel('dice', true)\"\n      @roll=\"handleRoll\"\n      @skill-check=\"handleSkillCheck\"\n    />\n    \n    <floating-notes\n      v-if=\"showNotes\"\n      :initial-x=\"300\"\n      :initial-y=\"300\"\n      :character-id=\"currentCharacterId\"\n      @close=\"toggleNotes\"\n      @note-saved=\"handleNoteSaved\"\n    />\n    \n    <!-- 群聊面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.groupChat && currentAIMode === 'script'\"\n      title=\"玩家讨论区\"\n      :initial-x=\"400\"\n      :initial-y=\"200\"\n      :initial-width=\"500\"\n      :initial-height=\"400\"\n      :min-width=\"300\"\n      :min-height=\"300\"\n      @close=\"togglePanel('groupChat', true)\"\n    >\n      <group-chat :room-id=\"internalRoomId\" :ai-mode=\"currentAIMode\" />\n    </floating-panel>\n    \n    <!-- 地图系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.map\"\n      title=\"地图系统\"\n      :initial-x=\"100\"\n      :initial-y=\"150\"\n      :initial-width=\"600\"\n      :initial-height=\"450\"\n      :min-width=\"400\"\n      :min-height=\"350\"\n      @close=\"togglePanel('map', true)\"\n    >\n      <map-system :room-id=\"internalRoomId\" />\n    </floating-panel>\n    \n    <!-- 线索墙面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.clueBoard\"\n      title=\"线索墙\"\n      :initial-x=\"150\"\n      :initial-y=\"200\"\n      :initial-width=\"700\"\n      :initial-height=\"500\"\n      :min-width=\"400\"\n      :min-height=\"400\"\n      @close=\"togglePanel('clueBoard', true)\"\n    >\n      <clue-board :room-id=\"internalRoomId\" />\n    </floating-panel>\n\n    <!-- 战斗系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.combat\"\n      title=\"战斗系统\"\n      :initial-x=\"100\"\n      :initial-y=\"100\"\n      :initial-width=\"800\"\n      :initial-height=\"600\"\n      :min-width=\"600\"\n      :min-height=\"500\"\n      @close=\"togglePanel('combat', true)\"\n    >\n      <combat-system\n        @combat-started=\"handleCombatStarted\"\n        @combat-ended=\"handleCombatEnded\"\n        @open-skill-check=\"openSkillCheckForCharacter\"\n      />\n    </floating-panel>\n\n    <!-- 技能检定系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.skillCheck\"\n      title=\"技能检定系统\"\n      :initial-x=\"200\"\n      :initial-y=\"150\"\n      :initial-width=\"700\"\n      :initial-height=\"550\"\n      :min-width=\"500\"\n      :min-height=\"450\"\n      @close=\"togglePanel('skillCheck', true)\"\n    >\n      <skill-check-system\n        :initial-character=\"skillCheckCharacter\"\n        :initial-skill=\"skillCheckSkill\"\n        @close=\"togglePanel('skillCheck', true)\"\n        @skill-check-result=\"handleSkillCheckResult\"\n        @opposed-check-result=\"handleOpposedCheckResult\"\n        @skill-growth=\"handleSkillGrowth\"\n        @growth-check-result=\"handleGrowthCheckResult\"\n        @character-selected=\"handleSkillCheckCharacterSelected\"\n      />\n    </floating-panel>\n\n    <!-- 装备系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.equipment\"\n      title=\"装备系统\"\n      :initial-x=\"150\"\n      :initial-y=\"100\"\n      :initial-width=\"900\"\n      :initial-height=\"650\"\n      :min-width=\"700\"\n      :min-height=\"500\"\n      @close=\"togglePanel('equipment', true)\"\n    >\n      <equipment-system\n        @weapon-equipped=\"handleWeaponEquipped\"\n        @weapon-unequipped=\"handleWeaponUnequipped\"\n        @weapon-used=\"handleWeaponUsed\"\n        @armor-equipped=\"handleArmorEquipped\"\n        @armor-unequipped=\"handleArmorUnequipped\"\n        @item-used=\"handleItemUsed\"\n        @item-purchased=\"handleItemPurchased\"\n        @item-dropped=\"handleItemDropped\"\n        @heal-character=\"handleHealCharacter\"\n        @read-book=\"handleReadBook\"\n      />\n    </floating-panel>\n\n    <!-- 经历包系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.experiencePack\"\n      title=\"经历包系统\"\n      :initial-x=\"250\"\n      :initial-y=\"150\"\n      :initial-width=\"800\"\n      :initial-height=\"600\"\n      :min-width=\"600\"\n      :min-height=\"500\"\n      @close=\"togglePanel('experiencePack', true)\"\n    >\n      <experience-pack-system\n        @character-updated=\"handleCharacterUpdate\"\n      />\n    </floating-panel>\n\n    <!-- 法术系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.spells\"\n      title=\"法术系统\"\n      :initial-x=\"100\"\n      :initial-y=\"120\"\n      :initial-width=\"900\"\n      :initial-height=\"700\"\n      :min-width=\"700\"\n      :min-height=\"600\"\n      @close=\"togglePanel('spells', true)\"\n    >\n      <spell-system\n        @character-updated=\"handleCharacterUpdate\"\n        @spell-cast=\"handleSpellCast\"\n      />\n    </floating-panel>\n\n    <!-- 疯狂症状系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.madness\"\n      title=\"疯狂症状系统\"\n      :initial-x=\"150\"\n      :initial-y=\"100\"\n      :initial-width=\"850\"\n      :initial-height=\"650\"\n      :min-width=\"650\"\n      :min-height=\"550\"\n      @close=\"togglePanel('madness', true)\"\n    >\n      <madness-system\n        @character-updated=\"handleCharacterUpdate\"\n      />\n    </floating-panel>\n\n    <!-- 神话典籍图书馆面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.library\"\n      title=\"神话典籍图书馆\"\n      :initial-x=\"200\"\n      :initial-y=\"80\"\n      :initial-width=\"950\"\n      :initial-height=\"750\"\n      :min-width=\"750\"\n      :min-height=\"650\"\n      @close=\"togglePanel('library', true)\"\n    >\n      <mythos-library\n        @character-updated=\"handleCharacterUpdate\"\n      />\n    </floating-panel>\n    \n    <!-- 音频系统面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.audioSystem && isRoomOwner\"\n      title=\"音频系统\"\n      :initial-x=\"200\"\n      :initial-y=\"250\"\n      :initial-width=\"600\"\n      :initial-height=\"400\"\n      :min-width=\"350\"\n      :min-height=\"300\"\n      @close=\"togglePanel('audioSystem', true)\"\n    >\n      <audio-system :roomId=\"internalRoomId\" :isKP=\"isRoomOwner\" />\n    </floating-panel>\n    \n    <!-- 语音聊天面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.voiceChat\"\n      title=\"语音聊天\"\n      :initial-x=\"250\"\n      :initial-y=\"300\"\n      :initial-width=\"300\"\n      :initial-height=\"400\"\n      :min-width=\"250\"\n      :min-height=\"300\"\n      @close=\"togglePanel('voiceChat', true)\"\n    >\n      <voice-chat \n        :roomId=\"internalRoomId\" \n        :username=\"$store.getters.currentUser?.username || '玩家'\" \n        :isKP=\"isKP\"\n        @voice-connected=\"handleVoiceConnected\" \n        @voice-disconnected=\"handleVoiceDisconnected\" \n      />\n    </floating-panel>\n    \n    <div class=\"room-toolbar\" :class=\"{ 'collapsed': isToolbarCollapsed }\">\n      <div class=\"toolbar-buttons\">\n        <!-- 角色相关工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleDiceRoller\" class=\"toolbar-btn\" :class=\"{ 'active': showDiceRoller }\">\n            <i class=\"fas fa-dice-d20\"></i>\n            <span class=\"tooltip\">骰子</span>\n          </button>\n\n          <button @click=\"toggleCharacterSheet\" class=\"toolbar-btn\" :class=\"{ 'active': showCharacterSheet }\">\n            <i class=\"fas fa-user\"></i>\n            <span class=\"tooltip\">角色卡</span>\n          </button>\n\n          <button @click=\"toggleSkillCheck\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.skillCheck }\">\n            <i class=\"fas fa-cog\"></i>\n            <span class=\"tooltip\">技能检定</span>\n          </button>\n\n          <button @click=\"toggleNotes\" class=\"toolbar-btn\" :class=\"{ 'active': showNotes }\">\n            <i class=\"fas fa-sticky-note\"></i>\n            <span class=\"tooltip\">笔记</span>\n          </button>\n        </div>\n\n        <!-- 战斗和装备工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleCombat\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.combat }\">\n            <i class=\"fas fa-sword\"></i>\n            <span class=\"tooltip\">战斗</span>\n          </button>\n\n          <button @click=\"toggleEquipment\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.equipment }\">\n            <i class=\"fas fa-shield-alt\"></i>\n            <span class=\"tooltip\">装备</span>\n          </button>\n\n          <button @click=\"toggleExperiencePack\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.experiencePack }\">\n            <i class=\"fas fa-star\"></i>\n            <span class=\"tooltip\">经历包</span>\n          </button>\n        </div>\n\n        <!-- 神话和魔法工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleSpells\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.spells }\">\n            <i class=\"fas fa-magic\"></i>\n            <span class=\"tooltip\">法术</span>\n          </button>\n\n          <button @click=\"toggleMadness\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.madness }\">\n            <i class=\"fas fa-brain\"></i>\n            <span class=\"tooltip\">疯狂</span>\n          </button>\n\n          <button @click=\"toggleLibrary\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.library }\">\n            <i class=\"fas fa-book-open\"></i>\n            <span class=\"tooltip\">典籍</span>\n          </button>\n        </div>\n        \n        <!-- 地图和线索工具 -->\n        <div class=\"toolbar-group\">\n          <button @click=\"toggleMap\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.map }\">\n            <i class=\"fas fa-map\"></i>\n            <span class=\"tooltip\">地图</span>\n          </button>\n          \n          <button @click=\"toggleClueBoard\" class=\"toolbar-btn\" :class=\"{ 'active': visiblePanels.clueBoard }\">\n            <i class=\"fas fa-thumbtack\"></i>\n            <span class=\"tooltip\">线索墙</span>\n          </button>\n          \n          <button \n            v-if=\"isRoomOwner\"\n            @click=\"toggleAudioSystem\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.audioSystem }\"\n          >\n            <i class=\"fas fa-music\"></i>\n            <span class=\"tooltip\">音频</span>\n          </button>\n        </div>\n        \n        <!-- 游戏存档按钮 -->\n        <div class=\"toolbar-group\" v-if=\"isRoomOwner\">\n          <button \n            @click=\"toggleSaveOptions\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': showSaveOptions }\"\n          >\n            <i class=\"fas fa-save\"></i>\n            <span class=\"tooltip\">游戏存档</span>\n          </button>\n        </div>\n        \n        <!-- 聊天工具 -->\n        <div class=\"toolbar-group\">\n          <button \n            @click=\"toggleVoiceChat\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.voiceChat }\"\n          >\n            <i class=\"fas fa-microphone\"></i>\n            <span class=\"tooltip\">语音</span>\n          </button>\n          \n          <button \n            @click=\"togglePrivateChat\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.privateChat }\"\n          >\n            <i class=\"fas fa-comments\"></i>\n            <span class=\"tooltip\">私聊</span>\n          </button>\n          \n          <button \n            v-if=\"currentAIMode === 'script'\"\n            @click=\"toggleGroupChat\" \n            class=\"toolbar-btn\"\n            :class=\"{ 'active': visiblePanels.groupChat }\"\n          >\n            <i class=\"fas fa-users\"></i>\n            <span class=\"tooltip\">群聊</span>\n          </button>\n        </div>\n      </div>\n      \n      <!-- 折叠按钮 -->\n      <button @click=\"toggleToolbar\" class=\"collapse-btn\">\n        <i :class=\"[\n          'fas', \n          isToolbarCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'\n        ]\"></i>\n      </button>\n    </div>\n    \n    <!-- 浮动面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.privateChat\"\n      title=\"私聊\"\n      :initial-x=\"windowWidth - 350\"\n      :initial-y=\"windowHeight - 450\"\n      :initial-width=\"320\"\n      :initial-height=\"400\"\n      :min-width=\"250\"\n      :min-height=\"300\"\n      @close=\"togglePanel('privateChat', true)\"\n    >\n      <private-chat-manager :online-users=\"onlineUsers\" />\n    </floating-panel>\n    \n    <!-- 私聊管理器 -->\n    <!-- <private-chat-manager :online-users=\"onlineUsers\" :hidden=\"isToolbarCollapsed\" /> -->\n    \n    <!-- 角色选择模态框 -->\n    <modal v-if=\"showCharacterModal\" @close=\"showCharacterModal = false\">\n      <template #header>\n        <h3>选择角色</h3>\n      </template>\n      <div class=\"character-list\">\n        <div \n          v-for=\"character in characters\" \n          :key=\"character.id\" \n          @click=\"selectCharacter(character)\"\n          class=\"character-item\"\n        >\n          <h4>{{ character.name }}</h4>\n          <p>{{ character.occupation }}</p>\n        </div>\n      </div>\n    </modal>\n    \n    <!-- 设置模态框 -->\n    <modal v-if=\"showSettingsModal\" @close=\"showSettingsModal = false\">\n      <template #header>\n        <h3>房间设置</h3>\n      </template>\n      <div class=\"settings-content\">\n        <div class=\"setting-group\">\n          <label>字体大小</label>\n          <div class=\"font-size-controls\">\n            <button @click=\"changeFontSize(-1)\" class=\"font-btn\">A-</button>\n            <span class=\"current-size\">{{ fontSize }}px</span>\n            <button @click=\"changeFontSize(1)\" class=\"font-btn\">A+</button>\n          </div>\n        </div>\n        \n        <div class=\"setting-group\">\n          <label>通知设置</label>\n          <div class=\"toggle-switch\">\n            <input type=\"checkbox\" id=\"soundToggle\" v-model=\"playSounds\">\n            <label for=\"soundToggle\">骰子声音</label>\n          </div>\n          <div class=\"toggle-switch\">\n            <input type=\"checkbox\" id=\"notifyToggle\" v-model=\"showNotifications\">\n            <label for=\"notifyToggle\">桌面通知</label>\n          </div>\n        </div>\n        \n        <div class=\"setting-actions\">\n          <button @click=\"saveSettings\" class=\"save-settings-btn\">保存设置</button>\n        </div>\n      </div>\n    </modal>\n    \n    <!-- 剧本模式设置已移至ChatBox.vue -->\n    \n    <!-- AI模型设置面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.aiSettings && isRoomOwner\"\n      title=\"AI模型设置\"\n      :initial-x=\"200\"\n      :initial-y=\"150\"\n      :initial-width=\"500\"\n      :initial-height=\"550\"\n      @close=\"togglePanel('aiSettings', true)\"\n    >\n      <a-i-model-settings \n        :room-id=\"roomId\"\n        :is-room-owner=\"isRoomOwner\"\n        @settings-updated=\"handleAISettingsUpdated\"\n      />\n    </floating-panel>\n    \n    <!-- 游戏存档管理面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.gameSaves && isRoomOwner\"\n      title=\"游戏存档\"\n      :initial-x=\"200\"\n      :initial-y=\"250\"\n      :initial-width=\"500\"\n      :initial-height=\"400\"\n      @close=\"togglePanel('gameSaves', true)\"\n    >\n      <game-save-manager \n        :room-id=\"internalRoomId\" \n        @save-created=\"handleSaveCreated\" \n        @save-updated=\"handleSaveUpdated\"\n        @save-deleted=\"handleSaveDeleted\"\n        @save-load=\"handleSaveLoad\" \n        @save-error=\"handleSaveError\" \n        @collect-state=\"collectGameState\"\n        @save-auto-created=\"handleSaveAutoCreated\"\n        @save-message=\"handleSaveMessage\"\n        @save-restored=\"handleSaveRestored\"\n        @save-preview=\"handleSavePreview\"\n      />\n    </floating-panel>\n    \n    <!-- 剧本查看器面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.scenarioViewer && scenarioContent\"\n      title=\"剧本查看器\"\n      :initial-x=\"300\"\n      :initial-y=\"150\"\n      :initial-width=\"700\"\n      :initial-height=\"500\"\n      :min-width=\"400\"\n      :min-height=\"350\"\n      @close=\"togglePanel('scenarioViewer', true)\"\n    >\n      <scenario-viewer \n        :scenario-content=\"scenarioContent\" \n        :scenario-title=\"scenarioTitle\"\n        :file-size=\"scenarioFileSize\"\n      />\n    </floating-panel>\n    \n    <!-- 公告展示面板 -->\n    <floating-panel\n      v-if=\"visiblePanels.announcement\"\n      title=\"公告展示\"\n      :initial-x=\"300\"\n      :initial-y=\"150\"\n      :initial-width=\"700\"\n      :initial-height=\"500\"\n      :min-width=\"400\"\n      :min-height=\"350\"\n      @close=\"togglePanel('announcement', true)\"\n    >\n      <announcement-viewer \n        :content=\"announcementContent\" \n        :title=\"announcementTitle\"\n        :publish-time=\"announcementTime\"\n        :is-editable=\"isRoomOwner\"\n        @save=\"handleAnnouncementSave\"\n      />\n    </floating-panel>\n    \n    <!-- 在模板中添加预览模式提示 -->\n    <div class=\"preview-mode-indicator\" v-if=\"isPreviewMode\">\n      <div class=\"preview-info\">\n        <i class=\"fas fa-eye\"></i>\n        <span>预览模式</span>\n      </div>\n      <div class=\"preview-actions\">\n        <button class=\"exit-preview-btn\" @click=\"exitPreviewMode\">\n          <i class=\"fas fa-times\"></i> 退出预览\n        </button>\n      </div>\n    </div>\n    \n    <!-- 存档选项面板 -->\n    <div class=\"save-options-panel\" v-if=\"showSaveOptions\">\n      <div class=\"save-options-content\">\n        <div class=\"save-option\" @click=\"quickSave\">\n          <i class=\"fas fa-save\"></i>\n          <span>快速存档</span>\n          <small>Ctrl+S</small>\n        </div>\n        <div class=\"save-option\" @click=\"quickLoad\" :class=\"{ 'disabled': !quickSaveId }\">\n          <i class=\"fas fa-folder-open\"></i>\n          <span>快速读档</span>\n          <small>Ctrl+O</small>\n        </div>\n        <div class=\"save-option\" @click=\"toggleGameSaves\">\n          <i class=\"fas fa-history\"></i>\n          <span>存档管理</span>\n          <small>Ctrl+H</small>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport ChatBox from '@/components/ChatBox.vue';\nimport Modal from '@/components/Modal.vue';\nimport FloatingPanel from '@/components/FloatingPanel.vue';\nimport FloatingCharacterSheet from '@/components/FloatingCharacterSheet.vue';\nimport FloatingDiceRoller from '@/components/FloatingDiceRoller.vue';\nimport FloatingNotes from '@/components/FloatingNotes.vue';\nimport AIModelSettings from '@/components/AIModelSettings.vue';\nimport MapSystem from '@/components/MapSystem.vue';\nimport ClueBoard from '@/components/ClueBoard.vue';\n\nimport AudioSystem from '@/components/AudioSystem.vue';\nimport VoiceChat from '@/components/VoiceChat.vue';\nimport PrivateChatManager from '@/components/PrivateChatManager.vue';\nimport GroupChat from '@/components/GroupChat.vue';\nimport GameSaveManager from '@/components/GameSaveManager.vue';\nimport ScenarioViewer from '@/components/ScenarioViewer.vue';\nimport AnnouncementViewer from '@/components/AnnouncementViewer.vue';\nimport apiService from '@/services/api';\nimport websocketService from '@/services/websocket';\nimport { storageMixin } from '@/mixins/storageMixin';\n\nexport default {\n  name: 'Room',\n  mixins: [storageMixin],\n  components: {\n    ChatBox,\n    Modal,\n    FloatingPanel,\n    FloatingCharacterSheet,\n    FloatingDiceRoller,\n    FloatingNotes,\n    AIModelSettings,\n    MapSystem,\n    ClueBoard,\n    CombatSystem: () => import('@/components/CombatSystem.vue'),\n    SkillCheckSystem: () => import('@/components/SkillCheckSystem.vue'),\n    EquipmentSystem: () => import('@/components/EquipmentSystem.vue'),\n    ExperiencePackSystem: () => import('@/components/ExperiencePackSystem.vue'),\n    SpellSystem: () => import('@/components/SpellSystem.vue'),\n    MadnessSystem: () => import('@/components/MadnessSystem.vue'),\n    MythosLibrary: () => import('@/components/MythosLibrary.vue'),\n\n    AudioSystem,\n    VoiceChat,\n    PrivateChatManager,\n    GroupChat,\n    GameSaveManager,\n    ScenarioViewer,\n    AnnouncementViewer\n  },\n  props: {\n    // 不再使用props传递roomId\n  },\n  data() {\n    return {\n      room: {},\n      messages: [],\n      characters: [],\n      selectedCharacter: null,\n      websocket: null,\n      showCharacterModal: false,\n      showSettingsModal: false,\n      diceHistory: [],\n      gameNotes: '',\n      onlineUsers: [],\n      isTyping: false,\n      typingUsername: '',\n      typingTimer: null,\n      lastTypingTime: 0,\n      fontSize: 16,\n      playSounds: true,\n      showNotifications: false,\n      isToolbarCollapsed: false,\n      isMobile: false,\n      internalRoomId: '', // 使用内部变量存储房间ID\n      hasUnsavedChanges: false, // 跟踪是否有未保存的更改\n      visiblePanels: {\n        character: false,\n        dice: false,\n        notes: false,\n        aiSettings: false,\n        map: false,\n        clueBoard: false,\n        audioSystem: false,\n        voiceChat: false,\n        privateChat: false, // 私聊面板状态\n        groupChat: false,\n        gameSaves: false,  // 游戏存档面板状态\n        scenarioViewer: false, // 剧本查看器面板状态\n        announcement: false, // 公告展示面板状态\n        combat: false, // 战斗系统面板状态\n        skillCheck: false, // 技能检定系统面板状态\n        equipment: false, // 装备系统面板状态\n        experiencePack: false, // 经历包系统面板状态\n        spells: false, // 法术系统面板状态\n        madness: false, // 疯狂症状系统面板状态\n        library: false // 神话典籍图书馆面板状态\n      },\n      currentAIMode: 'normal', // 将在 mounted 中从存储加载\n\n      // 新功能相关状态\n      skillCheckCharacter: null, // 技能检定选中的角色\n      skillCheckSkill: '', // 技能检定选中的技能\n      pendingSceneDescription: null,\n      // AI组件状态\n      aiStatus: {\n          active: false,\n          busy: false\n      },\n      // 房主状态\n      isRoomOwner: false,\n      // 控制API配置提醒显示\n      showConfigAlert: true,\n      // 角色同步定时器\n      characterSyncTimer: null,\n      // 移除默认场景数据\n      scenes: [],\n      currentSceneIndex: 0,\n      currentCharacterId: null,\n      showNotes: false,\n      // 添加缺失的变量\n      isKP: false,\n      currentUser: null,\n      users: [],\n      isLoading: false,\n      showDiceRoller: false,\n      showCharacterSheet: false,\n      // 添加方法引用\n      rollDice: null,\n      uploadScript: null,\n      quickSaveId: null, // 存储快速存档的ID\n      isPreviewMode: false,\n      tempState: null,\n      windowWidth: window.innerWidth,\n      windowHeight: window.innerHeight,\n      showSaveOptions: false,\n      // 剧本相关数据\n      scenarioContent: '',\n      scenarioTitle: '',\n      scenarioFileSize: '',\n      \n      // 公告相关数据\n      announcementContent: '',\n      announcementTitle: '房间公告',\n      announcementTime: new Date().toLocaleString(),\n      \n      // 房间名称编辑相关\n      isEditingRoomName: false,\n      editableRoomName: '',\n    };\n  },\n  computed: {\n    isDarkMode() {\n      return this.$store.getters.isDarkTheme;\n    },\n    hasQuickSave() {\n      // 检查是否有快速存档\n      return this.quickSaveId !== null;\n    }\n  },\n  async created() {\n    try {\n      // 从路由参数获取房间ID\n      const routeId = this.$route.params.id;\n      \n      console.log(`Room组件创建, 路由参数id = ${routeId}`);\n      \n      // 清除之前的房间数据，确保每次都获取新的房间\n      this.$store.commit('CLEAR_CURRENT_ROOM');\n      \n      // 尝试将路由ID转换为数字\n      const numericId = parseInt(routeId);\n      \n      // 如果转换成功并且是有效数字，使用数字ID；否则使用默认ID 1\n      if (!isNaN(numericId)) {\n        this.internalRoomId = numericId;\n      } else {\n        console.error(`无效的房间ID: ${routeId}，使用默认ID 1`);\n        this.internalRoomId = 1;\n      }\n      \n      console.log(`Room组件创建, 原始roomId = ${routeId}, 处理后roomId = ${this.internalRoomId}`);\n      \n      // 初始化用户数据\n      this.currentUser = this.$store.getters.currentUser || { username: '玩家' };\n      this.isKP = this.currentUser.id === 1; // 假设ID为1的是KP\n      \n      // 初始化窗口尺寸\n      this.updateWindowSize();\n      \n      // 检查移动设备\n      this.checkMobileDevice();\n      \n      // 加载工具栏折叠状态\n      const savedToolbarState = this.safeGetItem(`toolbar_collapsed_${this.internalRoomId}`);\n      if (savedToolbarState !== null) {\n        this.isToolbarCollapsed = savedToolbarState === 'true';\n        console.log('加载工具栏状态:', this.isToolbarCollapsed ? '已折叠' : '已展开');\n      } else {\n        // 默认不折叠\n        this.isToolbarCollapsed = false;\n      }\n      \n      // 首先加载房间数据\n      await this.fetchRoomData();\n      console.log('房间数据加载完成', this.room);\n      \n      // 检查当前用户是否是房主\n      this.checkRoomOwnership();\n      \n      // 强制设置为房主（临时解决方案）\n      this.isRoomOwner = true;\n      \n      // 建立WebSocket连接\n      this.connectWebSocket();\n      \n      // 获取用户角色\n      await this.fetchUserCharacters();\n      console.log('角色数据加载完成', this.characters);\n      \n      // 获取历史消息 (之后获取，因为需要WebSocket连接)\n      setTimeout(() => {\n        this.fetchRoomMessages();\n      }, 1000);\n      \n      // 加载用户设置\n      this.loadUserSettings();\n      \n      // 从本地存储加载公告\n      this.loadAnnouncementFromLocalStorage();\n      \n      // 从本地存储加载房间名称\n      this.loadRoomNameFromLocalStorage();\n      \n      // 默认显示公告面板\n      this.togglePanel('announcement');\n      \n      // 默认显示角色卡和骰子面板\n      // 注意：这里不需要设置默认值，因为在loadUserSettings中已经处理了\n      // 只需要确保同步状态到显示变量\n      this.showCharacterSheet = this.visiblePanels.character;\n      this.showDiceRoller = this.visiblePanels.dice;\n      \n      // 设置延时检查系统状态\n      setTimeout(() => {\n        this.checkSystemStatus();\n      }, 2000);\n      \n      // 检查是否已经关闭过提醒\n      if (this.safeGetItem('aiConfigAlertDismissed') === 'true') {\n        this.showConfigAlert = false;\n      }\n      \n      // 检查API密钥状态\n      this.checkApiKeyStatus();\n      \n      // 同步选中角色的状态\n      this.syncSelectedCharacterStatus();\n      \n      // 设置定时同步角色状态\n      this.setupCharacterSyncTimer();\n      \n      // 初始化方法引用\n      this.rollDice = this.handleRollDice;\n      this.uploadScript = this.handleUploadScript;\n      \n      // 初始化用户列表\n      this.users = this.onlineUsers;\n      \n      // 添加键盘事件监听\n      window.addEventListener('keydown', this.handleKeyDown);\n      \n    } catch (error) {\n      console.error('初始化房间失败', error);\n      if (error.message === \"房间不存在\" || error.response?.data?.detail === \"房间不存在\") {\n        // 如果房间不存在，创建一个新房间\n        console.log('尝试创建房间', this.internalRoomId);\n        const mockRoom = { \n          id: parseInt(this.internalRoomId), \n          name: `测试房间 ${this.internalRoomId}`, \n          description: '这是一个自动创建的房间' \n        };\n        this.$store.commit('SET_CURRENT_ROOM', mockRoom);\n        this.room = mockRoom;\n        // 重新尝试WebSocket连接\n        this.connectWebSocket();\n        \n        // 添加系统消息提示\n        this.showSystemMessage('已自动创建测试房间');\n        \n        // 获取用户角色\n        this.fetchUserCharacters().catch(e => console.error('获取角色失败', e));\n      } else {\n        this.showSystemMessage('加载房间失败，请尝试重新进入');\n        setTimeout(() => this.$router.push('/rooms'), 2000);\n      }\n    }\n  },\n  mounted() {\n    // 注意：房间ID已在created钩子中设置，这里不需要重新设置\n    console.log(`Room组件mounted, 当前房间ID = ${this.internalRoomId}`);\n    \n    // 加载用户设置\n    this.loadUserSettings();\n    \n    // 检查设备类型\n    this.detectDevice();\n    window.addEventListener('resize', this.detectDevice);\n    \n    // 监听全屏状态变化\n    document.addEventListener('fullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('webkitfullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('mozfullscreenchange', this.handleFullScreenChange);\n    document.addEventListener('MSFullscreenChange', this.handleFullScreenChange);\n    \n    // 尝试预先检测浏览器全屏支持情况\n    const elem = document.querySelector('.room-container');\n    if (!elem.requestFullscreen && \n        !elem.webkitRequestFullscreen && \n        !elem.mozRequestFullScreen &&\n        !elem.msRequestFullscreen) {\n      console.warn('当前浏览器可能不完全支持全屏API');\n    } else {\n      console.log('浏览器支持全屏API');\n    }\n    \n    // 添加键盘快捷键监听\n    window.addEventListener('keydown', this.handleKeyboardShortcuts);\n  },\n  beforeDestroy() {\n    // 移除键盘事件监听\n    window.removeEventListener('keydown', this.handleKeyDown);\n    \n    // 清除角色同步定时器\n    if (this.characterSyncTimer) {\n      clearInterval(this.characterSyncTimer);\n    }\n    \n    // 关闭WebSocket连接\n    if (this.websocket) {\n      this.websocket.close();\n    }\n    \n    // 断开WebSocket连接\n    this.disconnectWebSocket();\n    \n    // 移除事件监听器\n    window.removeEventListener('resize', this.detectDevice);\n    \n    // 移除全屏状态变化监听器\n    document.removeEventListener('fullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('webkitfullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('mozfullscreenchange', this.handleFullScreenChange);\n    document.removeEventListener('MSFullscreenChange', this.handleFullScreenChange);\n    \n    // 如果离开页面时处于全屏状态，尝试退出全屏\n    try {\n      const isFullScreen = document.fullscreenElement || \n                         document.webkitFullscreenElement || \n                         document.mozFullScreenElement || \n                         document.msFullscreenElement;\n      if (isFullScreen) {\n        if (document.exitFullscreen) {\n          document.exitFullscreen();\n        } else if (document.webkitExitFullscreen) {\n          document.webkitExitFullscreen();\n        } else if (document.mozCancelFullScreen) {\n          document.mozCancelFullScreen();\n        } else if (document.msExitFullscreen) {\n          document.msExitFullscreen();\n        }\n      }\n    } catch (error) {\n      console.error('退出全屏失败:', error);\n    }\n    \n    // 移除键盘快捷键监听\n    window.removeEventListener('keydown', this.handleKeyboardShortcuts);\n  },\n  methods: {\n    async fetchRoomData() {\n      try {\n        const currentRoom = this.$store.getters.currentRoomData;\n        \n        // 确保房间ID是有效的数字\n        const roomId = this.internalRoomId;\n        \n        console.log(`尝试获取房间数据，房间ID: ${roomId}, 当前房间:`, currentRoom);\n        \n        // 如果没有当前房间数据，或者当前房间ID与请求的不一致，则获取新的房间数据\n        if (!currentRoom || parseInt(currentRoom.id) !== parseInt(roomId)) {\n          // 尝试从服务器获取房间信息\n          try {\n            console.log(`当前房间数据不匹配，从服务器获取房间ID: ${roomId}`);\n            \n            // 使用store的joinRoom方法，它已经包含了错误处理逻辑\n            const room = await this.$store.dispatch('joinRoom', { roomId });\n            \n            // 确保房间ID是数字类型\n            room.id = parseInt(room.id);\n            this.room = room;\n            \n            console.log('获取到的房间数据:', room);\n            \n            // 如果房间ID与请求的ID不一致，重定向到正确的房间\n            if (room.id !== parseInt(roomId)) {\n              console.warn(`房间ID不匹配，请求ID: ${roomId}, 返回ID: ${room.id}`);\n              // 更新URL但不重新加载页面\n              window.history.replaceState(null, '', `/room/${room.id}`);\n              this.internalRoomId = room.id;\n            }\n          } catch (error) {\n            console.error('加载房间失败', error);\n            throw error;\n          }\n        } else {\n          console.log('使用当前房间数据:', currentRoom);\n          this.room = currentRoom;\n        }\n        \n        // 模拟在线用户数据\n        this.onlineUsers = [\n          { id: 1, username: 'KP' },\n          { id: 2, username: this.$store.getters.currentUser?.username || '玩家' }\n        ];\n      } catch (error) {\n        console.error('fetchRoomData失败:', error);\n        throw error; // 确保错误被传递出去\n      }\n    },\n    async fetchUserCharacters() {\n      try {\n        await this.$store.dispatch('fetchCharacters');\n        this.characters = this.$store.getters.userCharacters;\n        \n        // 如果有角色，默认选择第一个\n        if (this.characters.length > 0) {\n          this.selectedCharacter = this.characters[0];\n        }\n      } catch (error) {\n        console.error('加载角色失败', error);\n      }\n    },\n    async fetchRoomMessages() {\n      // 这里简化处理，实际应从API获取历史消息\n      // 演示用的假消息\n      this.messages = [\n        {\n          type: 'system',\n          content: '欢迎来到房间',\n          timestamp: Date.now() - 5000\n        },\n        {\n          type: 'chat',\n          user_id: 1,\n          username: 'KP',\n          content: '你们面前是一扇古老的门，上面刻着奇怪的符号...',\n          timestamp: Date.now() - 3000\n        }\n      ];\n    },\n    connectWebSocket() {\n      try {\n        // 获取当前用户ID\n        const userId = this.$store.getters.currentUser?.id || 'guest';\n        \n        // 设置WebSocket事件监听器\n        websocketService.onConnect = () => {\n          console.log('WebSocket连接成功');\n          this.showSystemMessage('已成功连接到房间');\n          \n          // 连接成功后，请求加入房间\n          // 优先使用this.room.id，因为这是已经确认的房间ID\n          const roomId = this.room?.id || this.internalRoomId;\n          \n          console.log(`WebSocket连接成功，加入房间ID: ${roomId}`);\n          \n          websocketService.sendMessage({\n            type: 'join_room',\n            room_id: roomId,\n            user_id: userId,\n            username: this.$store.getters.currentUser?.username || '玩家'\n          });\n        };\n        \n        websocketService.onDisconnect = (reason) => {\n          console.log('WebSocket连接断开:', reason);\n          this.showSystemMessage('与服务器的连接已断开，正在尝试重新连接...');\n        };\n        \n        websocketService.onError = (error) => {\n          console.error('WebSocket错误:', error);\n          this.showSystemMessage('连接发生错误: ' + error.message);\n        };\n        \n        // 设置自定义事件监听器\n        websocketService.setEventListeners({\n          'chat_message': this.handleChatMessage,\n          'user_joined': this.handleUserJoined,\n          'user_left': this.handleUserLeft,\n          'typing': this.handleTypingEvent,\n          'dice_roll': this.handleDiceRoll,\n          'skill_check': this.handleSkillCheck,\n          'character_update': this.handleCharacterUpdate,\n          'announcement_update': this.handleAnnouncementUpdate,\n          'room_name_update': this.handleRoomNameUpdate\n        });\n        \n        // 确保房间ID有效\n        // 优先使用this.room.id，因为这是已经确认的房间ID\n        const roomId = this.room?.id || this.internalRoomId;\n        \n        console.log(`尝试连接WebSocket，房间ID: ${roomId}, 用户ID: ${userId}`);\n        \n        // 连接WebSocket\n        websocketService.connect(roomId, userId);\n        \n        // 添加离开页面前的提示\n        window.addEventListener('beforeunload', this.handleBeforeUnload);\n      } catch (error) {\n        console.error('连接WebSocket失败:', error);\n        this.showSystemMessage('连接服务器失败，请检查网络连接或刷新页面重试');\n        \n        // 在开发环境中，提供更多调试信息\n        if (process.env.NODE_ENV === 'development') {\n          this.showSystemMessage('开发环境提示: 请确保WebSocket服务器已启动，端口为8084');\n          \n          // 5秒后显示模拟连接成功的消息，方便开发调试\n          setTimeout(() => {\n            this.showSystemMessage('开发环境模拟: 已成功连接到房间 (模拟)');\n            \n            // 添加一些模拟的用户\n            this.onlineUsers = [\n              { id: 1, username: 'KP' },\n              { id: 2, username: this.$store.getters.currentUser?.username || '玩家' },\n              { id: 3, username: '模拟玩家1' },\n              { id: 4, username: '模拟玩家2' }\n            ];\n          }, 5000);\n        }\n      }\n    },\n    disconnectWebSocket() {\n      websocketService.disconnect();\n      this.$store.dispatch('leaveRoom');\n    },\n    handleRoll(rollData) {\n        // 添加用户名\n        rollData.username = this.$store.getters.currentUser?.username || '玩家';\n      \n      // 如果没有结果，则在本地生成骰子结果\n      if (!rollData.results || !rollData.total) {\n        // 生成骰子结果\n        const count = parseInt(rollData.count) || 1;\n        const faces = parseInt(rollData.faces) || 100;\n        const modifier = parseInt(rollData.modifier) || 0;\n        \n        const results = [];\n        for (let i = 0; i < count; i++) {\n          results.push(Math.floor(Math.random() * faces) + 1);\n        }\n        \n        const total = results.reduce((sum, val) => sum + val, 0) + modifier;\n        \n        rollData.results = results;\n        rollData.total = total;\n      }\n      \n      // 将骰子结果添加到本地历史记录\n      this.diceHistory.unshift({\n        description: `${rollData.count}D${rollData.faces}${rollData.modifier > 0 ? '+' + rollData.modifier : ''}`,\n        total: rollData.total,\n        results: rollData.results\n      });\n      \n      // 限制历史记录长度\n      if (this.diceHistory.length > 10) {\n        this.diceHistory.pop();\n      }\n      \n      // 创建骰子结果消息\n      const diceMessage = {\n        type: 'system',\n        content: `${rollData.username} 投掷 ${rollData.count}D${rollData.faces}${rollData.modifier > 0 ? '+' + rollData.modifier : ''} = ${rollData.total} [${rollData.results.join(', ')}${rollData.modifier > 0 ? ' + ' + rollData.modifier : ''}]`,\n        timestamp: new Date().toISOString(),\n        roll_data: {\n          count: rollData.count,\n          faces: rollData.faces,\n          modifier: rollData.modifier,\n          results: rollData.results,\n          total: rollData.total\n        }\n      };\n      \n      // 将结果添加到消息列表\n      this.messages.push(diceMessage);\n      \n      // 使用websocketService发送消息\n        websocketService.sendMessage(rollData);\n        \n        if (this.playSounds) {\n          this.playDiceSound();\n      }\n    },\n    handleSkillCheck(skillData) {\n      // 添加必要的信息\n        skillData.type = 'skill-check';\n      if (!skillData.username) {\n        skillData.username = this.$store.getters.currentUser?.username || '玩家';\n      }\n      \n      // 如果有角色信息，添加角色相关数据\n      if (!skillData.character_name && this.selectedCharacter) {\n        skillData.character_name = this.selectedCharacter.name;\n        skillData.character_id = this.selectedCharacter.id;\n      }\n      \n      console.log('Room组件处理技能检定:', skillData);\n      \n      // 使用websocketService发送消息\n        websocketService.sendMessage(skillData);\n      \n      // 播放骰子声音\n      if (this.playSounds) {\n        this.playDiceSound();\n      }\n      \n      // 在本地执行技能检定并显示结果\n      this.performLocalSkillCheck(skillData);\n    },\n    // 在本地执行技能检定并显示结果\n    async performLocalSkillCheck(skillData) {\n      try {\n        // 生成1-100的随机数\n        const diceResult = Math.floor(Math.random() * 100) + 1;\n        const skillValue = skillData.skill_value;\n        \n        // 判定成功等级\n        let success = false;\n        let level = \"失败\";\n        \n        if (diceResult <= skillValue) {\n          success = true;\n          if (diceResult === 1) {\n            level = \"大成功\";\n          } else if (diceResult <= skillValue / 5) {\n            level = \"极难成功\";\n          } else if (diceResult <= skillValue / 2) {\n            level = \"困难成功\";\n          } else {\n            level = \"成功\";\n          }\n        } else {\n          if (diceResult >= 96 && skillValue < 50) {\n            level = \"大失败\";\n          } else if (diceResult === 100) {\n            level = \"大失败\";\n          } else {\n            level = \"失败\";\n          }\n        }\n        \n        // 创建技能检定结果消息\n        const skillName = skillData.skill_name || \"技能\";\n        const characterName = skillData.character_name || this.$store.getters.currentUser?.username || '玩家';\n        \n        const resultMessage = {\n          type: 'system',\n          content: `${characterName} 进行 ${skillName} 检定：掷出了 ${diceResult}/${skillValue}，${level}！`,\n          timestamp: new Date().toISOString(),\n          roll_result: {\n            dice: diceResult,\n            skill: skillValue,\n            success: success,\n            level: level\n          }\n        };\n        \n        // 将结果添加到消息列表\n        this.messages.push(resultMessage);\n        \n      } catch (error) {\n        console.error('本地技能检定失败:', error);\n        this.showSystemMessage('技能检定处理失败');\n      }\n    },\n    sendMessage(message) {\n      let messageData;\n      \n      console.log('Room组件收到消息:', message);\n      \n      // 检查message是否为字符串\n      if (typeof message === 'string') {\n        if (!message.trim()) return;\n        \n        messageData = {\n          type: 'chat',\n          content: message,\n          timestamp: new Date().toISOString(),\n          character_id: this.selectedCharacter ? this.selectedCharacter.id : null,\n          ai_mode: this.currentAIMode\n        };\n      } else {\n        // message已经是一个对象\n        messageData = message;\n        \n        // 确保有时间戳\n        if (!messageData.timestamp) {\n          messageData.timestamp = new Date().toISOString();\n        }\n        \n        // 添加AI模式信息\n        if (!messageData.ai_mode) {\n          messageData.ai_mode = this.currentAIMode;\n        }\n      }\n      \n      console.log('处理后的消息数据:', messageData);\n      \n      // 确保消息有用户名\n      if (messageData.type === 'chat' && !messageData.username) {\n        messageData.username = this.$store.getters.currentUser?.username || '玩家';\n        messageData.user_id = this.$store.getters.currentUser?.id || 2; // 确保用户ID不是1 (KP)\n      }\n      \n      // 过滤心跳消息，不显示在UI中\n      if (messageData.type === 'heartbeat') {\n        console.log('收到心跳消息，不添加到UI:', messageData);\n        websocketService.sendMessage(messageData);\n        return;\n      }\n      \n      // 先将消息添加到UI，确保即使WebSocket发送失败也能看到消息\n      if (messageData.type === 'chat' || messageData.type === 'system') {\n        console.log('直接将消息添加到UI:', messageData);\n        this.messages.push({...messageData});\n        \n        // 检查是非AI生成的玩家消息，并且AI KP处于活动状态，则处理该消息\n        if (messageData.type === 'chat' && \n            !messageData.ai_generated && \n          this.aiStatus.active) {\n          console.log('检测到普通玩家消息，但AI KP功能已移至ChatBox中');\n        }\n      }\n      \n      // 检查WebSocket连接状态\n      if (!websocketService.isConnected || !websocketService.socket) {\n        console.log('WebSocket未连接，尝试重新连接');\n        this.showSystemMessage('连接已断开，正在尝试重新连接...');\n        this.connectWebSocket();\n        \n        // 将消息推入队列等待重连\n        setTimeout(() => {\n          const result = websocketService.sendMessage(messageData);\n          console.log('消息发送结果:', result);\n        }, 1000);\n        return;\n      }\n      \n      console.log('发送消息到WebSocket:', messageData);\n      \n      // 直接发送消息，不依赖WebSocket的readyState属性\n      const sent = websocketService.sendMessage(messageData);\n      console.log('消息发送结果:', sent);\n\n      // 如果消息发送成功，清空输入框\n      if (typeof message === 'string' && sent) {\n        this.messageInput = '';\n      }\n      \n      // 检查是否有待处理的场景描述且玩家回复开始游戏\n      if (this.pendingSceneDescription && \n          messageData.type === 'chat' && \n          messageData.content && \n          typeof messageData.content === 'string' &&\n          !messageData.ai_generated) {\n        \n        // 检查玩家消息是否为开始游戏的确认\n        const startGameKeywords = ['开始游戏', '开始', '准备好了', '开始冒险', '我准备好了', '出发', '是的'];\n        const playerMessage = messageData.content.toLowerCase();\n        \n        if (startGameKeywords.some(keyword => playerMessage.includes(keyword))) {\n          console.log('检测到玩家确认开始游戏');\n          \n          // 发送玩家的确认消息\n          websocketService.sendMessage(messageData);\n          \n          // 稍后发送场景描述\n          setTimeout(() => {\n            console.log('发送延迟的场景描述');\n            const sceneMessage = {\n              type: \"chat\",\n              user_id: 1, // 使用KP的用户ID\n              username: \"KP\",\n              content: this.pendingSceneDescription,\n              ai_generated: true,\n              timestamp: Date.now(),\n              ai_mode: this.currentAIMode\n            };\n            \n            // 保存一个标记，表示这是一个重要的AI消息\n            this.$store.commit('SET_IMPORTANT_MESSAGE', sceneMessage);\n            \n            // 直接添加到UI\n            this.messages.push({...sceneMessage});\n            \n            // 发送消息\n            websocketService.sendMessage(sceneMessage);\n            this.pendingSceneDescription = null; // 清除待处理的场景描述\n          }, 1000);\n        }\n      }\n    },\n    selectCharacter(character) {\n      this.selectedCharacter = character;\n      this.showCharacterModal = false;\n      \n      // 发送系统消息通知角色选择\n      const messageData = {\n        type: 'system',\n        content: `${this.$store.getters.currentUser?.username || '玩家'} 选择了角色: ${character.name}`,\n        timestamp: Date.now()\n      };\n      this.sendMessage(messageData);\n      \n      // 同步选中角色的状态\n      this.syncSelectedCharacterStatus();\n      this.updateCurrentCharacterId(character.id);\n    },\n  \n    // 新增功能\n    playDiceSound() {\n      // 播放骰子声音\n      const audio = new Audio('/sounds/');\n      audio.volume = 0.5;\n      audio.play().catch(err => console.warn('无法播放音效:', err));\n    },\n    sendTypingStatus(isTyping) {\n      // 防止过于频繁发送状态更新\n      const now = Date.now();\n      if (now - this.lastTypingTime < 2000 && isTyping) {\n        return;\n      }\n      \n      // 发送正在输入状态\n      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n        websocketService.sendMessage({\n          type: 'typing',\n          username: this.$store.getters.currentUser?.username || '玩家',\n          isTyping: isTyping\n        });\n        this.lastTypingTime = now;\n      }\n    },\n    toggleDarkMode() {\n      this.$store.dispatch('toggleTheme');\n    },\n    changeFontSize(change) {\n      this.fontSize = Math.max(12, Math.min(24, this.fontSize + change));\n      document.documentElement.style.setProperty('--base-font-size', `${this.fontSize}px`);\n      this.safeSetItem('fontSize', this.fontSize);\n    },\n    saveNotes(notes) {\n      if (notes !== undefined) {\n        this.gameNotes = notes;\n      }\n            this.safeSetItem(`notes_${this.internalRoomId}`, this.gameNotes);\n    },\n    saveSettings() {\n      this.safeSetItem('fontSize', this.fontSize);\n      this.safeSetItem('playSounds', this.playSounds);\n      this.safeSetItem('showNotifications', this.showNotifications);\n      \n      this.showSettingsModal = false;\n    },\n    loadUserSettings() {\n      // 加载AI模式设置\n      this.currentAIMode = this.safeGetItem('ai_mode') || 'normal';\n      \n      // 加载之前保存的笔记\n      this.gameNotes = this.safeGetItem(`notes_${this.internalRoomId}`) || '';\n      \n      // 加载其他设置\n      const savedFontSize = this.safeGetItem('fontSize');\n      if (savedFontSize) {\n        this.fontSize = parseInt(savedFontSize);\n        document.documentElement.style.setProperty('--base-font-size', `${this.fontSize}px`);\n      }\n      \n      const playSounds = this.safeGetItem('playSounds');\n      if (playSounds) {\n        this.playSounds = playSounds === 'true';\n      }\n      \n      const showNotifications = this.safeGetItem('showNotifications');\n      if (showNotifications) {\n        this.showNotifications = showNotifications === 'true';\n      }\n      \n      // 加载面板可见性设置\n      const panelSettings = this.safeGetItem(`panels_${this.internalRoomId}`);\n      if (panelSettings) {\n        try {\n          this.visiblePanels = JSON.parse(panelSettings);\n          console.log('已加载面板设置:', this.visiblePanels);\n        } catch (e) {\n          console.error('解析面板设置失败', e);\n        }\n      } else {\n        // 如果没有保存的设置，设置默认值\n        this.visiblePanels.character = true;\n        this.visiblePanels.dice = true;\n        this.visiblePanels.privateChat = false; // 默认不显示私聊面板\n        console.log('使用默认面板设置');\n      }\n      \n      // 单独检查私聊面板设置\n      const privateChatSetting = this.safeGetItem(`panel_privateChat_${this.internalRoomId}`);\n      if (privateChatSetting) {\n        this.visiblePanels.privateChat = privateChatSetting === 'visible';\n        console.log('已加载私聊面板设置:', this.visiblePanels.privateChat ? '显示' : '隐藏');\n      }\n      \n      // 加载AI状态设置\n      const aiStatusSettings = this.safeGetItem(`ai_status_${this.internalRoomId}`);\n      if (aiStatusSettings) {\n        try {\n          this.aiStatus = JSON.parse(aiStatusSettings);\n        } catch (e) {\n          console.error('解析AI状态设置失败', e);\n        }\n      }\n      \n      // 确保状态与面板可见性一致\n      this.aiStatus.active = this.visiblePanels.ai;\n      \n      // 同步面板状态到其他变量\n      this.showCharacterSheet = this.visiblePanels.character;\n      this.showDiceRoller = this.visiblePanels.dice;\n    },\n    sendDesktopNotification(message) {\n      // 检查浏览器是否支持通知\n      if (\"Notification\" in window) {\n        if (Notification.permission === \"granted\") {\n          new Notification(`${message.username || '用户'} 在 ${this.room.name}`, {\n            body: message.content,\n            icon: '/favicon.ico'\n          });\n        } else if (Notification.permission !== \"denied\") {\n          Notification.requestPermission();\n        }\n      }\n    },\n    // 通用面板切换方法\n    togglePanel(panelName, forceClose = false) {\n      if (forceClose) {\n        this.visiblePanels[panelName] = false;\n      } else {\n        this.visiblePanels[panelName] = !this.visiblePanels[panelName];\n      }\n      \n      // 保存面板状态到本地存储\n      try {\n        // 保存单独的面板状态\n        this.safeSetItem(`panel_${panelName}_${this.internalRoomId}`, \n          this.visiblePanels[panelName] ? 'visible' : 'hidden');\n          \n        // 同时保存整个面板状态对象\n        this.safeSetJSON(`panels_${this.internalRoomId}`, this.visiblePanels);\n        \n        // 同步面板状态到显示变量\n        if (panelName === 'character') {\n          this.showCharacterSheet = this.visiblePanels.character;\n        } else if (panelName === 'dice') {\n          this.showDiceRoller = this.visiblePanels.dice;\n        }\n      } catch (e) {\n        console.warn('无法保存面板状态到本地存储:', e);\n      }\n      \n      console.log(`面板 ${panelName} 状态: ${this.visiblePanels[panelName] ? '显示' : '隐藏'}`);\n    },\n    handleTyping() {\n      // 发送正在输入状态\n      this.sendTypingStatus(true);\n    },\n    handleTypingStopped() {\n      // 发送停止输入状态\n      this.sendTypingStatus(false);\n    },\n    // 添加显示系统消息的方法\n    showSystemMessage(content) {\n      this.$store.dispatch('addMessage', {\n        type: 'system',\n        content: content,\n        timestamp: new Date().toISOString()\n      });\n    },\n    handleSanityUpdate(data) {\n      // 更新角色的理智值\n      if (this.selectedCharacter && this.selectedCharacter.id === data.characterId) {\n        // 创建一个新对象，避免直接修改props\n        this.selectedCharacter = {\n          ...this.selectedCharacter,\n          sanity: data.newSanity\n        };\n        \n        // 发送系统消息通知理智值变化\n        const messageData = {\n          type: 'system',\n          content: `${this.selectedCharacter.name} 的理智值变为 ${data.newSanity}`,\n          timestamp: Date.now()\n        };\n        this.sendMessage(messageData);\n      }\n    },\n\n\n    // 检查当前用户是否是房主\n    checkRoomOwnership() {\n      try {\n        const currentUser = this.$store.getters.currentUser;\n        \n        if (!currentUser) {\n          console.log('未登录用户，默认不是房主');\n          this.isRoomOwner = false;\n          return;\n        }\n        \n        if (!this.room) {\n          console.log('房间数据不存在，无法检查房主状态');\n          this.isRoomOwner = false;\n          return;\n        }\n        \n        // 如果是模拟房间，默认当前用户是房主\n        if (this.room.is_mock) {\n          console.log('模拟房间，当前用户设为房主');\n        this.isRoomOwner = true;\n          return;\n        }\n        \n        // 检查当前用户是否是房间创建者\n        this.isRoomOwner = this.room.keeper_id === currentUser.id;\n        console.log('房主检查结果:', this.isRoomOwner ? '是房主' : '不是房主');\n      } catch (error) {\n        console.error('检查房主状态失败:', error);\n        this.isRoomOwner = false;\n      }\n    },\n    // 检查系统组件状态\n    checkSystemStatus() {\n      console.log('检查系统组件状态...');\n      \n      // 检查WebSocket连接\n      const wsStatus = websocketService.isConnected ? '已连接' : '未连接';\n      console.log(`WebSocket状态: ${wsStatus}`);\n      \n      // 检查服务锁状态\n      console.log('服务锁状态:', websocketService.resourceLocks);\n      \n      // 检查消息队列\n      console.log(`消息队列长度: ${websocketService.messageQueue.length}`);\n      \n      // 检查AI组件状态\n      console.log('AI组件状态:', this.aiStatus);\n      \n      // 检查可见面板状态\n      console.log('可见面板:', this.visiblePanels);\n      \n      if (!websocketService.isConnected) {\n        console.log('WebSocket未连接，尝试重连');\n        this.showSystemMessage('正在重新建立连接...');\n        this.connectWebSocket();\n      }\n      \n      // 确保聊天输入框可用\n      this.ensureChatInputEnabled();\n      \n      // 确认组件正常工作，主动发送一条系统消息\n      this.showSystemMessage('系统检查完成，您可以正常发送消息');\n    },\n    // 确保聊天输入框可用\n    ensureChatInputEnabled() {\n      console.log('确保聊天输入框可用');\n      setTimeout(() => {\n        // 查找所有可能的输入框并确保它们可用\n        const textareas = document.querySelectorAll('textarea');\n        textareas.forEach(textarea => {\n          textarea.disabled = false;\n          console.log('已启用输入框:', textarea);\n        });\n        \n        // 如果有ChatBox引用，尝试直接操作\n        if (this.$refs.chatBox && this.$refs.chatBox.$refs.messageTextarea) {\n          this.$refs.chatBox.$refs.messageTextarea.disabled = false;\n          console.log('已直接启用ChatBox输入框');\n        }\n      }, 500);\n    },\n    // 处理AI设置更新\n    handleAISettingsUpdated(settings) {\n      console.log('AI设置已更新:', settings);\n      this.showSystemMessage('AI模型设置已更新');\n      \n      // 从本地存储加载AI设置并应用\n      try {\n        const parsedSettings = this.safeGetJSON(`aiSettings_${this.roomId}`);\n        if (parsedSettings) {\n          console.log('应用本地AI设置:', parsedSettings);\n          \n          // 如果有AI模式设置，更新当前模式\n          if (parsedSettings.ai_mode) {\n            this.updateAIMode(parsedSettings.ai_mode);\n          }\n        }\n      } catch (error) {\n        console.error('加载本地AI设置失败:', error);\n      }\n    },\n    \n    // 更新AI模式\n    updateAIMode(mode) {\n      if (this.currentAIMode !== mode) {\n        console.log(`AI模式从 ${this.currentAIMode} 切换到 ${mode}`);\n        this.currentAIMode = mode;\n        \n        // 保存到本地存储\n        this.safeSetItem('ai_mode', mode);\n        \n        // 通知WebSocket服务\n        websocketService.setAIMode(mode);\n        \n        // 显示系统消息\n        this.showSystemMessage(`AI模式已切换到: ${mode === 'normal' ? '普通模式' : '剧本模式'}`);\n        \n        // 如果切换到剧本模式，自动打开群聊面板\n        if (mode === 'script' && !this.visiblePanels.groupChat) {\n          setTimeout(() => {\n            this.togglePanel('groupChat', false);\n          }, 500);\n        }\n      }\n    },\n    // 跳转到AI设置\n    goToAISettings() {\n      this.togglePanel('settings');\n      setTimeout(() => {\n        // 聚焦到AI设置选项卡，如果有多个选项卡的话\n        this.$refs.aiSettings && this.$refs.aiSettings.focus();\n      }, 300);\n    },\n    // 关闭配置提醒\n    dismissConfigAlert() {\n      this.showConfigAlert = false;\n      // 可以保存到本地存储，避免重复显示\n      this.safeSetItem('aiConfigAlertDismissed', 'true');\n    },\n    // 检查API密钥状态\n    async checkApiKeyStatus() {\n      try {\n        const response = await apiService.aiSettings.getSettings(this.roomId);\n        if (response.data && response.data.success) {\n          const settings = response.data.settings;\n          \n          // 将设置保存到store\n          this.$store.commit('UPDATE_AI_SETTINGS', {\n            apiKey: settings.api_key,\n            apiUrl: settings.api_url,\n            modelName: settings.model_name\n          });\n          \n          // 如果之前通过测试，则标记为有效\n          const apiTested = this.safeGetItem(`apiTested_${this.roomId}`);\n          if (apiTested === 'true') {\n            this.$store.commit('SET_API_VALID', true);\n            this.showConfigAlert = false;\n          } else {\n            this.$store.commit('SET_API_VALID', false);\n            this.showConfigAlert = true;\n          }\n        }\n      } catch (error) {\n        console.error('检查API密钥状态失败:', error);\n      }\n    },\n    // 同步选中角色的状态\n    syncSelectedCharacterStatus() {\n      if (!this.selectedCharacter || !this.selectedCharacter.id) return;\n      \n      // 请求从服务器同步角色状态\n      this.$store.dispatch('syncCharacterData', this.selectedCharacter.id)\n        .then(() => {\n          console.log('角色状态同步完成:', this.selectedCharacter.name);\n          this.showSystemMessage(`角色 ${this.selectedCharacter.name} 数据已同步`);\n        })\n        .catch(error => {\n          console.error('角色状态同步失败:', error);\n        });\n    },\n    // 处理角色同步事件\n    handleCharacterSync(characterId) {\n      console.log(`收到角色同步事件: ${characterId}`);\n      \n      // 更新角色显示\n      if (this.selectedCharacter && this.selectedCharacter.id === characterId) {\n        // 从store获取最新的角色数据\n        const updatedCharacter = this.$store.getters.userCharacters.find(c => c.id === characterId);\n        if (updatedCharacter) {\n          this.selectedCharacter = updatedCharacter;\n          console.log('角色数据已更新:', updatedCharacter.name);\n        }\n      }\n    },\n    // 设置定时同步角色状态\n    setupCharacterSyncTimer() {\n      // 每隔5分钟同步一次角色状态\n      this.characterSyncTimer = setInterval(() => {\n        if (this.selectedCharacter && this.selectedCharacter.id) {\n          console.log('定时同步角色状态:', this.selectedCharacter.name);\n          this.syncSelectedCharacterStatus();\n        }\n      }, 5 * 60 * 1000); // 5分钟\n    },\n    // 移除场景相关方法\n    \n    handleSceneSave(data) {\n      // 保存场景数据\n      this.scenes[data.sceneIndex] = data.scene;\n      \n      // 发送场景更新消息\n      this.sendSystemMessage(`场景 \"${data.scene.name}\" 已更新`);\n      \n      // 持久化场景数据\n      this.saveScenesToServer();\n    },\n    \n    saveScenesToServer() {\n      // 这里可以添加将场景数据保存到服务器的逻辑\n      // 例如通过WebSocket发送或API调用\n      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n        this.websocket.send(JSON.stringify({\n          type: 'save-scenes',\n          scenes: this.scenes\n        }));\n      }\n    },\n    \n    loadScenesFromServer() {\n      // 从服务器加载场景数据\n      // 这里可以添加API调用或从WebSocket接收场景数据的逻辑\n    },\n    handleNoteSaved(note) {\n      // 可以在这里处理笔记保存事件\n      console.log('笔记已保存:', note);\n    },\n    updateCurrentCharacterId(characterId) {\n      this.currentCharacterId = characterId;\n    },\n    // 切换骰子面板显示状态\n    toggleDiceRoller() {\n      this.visiblePanels.dice = !this.visiblePanels.dice;\n      this.showDiceRoller = this.visiblePanels.dice;\n    },\n    // 切换角色卡面板显示状态\n    toggleCharacterSheet() {\n      this.visiblePanels.character = !this.visiblePanels.character;\n      this.showCharacterSheet = this.visiblePanels.character;\n    },\n    // 切换笔记面板显示状态\n    toggleNotes() {\n      this.showNotes = !this.showNotes;\n    },\n    // 切换地图面板显示状态\n    toggleMap() {\n      this.visiblePanels.map = !this.visiblePanels.map;\n    },\n    // 切换线索墙面板显示状态\n    toggleClueBoard() {\n      this.togglePanel('clueBoard');\n    },\n    // 切换音频系统面板显示状态\n    toggleAudioSystem() {\n      this.togglePanel('audioSystem');\n    },\n    // 切换语音聊天面板显示状态\n    toggleVoiceChat() {\n      this.togglePanel('voiceChat');\n    },\n    // 切换私聊面板显示状态\n    togglePrivateChat() {\n      this.togglePanel('privateChat');\n      console.log('私聊面板状态:', this.visiblePanels.privateChat ? '显示' : '隐藏');\n    },\n    // 切换群聊面板显示状态\n    toggleGroupChat() {\n      this.togglePanel('groupChat');\n    },\n    // 处理语音聊天连接事件\n    handleVoiceConnected() {\n      this.showSystemMessage('您已加入语音聊天');\n    },\n    handleVoiceDisconnected() {\n      this.showSystemMessage('您已离开语音聊天');\n    },\n    // 添加骰子投掷处理方法\n    handleRollDice(diceData) {\n      if (!diceData) return;\n      \n      // 创建骰子消息\n      const diceMessage = {\n        type: 'roll',\n        count: diceData.count || 1,\n        faces: diceData.faces || 100,\n        modifier: diceData.modifier || 0,\n        username: this.currentUser?.username || '玩家',\n        timestamp: new Date().toISOString()\n      };\n      \n      // 发送到WebSocket\n      if (websocketService.isConnected) {\n        websocketService.sendMessage(diceMessage);\n      } else {\n        // 本地模拟骰子结果\n        const results = [];\n        let total = 0;\n        for (let i = 0; i < diceMessage.count; i++) {\n          const result = Math.floor(Math.random() * diceMessage.faces) + 1;\n          results.push(result);\n          total += result;\n        }\n        total += diceMessage.modifier;\n        \n        // 显示本地骰子结果\n        this.showSystemMessage(`${diceMessage.username} 投掷 ${diceMessage.count}D${diceMessage.faces}${diceMessage.modifier > 0 ? '+' + diceMessage.modifier : ''} = ${total} [${results.join(', ')}${diceMessage.modifier > 0 ? ' + ' + diceMessage.modifier : ''}]`);\n      }\n    },\n    // 添加脚本上传处理方法\n    handleUploadScript(file) {\n      if (!file) return;\n      \n      // 显示上传中消息\n      this.showSystemMessage('正在上传剧本文件...');\n      \n      // 模拟上传过程\n      setTimeout(() => {\n        this.showSystemMessage('剧本文件上传成功！');\n      }, 1500);\n      \n      // 实际项目中，这里应该调用API上传文件\n      console.log('上传剧本文件:', file.name);\n    },\n    toggleToolbar() {\n      this.isToolbarCollapsed = !this.isToolbarCollapsed;\n      console.log('工具栏状态切换:', this.isToolbarCollapsed ? '已折叠' : '已展开');\n      \n      // 保存折叠状态到本地存储\n      this.safeSetItem(`toolbar_collapsed_${this.roomId}`, this.isToolbarCollapsed);\n    },\n    checkMobileDevice() {\n      this.isMobile = window.innerWidth < 768;\n      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');\n    },\n    // WebSocket事件处理函数\n    handleChatMessage(message) {\n      console.log('收到聊天消息:', message);\n      this.$store.dispatch('addMessage', message);\n    },\n    \n    handleUserJoined(data) {\n      console.log('用户加入房间:', data);\n      \n      // 添加到在线用户列表\n      if (!this.onlineUsers.find(user => user.id === data.user_id)) {\n        this.onlineUsers.push({\n          id: data.user_id,\n          username: data.username\n        });\n      }\n      \n      // 显示系统消息\n      this.showSystemMessage(`${data.username} 加入了房间`);\n    },\n    \n    handleUserLeft(data) {\n      console.log('用户离开房间:', data);\n      \n      // 从在线用户列表中移除\n      const index = this.onlineUsers.findIndex(user => user.id === data.user_id);\n      if (index !== -1) {\n        this.onlineUsers.splice(index, 1);\n      }\n      \n      // 显示系统消息\n      this.showSystemMessage(`${data.username} 离开了房间`);\n    },\n    \n    handleTypingEvent(data) {\n      if (data.user_id !== this.$store.getters.currentUser?.id) {\n        this.isTyping = data.typing;\n        this.typingUsername = data.username;\n        \n        // 设置超时，如果长时间没有收到新的typing事件，则自动清除\n        clearTimeout(this.typingTimer);\n        if (this.isTyping) {\n          this.typingTimer = setTimeout(() => {\n            this.isTyping = false;\n          }, 5000);\n        }\n      }\n    },\n    \n    handleDiceRoll(message) {\n      console.log('收到骰子消息:', message);\n      \n      // 创建骰子结果消息\n      const diceMessage = {\n        type: 'system',\n        content: `${message.username} 投掷 ${message.count}D${message.faces}${message.modifier > 0 ? '+' + message.modifier : ''} = ${message.total} [${message.results.join(', ')}${message.modifier > 0 ? ' + ' + message.modifier : ''}]`,\n        timestamp: message.timestamp || new Date().toISOString(),\n        roll_data: {\n          count: message.count,\n          faces: message.faces,\n          modifier: message.modifier,\n          results: message.results,\n          total: message.total\n        }\n      };\n      \n      // 添加到消息列表\n      this.$store.dispatch('addMessage', diceMessage);\n      \n      // 添加到骰子历史记录\n      this.diceHistory.push({\n        ...message,\n        timestamp: new Date().toISOString()\n      });\n      \n      // 保持历史记录不超过20条\n      if (this.diceHistory.length > 20) {\n        this.diceHistory.shift();\n      }\n    },\n    \n    // 这里已删除重复的handleSkillCheck和handleCharacterUpdate方法\n    // 检测设备类型\n    detectDevice() {\n      this.isMobile = window.innerWidth < 768;\n      console.log('设备检测:', this.isMobile ? '移动设备' : '桌面设备');\n    },\n    // 处理页面关闭前的操作\n    handleBeforeUnload(event) {\n      this.saveNotes();\n      // 提示用户确认离开\n      if (this.hasUnsavedChanges) {\n        event.preventDefault();\n        event.returnValue = '您有未保存的更改，确定要离开吗？';\n        return event.returnValue;\n      }\n    },\n    \n    // 切换全屏模式\n    toggleFullScreen() {\n      try {\n        // 使用.room-container作为全屏目标元素\n        const elem = document.querySelector('.room-container');\n        if (!elem) {\n          console.error('找不到.room-container元素');\n          this.showSystemMessage('全屏切换失败: 找不到目标元素');\n          return;\n        }\n        \n        console.log('开始全屏操作，目标元素:', elem);\n        \n        // 检查当前是否在全屏模式\n        const isFullScreen = \n          document.fullscreenElement || \n          document.webkitFullscreenElement || \n          document.mozFullScreenElement || \n          document.msFullscreenElement;\n        \n        if (!isFullScreen) {\n          // 进入全屏模式\n          console.log('尝试进入全屏模式');\n          \n          // 保存请求全屏前的状态\n          elem.setAttribute('data-was-fullscreen', 'true');\n          \n          // 为目标元素添加全屏样式\n          elem.classList.add('preparing-fullscreen');\n          \n          // 尝试所有可能的全屏API\n          const requestFullScreen = elem.requestFullscreen || \n                                   elem.webkitRequestFullscreen || \n                                   elem.mozRequestFullScreen || \n                                   elem.msRequestFullscreen;\n                                   \n          if (requestFullScreen) {\n            // 使用apply调用适当的方法\n            requestFullScreen.apply(elem).then(() => {\n              console.log('全屏请求成功');\n              elem.classList.add('in-fullscreen');\n              elem.classList.remove('preparing-fullscreen');\n              this.showSystemMessage('已进入全屏模式，再次双击顶部栏或按ESC可退出');\n            }).catch(err => {\n              console.error('全屏请求被拒绝:', err);\n              elem.classList.remove('preparing-fullscreen');\n              this.showSystemMessage('进入全屏失败: ' + err.message);\n            });\n          } else {\n            // 如果浏览器不支持全屏API，尝试使用CSS模拟全屏\n            console.warn('浏览器不支持全屏API，使用CSS模拟全屏');\n            elem.classList.add('in-fullscreen');\n            elem.classList.remove('preparing-fullscreen');\n            document.body.classList.add('fullscreen-mode');\n            this.showSystemMessage('已使用模拟全屏模式，再次双击顶部栏可退出');\n          }\n        } else {\n          // 退出全屏模式\n          console.log('尝试退出全屏模式');\n          \n          // 尝试所有可能的退出全屏API\n          const exitFullScreen = document.exitFullscreen || \n                               document.webkitExitFullscreen || \n                               document.mozCancelFullScreen || \n                               document.msExitFullscreen;\n                               \n          if (exitFullScreen) {\n            exitFullScreen.apply(document).then(() => {\n              console.log('已退出全屏模式');\n              elem.classList.remove('in-fullscreen');\n              this.showSystemMessage('已退出全屏模式');\n            }).catch(err => {\n              console.error('退出全屏失败:', err);\n              this.showSystemMessage('退出全屏失败: ' + err.message);\n            });\n          } else {\n            // 如果使用CSS模拟全屏，则移除相关样式\n            elem.classList.remove('in-fullscreen');\n            document.body.classList.remove('fullscreen-mode');\n            this.showSystemMessage('已退出全屏模式');\n          }\n        }\n      } catch (error) {\n        console.error('全屏切换失败:', error);\n        this.showSystemMessage('全屏切换失败: ' + error.message);\n        \n        // 移除可能残留的全屏状态\n        try {\n          const elem = document.querySelector('.room-container');\n          if (elem) {\n            elem.classList.remove('preparing-fullscreen');\n            elem.classList.remove('in-fullscreen');\n          }\n          document.body.classList.remove('fullscreen-mode');\n        } catch (e) {\n          console.error('清理全屏状态失败:', e);\n        }\n      }\n    },\n    // 处理全屏状态变化\n    handleFullScreenChange() {\n      try {\n        const isFullScreen = !!document.fullscreenElement || \n                            !!document.webkitFullscreenElement || \n                            !!document.mozFullScreenElement || \n                            !!document.msFullscreenElement;\n        \n        const container = document.querySelector('.room-container');\n        if (!container) return;\n        \n        if (isFullScreen) {\n          console.log('已进入全屏模式');\n          // 确保应用全屏样式\n          document.body.classList.add('fullscreen-mode');\n          container.classList.add('in-fullscreen');\n          \n          // 不显示全屏提示\n        } else {\n          console.log('已退出全屏模式');\n          // 移除全屏样式\n          document.body.classList.remove('fullscreen-mode');\n          container.classList.remove('in-fullscreen');\n          \n          // 移除可能存在的全屏提示\n          const notice = container.querySelector('.fullscreen-notice');\n          if (notice) {\n            notice.parentNode.removeChild(notice);\n          }\n        }\n      } catch (error) {\n        console.error('处理全屏状态变化时出错:', error);\n      }\n    },\n    // 处理剧本模式设置相关方法\n    openChatBoxSettings() {\n      // 直接调用ChatBox组件的方法（通过引用）\n      if (this.$refs.chatBox) {\n        this.$refs.chatBox.showAISettings = true;\n      } else {\n        console.warn('找不到ChatBox组件引用');\n      }\n    },\n    \n    handleMemoryCleared() {\n      this.showSystemMessage('AI记忆已清除，将重新开始对话');\n    },\n    \n    handleScriptSettingsSaved(settings) {\n      this.showSystemMessage('剧本模式设置已保存');\n      \n      // 如果有API密钥，更新到全局设置\n      if (settings.apiKey) {\n        this.$store.commit('UPDATE_AI_SETTINGS', {\n          apiKey: settings.apiKey\n        });\n      }\n    },\n    \n    // 打开剧本查看器\n    openScenarioViewer(content, title, fileSize) {\n      this.scenarioContent = content || '';\n      this.scenarioTitle = title || '未命名剧本';\n      this.scenarioFileSize = fileSize || '';\n      this.togglePanel('scenarioViewer');\n    },\n    \n    handleScriptUploaded(fileInfo) {\n      this.showSystemMessage(`剧本文件 ${fileInfo.fileName} 已上传成功`);\n    },\n    \n    // 添加游戏存档相关方法\n    toggleGameSaves() {\n      this.togglePanel('gameSaves');\n    },\n    \n    // 处理存档创建成功\n    handleSaveCreated(save) {\n      this.showSystemMessage(`存档 \"${save.name}\" 创建成功`);\n    },\n    \n    // 处理存档更新成功\n    handleSaveUpdated(save) {\n      this.showSystemMessage(`存档 \"${save.name}\" 更新成功`);\n    },\n    \n    // 处理存档删除成功\n    handleSaveDeleted() {\n      this.showSystemMessage('存档已成功删除');\n    },\n    \n    // 处理自动存档通知\n    handleSaveAutoCreated(save) {\n      this.showSystemMessage(`自动存档已完成`, 'info', 3000);\n    },\n    \n    // 处理存档消息\n    handleSaveMessage(message) {\n      this.showSystemMessage(message);\n    },\n    \n    // 处理存档版本恢复\n    handleSaveRestored(save) {\n      this.showSystemMessage(`存档已恢复到版本 ${save.version}`);\n      // 重新加载存档内容\n      this.handleSaveLoad(save);\n    },\n    \n    // 处理存档预览\n    handleSavePreview(saveData) {\n      // 这里可以实现预览功能，比如临时显示存档内容\n      this.showSystemMessage('正在预览存档历史版本，加载完成后点击\"恢复\"可应用此版本');\n      \n      // 临时存储当前状态\n      this.tempState = {\n        messages: [...this.messages],\n        characters: [...this.characters],\n        scenes: [...this.scenes],\n        currentSceneIndex: this.currentSceneIndex,\n        diceHistory: [...this.diceHistory]\n      };\n      \n      // 临时加载预览内容\n      this.messages = saveData.save_data.messages || [];\n      this.characters = saveData.save_data.characters || [];\n      this.scenes = saveData.save_data.scenes || [];\n      this.currentSceneIndex = saveData.save_data.currentScene || 0;\n      this.diceHistory = saveData.save_data.diceHistory || [];\n      \n      // 设置预览模式\n      this.isPreviewMode = true;\n    },\n    \n    // 退出预览模式\n    exitPreviewMode() {\n      if (!this.isPreviewMode || !this.tempState) return;\n      \n      // 恢复临时存储的状态\n      this.messages = this.tempState.messages;\n      this.characters = this.tempState.characters;\n      this.scenes = this.tempState.scenes;\n      this.currentSceneIndex = this.tempState.currentSceneIndex;\n      this.diceHistory = this.tempState.diceHistory;\n      \n      // 重置预览模式\n      this.isPreviewMode = false;\n      this.tempState = null;\n      \n      this.showSystemMessage('已退出预览模式');\n    },\n    \n    // 处理存档加载\n    async handleSaveLoad(save) {\n      try {\n        // 使用Vuex加载存档\n        await this.$store.dispatch('loadSave', save);\n        \n        // 更新UI状态\n        this.messages = this.$store.state.messages || [];\n        this.characters = this.$store.state.characters || [];\n        this.scenes = this.$store.state.scenes || [];\n        this.currentSceneIndex = this.$store.state.currentSceneIndex || 0;\n        this.diceHistory = this.$store.state.diceHistory || [];\n        \n        // 关闭存档面板\n        this.togglePanel('gameSaves', true);\n        \n        this.showSystemMessage(`存档 \"${save.name}\" 加载成功`);\n      } catch (error) {\n        console.error('加载存档失败:', error);\n        this.showSystemMessage('加载存档失败');\n      }\n    },\n    \n    // 处理存档错误\n    handleSaveError(errorMessage) {\n      this.showSystemMessage(errorMessage);\n    },\n    \n    // 收集游戏状态用于存档\n    collectGameState(customData) {\n      // 添加额外的状态数据\n      customData.roomName = this.room.name;\n      customData.roomDescription = this.room.description;\n      customData.onlineUsers = this.onlineUsers.map(user => ({\n        id: user.id,\n        username: user.username\n      }));\n      \n      // 添加其他需要保存的状态\n    },\n    \n    // 快速存档功能\n    async quickSave() {\n      try {\n        // 获取当前游戏状态\n        const gameState = this.collectGameStateForQuickSave();\n        \n        const saveData = {\n          name: `快速存档 - ${new Date().toLocaleString('zh-CN')}`,\n          description: '自动创建的快速存档',\n          room_id: parseInt(this.internalRoomId),\n          creator_id: this.$store.getters.currentUser?.id || 1,\n          save_data: gameState,\n          thumbnail: null,\n          is_auto_save: false // 快速存档不是自动存档\n        };\n        \n        let response;\n        \n        if (this.quickSaveId) {\n          // 更新现有的快速存档\n          response = await apiService.gameSaves.updateSave(this.quickSaveId, {\n            name: saveData.name,\n            description: saveData.description,\n            save_data: saveData.save_data\n          });\n        } else {\n          // 创建新的快速存档\n          response = await apiService.gameSaves.createSave(saveData);\n        }\n        \n        if (response.data) {\n          this.quickSaveId = response.data.id;\n          this.showSystemMessage('快速存档成功');\n        }\n      } catch (error) {\n        console.error('快速存档失败:', error);\n        this.showSystemMessage('快速存档失败');\n      }\n    },\n    \n    // 快速读档功能\n    async quickLoad() {\n      if (!this.quickSaveId) {\n        this.showSystemMessage('没有可用的快速存档');\n        return;\n      }\n      \n      try {\n        // 获取快速存档数据\n        const response = await apiService.gameSaves.getSave(this.quickSaveId);\n        \n        if (response.data) {\n          // 使用Vuex加载存档\n          await this.$store.dispatch('loadSave', response.data);\n          \n          // 更新UI状态\n          this.messages = this.$store.state.messages || [];\n          this.characters = this.$store.state.characters || [];\n          this.scenes = this.$store.state.scenes || [];\n          this.currentSceneIndex = this.$store.state.currentSceneIndex || 0;\n          this.diceHistory = this.$store.state.diceHistory || [];\n          \n          this.showSystemMessage('快速读档成功');\n        }\n      } catch (error) {\n        console.error('快速读档失败:', error);\n        this.showSystemMessage('快速读档失败');\n      }\n    },\n    \n    // 收集游戏状态用于快速存档\n    collectGameStateForQuickSave() {\n      // 收集当前游戏状态\n      const gameState = {\n        timestamp: Date.now(),\n        messages: this.messages,\n        characters: this.characters,\n        scenes: this.scenes,\n        currentScene: this.currentSceneIndex,\n        diceHistory: this.diceHistory,\n        notes: this.$store.state.notes || {},\n        clues: this.$store.state.clues || [],\n        aiSettings: this.$store.state.aiSettings || {},\n        customData: {\n          roomName: this.room.name,\n          roomDescription: this.room.description,\n          onlineUsers: this.onlineUsers.map(user => ({\n            id: user.id,\n            username: user.username\n          }))\n        }\n      };\n      \n      return gameState;\n    },\n    // 处理键盘快捷键 - 已移除所有快捷键绑定\n    handleKeyDown(event) {\n      // 所有快捷键绑定已移除\n    },\n    // 处理键盘快捷键 - 已移除所有快捷键绑定\n    handleKeyboardShortcuts(event) {\n      // 所有快捷键绑定已移除\n    },\n    // 更新窗口尺寸\n    updateWindowSize() {\n      this.windowWidth = window.innerWidth;\n      this.windowHeight = window.innerHeight;\n    },\n    \n    // 切换存档选项面板\n    toggleSaveOptions() {\n      this.showSaveOptions = !this.showSaveOptions;\n      \n      // 点击其他区域关闭面板\n      if (this.showSaveOptions) {\n        setTimeout(() => {\n          const closePanel = (e) => {\n            const panel = document.querySelector('.save-options-panel');\n            const button = document.querySelector('.toolbar-btn.active');\n            if (panel && !panel.contains(e.target) && (!button || !button.contains(e.target))) {\n              this.showSaveOptions = false;\n              document.removeEventListener('click', closePanel);\n            }\n          };\n          document.addEventListener('click', closePanel);\n        }, 100);\n      }\n    },\n    // 处理查看剧本事件\n    handleViewScenario(data) {\n      this.openScenarioViewer(data.content, data.title, data.fileSize);\n    },\n    \n    // 打开公告展示面板\n    openAnnouncement() {\n      // 如果没有公告内容且是房主，显示默认编辑提示\n      if (!this.announcementContent && this.isRoomOwner) {\n        this.announcementContent = '在此输入公告内容，玩家可以查看和复制这些信息。\\n\\n可以放置：\\n- 规则提示\\n- 背景设定\\n- 重要信息\\n- 其他需要玩家了解的内容';\n      }\n      \n      this.togglePanel('announcement');\n    },\n    \n    // 处理公告保存\n    handleAnnouncementSave(data) {\n      this.announcementContent = data.content;\n      this.announcementTitle = data.title;\n      this.announcementTime = new Date().toLocaleString();\n      \n      // 保存到本地存储\n      this.saveAnnouncementToLocalStorage();\n      \n      // 使用新的方法发送公告更新消息\n      if (websocketService.isConnected) {\n        websocketService.sendAnnouncementUpdate(this.internalRoomId, {\n          content: data.content,\n          title: data.title,\n          timestamp: data.timestamp\n        });\n      }\n      \n      this.showSystemMessage('公告已更新');\n    },\n    \n    // 保存公告到本地存储\n    saveAnnouncementToLocalStorage() {\n      try {\n        const announcementData = {\n          content: this.announcementContent,\n          title: this.announcementTitle,\n          time: this.announcementTime,\n          roomId: this.internalRoomId\n        };\n        \n        this.safeSetJSON(`room_announcement_${this.internalRoomId}`, announcementData);\n      } catch (error) {\n        console.error('保存公告到本地存储失败:', error);\n      }\n    },\n    \n    // 从本地存储加载公告\n    loadAnnouncementFromLocalStorage() {\n      try {\n        const announcementData = this.safeGetJSON(`room_announcement_${this.internalRoomId}`);\n        if (announcementData) {\n          this.announcementContent = announcementData.content || '';\n          this.announcementTitle = announcementData.title || '房间公告';\n          this.announcementTime = announcementData.time || new Date().toLocaleString();\n        }\n      } catch (error) {\n        console.error('从本地存储加载公告失败:', error);\n      }\n    },\n    \n    // 处理WebSocket接收到的公告更新\n    handleAnnouncementUpdate(data) {\n      // 只有非房主才接收更新，房主是发送方\n      if (!this.isRoomOwner) {\n        this.announcementContent = data.content;\n        this.announcementTitle = data.title;\n        this.announcementTime = new Date(data.timestamp).toLocaleString();\n        \n        // 保存到本地存储\n        this.saveAnnouncementToLocalStorage();\n        \n        // 显示通知\n        this.showSystemMessage('房主更新了公告，点击顶部公告按钮查看');\n      }\n    },\n    \n    // 从本地存储加载房间名称\n    loadRoomNameFromLocalStorage() {\n      try {\n        // 确保房间ID是数字\n        const roomId = parseInt(this.internalRoomId) || this.room.id;\n        \n        console.log('尝试从本地存储加载房间名称:', {\n          roomId: roomId,\n          internalRoomId: this.internalRoomId\n        });\n        \n        const storedName = this.safeGetItem(`room_name_${roomId}`);\n        if (storedName) {\n          console.log('找到存储的房间名称:', storedName);\n          this.room.name = storedName;\n          \n          // 同步更新到store\n          if (this.$store.state.rooms && this.$store.state.rooms.length > 0) {\n            this.$store.commit('UPDATE_ROOM_LIST_NAME', { \n              roomId: roomId, \n              name: storedName \n            });\n          }\n        } else {\n          console.log('未找到存储的房间名称');\n        }\n      } catch (error) {\n        console.error('从本地存储加载房间名称失败:', error);\n      }\n    },\n    \n    // 开始编辑房间名称\n    startEditRoomName() {\n      if (!this.isRoomOwner) return;\n      \n      this.isEditingRoomName = true;\n      this.editableRoomName = this.room.name || '';\n      \n      // 等待DOM更新后聚焦输入框\n      this.$nextTick(() => {\n        if (this.$refs.roomNameInput) {\n          this.$refs.roomNameInput.focus();\n        }\n      });\n    },\n    \n    // 保存房间名称\n    async saveRoomName() {\n      if (!this.isRoomOwner) return;\n      \n      const newName = this.editableRoomName.trim();\n      if (!newName) {\n        this.cancelEditRoomName();\n        return;\n      }\n      \n      // 调试信息\n      console.log('保存房间名称:', {\n        roomId: this.internalRoomId,\n        oldName: this.room.name,\n        newName: newName,\n        roomObject: this.room\n      });\n      \n      // 更新房间名称\n      if (newName !== this.room.name) {\n        // 确保房间ID是数字\n        const roomId = parseInt(this.internalRoomId) || this.room.id;\n        \n        // 使用store的action更新房间名称\n        await this.$store.dispatch('updateRoomName', {\n          roomId: roomId,\n          name: newName\n        });\n        \n        // 如果有WebSocket连接，发送房间名称更新\n        if (websocketService.isConnected) {\n          websocketService.sendMessage({\n            type: 'room_name_update',\n            roomId: roomId,\n            name: newName\n          });\n        }\n        \n        this.showSystemMessage('房间名称已更新');\n      }\n      \n      this.isEditingRoomName = false;\n    },\n    \n    // 取消编辑房间名称\n    cancelEditRoomName() {\n      this.isEditingRoomName = false;\n    },\n    \n    // 处理房间名称更新\n    handleRoomNameUpdate(data) {\n      if (!this.isRoomOwner) {\n        // 确保房间ID是数字\n        const roomId = parseInt(this.internalRoomId) || this.room.id;\n        \n        console.log('接收到房间名称更新:', {\n          roomId: roomId,\n          receivedData: data\n        });\n        \n        // 使用store的action更新房间名称\n        this.$store.dispatch('updateRoomName', {\n          roomId: roomId,\n          name: data.name\n        });\n        this.showSystemMessage('房主更新了房间名称');\n      }\n    },\n\n    // 新功能相关方法\n    toggleCombat() {\n      this.togglePanel('combat');\n    },\n\n    toggleSkillCheck() {\n      this.togglePanel('skillCheck');\n    },\n\n    toggleEquipment() {\n      this.togglePanel('equipment');\n    },\n\n    toggleExperiencePack() {\n      this.togglePanel('experiencePack');\n    },\n\n    toggleSpells() {\n      this.togglePanel('spells');\n    },\n\n    toggleMadness() {\n      this.togglePanel('madness');\n    },\n\n    toggleLibrary() {\n      this.togglePanel('library');\n    },\n\n    // 战斗系统事件处理\n    handleCombatStarted() {\n      this.showSystemMessage('战斗开始！');\n    },\n\n    handleCombatEnded() {\n      this.showSystemMessage('战斗结束！');\n    },\n\n    openSkillCheckForCharacter(character) {\n      this.skillCheckCharacter = character;\n      this.skillCheckSkill = '';\n      this.togglePanel('skillCheck', false);\n    },\n\n    // 技能检定事件处理\n    handleSkillCheckResult(result) {\n      const message = `${result.characterName} 进行 ${result.skillName} 检定: ${result.roll}/${result.targetValue} ${this.getSuccessLevelText(result.successLevel)}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleOpposedCheckResult(result) {\n      const message = `对抗检定: ${result.initiator.name}(${result.initiator.roll}) VS ${result.opponent.name}(${result.opponent.roll}) - 胜者: ${result.winner || '平局'}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleSkillGrowth(data) {\n      const message = `${data.character.name} 的 ${data.skill} 技能成长 +${data.improvement}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleGrowthCheckResult(result) {\n      const message = `${result.character} 进行 ${result.skill} 成长检定: ${result.roll}/${result.currentValue} ${result.success ? '成功' : '失败'}${result.success ? ` +${result.improvement}` : ''}`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleSkillCheckCharacterSelected(character) {\n      this.skillCheckCharacter = character;\n    },\n\n    // 装备系统事件处理\n    handleWeaponEquipped(weapon) {\n      this.showSystemMessage(`装备了武器: ${weapon.name}`);\n    },\n\n    handleWeaponUnequipped(weapon) {\n      this.showSystemMessage(`卸下了武器: ${weapon.name}`);\n    },\n\n    handleWeaponUsed(weapon) {\n      this.showSystemMessage(`使用了武器: ${weapon.name}`);\n    },\n\n    handleArmorEquipped(armor) {\n      this.showSystemMessage(`装备了防具: ${armor.name}`);\n    },\n\n    handleArmorUnequipped(armor) {\n      this.showSystemMessage(`卸下了防具: ${armor.name}`);\n    },\n\n    handleItemUsed(item) {\n      this.showSystemMessage(`使用了物品: ${item.name}`);\n    },\n\n    handleItemPurchased(item) {\n      this.showSystemMessage(`购买了物品: ${item.name}`);\n    },\n\n    handleItemDropped(item) {\n      this.showSystemMessage(`丢弃了物品: ${item.name}`);\n    },\n\n    handleHealCharacter(data) {\n      const message = `${data.character.name} 使用 ${data.source} 恢复了 ${data.amount} 点生命值`;\n      this.sendMessage(message, 'system');\n    },\n\n    handleReadBook(data) {\n      const message = `${data.character.name} 阅读了 ${data.book.name}`;\n      this.sendMessage(message, 'system');\n    },\n\n    // 法术系统事件处理\n    handleSpellCast(data) {\n      const message = `${data.caster.name} 施放了 ${data.spell.name}`;\n      this.sendMessage(message, 'system');\n\n      // 如果有目标，添加目标信息\n      if (data.target && data.target !== 'self') {\n        this.sendMessage(`目标: ${data.target}`, 'system');\n      }\n\n      // 如果有描述，添加描述\n      if (data.description) {\n        this.sendMessage(`施法描述: ${data.description}`, 'system');\n      }\n    },\n\n    // 角色更新处理\n    handleCharacterUpdate(character) {\n      // 更新角色数据\n      this.$emit('character-updated', character);\n\n      // 如果是当前选中的角色，更新本地状态\n      if (this.selectedCharacter && this.selectedCharacter.id === character.id) {\n        this.selectedCharacter = character;\n      }\n    },\n\n    // 辅助方法\n    getSuccessLevelText(level) {\n      const levels = {\n        'extreme': '极难成功',\n        'hard': '困难成功',\n        'normal': '常规成功',\n        'failure': '失败'\n      };\n      return levels[level] || '未知';\n    }\n  }\n};\n</script>\n\n<style scoped>\n:root {\n  --base-font-size: 16px;\n}\n\n/* 全屏提示样式 */\n.fullscreen-notice {\n  position: fixed;\n  top: 15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 10px 20px;\n  border-radius: 5px;\n  font-size: 14px;\n  z-index: 10000;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);\n  animation: fadeInOut 3s forwards;\n  pointer-events: none;\n}\n\n@keyframes fadeInOut {\n  0% { opacity: 0; }\n  10% { opacity: 1; }\n  80% { opacity: 1; }\n  100% { opacity: 0; }\n}\n\n/* 准备全屏的过渡样式 */\n.preparing-fullscreen {\n  transition: all 0.3s ease-out;\n}\n\n.room-container {\n  display: flex;\n  flex-direction: column;\n  height: calc(100vh - 80px);\n  padding: 10px;\n  overflow: hidden;\n  font-size: var(--base-font-size);\n}\n\n.room-header {\n  margin-bottom: 10px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 12px;\n  background-color: rgba(40, 44, 52, 0.8);\n  border-radius: 6px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  cursor: pointer;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.room-header:hover {\n  background-color: rgba(50, 55, 65, 0.9);\n}\n\n.room-header:hover::after {\n  content: \"\";\n  display: none;\n}\n\n.room-container.in-fullscreen .room-header:hover::after {\n  content: \"\";\n  display: none;\n}\n\n.room-title {\n  display: flex;\n  align-items: center;\n}\n\n.room-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.room-action-btn {\n  padding: 5px 10px;\n  border-radius: 4px;\n  border: none;\n  background-color: #444;\n  color: #e0e0e0;\n  cursor: pointer;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  margin-left: 5px;\n  min-width: 80px;\n  justify-content: center;\n}\n\n.room-action-btn:hover {\n  background-color: #555;\n}\n\n.ai-settings-btn {\n  background-color: #2c3e50;\n}\n\n.ai-settings-btn:hover {\n  background-color: #34495e;\n}\n\n.room-header h2 {\n  margin: 0;\n  color: #e0e0e0;\n  font-size: clamp(1.2rem, 3vw, 1.5rem);\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.room-header h2.editable {\n  cursor: pointer;\n  padding: 2px 6px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.room-header h2.editable:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.room-header h2 .edit-icon {\n  font-size: 0.8em;\n  opacity: 0.6;\n}\n\n.room-header h2.editable:hover .edit-icon {\n  opacity: 1;\n}\n\n.room-name-input {\n  background-color: rgba(0, 0, 0, 0.3);\n  border: 1px solid #555;\n  color: #e0e0e0;\n  font-size: clamp(1.2rem, 3vw, 1.5rem);\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-weight: bold;\n  width: 100%;\n  max-width: 300px;\n}\n\n.room-participants {\n  font-size: 0.8rem;\n  color: #b0b0b0;\n  padding: 3px 6px;\n  background: rgba(255,255,255,0.1);\n  border-radius: 10px;\n}\n\n.room-header p {\n  color: #b0b0b0;\n  margin: 0 0 8px;\n  font-size: 0.9rem;\n}\n\n.room-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n}\n\n.panel-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow: auto;\n  min-height: 400px;\n  height: calc(100vh - 220px);\n}\n\n.online-users {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 6px;\n  margin-bottom: 8px;\n  padding: 6px;\n  background: rgba(255,255,255,0.05);\n  border-radius: 4px;\n}\n\n.user-badge {\n  background: #3498db;\n  color: white;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-size: 0.75rem;\n}\n\n.character-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n  gap: 15px;\n  padding: 15px;\n}\n\n.character-item {\n  background: #333333;\n  border: 1px solid #444;\n  border-radius: 8px;\n  padding: 15px;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.character-item:hover {\n  background: #3a3a3a;\n  transform: translateY(-2px);\n}\n\n.character-item h4 {\n  margin: 0 0 5px;\n  color: #e0e0e0;\n}\n\n.character-item p {\n  margin: 0;\n  color: #b0b0b0;\n  font-size: 0.9em;\n}\n\n/* 设置模态框样式 */\n.settings-content {\n  padding: 20px;\n}\n\n.setting-group {\n  margin-bottom: 20px;\n}\n\n.setting-group label {\n  display: block;\n  margin-bottom: 10px;\n  color: #e0e0e0;\n  font-weight: 500;\n}\n\n.font-size-controls {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.font-btn {\n  background: #3498db;\n  color: white;\n  border: none;\n  width: 40px;\n  height: 40px;\n  border-radius: 4px;\n  font-weight: bold;\n  font-size: 1.2rem;\n  cursor: pointer;\n}\n\n.current-size {\n  font-weight: 500;\n  color: #e0e0e0;\n}\n\n.toggle-switch {\n  margin-bottom: 10px;\n  display: flex;\n  align-items: center;\n}\n\n.toggle-switch input {\n  margin-right: 10px;\n  width: 18px;\n  height: 18px;\n  cursor: pointer;\n}\n\n.toggle-switch label {\n  display: inline;\n  margin: 0;\n  cursor: pointer;\n}\n\n.setting-actions {\n  text-align: right;\n  margin-top: 20px;\n}\n\n.save-settings-btn {\n  background-color: #3498db;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n/* 深色/浅色模式支持 */\n:root {\n  --text-color: #e0e0e0;\n  --bg-color: #2a2a2a;\n  --secondary-bg: #333333;\n  --border-color: #444;\n  --highlight-color: #3498db;\n}\n\n.dark-mode .room-container {\n  --text-color: #e0e0e0;\n  --bg-color: #2a2a2a;\n  --secondary-bg: #333333;\n  --border-color: #444;\n}\n\n/* 媒体查询 - 响应式布局 */\n@media (max-width: 768px) {\n  .room-header h2 {\n    font-size: 1.2rem;\n  }\n  \n  .room-container {\n    padding: 5px;\n    height: calc(100vh - 60px);\n  }\n  \n  .room-header {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .room-title {\n    margin-bottom: 10px;\n    width: 100%;\n  }\n  \n  .room-actions {\n    width: 100%;\n    justify-content: flex-start;\n  }\n  \n  .control-btn {\n    padding: 5px 6px;\n    font-size: 0.8rem;\n    min-width: 60px;\n  }\n  \n  .room-controls {\n    overflow-x: auto;\n    padding-bottom: 5px;\n    -webkit-overflow-scrolling: touch;\n    scroll-snap-type: x mandatory;\n    white-space: nowrap;\n    width: 100%;\n  }\n  \n  .panel-content {\n    height: calc(100vh - 180px);\n  }\n  \n  .message-input textarea {\n    height: 40px;\n  }\n  \n  .btn-text {\n    display: none;\n  }\n  \n  .toggle-btn {\n    min-width: 40px;\n  }\n  \n  .room-header:hover::after {\n    right: 150px;\n  }\n}\n\n.room-controls {\n  display: flex;\n  gap: 8px;\n  margin-top: 8px;\n  margin-bottom: 10px;\n  flex-wrap: wrap;\n}\n\n.control-btn {\n  background: rgba(255,255,255,0.1);\n  border: none;\n  color: #e0e0e0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: background 0.2s;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n  white-space: nowrap;\n  min-width: 80px;\n  justify-content: center;\n}\n\n.control-btn:hover {\n  background: rgba(255,255,255,0.2);\n}\n\n.script-btn {\n  background-color: rgba(142, 68, 173, 0.2);\n  border: 1px solid #8e44ad;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.script-btn:hover {\n  background-color: rgba(142, 68, 173, 0.3);\n}\n\n.script-btn i {\n  font-size: 0.9rem;\n}\n\n.ai-config-alert {\n  background-color: rgba(255, 193, 7, 0.1);\n  border-left: 4px solid #ffc107;\n  margin-bottom: 10px;\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.ai-config-alert .alert-content {\n  padding: 10px 15px;\n  position: relative;\n  font-size: 0.9rem;\n}\n\n.ai-config-alert p {\n  margin: 5px 0;\n}\n\n.ai-config-alert .link-button {\n  background: none;\n  border: none;\n  color: #3498db;\n  padding: 0;\n  text-decoration: underline;\n  cursor: pointer;\n}\n\n.ai-config-alert .close-button {\n  position: absolute;\n  top: 5px;\n  right: 5px;\n  background: none;\n  border: none;\n  font-size: 1.2rem;\n  cursor: pointer;\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.ai-config-alert .close-button:hover {\n  opacity: 1;\n}\n\n/* 添加场景相关样式 */\n.main-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.scene-area {\n  flex: 1;\n  min-height: 300px;\n  margin-bottom: 15px;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n}\n\n.message-area {\n  flex: 1;\n  min-height: 300px;\n}\n\n/* 在大屏幕上使用水平布局 */\n@media (min-width: 1024px) {\n  .main-content {\n    flex-direction: row;\n    gap: 15px;\n  }\n  \n  .scene-area {\n    flex: 1;\n    margin-bottom: 0;\n  }\n  \n  .message-area {\n    flex: 1;\n  }\n}\n\n.room-toolbar {\n  position: fixed;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n  background-color: rgba(52, 152, 219, 0.95);\n  border-radius: 0 10px 10px 0;\n  padding: 20px 15px 20px 20px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(5px);\n  border-right: 5px solid #2ecc71;\n  border-left: none;\n}\n\n/* 修改侧边栏样式 */\n.room-toolbar {\n  background-color: #2980b9;\n  border-radius: 0 10px 10px 0;\n  padding: 15px;\n  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.5);\n  border-right: 3px solid #3498db;\n  width: 85px;\n  position: fixed;\n  top: 50%;\n  left: 0;\n  transform: translateY(-50%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  z-index: 9999;\n  transition: all 0.3s ease;\n}\n\n.room-toolbar.collapsed {\n  left: -85px;\n}\n\n.toolbar-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n  transition: all 0.3s ease;\n  width: 100%;\n  align-items: center;\n}\n\n/* 工具组样式 */\n.toolbar-group {\n  background-color: #2c3e50;\n  border-radius: 8px;\n  margin-bottom: 10px;\n  padding: 10px;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  width: 65px;\n}\n\n.toolbar-group:last-child {\n  border-bottom: none;\n}\n\n.collapse-btn {\n  background: #1a5276;\n  border: 2px solid #ffffff;\n  color: #ffffff;\n  cursor: pointer;\n  font-size: 1.2rem;\n  transition: all 0.3s;\n  position: absolute;\n  right: -30px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 30px;\n  height: 80px;\n  border-radius: 0 5px 5px 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);\n  animation: pulse-left 2s infinite;\n}\n\n@keyframes pulse-left {\n  0% {\n    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);\n  }\n  50% {\n    box-shadow: 3px 0 15px rgba(46, 204, 113, 0.7);\n  }\n  100% {\n    box-shadow: 3px 0 10px rgba(0, 0, 0, 0.3);\n  }\n}\n\n.collapse-btn:hover {\n  background-color: #2980b9;\n  transform: translateY(-50%) scale(1.1);\n}\n\n.room-toolbar.collapsed .collapse-btn {\n  right: 0;\n  left: auto;\n  border-right: none;\n  border-radius: 0;\n  animation: none;\n}\n\n.toolbar-btn {\n  width: 45px;\n  height: 45px;\n  border-radius: 6px;\n  background-color: #2c3e50;\n  border: none;\n  color: #e0e0e0;\n  font-size: 1.2rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n.toolbar-btn:hover {\n  background-color: #2980b9;\n  transform: scale(1.1);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n}\n\n.toolbar-btn.active {\n  background-color: #16a085;\n  box-shadow: 0 0 12px rgba(22, 160, 133, 0.7);\n  border-color: #2ecc71;\n}\n\n.toolbar-btn:active {\n  transform: scale(0.95);\n}\n\n.tooltip {\n  position: absolute;\n  left: 60px;\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 0.9rem;\n  opacity: 0;\n  transition: opacity 0.2s, transform 0.2s;\n  pointer-events: none;\n  white-space: nowrap;\n  transform: translateX(-10px);\n  font-weight: 500;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);\n  z-index: 10001;\n}\n\n.toolbar-btn:hover .tooltip {\n  opacity: 1;\n  transform: translateX(0);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .room-toolbar {\n    left: 0;\n    right: 0;\n    top: auto;\n    bottom: 0;\n    transform: none;\n    width: 100%;\n    border-radius: 8px 8px 0 0;\n    padding: 10px;\n    justify-content: center;\n    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);\n    flex-direction: row;\n    border-right: none;\n    border-left: none;\n    border-top: 5px solid #2ecc71;\n  }\n  \n  .room-toolbar.collapsed {\n    bottom: -70px;\n    left: 0;\n    right: 0;\n  }\n  \n  .collapse-btn {\n    top: -30px;\n    left: 50%;\n    right: auto;\n    transform: translateX(-50%);\n    width: 80px;\n    height: 30px;\n    border-radius: 5px 5px 0 0;\n    animation: pulse-mobile 2s infinite;\n  }\n  \n  @keyframes pulse-mobile {\n    0% {\n      box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);\n    }\n    50% {\n      box-shadow: 0 -3px 15px rgba(46, 204, 113, 0.7);\n    }\n    100% {\n      box-shadow: 0 -3px 10px rgba(0, 0, 0, 0.3);\n    }\n  }\n  \n  .collapse-btn:hover {\n    transform: translateX(-50%) scale(1.1);\n  }\n  \n  .room-toolbar.collapsed .collapse-btn {\n    top: 0;\n    left: 50%;\n    right: auto;\n    animation: none;\n    border-bottom: none;\n    border-right: 2px solid #ffffff;\n  }\n  \n  .toolbar-buttons {\n    flex-direction: row;\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n  \n  .toolbar-group {\n    flex-direction: row;\n    padding: 0;\n    border-bottom: none;\n    border-right: 1px solid rgba(255, 255, 255, 0.2);\n    padding-right: 10px;\n    margin-right: 10px;\n    width: auto;\n    gap: 10px;\n  }\n  \n  .toolbar-group:last-child {\n    border-right: none;\n    padding-right: 0;\n    margin-right: 0;\n  }\n  \n  .toolbar-btn {\n    width: 36px;\n    height: 36px;\n    font-size: 1rem;\n    margin: 0;\n  }\n  \n  .tooltip {\n    left: 50%;\n    transform: translateX(-50%);\n    bottom: 45px;\n    top: auto;\n  }\n}\n\n/* 全屏模式样式 */\n.fullscreen-mode .room-container,\n.room-container.in-fullscreen {\n  height: 100vh !important;\n  padding: 10px;\n  box-sizing: border-box;\n  width: 100vw !important;\n  max-width: 100vw !important;\n  overflow: auto;\n  z-index: 9999;\n  background-color: var(--bg-color, #2a2a2a);\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  margin: 0;\n}\n\n.fullscreen-mode .room-header,\n.room-container.in-fullscreen .room-header {\n  background-color: rgba(30, 33, 40, 0.95);\n  position: sticky;\n  top: 0;\n  z-index: 1000;\n}\n\n.fullscreen-mode .panel-content,\n.room-container.in-fullscreen .panel-content {\n  height: calc(100vh - 150px);\n}\n\n/* 在全屏模式下特别突出显示双击提示 */\n.room-container.in-fullscreen .room-header:hover::after {\n  content: \"\";\n  display: none;\n}\n\n@keyframes pulse {\n  0% { opacity: 0.8; }\n  50% { opacity: 1; }\n  100% { opacity: 0.8; }\n}\n\n.toggle-btn {\n  min-width: 80px;\n}\n\n.btn-text {\n  margin-left: 5px;\n}\n\n@media (max-width: 768px) {\n  .btn-text {\n    display: none;\n  }\n  \n  .toggle-btn {\n    min-width: 40px;\n  }\n  \n  .room-header:hover::after {\n    right: 150px;\n  }\n}\n\n.control-btn.save-btn {\n  background-color: #4caf50;\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.control-btn.save-btn:hover {\n  background-color: #45a049;\n}\n\n.control-btn.load-btn {\n  background-color: #2196f3;\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.control-btn.load-btn:hover {\n  background-color: #0b7dda;\n}\n\n.preview-mode-indicator {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background-color: rgba(76, 175, 80, 0.9);\n  color: white;\n  padding: 8px 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  z-index: 1000;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n}\n\n.preview-info {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: bold;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.exit-preview-btn {\n  background-color: white;\n  color: #4caf50;\n  border: none;\n  border-radius: 4px;\n  padding: 4px 10px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.exit-preview-btn:hover {\n  background-color: #f5f5f5;\n}\n\n.toolbar-buttons {\n  display: flex;\n  gap: 10px;\n}\n\n.toolbar-btn {\n  background-color: #333;\n  color: #e0e0e0;\n  border: none;\n  border-radius: 4px;\n  padding: 6px 12px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 0.9rem;\n  transition: background-color 0.2s;\n}\n\n.toolbar-btn:hover {\n  background-color: #444;\n}\n\n.toolbar-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.toolbar-btn i {\n  font-size: 1rem;\n}\n\n/* 修改按钮样式 */\n.toolbar-btn {\n  width: 40px;\n  height: 40px;\n  border-radius: 4px;\n  background-color: #333;\n  border: none;\n  color: #e0e0e0;\n  font-size: 1.1rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: all 0.3s;\n  position: relative;\n  margin: 0;\n}\n\n.toolbar-btn:hover {\n  background-color: #2980b9;\n  transform: scale(1.1);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\n}\n\n.toolbar-btn.active {\n  background-color: #16a085;\n  box-shadow: 0 0 12px rgba(22, 160, 133, 0.7);\n  border-color: #2ecc71;\n}\n\n/* 存档选项面板样式 */\n.save-options-panel {\n  position: fixed;\n  left: 95px;\n  top: 50%;\n  transform: translateY(-50%);\n  background-color: #2c3e50;\n  border-radius: 8px;\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);\n  z-index: 9998;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  padding: 10px;\n  width: 180px;\n}\n\n.save-options-panel::before {\n  content: \"\";\n  position: absolute;\n  left: -10px;\n  top: 50%;\n  transform: translateY(-50%);\n  border-width: 10px 10px 10px 0;\n  border-style: solid;\n  border-color: transparent #2c3e50 transparent transparent;\n}\n\n.save-options-content {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.save-option {\n  display: flex;\n  align-items: center;\n  padding: 10px;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  position: relative;\n}\n\n.save-option:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.save-option i {\n  font-size: 1.2rem;\n  margin-right: 12px;\n  width: 20px;\n  text-align: center;\n}\n\n.save-option span {\n  flex: 1;\n  font-size: 0.95rem;\n}\n\n.save-option small {\n  font-size: 0.75rem;\n  opacity: 0.7;\n  position: absolute;\n  right: 10px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n.save-option.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.user-role-indicator {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #e0e0e0;\n  font-size: 0.95rem;\n  position: absolute;\n  top: 15px;\n  left: 15px;\n  background-color: rgba(44, 62, 80, 0.9);\n  padding: 8px 15px;\n  border-radius: 6px;\n  font-weight: 600;\n  z-index: 1000;\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(5px);\n  transition: all 0.3s ease;\n}\n\n.user-role-indicator:hover {\n  background-color: rgba(52, 73, 94, 0.95);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.5);\n}\n\n.user-role-indicator i {\n  font-size: 1.3rem;\n}\n\n.user-role-indicator i.fa-crown {\n  color: #f1c40f;\n  text-shadow: 0 0 5px rgba(241, 196, 15, 0.5);\n}\n\n.user-role-indicator i.fa-user {\n  color: #3498db;\n  text-shadow: 0 0 5px rgba(52, 152, 219, 0.5);\n}\n\n.room-action-btn.announcement-btn {\n  background-color: #4caf50;\n  color: white;\n}\n\n.room-action-btn.announcement-btn:hover {\n  background-color: #388e3c;\n}\n\n.room-action-btn.announcement-btn i {\n  color: white;\n}\n\n\n\n</style> "], "mappings": ";;;;;;;EACO,SAAM;AAAgB;;EAEpB,SAAM;AAAqB;;EAOzB,SAAM;AAAY;;;EAGG,SAAM;;;;EAYxB,SAAM;;;EAIT,SAAM;AAAc;;EAOf,SAAM;AAAU;;EAWvB,SAAM;AAAe;;EAClB,SAAM;AAAmB;;EAwB5B,SAAM;AAAc;;EAGlB,SAAM;AAAc;;EA6QpB,SAAM;AAAiB;;EAErB,SAAM;AAAe;;EAuBrB,SAAM;AAAe;;EAkBrB,SAAM;AAAe;;EAkBrB,SAAM;AAAe;;;EAuBrB,SAAM;;;EAYN,SAAM;AAAe;;EA+DvB,SAAM;AAAgB;;;EAkBtB,SAAM;AAAkB;;EACtB,SAAM;AAAe;;EAEnB,SAAM;AAAoB;;EAEvB,SAAM;AAAc;;EAKzB,SAAM;AAAe;;EAEnB,SAAM;AAAe;;EAIrB,SAAM;AAAe;;EAMvB,SAAM;AAAiB;;;EA2F3B,SAAM;;;EAKJ,SAAM;AAAiB;;;EAQzB,SAAM;;;EACJ,SAAM;AAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;uBAvoBrCA,mBAAA,CAypBM,OAzpBNC,UAypBM,GAxpBJC,mBAAA,cAAiB,EACjBC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAAiE;IAA9D,SAAKE,eAAA,EAAC,KAAK,EAASC,KAAA,CAAAC,WAAW;2BAClCJ,mBAAA,CAA4C,cAAAK,gBAAA,CAAnCF,KAAA,CAAAC,WAAW,+B,GAGtBL,mBAAA,kBAAqB,EACrBC,mBAAA,CAoCM;IApCD,SAAM,aAAa;IAAEM,UAAQ,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;MAClDX,mBAAA,CAkBM,OAlBNY,UAkBM,G,CAjBOT,KAAA,CAAAU,iBAAiB,KAAKV,KAAA,CAAAC,WAAW,I,cAA5CP,mBAAA,CAGK;;IAH0CiB,OAAK,EAAAP,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAO,iBAAA,IAAAP,QAAA,CAAAO,iBAAA,CAAAL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAiB;IAAA;IAAG,SAAKT,eAAA;MAAA,YAAgBC,KAAA,CAAAC;IAAW;wCACrGD,KAAA,CAAAa,IAAI,CAACC,IAAI,gBAAe,GAC3B,iBAASd,KAAA,CAAAC,WAAW,I,cAApBP,mBAAA,CAAwD,KAAxDqB,UAAwD,K,qFAE1DrB,mBAAA,CASE;;IAPAsB,IAAI,EAAC,MAAM;;aACFhB,KAAA,CAAAiB,gBAAgB,GAAAC,MAAA;IAAA;IACxBC,MAAI,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAe,YAAA,IAAAf,QAAA,CAAAe,YAAA,CAAAb,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;IAAA;IAClBa,OAAK,G;aAAQhB,QAAA,CAAAe,YAAA,IAAAf,QAAA,CAAAe,YAAA,CAAAb,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;IAAA,gB;aACdH,QAAA,CAAAiB,kBAAA,IAAAjB,QAAA,CAAAiB,kBAAA,CAAAf,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAkB;IAAA,a;IAC9Be,GAAG,EAAC,eAAe;IACnB,SAAM;kEALGvB,KAAA,CAAAiB,gBAAgB,E,GAOWjB,KAAA,CAAAwB,WAAW,CAACC,MAAM,Q,cAAxD/B,mBAAA,CAEO,QAFPgC,UAEO,EAAAxB,gBAAA,CADFF,KAAA,CAAAwB,WAAW,CAACC,MAAM,IAAG,OAC1B,mB,qCAEF5B,mBAAA,CAeM,OAfN8B,UAeM,GAdJ9B,mBAAA,CAGS;IAHD,SAAM,kCAAkC;IAAEc,OAAK,EAAAP,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAuB,gBAAA,IAAAvB,QAAA,CAAAuB,gBAAA,CAAArB,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;IAAEqB,KAAK,EAAC;kCAC/EhC,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAAgC;IAA1B,SAAM;EAAU,GAAC,IAAE,mB,IAE3BA,mBAAA,CAGS;IAHD,SAAM,4BAA4B;IAAEc,OAAK,EAAAP,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAyB,aAAA,IAAAzB,QAAA,CAAAyB,aAAA,CAAAvB,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;MAC9DX,mBAAA,CAAoF;IAAhF,SAAKE,eAAA,CAAEC,KAAA,CAAA+B,kBAAkB;2BAC7BlC,mBAAA,CAAoE,QAApEmC,UAAoE,EAAA9B,gBAAA,CAA1CF,KAAA,CAAA+B,kBAAkB,+B,GAE9ClC,mBAAA,CAES;IAFD,SAAM,iBAAiB;IAAEc,OAAK,EAAAP,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAC,gBAAA,IAAAD,QAAA,CAAAC,gBAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;IAAEqB,KAAK,EAAC;kCAC9DhC,mBAAA,CAA6B;IAA1B,SAAM;EAAe,0B,IAE1BA,mBAAA,CAES;IAFD,SAAM,iBAAiB;IAAEc,OAAK,EAAAP,MAAA,QAAAA,MAAA,gBAAAc,MAAA;MAAA,OAAElB,KAAA,CAAAiC,iBAAiB;IAAA;IAASJ,KAAK,EAAC;kCACtEhC,mBAAA,CAA0B;IAAvB,SAAM;EAAY,0B,iCAK3BA,mBAAA,CAuBM,OAvBNqC,UAuBM,GAtBJrC,mBAAA,CAAsE,QAAtEsC,UAAsE,EAAtC,KAAG,GAAAjC,gBAAA,CAAGF,KAAA,CAAAwB,WAAW,CAACC,MAAM,IAAG,MAAI,iBAC/D5B,mBAAA,CAES;IAFAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA+B,cAAA,IAAA/B,QAAA,CAAA+B,cAAA,CAAA7B,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAc;IAAA;IAAE,SAAM;sBACjCH,QAAA,CAAAgC,UAAU,oCAGfzC,mBAAA,gBAAmB,EACLI,KAAA,CAAAsC,aAAa,iB,cAA3B5C,mBAAA,CAGS;;IAHkCiB,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAkC,mBAAA,IAAAlC,QAAA,CAAAkC,mBAAA,CAAAhC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAmB;IAAA;IAAE,SAAM;kCAC3EX,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,2B,iBAAK,UAElC,E,yCAEAD,mBAAA,cAAiB,EACHI,KAAA,CAAAC,WAAW,I,cAAzBP,mBAAA,CAGS;;IAHmBiB,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAmC,SAAA,IAAAnC,QAAA,CAAAmC,SAAA,CAAAjC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;IAAE,SAAM;kCAClDX,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2B,iBAAK,QAE7B,E,yCAEAD,mBAAA,cAAiB,EACHI,KAAA,CAAAC,WAAW,IAAII,QAAA,CAAAoC,YAAY,I,cAAzC/C,mBAAA,CAGS;;IAHmCiB,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAqC,SAAA,IAAArC,QAAA,CAAAqC,SAAA,CAAAnC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;IAAE,SAAM;kCAClEX,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,2B,iBAAK,QAEpC,E,2CAGFA,mBAAA,CAiBM,OAjBN8C,WAiBM,GAfJ/C,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbN+C,WAaM,GAZJC,YAAA,CAWEC,mBAAA;IAVAvB,GAAG,EAAC,SAAS;IACZwB,QAAQ,EAAE/C,KAAA,CAAA+C,QAAQ;IAClBC,WAAW,EAAEhD,KAAA,CAAAgD,WAAW;IACxBC,KAAK,EAAEjD,KAAA,CAAAiD,KAAK;IACZC,SAAS,EAAElD,KAAA,CAAAkD,SAAS;IACpBC,YAAY,EAAE,EAAAC,WAAA,GAAApD,KAAA,CAAAa,IAAI,cAAAuC,WAAA,uBAAJA,WAAA,CAAMC,SAAS;IAC7BC,aAAY,EAAEjD,QAAA,CAAAkD,WAAW;IACzBC,UAAS,EAAExD,KAAA,CAAAyD,QAAQ;IACnBC,cAAa,EAAE1D,KAAA,CAAA2D,YAAY;IAC3BC,cAAa,EAAEvD,QAAA,CAAAwD;qKAKtBjE,mBAAA,UAAa,EAELI,KAAA,CAAA8D,aAAa,CAACC,SAAS,I,cAD/BC,YAAA,CAWEC,mCAAA;;IATCF,SAAS,EAAE/D,KAAA,CAAAkE,iBAAiB;IAC5B,WAAS,EAAE,EAAE;IACb,WAAS,EAAE,GAAG;IACdC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;IAClBC,YAAW,EAAEhE,QAAA,CAAAiE,gBAAgB;IAC7BC,iBAAgB,EAAAnE,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAElB,KAAA,CAAAwE,kBAAkB;IAAA;IACpCC,cAAa,EAAEpE,QAAA,CAAAqE,kBAAkB;IACjCC,iBAAgB,EAAEtE,QAAA,CAAAuE,qBAAqB;IACvCC,eAAc,EAAExE,QAAA,CAAAyE;yJAIX9E,KAAA,CAAA8D,aAAa,CAACiB,IAAI,I,cAD1Bf,YAAA,CAQEgB,+BAAA;;IANC,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,cAAY,EAAEhF,KAAA,CAAAiF,WAAW;IACzBd,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;IAClBc,MAAI,EAAE7E,QAAA,CAAA8E,UAAU;IAChBd,YAAW,EAAEhE,QAAA,CAAAiE;4GAIRtE,KAAA,CAAAoF,SAAS,I,cADjBpB,YAAA,CAOEqB,yBAAA;;IALC,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,cAAY,EAAErF,KAAA,CAAAsF,kBAAkB;IAChCnB,OAAK,EAAE9D,QAAA,CAAAkF,WAAW;IAClBC,WAAU,EAAEnF,QAAA,CAAAoF;4GAGf7F,mBAAA,UAAa,EAELI,KAAA,CAAA8D,aAAa,CAAC4B,SAAS,IAAI1F,KAAA,CAAAsC,aAAa,iB,cADhD0B,YAAA,CAYiB2B,yBAAA;;IAVf9D,KAAK,EAAC,OAAO;IACZ,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAAiE,CAAjEvB,YAAA,CAAiE+C,qBAAA;QAApD,SAAO,EAAE5F,KAAA,CAAA6F,cAAc;QAAG,SAAO,EAAE7F,KAAA,CAAAsC;;;;2CAGlD1C,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAACgC,GAAG,I,cADzB9B,YAAA,CAYiB2B,yBAAA;;IAVf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAAwC,CAAxCvB,YAAA,CAAwCkD,qBAAA;QAA3B,SAAO,EAAE/F,KAAA,CAAA6F;MAAc,qC;;;2CAGtCjG,mBAAA,WAAc,EAENI,KAAA,CAAA8D,aAAa,CAACkC,SAAS,I,cAD/BhC,YAAA,CAYiB2B,yBAAA;;IAVf9D,KAAK,EAAC,KAAK;IACV,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAAwC,CAAxCvB,YAAA,CAAwCoD,qBAAA;QAA3B,SAAO,EAAEjG,KAAA,CAAA6F;MAAc,qC;;;2CAGtCjG,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAACoC,MAAM,I,cAD5BlC,YAAA,CAgBiB2B,yBAAA;;IAdf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAIE,CAJFvB,YAAA,CAIEsD,wBAAA;QAHCC,eAAc,EAAE/F,QAAA,CAAAgG,mBAAmB;QACnCC,aAAY,EAAEjG,QAAA,CAAAkG,iBAAiB;QAC/BC,gBAAgB,EAAEnG,QAAA,CAAAoG;;;;2CAIvB7G,mBAAA,cAAiB,EAETI,KAAA,CAAA8D,aAAa,CAAC4C,UAAU,I,cADhC1C,YAAA,CAqBiB2B,yBAAA;;IAnBf9D,KAAK,EAAC,QAAQ;IACb,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OASE,CATFvB,YAAA,CASE8D,6BAAA;QARC,mBAAiB,EAAE3G,KAAA,CAAA4G,mBAAmB;QACtC,eAAa,EAAE5G,KAAA,CAAA6G,eAAe;QAC9B1C,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;UAAA,OAAEb,QAAA,CAAA+D,WAAW;QAAA;QAClB0C,kBAAkB,EAAEzG,QAAA,CAAA0G,sBAAsB;QAC1CC,oBAAoB,EAAE3G,QAAA,CAAA4G,wBAAwB;QAC9CC,aAAY,EAAE7G,QAAA,CAAA8G,iBAAiB;QAC/BC,mBAAmB,EAAE/G,QAAA,CAAAgH,uBAAuB;QAC5CC,mBAAkB,EAAEjH,QAAA,CAAAkH;;;;2CAIzB3H,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAAC0D,SAAS,I,cAD/BxD,YAAA,CAuBiB2B,yBAAA;;IArBf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAWE,CAXFvB,YAAA,CAWE4E,2BAAA;QAVCC,gBAAe,EAAErH,QAAA,CAAAsH,oBAAoB;QACrCC,kBAAiB,EAAEvH,QAAA,CAAAwH,sBAAsB;QACzCC,YAAW,EAAEzH,QAAA,CAAA0H,gBAAgB;QAC7BC,eAAc,EAAE3H,QAAA,CAAA4H,mBAAmB;QACnCC,iBAAgB,EAAE7H,QAAA,CAAA8H,qBAAqB;QACvCC,UAAS,EAAE/H,QAAA,CAAAgI,cAAc;QACzBC,eAAc,EAAEjI,QAAA,CAAAkI,mBAAmB;QACnCC,aAAY,EAAEnI,QAAA,CAAAoI,iBAAiB;QAC/BC,eAAc,EAAErI,QAAA,CAAAsI,mBAAmB;QACnCC,UAAS,EAAEvI,QAAA,CAAAwI;;;;2CAIhBjJ,mBAAA,aAAgB,EAERI,KAAA,CAAA8D,aAAa,CAACgF,cAAc,I,cADpC9E,YAAA,CAciB2B,yBAAA;;IAZf9D,KAAK,EAAC,OAAO;IACZ,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAEE,CAFFvB,YAAA,CAEEkG,iCAAA;QADCC,kBAAiB,EAAE3I,QAAA,CAAAuE;MAAqB,gD;;;2CAI7ChF,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAACmF,MAAM,I,cAD5BjF,YAAA,CAeiB2B,yBAAA;;IAbf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAGE,CAHFvB,YAAA,CAGEqG,uBAAA;QAFCF,kBAAiB,EAAE3I,QAAA,CAAAuE,qBAAqB;QACxCuE,WAAU,EAAE9I,QAAA,CAAA+I;;;;2CAIjBxJ,mBAAA,cAAiB,EAETI,KAAA,CAAA8D,aAAa,CAACuF,OAAO,I,cAD7BrF,YAAA,CAciB2B,yBAAA;;IAZf9D,KAAK,EAAC,QAAQ;IACb,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAEE,CAFFvB,YAAA,CAEEyG,yBAAA;QADCN,kBAAiB,EAAE3I,QAAA,CAAAuE;MAAqB,gD;;;2CAI7ChF,mBAAA,eAAkB,EAEVI,KAAA,CAAA8D,aAAa,CAACyF,OAAO,I,cAD7BvF,YAAA,CAciB2B,yBAAA;;IAZf9D,KAAK,EAAC,SAAS;IACd,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,EAAE;IACb,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAEE,CAFFvB,YAAA,CAEE2G,yBAAA;QADCR,kBAAiB,EAAE3I,QAAA,CAAAuE;MAAqB,gD;;;2CAI7ChF,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAAC2F,WAAW,IAAIzJ,KAAA,CAAAC,WAAW,I,cADhD+D,YAAA,CAYiB2B,yBAAA;;IAVf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAA6D,CAA7DvB,YAAA,CAA6D6G,uBAAA;QAA9CC,MAAM,EAAE3J,KAAA,CAAA6F,cAAc;QAAG+D,IAAI,EAAE5J,KAAA,CAAAC;;;;2CAGhDL,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAAC+F,SAAS,I,cAD/B7F,YAAA,CAkBiB2B,yBAAA;;IAhBf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,IAAA0F,qBAAA;MAAA,OAME,CANFjH,YAAA,CAMEkH,qBAAA;QALCJ,MAAM,EAAE3J,KAAA,CAAA6F,cAAc;QACtBmE,QAAQ,EAAE,EAAAF,qBAAA,GAAAG,IAAA,CAAAC,MAAM,CAACC,OAAO,CAACnH,WAAW,cAAA8G,qBAAA,uBAA1BA,qBAAA,CAA4BE,QAAQ;QAC9CJ,IAAI,EAAE5J,KAAA,CAAA4J,IAAI;QACVQ,gBAAe,EAAE/J,QAAA,CAAAgK,oBAAoB;QACrCC,mBAAkB,EAAEjK,QAAA,CAAAkK;;;;2CAIzB1K,mBAAA,CAuIM;IAvID,SAAKE,eAAA,EAAC,cAAc;MAAA,aAAwBC,KAAA,CAAA+B;IAAkB;MACjElC,mBAAA,CA6HM,OA7HN2K,WA6HM,GA5HJ5K,mBAAA,YAAe,EACfC,mBAAA,CAoBM,OApBN4K,WAoBM,GAnBJ5K,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAqK,gBAAA,IAAArK,QAAA,CAAAqK,gBAAA,CAAAnK,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA2K;IAAc;kCACtF9K,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAuK,oBAAA,IAAAvK,QAAA,CAAAuK,oBAAA,CAAArK,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAoB;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA6K;IAAkB;kCAC9FhL,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAgC;IAA1B,SAAM;EAAS,GAAC,KAAG,mB,mBAG3BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAyK,gBAAA,IAAAzK,QAAA,CAAAyK,gBAAA,CAAAvK,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAgB;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAAC4C;IAAU;kCAChG7G,mBAAA,CAA0B;IAAvB,SAAM;EAAY,2BACrBA,mBAAA,CAAiC;IAA3B,SAAM;EAAS,GAAC,MAAI,mB,mBAG5BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAkF,WAAA,IAAAlF,QAAA,CAAAkF,WAAA,CAAAhF,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAW;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAAoF;IAAS;kCAC5EvF,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,2BAC7BA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,qBAI5BD,mBAAA,aAAgB,EAChBC,mBAAA,CAeM,OAfNkL,WAeM,GAdJlL,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA2K,YAAA,IAAA3K,QAAA,CAAA2K,YAAA,CAAAzK,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACoC;IAAM;kCACxFrG,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA4K,eAAA,IAAA5K,QAAA,CAAA4K,eAAA,CAAA1K,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAAC0D;IAAS;kCAC9F3H,mBAAA,CAAiC;IAA9B,SAAM;EAAmB,2BAC5BA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA6K,oBAAA,IAAA7K,QAAA,CAAA6K,oBAAA,CAAA3K,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAoB;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACgF;IAAc;kCACxGjJ,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAgC;IAA1B,SAAM;EAAS,GAAC,KAAG,mB,qBAI7BD,mBAAA,aAAgB,EAChBC,mBAAA,CAeM,OAfNsL,WAeM,GAdJtL,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA+K,YAAA,IAAA/K,QAAA,CAAA+K,YAAA,CAAA7K,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACmF;IAAM;kCACxFpJ,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAgL,aAAA,IAAAhL,QAAA,CAAAgL,aAAA,CAAA9K,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACuF;IAAO;kCAC1FxJ,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAiL,aAAA,IAAAjL,QAAA,CAAAiL,aAAA,CAAA/K,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACyF;IAAO;kCAC1F1J,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,2BAC3BA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,qBAI5BD,mBAAA,aAAgB,EAChBC,mBAAA,CAoBM,OApBN0L,WAoBM,GAnBJ1L,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAmL,SAAA,IAAAnL,QAAA,CAAAmL,SAAA,CAAAjL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACgC;IAAG;kCAClFjG,mBAAA,CAA0B;IAAvB,SAAM;EAAY,2BACrBA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAGS;IAHAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAoL,eAAA,IAAApL,QAAA,CAAAoL,eAAA,CAAAlL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAAE,SAAKT,eAAA,EAAC,aAAa;MAAA,UAAqBC,KAAA,CAAA8D,aAAa,CAACkC;IAAS;kCAC9FnG,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,2BAC3BA,mBAAA,CAAgC;IAA1B,SAAM;EAAS,GAAC,KAAG,mB,mBAInBG,KAAA,CAAAC,WAAW,I,cADnBP,mBAAA,CAQS;;IANNiB,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAqL,iBAAA,IAAArL,QAAA,CAAAqL,iBAAA,CAAAnL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAiB;IAAA;IACzB,SAAKT,eAAA,EAAC,aAAa;MAAA,UACCC,KAAA,CAAA8D,aAAa,CAAC2F;IAAW;kCAE7C5J,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,0DAI5BD,mBAAA,YAAe,EACkBI,KAAA,CAAAC,WAAW,I,cAA5CP,mBAAA,CASM,OATNiM,WASM,GARJ9L,mBAAA,CAOS;IANNc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAuL,iBAAA,IAAAvL,QAAA,CAAAuL,iBAAA,CAAArL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAiB;IAAA;IACzB,SAAKT,eAAA,EAAC,aAAa;MAAA,UACCC,KAAA,CAAA6L;IAAe;kCAEnChM,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiC;IAA3B,SAAM;EAAS,GAAC,MAAI,mB,0DAI9BD,mBAAA,UAAa,EACbC,mBAAA,CA4BM,OA5BNiM,WA4BM,GA3BJjM,mBAAA,CAOS;IANNc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA0L,eAAA,IAAA1L,QAAA,CAAA0L,eAAA,CAAAxL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IACvB,SAAKT,eAAA,EAAC,aAAa;MAAA,UACCC,KAAA,CAAA8D,aAAa,CAAC+F;IAAS;kCAE3ChK,mBAAA,CAAiC;IAA9B,SAAM;EAAmB,2BAC5BA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAG1BA,mBAAA,CAOS;IANNc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA2L,iBAAA,IAAA3L,QAAA,CAAA2L,iBAAA,CAAAzL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAiB;IAAA;IACzB,SAAKT,eAAA,EAAC,aAAa;MAAA,UACCC,KAAA,CAAA8D,aAAa,CAACmI;IAAW;kCAE7CpM,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,mBAIlBG,KAAA,CAAAsC,aAAa,iB,cADrB5C,mBAAA,CAQS;;IANNiB,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA6L,eAAA,IAAA7L,QAAA,CAAA6L,eAAA,CAAA3L,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IACvB,SAAKT,eAAA,EAAC,aAAa;MAAA,UACCC,KAAA,CAAA8D,aAAa,CAAC4B;IAAS;kCAE3C7F,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAA+B;IAAzB,SAAM;EAAS,GAAC,IAAE,mB,4DAK9BD,mBAAA,UAAa,EACbC,mBAAA,CAKS;IALAc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAyB,aAAA,IAAAzB,QAAA,CAAAyB,aAAA,CAAAvB,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;IAAE,SAAM;MACnCX,mBAAA,CAGO;IAHH,SAAKE,eAAA,E,OAAgCC,KAAA,CAAA+B,kBAAkB,0C;8CAO/DnC,mBAAA,UAAa,EAELI,KAAA,CAAA8D,aAAa,CAACmI,WAAW,I,cADjCjI,YAAA,CAYiB2B,yBAAA;;IAVf9D,KAAK,EAAC,IAAI;IACT,WAAS,EAAE7B,KAAA,CAAAmM,WAAW;IACtB,WAAS,EAAEnM,KAAA,CAAAoM,YAAY;IACvB,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfjI,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAAoD,CAApDvB,YAAA,CAAoDwJ,+BAAA;QAA7B,cAAY,EAAErM,KAAA,CAAAwB;MAAW,0C;;;sFAGlD5B,mBAAA,WAAc,EACdA,mBAAA,2FAA0F,EAE1FA,mBAAA,aAAgB,EACHI,KAAA,CAAAwE,kBAAkB,I,cAA/BR,YAAA,CAeQsI,gBAAA;;IAf0BnI,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAElB,KAAA,CAAAwE,kBAAkB;IAAA;;IAC9C+H,MAAM,EAAAC,QAAA,CACf;MAAA,OAAapM,MAAA,SAAAA,MAAA,QAAbP,mBAAA,CAAa,YAAT,MAAI,mB;;wBAEV;MAAA,OAUM,CAVNA,mBAAA,CAUM,OAVN4M,WAUM,I,kBATJ/M,mBAAA,CAQMgN,SAAA,QAAAC,WAAA,CAPgB3M,KAAA,CAAA4M,UAAU,YAAvB7I,SAAS;6BADlBrE,mBAAA,CAQM;UANHmN,GAAG,EAAE9I,SAAS,CAAC+I,EAAE;UACjBnM,OAAK,WAALA,OAAKA,CAAAO,MAAA;YAAA,OAAEb,QAAA,CAAA0M,eAAe,CAAChJ,SAAS;UAAA;UACjC,SAAM;YAENlE,mBAAA,CAA6B,YAAAK,gBAAA,CAAtB6D,SAAS,CAACjD,IAAI,kBACrBjB,mBAAA,CAAiC,WAAAK,gBAAA,CAA3B6D,SAAS,CAACiJ,UAAU,iB;;;;2CAKhCpN,mBAAA,WAAc,EACDI,KAAA,CAAAiC,iBAAiB,I,cAA9B+B,YAAA,CA8BQsI,gBAAA;;IA9ByBnI,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAElB,KAAA,CAAAiC,iBAAiB;IAAA;;IAC5CsK,MAAM,EAAAC,QAAA,CACf;MAAA,OAAapM,MAAA,SAAAA,MAAA,QAAbP,mBAAA,CAAa,YAAT,MAAI,mB;;wBAEV;MAAA,OAyBM,CAzBNA,mBAAA,CAyBM,OAzBNoN,WAyBM,GAxBJpN,mBAAA,CAOM,OAPNqN,WAOM,G,4BANJrN,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAIM,OAJNsN,WAIM,GAHJtN,mBAAA,CAAgE;QAAvDc,OAAK,EAAAP,MAAA,SAAAA,MAAA,iBAAAc,MAAA;UAAA,OAAEb,QAAA,CAAA+M,cAAc;QAAA;QAAM,SAAM;SAAW,IAAE,GACvDvN,mBAAA,CAAkD,QAAlDwN,WAAkD,EAAAnN,gBAAA,CAApBF,KAAA,CAAAsN,QAAQ,IAAG,IAAE,iBAC3CzN,mBAAA,CAA+D;QAAtDc,OAAK,EAAAP,MAAA,SAAAA,MAAA,iBAAAc,MAAA;UAAA,OAAEb,QAAA,CAAA+M,cAAc;QAAA;QAAK,SAAM;SAAW,IAAE,E,KAI1DvN,mBAAA,CAUM,OAVN0N,WAUM,G,4BATJ1N,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAGM,OAHN2N,WAGM,G,gBAFJ3N,mBAAA,CAA6D;QAAtDmB,IAAI,EAAC,UAAU;QAAC8L,EAAE,EAAC,aAAa;;iBAAU9M,KAAA,CAAAyN,UAAU,GAAAvM,MAAA;QAAA;yDAAVlB,KAAA,CAAAyN,UAAU,E,+BAC3D5N,mBAAA,CAAqC;QAA9B,OAAI;MAAa,GAAC,MAAI,oB,GAE/BA,mBAAA,CAGM,OAHN6N,WAGM,G,gBAFJ7N,mBAAA,CAAqE;QAA9DmB,IAAI,EAAC,UAAU;QAAC8L,EAAE,EAAC,cAAc;;iBAAU9M,KAAA,CAAA2N,iBAAiB,GAAAzM,MAAA;QAAA;yDAAjBlB,KAAA,CAAA2N,iBAAiB,E,+BACnE9N,mBAAA,CAAsC;QAA/B,OAAI;MAAc,GAAC,MAAI,oB,KAIlCA,mBAAA,CAEM,OAFN+N,WAEM,GADJ/N,mBAAA,CAAqE;QAA5Dc,OAAK,EAAAP,MAAA,SAAAA,MAAA;UAAA,OAAEC,QAAA,CAAAwN,YAAA,IAAAxN,QAAA,CAAAwN,YAAA,CAAAtN,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAY;QAAA;QAAE,SAAM;SAAoB,MAAI,E;;;2CAKlEZ,mBAAA,0BAA6B,EAE7BA,mBAAA,cAAiB,EAETI,KAAA,CAAA8D,aAAa,CAACgK,UAAU,IAAI9N,KAAA,CAAAC,WAAW,I,cAD/C+D,YAAA,CAciB2B,yBAAA;;IAZf9D,KAAK,EAAC,QAAQ;IACb,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnBsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAIE,CAJFvB,YAAA,CAIEkL,6BAAA;QAHC,SAAO,EAAE9D,IAAA,CAAAN,MAAM;QACf,eAAa,EAAE3J,KAAA,CAAAC,WAAW;QAC1B+N,iBAAgB,EAAE3N,QAAA,CAAA4N;;;;2CAIvBrO,mBAAA,cAAiB,EAETI,KAAA,CAAA8D,aAAa,CAACoK,SAAS,IAAIlO,KAAA,CAAAC,WAAW,I,cAD9C+D,YAAA,CAsBiB2B,yBAAA;;IApBf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnBsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAYE,CAZFvB,YAAA,CAYEsL,4BAAA;QAXC,SAAO,EAAEnO,KAAA,CAAA6F,cAAc;QACvBuI,aAAY,EAAE/N,QAAA,CAAAgO,iBAAiB;QAC/BC,aAAY,EAAEjO,QAAA,CAAAkO,iBAAiB;QAC/BC,aAAY,EAAEnO,QAAA,CAAAoO,iBAAiB;QAC/BC,UAAS,EAAErO,QAAA,CAAAsO,cAAc;QACzBC,WAAU,EAAEvO,QAAA,CAAAwO,eAAe;QAC3BC,cAAa,EAAEzO,QAAA,CAAA0O,gBAAgB;QAC/BC,iBAAiB,EAAE3O,QAAA,CAAA4O,qBAAqB;QACxCC,aAAY,EAAE7O,QAAA,CAAA8O,iBAAiB;QAC/BC,cAAa,EAAE/O,QAAA,CAAAgP,kBAAkB;QACjCC,aAAY,EAAEjP,QAAA,CAAAkP;;;;2CAInB3P,mBAAA,aAAgB,EAERI,KAAA,CAAA8D,aAAa,CAAC0L,cAAc,IAAIxP,KAAA,CAAAyP,eAAe,I,cADvDzL,YAAA,CAgBiB2B,yBAAA;;IAdf9D,KAAK,EAAC,OAAO;IACZ,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAIE,CAJFvB,YAAA,CAIE6M,0BAAA;QAHC,kBAAgB,EAAE1P,KAAA,CAAAyP,eAAe;QACjC,gBAAc,EAAEzP,KAAA,CAAA2P,aAAa;QAC7B,WAAS,EAAE3P,KAAA,CAAA4P;;;;2CAIhBhQ,mBAAA,YAAe,EAEPI,KAAA,CAAA8D,aAAa,CAAC+L,YAAY,I,cADlC7L,YAAA,CAkBiB2B,yBAAA;;IAhBf9D,KAAK,EAAC,MAAM;IACX,WAAS,EAAE,GAAG;IACd,WAAS,EAAE,GAAG;IACd,eAAa,EAAE,GAAG;IAClB,gBAAc,EAAE,GAAG;IACnB,WAAS,EAAE,GAAG;IACd,YAAU,EAAE,GAAG;IACfsC,OAAK,EAAA/D,MAAA,SAAAA,MAAA,iBAAAc,MAAA;MAAA,OAAEb,QAAA,CAAA+D,WAAW;IAAA;;wBAEnB;MAAA,OAME,CANFvB,YAAA,CAMEiN,8BAAA;QALCC,OAAO,EAAE/P,KAAA,CAAAgQ,mBAAmB;QAC5BnO,KAAK,EAAE7B,KAAA,CAAAiQ,iBAAiB;QACxB,cAAY,EAAEjQ,KAAA,CAAAkQ,gBAAgB;QAC9B,aAAW,EAAElQ,KAAA,CAAAC,WAAW;QACxBkQ,MAAI,EAAE9P,QAAA,CAAA+P;;;;2CAIXxQ,mBAAA,kBAAqB,EACqBI,KAAA,CAAAqQ,aAAa,I,cAAvD3Q,mBAAA,CAUM,OAVN4Q,WAUM,G,4BATJzQ,mBAAA,CAGM;IAHD,SAAM;EAAc,IACvBA,mBAAA,CAA0B;IAAvB,SAAM;EAAY,IACrBA,mBAAA,CAAiB,cAAX,MAAI,E,qBAEZA,mBAAA,CAIM,OAJN0Q,WAIM,GAHJ1Q,mBAAA,CAES;IAFD,SAAM,kBAAkB;IAAEc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAmQ,eAAA,IAAAnQ,QAAA,CAAAmQ,eAAA,CAAAjQ,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;kCACtDX,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2B,iBAAK,QAC9B,E,6CAIJD,mBAAA,YAAe,EACuBI,KAAA,CAAA6L,eAAe,I,cAArDnM,mBAAA,CAkBM,OAlBN+Q,WAkBM,GAjBJ5Q,mBAAA,CAgBM,OAhBN6Q,WAgBM,GAfJ7Q,mBAAA,CAIM;IAJD,SAAM,aAAa;IAAEc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAmC,SAAA,IAAAnC,QAAA,CAAAmC,SAAA,CAAAjC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;kCACxCX,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,oBACVA,mBAAA,CAAqB,eAAd,QAAM,mB,IAEfA,mBAAA,CAIM;IAJD,SAAKE,eAAA,EAAC,aAAa;MAAA,aAA2CC,KAAA,CAAA2Q;IAAW;IAApDhQ,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAqC,SAAA,IAAArC,QAAA,CAAAqC,SAAA,CAAAnC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;kCACxCX,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,2BAC7BA,mBAAA,CAAiB,cAAX,MAAI,oBACVA,mBAAA,CAAqB,eAAd,QAAM,mB,mBAEfA,mBAAA,CAIM;IAJD,SAAM,aAAa;IAAEc,OAAK,EAAAP,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAuQ,eAAA,IAAAvQ,QAAA,CAAAuQ,eAAA,CAAArQ,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;kCAC9CX,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2BACzBA,mBAAA,CAAiB,cAAX,MAAI,oBACVA,mBAAA,CAAqB,eAAd,QAAM,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}