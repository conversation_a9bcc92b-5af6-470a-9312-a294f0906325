# 旧战斗系统安全清理方案

## 🔍 需要清理的旧文件

### 1. 旧的房间组件（可以安全删除）
```
7.26/coc_trpgs/frontend/src/views/Room.vue                    ❌ 旧版本，已被替换
7.26/coc_trpgs/frontend/src/views/GameRoom.vue               ❌ 旧版本，已被替换
```

### 2. 旧的战斗组件（需要谨慎处理）
```
7.26/coc_trpgs/frontend/src/components/combat/CombatField.vue ❌ 旧版本，功能已集成到新系统
7.26/coc_trpgs/frontend/src/components/combat/CharacterCard.vue ❌ 旧版本，已被CharacterToken.vue替换
```

### 3. 保留的文件（不要删除）
```
7.26/coc_trpgs/frontend/src/components/combat/CombatLog.vue                ✅ 保留，新系统使用
7.26/coc_trpgs/frontend/src/components/combat/InitiativeTracker.vue        ✅ 保留，新系统使用
7.26/coc_trpgs/frontend/src/components/combat/KeeperCombatPanel.vue        ✅ 保留，新系统使用
7.26/coc_trpgs/frontend/src/components/combat/ForcedCombatMode.vue         ✅ 保留，新系统使用
7.26/coc_trpgs/frontend/src/components/combat/PlayerCombatInterface.vue    ✅ 保留，新系统使用
7.26/coc_trpgs/frontend/src/services/combatWebSocket.js                    ✅ 保留，新系统使用
7.26/coc_trpgs/backend/combat_data_sync.py                                 ✅ 保留，后端服务
7.26/coc_trpgs/backend/combat_database_schema.sql                          ✅ 保留，数据库结构
```

## 🚀 执行清理