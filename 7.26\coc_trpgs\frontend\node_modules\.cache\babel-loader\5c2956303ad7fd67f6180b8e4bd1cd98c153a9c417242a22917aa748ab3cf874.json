{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, vModelSelect as _vModelSelect, withModifiers as _withModifiers, normalizeStyle as _normalizeStyle, resolveComponent as _resolveComponent, createVNode as _createVNode, createStaticVNode as _createStaticVNode } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"character-manager\"\n};\nvar _hoisted_2 = {\n  \"class\": \"page-header\"\n};\nvar _hoisted_3 = {\n  \"class\": \"header-content\"\n};\nvar _hoisted_4 = {\n  \"class\": \"header-actions\"\n};\nvar _hoisted_5 = {\n  \"class\": \"view-controls\"\n};\nvar _hoisted_6 = {\n  \"class\": \"search-box\"\n};\nvar _hoisted_7 = {\n  key: 0,\n  \"class\": \"stats-section\"\n};\nvar _hoisted_8 = {\n  \"class\": \"stats-grid\"\n};\nvar _hoisted_9 = {\n  \"class\": \"stat-card\"\n};\nvar _hoisted_10 = {\n  \"class\": \"stat-content\"\n};\nvar _hoisted_11 = {\n  \"class\": \"stat-number\"\n};\nvar _hoisted_12 = {\n  \"class\": \"stat-card\"\n};\nvar _hoisted_13 = {\n  \"class\": \"stat-content\"\n};\nvar _hoisted_14 = {\n  \"class\": \"stat-number\"\n};\nvar _hoisted_15 = {\n  \"class\": \"stat-card\"\n};\nvar _hoisted_16 = {\n  \"class\": \"stat-content\"\n};\nvar _hoisted_17 = {\n  \"class\": \"stat-number\"\n};\nvar _hoisted_18 = {\n  \"class\": \"stat-card\"\n};\nvar _hoisted_19 = {\n  \"class\": \"stat-content\"\n};\nvar _hoisted_20 = {\n  \"class\": \"stat-number\"\n};\nvar _hoisted_21 = {\n  key: 1,\n  \"class\": \"filter-section\"\n};\nvar _hoisted_22 = {\n  \"class\": \"filter-controls\"\n};\nvar _hoisted_23 = {\n  \"class\": \"filter-group\"\n};\nvar _hoisted_24 = [\"value\"];\nvar _hoisted_25 = {\n  \"class\": \"filter-group\"\n};\nvar _hoisted_26 = {\n  \"class\": \"filter-group\"\n};\nvar _hoisted_27 = {\n  key: 2,\n  \"class\": \"loading-state\"\n};\nvar _hoisted_28 = {\n  \"class\": \"empty-state\"\n};\nvar _hoisted_29 = {\n  \"class\": \"characters-section\"\n};\nvar _hoisted_30 = {\n  key: 0,\n  \"class\": \"character-grid\"\n};\nvar _hoisted_31 = [\"onClick\"];\nvar _hoisted_32 = {\n  \"class\": \"character-avatar-section\"\n};\nvar _hoisted_33 = {\n  \"class\": \"character-avatar\"\n};\nvar _hoisted_34 = [\"src\", \"alt\"];\nvar _hoisted_35 = [\"onClick\"];\nvar _hoisted_36 = {\n  \"class\": \"character-basic-info\"\n};\nvar _hoisted_37 = {\n  \"class\": \"character-name\"\n};\nvar _hoisted_38 = {\n  \"class\": \"character-meta\"\n};\nvar _hoisted_39 = {\n  \"class\": \"character-occupation\"\n};\nvar _hoisted_40 = {\n  \"class\": \"character-age\"\n};\nvar _hoisted_41 = {\n  key: 0,\n  \"class\": \"character-attributes\"\n};\nvar _hoisted_42 = {\n  \"class\": \"attribute-row\"\n};\nvar _hoisted_43 = {\n  \"class\": \"attribute-item\"\n};\nvar _hoisted_44 = {\n  \"class\": \"attr-value\"\n};\nvar _hoisted_45 = {\n  \"class\": \"attribute-item\"\n};\nvar _hoisted_46 = {\n  \"class\": \"attr-value\"\n};\nvar _hoisted_47 = {\n  \"class\": \"attribute-item\"\n};\nvar _hoisted_48 = {\n  \"class\": \"attr-value\"\n};\nvar _hoisted_49 = {\n  \"class\": \"attribute-row\"\n};\nvar _hoisted_50 = {\n  \"class\": \"attribute-item\"\n};\nvar _hoisted_51 = {\n  \"class\": \"attr-value\"\n};\nvar _hoisted_52 = {\n  \"class\": \"attribute-item\"\n};\nvar _hoisted_53 = {\n  \"class\": \"attr-value\"\n};\nvar _hoisted_54 = {\n  \"class\": \"attribute-item\"\n};\nvar _hoisted_55 = {\n  \"class\": \"attr-value\"\n};\nvar _hoisted_56 = {\n  \"class\": \"character-vitals\"\n};\nvar _hoisted_57 = {\n  \"class\": \"vital-item\"\n};\nvar _hoisted_58 = {\n  \"class\": \"vital-bar\"\n};\nvar _hoisted_59 = {\n  \"class\": \"vital-text\"\n};\nvar _hoisted_60 = {\n  \"class\": \"vital-item\"\n};\nvar _hoisted_61 = {\n  \"class\": \"vital-bar\"\n};\nvar _hoisted_62 = {\n  \"class\": \"vital-text\"\n};\nvar _hoisted_63 = {\n  \"class\": \"character-actions\"\n};\nvar _hoisted_64 = [\"onClick\"];\nvar _hoisted_65 = [\"onClick\"];\nvar _hoisted_66 = [\"onClick\"];\nvar _hoisted_67 = [\"onClick\"];\nvar _hoisted_68 = {\n  \"class\": \"character-list\"\n};\nvar _hoisted_69 = [\"onClick\"];\nvar _hoisted_70 = {\n  \"class\": \"list-col name\"\n};\nvar _hoisted_71 = {\n  \"class\": \"character-name-cell\"\n};\nvar _hoisted_72 = [\"src\", \"alt\"];\nvar _hoisted_73 = {\n  \"class\": \"character-name\"\n};\nvar _hoisted_74 = {\n  \"class\": \"character-id\"\n};\nvar _hoisted_75 = {\n  \"class\": \"list-col occupation\"\n};\nvar _hoisted_76 = {\n  \"class\": \"list-col age\"\n};\nvar _hoisted_77 = {\n  \"class\": \"list-col vitals\"\n};\nvar _hoisted_78 = {\n  \"class\": \"vitals-cell\"\n};\nvar _hoisted_79 = {\n  \"class\": \"vital-mini\"\n};\nvar _hoisted_80 = {\n  \"class\": \"vital-value\"\n};\nvar _hoisted_81 = {\n  \"class\": \"vital-mini\"\n};\nvar _hoisted_82 = {\n  \"class\": \"vital-value\"\n};\nvar _hoisted_83 = {\n  \"class\": \"list-col status\"\n};\nvar _hoisted_84 = {\n  \"class\": \"list-col actions\"\n};\nvar _hoisted_85 = {\n  \"class\": \"list-actions\"\n};\nvar _hoisted_86 = [\"onClick\"];\nvar _hoisted_87 = [\"onClick\"];\nvar _hoisted_88 = [\"onClick\"];\nvar _hoisted_89 = [\"onClick\"];\nvar _hoisted_90 = {\n  \"class\": \"character-edit-modal-header\"\n};\nvar _hoisted_91 = {\n  \"class\": \"character-edit-modal-body\"\n};\nvar _hoisted_92 = {\n  \"class\": \"character-view-modal-header\"\n};\nvar _hoisted_93 = {\n  \"class\": \"modal-title-section\"\n};\nvar _hoisted_94 = {\n  \"class\": \"character-avatar-small\"\n};\nvar _hoisted_95 = [\"src\", \"alt\"];\nvar _hoisted_96 = {\n  \"class\": \"character-title-info\"\n};\nvar _hoisted_97 = {\n  \"class\": \"modal-title\"\n};\nvar _hoisted_98 = {\n  \"class\": \"character-subtitle\"\n};\nvar _hoisted_99 = {\n  \"class\": \"modal-actions\"\n};\nvar _hoisted_100 = {\n  \"class\": \"character-view-modal-body\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$data$selectedCharac, _$data$selectedCharac2, _$data$selectedCharac3, _$data$selectedCharac4;\n  var _component_character_form = _resolveComponent(\"character-form\");\n  var _component_character_detail_view = _resolveComponent(\"character-detail-view\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 页面头部 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    \"class\": \"header-left\"\n  }, [_createElementVNode(\"h1\", {\n    \"class\": \"page-title\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-user-friends\"\n  }), _createElementVNode(\"span\", null, \"角色管理\")]), _createElementVNode(\"p\", {\n    \"class\": \"page-subtitle\"\n  }, \"管理您的COC角色，创建新的冒险者\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $data.viewMode = 'grid';\n    }),\n    \"class\": _normalizeClass([\"view-btn\", {\n      'active': $data.viewMode === 'grid'\n    }]),\n    title: \"网格视图\"\n  }, _cache[17] || (_cache[17] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-th\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $data.viewMode = 'list';\n    }),\n    \"class\": _normalizeClass([\"view-btn\", {\n      'active': $data.viewMode === 'list'\n    }]),\n    title: \"列表视图\"\n  }, _cache[18] || (_cache[18] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-list\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_6, [_cache[19] || (_cache[19] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-search search-icon\"\n  }, null, -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n      return $data.searchQuery = $event;\n    }),\n    type: \"text\",\n    placeholder: \"搜索角色...\",\n    \"class\": \"search-input\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, $data.searchQuery]])]), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = function () {\n      return $options.createCharacter && $options.createCharacter.apply($options, arguments);\n    }),\n    \"class\": \"create-btn primary\"\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"创建角色\", -1 /* CACHED */)]))])])]), _createCommentVNode(\" 统计信息 \"), $data.characters.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[23] || (_cache[23] = _createElementVNode(\"div\", {\n    \"class\": \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-users\"\n  })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, _toDisplayString($data.characters.length), 1 /* TEXT */), _cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n    \"class\": \"stat-label\"\n  }, \"总角色数\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_12, [_cache[25] || (_cache[25] = _createElementVNode(\"div\", {\n    \"class\": \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-heart\"\n  })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($options.aliveCharacters), 1 /* TEXT */), _cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n    \"class\": \"stat-label\"\n  }, \"存活角色\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_15, [_cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n    \"class\": \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-star\"\n  })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($options.favoriteCharacters), 1 /* TEXT */), _cache[26] || (_cache[26] = _createElementVNode(\"div\", {\n    \"class\": \"stat-label\"\n  }, \"收藏角色\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_18, [_cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n    \"class\": \"stat-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-clock\"\n  })], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, _toDisplayString($options.recentCharacters), 1 /* TEXT */), _cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n    \"class\": \"stat-label\"\n  }, \"最近使用\", -1 /* CACHED */))])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 筛选和排序 \"), $data.characters.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", _hoisted_23, [_cache[31] || (_cache[31] = _createElementVNode(\"label\", {\n    \"class\": \"filter-label\"\n  }, \"职业筛选:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $data.selectedOccupation = $event;\n    }),\n    \"class\": \"filter-select\"\n  }, [_cache[30] || (_cache[30] = _createElementVNode(\"option\", {\n    value: \"\"\n  }, \"全部职业\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.occupations, function (occupation) {\n    return _openBlock(), _createElementBlock(\"option\", {\n      key: occupation,\n      value: occupation\n    }, _toDisplayString(occupation), 9 /* TEXT, PROPS */, _hoisted_24);\n  }), 128 /* KEYED_FRAGMENT */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.selectedOccupation]])]), _createElementVNode(\"div\", _hoisted_25, [_cache[33] || (_cache[33] = _createElementVNode(\"label\", {\n    \"class\": \"filter-label\"\n  }, \"状态筛选:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n      return $data.selectedStatus = $event;\n    }),\n    \"class\": \"filter-select\"\n  }, _cache[32] || (_cache[32] = [_createElementVNode(\"option\", {\n    value: \"\"\n  }, \"全部状态\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"alive\"\n  }, \"存活\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"dead\"\n  }, \"死亡\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"insane\"\n  }, \"疯狂\", -1 /* CACHED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.selectedStatus]])]), _createElementVNode(\"div\", _hoisted_26, [_cache[35] || (_cache[35] = _createElementVNode(\"label\", {\n    \"class\": \"filter-label\"\n  }, \"排序方式:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n      return $data.sortBy = $event;\n    }),\n    \"class\": \"filter-select\"\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"option\", {\n    value: \"name\"\n  }, \"按名称\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"created\"\n  }, \"按创建时间\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"updated\"\n  }, \"按更新时间\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"level\"\n  }, \"按等级\", -1 /* CACHED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, $data.sortBy]])])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载状态 \"), $data.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, _cache[36] || (_cache[36] = [_createElementVNode(\"div\", {\n    \"class\": \"loading-spinner\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"p\", null, \"正在加载角色数据...\", -1 /* CACHED */)]))) : $data.characters.length === 0 ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createCommentVNode(\" 空状态 \"), _createElementVNode(\"div\", _hoisted_28, [_cache[38] || (_cache[38] = _createElementVNode(\"div\", {\n    \"class\": \"empty-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-user-plus\"\n  })], -1 /* CACHED */)), _cache[39] || (_cache[39] = _createElementVNode(\"h3\", {\n    \"class\": \"empty-title\"\n  }, \"还没有角色\", -1 /* CACHED */)), _cache[40] || (_cache[40] = _createElementVNode(\"p\", {\n    \"class\": \"empty-description\"\n  }, \"创建您的第一个COC角色，开始您的克苏鲁冒险之旅\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[7] || (_cache[7] = function () {\n      return $options.createCharacter && $options.createCharacter.apply($options, arguments);\n    }),\n    \"class\": \"create-btn primary large\"\n  }, _cache[37] || (_cache[37] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"创建第一个角色\", -1 /* CACHED */)]))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 4\n  }, [_createCommentVNode(\" 角色列表 \"), _createElementVNode(\"div\", _hoisted_29, [_createCommentVNode(\" 网格视图 \"), $data.viewMode === 'grid' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredCharacters, function (character) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: character.id,\n      \"class\": _normalizeClass([\"character-card\", {\n        'favorite': character.is_favorite\n      }]),\n      onClick: function onClick($event) {\n        return $options.selectCharacter(character);\n      }\n    }, [_createCommentVNode(\" 角色头像和状态 \"), _createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"div\", _hoisted_33, [_createElementVNode(\"img\", {\n      src: $options.getCharacterAvatar(character),\n      alt: character.name\n    }, null, 8 /* PROPS */, _hoisted_34), _createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"character-status-indicator\", character.status])\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.toggleFavorite(character);\n      }, [\"stop\"]),\n      \"class\": _normalizeClass([\"favorite-btn\", {\n        'active': character.is_favorite\n      }])\n    }, _toConsumableArray(_cache[41] || (_cache[41] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-star\"\n    }, null, -1 /* CACHED */)])), 10 /* CLASS, PROPS */, _hoisted_35)]), _createCommentVNode(\" 角色基本信息 \"), _createElementVNode(\"div\", _hoisted_36, [_createElementVNode(\"h3\", _hoisted_37, _toDisplayString(character.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"span\", _hoisted_39, _toDisplayString(character.occupation || '未知职业'), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_40, _toDisplayString(character.age || '?') + \"岁\", 1 /* TEXT */)])]), _createCommentVNode(\" 角色属性预览 \"), character.attributes ? (_openBlock(), _createElementBlock(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_cache[42] || (_cache[42] = _createElementVNode(\"span\", {\n      \"class\": \"attr-label\"\n    }, \"力量\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_44, _toDisplayString(character.attributes.STR || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_45, [_cache[43] || (_cache[43] = _createElementVNode(\"span\", {\n      \"class\": \"attr-label\"\n    }, \"体质\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_46, _toDisplayString(character.attributes.CON || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_47, [_cache[44] || (_cache[44] = _createElementVNode(\"span\", {\n      \"class\": \"attr-label\"\n    }, \"敏捷\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_48, _toDisplayString(character.attributes.DEX || 0), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"div\", _hoisted_50, [_cache[45] || (_cache[45] = _createElementVNode(\"span\", {\n      \"class\": \"attr-label\"\n    }, \"智力\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_51, _toDisplayString(character.attributes.INT || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_52, [_cache[46] || (_cache[46] = _createElementVNode(\"span\", {\n      \"class\": \"attr-label\"\n    }, \"意志\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_53, _toDisplayString(character.attributes.POW || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_54, [_cache[47] || (_cache[47] = _createElementVNode(\"span\", {\n      \"class\": \"attr-label\"\n    }, \"魅力\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_55, _toDisplayString(character.attributes.APP || 0), 1 /* TEXT */)])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 生命值和理智值 \"), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"div\", _hoisted_57, [_cache[48] || (_cache[48] = _createElementVNode(\"div\", {\n      \"class\": \"vital-label\"\n    }, [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-heart\"\n    }), _createElementVNode(\"span\", null, \"HP\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_58, [_createElementVNode(\"div\", {\n      \"class\": \"vital-fill hp\",\n      style: _normalizeStyle({\n        width: \"\".concat($options.getHPPercentage(character), \"%\")\n      })\n    }, null, 4 /* STYLE */), _createElementVNode(\"span\", _hoisted_59, _toDisplayString(character.current_hp || 0) + \"/\" + _toDisplayString(character.max_hp || 0), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_60, [_cache[49] || (_cache[49] = _createElementVNode(\"div\", {\n      \"class\": \"vital-label\"\n    }, [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-brain\"\n    }), _createElementVNode(\"span\", null, \"SAN\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"div\", {\n      \"class\": \"vital-fill san\",\n      style: _normalizeStyle({\n        width: \"\".concat($options.getSANPercentage(character), \"%\")\n      })\n    }, null, 4 /* STYLE */), _createElementVNode(\"span\", _hoisted_62, _toDisplayString(character.current_san || 0) + \"/\" + _toDisplayString(character.max_san || 0), 1 /* TEXT */)])])]), _createCommentVNode(\" 角色操作按钮 \"), _createElementVNode(\"div\", _hoisted_63, [_createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.viewCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"action-btn view\"\n    }, _toConsumableArray(_cache[50] || (_cache[50] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-eye\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"查看\", -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_64), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.editCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"action-btn edit\"\n    }, _toConsumableArray(_cache[51] || (_cache[51] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-edit\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"编辑\", -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_65), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.duplicateCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"action-btn duplicate\"\n    }, _toConsumableArray(_cache[52] || (_cache[52] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-copy\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"复制\", -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_66), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.deleteCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"action-btn delete\"\n    }, _toConsumableArray(_cache[53] || (_cache[53] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-trash\"\n    }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"删除\", -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_67)])], 10 /* CLASS, PROPS */, _hoisted_31);\n  }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 列表视图 \"), _createElementVNode(\"div\", _hoisted_68, [_cache[60] || (_cache[60] = _createStaticVNode(\"<div class=\\\"list-header\\\" data-v-14506b04><div class=\\\"list-col name\\\" data-v-14506b04>角色名称</div><div class=\\\"list-col occupation\\\" data-v-14506b04>职业</div><div class=\\\"list-col age\\\" data-v-14506b04>年龄</div><div class=\\\"list-col vitals\\\" data-v-14506b04>生命/理智</div><div class=\\\"list-col status\\\" data-v-14506b04>状态</div><div class=\\\"list-col actions\\\" data-v-14506b04>操作</div></div>\", 1)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredCharacters, function (character) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: character.id,\n      \"class\": \"list-item\",\n      onClick: function onClick($event) {\n        return $options.selectCharacter(character);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_70, [_createElementVNode(\"div\", _hoisted_71, [_createElementVNode(\"img\", {\n      src: $options.getCharacterAvatar(character),\n      alt: character.name,\n      \"class\": \"list-avatar\"\n    }, null, 8 /* PROPS */, _hoisted_72), _createElementVNode(\"div\", null, [_createElementVNode(\"div\", _hoisted_73, _toDisplayString(character.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_74, \"ID: \" + _toDisplayString(character.id), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_75, _toDisplayString(character.occupation || '未知'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_76, _toDisplayString(character.age || '?') + \"岁\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_77, [_createElementVNode(\"div\", _hoisted_78, [_createElementVNode(\"div\", _hoisted_79, [_cache[54] || (_cache[54] = _createElementVNode(\"span\", {\n      \"class\": \"vital-label\"\n    }, \"HP:\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_80, _toDisplayString(character.current_hp || 0) + \"/\" + _toDisplayString(character.max_hp || 0), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_81, [_cache[55] || (_cache[55] = _createElementVNode(\"span\", {\n      \"class\": \"vital-label\"\n    }, \"SAN:\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_82, _toDisplayString(character.current_san || 0) + \"/\" + _toDisplayString(character.max_san || 0), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_83, [_createElementVNode(\"span\", {\n      \"class\": _normalizeClass([\"status-badge\", character.status])\n    }, _toDisplayString($options.getStatusText(character.status)), 3 /* TEXT, CLASS */)]), _createElementVNode(\"div\", _hoisted_84, [_createElementVNode(\"div\", _hoisted_85, [_createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.viewCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"list-action-btn\",\n      title: \"查看\"\n    }, _toConsumableArray(_cache[56] || (_cache[56] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-eye\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_86), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.editCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"list-action-btn\",\n      title: \"编辑\"\n    }, _toConsumableArray(_cache[57] || (_cache[57] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-edit\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_87), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.duplicateCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"list-action-btn\",\n      title: \"复制\"\n    }, _toConsumableArray(_cache[58] || (_cache[58] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-copy\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_88), _createElementVNode(\"button\", {\n      onClick: _withModifiers(function ($event) {\n        return $options.deleteCharacter(character);\n      }, [\"stop\"]),\n      \"class\": \"list-action-btn delete\",\n      title: \"删除\"\n    }, _toConsumableArray(_cache[59] || (_cache[59] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-trash\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_89)])])], 8 /* PROPS */, _hoisted_69);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 编辑模态框 \"), $data.showEditForm ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 5,\n    \"class\": \"character-edit-modal-overlay\",\n    onClick: _cache[10] || (_cache[10] = function () {\n      return $options.closeEditForm && $options.closeEditForm.apply($options, arguments);\n    })\n  }, [_createElementVNode(\"div\", {\n    \"class\": \"character-edit-modal\",\n    onClick: _cache[9] || (_cache[9] = _withModifiers(function () {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_90, [_cache[62] || (_cache[62] = _createElementVNode(\"h3\", {\n    \"class\": \"modal-title\"\n  }, \"编辑角色\", -1 /* CACHED */)), _createElementVNode(\"button\", {\n    onClick: _cache[8] || (_cache[8] = function () {\n      return $options.closeEditForm && $options.closeEditForm.apply($options, arguments);\n    }),\n    \"class\": \"modal-close\"\n  }, _cache[61] || (_cache[61] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))]), _createElementVNode(\"div\", _hoisted_91, [_createVNode(_component_character_form, {\n    \"is-edit\": true,\n    character: $data.selectedCharacter,\n    onSubmit: $options.updateCharacter,\n    onCancel: $options.closeEditForm\n  }, null, 8 /* PROPS */, [\"character\", \"onSubmit\", \"onCancel\"])])])])) : _createCommentVNode(\"v-if\", true), $data.showCharacterView ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 6,\n    \"class\": \"character-view-modal-overlay\",\n    onClick: _cache[16] || (_cache[16] = function () {\n      return $options.closeCharacterView && $options.closeCharacterView.apply($options, arguments);\n    })\n  }, [_createElementVNode(\"div\", {\n    \"class\": \"character-view-modal\",\n    onClick: _cache[15] || (_cache[15] = _withModifiers(function () {}, [\"stop\"]))\n  }, [_createElementVNode(\"div\", _hoisted_92, [_createElementVNode(\"div\", _hoisted_93, [_createElementVNode(\"div\", _hoisted_94, [_createElementVNode(\"img\", {\n    src: $options.getCharacterAvatar($data.selectedCharacter),\n    alt: (_$data$selectedCharac = $data.selectedCharacter) === null || _$data$selectedCharac === void 0 ? void 0 : _$data$selectedCharac.name\n  }, null, 8 /* PROPS */, _hoisted_95)]), _createElementVNode(\"div\", _hoisted_96, [_createElementVNode(\"h3\", _hoisted_97, _toDisplayString((_$data$selectedCharac2 = $data.selectedCharacter) === null || _$data$selectedCharac2 === void 0 ? void 0 : _$data$selectedCharac2.name), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_98, _toDisplayString((_$data$selectedCharac3 = $data.selectedCharacter) === null || _$data$selectedCharac3 === void 0 ? void 0 : _$data$selectedCharac3.occupation) + \" • ID: \" + _toDisplayString((_$data$selectedCharac4 = $data.selectedCharacter) === null || _$data$selectedCharac4 === void 0 ? void 0 : _$data$selectedCharac4.id), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_99, [_createElementVNode(\"button\", {\n    onClick: _cache[11] || (_cache[11] = function () {\n      return $options.editCharacterFromView && $options.editCharacterFromView.apply($options, arguments);\n    }),\n    \"class\": \"modal-action-btn edit\",\n    title: \"编辑角色\"\n  }, _cache[63] || (_cache[63] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-edit\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[12] || (_cache[12] = function () {\n      return $options.duplicateCharacterFromView && $options.duplicateCharacterFromView.apply($options, arguments);\n    }),\n    \"class\": \"modal-action-btn duplicate\",\n    title: \"复制角色\"\n  }, _cache[64] || (_cache[64] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-copy\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[13] || (_cache[13] = function () {\n      return $options.deleteCharacterFromView && $options.deleteCharacterFromView.apply($options, arguments);\n    }),\n    \"class\": \"modal-action-btn delete\",\n    title: \"删除角色\"\n  }, _cache[65] || (_cache[65] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-trash\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[14] || (_cache[14] = function () {\n      return $options.closeCharacterView && $options.closeCharacterView.apply($options, arguments);\n    }),\n    \"class\": \"modal-close\"\n  }, _cache[66] || (_cache[66] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-times\"\n  }, null, -1 /* CACHED */)]))])]), _createElementVNode(\"div\", _hoisted_100, [_createVNode(_component_character_detail_view, {\n    character: $data.selectedCharacter\n  }, null, 8 /* PROPS */, [\"character\"])])])])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "onClick", "_cache", "$event", "$data", "viewMode", "_normalizeClass", "title", "_hoisted_6", "searchQuery", "type", "placeholder", "$options", "createCharacter", "apply", "arguments", "characters", "length", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_toDisplayString", "_hoisted_12", "_hoisted_13", "_hoisted_14", "aliveCharacters", "_hoisted_15", "_hoisted_16", "_hoisted_17", "favoriteCharacters", "_hoisted_18", "_hoisted_19", "_hoisted_20", "recentCharacters", "_hoisted_21", "_hoisted_22", "_hoisted_23", "selectedOccupation", "value", "_Fragment", "_renderList", "occupations", "occupation", "key", "_hoisted_24", "_hoisted_25", "selectedStatus", "_hoisted_26", "sortBy", "loading", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "filteredCharacters", "character", "id", "is_favorite", "selectCharacter", "_hoisted_32", "_hoisted_33", "src", "getCharacterAvatar", "alt", "name", "status", "_withModifiers", "toggleFavorite", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "age", "attributes", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "STR", "_hoisted_45", "_hoisted_46", "CON", "_hoisted_47", "_hoisted_48", "DEX", "_hoisted_49", "_hoisted_50", "_hoisted_51", "INT", "_hoisted_52", "_hoisted_53", "POW", "_hoisted_54", "_hoisted_55", "APP", "_hoisted_56", "_hoisted_57", "_hoisted_58", "style", "_normalizeStyle", "width", "concat", "getHPPercentage", "_hoisted_59", "current_hp", "max_hp", "_hoisted_60", "_hoisted_61", "getSANPercentage", "_hoisted_62", "current_san", "max_san", "_hoisted_63", "viewCharacter", "edit<PERSON><PERSON><PERSON>", "duplicateCharacter", "deleteCharacter", "_hoisted_68", "_hoisted_70", "_hoisted_71", "_hoisted_73", "_hoisted_74", "_hoisted_75", "_hoisted_76", "_hoisted_77", "_hoisted_78", "_hoisted_79", "_hoisted_80", "_hoisted_81", "_hoisted_82", "_hoisted_83", "getStatusText", "_hoisted_84", "_hoisted_85", "showEditForm", "closeEditForm", "_hoisted_90", "_hoisted_91", "_createVNode", "_component_character_form", "<PERSON><PERSON><PERSON><PERSON>", "onSubmit", "updateCharacter", "onCancel", "showCharacterView", "closeCharacterView", "_hoisted_92", "_hoisted_93", "_hoisted_94", "_$data$selectedCharac", "_hoisted_96", "_hoisted_97", "_$data$selectedCharac2", "_hoisted_98", "_$data$selectedCharac3", "_$data$selectedCharac4", "_hoisted_99", "editCharacterFromView", "duplicateCharacterFromView", "deleteCharacterFromView", "_hoisted_100", "_component_character_detail_view"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CharacterManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"character-manager\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <h1 class=\"page-title\">\r\n            <i class=\"fas fa-user-friends\"></i>\r\n            <span>角色管理</span>\r\n          </h1>\r\n          <p class=\"page-subtitle\">管理您的COC角色，创建新的冒险者</p>\r\n        </div>\r\n        \r\n        <div class=\"header-actions\">\r\n          <div class=\"view-controls\">\r\n            <button \r\n              @click=\"viewMode = 'grid'\" \r\n              class=\"view-btn\"\r\n              :class=\"{ 'active': viewMode === 'grid' }\"\r\n              title=\"网格视图\"\r\n            >\r\n              <i class=\"fas fa-th\"></i>\r\n            </button>\r\n            <button \r\n              @click=\"viewMode = 'list'\" \r\n              class=\"view-btn\"\r\n              :class=\"{ 'active': viewMode === 'list' }\"\r\n              title=\"列表视图\"\r\n            >\r\n              <i class=\"fas fa-list\"></i>\r\n            </button>\r\n          </div>\r\n          \r\n          <div class=\"search-box\">\r\n            <i class=\"fas fa-search search-icon\"></i>\r\n            <input \r\n              v-model=\"searchQuery\" \r\n              type=\"text\" \r\n              placeholder=\"搜索角色...\" \r\n              class=\"search-input\"\r\n            />\r\n          </div>\r\n          \r\n          <button @click=\"createCharacter\" class=\"create-btn primary\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>创建角色</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 统计信息 -->\r\n    <div class=\"stats-section\" v-if=\"characters.length > 0\">\r\n      <div class=\"stats-grid\">\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-users\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ characters.length }}</div>\r\n            <div class=\"stat-label\">总角色数</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-heart\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ aliveCharacters }}</div>\r\n            <div class=\"stat-label\">存活角色</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-star\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ favoriteCharacters }}</div>\r\n            <div class=\"stat-label\">收藏角色</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-clock\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ recentCharacters }}</div>\r\n            <div class=\"stat-label\">最近使用</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 筛选和排序 -->\r\n    <div class=\"filter-section\" v-if=\"characters.length > 0\">\r\n      <div class=\"filter-controls\">\r\n        <div class=\"filter-group\">\r\n          <label class=\"filter-label\">职业筛选:</label>\r\n          <select v-model=\"selectedOccupation\" class=\"filter-select\">\r\n            <option value=\"\">全部职业</option>\r\n            <option v-for=\"occupation in occupations\" :key=\"occupation\" :value=\"occupation\">\r\n              {{ occupation }}\r\n            </option>\r\n          </select>\r\n        </div>\r\n        \r\n        <div class=\"filter-group\">\r\n          <label class=\"filter-label\">状态筛选:</label>\r\n          <select v-model=\"selectedStatus\" class=\"filter-select\">\r\n            <option value=\"\">全部状态</option>\r\n            <option value=\"alive\">存活</option>\r\n            <option value=\"dead\">死亡</option>\r\n            <option value=\"insane\">疯狂</option>\r\n          </select>\r\n        </div>\r\n        \r\n        <div class=\"filter-group\">\r\n          <label class=\"filter-label\">排序方式:</label>\r\n          <select v-model=\"sortBy\" class=\"filter-select\">\r\n            <option value=\"name\">按名称</option>\r\n            <option value=\"created\">按创建时间</option>\r\n            <option value=\"updated\">按更新时间</option>\r\n            <option value=\"level\">按等级</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-state\">\r\n      <div class=\"loading-spinner\"></div>\r\n      <p>正在加载角色数据...</p>\r\n    </div>\r\n    \r\n    <!-- 空状态 -->\r\n    <div v-else-if=\"characters.length === 0\" class=\"empty-state\">\r\n      <div class=\"empty-icon\">\r\n        <i class=\"fas fa-user-plus\"></i>\r\n      </div>\r\n      <h3 class=\"empty-title\">还没有角色</h3>\r\n      <p class=\"empty-description\">创建您的第一个COC角色，开始您的克苏鲁冒险之旅</p>\r\n      <button @click=\"createCharacter\" class=\"create-btn primary large\">\r\n        <i class=\"fas fa-plus\"></i>\r\n        <span>创建第一个角色</span>\r\n      </button>\r\n    </div>\r\n    \r\n    <!-- 角色列表 -->\r\n    <div v-else class=\"characters-section\">\r\n      <!-- 网格视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"character-grid\">\r\n        <div \r\n          v-for=\"character in filteredCharacters\" \r\n          :key=\"character.id\" \r\n          class=\"character-card\"\r\n          :class=\"{ 'favorite': character.is_favorite }\"\r\n          @click=\"selectCharacter(character)\"\r\n        >\r\n          <!-- 角色头像和状态 -->\r\n          <div class=\"character-avatar-section\">\r\n            <div class=\"character-avatar\">\r\n              <img :src=\"getCharacterAvatar(character)\" :alt=\"character.name\" />\r\n              <div class=\"character-status-indicator\" :class=\"character.status\"></div>\r\n            </div>\r\n            <button \r\n              @click.stop=\"toggleFavorite(character)\" \r\n              class=\"favorite-btn\"\r\n              :class=\"{ 'active': character.is_favorite }\"\r\n            >\r\n              <i class=\"fas fa-star\"></i>\r\n            </button>\r\n          </div>\r\n          \r\n          <!-- 角色基本信息 -->\r\n          <div class=\"character-basic-info\">\r\n            <h3 class=\"character-name\">{{ character.name }}</h3>\r\n            <div class=\"character-meta\">\r\n              <span class=\"character-occupation\">{{ character.occupation || '未知职业' }}</span>\r\n              <span class=\"character-age\">{{ character.age || '?' }}岁</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 角色属性预览 -->\r\n          <div class=\"character-attributes\" v-if=\"character.attributes\">\r\n            <div class=\"attribute-row\">\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">力量</span>\r\n                <span class=\"attr-value\">{{ character.attributes.STR || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">体质</span>\r\n                <span class=\"attr-value\">{{ character.attributes.CON || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">敏捷</span>\r\n                <span class=\"attr-value\">{{ character.attributes.DEX || 0 }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"attribute-row\">\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">智力</span>\r\n                <span class=\"attr-value\">{{ character.attributes.INT || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">意志</span>\r\n                <span class=\"attr-value\">{{ character.attributes.POW || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">魅力</span>\r\n                <span class=\"attr-value\">{{ character.attributes.APP || 0 }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 生命值和理智值 -->\r\n          <div class=\"character-vitals\">\r\n            <div class=\"vital-item\">\r\n              <div class=\"vital-label\">\r\n                <i class=\"fas fa-heart\"></i>\r\n                <span>HP</span>\r\n              </div>\r\n              <div class=\"vital-bar\">\r\n                <div \r\n                  class=\"vital-fill hp\" \r\n                  :style=\"{ width: `${getHPPercentage(character)}%` }\"\r\n                ></div>\r\n                <span class=\"vital-text\">{{ character.current_hp || 0 }}/{{ character.max_hp || 0 }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"vital-item\">\r\n              <div class=\"vital-label\">\r\n                <i class=\"fas fa-brain\"></i>\r\n                <span>SAN</span>\r\n              </div>\r\n              <div class=\"vital-bar\">\r\n                <div \r\n                  class=\"vital-fill san\" \r\n                  :style=\"{ width: `${getSANPercentage(character)}%` }\"\r\n                ></div>\r\n                <span class=\"vital-text\">{{ character.current_san || 0 }}/{{ character.max_san || 0 }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 角色操作按钮 -->\r\n          <div class=\"character-actions\">\r\n            <button @click.stop=\"viewCharacter(character)\" class=\"action-btn view\">\r\n              <i class=\"fas fa-eye\"></i>\r\n              <span>查看</span>\r\n            </button>\r\n            <button @click.stop=\"editCharacter(character)\" class=\"action-btn edit\">\r\n              <i class=\"fas fa-edit\"></i>\r\n              <span>编辑</span>\r\n            </button>\r\n            <button @click.stop=\"duplicateCharacter(character)\" class=\"action-btn duplicate\">\r\n              <i class=\"fas fa-copy\"></i>\r\n              <span>复制</span>\r\n            </button>\r\n            <button @click.stop=\"deleteCharacter(character)\" class=\"action-btn delete\">\r\n              <i class=\"fas fa-trash\"></i>\r\n              <span>删除</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 列表视图 -->\r\n      <div v-else class=\"character-list\">\r\n        <div class=\"list-header\">\r\n          <div class=\"list-col name\">角色名称</div>\r\n          <div class=\"list-col occupation\">职业</div>\r\n          <div class=\"list-col age\">年龄</div>\r\n          <div class=\"list-col vitals\">生命/理智</div>\r\n          <div class=\"list-col status\">状态</div>\r\n          <div class=\"list-col actions\">操作</div>\r\n        </div>\r\n        \r\n        <div \r\n          v-for=\"character in filteredCharacters\" \r\n          :key=\"character.id\" \r\n          class=\"list-item\"\r\n          @click=\"selectCharacter(character)\"\r\n        >\r\n          <div class=\"list-col name\">\r\n            <div class=\"character-name-cell\">\r\n              <img :src=\"getCharacterAvatar(character)\" :alt=\"character.name\" class=\"list-avatar\" />\r\n              <div>\r\n                <div class=\"character-name\">{{ character.name }}</div>\r\n                <div class=\"character-id\">ID: {{ character.id }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"list-col occupation\">{{ character.occupation || '未知' }}</div>\r\n          <div class=\"list-col age\">{{ character.age || '?' }}岁</div>\r\n          <div class=\"list-col vitals\">\r\n            <div class=\"vitals-cell\">\r\n              <div class=\"vital-mini\">\r\n                <span class=\"vital-label\">HP:</span>\r\n                <span class=\"vital-value\">{{ character.current_hp || 0 }}/{{ character.max_hp || 0 }}</span>\r\n              </div>\r\n              <div class=\"vital-mini\">\r\n                <span class=\"vital-label\">SAN:</span>\r\n                <span class=\"vital-value\">{{ character.current_san || 0 }}/{{ character.max_san || 0 }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"list-col status\">\r\n            <span class=\"status-badge\" :class=\"character.status\">\r\n              {{ getStatusText(character.status) }}\r\n            </span>\r\n          </div>\r\n          <div class=\"list-col actions\">\r\n            <div class=\"list-actions\">\r\n              <button @click.stop=\"viewCharacter(character)\" class=\"list-action-btn\" title=\"查看\">\r\n                <i class=\"fas fa-eye\"></i>\r\n              </button>\r\n              <button @click.stop=\"editCharacter(character)\" class=\"list-action-btn\" title=\"编辑\">\r\n                <i class=\"fas fa-edit\"></i>\r\n              </button>\r\n              <button @click.stop=\"duplicateCharacter(character)\" class=\"list-action-btn\" title=\"复制\">\r\n                <i class=\"fas fa-copy\"></i>\r\n              </button>\r\n              <button @click.stop=\"deleteCharacter(character)\" class=\"list-action-btn delete\" title=\"删除\">\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 编辑模态框 -->\r\n    <div v-if=\"showEditForm\" class=\"character-edit-modal-overlay\" @click=\"closeEditForm\">\r\n      <div class=\"character-edit-modal\" @click.stop>\r\n        <div class=\"character-edit-modal-header\">\r\n          <h3 class=\"modal-title\">编辑角色</h3>\r\n          <button @click=\"closeEditForm\" class=\"modal-close\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"character-edit-modal-body\">\r\n          <character-form\r\n            :is-edit=\"true\"\r\n            :character=\"selectedCharacter\"\r\n            @submit=\"updateCharacter\"\r\n            @cancel=\"closeEditForm\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <div v-if=\"showCharacterView\" class=\"character-view-modal-overlay\" @click=\"closeCharacterView\">\r\n      <div class=\"character-view-modal\" @click.stop>\r\n        <div class=\"character-view-modal-header\">\r\n          <div class=\"modal-title-section\">\r\n            <div class=\"character-avatar-small\">\r\n              <img :src=\"getCharacterAvatar(selectedCharacter)\" :alt=\"selectedCharacter?.name\" />\r\n            </div>\r\n            <div class=\"character-title-info\">\r\n              <h3 class=\"modal-title\">{{ selectedCharacter?.name }}</h3>\r\n              <p class=\"character-subtitle\">{{ selectedCharacter?.occupation }} • ID: {{ selectedCharacter?.id }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-actions\">\r\n            <button @click=\"editCharacterFromView\" class=\"modal-action-btn edit\" title=\"编辑角色\">\r\n              <i class=\"fas fa-edit\"></i>\r\n            </button>\r\n            <button @click=\"duplicateCharacterFromView\" class=\"modal-action-btn duplicate\" title=\"复制角色\">\r\n              <i class=\"fas fa-copy\"></i>\r\n            </button>\r\n            <button @click=\"deleteCharacterFromView\" class=\"modal-action-btn delete\" title=\"删除角色\">\r\n              <i class=\"fas fa-trash\"></i>\r\n            </button>\r\n            <button @click=\"closeCharacterView\" class=\"modal-close\">\r\n              <i class=\"fas fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"character-view-modal-body\">\r\n          <character-detail-view :character=\"selectedCharacter\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport CharacterForm from '@/components/CharacterForm.vue'\r\nimport CharacterDetailView from '@/components/CharacterDetailView.vue'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'CharacterManager',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    CharacterForm,\r\n    CharacterDetailView\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      characters: [],\r\n      showEditForm: false,\r\n      showCharacterView: false,\r\n      selectedCharacter: null,\r\n      viewMode: 'grid', // 'grid' or 'list'\r\n      searchQuery: '',\r\n      selectedOccupation: '',\r\n      selectedStatus: '',\r\n      sortBy: 'name',\r\n      defaultAvatar: '/images/default-character.png'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userCharacters']),\r\n    \r\n    filteredCharacters() {\r\n      let filtered = [...this.characters]\r\n      \r\n      // 搜索过滤\r\n      if (this.searchQuery) {\r\n        const query = this.searchQuery.toLowerCase()\r\n        filtered = filtered.filter(char => \r\n          char.name.toLowerCase().includes(query) ||\r\n          (char.occupation && char.occupation.toLowerCase().includes(query))\r\n        )\r\n      }\r\n      \r\n      // 职业过滤\r\n      if (this.selectedOccupation) {\r\n        filtered = filtered.filter(char => char.occupation === this.selectedOccupation)\r\n      }\r\n      \r\n      // 状态过滤\r\n      if (this.selectedStatus) {\r\n        filtered = filtered.filter(char => char.status === this.selectedStatus)\r\n      }\r\n      \r\n      // 排序\r\n      filtered.sort((a, b) => {\r\n        switch (this.sortBy) {\r\n          case 'name':\r\n            return a.name.localeCompare(b.name)\r\n          case 'created':\r\n            return new Date(b.created_at) - new Date(a.created_at)\r\n          case 'updated':\r\n            return new Date(b.updated_at) - new Date(a.updated_at)\r\n          case 'level':\r\n            return (b.level || 0) - (a.level || 0)\r\n          default:\r\n            return 0\r\n        }\r\n      })\r\n      \r\n      return filtered\r\n    },\r\n    \r\n    occupations() {\r\n      const occupations = [...new Set(this.characters.map(char => char.occupation).filter(Boolean))]\r\n      return occupations.sort()\r\n    },\r\n    \r\n    aliveCharacters() {\r\n      return this.characters.filter(char => char.status === 'alive' || !char.status).length\r\n    },\r\n    \r\n    favoriteCharacters() {\r\n      return this.characters.filter(char => char.is_favorite).length\r\n    },\r\n    \r\n    recentCharacters() {\r\n      const oneWeekAgo = new Date()\r\n      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)\r\n      return this.characters.filter(char => \r\n        new Date(char.updated_at || char.created_at) > oneWeekAgo\r\n      ).length\r\n    }\r\n  },\r\n  async created() {\r\n    await this.fetchCharacters()\r\n    this.restoreViewSettings()\r\n  },\r\n  beforeUnmount() {\r\n    this.saveViewSettings()\r\n  },\r\n  methods: {\r\n    async fetchCharacters() {\r\n      try {\r\n        this.loading = true\r\n        await this.$store.dispatch('fetchCharacters')\r\n        this.characters = this.userCharacters || []\r\n      } catch (error) {\r\n        console.error('加载角色失败:', error)\r\n        this.$message.error('加载角色失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    createCharacter() {\r\n      this.$router.push({ name: 'CharacterCreator' })\r\n    },\r\n    \r\n    selectCharacter(character) {\r\n      // 可以添加选中角色的逻辑\r\n      console.log('选中角色:', character)\r\n    },\r\n    \r\n    viewCharacter(character) {\r\n      console.log('🔍 查看角色:', character)\r\n      this.selectedCharacter = character\r\n      this.showCharacterView = true\r\n      console.log('🔍 showCharacterView:', this.showCharacterView)\r\n      console.log('🔍 selectedCharacter:', this.selectedCharacter)\r\n    },\r\n    \r\n    editCharacter(character) {\r\n      this.selectedCharacter = { ...character }\r\n      this.showEditForm = true\r\n    },\r\n    \r\n    async updateCharacter(characterData) {\r\n      try {\r\n        // 先更新角色基本信息\r\n        await this.$store.dispatch('updateCharacter', {\r\n          id: this.selectedCharacter.id,\r\n          ...characterData\r\n        })\r\n\r\n        // 如果有头像文件，单独上传头像\r\n        if (characterData.avatarFile) {\r\n          await this.uploadCharacterAvatar(this.selectedCharacter.id, characterData.avatarFile)\r\n        }\r\n\r\n        this.showEditForm = false\r\n        await this.fetchCharacters()\r\n        this.$message.success('角色更新成功')\r\n      } catch (error) {\r\n        console.error('更新角色失败:', error)\r\n        this.$message.error('更新角色失败')\r\n      }\r\n    },\r\n    \r\n    async duplicateCharacter(character) {\r\n      try {\r\n        // 生成唯一的复制名称\r\n        const newName = this.generateDuplicateName(character.name)\r\n\r\n        const duplicatedData = {\r\n          ...character,\r\n          name: newName,\r\n          id: undefined,\r\n          created_at: undefined,\r\n          updated_at: undefined,\r\n          // 清除头像数据，让复制的角色使用默认头像\r\n          avatar_data: null,\r\n          avatar_filename: null,\r\n          avatar_content_type: null\r\n        }\r\n\r\n        await this.$store.dispatch('createCharacter', duplicatedData)\r\n        await this.fetchCharacters()\r\n        this.$message.success(`角色复制成功，新角色名称：${newName}`)\r\n      } catch (error) {\r\n        console.error('复制角色失败:', error)\r\n        this.$message.error('复制角色失败: ' + (error.response?.data?.detail || error.message))\r\n      }\r\n    },\r\n\r\n    generateDuplicateName(originalName) {\r\n      const existingNames = this.characters.map(c => c.name)\r\n      let newName = `${originalName} (副本)`\r\n      let counter = 1\r\n\r\n      // 如果名称已存在，添加数字后缀\r\n      while (existingNames.includes(newName)) {\r\n        counter++\r\n        newName = `${originalName} (副本${counter})`\r\n      }\r\n\r\n      return newName\r\n    },\r\n    \r\n    async deleteCharacter(character) {\r\n      // 使用更友好的确认对话框\r\n      const confirmed = await this.showDeleteConfirmDialog(character)\r\n      if (!confirmed) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        await this.$store.dispatch('deleteCharacter', character.id)\r\n        await this.fetchCharacters()\r\n        this.$message.success(`角色 \"${character.name}\" 删除成功`)\r\n      } catch (error) {\r\n        console.error('删除角色失败:', error)\r\n        this.$message.error('删除角色失败: ' + (error.response?.data?.detail || error.message))\r\n      }\r\n    },\r\n\r\n    showDeleteConfirmDialog(character) {\r\n      return new Promise((resolve) => {\r\n        const confirmed = confirm(\r\n          `⚠️ 确定要删除角色 \"${character.name}\" 吗？\\n\\n` +\r\n          `角色信息：\\n` +\r\n          `• 职业：${character.occupation || '未知'}\\n` +\r\n          `• 等级：${character.level || '未知'}\\n` +\r\n          `• 创建时间：${character.created_at ? new Date(character.created_at).toLocaleDateString() : '未知'}\\n\\n` +\r\n          `⚠️ 此操作不可撤销，角色的所有数据将被永久删除！`\r\n        )\r\n        resolve(confirmed)\r\n      })\r\n    },\r\n    \r\n    async toggleFavorite(character) {\r\n      try {\r\n        await this.$store.dispatch('updateCharacter', {\r\n          id: character.id,\r\n          is_favorite: !character.is_favorite\r\n        })\r\n        await this.fetchCharacters()\r\n      } catch (error) {\r\n        console.error('更新收藏状态失败:', error)\r\n      }\r\n    },\r\n    \r\n    closeEditForm() {\r\n      this.showEditForm = false\r\n      this.selectedCharacter = null\r\n    },\r\n    \r\n    closeCharacterView() {\r\n      this.showCharacterView = false\r\n      this.selectedCharacter = null\r\n    },\r\n\r\n    // 从查看模态框中进行操作\r\n    editCharacterFromView() {\r\n      this.showCharacterView = false\r\n      this.editCharacter(this.selectedCharacter)\r\n    },\r\n\r\n    async duplicateCharacterFromView() {\r\n      this.showCharacterView = false\r\n      await this.duplicateCharacter(this.selectedCharacter)\r\n    },\r\n\r\n    async deleteCharacterFromView() {\r\n      const character = this.selectedCharacter\r\n      this.showCharacterView = false\r\n      await this.deleteCharacter(character)\r\n    },\r\n\r\n    // 头像上传方法\r\n    async uploadCharacterAvatar(characterId, avatarFile) {\r\n      try {\r\n        const formData = new FormData()\r\n        formData.append('file', avatarFile)\r\n\r\n        const response = await this.$http.post(`/api/characters/${characterId}/avatar`, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        })\r\n\r\n        if (response.data.success) {\r\n          console.log('✅ 头像上传成功')\r\n          return true\r\n        } else {\r\n          console.error('❌ 头像上传失败:', response.data.message)\r\n          throw new Error(response.data.message || '头像上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ 头像上传错误:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // 获取角色头像URL\r\n    getCharacterAvatar(character) {\r\n      if (character && character.id) {\r\n        // 尝试从API获取头像，如果失败则使用默认头像\r\n        return `http://localhost:8000/api/characters/${character.id}/avatar`\r\n      }\r\n      return this.defaultAvatar\r\n    },\r\n\r\n    // 获取属性中文名称\r\n    getAttributeName(key) {\r\n      const attributeNames = {\r\n        'STR': '力量',\r\n        'CON': '体质',\r\n        'SIZ': '体型',\r\n        'DEX': '敏捷',\r\n        'APP': '外貌',\r\n        'INT': '智力',\r\n        'POW': '意志',\r\n        'EDU': '教育'\r\n      }\r\n      return attributeNames[key] || key\r\n    },\r\n    \r\n    getHPPercentage(character) {\r\n      if (!character.max_hp || character.max_hp === 0) return 0\r\n      return Math.max(0, Math.min(100, (character.current_hp || 0) / character.max_hp * 100))\r\n    },\r\n    \r\n    getSANPercentage(character) {\r\n      if (!character.max_san || character.max_san === 0) return 0\r\n      return Math.max(0, Math.min(100, (character.current_san || 0) / character.max_san * 100))\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'alive': '存活',\r\n        'dead': '死亡',\r\n        'insane': '疯狂',\r\n        'unconscious': '昏迷'\r\n      }\r\n      return statusMap[status] || '正常'\r\n    },\r\n    \r\n    saveViewSettings() {\r\n      const settings = {\r\n        viewMode: this.viewMode,\r\n        sortBy: this.sortBy\r\n      }\r\n      this.safeSetJSON('character_manager_settings', settings)\r\n    },\r\n    \r\n    restoreViewSettings() {\r\n      const settings = this.safeGetJSON('character_manager_settings')\r\n      if (settings) {\r\n        try {\r\n          this.viewMode = settings.viewMode || 'grid'\r\n          this.sortBy = settings.sortBy || 'name'\r\n        } catch (error) {\r\n          console.warn('恢复视图设置失败:', error)\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.character-manager {\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  padding: var(--spacing-4);\r\n}\r\n\r\n/* ===== 页面头部 ===== */\r\n.page-header {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  margin: 0 0 var(--spacing-2);\r\n  font-size: var(--font-size-3xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.page-subtitle {\r\n  margin: 0;\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-base);\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.view-controls {\r\n  display: flex;\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  overflow: hidden;\r\n}\r\n\r\n.view-btn {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: none;\r\n  background: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.view-btn:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.view-btn.active {\r\n  background: var(--primary-600);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: var(--spacing-3);\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.search-input {\r\n  padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-8);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  width: 250px;\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: var(--primary-400);\r\n  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.create-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-2) var(--spacing-4);\r\n  border: none;\r\n  border-radius: var(--radius-md);\r\n  background: var(--primary-600);\r\n  color: var(--text-inverse);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.create-btn:hover {\r\n  background: var(--primary-700);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.create-btn.large {\r\n  padding: var(--spacing-3) var(--spacing-6);\r\n  font-size: var(--font-size-base);\r\n}\r\n\r\n/* ===== 统计信息 ===== */\r\n.stats-section {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.stats-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.stat-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  padding: var(--spacing-4);\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-lg);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.stat-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: var(--text-inverse);\r\n  font-size: var(--font-size-lg);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--spacing-1);\r\n}\r\n\r\n.stat-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-muted);\r\n}\r\n\r\n/* ===== 筛选区域 ===== */\r\n.filter-section {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  gap: var(--spacing-4);\r\n  padding: var(--spacing-4);\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-lg);\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.filter-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  white-space: nowrap;\r\n}\r\n\r\n.filter-select {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  min-width: 120px;\r\n}\r\n\r\n/* ===== 加载和空状态 ===== */\r\n.loading-state, .empty-state {\r\n  text-align: center;\r\n  padding: var(--spacing-8);\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-xl);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid var(--primary-200);\r\n  border-top: 3px solid var(--primary-600);\r\n  border-radius: var(--radius-full);\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto var(--spacing-4);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.empty-icon {\r\n  font-size: var(--font-size-4xl);\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.empty-title {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.empty-description {\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n/* ===== 角色网格 ===== */\r\n.character-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.character-card {\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--spacing-4);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.character-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: var(--shadow-lg);\r\n  border-color: var(--primary-300);\r\n}\r\n\r\n.character-card.favorite {\r\n  border-color: var(--warning-400);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--warning-50) 100%);\r\n}\r\n\r\n.character-avatar-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-3);\r\n}\r\n\r\n.character-avatar {\r\n  position: relative;\r\n  width: 64px;\r\n  height: 64px;\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  border: 3px solid var(--primary-200);\r\n}\r\n\r\n.character-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.character-status-indicator {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: var(--radius-full);\r\n  border: 2px solid var(--bg-elevated);\r\n  background: var(--success-500);\r\n}\r\n\r\n.character-status-indicator.dead {\r\n  background: var(--error-500);\r\n}\r\n\r\n.character-status-indicator.insane {\r\n  background: var(--warning-500);\r\n}\r\n\r\n.favorite-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--radius-full);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.favorite-btn:hover {\r\n  background: var(--warning-100);\r\n  color: var(--warning-600);\r\n}\r\n\r\n.favorite-btn.active {\r\n  background: var(--warning-500);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.character-basic-info {\r\n  margin-bottom: var(--spacing-3);\r\n}\r\n\r\n.character-name {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-1);\r\n}\r\n\r\n.character-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.character-occupation {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.character-age {\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.character-attributes {\r\n  margin-bottom: var(--spacing-3);\r\n}\r\n\r\n.attribute-row {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.attribute-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: var(--spacing-2);\r\n  background: var(--bg-tertiary);\r\n  border-radius: var(--radius-md);\r\n}\r\n\r\n.attr-label {\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-1);\r\n}\r\n\r\n.attr-value {\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.character-vitals {\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.vital-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.vital-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  min-width: 50px;\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.vital-bar {\r\n  flex: 1;\r\n  height: 20px;\r\n  background: var(--gray-200);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.vital-fill {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  transition: width var(--transition-fast);\r\n}\r\n\r\n.vital-fill.hp {\r\n  background: var(--error-500);\r\n}\r\n\r\n.vital-fill.san {\r\n  background: var(--primary-500);\r\n}\r\n\r\n.vital-text {\r\n  position: relative;\r\n  z-index: 1;\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  color: var(--text-primary);\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.character-actions {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  padding: var(--spacing-2);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.action-btn:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.action-btn.view:hover {\r\n  background: var(--primary-50);\r\n  color: var(--primary-600);\r\n  border-color: var(--primary-300);\r\n}\r\n\r\n.action-btn.edit:hover {\r\n  background: var(--warning-50);\r\n  color: var(--warning-600);\r\n  border-color: var(--warning-300);\r\n}\r\n\r\n.action-btn.duplicate:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n}\r\n\r\n.action-btn.delete:hover {\r\n  background: var(--error-50);\r\n  color: var(--error-600);\r\n  border-color: var(--error-300);\r\n}\r\n\r\n/* ===== 列表视图 ===== */\r\n.character-list {\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-lg);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  display: flex;\r\n  background: var(--bg-tertiary);\r\n  border-bottom: 1px solid var(--border-primary);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.list-item {\r\n  display: flex;\r\n  border-bottom: 1px solid var(--border-primary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.list-item:hover {\r\n  background: var(--hover-bg);\r\n}\r\n\r\n.list-col {\r\n  padding: var(--spacing-3);\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.list-col.name {\r\n  flex: 2;\r\n}\r\n\r\n.list-col.occupation {\r\n  flex: 1;\r\n}\r\n\r\n.list-col.age {\r\n  flex: 0.8;\r\n}\r\n\r\n.list-col.vitals {\r\n  flex: 1.2;\r\n}\r\n\r\n.list-col.status {\r\n  flex: 0.8;\r\n}\r\n\r\n.list-col.actions {\r\n  flex: 1;\r\n}\r\n\r\n.character-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.list-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: var(--radius-full);\r\n  object-fit: cover;\r\n  border: 2px solid var(--primary-200);\r\n}\r\n\r\n.character-id {\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.vitals-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.vital-mini {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.vital-label {\r\n  color: var(--text-muted);\r\n}\r\n\r\n.vital-value {\r\n  color: var(--text-primary);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.status-badge {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.status-badge.alive {\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n}\r\n\r\n.status-badge.dead {\r\n  background: var(--error-100);\r\n  color: var(--error-700);\r\n}\r\n\r\n.status-badge.insane {\r\n  background: var(--warning-100);\r\n  color: var(--warning-700);\r\n}\r\n\r\n.list-actions {\r\n  display: flex;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.list-action-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.list-action-btn:hover {\r\n  background: var(--primary-100);\r\n  color: var(--primary-600);\r\n}\r\n\r\n.list-action-btn.delete:hover {\r\n  background: var(--error-100);\r\n  color: var(--error-600);\r\n}\r\n\r\n/* ===== 模态框 ===== */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n}\r\n\r\n.modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  animation: modal-appear 0.2s ease-out;\r\n  width: 600px;\r\n}\r\n\r\n.modal.large {\r\n  width: 95vw;\r\n  max-width: 1200px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n@keyframes modal-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.9) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1) translateY(0);\r\n  }\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.modal-title-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.character-avatar-small {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  border: 2px solid var(--primary-200);\r\n}\r\n\r\n.character-avatar-small img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.character-title-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.character-subtitle {\r\n  margin: 0;\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.modal-action-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-action-btn:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.modal-action-btn.edit:hover {\r\n  background: var(--warning-50);\r\n  color: var(--warning-600);\r\n  border-color: var(--warning-300);\r\n}\r\n\r\n.modal-action-btn.duplicate:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n}\r\n\r\n.modal-action-btn.delete:hover {\r\n  background: var(--error-50);\r\n  color: var(--error-600);\r\n  border-color: var(--error-300);\r\n}\r\n\r\n.modal-title {\r\n  margin: 0;\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.modal-close {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--radius-md);\r\n  background: none;\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-close:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.modal-body {\r\n  padding: var(--spacing-6);\r\n  overflow-y: auto;\r\n  max-height: 70vh;\r\n}\r\n\r\n.modal-body.character-detail-body {\r\n  padding: 0;\r\n  max-height: none;\r\n  overflow: visible;\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n@media (max-width: 1024px) {\r\n  .character-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  }\r\n  \r\n  .filter-controls {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n  }\r\n  \r\n  .filter-group {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .character-manager {\r\n    padding: var(--spacing-3);\r\n  }\r\n  \r\n  .header-content {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n  }\r\n  \r\n  .search-input {\r\n    width: 100%;\r\n  }\r\n  \r\n  .stats-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n  \r\n  .character-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .character-list {\r\n    overflow-x: auto;\r\n  }\r\n  \r\n  .list-header, .list-item {\r\n    min-width: 600px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .stats-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .modal {\r\n    width: 95vw;\r\n    margin: var(--spacing-2);\r\n  }\r\n  \r\n  .modal-header, .modal-body {\r\n    padding: var(--spacing-3) var(--spacing-4);\r\n  }\r\n}\r\n\r\n/* ===== 角色查看模态框专用样式 ===== */\r\n.character-view-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n}\r\n\r\n.character-view-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n  width: 95vw;\r\n  max-width: 1200px;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  animation: modal-appear 0.2s ease-out;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.character-view-modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.character-view-modal-body {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  background: white;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .character-view-modal {\r\n    width: 95vw;\r\n    margin: 8px;\r\n  }\r\n\r\n  .character-view-modal-header {\r\n    padding: 12px 16px;\r\n  }\r\n}\r\n\r\n/* ===== 角色编辑模态框专用样式 ===== */\r\n.character-edit-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n}\r\n\r\n.character-edit-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n  width: 90vw;\r\n  max-width: 800px;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  animation: modal-appear 0.2s ease-out;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.character-edit-modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.character-edit-modal-body {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  background: white;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .character-edit-modal {\r\n    width: 95vw;\r\n    margin: 8px;\r\n  }\r\n\r\n  .character-edit-modal-header {\r\n    padding: 12px 16px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;EACO,SAAM;AAAmB;;EAEvB,SAAM;AAAa;;EACjB,SAAM;AAAgB;;EASpB,SAAM;AAAgB;;EACpB,SAAM;AAAe;;EAmBrB,SAAM;AAAY;;;EAmBxB,SAAM;;;EACJ,SAAM;AAAY;;EAChB,SAAM;AAAW;;EAIf,SAAM;AAAc;;EAClB,SAAM;AAAa;;EAIvB,SAAM;AAAW;;EAIf,SAAM;AAAc;;EAClB,SAAM;AAAa;;EAIvB,SAAM;AAAW;;EAIf,SAAM;AAAc;;EAClB,SAAM;AAAa;;EAIvB,SAAM;AAAW;;EAIf,SAAM;AAAc;;EAClB,SAAM;AAAa;;;EAQ3B,SAAM;;;EACJ,SAAM;AAAiB;;EACrB,SAAM;AAAc;;;EAUpB,SAAM;AAAc;;EAUpB,SAAM;AAAc;;;EAaT,SAAM;;;EAMe,SAAM;AAAa;;EAahD,SAAM;AAAoB;;;EAEJ,SAAM;;;;EAS7B,SAAM;AAA0B;;EAC9B,SAAM;AAAkB;;;;EAc1B,SAAM;AAAsB;;EAC3B,SAAM;AAAgB;;EACrB,SAAM;AAAgB;;EACnB,SAAM;AAAsB;;EAC5B,SAAM;AAAe;;;EAK1B,SAAM;;;EACJ,SAAM;AAAe;;EACnB,SAAM;AAAgB;;EAEnB,SAAM;AAAY;;EAErB,SAAM;AAAgB;;EAEnB,SAAM;AAAY;;EAErB,SAAM;AAAgB;;EAEnB,SAAM;AAAY;;EAGvB,SAAM;AAAe;;EACnB,SAAM;AAAgB;;EAEnB,SAAM;AAAY;;EAErB,SAAM;AAAgB;;EAEnB,SAAM;AAAY;;EAErB,SAAM;AAAgB;;EAEnB,SAAM;AAAY;;EAMzB,SAAM;AAAkB;;EACtB,SAAM;AAAY;;EAKhB,SAAM;AAAW;;EAKd,SAAM;AAAY;;EAGvB,SAAM;AAAY;;EAKhB,SAAM;AAAW;;EAKd,SAAM;AAAY;;EAMzB,SAAM;AAAmB;;;;;;EAsBtB,SAAM;AAAgB;;;EAgBzB,SAAM;AAAe;;EACnB,SAAM;AAAqB;;;EAGvB,SAAM;AAAgB;;EACtB,SAAM;AAAc;;EAI1B,SAAM;AAAqB;;EAC3B,SAAM;AAAc;;EACpB,SAAM;AAAiB;;EACrB,SAAM;AAAa;;EACjB,SAAM;AAAY;;EAEf,SAAM;AAAa;;EAEtB,SAAM;AAAY;;EAEf,SAAM;AAAa;;EAI1B,SAAM;AAAiB;;EAKvB,SAAM;AAAkB;;EACtB,SAAM;AAAc;;;;;;EAsBxB,SAAM;AAA6B;;EAMnC,SAAM;AAA2B;;EAajC,SAAM;AAA6B;;EACjC,SAAM;AAAqB;;EACzB,SAAM;AAAwB;;;EAG9B,SAAM;AAAsB;;EAC3B,SAAM;AAAa;;EACpB,SAAM;AAAoB;;EAG5B,SAAM;AAAe;;EAevB,SAAM;AAA2B;;;;;uBAzX5CA,mBAAA,CA8XM,OA9XNC,UA8XM,GA7XJC,mBAAA,UAAa,EACbC,mBAAA,CA8CM,OA9CNC,UA8CM,GA7CJD,mBAAA,CA4CM,OA5CNE,UA4CM,G,4BA3CJF,mBAAA,CAMM;IAND,SAAM;EAAa,IACtBA,mBAAA,CAGK;IAHD,SAAM;EAAY,IACpBA,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,IAC9BA,mBAAA,CAAiB,cAAX,MAAI,E,GAEZA,mBAAA,CAA8C;IAA3C,SAAM;EAAe,GAAC,mBAAiB,E,qBAG5CA,mBAAA,CAkCM,OAlCNG,UAkCM,GAjCJH,mBAAA,CAiBM,OAjBNI,UAiBM,GAhBJJ,mBAAA,CAOS;IANNK,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,KAAA,CAAAC,QAAQ;IAAA;IAChB,SAAKC,eAAA,EAAC,UAAU;MAAA,UACIF,KAAA,CAAAC,QAAQ;IAAA;IAC5BE,KAAK,EAAC;kCAENX,mBAAA,CAAyB;IAAtB,SAAM;EAAW,0B,mBAEtBA,mBAAA,CAOS;IANNK,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,KAAA,CAAAC,QAAQ;IAAA;IAChB,SAAKC,eAAA,EAAC,UAAU;MAAA,UACIF,KAAA,CAAAC,QAAQ;IAAA;IAC5BE,KAAK,EAAC;kCAENX,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,qBAI1BA,mBAAA,CAQM,OARNY,UAQM,G,4BAPJZ,mBAAA,CAAyC;IAAtC,SAAM;EAA2B,4B,gBACpCA,mBAAA,CAKE;;aAJSQ,KAAA,CAAAK,WAAW,GAAAN,MAAA;IAAA;IACpBO,IAAI,EAAC,MAAM;IACXC,WAAW,EAAC,SAAS;IACrB,SAAM;iDAHGP,KAAA,CAAAK,WAAW,E,KAOxBb,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAC,eAAA,IAAAD,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAAE,SAAM;kCACrCnB,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,UAMlBD,mBAAA,UAAa,EACoBS,KAAA,CAAAY,UAAU,CAACC,MAAM,Q,cAAlDxB,mBAAA,CAuCM,OAvCNyB,UAuCM,GAtCJtB,mBAAA,CAqCM,OArCNuB,UAqCM,GApCJvB,mBAAA,CAQM,OARNwB,UAQM,G,4BAPJxB,mBAAA,CAEM;IAFD,SAAM;EAAW,IACpBA,mBAAA,CAA4B;IAAzB,SAAM;EAAc,G,qBAEzBA,mBAAA,CAGM,OAHNyB,WAGM,GAFJzB,mBAAA,CAAsD,OAAtD0B,WAAsD,EAAAC,gBAAA,CAA1BnB,KAAA,CAAAY,UAAU,CAACC,MAAM,kB,4BAC7CrB,mBAAA,CAAkC;IAA7B,SAAM;EAAY,GAAC,MAAI,oB,KAGhCA,mBAAA,CAQM,OARN4B,WAQM,G,4BAPJ5B,mBAAA,CAEM;IAFD,SAAM;EAAW,IACpBA,mBAAA,CAA4B;IAAzB,SAAM;EAAc,G,qBAEzBA,mBAAA,CAGM,OAHN6B,WAGM,GAFJ7B,mBAAA,CAAoD,OAApD8B,WAAoD,EAAAH,gBAAA,CAAxBX,QAAA,CAAAe,eAAe,kB,4BAC3C/B,mBAAA,CAAkC;IAA7B,SAAM;EAAY,GAAC,MAAI,oB,KAGhCA,mBAAA,CAQM,OARNgC,WAQM,G,4BAPJhC,mBAAA,CAEM;IAFD,SAAM;EAAW,IACpBA,mBAAA,CAA2B;IAAxB,SAAM;EAAa,G,qBAExBA,mBAAA,CAGM,OAHNiC,WAGM,GAFJjC,mBAAA,CAAuD,OAAvDkC,WAAuD,EAAAP,gBAAA,CAA3BX,QAAA,CAAAmB,kBAAkB,kB,4BAC9CnC,mBAAA,CAAkC;IAA7B,SAAM;EAAY,GAAC,MAAI,oB,KAGhCA,mBAAA,CAQM,OARNoC,WAQM,G,4BAPJpC,mBAAA,CAEM;IAFD,SAAM;EAAW,IACpBA,mBAAA,CAA4B;IAAzB,SAAM;EAAc,G,qBAEzBA,mBAAA,CAGM,OAHNqC,WAGM,GAFJrC,mBAAA,CAAqD,OAArDsC,WAAqD,EAAAX,gBAAA,CAAzBX,QAAA,CAAAuB,gBAAgB,kB,4BAC5CvC,mBAAA,CAAkC;IAA7B,SAAM;EAAY,GAAC,MAAI,oB,8CAMpCD,mBAAA,WAAc,EACoBS,KAAA,CAAAY,UAAU,CAACC,MAAM,Q,cAAnDxB,mBAAA,CAgCM,OAhCN2C,WAgCM,GA/BJxC,mBAAA,CA8BM,OA9BNyC,WA8BM,GA7BJzC,mBAAA,CAQM,OARN0C,WAQM,G,4BAPJ1C,mBAAA,CAAyC;IAAlC,SAAM;EAAc,GAAC,OAAK,qB,gBACjCA,mBAAA,CAKS;;aALQQ,KAAA,CAAAmC,kBAAkB,GAAApC,MAAA;IAAA;IAAE,SAAM;kCACzCP,mBAAA,CAA8B;IAAtB4C,KAAK,EAAC;EAAE,GAAC,MAAI,sB,kBACrB/C,mBAAA,CAESgD,SAAA,QAAAC,WAAA,CAFoB9B,QAAA,CAAA+B,WAAW,YAAzBC,UAAU;yBAAzBnD,mBAAA,CAES;MAFkCoD,GAAG,EAAED,UAAU;MAAGJ,KAAK,EAAEI;wBAC/DA,UAAU,wBAAAE,WAAA;2EAHA1C,KAAA,CAAAmC,kBAAkB,E,KAQrC3C,mBAAA,CAQM,OARNmD,WAQM,G,4BAPJnD,mBAAA,CAAyC;IAAlC,SAAM;EAAc,GAAC,OAAK,qB,gBACjCA,mBAAA,CAKS;;aALQQ,KAAA,CAAA4C,cAAc,GAAA7C,MAAA;IAAA;IAAE,SAAM;kCACrCP,mBAAA,CAA8B;IAAtB4C,KAAK,EAAC;EAAE,GAAC,MAAI,oBACrB5C,mBAAA,CAAiC;IAAzB4C,KAAK,EAAC;EAAO,GAAC,IAAE,oBACxB5C,mBAAA,CAAgC;IAAxB4C,KAAK,EAAC;EAAM,GAAC,IAAE,oBACvB5C,mBAAA,CAAkC;IAA1B4C,KAAK,EAAC;EAAQ,GAAC,IAAE,mB,2CAJVpC,KAAA,CAAA4C,cAAc,E,KAQjCpD,mBAAA,CAQM,OARNqD,WAQM,G,4BAPJrD,mBAAA,CAAyC;IAAlC,SAAM;EAAc,GAAC,OAAK,qB,gBACjCA,mBAAA,CAKS;;aALQQ,KAAA,CAAA8C,MAAM,GAAA/C,MAAA;IAAA;IAAE,SAAM;kCAC7BP,mBAAA,CAAiC;IAAzB4C,KAAK,EAAC;EAAM,GAAC,KAAG,oBACxB5C,mBAAA,CAAsC;IAA9B4C,KAAK,EAAC;EAAS,GAAC,OAAK,oBAC7B5C,mBAAA,CAAsC;IAA9B4C,KAAK,EAAC;EAAS,GAAC,OAAK,oBAC7B5C,mBAAA,CAAkC;IAA1B4C,KAAK,EAAC;EAAO,GAAC,KAAG,mB,2CAJVpC,KAAA,CAAA8C,MAAM,E,8CAU7BvD,mBAAA,UAAa,EACFS,KAAA,CAAA+C,OAAO,I,cAAlB1D,mBAAA,CAGM,OAHN2D,WAGM,EAAAlD,MAAA,SAAAA,MAAA,QAFJN,mBAAA,CAAmC;IAA9B,SAAM;EAAiB,2BAC5BA,mBAAA,CAAkB,WAAf,aAAW,mB,MAIAQ,KAAA,CAAAY,UAAU,CAACC,MAAM,U,cAAjCxB,mBAAA,CAUMgD,SAAA;IAAAI,GAAA;EAAA,IAXNlD,mBAAA,SAAY,EACZC,mBAAA,CAUM,OAVNyD,WAUM,G,4BATJzD,mBAAA,CAEM;IAFD,SAAM;EAAY,IACrBA,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,G,iDAE7BA,mBAAA,CAAkC;IAA9B,SAAM;EAAa,GAAC,OAAK,qB,4BAC7BA,mBAAA,CAAyD;IAAtD,SAAM;EAAmB,GAAC,0BAAwB,qBACrDA,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAC,eAAA,IAAAD,QAAA,CAAAC,eAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAAE,SAAM;kCACrCnB,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAoB,cAAd,SAAO,mB,wEAKjBH,mBAAA,CAqLMgD,SAAA;IAAAI,GAAA;EAAA,IAtLNlD,mBAAA,UAAa,EACbC,mBAAA,CAqLM,OArLN0D,WAqLM,GApLJ3D,mBAAA,UAAa,EACFS,KAAA,CAAAC,QAAQ,e,cAAnBZ,mBAAA,CAkHM,OAlHN8D,WAkHM,I,kBAjHJ9D,mBAAA,CAgHMgD,SAAA,QAAAC,WAAA,CA/GgB9B,QAAA,CAAA4C,kBAAkB,YAA/BC,SAAS;yBADlBhE,mBAAA,CAgHM;MA9GHoD,GAAG,EAAEY,SAAS,CAACC,EAAE;MAClB,SAAKpD,eAAA,EAAC,gBAAgB;QAAA,YACAmD,SAAS,CAACE;MAAW;MAC1C1D,OAAK,WAALA,OAAKA,CAAAE,MAAA;QAAA,OAAES,QAAA,CAAAgD,eAAe,CAACH,SAAS;MAAA;QAEjC9D,mBAAA,aAAgB,EAChBC,mBAAA,CAYM,OAZNiE,WAYM,GAXJjE,mBAAA,CAGM,OAHNkE,WAGM,GAFJlE,mBAAA,CAAkE;MAA5DmE,GAAG,EAAEnD,QAAA,CAAAoD,kBAAkB,CAACP,SAAS;MAAIQ,GAAG,EAAER,SAAS,CAACS;0CAC1DtE,mBAAA,CAAwE;MAAnE,SAAKU,eAAA,EAAC,4BAA4B,EAASmD,SAAS,CAACU,MAAM;+BAElEvE,mBAAA,CAMS;MALNK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAAyD,cAAc,CAACZ,SAAS;MAAA;MACrC,SAAKnD,eAAA,EAAC,cAAc;QAAA,UACAmD,SAAS,CAACE;MAAW;uDAEzC/D,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B,2CAI1BD,mBAAA,YAAe,EACfC,mBAAA,CAMM,OANN0E,WAMM,GALJ1E,mBAAA,CAAoD,MAApD2E,WAAoD,EAAAhD,gBAAA,CAAtBkC,SAAS,CAACS,IAAI,kBAC5CtE,mBAAA,CAGM,OAHN4E,WAGM,GAFJ5E,mBAAA,CAA8E,QAA9E6E,WAA8E,EAAAlD,gBAAA,CAAxCkC,SAAS,CAACb,UAAU,4BAC1DhD,mBAAA,CAA8D,QAA9D8E,WAA8D,EAAAnD,gBAAA,CAA/BkC,SAAS,CAACkB,GAAG,WAAU,GAAC,gB,KAI3DhF,mBAAA,YAAe,EACyB8D,SAAS,CAACmB,UAAU,I,cAA5DnF,mBAAA,CA6BM,OA7BNoF,WA6BM,GA5BJjF,mBAAA,CAaM,OAbNkF,WAaM,GAZJlF,mBAAA,CAGM,OAHNmF,WAGM,G,4BAFJnF,mBAAA,CAAkC;MAA5B,SAAM;IAAY,GAAC,IAAE,qBAC3BA,mBAAA,CAAmE,QAAnEoF,WAAmE,EAAAzD,gBAAA,CAAvCkC,SAAS,CAACmB,UAAU,CAACK,GAAG,sB,GAEtDrF,mBAAA,CAGM,OAHNsF,WAGM,G,4BAFJtF,mBAAA,CAAkC;MAA5B,SAAM;IAAY,GAAC,IAAE,qBAC3BA,mBAAA,CAAmE,QAAnEuF,WAAmE,EAAA5D,gBAAA,CAAvCkC,SAAS,CAACmB,UAAU,CAACQ,GAAG,sB,GAEtDxF,mBAAA,CAGM,OAHNyF,WAGM,G,4BAFJzF,mBAAA,CAAkC;MAA5B,SAAM;IAAY,GAAC,IAAE,qBAC3BA,mBAAA,CAAmE,QAAnE0F,WAAmE,EAAA/D,gBAAA,CAAvCkC,SAAS,CAACmB,UAAU,CAACW,GAAG,sB,KAGxD3F,mBAAA,CAaM,OAbN4F,WAaM,GAZJ5F,mBAAA,CAGM,OAHN6F,WAGM,G,4BAFJ7F,mBAAA,CAAkC;MAA5B,SAAM;IAAY,GAAC,IAAE,qBAC3BA,mBAAA,CAAmE,QAAnE8F,WAAmE,EAAAnE,gBAAA,CAAvCkC,SAAS,CAACmB,UAAU,CAACe,GAAG,sB,GAEtD/F,mBAAA,CAGM,OAHNgG,WAGM,G,4BAFJhG,mBAAA,CAAkC;MAA5B,SAAM;IAAY,GAAC,IAAE,qBAC3BA,mBAAA,CAAmE,QAAnEiG,WAAmE,EAAAtE,gBAAA,CAAvCkC,SAAS,CAACmB,UAAU,CAACkB,GAAG,sB,GAEtDlG,mBAAA,CAGM,OAHNmG,WAGM,G,4BAFJnG,mBAAA,CAAkC;MAA5B,SAAM;IAAY,GAAC,IAAE,qBAC3BA,mBAAA,CAAmE,QAAnEoG,WAAmE,EAAAzE,gBAAA,CAAvCkC,SAAS,CAACmB,UAAU,CAACqB,GAAG,sB,4CAK1DtG,mBAAA,aAAgB,EAChBC,mBAAA,CA2BM,OA3BNsG,WA2BM,GA1BJtG,mBAAA,CAYM,OAZNuG,WAYM,G,4BAXJvG,mBAAA,CAGM;MAHD,SAAM;IAAa,IACtBA,mBAAA,CAA4B;MAAzB,SAAM;IAAc,IACvBA,mBAAA,CAAe,cAAT,IAAE,E,qBAEVA,mBAAA,CAMM,OANNwG,WAMM,GALJxG,mBAAA,CAGO;MAFL,SAAM,eAAe;MACpByG,KAAK,EAAAC,eAAA;QAAAC,KAAA,KAAAC,MAAA,CAAc5F,QAAA,CAAA6F,eAAe,CAAChD,SAAS;MAAA;6BAE/C7D,mBAAA,CAA2F,QAA3F8G,WAA2F,EAAAnF,gBAAA,CAA/DkC,SAAS,CAACkD,UAAU,SAAQ,GAAC,GAAApF,gBAAA,CAAGkC,SAAS,CAACmD,MAAM,sB,KAGhFhH,mBAAA,CAYM,OAZNiH,WAYM,G,4BAXJjH,mBAAA,CAGM;MAHD,SAAM;IAAa,IACtBA,mBAAA,CAA4B;MAAzB,SAAM;IAAc,IACvBA,mBAAA,CAAgB,cAAV,KAAG,E,qBAEXA,mBAAA,CAMM,OANNkH,WAMM,GALJlH,mBAAA,CAGO;MAFL,SAAM,gBAAgB;MACrByG,KAAK,EAAAC,eAAA;QAAAC,KAAA,KAAAC,MAAA,CAAc5F,QAAA,CAAAmG,gBAAgB,CAACtD,SAAS;MAAA;6BAEhD7D,mBAAA,CAA6F,QAA7FoH,WAA6F,EAAAzF,gBAAA,CAAjEkC,SAAS,CAACwD,WAAW,SAAQ,GAAC,GAAA1F,gBAAA,CAAGkC,SAAS,CAACyD,OAAO,sB,OAKpFvH,mBAAA,YAAe,EACfC,mBAAA,CAiBM,OAjBNuH,WAiBM,GAhBJvH,mBAAA,CAGS;MAHAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAAwG,aAAa,CAAC3D,SAAS;MAAA;MAAG,SAAM;uDACnD7D,mBAAA,CAA0B;MAAvB,SAAM;IAAY,2BACrBA,mBAAA,CAAe,cAAT,IAAE,mB,iCAEVA,mBAAA,CAGS;MAHAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAAyG,aAAa,CAAC5D,SAAS;MAAA;MAAG,SAAM;uDACnD7D,mBAAA,CAA2B;MAAxB,SAAM;IAAa,2BACtBA,mBAAA,CAAe,cAAT,IAAE,mB,iCAEVA,mBAAA,CAGS;MAHAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAA0G,kBAAkB,CAAC7D,SAAS;MAAA;MAAG,SAAM;uDACxD7D,mBAAA,CAA2B;MAAxB,SAAM;IAAa,2BACtBA,mBAAA,CAAe,cAAT,IAAE,mB,iCAEVA,mBAAA,CAGS;MAHAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAA2G,eAAe,CAAC9D,SAAS;MAAA;MAAG,SAAM;uDACrD7D,mBAAA,CAA4B;MAAzB,SAAM;IAAc,2BACvBA,mBAAA,CAAe,cAAT,IAAE,mB;qDAOhBH,mBAAA,CA6DMgD,SAAA;IAAAI,GAAA;EAAA,IA9DNlD,mBAAA,UAAa,EACbC,mBAAA,CA6DM,OA7DN4H,WA6DM,G,2cAnDJ/H,mBAAA,CAkDMgD,SAAA,QAAAC,WAAA,CAjDgB9B,QAAA,CAAA4C,kBAAkB,YAA/BC,SAAS;yBADlBhE,mBAAA,CAkDM;MAhDHoD,GAAG,EAAEY,SAAS,CAACC,EAAE;MAClB,SAAM,WAAW;MAChBzD,OAAK,WAALA,OAAKA,CAAAE,MAAA;QAAA,OAAES,QAAA,CAAAgD,eAAe,CAACH,SAAS;MAAA;QAEjC7D,mBAAA,CAQM,OARN6H,WAQM,GAPJ7H,mBAAA,CAMM,OANN8H,WAMM,GALJ9H,mBAAA,CAAsF;MAAhFmE,GAAG,EAAEnD,QAAA,CAAAoD,kBAAkB,CAACP,SAAS;MAAIQ,GAAG,EAAER,SAAS,CAACS,IAAI;MAAE,SAAM;0CACtEtE,mBAAA,CAGM,cAFJA,mBAAA,CAAsD,OAAtD+H,WAAsD,EAAApG,gBAAA,CAAvBkC,SAAS,CAACS,IAAI,kBAC7CtE,mBAAA,CAAsD,OAAtDgI,WAAsD,EAA5B,MAAI,GAAArG,gBAAA,CAAGkC,SAAS,CAACC,EAAE,iB,OAInD9D,mBAAA,CAAyE,OAAzEiI,WAAyE,EAAAtG,gBAAA,CAArCkC,SAAS,CAACb,UAAU,0BACxDhD,mBAAA,CAA2D,OAA3DkI,WAA2D,EAAAvG,gBAAA,CAA9BkC,SAAS,CAACkB,GAAG,WAAU,GAAC,iBACrD/E,mBAAA,CAWM,OAXNmI,WAWM,GAVJnI,mBAAA,CASM,OATNoI,WASM,GARJpI,mBAAA,CAGM,OAHNqI,WAGM,G,4BAFJrI,mBAAA,CAAoC;MAA9B,SAAM;IAAa,GAAC,KAAG,qBAC7BA,mBAAA,CAA4F,QAA5FsI,WAA4F,EAAA3G,gBAAA,CAA/DkC,SAAS,CAACkD,UAAU,SAAQ,GAAC,GAAApF,gBAAA,CAAGkC,SAAS,CAACmD,MAAM,sB,GAE/EhH,mBAAA,CAGM,OAHNuI,WAGM,G,4BAFJvI,mBAAA,CAAqC;MAA/B,SAAM;IAAa,GAAC,MAAI,qBAC9BA,mBAAA,CAA8F,QAA9FwI,WAA8F,EAAA7G,gBAAA,CAAjEkC,SAAS,CAACwD,WAAW,SAAQ,GAAC,GAAA1F,gBAAA,CAAGkC,SAAS,CAACyD,OAAO,sB,OAIrFtH,mBAAA,CAIM,OAJNyI,WAIM,GAHJzI,mBAAA,CAEO;MAFD,SAAKU,eAAA,EAAC,cAAc,EAASmD,SAAS,CAACU,MAAM;wBAC9CvD,QAAA,CAAA0H,aAAa,CAAC7E,SAAS,CAACU,MAAM,yB,GAGrCvE,mBAAA,CAeM,OAfN2I,WAeM,GAdJ3I,mBAAA,CAaM,OAbN4I,WAaM,GAZJ5I,mBAAA,CAES;MAFAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAAwG,aAAa,CAAC3D,SAAS;MAAA;MAAG,SAAM,iBAAiB;MAAClD,KAAK,EAAC;uDAC3EX,mBAAA,CAA0B;MAAvB,SAAM;IAAY,0B,iCAEvBA,mBAAA,CAES;MAFAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAAyG,aAAa,CAAC5D,SAAS;MAAA;MAAG,SAAM,iBAAiB;MAAClD,KAAK,EAAC;uDAC3EX,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B,iCAExBA,mBAAA,CAES;MAFAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAA0G,kBAAkB,CAAC7D,SAAS;MAAA;MAAG,SAAM,iBAAiB;MAAClD,KAAK,EAAC;uDAChFX,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B,iCAExBA,mBAAA,CAES;MAFAK,OAAK,EAAAmE,cAAA,WAAAjE,MAAA;QAAA,OAAOS,QAAA,CAAA2G,eAAe,CAAC9D,SAAS;MAAA;MAAG,SAAM,wBAAwB;MAAClD,KAAK,EAAC;uDACpFX,mBAAA,CAA4B;MAAzB,SAAM;IAAc,0B;0IAQnCD,mBAAA,WAAc,EACHS,KAAA,CAAAqI,YAAY,I,cAAvBhJ,mBAAA,CAiBM;;IAjBmB,SAAM,8BAA8B;IAAEQ,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEU,QAAA,CAAA8H,aAAA,IAAA9H,QAAA,CAAA8H,aAAA,CAAA5H,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;MACjFnB,mBAAA,CAeM;IAfD,SAAM,sBAAsB;IAAEK,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAkE,cAAA,CAAN,cAAW;MAC3CxE,mBAAA,CAKM,OALN+I,WAKM,G,4BAJJ/I,mBAAA,CAAiC;IAA7B,SAAM;EAAa,GAAC,MAAI,qBAC5BA,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEU,QAAA,CAAA8H,aAAA,IAAA9H,QAAA,CAAA8H,aAAA,CAAA5H,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;IAAE,SAAM;kCACnCnB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,MAG3BA,mBAAA,CAOM,OAPNgJ,WAOM,GANJC,YAAA,CAKEC,yBAAA;IAJC,SAAO,EAAE,IAAI;IACbrF,SAAS,EAAErD,KAAA,CAAA2I,iBAAiB;IAC5BC,QAAM,EAAEpI,QAAA,CAAAqI,eAAe;IACvBC,QAAM,EAAEtI,QAAA,CAAA8H;6GAMNtI,KAAA,CAAA+I,iBAAiB,I,cAA5B1J,mBAAA,CA+BM;;IA/BwB,SAAM,8BAA8B;IAAEQ,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAwI,kBAAA,IAAAxI,QAAA,CAAAwI,kBAAA,CAAAtI,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAkB;IAAA;MAC3FnB,mBAAA,CA6BM;IA7BD,SAAM,sBAAsB;IAAEK,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAkE,cAAA,CAAN,cAAW;MAC3CxE,mBAAA,CAwBM,OAxBNyJ,WAwBM,GAvBJzJ,mBAAA,CAQM,OARN0J,WAQM,GAPJ1J,mBAAA,CAEM,OAFN2J,WAEM,GADJ3J,mBAAA,CAAmF;IAA7EmE,GAAG,EAAEnD,QAAA,CAAAoD,kBAAkB,CAAC5D,KAAA,CAAA2I,iBAAiB;IAAI9E,GAAG,GAAAuF,qBAAA,GAAEpJ,KAAA,CAAA2I,iBAAiB,cAAAS,qBAAA,uBAAjBA,qBAAA,CAAmBtF;0CAE7EtE,mBAAA,CAGM,OAHN6J,WAGM,GAFJ7J,mBAAA,CAA0D,MAA1D8J,WAA0D,EAAAnI,gBAAA,EAAAoI,sBAAA,GAA/BvJ,KAAA,CAAA2I,iBAAiB,cAAAY,sBAAA,uBAAjBA,sBAAA,CAAmBzF,IAAI,kBAClDtE,mBAAA,CAAuG,KAAvGgK,WAAuG,EAAArI,gBAAA,EAAAsI,sBAAA,GAAtEzJ,KAAA,CAAA2I,iBAAiB,cAAAc,sBAAA,uBAAjBA,sBAAA,CAAmBjH,UAAU,IAAG,SAAO,GAAArB,gBAAA,EAAAuI,sBAAA,GAAG1J,KAAA,CAAA2I,iBAAiB,cAAAe,sBAAA,uBAAjBA,sBAAA,CAAmBpG,EAAE,iB,KAGpG9D,mBAAA,CAaM,OAbNmK,WAaM,GAZJnK,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAoJ,qBAAA,IAAApJ,QAAA,CAAAoJ,qBAAA,CAAAlJ,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAqB;IAAA;IAAE,SAAM,uBAAuB;IAACR,KAAK,EAAC;kCACzEX,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,IAExBA,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAqJ,0BAAA,IAAArJ,QAAA,CAAAqJ,0BAAA,CAAAnJ,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAA0B;IAAA;IAAE,SAAM,4BAA4B;IAACR,KAAK,EAAC;kCACnFX,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,IAExBA,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAsJ,uBAAA,IAAAtJ,QAAA,CAAAsJ,uBAAA,CAAApJ,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAuB;IAAA;IAAE,SAAM,yBAAyB;IAACR,KAAK,EAAC;kCAC7EX,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,IAEzBA,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEU,QAAA,CAAAwI,kBAAA,IAAAxI,QAAA,CAAAwI,kBAAA,CAAAtI,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAkB;IAAA;IAAE,SAAM;kCACxCnB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,QAI7BA,mBAAA,CAEM,OAFNuK,YAEM,GADJtB,YAAA,CAAwDuB,gCAAA;IAAhC3G,SAAS,EAAErD,KAAA,CAAA2I;EAAiB,uC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}