/**
 * COC 7版武器装备系统测试
 * 验证武器切换、弹药管理、护甲系统
 */

import WeaponSystem from './weaponSystem.js'

console.log('=== COC 7版武器装备系统测试 ===\n')

// 创建测试角色
const soldier = {
  id: 'soldier1',
  name: '士兵',
  
  // 技能
  firearms_handgun: 70,
  firearms_rifle: 75,
  firearms_shotgun: 60,
  fighting_brawl: 65,
  sword: 50,
  mechanical_repair: 55,
  
  // 当前装备
  currentWeapon: null,
  currentArmor: null,
  currentShield: null,
  
  // 临时效果
  tempPenalties: {},
  
  // 背包武器和装备
  inventory: {
    // 武器
    pistol: { quantity: 1, currentAmmo: 6 },
    rifle: { quantity: 1, currentAmmo: 3 },
    shotgun: { quantity: 1, currentAmmo: 2 },
    sword: { quantity: 1 },
    knife: { quantity: 2 },
    
    // 护甲
    kevlar_vest: { quantity: 1 },
    leather_jacket: { quantity: 1 },
    
    // 弹药
    pistol_ammo: { quantity: 50 },
    rifle_ammo: { quantity: 30 },
    shotgun_shells: { quantity: 20 }
  }
}

// 测试1: 武器装备
console.log('=== 测试1: 武器装备 ===')

console.log(`${soldier.name} 初始装备:`)
console.log(`  武器: ${soldier.currentWeapon?.name || '无'}`)
console.log(`  护甲: ${soldier.currentArmor?.name || '无'}`)

// 装备手枪
console.log(`\n1.1 装备手枪:`)
const equipPistol = WeaponSystem.equipWeapon(soldier, 'pistol')
console.log(`  ${equipPistol.description}`)
console.log(`  成功: ${equipPistol.success}`)
if (equipPistol.success) {
  console.log(`  当前武器: ${soldier.currentWeapon.name}`)
  console.log(`  弹药: ${soldier.currentWeapon.currentAmmo}/${soldier.currentWeapon.ammo}`)
}

// 装备步枪 (切换武器)
console.log(`\n1.2 切换到步枪:`)
const switchToRifle = WeaponSystem.switchWeapon(soldier, 'rifle')
console.log(`  ${switchToRifle.description}`)
console.log(`  成功: ${switchToRifle.success}`)
if (switchToRifle.success) {
  console.log(`  当前武器: ${soldier.currentWeapon.name}`)
  console.log(`  弹药: ${soldier.currentWeapon.currentAmmo}/${soldier.currentWeapon.ammo}`)
}

// 尝试装备双手剑
console.log(`\n1.3 装备双手剑:`)
const equipSword = WeaponSystem.equipWeapon(soldier, 'sword')
console.log(`  ${equipSword.description}`)
console.log(`  成功: ${equipSword.success}`)

// 测试2: 护甲装备
console.log('\n\n=== 测试2: 护甲装备 ===')

// 装备防弹背心
console.log(`\n2.1 装备防弹背心:`)
const equipKevlar = WeaponSystem.equipArmor(soldier, 'kevlar_vest')
console.log(`  ${equipKevlar.description}`)
console.log(`  成功: ${equipKevlar.success}`)
if (equipKevlar.success) {
  console.log(`  当前护甲: ${soldier.currentArmor.name}`)
  console.log(`  防护值: ${soldier.currentArmor.protection}`)
}

// 切换到皮夹克
console.log(`\n2.2 切换到皮夹克:`)
const equipLeather = WeaponSystem.equipArmor(soldier, 'leather_jacket')
console.log(`  ${equipLeather.description}`)
console.log(`  成功: ${equipLeather.success}`)

// 测试3: 弹药管理
console.log('\n\n=== 测试3: 弹药管理 ===')

// 切换回手枪进行弹药测试
WeaponSystem.equipWeapon(soldier, 'pistol')

console.log(`当前武器: ${soldier.currentWeapon.name}`)
console.log(`弹药状态: ${soldier.currentWeapon.currentAmmo}/${soldier.currentWeapon.ammo}`)
console.log(`库存弹药: ${WeaponSystem.getAmmoCount(soldier, 'pistol_ammo')}`)

// 模拟射击消耗弹药
console.log(`\n3.1 射击消耗弹药:`)
for (let i = 0; i < 4; i++) {
  const shootResult = WeaponSystem.consumeAmmoForShooting(soldier.currentWeapon, 1)
  console.log(`  第${i + 1}发: ${shootResult.description || shootResult.reason}`)
  if (!shootResult.success) break
}

console.log(`射击后弹药: ${soldier.currentWeapon.currentAmmo}/${soldier.currentWeapon.ammo}`)

// 装填弹药
console.log(`\n3.2 装填弹药:`)
const reloadResult = WeaponSystem.reloadWeapon(soldier)
console.log(`  ${reloadResult.description}`)
console.log(`  成功: ${reloadResult.success}`)
if (reloadResult.success) {
  console.log(`  装填后弹药: ${soldier.currentWeapon.currentAmmo}/${soldier.currentWeapon.ammo}`)
  console.log(`  剩余库存: ${WeaponSystem.getAmmoCount(soldier, 'pistol_ammo')}`)
}

// 测试4: 武器故障和修理
console.log('\n\n=== 测试4: 武器故障和修理 ===')

// 模拟武器故障
console.log(`\n4.1 模拟武器故障:`)
const malfunctionRoll = 100 // 故障投掷
const isMalfunction = WeaponSystem.checkWeaponMalfunction(soldier.currentWeapon, malfunctionRoll)
console.log(`  投掷结果: ${malfunctionRoll}`)
console.log(`  故障阈值: ${soldier.currentWeapon.malfunction}`)
console.log(`  是否故障: ${isMalfunction}`)

if (isMalfunction) {
  soldier.currentWeapon.malfunctioned = true
  console.log(`  ${soldier.currentWeapon.name} 发生故障！`)
  
  // 尝试修理
  console.log(`\n4.2 尝试修理武器:`)
  const repairResult = WeaponSystem.repairWeapon(soldier, soldier.currentWeapon)
  console.log(`  ${repairResult.description}`)
  console.log(`  修理检定: ${repairResult.roll} vs ${repairResult.skill}`)
  console.log(`  修理成功: ${repairResult.success}`)
}

// 测试5: 不同武器类型
console.log('\n\n=== 测试5: 不同武器类型测试 ===')

const weaponsToTest = ['pistol', 'rifle', 'shotgun', 'sword', 'knife']

weaponsToTest.forEach(weaponId => {
  console.log(`\n5.${weaponsToTest.indexOf(weaponId) + 1} ${weaponId}:`)
  
  const weaponDetails = WeaponSystem.getWeaponDetails(weaponId)
  if (weaponDetails) {
    console.log(`  名称: ${weaponDetails.name}`)
    console.log(`  类型: ${weaponDetails.type}`)
    console.log(`  伤害: ${weaponDetails.damage}`)
    console.log(`  技能: ${weaponDetails.skill}`)
    console.log(`  重量: ${weaponDetails.weight}kg`)
    console.log(`  双手武器: ${weaponDetails.twoHanded ? '是' : '否'}`)
    console.log(`  可隐藏: ${weaponDetails.concealable ? '是' : '否'}`)
    
    if (weaponDetails.ammoType) {
      console.log(`  弹药类型: ${weaponDetails.ammoType}`)
      console.log(`  弹药容量: ${weaponDetails.ammo}`)
      console.log(`  装填时间: ${weaponDetails.reloadTime}回合`)
    }
  }
})

// 测试6: 霰弹枪距离伤害
console.log('\n\n=== 测试6: 霰弹枪距离伤害 ===')

WeaponSystem.equipWeapon(soldier, 'shotgun')
const shotgun = soldier.currentWeapon

console.log(`武器: ${shotgun.name}`)
console.log(`基础伤害: ${shotgun.damage}`)

const distances = [3, 7, 15, 25]
distances.forEach(distance => {
  const damage = WeaponSystem.getWeaponDamage(shotgun, distance)
  console.log(`  距离${distance}米: ${damage}`)
})

// 测试7: 装备时间计算
console.log('\n\n=== 测试7: 装备时间计算 ===')

const equipmentTests = [
  { from: null, to: 'pistol', desc: '装备手枪' },
  { from: 'pistol', to: 'rifle', desc: '从手枪切换到步枪' },
  { from: 'rifle', to: 'sword', desc: '从步枪切换到剑' },
  { from: 'sword', to: 'two_handed_sword', desc: '从单手剑切换到双手剑' }
]

equipmentTests.forEach((test, index) => {
  const fromWeapon = test.from ? WeaponSystem.getWeaponDetails(test.from) : null
  const toWeapon = WeaponSystem.getWeaponDetails(test.to)
  
  let time
  if (test.from === null) {
    time = WeaponSystem.calculateEquipTime(toWeapon, null)
  } else {
    time = WeaponSystem.calculateSwitchTime(fromWeapon, toWeapon)
  }
  
  console.log(`  ${test.desc}: ${time}回合`)
})

// 测试8: 角色武器库存
console.log('\n\n=== 测试8: 角色武器库存 ===')

const characterWeapons = WeaponSystem.getCharacterWeapons(soldier)
console.log(`${soldier.name} 拥有的武器:`)

characterWeapons.forEach(weapon => {
  console.log(`  ${weapon.name}:`)
  console.log(`    数量: ${weapon.quantity}`)
  console.log(`    类型: ${weapon.type}`)
  console.log(`    伤害: ${weapon.damage}`)
  if (weapon.currentAmmo !== undefined) {
    console.log(`    弹药: ${weapon.currentAmmo}/${weapon.ammo}`)
  }
})

// 测试9: 护甲系统
console.log('\n\n=== 测试9: 护甲系统详细测试 ===')

const armorTypes = ['none', 'leather_jacket', 'kevlar_vest', 'riot_gear', 'medieval_armor']

armorTypes.forEach(armorId => {
  const armorDetails = WeaponSystem.getArmorDetails(armorId)
  if (armorDetails) {
    console.log(`\n${armorDetails.name}:`)
    console.log(`  防护值: ${armorDetails.protection}`)
    console.log(`  覆盖范围: ${armorDetails.coverage}`)
    console.log(`  重量: ${armorDetails.weight}kg`)
    console.log(`  可隐藏: ${armorDetails.concealable ? '是' : '否'}`)
    if (armorDetails.dexterityPenalty) {
      console.log(`  敏捷惩罚: -${armorDetails.dexterityPenalty}`)
    }
    if (armorDetails.type) {
      console.log(`  类型: ${armorDetails.type}`)
    }
  }
})

// 测试10: 弹药消耗和补充
console.log('\n\n=== 测试10: 弹药消耗和补充 ===')

console.log(`初始弹药库存:`)
console.log(`  手枪弹药: ${WeaponSystem.getAmmoCount(soldier, 'pistol_ammo')}`)
console.log(`  步枪弹药: ${WeaponSystem.getAmmoCount(soldier, 'rifle_ammo')}`)
console.log(`  霰弹: ${WeaponSystem.getAmmoCount(soldier, 'shotgun_shells')}`)

// 模拟大量射击
console.log(`\n模拟连续射击:`)
WeaponSystem.equipWeapon(soldier, 'rifle')

for (let i = 0; i < 5; i++) {
  const shootResult = WeaponSystem.consumeAmmoForShooting(soldier.currentWeapon, 1)
  console.log(`  射击${i + 1}: ${shootResult.success ? `剩余${shootResult.remainingAmmo}发` : shootResult.reason}`)
  
  if (!shootResult.success) {
    // 尝试装填
    const reloadResult = WeaponSystem.reloadWeapon(soldier)
    console.log(`    装填: ${reloadResult.success ? reloadResult.description : reloadResult.reason}`)
    if (reloadResult.success) {
      // 装填成功后继续射击
      const retryShoot = WeaponSystem.consumeAmmoForShooting(soldier.currentWeapon, 1)
      console.log(`    重新射击: ${retryShoot.success ? `剩余${retryShoot.remainingAmmo}发` : retryShoot.reason}`)
    }
  }
}

console.log(`\n最终弹药库存:`)
console.log(`  手枪弹药: ${WeaponSystem.getAmmoCount(soldier, 'pistol_ammo')}`)
console.log(`  步枪弹药: ${WeaponSystem.getAmmoCount(soldier, 'rifle_ammo')}`)
console.log(`  霰弹: ${WeaponSystem.getAmmoCount(soldier, 'shotgun_shells')}`)

console.log('\n=== 武器装备系统测试完成 ===')
console.log('所有武器装备功能已验证，弹药管理和护甲系统运行正常！')