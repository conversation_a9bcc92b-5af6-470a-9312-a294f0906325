<template>
  <div class="combat-test">
    <h1>战斗系统测试页面</h1>
    
    <!-- 测试按钮 -->
    <div class="test-controls">
      <button @click="toggleCombat" class="test-btn">
        {{ combatActive ? '结束战斗' : '开始战斗' }}
      </button>
      <button @click="addTestLog" class="test-btn">
        添加测试日志
      </button>
    </div>
    
    <!-- 战斗日志测试 -->
    <div class="combat-log-test">
      <h2>战斗日志测试</h2>
      <CombatLog 
        :combat-logs="testLogs"
        @clear-log="clearTestLogs"
        @toggle-auto-scroll="handleAutoScroll"
      />
    </div>
    
    <!-- 先攻追踪器测试 -->
    <div class="initiative-test" v-if="combatActive">
      <h2>先攻追踪器测试</h2>
      <InitiativeTracker
        :initiative-order="testInitiative"
        :current-round="currentRound"
        :current-turn="currentTurn"
        @participant-selected="handleParticipantSelect"
      />
    </div>
  </div>
</template>

<script>
import CombatLog from '@/components/combat/CombatLog.vue'
import InitiativeTracker from '@/components/combat/InitiativeTracker.vue'

export default {
  name: 'CombatTest',
  components: {
    CombatLog,
    InitiativeTracker
  },
  
  data() {
    return {
      combatActive: false,
      currentRound: 1,
      currentTurn: 0,
      
      testLogs: [
        {
          id: 1,
          type: 'system',
          message: '战斗开始！',
          timestamp: new Date(),
          round: 1
        },
        {
          id: 2,
          type: 'attack',
          message: '侦探约翰对邪教徒发起攻击',
          timestamp: new Date(),
          round: 1,
          participant: {
            name: '侦探约翰',
            avatar: '/images/default-avatar.png'
          },
          diceRoll: {
            formula: '1d100',
            rolls: [45],
            total: 45
          },
          successLevel: 'regular'
        },
        {
          id: 3,
          type: 'damage',
          message: '攻击命中，造成8点伤害',
          timestamp: new Date(),
          round: 1,
          severity: 'critical'
        }
      ],
      
      testInitiative: [
        {
          id: 1,
          name: '侦探约翰',
          initiative: 85,
          isPlayer: true,
          avatar: '/images/default-avatar.png',
          currentHP: 85,
          maxHP: 100,
          hasActed: false
        },
        {
          id: 2,
          name: '邪教徒',
          initiative: 72,
          isPlayer: false,
          avatar: '/images/default-enemy.png',
          currentHP: 42,
          maxHP: 50,
          hasActed: true
        },
        {
          id: 3,
          name: '记者玛丽',
          initiative: 68,
          isPlayer: true,
          avatar: '/images/default-avatar.png',
          currentHP: 78,
          maxHP: 90,
          hasActed: false
        }
      ]
    }
  },
  
  methods: {
    toggleCombat() {
      this.combatActive = !this.combatActive
      
      if (this.combatActive) {
        this.addTestLog({
          type: 'system',
          message: '战斗开始！',
          round: this.currentRound
        })
      } else {
        this.addTestLog({
          type: 'system',
          message: '战斗结束！'
        })
      }
    },
    
    addTestLog(customLog = null) {
      const newLog = customLog || {
        id: Date.now(),
        type: 'skill',
        message: `测试日志 - ${new Date().toLocaleTimeString()}`,
        timestamp: new Date(),
        round: this.currentRound,
        participant: {
          name: '测试角色',
          avatar: '/images/default-avatar.png'
        }
      }
      
      if (!newLog.id) {
        newLog.id = Date.now()
      }
      if (!newLog.timestamp) {
        newLog.timestamp = new Date()
      }
      
      this.testLogs.push(newLog)
    },
    
    clearTestLogs() {
      this.testLogs = []
    },
    
    handleAutoScroll(enabled) {
      console.log('自动滚动:', enabled)
    },
    
    handleParticipantSelect(participant) {
      console.log('选中参与者:', participant)
    }
  }
}
</script>

<style scoped>
.combat-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.test-btn {
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.test-btn:hover {
  background: #0056b3;
}

.combat-log-test {
  margin-bottom: 30px;
}

.combat-log-test h2,
.initiative-test h2 {
  margin-bottom: 15px;
  color: #333;
}

.initiative-test {
  margin-top: 30px;
}

/* 为测试页面调整战斗日志高度 */
.combat-log-test :deep(.combat-log) {
  height: 400px;
}
</style>