"""
COC 7版战斗系统数据同步服务
处理角色数据的实时同步、状态管理和数据一致性
"""

import json
import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

import mysql.connector
from mysql.connector import Error
import redis
import websockets

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SyncEventType(Enum):
    """同步事件类型"""
    CHARACTER_UPDATE = "character_update"
    COMBAT_STATE_CHANGE = "combat_state_change"
    POSITION_UPDATE = "position_update"
    STATUS_EFFECT_CHANGE = "status_effect_change"
    INVENTORY_UPDATE = "inventory_update"
    ATTRIBUTE_CHANGE = "attribute_change"
    SKILL_UPDATE = "skill_update"

@dataclass
class SyncEvent:
    """同步事件数据结构"""
    event_type: SyncEventType
    session_id: int
    character_id: int
    data: Dict[str, Any]
    timestamp: datetime
    source: str  # 'client' or 'server'

class CombatDataSync:
    """战斗数据同步管理器"""
    
    def __init__(self, db_config: Dict, redis_config: Dict):
        self.db_config = db_config
        self.redis_config = redis_config
        self.db_connection = None
        self.redis_client = None
        self.active_sessions: Dict[int, Dict] = {}
        self.client_connections: Dict[str, websockets.WebSocketServerProtocol] = {}
        
    async def initialize(self):
        """初始化数据库和Redis连接"""
        try:
            # 初始化MySQL连接
            self.db_connection = mysql.connector.connect(**self.db_config)
            logger.info("数据库连接已建立")
            
            # 初始化Redis连接
            self.redis_client = redis.Redis(**self.redis_config)
            await self.redis_client.ping()
            logger.info("Redis连接已建立")
            
            # 恢复活跃的战斗会话
            await self.restore_active_sessions()
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def restore_active_sessions(self):
        """恢复活跃的战斗会话"""
        try:
            cursor = self.db_connection.cursor(dictionary=True)
            cursor.execute("""
                SELECT id, room_id, participants, current_round, current_turn, 
                       initiative_order, battlefield_config
                FROM combat_sessions 
                WHERE status IN ('active', 'paused')
            """)
            
            sessions = cursor.fetchall()
            for session in sessions:
                self.active_sessions[session['id']] = {
                    'room_id': session['room_id'],
                    'participants': json.loads(session['participants']),
                    'current_round': session['current_round'],
                    'current_turn': session['current_turn'],
                    'initiative_order': json.loads(session['initiative_order'] or '[]'),
                    'battlefield_config': json.loads(session['battlefield_config']),
                    'character_states': {},
                    'last_sync': datetime.now()
                }
                
                # 加载角色状态
                await self.load_character_states(session['id'])
            
            logger.info(f"恢复了 {len(sessions)} 个活跃战斗会话")
            
        except Error as e:
            logger.error(f"恢复会话失败: {e}")
    
    async def load_character_states(self, session_id: int):
        """加载战斗会话中的角色状态"""
        try:
            cursor = self.db_connection.cursor(dictionary=True)
            
            # 获取最新的角色状态快照
            cursor.execute("""
                SELECT character_id, current_hp, current_mp, current_san, current_luck,
                       equipped_weapons, equipped_armor, inventory, conditions,
                       temporary_modifiers, position, facing, has_acted, action_points
                FROM character_combat_snapshots 
                WHERE session_id = %s 
                AND snapshot_type = 'round_start'
                AND round_number = (
                    SELECT current_round FROM combat_sessions WHERE id = %s
                )
            """, (session_id, session_id))
            
            snapshots = cursor.fetchall()
            session = self.active_sessions[session_id]
            
            for snapshot in snapshots:
                character_id = snapshot['character_id']
                session['character_states'][character_id] = {
                    'current_hp': snapshot['current_hp'],
                    'current_mp': snapshot['current_mp'],
                    'current_san': snapshot['current_san'],
                    'current_luck': snapshot['current_luck'],
                    'equipped_weapons': json.loads(snapshot['equipped_weapons'] or '[]'),
                    'equipped_armor': json.loads(snapshot['equipped_armor'] or '[]'),
                    'inventory': json.loads(snapshot['inventory'] or '{}'),
                    'conditions': json.loads(snapshot['conditions'] or '[]'),
                    'temporary_modifiers': json.loads(snapshot['temporary_modifiers'] or '{}'),
                    'position': json.loads(snapshot['position'] or '{"x": 0, "y": 0}'),
                    'facing': snapshot['facing'],
                    'has_acted': snapshot['has_acted'],
                    'action_points': snapshot['action_points'],
                    'last_update': datetime.now()
                }
            
        except Error as e:
            logger.error(f"加载角色状态失败: {e}")
    
    async def sync_character_data(self, sync_event: SyncEvent):
        """同步角色数据"""
        try:
            session_id = sync_event.session_id
            character_id = sync_event.character_id
            
            if session_id not in self.active_sessions:
                logger.warning(f"会话 {session_id} 不存在或未激活")
                return False
            
            session = self.active_sessions[session_id]
            
            # 更新内存中的角色状态
            if character_id not in session['character_states']:
                session['character_states'][character_id] = {}
            
            character_state = session['character_states'][character_id]
            
            # 根据事件类型更新数据
            if sync_event.event_type == SyncEventType.CHARACTER_UPDATE:
                character_state.update(sync_event.data)
                
            elif sync_event.event_type == SyncEventType.POSITION_UPDATE:
                character_state['position'] = sync_event.data['position']
                character_state['facing'] = sync_event.data.get('facing', character_state.get('facing', 0))
                
            elif sync_event.event_type == SyncEventType.STATUS_EFFECT_CHANGE:
                character_state['conditions'] = sync_event.data['conditions']
                
            elif sync_event.event_type == SyncEventType.INVENTORY_UPDATE:
                character_state['inventory'] = sync_event.data['inventory']
                character_state['equipped_weapons'] = sync_event.data.get('equipped_weapons', character_state.get('equipped_weapons', []))
                character_state['equipped_armor'] = sync_event.data.get('equipped_armor', character_state.get('equipped_armor', []))
                
            elif sync_event.event_type == SyncEventType.ATTRIBUTE_CHANGE:
                for attr, value in sync_event.data.items():
                    character_state[attr] = value
            
            character_state['last_update'] = datetime.now()
            
            # 保存到数据库
            await self.save_character_snapshot(sync_event)
            
            # 缓存到Redis
            await self.cache_character_state(session_id, character_id, character_state)
            
            # 广播更新给所有客户端
            await self.broadcast_update(sync_event)
            
            logger.info(f"角色 {character_id} 数据同步完成")
            return True
            
        except Exception as e:
            logger.error(f"同步角色数据失败: {e}")
            return False
    
    async def save_character_snapshot(self, sync_event: SyncEvent):
        """保存角色状态快照到数据库"""
        try:
            session_id = sync_event.session_id
            character_id = sync_event.character_id
            session = self.active_sessions[session_id]
            character_state = session['character_states'][character_id]
            
            cursor = self.db_connection.cursor()
            
            # 插入新的快照记录
            cursor.execute("""
                INSERT INTO character_combat_snapshots (
                    session_id, character_id, snapshot_type, round_number, turn_number,
                    current_hp, current_mp, current_san, current_luck,
                    equipped_weapons, equipped_armor, inventory, conditions,
                    temporary_modifiers, position, facing, has_acted, action_points
                ) VALUES (
                    %s, %s, 'action_after', %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s
                )
            """, (
                session_id, character_id, session['current_round'], session['current_turn'],
                character_state.get('current_hp', 0),
                character_state.get('current_mp', 0),
                character_state.get('current_san', 0),
                character_state.get('current_luck', 0),
                json.dumps(character_state.get('equipped_weapons', [])),
                json.dumps(character_state.get('equipped_armor', [])),
                json.dumps(character_state.get('inventory', {})),
                json.dumps(character_state.get('conditions', [])),
                json.dumps(character_state.get('temporary_modifiers', {})),
                json.dumps(character_state.get('position', {'x': 0, 'y': 0})),
                character_state.get('facing', 0),
                character_state.get('has_acted', False),
                character_state.get('action_points', 1)
            ))
            
            self.db_connection.commit()
            
        except Error as e:
            logger.error(f"保存角色快照失败: {e}")
            self.db_connection.rollback()
    
    async def cache_character_state(self, session_id: int, character_id: int, character_state: Dict):
        """缓存角色状态到Redis"""
        try:
            cache_key = f"combat:session:{session_id}:character:{character_id}"
            
            # 序列化状态数据
            cache_data = {
                **character_state,
                'last_update': character_state['last_update'].isoformat()
            }
            
            # 设置缓存，过期时间1小时
            await self.redis_client.setex(
                cache_key, 
                3600, 
                json.dumps(cache_data, default=str)
            )
            
        except Exception as e:
            logger.error(f"缓存角色状态失败: {e}")
    
    async def get_cached_character_state(self, session_id: int, character_id: int) -> Optional[Dict]:
        """从Redis获取缓存的角色状态"""
        try:
            cache_key = f"combat:session:{session_id}:character:{character_id}"
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                return json.loads(cached_data)
            return None
            
        except Exception as e:
            logger.error(f"获取缓存角色状态失败: {e}")
            return None
    
    async def broadcast_update(self, sync_event: SyncEvent):
        """广播更新给所有相关客户端"""
        try:
            session_id = sync_event.session_id
            session = self.active_sessions.get(session_id)
            
            if not session:
                return
            
            # 构建广播消息
            message = {
                'type': 'combat_sync_update',
                'event_type': sync_event.event_type.value,
                'session_id': session_id,
                'character_id': sync_event.character_id,
                'data': sync_event.data,
                'timestamp': sync_event.timestamp.isoformat(),
                'round': session['current_round'],
                'turn': session['current_turn']
            }
            
            # 获取房间内的所有连接
            room_id = session['room_id']
            room_connections = [
                conn for conn_id, conn in self.client_connections.items()
                if conn_id.startswith(f"room_{room_id}_")
            ]
            
            # 并发发送消息
            if room_connections:
                await asyncio.gather(
                    *[self.send_message(conn, message) for conn in room_connections],
                    return_exceptions=True
                )
            
        except Exception as e:
            logger.error(f"广播更新失败: {e}")
    
    async def send_message(self, websocket, message: Dict):
        """发送消息给WebSocket客户端"""
        try:
            await websocket.send(json.dumps(message))
        except websockets.exceptions.ConnectionClosed:
            # 连接已关闭，从活跃连接中移除
            self.remove_connection(websocket)
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
    
    def remove_connection(self, websocket):
        """移除断开的连接"""
        to_remove = []
        for conn_id, conn in self.client_connections.items():
            if conn == websocket:
                to_remove.append(conn_id)
        
        for conn_id in to_remove:
            del self.client_connections[conn_id]
    
    async def handle_client_connection(self, websocket, path):
        """处理客户端WebSocket连接"""
        try:
            # 从路径中提取连接信息
            path_parts = path.strip('/').split('/')
            if len(path_parts) >= 3:
                room_id = path_parts[1]
                user_id = path_parts[2]
                conn_id = f"room_{room_id}_user_{user_id}"
                
                self.client_connections[conn_id] = websocket
                logger.info(f"客户端连接: {conn_id}")
                
                # 发送当前状态
                await self.send_current_state(websocket, int(room_id))
                
                # 监听客户端消息
                async for message in websocket:
                    await self.handle_client_message(websocket, message, conn_id)
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("客户端连接已关闭")
        except Exception as e:
            logger.error(f"处理客户端连接失败: {e}")
        finally:
            self.remove_connection(websocket)
    
    async def send_current_state(self, websocket, room_id: int):
        """发送当前战斗状态给新连接的客户端"""
        try:
            # 查找房间的活跃战斗会话
            active_session = None
            for session_id, session in self.active_sessions.items():
                if session['room_id'] == room_id:
                    active_session = session
                    break
            
            if not active_session:
                return
            
            # 构建当前状态消息
            message = {
                'type': 'combat_state_sync',
                'session_id': session_id,
                'current_round': active_session['current_round'],
                'current_turn': active_session['current_turn'],
                'initiative_order': active_session['initiative_order'],
                'battlefield_config': active_session['battlefield_config'],
                'character_states': active_session['character_states'],
                'timestamp': datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(message, default=str))
            
        except Exception as e:
            logger.error(f"发送当前状态失败: {e}")
    
    async def handle_client_message(self, websocket, message: str, conn_id: str):
        """处理客户端消息"""
        try:
            data = json.loads(message)
            message_type = data.get('type')
            
            if message_type == 'sync_request':
                # 处理同步请求
                sync_event = SyncEvent(
                    event_type=SyncEventType(data['event_type']),
                    session_id=data['session_id'],
                    character_id=data['character_id'],
                    data=data['data'],
                    timestamp=datetime.now(),
                    source='client'
                )
                
                await self.sync_character_data(sync_event)
                
            elif message_type == 'state_request':
                # 处理状态请求
                session_id = data['session_id']
                character_id = data.get('character_id')
                
                if character_id:
                    # 请求特定角色状态
                    state = await self.get_character_state(session_id, character_id)
                    response = {
                        'type': 'character_state_response',
                        'session_id': session_id,
                        'character_id': character_id,
                        'state': state
                    }
                else:
                    # 请求整个会话状态
                    state = await self.get_session_state(session_id)
                    response = {
                        'type': 'session_state_response',
                        'session_id': session_id,
                        'state': state
                    }
                
                await websocket.send(json.dumps(response, default=str))
                
        except Exception as e:
            logger.error(f"处理客户端消息失败: {e}")
    
    async def get_character_state(self, session_id: int, character_id: int) -> Optional[Dict]:
        """获取角色状态"""
        try:
            # 先尝试从缓存获取
            cached_state = await self.get_cached_character_state(session_id, character_id)
            if cached_state:
                return cached_state
            
            # 从内存获取
            session = self.active_sessions.get(session_id)
            if session and character_id in session['character_states']:
                return session['character_states'][character_id]
            
            # 从数据库获取
            cursor = self.db_connection.cursor(dictionary=True)
            cursor.execute("""
                SELECT * FROM character_combat_snapshots 
                WHERE session_id = %s AND character_id = %s
                ORDER BY created_at DESC LIMIT 1
            """, (session_id, character_id))
            
            snapshot = cursor.fetchone()
            if snapshot:
                return {
                    'current_hp': snapshot['current_hp'],
                    'current_mp': snapshot['current_mp'],
                    'current_san': snapshot['current_san'],
                    'current_luck': snapshot['current_luck'],
                    'equipped_weapons': json.loads(snapshot['equipped_weapons'] or '[]'),
                    'equipped_armor': json.loads(snapshot['equipped_armor'] or '[]'),
                    'inventory': json.loads(snapshot['inventory'] or '{}'),
                    'conditions': json.loads(snapshot['conditions'] or '[]'),
                    'temporary_modifiers': json.loads(snapshot['temporary_modifiers'] or '{}'),
                    'position': json.loads(snapshot['position'] or '{"x": 0, "y": 0}'),
                    'facing': snapshot['facing'],
                    'has_acted': snapshot['has_acted'],
                    'action_points': snapshot['action_points']
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取角色状态失败: {e}")
            return None
    
    async def get_session_state(self, session_id: int) -> Optional[Dict]:
        """获取会话状态"""
        try:
            session = self.active_sessions.get(session_id)
            if session:
                return {
                    'room_id': session['room_id'],
                    'current_round': session['current_round'],
                    'current_turn': session['current_turn'],
                    'initiative_order': session['initiative_order'],
                    'battlefield_config': session['battlefield_config'],
                    'character_states': session['character_states'],
                    'last_sync': session['last_sync'].isoformat()
                }
            
            return None
            
        except Exception as e:
            logger.error(f"获取会话状态失败: {e}")
            return None
    
    async def start_combat_session(self, room_id: int, session_name: str, 
                                 created_by: int, battlefield_config: Dict, 
                                 participants: List[Dict]) -> int:
        """开始新的战斗会话"""
        try:
            cursor = self.db_connection.cursor()
            
            # 调用存储过程创建战斗会话
            cursor.callproc('StartNewCombat', [
                room_id, session_name, created_by,
                json.dumps(battlefield_config),
                json.dumps(participants)
            ])
            
            # 获取会话ID
            for result in cursor.stored_results():
                session_data = result.fetchone()
                session_id = session_data[0]
            
            # 初始化内存中的会话状态
            self.active_sessions[session_id] = {
                'room_id': room_id,
                'participants': participants,
                'current_round': 1,
                'current_turn': 0,
                'initiative_order': [],
                'battlefield_config': battlefield_config,
                'character_states': {},
                'last_sync': datetime.now()
            }
            
            # 为每个参与者创建初始状态快照
            for participant in participants:
                if participant['type'] == 'character':
                    await self.create_initial_character_snapshot(session_id, participant)
            
            logger.info(f"战斗会话 {session_id} 已创建")
            return session_id
            
        except Exception as e:
            logger.error(f"创建战斗会话失败: {e}")
            raise
    
    async def create_initial_character_snapshot(self, session_id: int, participant: Dict):
        """创建角色的初始状态快照"""
        try:
            character_id = participant['character_id']
            character_data = participant['character_data']
            
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO character_combat_snapshots (
                    session_id, character_id, snapshot_type, round_number,
                    current_hp, current_mp, current_san, current_luck,
                    equipped_weapons, equipped_armor, inventory, conditions,
                    temporary_modifiers, position, facing, has_acted, action_points
                ) VALUES (
                    %s, %s, 'initial', 1,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s, %s
                )
            """, (
                session_id, character_id,
                character_data.get('current_hp', character_data.get('hit_points', 0)),
                character_data.get('current_mp', character_data.get('magic_points', 0)),
                character_data.get('current_san', character_data.get('sanity', 0)),
                character_data.get('current_luck', character_data.get('luck', 0)),
                json.dumps(character_data.get('equipped_weapons', [])),
                json.dumps(character_data.get('equipped_armor', [])),
                json.dumps(character_data.get('inventory', {})),
                json.dumps([]),  # 初始无状态效果
                json.dumps({}),  # 初始无临时修正
                json.dumps(participant.get('initial_position', {'x': 0, 'y': 0})),
                0,  # 初始面向
                False,  # 未行动
                1  # 初始行动点
            ))
            
            self.db_connection.commit()
            
        except Exception as e:
            logger.error(f"创建初始角色快照失败: {e}")
            self.db_connection.rollback()
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 关闭所有WebSocket连接
            for websocket in self.client_connections.values():
                await websocket.close()
            
            # 关闭数据库连接
            if self.db_connection:
                self.db_connection.close()
            
            # 关闭Redis连接
            if self.redis_client:
                await self.redis_client.close()
            
            logger.info("数据同步服务已清理")
            
        except Exception as e:
            logger.error(f"清理资源失败: {e}")

# 使用示例
async def main():
    """主函数示例"""
    db_config = {
        'host': 'localhost',
        'database': 'coc_trpgs',
        'user': 'root',
        'password': 'password'
    }
    
    redis_config = {
        'host': 'localhost',
        'port': 6379,
        'db': 0
    }
    
    sync_service = CombatDataSync(db_config, redis_config)
    
    try:
        await sync_service.initialize()
        
        # 启动WebSocket服务器
        start_server = websockets.serve(
            sync_service.handle_client_connection,
            "localhost",
            8765
        )
        
        logger.info("战斗数据同步服务已启动")
        await start_server
        
        # 保持服务运行
        await asyncio.Future()  # 永远等待
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    finally:
        await sync_service.cleanup()

if __name__ == "__main__":
    asyncio.run(main())