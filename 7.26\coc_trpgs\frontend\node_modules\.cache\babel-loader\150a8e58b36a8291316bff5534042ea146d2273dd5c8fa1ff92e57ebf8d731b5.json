{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode, withModifiers as _withModifiers, normalizeClass as _normalizeClass } from \"vue\";\nvar _hoisted_1 = [\"transform\"];\nvar _hoisted_2 = [\"r\"];\nvar _hoisted_3 = [\"r\"];\nvar _hoisted_4 = [\"r\"];\nvar _hoisted_5 = [\"r\"];\nvar _hoisted_6 = {\n  \"class\": \"character-body\"\n};\nvar _hoisted_7 = [\"r\", \"fill\"];\nvar _hoisted_8 = [\"id\"];\nvar _hoisted_9 = [\"href\", \"width\", \"height\"];\nvar _hoisted_10 = [\"r\", \"fill\"];\nvar _hoisted_11 = [\"r\"];\nvar _hoisted_12 = [\"r\", \"stroke\", \"stroke-dasharray\"];\nvar _hoisted_13 = [\"y\", \"fill\"];\nvar _hoisted_14 = [\"transform\"];\nvar _hoisted_15 = [\"transform\"];\nvar _hoisted_16 = {\n  \"text-anchor\": \"middle\",\n  dy: \"4\",\n  \"font-size\": \"10\",\n  fill: \"white\"\n};\nvar _hoisted_17 = [\"transform\"];\nvar _hoisted_18 = {\n  key: 0,\n  r: \"6\",\n  fill: \"#ff9800\",\n  stroke: \"#fff\",\n  \"stroke-width\": \"1\"\n};\nvar _hoisted_19 = {\n  key: 1,\n  \"text-anchor\": \"middle\",\n  dy: \"3\",\n  \"font-size\": \"8\",\n  fill: \"white\",\n  \"font-weight\": \"bold\"\n};\nvar _hoisted_20 = {\n  key: 2,\n  r: \"6\",\n  fill: \"#9c27b0\",\n  stroke: \"#fff\",\n  \"stroke-width\": \"1\"\n};\nvar _hoisted_21 = {\n  key: 3,\n  \"text-anchor\": \"middle\",\n  dy: \"3\",\n  \"font-size\": \"8\",\n  fill: \"white\",\n  \"font-weight\": \"bold\"\n};\nvar _hoisted_22 = {\n  key: 4,\n  \"class\": \"damage-animation\"\n};\nvar _hoisted_23 = [\"y\", \"fill\"];\nvar _hoisted_24 = [\"values\"];\nvar _hoisted_25 = [\"r\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 2D战场上的角色令牌 \"), _createElementVNode(\"g\", {\n    \"class\": _normalizeClass([\"character-token\", {\n      selected: $props.selected,\n      'current-turn': $props.currentTurn,\n      'can-move': $props.canMove,\n      'can-act': $props.canAct,\n      player: $props.character.isPlayer,\n      enemy: !$props.character.isPlayer\n    }]),\n    transform: \"translate(\".concat($options.x, \", \").concat($options.y, \")\"),\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return _ctx.$emit('select', $props.character);\n    }),\n    onContextmenu: _cache[2] || (_cache[2] = _withModifiers(function ($event) {\n      return _ctx.$emit('context-menu', $props.character, $event);\n    }, [\"prevent\"]))\n  }, [_createCommentVNode(\" 选中光环 \"), $props.selected ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 0,\n    r: $data.tokenRadius + 8,\n    fill: \"none\",\n    stroke: \"#3498db\",\n    \"stroke-width\": \"3\",\n    opacity: \"0.8\"\n  }, _cache[3] || (_cache[3] = [_createElementVNode(\"animate\", {\n    attributeName: \"stroke-opacity\",\n    values: \"0.8;0.3;0.8\",\n    dur: \"2s\",\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_2)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 当前回合光环 \"), $props.currentTurn ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 1,\n    r: $data.tokenRadius + 12,\n    fill: \"none\",\n    stroke: \"#e74c3c\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"5,5\",\n    opacity: \"0.9\"\n  }, _cache[4] || (_cache[4] = [_createElementVNode(\"animateTransform\", {\n    attributeName: \"transform\",\n    type: \"rotate\",\n    values: \"0;360\",\n    dur: \"3s\",\n    repeatCount: \"indefinite\"\n  }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 移动范围指示 \"), $props.showMovementRange && $props.canMove ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 2,\n    r: $options.movementRange * $props.gridSize,\n    fill: \"rgba(76, 175, 80, 0.1)\",\n    stroke: \"#4caf50\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"3,3\",\n    opacity: \"0.6\"\n  }, null, 8 /* PROPS */, _hoisted_4)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 攻击范围指示 \"), $props.showAttackRange && $props.canAct ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 3,\n    r: $options.attackRange * $props.gridSize,\n    fill: \"rgba(244, 67, 54, 0.1)\",\n    stroke: \"#f44336\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"5,5\",\n    opacity: \"0.5\"\n  }, null, 8 /* PROPS */, _hoisted_5)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 角色主体 \"), _createElementVNode(\"g\", _hoisted_6, [_createCommentVNode(\" 背景圆 \"), _createElementVNode(\"circle\", {\n    r: $data.tokenRadius,\n    fill: $props.character.isPlayer ? '#2196f3' : '#f44336',\n    stroke: \"#fff\",\n    \"stroke-width\": \"2\",\n    opacity: \"0.9\"\n  }, null, 8 /* PROPS */, _hoisted_7), _createCommentVNode(\" 角色头像 \"), _createElementVNode(\"defs\", null, [_createElementVNode(\"pattern\", {\n    id: \"avatar-\".concat($props.character.id),\n    patternUnits: \"objectBoundingBox\",\n    width: \"100%\",\n    height: \"100%\"\n  }, [_createElementVNode(\"image\", {\n    href: $props.character.avatar || $options.getDefaultAvatar(),\n    x: \"0\",\n    y: \"0\",\n    width: $data.tokenRadius * 2,\n    height: $data.tokenRadius * 2,\n    preserveAspectRatio: \"xMidYMid slice\"\n  }, null, 8 /* PROPS */, _hoisted_9)], 8 /* PROPS */, _hoisted_8)]), _createElementVNode(\"circle\", {\n    r: $data.tokenRadius - 3,\n    fill: \"url(#avatar-\".concat($props.character.id, \")\"),\n    opacity: \"0.95\"\n  }, null, 8 /* PROPS */, _hoisted_10), _createCommentVNode(\" 生命值环 \"), _createElementVNode(\"circle\", {\n    r: $data.tokenRadius + 2,\n    fill: \"none\",\n    stroke: \"#ddd\",\n    \"stroke-width\": \"4\",\n    opacity: \"0.3\"\n  }, null, 8 /* PROPS */, _hoisted_11), _createElementVNode(\"circle\", {\n    r: $data.tokenRadius + 2,\n    fill: \"none\",\n    stroke: $options.getHealthColor(),\n    \"stroke-width\": \"4\",\n    \"stroke-dasharray\": $options.getHealthDashArray(),\n    \"stroke-linecap\": \"round\",\n    transform: \"rotate(-90)\",\n    opacity: \"0.8\"\n  }, null, 8 /* PROPS */, _hoisted_12)]), _createCommentVNode(\" 角色名称 \"), _createElementVNode(\"text\", {\n    y: $data.tokenRadius + 20,\n    \"text-anchor\": \"middle\",\n    \"font-size\": \"12\",\n    \"font-weight\": \"bold\",\n    fill: $props.character.isPlayer ? '#2196f3' : '#f44336',\n    \"class\": \"character-name-text\"\n  }, _toDisplayString($props.character.name), 9 /* TEXT, PROPS */, _hoisted_13), _createCommentVNode(\" 状态效果图标 \"), _createElementVNode(\"g\", {\n    \"class\": \"status-effects\",\n    transform: \"translate(\".concat(-$data.tokenRadius, \", \").concat(-$data.tokenRadius - 15, \")\")\n  }, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.character.conditions, function (condition, index) {\n    return _openBlock(), _createElementBlock(\"g\", {\n      key: condition,\n      transform: \"translate(\".concat(index * 16, \", 0)\")\n    }, [_cache[5] || (_cache[5] = _createElementVNode(\"circle\", {\n      r: \"8\",\n      fill: \"#333\",\n      opacity: \"0.8\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"text\", _hoisted_16, _toDisplayString($options.getConditionIcon(condition)), 1 /* TEXT */)], 8 /* PROPS */, _hoisted_15);\n  }), 128 /* KEYED_FRAGMENT */))], 8 /* PROPS */, _hoisted_14), _createCommentVNode(\" 行动状态指示器 \"), _createElementVNode(\"g\", {\n    \"class\": \"action-indicators\",\n    transform: \"translate(\".concat($data.tokenRadius - 8, \", \").concat(-$data.tokenRadius + 8, \")\")\n  }, [_createCommentVNode(\" 已行动指示 \"), $props.character.hasActed ? (_openBlock(), _createElementBlock(\"circle\", _hoisted_18)) : _createCommentVNode(\"v-if\", true), $props.character.hasActed ? (_openBlock(), _createElementBlock(\"text\", _hoisted_19, \" ✓ \")) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 延迟行动指示 \"), $props.character.isDelaying ? (_openBlock(), _createElementBlock(\"circle\", _hoisted_20)) : _createCommentVNode(\"v-if\", true), $props.character.isDelaying ? (_openBlock(), _createElementBlock(\"text\", _hoisted_21, \" ⏸ \")) : _createCommentVNode(\"v-if\", true)], 8 /* PROPS */, _hoisted_17), _createCommentVNode(\" 伤害数字动画 \"), $data.damageAnimation ? (_openBlock(), _createElementBlock(\"g\", _hoisted_22, [_createElementVNode(\"text\", {\n    y: -$data.tokenRadius - 10,\n    \"text-anchor\": \"middle\",\n    \"font-size\": \"16\",\n    \"font-weight\": \"bold\",\n    fill: $data.damageAnimation.color,\n    opacity: \"0\"\n  }, [_createTextVNode(_toDisplayString($data.damageAnimation.text) + \" \", 1 /* TEXT */), _createElementVNode(\"animate\", {\n    attributeName: \"y\",\n    values: \"\".concat(-$data.tokenRadius - 10, \";\").concat(-$data.tokenRadius - 40),\n    dur: \"1.5s\"\n  }, null, 8 /* PROPS */, _hoisted_24), _cache[6] || (_cache[6] = _createElementVNode(\"animate\", {\n    attributeName: \"opacity\",\n    values: \"0;1;1;0\",\n    dur: \"1.5s\"\n  }, null, -1 /* CACHED */))], 8 /* PROPS */, _hoisted_23)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 拖拽手柄 (仅在可移动时显示) \"), $props.canMove && $props.selected ? (_openBlock(), _createElementBlock(\"circle\", {\n    key: 5,\n    r: $data.tokenRadius,\n    fill: \"transparent\",\n    stroke: \"none\",\n    style: {\n      \"cursor\": \"move\"\n    },\n    onMousedown: _cache[0] || (_cache[0] = function () {\n      return $options.startDrag && $options.startDrag.apply($options, arguments);\n    })\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_25)) : _createCommentVNode(\"v-if\", true)], 42 /* CLASS, PROPS, NEED_HYDRATION */, _hoisted_1)], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["dy", "fill", "r", "stroke", "_createCommentVNode", "_createElementVNode", "_normalizeClass", "$props", "selected", "currentTurn", "canMove", "canAct", "character", "isPlayer", "transform", "concat", "$options", "x", "y", "onClick", "_cache", "$event", "_ctx", "$emit", "onContextmenu", "_withModifiers", "_createElementBlock", "$data", "tokenRadius", "opacity", "attributeName", "values", "dur", "repeatCount", "type", "showMovementRange", "movementRange", "gridSize", "showAttackRange", "attackRange", "_hoisted_6", "id", "patternUnits", "width", "height", "href", "avatar", "getDefaultAvatar", "preserveAspectRatio", "getHealthColor", "getHealthDashArray", "name", "_hoisted_13", "_Fragment", "_renderList", "conditions", "condition", "index", "key", "_hoisted_16", "_toDisplayString", "getConditionIcon", "hasActed", "_hoisted_18", "_hoisted_19", "is<PERSON><PERSON>ying", "_hoisted_20", "_hoisted_21", "damageAnimation", "_hoisted_22", "color", "text", "style", "onMousedown", "startDrag", "apply", "arguments"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\CharacterToken.vue"], "sourcesContent": ["<template>\r\n  <!-- 2D战场上的角色令牌 -->\r\n  <g \r\n    class=\"character-token\"\r\n    :class=\"{ \r\n      selected, \r\n      'current-turn': currentTurn,\r\n      'can-move': canMove,\r\n      'can-act': canAct,\r\n      player: character.isPlayer,\r\n      enemy: !character.isPlayer\r\n    }\"\r\n    :transform=\"`translate(${x}, ${y})`\"\r\n    @click=\"$emit('select', character)\"\r\n    @contextmenu.prevent=\"$emit('context-menu', character, $event)\"\r\n  >\r\n    <!-- 选中光环 -->\r\n    <circle \r\n      v-if=\"selected\"\r\n      :r=\"tokenRadius + 8\"\r\n      fill=\"none\"\r\n      stroke=\"#3498db\"\r\n      stroke-width=\"3\"\r\n      opacity=\"0.8\"\r\n    >\r\n      <animate \r\n        attributeName=\"stroke-opacity\" \r\n        values=\"0.8;0.3;0.8\" \r\n        dur=\"2s\" \r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- 当前回合光环 -->\r\n    <circle \r\n      v-if=\"currentTurn\"\r\n      :r=\"tokenRadius + 12\"\r\n      fill=\"none\"\r\n      stroke=\"#e74c3c\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"5,5\"\r\n      opacity=\"0.9\"\r\n    >\r\n      <animateTransform\r\n        attributeName=\"transform\"\r\n        type=\"rotate\"\r\n        values=\"0;360\"\r\n        dur=\"3s\"\r\n        repeatCount=\"indefinite\"\r\n      />\r\n    </circle>\r\n    \r\n    <!-- 移动范围指示 -->\r\n    <circle \r\n      v-if=\"showMovementRange && canMove\"\r\n      :r=\"movementRange * gridSize\"\r\n      fill=\"rgba(76, 175, 80, 0.1)\"\r\n      stroke=\"#4caf50\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"3,3\"\r\n      opacity=\"0.6\"\r\n    />\r\n    \r\n    <!-- 攻击范围指示 -->\r\n    <circle \r\n      v-if=\"showAttackRange && canAct\"\r\n      :r=\"attackRange * gridSize\"\r\n      fill=\"rgba(244, 67, 54, 0.1)\"\r\n      stroke=\"#f44336\"\r\n      stroke-width=\"2\"\r\n      stroke-dasharray=\"5,5\"\r\n      opacity=\"0.5\"\r\n    />\r\n    \r\n    <!-- 角色主体 -->\r\n    <g class=\"character-body\">\r\n      <!-- 背景圆 -->\r\n      <circle \r\n        :r=\"tokenRadius\"\r\n        :fill=\"character.isPlayer ? '#2196f3' : '#f44336'\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"2\"\r\n        opacity=\"0.9\"\r\n      />\r\n      \r\n      <!-- 角色头像 -->\r\n      <defs>\r\n        <pattern \r\n          :id=\"`avatar-${character.id}`\" \r\n          patternUnits=\"objectBoundingBox\" \r\n          width=\"100%\" \r\n          height=\"100%\"\r\n        >\r\n          <image \r\n            :href=\"character.avatar || getDefaultAvatar()\"\r\n            x=\"0\" \r\n            y=\"0\" \r\n            :width=\"tokenRadius * 2\" \r\n            :height=\"tokenRadius * 2\"\r\n            preserveAspectRatio=\"xMidYMid slice\"\r\n          />\r\n        </pattern>\r\n      </defs>\r\n      \r\n      <circle \r\n        :r=\"tokenRadius - 3\"\r\n        :fill=\"`url(#avatar-${character.id})`\"\r\n        opacity=\"0.95\"\r\n      />\r\n      \r\n      <!-- 生命值环 -->\r\n      <circle \r\n        :r=\"tokenRadius + 2\"\r\n        fill=\"none\"\r\n        stroke=\"#ddd\"\r\n        stroke-width=\"4\"\r\n        opacity=\"0.3\"\r\n      />\r\n      <circle \r\n        :r=\"tokenRadius + 2\"\r\n        fill=\"none\"\r\n        :stroke=\"getHealthColor()\"\r\n        stroke-width=\"4\"\r\n        :stroke-dasharray=\"getHealthDashArray()\"\r\n        stroke-linecap=\"round\"\r\n        transform=\"rotate(-90)\"\r\n        opacity=\"0.8\"\r\n      />\r\n    </g>\r\n    \r\n    <!-- 角色名称 -->\r\n    <text \r\n      :y=\"tokenRadius + 20\"\r\n      text-anchor=\"middle\"\r\n      font-size=\"12\"\r\n      font-weight=\"bold\"\r\n      :fill=\"character.isPlayer ? '#2196f3' : '#f44336'\"\r\n      class=\"character-name-text\"\r\n    >\r\n      {{ character.name }}\r\n    </text>\r\n    \r\n    <!-- 状态效果图标 -->\r\n    <g class=\"status-effects\" :transform=\"`translate(${-tokenRadius}, ${-tokenRadius - 15})`\">\r\n      <g \r\n        v-for=\"(condition, index) in character.conditions\" \r\n        :key=\"condition\"\r\n        :transform=\"`translate(${index * 16}, 0)`\"\r\n      >\r\n        <circle r=\"8\" fill=\"#333\" opacity=\"0.8\"/>\r\n        <text \r\n          text-anchor=\"middle\" \r\n          dy=\"4\" \r\n          font-size=\"10\" \r\n          fill=\"white\"\r\n        >\r\n          {{ getConditionIcon(condition) }}\r\n        </text>\r\n      </g>\r\n    </g>\r\n    \r\n    <!-- 行动状态指示器 -->\r\n    <g class=\"action-indicators\" :transform=\"`translate(${tokenRadius - 8}, ${-tokenRadius + 8})`\">\r\n      <!-- 已行动指示 -->\r\n      <circle \r\n        v-if=\"character.hasActed\"\r\n        r=\"6\"\r\n        fill=\"#ff9800\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"character.hasActed\"\r\n        text-anchor=\"middle\"\r\n        dy=\"3\"\r\n        font-size=\"8\"\r\n        fill=\"white\"\r\n        font-weight=\"bold\"\r\n      >\r\n        ✓\r\n      </text>\r\n      \r\n      <!-- 延迟行动指示 -->\r\n      <circle \r\n        v-if=\"character.isDelaying\"\r\n        r=\"6\"\r\n        fill=\"#9c27b0\"\r\n        stroke=\"#fff\"\r\n        stroke-width=\"1\"\r\n      />\r\n      <text \r\n        v-if=\"character.isDelaying\"\r\n        text-anchor=\"middle\"\r\n        dy=\"3\"\r\n        font-size=\"8\"\r\n        fill=\"white\"\r\n        font-weight=\"bold\"\r\n      >\r\n        ⏸\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 伤害数字动画 -->\r\n    <g v-if=\"damageAnimation\" class=\"damage-animation\">\r\n      <text \r\n        :y=\"-tokenRadius - 10\"\r\n        text-anchor=\"middle\"\r\n        font-size=\"16\"\r\n        font-weight=\"bold\"\r\n        :fill=\"damageAnimation.color\"\r\n        opacity=\"0\"\r\n      >\r\n        {{ damageAnimation.text }}\r\n        <animate \r\n          attributeName=\"y\" \r\n          :values=\"`${-tokenRadius - 10};${-tokenRadius - 40}`\"\r\n          dur=\"1.5s\"\r\n        />\r\n        <animate \r\n          attributeName=\"opacity\" \r\n          values=\"0;1;1;0\"\r\n          dur=\"1.5s\"\r\n        />\r\n      </text>\r\n    </g>\r\n    \r\n    <!-- 拖拽手柄 (仅在可移动时显示) -->\r\n    <circle \r\n      v-if=\"canMove && selected\"\r\n      :r=\"tokenRadius\"\r\n      fill=\"transparent\"\r\n      stroke=\"none\"\r\n      style=\"cursor: move\"\r\n      @mousedown=\"startDrag\"\r\n    />\r\n  </g>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CharacterToken',\r\n  props: {\r\n    character: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    },\r\n    zoom: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    selected: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    currentTurn: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canMove: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    canAct: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showMovementRange: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    showAttackRange: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      tokenRadius: 20,\r\n      isDragging: false,\r\n      dragStart: null,\r\n      damageAnimation: null\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    x() {\r\n      return (this.character.position?.x || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    y() {\r\n      return (this.character.position?.y || 0) * this.gridSize * this.zoom\r\n    },\r\n    \r\n    movementRange() {\r\n      // 根据角色移动力计算移动范围\r\n      return this.character.movement || 3\r\n    },\r\n    \r\n    attackRange() {\r\n      // 根据装备武器计算攻击范围\r\n      const weapon = this.character.equippedWeapon\r\n      if (!weapon) return 1.5 // 徒手攻击\r\n      \r\n      if (weapon.type === 'melee') {\r\n        return weapon.reach || 1.5\r\n      } else if (weapon.type === 'ranged') {\r\n        return Math.min(weapon.range?.base || 10, 10) // 限制显示范围\r\n      }\r\n      \r\n      return 1.5\r\n    }\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 获取默认头像\r\n     */\r\n    getDefaultAvatar() {\r\n      return this.character.isPlayer \r\n        ? '/images/default-player-avatar.png'\r\n        : '/images/default-enemy-avatar.png'\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值颜色\r\n     */\r\n    getHealthColor() {\r\n      const healthPercent = this.getHealthPercentage()\r\n      \r\n      if (healthPercent > 75) return '#4caf50'\r\n      if (healthPercent > 50) return '#ff9800'\r\n      if (healthPercent > 25) return '#f44336'\r\n      return '#9c27b0'\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值百分比\r\n     */\r\n    getHealthPercentage() {\r\n      const current = this.character.currentHP || this.character.hitPoints\r\n      const max = this.character.maxHP || this.character.hitPoints\r\n      return max > 0 ? (current / max) * 100 : 0\r\n    },\r\n    \r\n    /**\r\n     * 获取生命值环的虚线数组\r\n     */\r\n    getHealthDashArray() {\r\n      const circumference = 2 * Math.PI * (this.tokenRadius + 2)\r\n      const healthPercent = this.getHealthPercentage() / 100\r\n      const healthLength = circumference * healthPercent\r\n      const gapLength = circumference - healthLength\r\n      \r\n      return `${healthLength} ${gapLength}`\r\n    },\r\n    \r\n    /**\r\n     * 获取状态效果图标\r\n     */\r\n    getConditionIcon(condition) {\r\n      const iconMap = {\r\n        'unconscious': '💤',\r\n        'dying': '💀',\r\n        'prone': '⬇️',\r\n        'stunned': '😵',\r\n        'restrained': '🔒',\r\n        'frightened': '😨',\r\n        'poisoned': '☠️',\r\n        'bleeding': '🩸',\r\n        'burning': '🔥',\r\n        'frozen': '❄️',\r\n        'paralyzed': '⚡',\r\n        'blinded': '👁️',\r\n        'deafened': '👂',\r\n        'charmed': '💖',\r\n        'confused': '❓'\r\n      }\r\n      \r\n      return iconMap[condition] || '?'\r\n    },\r\n    \r\n    /**\r\n     * 开始拖拽\r\n     */\r\n    startDrag(event) {\r\n      if (!this.canMove) return\r\n      \r\n      this.isDragging = true\r\n      this.dragStart = {\r\n        x: event.clientX,\r\n        y: event.clientY,\r\n        characterX: this.character.position.x,\r\n        characterY: this.character.position.y\r\n      }\r\n      \r\n      document.addEventListener('mousemove', this.handleDrag)\r\n      document.addEventListener('mouseup', this.endDrag)\r\n      \r\n      event.stopPropagation()\r\n    },\r\n    \r\n    /**\r\n     * 处理拖拽\r\n     */\r\n    handleDrag(event) {\r\n      if (!this.isDragging || !this.dragStart) return\r\n      \r\n      const deltaX = event.clientX - this.dragStart.x\r\n      const deltaY = event.clientY - this.dragStart.y\r\n      \r\n      const gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom))\r\n      const gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom))\r\n      \r\n      const newX = this.dragStart.characterX + gridDeltaX\r\n      const newY = this.dragStart.characterY + gridDeltaY\r\n      \r\n      // 检查移动范围\r\n      const distance = Math.sqrt(\r\n        Math.pow(newX - this.dragStart.characterX, 2) + \r\n        Math.pow(newY - this.dragStart.characterY, 2)\r\n      )\r\n      \r\n      if (distance <= this.movementRange) {\r\n        this.$emit('move', this.character, { x: newX, y: newY })\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 结束拖拽\r\n     */\r\n    endDrag() {\r\n      this.isDragging = false\r\n      this.dragStart = null\r\n      \r\n      document.removeEventListener('mousemove', this.handleDrag)\r\n      document.removeEventListener('mouseup', this.endDrag)\r\n    },\r\n    \r\n    /**\r\n     * 播放伤害动画\r\n     */\r\n    playDamageAnimation(damage, type = 'normal') {\r\n      const colors = {\r\n        normal: '#f44336',\r\n        critical: '#e91e63',\r\n        healing: '#4caf50',\r\n        miss: '#9e9e9e'\r\n      }\r\n      \r\n      const texts = {\r\n        normal: `-${damage}`,\r\n        critical: `CRIT! -${damage}`,\r\n        healing: `+${damage}`,\r\n        miss: 'MISS'\r\n      }\r\n      \r\n      this.damageAnimation = {\r\n        text: texts[type] || `-${damage}`,\r\n        color: colors[type] || '#f44336'\r\n      }\r\n      \r\n      // 1.5秒后清除动画\r\n      setTimeout(() => {\r\n        this.damageAnimation = null\r\n      }, 1500)\r\n    },\r\n    \r\n    /**\r\n     * 播放治疗动画\r\n     */\r\n    playHealingAnimation(healing) {\r\n      this.playDamageAnimation(healing, 'healing')\r\n    },\r\n    \r\n    /**\r\n     * 播放闪避动画\r\n     */\r\n    playMissAnimation() {\r\n      this.playDamageAnimation(0, 'miss')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.character-token {\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.character-token:hover {\r\n  filter: brightness(1.1);\r\n}\r\n\r\n.character-token.selected {\r\n  filter: drop-shadow(0 0 8px rgba(52, 152, 219, 0.8));\r\n}\r\n\r\n.character-token.current-turn {\r\n  filter: drop-shadow(0 0 12px rgba(231, 76, 60, 0.9));\r\n}\r\n\r\n.character-token.can-move {\r\n  cursor: move;\r\n}\r\n\r\n.character-token.player .character-body circle:first-child {\r\n  filter: drop-shadow(0 2px 4px rgba(33, 150, 243, 0.3));\r\n}\r\n\r\n.character-token.enemy .character-body circle:first-child {\r\n  filter: drop-shadow(0 2px 4px rgba(244, 67, 54, 0.3));\r\n}\r\n\r\n.character-name-text {\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n  font-family: 'Arial', sans-serif;\r\n}\r\n\r\n.status-effects circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.action-indicators circle {\r\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));\r\n}\r\n\r\n.damage-animation text {\r\n  font-family: 'Arial Black', sans-serif;\r\n  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);\r\n}\r\n\r\n/* 动画效果 */\r\n@keyframes pulse {\r\n  0%, 100% { opacity: 0.8; }\r\n  50% { opacity: 0.3; }\r\n}\r\n\r\n@keyframes rotate {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes damageFloat {\r\n  0% { \r\n    transform: translateY(0); \r\n    opacity: 0; \r\n  }\r\n  20% { \r\n    opacity: 1; \r\n  }\r\n  80% { \r\n    opacity: 1; \r\n  }\r\n  100% { \r\n    transform: translateY(-30px); \r\n    opacity: 0; \r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;EA2EO,SAAM;AAAgB;;;;;;;;;;;EA4EnB,aAAW,EAAC,QAAQ;EACpBA,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,IAAI;EACdC,IAAI,EAAC;;;;;EAYPC,CAAC,EAAC,GAAG;EACLD,IAAI,EAAC,SAAS;EACdE,MAAM,EAAC,MAAM;EACb,cAAY,EAAC;;;;EAIb,aAAW,EAAC,QAAQ;EACpBH,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,GAAG;EACbC,IAAI,EAAC,OAAO;EACZ,aAAW,EAAC;;;;EAQZC,CAAC,EAAC,GAAG;EACLD,IAAI,EAAC,SAAS;EACdE,MAAM,EAAC,MAAM;EACb,cAAY,EAAC;;;;EAIb,aAAW,EAAC,QAAQ;EACpBH,EAAE,EAAC,GAAG;EACN,WAAS,EAAC,GAAG;EACbC,IAAI,EAAC,OAAO;EACZ,aAAW,EAAC;;;;EAOU,SAAM;;;;;;6DA1MlCG,mBAAA,gBAAmB,EACnBC,mBAAA,CAyOI;IAxOF,SAAKC,eAAA,EAAC,iBAAiB;gBACLC,MAAA,CAAAC,QAAQ;sBAA0BD,MAAA,CAAAE,WAAW;kBAAqBF,MAAA,CAAAG,OAAO;iBAAoBH,MAAA,CAAAI,MAAM;cAAiBJ,MAAA,CAAAK,SAAS,CAACC,QAAQ;cAAiBN,MAAA,CAAAK,SAAS,CAACC;;IAQlLC,SAAS,eAAAC,MAAA,CAAeC,QAAA,CAAAC,CAAC,QAAAF,MAAA,CAAKC,QAAA,CAAAE,CAAC;IAC/BC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,IAAA,CAAAC,KAAK,WAAWhB,MAAA,CAAAK,SAAS;IAAA;IAChCY,aAAW,EAAAJ,MAAA,QAAAA,MAAA,MAAAK,cAAA,WAAAJ,MAAA;MAAA,OAAUC,IAAA,CAAAC,KAAK,iBAAiBhB,MAAA,CAAAK,SAAS,EAAES,MAAM;IAAA;MAE7DjB,mBAAA,UAAa,EAELG,MAAA,CAAAC,QAAQ,I,cADhBkB,mBAAA,CAcS;;IAZNxB,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACf3B,IAAI,EAAC,MAAM;IACXE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB0B,OAAO,EAAC;gCAERxB,mBAAA,CAKE;IAJAyB,aAAa,EAAC,gBAAgB;IAC9BC,MAAM,EAAC,aAAa;IACpBC,GAAG,EAAC,IAAI;IACRC,WAAW,EAAC;gGAIhB7B,mBAAA,YAAe,EAEPG,MAAA,CAAAE,WAAW,I,cADnBiB,mBAAA,CAgBS;;IAdNxB,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACf3B,IAAI,EAAC,MAAM;IACXE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtB0B,OAAO,EAAC;gCAERxB,mBAAA,CAME;IALAyB,aAAa,EAAC,WAAW;IACzBI,IAAI,EAAC,QAAQ;IACbH,MAAM,EAAC,OAAO;IACdC,GAAG,EAAC,IAAI;IACRC,WAAW,EAAC;gGAIhB7B,mBAAA,YAAe,EAEPG,MAAA,CAAA4B,iBAAiB,IAAI5B,MAAA,CAAAG,OAAO,I,cADpCgB,mBAAA,CAQE;;IANCxB,CAAC,EAAEc,QAAA,CAAAoB,aAAa,GAAG7B,MAAA,CAAA8B,QAAQ;IAC5BpC,IAAI,EAAC,wBAAwB;IAC7BE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtB0B,OAAO,EAAC;4EAGVzB,mBAAA,YAAe,EAEPG,MAAA,CAAA+B,eAAe,IAAI/B,MAAA,CAAAI,MAAM,I,cADjCe,mBAAA,CAQE;;IANCxB,CAAC,EAAEc,QAAA,CAAAuB,WAAW,GAAGhC,MAAA,CAAA8B,QAAQ;IAC1BpC,IAAI,EAAC,wBAAwB;IAC7BE,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtB0B,OAAO,EAAC;4EAGVzB,mBAAA,UAAa,EACbC,mBAAA,CAqDI,KArDJmC,UAqDI,GApDFpC,mBAAA,SAAY,EACZC,mBAAA,CAME;IALCH,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACd3B,IAAI,EAAEM,MAAA,CAAAK,SAAS,CAACC,QAAQ;IACzBV,MAAM,EAAC,MAAM;IACb,cAAY,EAAC,GAAG;IAChB0B,OAAO,EAAC;uCAGVzB,mBAAA,UAAa,EACbC,mBAAA,CAgBO,eAfLA,mBAAA,CAcU;IAbPoC,EAAE,YAAA1B,MAAA,CAAYR,MAAA,CAAAK,SAAS,CAAC6B,EAAE;IAC3BC,YAAY,EAAC,mBAAmB;IAChCC,KAAK,EAAC,MAAM;IACZC,MAAM,EAAC;MAEPvC,mBAAA,CAOE;IANCwC,IAAI,EAAEtC,MAAA,CAAAK,SAAS,CAACkC,MAAM,IAAI9B,QAAA,CAAA+B,gBAAgB;IAC3C9B,CAAC,EAAC,GAAG;IACLC,CAAC,EAAC,GAAG;IACJyB,KAAK,EAAEhB,KAAA,CAAAC,WAAW;IAClBgB,MAAM,EAAEjB,KAAA,CAAAC,WAAW;IACpBoB,mBAAmB,EAAC;sEAK1B3C,mBAAA,CAIE;IAHCH,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACd3B,IAAI,iBAAAc,MAAA,CAAiBR,MAAA,CAAAK,SAAS,CAAC6B,EAAE;IAClCZ,OAAO,EAAC;wCAGVzB,mBAAA,UAAa,EACbC,mBAAA,CAME;IALCH,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACf3B,IAAI,EAAC,MAAM;IACXE,MAAM,EAAC,MAAM;IACb,cAAY,EAAC,GAAG;IAChB0B,OAAO,EAAC;wCAEVxB,mBAAA,CASE;IARCH,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACf3B,IAAI,EAAC,MAAM;IACVE,MAAM,EAAEa,QAAA,CAAAiC,cAAc;IACvB,cAAY,EAAC,GAAG;IACf,kBAAgB,EAAEjC,QAAA,CAAAkC,kBAAkB;IACrC,gBAAc,EAAC,OAAO;IACtBpC,SAAS,EAAC,aAAa;IACvBe,OAAO,EAAC;0CAIZzB,mBAAA,UAAa,EACbC,mBAAA,CASO;IARJa,CAAC,EAAES,KAAA,CAAAC,WAAW;IACf,aAAW,EAAC,QAAQ;IACpB,WAAS,EAAC,IAAI;IACd,aAAW,EAAC,MAAM;IACjB3B,IAAI,EAAEM,MAAA,CAAAK,SAAS,CAACC,QAAQ;IACzB,SAAM;sBAEHN,MAAA,CAAAK,SAAS,CAACuC,IAAI,wBAAAC,WAAA,GAGnBhD,mBAAA,YAAe,EACfC,mBAAA,CAgBI;IAhBD,SAAM,gBAAgB;IAAES,SAAS,eAAAC,MAAA,EAAgBY,KAAA,CAAAC,WAAW,QAAAb,MAAA,EAAMY,KAAA,CAAAC,WAAW;yBAC9EF,mBAAA,CAcI2B,SAAA,QAAAC,WAAA,CAb2B/C,MAAA,CAAAK,SAAS,CAAC2C,UAAU,YAAzCC,SAAS,EAAEC,KAAK;yBAD1B/B,mBAAA,CAcI;MAZDgC,GAAG,EAAEF,SAAS;MACd1C,SAAS,eAAAC,MAAA,CAAe0C,KAAK;kCAE9BpD,mBAAA,CAAyC;MAAjCH,CAAC,EAAC,GAAG;MAACD,IAAI,EAAC,MAAM;MAAC4B,OAAO,EAAC;gCAClCxB,mBAAA,CAOO,QAPPsD,WAOO,EAAAC,gBAAA,CADF5C,QAAA,CAAA6C,gBAAgB,CAACL,SAAS,kB;gEAKnCpD,mBAAA,aAAgB,EAChBC,mBAAA,CAsCI;IAtCD,SAAM,mBAAmB;IAAES,SAAS,eAAAC,MAAA,CAAeY,KAAA,CAAAC,WAAW,YAAAb,MAAA,EAAUY,KAAA,CAAAC,WAAW;MACpFxB,mBAAA,WAAc,EAENG,MAAA,CAAAK,SAAS,CAACkD,QAAQ,I,cAD1BpC,mBAAA,CAME,UANFqC,WAME,K,mCAEMxD,MAAA,CAAAK,SAAS,CAACkD,QAAQ,I,cAD1BpC,mBAAA,CASO,QATPsC,WASO,EAFN,KAED,K,mCAEA5D,mBAAA,YAAe,EAEPG,MAAA,CAAAK,SAAS,CAACqD,UAAU,I,cAD5BvC,mBAAA,CAME,UANFwC,WAME,K,mCAEM3D,MAAA,CAAAK,SAAS,CAACqD,UAAU,I,cAD5BvC,mBAAA,CASO,QATPyC,WASO,EAFN,KAED,K,iEAGF/D,mBAAA,YAAe,EACNuB,KAAA,CAAAyC,eAAe,I,cAAxB1C,mBAAA,CAqBI,KArBJ2C,WAqBI,GApBFhE,mBAAA,CAmBO;IAlBJa,CAAC,GAAGS,KAAA,CAAAC,WAAW;IAChB,aAAW,EAAC,QAAQ;IACpB,WAAS,EAAC,IAAI;IACd,aAAW,EAAC,MAAM;IACjB3B,IAAI,EAAE0B,KAAA,CAAAyC,eAAe,CAACE,KAAK;IAC5BzC,OAAO,EAAC;wCAELF,KAAA,CAAAyC,eAAe,CAACG,IAAI,IAAG,GAC1B,iBAAAlE,mBAAA,CAIE;IAHAyB,aAAa,EAAC,GAAG;IAChBC,MAAM,KAAAhB,MAAA,EAAMY,KAAA,CAAAC,WAAW,YAAAb,MAAA,EAAUY,KAAA,CAAAC,WAAW;IAC7CI,GAAG,EAAC;kEAEN3B,mBAAA,CAIE;IAHAyB,aAAa,EAAC,SAAS;IACvBC,MAAM,EAAC,SAAS;IAChBC,GAAG,EAAC;mGAKV5B,mBAAA,qBAAwB,EAEhBG,MAAA,CAAAG,OAAO,IAAIH,MAAA,CAAAC,QAAQ,I,cAD3BkB,mBAAA,CAOE;;IALCxB,CAAC,EAAEyB,KAAA,CAAAC,WAAW;IACf3B,IAAI,EAAC,aAAa;IAClBE,MAAM,EAAC,MAAM;IACbqE,KAAoB,EAApB;MAAA;IAAA,CAAoB;IACnBC,WAAS,EAAArD,MAAA,QAAAA,MAAA;MAAA,OAAEJ,QAAA,CAAA0D,SAAA,IAAA1D,QAAA,CAAA0D,SAAA,CAAAC,KAAA,CAAA3D,QAAA,EAAA4D,SAAA,CAAS;IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}