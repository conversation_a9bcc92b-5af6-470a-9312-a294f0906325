<template>
  <!-- 强制战斗模式全屏覆盖 -->
  <div class="forced-combat-overlay" v-if="isActive">
    <!-- 背景遮罩 -->
    <div class="combat-backdrop"></div>
    
    <!-- 战斗界面容器 -->
    <div class="combat-container">
      <!-- 顶部状态栏 -->
      <div class="combat-status-bar">
        <div class="status-left">
          <div class="combat-indicator">
            <i class="fas fa-sword"></i>
            <span class="combat-text">战斗模式</span>
            <div class="pulse-ring"></div>
          </div>
          <div class="round-info">
            <span class="round-number">第 {{ currentRound }} 轮</span>
            <span class="turn-info">{{ currentTurnInfo }}</span>
          </div>
        </div>
        
        <div class="status-right">
          <div class="connection-status" :class="connectionStatus">
            <i class="fas fa-wifi"></i>
            <span>{{ connectionText }}</span>
          </div>
        </div>
      </div>

      <!-- 主战斗区域 -->
      <div class="combat-main-area">
        <!-- 战场视图 -->
        <div class="battlefield-container">
          <BattlefieldGrid
            :characters="participants?.filter(p => p.isPlayer) || []"
            :monsters="participants?.filter(p => !p.isPlayer) || []"
            :selected-character="selectedParticipant?.isPlayer ? selectedParticipant : null"
            :selected-monster="!selectedParticipant?.isPlayer ? selectedParticipant : null"
            :current-turn="currentTurn"
            :target-participant="targetParticipant"
            :battlefield-size="battlefieldSize"
            @participant-clicked="handleParticipantClick"
            @position-clicked="handlePositionClick"
            @participant-moved="handleParticipantMove"
          />
        </div>

        <!-- 右侧信息面板 -->
        <div class="info-panel">
          <!-- 当前角色信息 -->
          <div class="current-character-panel" v-if="currentParticipant">
            <div class="panel-header">
              <h3>{{ currentParticipant.name }}</h3>
              <div class="character-type" :class="{ player: currentParticipant.isPlayer }">
                {{ currentParticipant.isPlayer ? '玩家' : 'NPC' }}
              </div>
            </div>
            
            <div class="character-stats">
              <div class="stat-bar">
                <label>生命值</label>
                <div class="progress-bar health">
                  <div 
                    class="progress-fill" 
                    :style="{ width: `${getHealthPercentage(currentParticipant)}%` }"
                  ></div>
                  <span class="progress-text">
                    {{ currentParticipant.currentHP }}/{{ currentParticipant.maxHP }}
                  </span>
                </div>
              </div>
              
              <div class="stat-bar" v-if="currentParticipant.currentSAN">
                <label>理智值</label>
                <div class="progress-bar sanity">
                  <div 
                    class="progress-fill" 
                    :style="{ width: `${getSanityPercentage(currentParticipant)}%` }"
                  ></div>
                  <span class="progress-text">
                    {{ currentParticipant.currentSAN }}/{{ currentParticipant.maxSAN }}
                  </span>
                </div>
              </div>
              
              <div class="status-effects" v-if="currentParticipant.conditions?.length">
                <label>状态效果</label>
                <div class="effects-list">
                  <span 
                    v-for="effect in currentParticipant.conditions" 
                    :key="effect"
                    class="effect-badge"
                    :class="effect"
                  >
                    {{ getStatusEffectName(effect) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 先攻顺序 -->
          <div class="initiative-panel">
            <div class="panel-header">
              <h3>先攻顺序</h3>
            </div>
            <div class="initiative-list">
              <div 
                v-for="(participant, index) in participants" 
                :key="participant.id"
                class="initiative-item"
                :class="{ 
                  current: index === currentTurn,
                  player: participant.isPlayer,
                  selected: selectedParticipant?.id === participant.id
                }"
                @click="selectParticipant(participant)"
              >
                <div class="initiative-number">{{ participant.initiative }}</div>
                <div class="participant-info">
                  <span class="participant-name">{{ participant.name }}</span>
                  <div class="participant-health">
                    {{ participant.currentHP }}/{{ participant.maxHP }}
                  </div>
                </div>
                <div class="turn-indicator" v-if="index === currentTurn">
                  <i class="fas fa-play"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="combat-action-bar">
        <!-- 玩家操作区 (仅当前回合玩家可见) -->
        <div class="player-actions" v-if="isPlayerTurn && !isKeeper">
          <div class="action-section">
            <h4>选择行动</h4>
            <div class="action-buttons">
              <button 
                @click="selectAction('attack')" 
                class="action-btn attack"
                :class="{ active: selectedAction === 'attack' }"
              >
                <i class="fas fa-sword"></i>
                <span>攻击</span>
              </button>
              <button 
                @click="selectAction('defend')" 
                class="action-btn defend"
                :class="{ active: selectedAction === 'defend' }"
              >
                <i class="fas fa-shield"></i>
                <span>防御</span>
              </button>
              <button 
                @click="selectAction('move')" 
                class="action-btn move"
                :class="{ active: selectedAction === 'move' }"
              >
                <i class="fas fa-running"></i>
                <span>移动</span>
              </button>
              <button 
                @click="selectAction('item')" 
                class="action-btn item"
                :class="{ active: selectedAction === 'item' }"
              >
                <i class="fas fa-backpack"></i>
                <span>道具</span>
              </button>
              <button 
                @click="selectAction('maneuver')" 
                class="action-btn maneuver"
                :class="{ active: selectedAction === 'maneuver' }"
              >
                <i class="fas fa-fist-raised"></i>
                <span>战技</span>
              </button>
            </div>
          </div>
          
          <!-- 确认操作 -->
          <div class="action-confirm" v-if="selectedAction">
            <button @click="confirmAction" class="btn-confirm">
              <i class="fas fa-check"></i>
              确认行动
            </button>
            <button @click="cancelAction" class="btn-cancel">
              <i class="fas fa-times"></i>
              取消
            </button>
          </div>
        </div>

        <!-- 等待提示 (非当前回合玩家) -->
        <div class="waiting-indicator" v-else-if="!isKeeper">
          <div class="waiting-content">
            <div class="spinner"></div>
            <div class="waiting-text">
              <h4>等待其他玩家行动</h4>
              <p>当前回合: {{ currentParticipant?.name || '未知' }}</p>
            </div>
          </div>
        </div>

        <!-- KP控制区 -->
        <div class="keeper-controls" v-if="isKeeper">
          <div class="control-section">
            <h4>KP控制</h4>
            <div class="keeper-buttons">
              <button @click="nextTurn" class="keeper-btn">
                <i class="fas fa-forward"></i>
                下一回合
              </button>
              <button @click="pauseCombat" class="keeper-btn">
                <i class="fas fa-pause"></i>
                暂停战斗
              </button>
              <button @click="endCombat" class="keeper-btn danger">
                <i class="fas fa-stop"></i>
                结束战斗
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 战斗结果弹窗 -->
    <div class="combat-result-modal" v-if="showResult">
      <div class="result-content">
        <div class="result-header">
          <h2>{{ resultData.title }}</h2>
        </div>
        <div class="result-body">
          <p>{{ resultData.message }}</p>
          <div class="result-details" v-if="resultData.details">
            <div v-for="detail in resultData.details" :key="detail.label">
              <strong>{{ detail.label }}:</strong> {{ detail.value }}
            </div>
          </div>
        </div>
        <div class="result-actions">
          <button @click="closeResult" class="btn-primary">确定</button>
        </div>
      </div>
    </div>

    <!-- 断线重连提示 -->
    <div class="reconnect-overlay" v-if="showReconnect">
      <div class="reconnect-content">
        <div class="reconnect-icon">
          <i class="fas fa-wifi"></i>
        </div>
        <h3>连接中断</h3>
        <p>正在尝试重新连接到战斗服务器...</p>
        <div class="reconnect-progress">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: `${reconnectProgress}%` }"></div>
          </div>
          <span>{{ reconnectAttempts }}/{{ maxReconnectAttempts }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BattlefieldGrid from './BattlefieldGrid.vue'

export default {
  name: 'ForcedCombatMode',
  components: {
    BattlefieldGrid
  },
  props: {
    isActive: {
      type: Boolean,
      default: false
    },
    isKeeper: {
      type: Boolean,
      default: false
    },
    combatData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 战斗状态
      participants: [],
      currentTurn: 0,
      currentRound: 1,
      selectedParticipant: null,
      targetParticipant: null,
      
      // 玩家操作
      selectedAction: null,
      actionData: null,
      
      // 界面状态
      showResult: false,
      resultData: {},
      showReconnect: false,
      reconnectProgress: 0,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      
      // 连接状态
      connectionStatus: 'connected',
      
      // 战场配置
      battlefieldSize: { width: 20, height: 15 }
    }
  },
  
  computed: {
    currentParticipant() {
      return this.participants[this.currentTurn] || null
    },
    
    isPlayerTurn() {
      return this.currentParticipant?.isPlayer && 
             this.currentParticipant?.playerId === this.$store.state.auth.user?.id
    },
    
    currentTurnInfo() {
      if (!this.currentParticipant) return '等待开始'
      return `${this.currentParticipant.name} 的回合`
    },
    
    connectionText() {
      const texts = {
        connected: '已连接',
        connecting: '连接中',
        disconnected: '已断开',
        error: '连接错误'
      }
      return texts[this.connectionStatus] || '未知状态'
    }
  },
  
  methods: {
    // 参与者操作
    selectParticipant(participant) {
      this.selectedParticipant = participant
      this.$emit('participant-selected', participant)
    },
    
    handleParticipantClick(participant) {
      if (this.selectedAction === 'attack') {
        this.targetParticipant = participant
      } else {
        this.selectParticipant(participant)
      }
    },
    
    handlePositionClick(position) {
      if (this.selectedAction === 'move') {
        this.actionData = { targetPosition: position }
      }
    },
    
    handleParticipantMove(participant, newPosition) {
      this.$emit('participant-moved', participant, newPosition)
    },
    
    // 行动选择
    selectAction(actionType) {
      this.selectedAction = actionType
      this.actionData = null
      this.targetParticipant = null
      
      // 根据行动类型设置界面状态
      switch (actionType) {
        case 'attack':
          this.showMessage('请选择攻击目标')
          break
        case 'move':
          this.showMessage('请选择移动位置')
          break
        case 'defend':
          this.actionData = { type: 'full_defense' }
          break
        case 'item':
          this.showItemSelection()
          break
        case 'maneuver':
          this.showManeuverSelection()
          break
      }
    },
    
    confirmAction() {
      if (!this.selectedAction) return
      
      const actionData = {
        type: this.selectedAction,
        participant: this.currentParticipant,
        target: this.targetParticipant,
        data: this.actionData
      }
      
      this.$emit('action-confirmed', actionData)
      this.resetActionState()
    },
    
    cancelAction() {
      this.resetActionState()
    },
    
    resetActionState() {
      this.selectedAction = null
      this.actionData = null
      this.targetParticipant = null
    },
    
    // KP控制
    nextTurn() {
      this.$emit('next-turn')
    },
    
    pauseCombat() {
      this.$emit('pause-combat')
    },
    
    endCombat() {
      this.$emit('end-combat')
    },
    
    // 界面辅助
    getHealthPercentage(participant) {
      if (!participant.maxHP) return 0
      return Math.max(0, (participant.currentHP / participant.maxHP) * 100)
    },
    
    getSanityPercentage(participant) {
      if (!participant.maxSAN) return 0
      return Math.max(0, (participant.currentSAN / participant.maxSAN) * 100)
    },
    
    getStatusEffectName(effect) {
      const names = {
        bleeding: '流血',
        poisoned: '中毒',
        stunned: '眩晕',
        frightened: '恐惧',
        blessed: '祝福',
        cursed: '诅咒',
        prone: '倒地',
        grappled: '被擒抱',
        unconscious: '昏迷'
      }
      return names[effect] || effect
    },
    
    showMessage(message) {
      // 显示临时消息
      this.$emit('show-message', message)
    },
    
    showItemSelection() {
      // 显示道具选择界面
      this.$emit('show-item-selection')
    },
    
    showManeuverSelection() {
      // 显示战技选择界面
      this.$emit('show-maneuver-selection')
    },
    
    // 结果处理
    showCombatResult(resultData) {
      this.resultData = resultData
      this.showResult = true
    },
    
    closeResult() {
      this.showResult = false
      this.resultData = {}
    },
    
    // 连接管理
    handleConnectionLost() {
      this.connectionStatus = 'disconnected'
      this.showReconnect = true
      this.startReconnectAttempts()
    },
    
    startReconnectAttempts() {
      this.reconnectAttempts = 0
      this.reconnectProgress = 0
      
      const attemptReconnect = () => {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          this.handleReconnectFailed()
          return
        }
        
        this.reconnectAttempts++
        this.reconnectProgress = (this.reconnectAttempts / this.maxReconnectAttempts) * 100
        
        // 尝试重连
        this.$emit('reconnect-attempt')
        
        setTimeout(() => {
          if (this.connectionStatus !== 'connected') {
            attemptReconnect()
          } else {
            this.handleReconnectSuccess()
          }
        }, 2000)
      }
      
      attemptReconnect()
    },
    
    handleReconnectSuccess() {
      this.showReconnect = false
      this.connectionStatus = 'connected'
      this.showMessage('重新连接成功')
    },
    
    handleReconnectFailed() {
      this.showReconnect = false
      this.showCombatResult({
        title: '连接失败',
        message: '无法重新连接到战斗服务器，战斗将被强制结束。',
        details: [
          { label: '错误代码', value: 'CONNECTION_TIMEOUT' },
          { label: '尝试次数', value: this.maxReconnectAttempts }
        ]
      })
    }
  },
  
  watch: {
    combatData: {
      handler(newData) {
        if (newData) {
          this.participants = newData.participants || []
          this.currentTurn = newData.currentTurn || 0
          this.currentRound = newData.currentRound || 1
          this.battlefieldSize = newData.battlefieldSize || { width: 20, height: 15 }
        }
      },
      deep: true,
      immediate: true
    },
    
    isActive(newVal) {
      if (newVal) {
        // 进入强制战斗模式
        document.body.style.overflow = 'hidden'
        this.$emit('combat-mode-entered')
      } else {
        // 退出强制战斗模式
        document.body.style.overflow = ''
        this.$emit('combat-mode-exited')
      }
    }
  },
  
  beforeUnmount() {
    // 清理样式
    document.body.style.overflow = ''
  }
}
</script>