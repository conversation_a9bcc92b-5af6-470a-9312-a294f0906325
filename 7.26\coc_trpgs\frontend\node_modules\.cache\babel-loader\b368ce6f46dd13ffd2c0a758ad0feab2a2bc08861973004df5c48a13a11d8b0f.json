{"ast": null, "code": "import \"core-js/modules/es.object.to-string.js\";\nimport axios from 'axios';\n\n// 创建带有基础URL的axios实例\nvar apiClient = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000',\n  withCredentials: true,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 安全获取token的函数\nfunction safeGetToken() {\n  try {\n    if (window.storageManager) {\n      return window.storageManager.getItem('token');\n    }\n    return localStorage.getItem('token');\n  } catch (error) {\n    console.warn('[API] 无法获取token:', error.message);\n    return null;\n  }\n}\nfunction safeGetAdminToken() {\n  try {\n    if (window.storageManager) {\n      return window.storageManager.getItem('admin_token');\n    }\n    return localStorage.getItem('admin_token');\n  } catch (error) {\n    console.warn('[API] 无法获取admin token:', error.message);\n    return null;\n  }\n}\n\n// 请求拦截器，添加认证token\napiClient.interceptors.request.use(function (config) {\n  var token = safeGetToken();\n  if (token) {\n    config.headers.Authorization = \"Bearer \".concat(token);\n  }\n  return config;\n}, function (error) {\n  return Promise.reject(error);\n});\n\n// 创建管理员API客户端实例\nvar adminApiClient = axios.create({\n  baseURL: process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000',\n  headers: {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json'\n  }\n});\n\n// 请求拦截器，添加管理员认证token\nadminApiClient.interceptors.request.use(function (config) {\n  var adminToken = safeGetAdminToken();\n  if (adminToken) {\n    config.headers.Authorization = \"Bearer \".concat(adminToken);\n  }\n  return config;\n}, function (error) {\n  return Promise.reject(error);\n});\n\n// 消息服务\nvar messagesService = {\n  getMessages: function getMessages(roomId) {\n    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    return apiClient.get(\"/api/rooms/\".concat(roomId, \"/messages\"), {\n      params: params\n    });\n  },\n  sendMessage: function sendMessage(roomId, message) {\n    return apiClient.post(\"/api/rooms/\".concat(roomId, \"/messages\"), message);\n  }\n};\n\n// AI服务\nvar aiService = {\n  sendMessage: function sendMessage(data) {\n    return apiClient.post('/api/ai/chat', data);\n  },\n  processScenario: function processScenario(data) {\n    return apiClient.post('/api/ai/scenario', data);\n  },\n  testConnection: function testConnection(data) {\n    return apiClient.post('/api/ai-settings/test-connection', data);\n  },\n  clearMemories: function clearMemories() {\n    return apiClient.post('/api/ai/clear-memories');\n  }\n};\n\n// KP服务\nvar kpService = {\n  loadScenario: function loadScenario(scenarioPath) {\n    return apiClient.post('/api/ai-kp/load-scenario', {\n      scenarioPath: scenarioPath\n    });\n  },\n  asyncLoadScenario: function asyncLoadScenario(scenarioPath) {\n    return apiClient.post('/api/ai-kp/async-load-scenario', {\n      scenarioPath: scenarioPath\n    });\n  },\n  getDocumentTaskStatus: function getDocumentTaskStatus(taskId) {\n    return apiClient.get(\"/api/ai-kp/document-task-status/\".concat(taskId));\n  },\n  completeAsyncScenario: function completeAsyncScenario(taskId) {\n    return apiClient.post('/api/ai-kp/complete-async-scenario', {\n      taskId: taskId\n    });\n  },\n  processDocument: function processDocument(filePath) {\n    return apiClient.post('/api/ai-kp/process-document', {\n      filePath: filePath\n    });\n  },\n  getCacheStats: function getCacheStats() {\n    return apiClient.get('/api/ai-kp/cache-stats');\n  },\n  clearExpiredCache: function clearExpiredCache() {\n    return apiClient.post('/api/ai-kp/clear-expired-cache');\n  },\n  getAvailableScenarios: function getAvailableScenarios() {\n    return apiClient.get('/api/ai-kp/available-scenarios');\n  },\n  startGame: function startGame(data) {\n    return apiClient.post('/api/ai-kp/start-game', data);\n  },\n  advanceScene: function advanceScene() {\n    return apiClient.post('/api/ai-kp/advance-scene');\n  },\n  processInput: function processInput(data) {\n    return apiClient.post('/api/ai-kp/process-input', data);\n  }\n};\n\n// 房间服务\nvar roomsService = {\n  getRooms: function getRooms() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return apiClient.get('/api/rooms/', {\n      params: params\n    });\n  },\n  getRoom: function getRoom(roomId) {\n    return apiClient.get(\"/api/rooms/\".concat(roomId, \"/\"));\n  },\n  createRoom: function createRoom(data) {\n    return apiClient.post('/api/rooms/', data);\n  },\n  updateRoom: function updateRoom(roomId, data) {\n    return apiClient.put(\"/api/rooms/\".concat(roomId, \"/\"), data);\n  },\n  deleteRoom: function deleteRoom(roomId) {\n    return apiClient[\"delete\"](\"/api/rooms/\".concat(roomId, \"/\"));\n  },\n  joinRoom: function joinRoom(roomId, data) {\n    return apiClient.post(\"/api/rooms/\".concat(roomId, \"/join/\"), data);\n  },\n  leaveRoom: function leaveRoom(roomId) {\n    return apiClient.post(\"/api/rooms/\".concat(roomId, \"/leave/\"));\n  }\n};\n\n// 用户服务\nvar usersService = {\n  login: function login(data) {\n    return apiClient.post('/api/users/login', data);\n  },\n  register: function register(data) {\n    return apiClient.post('/api/users/register', data);\n  },\n  logout: function logout() {\n    return apiClient.post('/api/users/logout');\n  },\n  getCurrentUser: function getCurrentUser() {\n    return apiClient.get('/api/users/me');\n  },\n  getUser: function getUser(userId) {\n    return apiClient.get(\"/api/users/\".concat(userId));\n  }\n};\n\n// 角色服务\nvar charactersService = {\n  getCharacters: function getCharacters() {\n    var params = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return apiClient.get('/api/characters', {\n      params: params\n    });\n  },\n  getUserCharacters: function getUserCharacters(userId) {\n    return apiClient.get(\"/api/users/\".concat(userId, \"/characters\"));\n  },\n  getCharacter: function getCharacter(characterId) {\n    return apiClient.get(\"/api/characters/\".concat(characterId));\n  },\n  createCharacter: function createCharacter(data) {\n    var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    // 如果没有传入userId，使用默认值1\n    var targetUserId = userId || 1;\n    return apiClient.post(\"/users/\".concat(targetUserId, \"/characters/\"), data);\n  },\n  updateCharacter: function updateCharacter(characterId, data) {\n    return apiClient.put(\"/characters/\".concat(characterId), data);\n  },\n  deleteCharacter: function deleteCharacter(characterId) {\n    return apiClient[\"delete\"](\"/characters/\".concat(characterId));\n  }\n};\n\n// 骰子服务\nvar diceService = {\n  roll: function roll(data) {\n    return apiClient.post('/api/roll', data);\n  },\n  cocRoll: function cocRoll(data) {\n    return apiClient.post('/api/coc-roll', data);\n  },\n  sanityCheck: function sanityCheck(data) {\n    return apiClient.post('/api/sanity-check', data);\n  }\n};\n\n// 游戏存档服务\nvar savesService = {\n  getSaves: function getSaves(roomId) {\n    var params = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    return apiClient.get(\"/api/rooms/\".concat(roomId, \"/game-saves\"), {\n      params: params\n    });\n  },\n  getSave: function getSave(saveId) {\n    return apiClient.get(\"/api/game-saves/\".concat(saveId));\n  },\n  createSave: function createSave(data) {\n    return apiClient.post('/api/game-saves', data);\n  },\n  updateSave: function updateSave(saveId, data) {\n    return apiClient.put(\"/api/game-saves/\".concat(saveId), data);\n  },\n  deleteSave: function deleteSave(saveId) {\n    return apiClient[\"delete\"](\"/api/game-saves/\".concat(saveId));\n  },\n  autoSave: function autoSave(roomId, userId, data, previousAutoSaveId) {\n    return apiClient.post('/api/game-saves/auto-save', {\n      roomId: roomId,\n      creatorId: userId,\n      saveData: data,\n      previousAutoSaveId: previousAutoSaveId\n    });\n  }\n};\n\n// 管理员服务\nvar adminService = {\n  login: function login(data) {\n    console.log('管理员登录API调用，使用路径: /api/users/login', data);\n    return apiClient.post('/api/users/login', data);\n  },\n  getSystemStats: function getSystemStats() {\n    return adminApiClient.get('/admin/system-stats');\n  },\n  getUsers: function getUsers() {\n    return adminApiClient.get('/admin/users');\n  },\n  getUserDetails: function getUserDetails(userId) {\n    return adminApiClient.get(\"/admin/users/\".concat(userId));\n  },\n  toggleAdminStatus: function toggleAdminStatus(userId) {\n    return adminApiClient.put(\"/admin/users/\".concat(userId, \"/toggle-admin\"));\n  },\n  toggleUserActiveStatus: function toggleUserActiveStatus(userId) {\n    return adminApiClient.put(\"/admin/users/\".concat(userId, \"/toggle-active\"));\n  },\n  deleteUser: function deleteUser(userId) {\n    return adminApiClient[\"delete\"](\"/admin/users/\".concat(userId));\n  },\n  getRooms: function getRooms() {\n    return adminApiClient.get('/admin/rooms');\n  },\n  getRoomDetails: function getRoomDetails(roomId) {\n    return adminApiClient.get(\"/admin/rooms/\".concat(roomId));\n  },\n  deleteRoom: function deleteRoom(roomId) {\n    return adminApiClient[\"delete\"](\"/admin/rooms/\".concat(roomId));\n  },\n  getOccupations: function getOccupations() {\n    return apiClient.get('/api/occupations/');\n  },\n  getSkills: function getSkills() {\n    return apiClient.get('/api/skills/');\n  },\n  getWeapons: function getWeapons() {\n    return apiClient.get('/rules/weapons');\n  },\n  getArmors: function getArmors() {\n    return apiClient.get('/rules/armors');\n  },\n  // 角色管理API (已移至charactersService，此处保留兼容性)\n  createCharacter: function createCharacter(characterData) {\n    var userId = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var targetUserId = userId || 1;\n    return apiClient.post(\"/users/\".concat(targetUserId, \"/characters/\"), characterData);\n  },\n  getCharacter: function getCharacter(characterId) {\n    return apiClient.get(\"/api/characters/\".concat(characterId));\n  },\n  // 角色创建辅助API\n  calculateSkillPoints: function calculateSkillPoints(occupationData) {\n    return apiClient.post('/api/calculate-skill-points/', occupationData);\n  },\n  validateAttributes: function validateAttributes(attributeData) {\n    return apiClient.post('/api/characters/validate-attributes', attributeData);\n  },\n  validateSkills: function validateSkills(skillData) {\n    return apiClient.post('/api/characters/validate-skills', skillData);\n  },\n  addOccupation: function addOccupation(occupationData) {\n    return adminApiClient.post('/admin/occupations', occupationData);\n  }\n};\n\n// 导出服务\nvar apiService = {\n  messages: messagesService,\n  ai: aiService,\n  kp: kpService,\n  rooms: roomsService,\n  users: usersService,\n  characters: charactersService,\n  dice: diceService,\n  saves: savesService,\n  admin: adminService\n};\nexport default apiService;", "map": {"version": 3, "names": ["axios", "apiClient", "create", "baseURL", "process", "env", "VUE_APP_API_URL", "withCredentials", "headers", "safeGetToken", "window", "storageManager", "getItem", "localStorage", "error", "console", "warn", "message", "safeGetAdminToken", "interceptors", "request", "use", "config", "token", "Authorization", "concat", "Promise", "reject", "adminApiClient", "adminToken", "messagesService", "getMessages", "roomId", "params", "arguments", "length", "undefined", "get", "sendMessage", "post", "aiService", "data", "processScenario", "testConnection", "clearMemories", "kpService", "loadScenario", "scenarioPath", "asyncLoadScenario", "getDocumentTaskStatus", "taskId", "completeAsyncScenario", "processDocument", "filePath", "getCacheStats", "clearExpiredCache", "getAvailableScenarios", "startGame", "advanceScene", "processInput", "roomsService", "getRooms", "getRoom", "createRoom", "updateRoom", "put", "deleteRoom", "joinRoom", "leaveRoom", "usersService", "login", "register", "logout", "getCurrentUser", "getUser", "userId", "charactersService", "getCharacters", "getUserCharacters", "getCharacter", "characterId", "createCharacter", "targetUserId", "updateCharacter", "deleteCharacter", "diceService", "roll", "coc<PERSON><PERSON>", "sanityCheck", "savesService", "getSaves", "getSave", "saveId", "createSave", "updateSave", "deleteSave", "autoSave", "previousAutoSaveId", "creatorId", "saveData", "adminService", "log", "getSystemStats", "getUsers", "getUserDetails", "toggleAdminStatus", "toggleUserActiveStatus", "deleteUser", "getRoomDetails", "getOccupations", "getSkills", "getWeapons", "getArmors", "characterData", "calculateSkillPoints", "occupationData", "validateAttributes", "attributeData", "validateSkills", "skillData", "addOccupation", "apiService", "messages", "ai", "kp", "rooms", "users", "characters", "dice", "saves", "admin"], "sources": ["C:/Users/<USER>/Desktop/最新的 - 副本/7.26/coc_trpgs/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// 创建带有基础URL的axios实例\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000',\r\n  withCredentials: true,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n});\r\n\r\n// 安全获取token的函数\r\nfunction safeGetToken() {\r\n  try {\r\n    if (window.storageManager) {\r\n      return window.storageManager.getItem('token');\r\n    }\r\n    return localStorage.getItem('token');\r\n  } catch (error) {\r\n    console.warn('[API] 无法获取token:', error.message);\r\n    return null;\r\n  }\r\n}\r\n\r\nfunction safeGetAdminToken() {\r\n  try {\r\n    if (window.storageManager) {\r\n      return window.storageManager.getItem('admin_token');\r\n    }\r\n    return localStorage.getItem('admin_token');\r\n  } catch (error) {\r\n    console.warn('[API] 无法获取admin token:', error.message);\r\n    return null;\r\n  }\r\n}\r\n\r\n// 请求拦截器，添加认证token\r\napiClient.interceptors.request.use(config => {\r\n  const token = safeGetToken();\r\n  if (token) {\r\n    config.headers.Authorization = `Bearer ${token}`;\r\n  }\r\n  return config;\r\n}, error => {\r\n  return Promise.reject(error);\r\n});\r\n\r\n// 创建管理员API客户端实例\r\nconst adminApiClient = axios.create({\r\n  baseURL: process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000',\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json'\r\n  }\r\n});\r\n\r\n// 请求拦截器，添加管理员认证token\r\nadminApiClient.interceptors.request.use(config => {\r\n  const adminToken = safeGetAdminToken();\r\n  if (adminToken) {\r\n    config.headers.Authorization = `Bearer ${adminToken}`;\r\n  }\r\n  return config;\r\n}, error => {\r\n  return Promise.reject(error);\r\n});\r\n\r\n// 消息服务\r\nconst messagesService = {\r\n  getMessages(roomId, params = {}) {\r\n    return apiClient.get(`/api/rooms/${roomId}/messages`, { params });\r\n  },\r\n  \r\n  sendMessage(roomId, message) {\r\n    return apiClient.post(`/api/rooms/${roomId}/messages`, message);\r\n  }\r\n};\r\n\r\n// AI服务\r\nconst aiService = {\r\n  sendMessage(data) {\r\n    return apiClient.post('/api/ai/chat', data);\r\n  },\r\n  \r\n  processScenario(data) {\r\n    return apiClient.post('/api/ai/scenario', data);\r\n  },\r\n  \r\n  testConnection(data) {\r\n    return apiClient.post('/api/ai-settings/test-connection', data);\r\n  },\r\n  \r\n  clearMemories() {\r\n    return apiClient.post('/api/ai/clear-memories');\r\n  }\r\n};\r\n\r\n// KP服务\r\nconst kpService = {\r\n  loadScenario(scenarioPath) {\r\n    return apiClient.post('/api/ai-kp/load-scenario', { scenarioPath });\r\n  },\r\n  \r\n  asyncLoadScenario(scenarioPath) {\r\n    return apiClient.post('/api/ai-kp/async-load-scenario', { scenarioPath });\r\n  },\r\n  \r\n  getDocumentTaskStatus(taskId) {\r\n    return apiClient.get(`/api/ai-kp/document-task-status/${taskId}`);\r\n  },\r\n  \r\n  completeAsyncScenario(taskId) {\r\n    return apiClient.post('/api/ai-kp/complete-async-scenario', { taskId });\r\n  },\r\n  \r\n  processDocument(filePath) {\r\n    return apiClient.post('/api/ai-kp/process-document', { filePath });\r\n  },\r\n  \r\n  getCacheStats() {\r\n    return apiClient.get('/api/ai-kp/cache-stats');\r\n  },\r\n  \r\n  clearExpiredCache() {\r\n    return apiClient.post('/api/ai-kp/clear-expired-cache');\r\n  },\r\n  \r\n  getAvailableScenarios() {\r\n    return apiClient.get('/api/ai-kp/available-scenarios');\r\n  },\r\n  \r\n  startGame(data) {\r\n    return apiClient.post('/api/ai-kp/start-game', data);\r\n  },\r\n  \r\n  advanceScene() {\r\n    return apiClient.post('/api/ai-kp/advance-scene');\r\n  },\r\n  \r\n  processInput(data) {\r\n    return apiClient.post('/api/ai-kp/process-input', data);\r\n  }\r\n};\r\n\r\n// 房间服务\r\nconst roomsService = {\r\n  getRooms(params = {}) {\r\n    return apiClient.get('/api/rooms/', { params });\r\n  },\r\n  \r\n  getRoom(roomId) {\r\n    return apiClient.get(`/api/rooms/${roomId}/`);\r\n  },\r\n  \r\n  createRoom(data) {\r\n    return apiClient.post('/api/rooms/', data);\r\n  },\r\n  \r\n  updateRoom(roomId, data) {\r\n    return apiClient.put(`/api/rooms/${roomId}/`, data);\r\n  },\r\n  \r\n  deleteRoom(roomId) {\r\n    return apiClient.delete(`/api/rooms/${roomId}/`);\r\n  },\r\n  \r\n  joinRoom(roomId, data) {\r\n    return apiClient.post(`/api/rooms/${roomId}/join/`, data);\r\n  },\r\n  \r\n  leaveRoom(roomId) {\r\n    return apiClient.post(`/api/rooms/${roomId}/leave/`);\r\n  }\r\n};\r\n\r\n// 用户服务\r\nconst usersService = {\r\n  login(data) {\r\n    return apiClient.post('/api/users/login', data);\r\n  },\r\n  \r\n  register(data) {\r\n    return apiClient.post('/api/users/register', data);\r\n  },\r\n  \r\n  logout() {\r\n    return apiClient.post('/api/users/logout');\r\n  },\r\n  \r\n  getCurrentUser() {\r\n    return apiClient.get('/api/users/me');\r\n  },\r\n  \r\n  getUser(userId) {\r\n    return apiClient.get(`/api/users/${userId}`);\r\n  }\r\n};\r\n\r\n// 角色服务\r\nconst charactersService = {\r\n  getCharacters(params = {}) {\r\n    return apiClient.get('/api/characters', { params });\r\n  },\r\n  \r\n  getUserCharacters(userId) {\r\n    return apiClient.get(`/api/users/${userId}/characters`);\r\n  },\r\n  \r\n  getCharacter(characterId) {\r\n    return apiClient.get(`/api/characters/${characterId}`);\r\n  },\r\n  \r\n  createCharacter(data, userId = null) {\r\n    // 如果没有传入userId，使用默认值1\r\n    const targetUserId = userId || 1;\r\n    return apiClient.post(`/users/${targetUserId}/characters/`, data);\r\n  },\r\n  \r\n  updateCharacter(characterId, data) {\r\n    return apiClient.put(`/characters/${characterId}`, data);\r\n  },\r\n\r\n  deleteCharacter(characterId) {\r\n    return apiClient.delete(`/characters/${characterId}`);\r\n  }\r\n};\r\n\r\n// 骰子服务\r\nconst diceService = {\r\n  roll(data) {\r\n    return apiClient.post('/api/roll', data);\r\n  },\r\n  \r\n  cocRoll(data) {\r\n    return apiClient.post('/api/coc-roll', data);\r\n  },\r\n  \r\n  sanityCheck(data) {\r\n    return apiClient.post('/api/sanity-check', data);\r\n  }\r\n};\r\n\r\n// 游戏存档服务\r\nconst savesService = {\r\n  getSaves(roomId, params = {}) {\r\n    return apiClient.get(`/api/rooms/${roomId}/game-saves`, { params });\r\n  },\r\n  \r\n  getSave(saveId) {\r\n    return apiClient.get(`/api/game-saves/${saveId}`);\r\n  },\r\n  \r\n  createSave(data) {\r\n    return apiClient.post('/api/game-saves', data);\r\n  },\r\n  \r\n  updateSave(saveId, data) {\r\n    return apiClient.put(`/api/game-saves/${saveId}`, data);\r\n  },\r\n  \r\n  deleteSave(saveId) {\r\n    return apiClient.delete(`/api/game-saves/${saveId}`);\r\n  },\r\n  \r\n  autoSave(roomId, userId, data, previousAutoSaveId) {\r\n    return apiClient.post('/api/game-saves/auto-save', {\r\n      roomId, \r\n      creatorId: userId, \r\n      saveData: data, \r\n      previousAutoSaveId\r\n    });\r\n  }\r\n};\r\n\r\n// 管理员服务\r\nconst adminService = {\r\n  login(data) {\r\n    console.log('管理员登录API调用，使用路径: /api/users/login', data);\r\n    return apiClient.post('/api/users/login', data);\r\n  },\r\n  \r\n  getSystemStats() {\r\n    return adminApiClient.get('/admin/system-stats');\r\n  },\r\n  \r\n  getUsers() {\r\n    return adminApiClient.get('/admin/users');\r\n  },\r\n  \r\n  getUserDetails(userId) {\r\n    return adminApiClient.get(`/admin/users/${userId}`);\r\n  },\r\n  \r\n  toggleAdminStatus(userId) {\r\n    return adminApiClient.put(`/admin/users/${userId}/toggle-admin`);\r\n  },\r\n  \r\n  toggleUserActiveStatus(userId) {\r\n    return adminApiClient.put(`/admin/users/${userId}/toggle-active`);\r\n  },\r\n  \r\n  deleteUser(userId) {\r\n    return adminApiClient.delete(`/admin/users/${userId}`);\r\n  },\r\n  \r\n  getRooms() {\r\n    return adminApiClient.get('/admin/rooms');\r\n  },\r\n  \r\n  getRoomDetails(roomId) {\r\n    return adminApiClient.get(`/admin/rooms/${roomId}`);\r\n  },\r\n  \r\n  deleteRoom(roomId) {\r\n    return adminApiClient.delete(`/admin/rooms/${roomId}`);\r\n  },\r\n  \r\n  getOccupations() {\r\n    return apiClient.get('/api/occupations/');\r\n  },\r\n\r\n  getSkills() {\r\n    return apiClient.get('/api/skills/');\r\n  },\r\n\r\n  getWeapons() {\r\n    return apiClient.get('/rules/weapons');\r\n  },\r\n\r\n  getArmors() {\r\n    return apiClient.get('/rules/armors');\r\n  },\r\n\r\n  // 角色管理API (已移至charactersService，此处保留兼容性)\r\n  createCharacter(characterData, userId = null) {\r\n    const targetUserId = userId || 1;\r\n    return apiClient.post(`/users/${targetUserId}/characters/`, characterData);\r\n  },\r\n\r\n  getCharacter(characterId) {\r\n    return apiClient.get(`/api/characters/${characterId}`);\r\n  },\r\n\r\n\r\n\r\n  // 角色创建辅助API\r\n  calculateSkillPoints(occupationData) {\r\n    return apiClient.post('/api/calculate-skill-points/', occupationData);\r\n  },\r\n\r\n  validateAttributes(attributeData) {\r\n    return apiClient.post('/api/characters/validate-attributes', attributeData);\r\n  },\r\n\r\n  validateSkills(skillData) {\r\n    return apiClient.post('/api/characters/validate-skills', skillData);\r\n  },\r\n\r\n  addOccupation(occupationData) {\r\n    return adminApiClient.post('/admin/occupations', occupationData);\r\n  }\r\n};\r\n\r\n// 导出服务\r\nconst apiService = {\r\n  messages: messagesService,\r\n  ai: aiService,\r\n  kp: kpService,\r\n  rooms: roomsService,\r\n  users: usersService,\r\n  characters: charactersService,\r\n  dice: diceService,\r\n  saves: savesService,\r\n  admin: adminService\r\n};\r\n\r\nexport default apiService; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,IAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC7BC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,uBAAuB;EAC/DC,eAAe,EAAE,IAAI;EACrBC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB,IAAI;IACF,IAAIC,MAAM,CAACC,cAAc,EAAE;MACzB,OAAOD,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC;IAC/C;IACA,OAAOC,YAAY,CAACD,OAAO,CAAC,OAAO,CAAC;EACtC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,kBAAkB,EAAEF,KAAK,CAACG,OAAO,CAAC;IAC/C,OAAO,IAAI;EACb;AACF;AAEA,SAASC,iBAAiBA,CAAA,EAAG;EAC3B,IAAI;IACF,IAAIR,MAAM,CAACC,cAAc,EAAE;MACzB,OAAOD,MAAM,CAACC,cAAc,CAACC,OAAO,CAAC,aAAa,CAAC;IACrD;IACA,OAAOC,YAAY,CAACD,OAAO,CAAC,aAAa,CAAC;EAC5C,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEF,KAAK,CAACG,OAAO,CAAC;IACrD,OAAO,IAAI;EACb;AACF;;AAEA;AACAhB,SAAS,CAACkB,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAI;EAC3C,IAAMC,KAAK,GAAGd,YAAY,CAAC,CAAC;EAC5B,IAAIc,KAAK,EAAE;IACTD,MAAM,CAACd,OAAO,CAACgB,aAAa,aAAAC,MAAA,CAAaF,KAAK,CAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EAAE,UAAAR,KAAK,EAAI;EACV,OAAOY,OAAO,CAACC,MAAM,CAACb,KAAK,CAAC;AAC9B,CAAC,CAAC;;AAEF;AACA,IAAMc,cAAc,GAAG5B,KAAK,CAACE,MAAM,CAAC;EAClCC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,uBAAuB;EAC/DE,OAAO,EAAE;IACP,cAAc,EAAE,kBAAkB;IAClC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;;AAEF;AACAoB,cAAc,CAACT,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAI;EAChD,IAAMO,UAAU,GAAGX,iBAAiB,CAAC,CAAC;EACtC,IAAIW,UAAU,EAAE;IACdP,MAAM,CAACd,OAAO,CAACgB,aAAa,aAAAC,MAAA,CAAaI,UAAU,CAAE;EACvD;EACA,OAAOP,MAAM;AACf,CAAC,EAAE,UAAAR,KAAK,EAAI;EACV,OAAOY,OAAO,CAACC,MAAM,CAACb,KAAK,CAAC;AAC9B,CAAC,CAAC;;AAEF;AACA,IAAMgB,eAAe,GAAG;EACtBC,WAAW,WAAXA,WAAWA,CAACC,MAAM,EAAe;IAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC7B,OAAOjC,SAAS,CAACoC,GAAG,eAAAZ,MAAA,CAAeO,MAAM,gBAAa;MAAEC,MAAM,EAANA;IAAO,CAAC,CAAC;EACnE,CAAC;EAEDK,WAAW,WAAXA,WAAWA,CAACN,MAAM,EAAEf,OAAO,EAAE;IAC3B,OAAOhB,SAAS,CAACsC,IAAI,eAAAd,MAAA,CAAeO,MAAM,gBAAaf,OAAO,CAAC;EACjE;AACF,CAAC;;AAED;AACA,IAAMuB,SAAS,GAAG;EAChBF,WAAW,WAAXA,WAAWA,CAACG,IAAI,EAAE;IAChB,OAAOxC,SAAS,CAACsC,IAAI,CAAC,cAAc,EAAEE,IAAI,CAAC;EAC7C,CAAC;EAEDC,eAAe,WAAfA,eAAeA,CAACD,IAAI,EAAE;IACpB,OAAOxC,SAAS,CAACsC,IAAI,CAAC,kBAAkB,EAAEE,IAAI,CAAC;EACjD,CAAC;EAEDE,cAAc,WAAdA,cAAcA,CAACF,IAAI,EAAE;IACnB,OAAOxC,SAAS,CAACsC,IAAI,CAAC,kCAAkC,EAAEE,IAAI,CAAC;EACjE,CAAC;EAEDG,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,OAAO3C,SAAS,CAACsC,IAAI,CAAC,wBAAwB,CAAC;EACjD;AACF,CAAC;;AAED;AACA,IAAMM,SAAS,GAAG;EAChBC,YAAY,WAAZA,YAAYA,CAACC,YAAY,EAAE;IACzB,OAAO9C,SAAS,CAACsC,IAAI,CAAC,0BAA0B,EAAE;MAAEQ,YAAY,EAAZA;IAAa,CAAC,CAAC;EACrE,CAAC;EAEDC,iBAAiB,WAAjBA,iBAAiBA,CAACD,YAAY,EAAE;IAC9B,OAAO9C,SAAS,CAACsC,IAAI,CAAC,gCAAgC,EAAE;MAAEQ,YAAY,EAAZA;IAAa,CAAC,CAAC;EAC3E,CAAC;EAEDE,qBAAqB,WAArBA,qBAAqBA,CAACC,MAAM,EAAE;IAC5B,OAAOjD,SAAS,CAACoC,GAAG,oCAAAZ,MAAA,CAAoCyB,MAAM,CAAE,CAAC;EACnE,CAAC;EAEDC,qBAAqB,WAArBA,qBAAqBA,CAACD,MAAM,EAAE;IAC5B,OAAOjD,SAAS,CAACsC,IAAI,CAAC,oCAAoC,EAAE;MAAEW,MAAM,EAANA;IAAO,CAAC,CAAC;EACzE,CAAC;EAEDE,eAAe,WAAfA,eAAeA,CAACC,QAAQ,EAAE;IACxB,OAAOpD,SAAS,CAACsC,IAAI,CAAC,6BAA6B,EAAE;MAAEc,QAAQ,EAARA;IAAS,CAAC,CAAC;EACpE,CAAC;EAEDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,OAAOrD,SAAS,CAACoC,GAAG,CAAC,wBAAwB,CAAC;EAChD,CAAC;EAEDkB,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;IAClB,OAAOtD,SAAS,CAACsC,IAAI,CAAC,gCAAgC,CAAC;EACzD,CAAC;EAEDiB,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;IACtB,OAAOvD,SAAS,CAACoC,GAAG,CAAC,gCAAgC,CAAC;EACxD,CAAC;EAEDoB,SAAS,WAATA,SAASA,CAAChB,IAAI,EAAE;IACd,OAAOxC,SAAS,CAACsC,IAAI,CAAC,uBAAuB,EAAEE,IAAI,CAAC;EACtD,CAAC;EAEDiB,YAAY,WAAZA,YAAYA,CAAA,EAAG;IACb,OAAOzD,SAAS,CAACsC,IAAI,CAAC,0BAA0B,CAAC;EACnD,CAAC;EAEDoB,YAAY,WAAZA,YAAYA,CAAClB,IAAI,EAAE;IACjB,OAAOxC,SAAS,CAACsC,IAAI,CAAC,0BAA0B,EAAEE,IAAI,CAAC;EACzD;AACF,CAAC;;AAED;AACA,IAAMmB,YAAY,GAAG;EACnBC,QAAQ,WAARA,QAAQA,CAAA,EAAc;IAAA,IAAb5B,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAClB,OAAOjC,SAAS,CAACoC,GAAG,CAAC,aAAa,EAAE;MAAEJ,MAAM,EAANA;IAAO,CAAC,CAAC;EACjD,CAAC;EAED6B,OAAO,WAAPA,OAAOA,CAAC9B,MAAM,EAAE;IACd,OAAO/B,SAAS,CAACoC,GAAG,eAAAZ,MAAA,CAAeO,MAAM,MAAG,CAAC;EAC/C,CAAC;EAED+B,UAAU,WAAVA,UAAUA,CAACtB,IAAI,EAAE;IACf,OAAOxC,SAAS,CAACsC,IAAI,CAAC,aAAa,EAAEE,IAAI,CAAC;EAC5C,CAAC;EAEDuB,UAAU,WAAVA,UAAUA,CAAChC,MAAM,EAAES,IAAI,EAAE;IACvB,OAAOxC,SAAS,CAACgE,GAAG,eAAAxC,MAAA,CAAeO,MAAM,QAAKS,IAAI,CAAC;EACrD,CAAC;EAEDyB,UAAU,WAAVA,UAAUA,CAAClC,MAAM,EAAE;IACjB,OAAO/B,SAAS,UAAO,eAAAwB,MAAA,CAAeO,MAAM,MAAG,CAAC;EAClD,CAAC;EAEDmC,QAAQ,WAARA,QAAQA,CAACnC,MAAM,EAAES,IAAI,EAAE;IACrB,OAAOxC,SAAS,CAACsC,IAAI,eAAAd,MAAA,CAAeO,MAAM,aAAUS,IAAI,CAAC;EAC3D,CAAC;EAED2B,SAAS,WAATA,SAASA,CAACpC,MAAM,EAAE;IAChB,OAAO/B,SAAS,CAACsC,IAAI,eAAAd,MAAA,CAAeO,MAAM,YAAS,CAAC;EACtD;AACF,CAAC;;AAED;AACA,IAAMqC,YAAY,GAAG;EACnBC,KAAK,WAALA,KAAKA,CAAC7B,IAAI,EAAE;IACV,OAAOxC,SAAS,CAACsC,IAAI,CAAC,kBAAkB,EAAEE,IAAI,CAAC;EACjD,CAAC;EAED8B,QAAQ,WAARA,QAAQA,CAAC9B,IAAI,EAAE;IACb,OAAOxC,SAAS,CAACsC,IAAI,CAAC,qBAAqB,EAAEE,IAAI,CAAC;EACpD,CAAC;EAED+B,MAAM,WAANA,MAAMA,CAAA,EAAG;IACP,OAAOvE,SAAS,CAACsC,IAAI,CAAC,mBAAmB,CAAC;EAC5C,CAAC;EAEDkC,cAAc,WAAdA,cAAcA,CAAA,EAAG;IACf,OAAOxE,SAAS,CAACoC,GAAG,CAAC,eAAe,CAAC;EACvC,CAAC;EAEDqC,OAAO,WAAPA,OAAOA,CAACC,MAAM,EAAE;IACd,OAAO1E,SAAS,CAACoC,GAAG,eAAAZ,MAAA,CAAekD,MAAM,CAAE,CAAC;EAC9C;AACF,CAAC;;AAED;AACA,IAAMC,iBAAiB,GAAG;EACxBC,aAAa,WAAbA,aAAaA,CAAA,EAAc;IAAA,IAAb5C,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACvB,OAAOjC,SAAS,CAACoC,GAAG,CAAC,iBAAiB,EAAE;MAAEJ,MAAM,EAANA;IAAO,CAAC,CAAC;EACrD,CAAC;EAED6C,iBAAiB,WAAjBA,iBAAiBA,CAACH,MAAM,EAAE;IACxB,OAAO1E,SAAS,CAACoC,GAAG,eAAAZ,MAAA,CAAekD,MAAM,gBAAa,CAAC;EACzD,CAAC;EAEDI,YAAY,WAAZA,YAAYA,CAACC,WAAW,EAAE;IACxB,OAAO/E,SAAS,CAACoC,GAAG,oBAAAZ,MAAA,CAAoBuD,WAAW,CAAE,CAAC;EACxD,CAAC;EAEDC,eAAe,WAAfA,eAAeA,CAACxC,IAAI,EAAiB;IAAA,IAAfkC,MAAM,GAAAzC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACjC;IACA,IAAMgD,YAAY,GAAGP,MAAM,IAAI,CAAC;IAChC,OAAO1E,SAAS,CAACsC,IAAI,WAAAd,MAAA,CAAWyD,YAAY,mBAAgBzC,IAAI,CAAC;EACnE,CAAC;EAED0C,eAAe,WAAfA,eAAeA,CAACH,WAAW,EAAEvC,IAAI,EAAE;IACjC,OAAOxC,SAAS,CAACgE,GAAG,gBAAAxC,MAAA,CAAgBuD,WAAW,GAAIvC,IAAI,CAAC;EAC1D,CAAC;EAED2C,eAAe,WAAfA,eAAeA,CAACJ,WAAW,EAAE;IAC3B,OAAO/E,SAAS,UAAO,gBAAAwB,MAAA,CAAgBuD,WAAW,CAAE,CAAC;EACvD;AACF,CAAC;;AAED;AACA,IAAMK,WAAW,GAAG;EAClBC,IAAI,WAAJA,IAAIA,CAAC7C,IAAI,EAAE;IACT,OAAOxC,SAAS,CAACsC,IAAI,CAAC,WAAW,EAAEE,IAAI,CAAC;EAC1C,CAAC;EAED8C,OAAO,WAAPA,OAAOA,CAAC9C,IAAI,EAAE;IACZ,OAAOxC,SAAS,CAACsC,IAAI,CAAC,eAAe,EAAEE,IAAI,CAAC;EAC9C,CAAC;EAED+C,WAAW,WAAXA,WAAWA,CAAC/C,IAAI,EAAE;IAChB,OAAOxC,SAAS,CAACsC,IAAI,CAAC,mBAAmB,EAAEE,IAAI,CAAC;EAClD;AACF,CAAC;;AAED;AACA,IAAMgD,YAAY,GAAG;EACnBC,QAAQ,WAARA,QAAQA,CAAC1D,MAAM,EAAe;IAAA,IAAbC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC1B,OAAOjC,SAAS,CAACoC,GAAG,eAAAZ,MAAA,CAAeO,MAAM,kBAAe;MAAEC,MAAM,EAANA;IAAO,CAAC,CAAC;EACrE,CAAC;EAED0D,OAAO,WAAPA,OAAOA,CAACC,MAAM,EAAE;IACd,OAAO3F,SAAS,CAACoC,GAAG,oBAAAZ,MAAA,CAAoBmE,MAAM,CAAE,CAAC;EACnD,CAAC;EAEDC,UAAU,WAAVA,UAAUA,CAACpD,IAAI,EAAE;IACf,OAAOxC,SAAS,CAACsC,IAAI,CAAC,iBAAiB,EAAEE,IAAI,CAAC;EAChD,CAAC;EAEDqD,UAAU,WAAVA,UAAUA,CAACF,MAAM,EAAEnD,IAAI,EAAE;IACvB,OAAOxC,SAAS,CAACgE,GAAG,oBAAAxC,MAAA,CAAoBmE,MAAM,GAAInD,IAAI,CAAC;EACzD,CAAC;EAEDsD,UAAU,WAAVA,UAAUA,CAACH,MAAM,EAAE;IACjB,OAAO3F,SAAS,UAAO,oBAAAwB,MAAA,CAAoBmE,MAAM,CAAE,CAAC;EACtD,CAAC;EAEDI,QAAQ,WAARA,QAAQA,CAAChE,MAAM,EAAE2C,MAAM,EAAElC,IAAI,EAAEwD,kBAAkB,EAAE;IACjD,OAAOhG,SAAS,CAACsC,IAAI,CAAC,2BAA2B,EAAE;MACjDP,MAAM,EAANA,MAAM;MACNkE,SAAS,EAAEvB,MAAM;MACjBwB,QAAQ,EAAE1D,IAAI;MACdwD,kBAAkB,EAAlBA;IACF,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,IAAMG,YAAY,GAAG;EACnB9B,KAAK,WAALA,KAAKA,CAAC7B,IAAI,EAAE;IACV1B,OAAO,CAACsF,GAAG,CAAC,mCAAmC,EAAE5D,IAAI,CAAC;IACtD,OAAOxC,SAAS,CAACsC,IAAI,CAAC,kBAAkB,EAAEE,IAAI,CAAC;EACjD,CAAC;EAED6D,cAAc,WAAdA,cAAcA,CAAA,EAAG;IACf,OAAO1E,cAAc,CAACS,GAAG,CAAC,qBAAqB,CAAC;EAClD,CAAC;EAEDkE,QAAQ,WAARA,QAAQA,CAAA,EAAG;IACT,OAAO3E,cAAc,CAACS,GAAG,CAAC,cAAc,CAAC;EAC3C,CAAC;EAEDmE,cAAc,WAAdA,cAAcA,CAAC7B,MAAM,EAAE;IACrB,OAAO/C,cAAc,CAACS,GAAG,iBAAAZ,MAAA,CAAiBkD,MAAM,CAAE,CAAC;EACrD,CAAC;EAED8B,iBAAiB,WAAjBA,iBAAiBA,CAAC9B,MAAM,EAAE;IACxB,OAAO/C,cAAc,CAACqC,GAAG,iBAAAxC,MAAA,CAAiBkD,MAAM,kBAAe,CAAC;EAClE,CAAC;EAED+B,sBAAsB,WAAtBA,sBAAsBA,CAAC/B,MAAM,EAAE;IAC7B,OAAO/C,cAAc,CAACqC,GAAG,iBAAAxC,MAAA,CAAiBkD,MAAM,mBAAgB,CAAC;EACnE,CAAC;EAEDgC,UAAU,WAAVA,UAAUA,CAAChC,MAAM,EAAE;IACjB,OAAO/C,cAAc,UAAO,iBAAAH,MAAA,CAAiBkD,MAAM,CAAE,CAAC;EACxD,CAAC;EAEDd,QAAQ,WAARA,QAAQA,CAAA,EAAG;IACT,OAAOjC,cAAc,CAACS,GAAG,CAAC,cAAc,CAAC;EAC3C,CAAC;EAEDuE,cAAc,WAAdA,cAAcA,CAAC5E,MAAM,EAAE;IACrB,OAAOJ,cAAc,CAACS,GAAG,iBAAAZ,MAAA,CAAiBO,MAAM,CAAE,CAAC;EACrD,CAAC;EAEDkC,UAAU,WAAVA,UAAUA,CAAClC,MAAM,EAAE;IACjB,OAAOJ,cAAc,UAAO,iBAAAH,MAAA,CAAiBO,MAAM,CAAE,CAAC;EACxD,CAAC;EAED6E,cAAc,WAAdA,cAAcA,CAAA,EAAG;IACf,OAAO5G,SAAS,CAACoC,GAAG,CAAC,mBAAmB,CAAC;EAC3C,CAAC;EAEDyE,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,OAAO7G,SAAS,CAACoC,GAAG,CAAC,cAAc,CAAC;EACtC,CAAC;EAED0E,UAAU,WAAVA,UAAUA,CAAA,EAAG;IACX,OAAO9G,SAAS,CAACoC,GAAG,CAAC,gBAAgB,CAAC;EACxC,CAAC;EAED2E,SAAS,WAATA,SAASA,CAAA,EAAG;IACV,OAAO/G,SAAS,CAACoC,GAAG,CAAC,eAAe,CAAC;EACvC,CAAC;EAED;EACA4C,eAAe,WAAfA,eAAeA,CAACgC,aAAa,EAAiB;IAAA,IAAftC,MAAM,GAAAzC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAC1C,IAAMgD,YAAY,GAAGP,MAAM,IAAI,CAAC;IAChC,OAAO1E,SAAS,CAACsC,IAAI,WAAAd,MAAA,CAAWyD,YAAY,mBAAgB+B,aAAa,CAAC;EAC5E,CAAC;EAEDlC,YAAY,WAAZA,YAAYA,CAACC,WAAW,EAAE;IACxB,OAAO/E,SAAS,CAACoC,GAAG,oBAAAZ,MAAA,CAAoBuD,WAAW,CAAE,CAAC;EACxD,CAAC;EAID;EACAkC,oBAAoB,WAApBA,oBAAoBA,CAACC,cAAc,EAAE;IACnC,OAAOlH,SAAS,CAACsC,IAAI,CAAC,8BAA8B,EAAE4E,cAAc,CAAC;EACvE,CAAC;EAEDC,kBAAkB,WAAlBA,kBAAkBA,CAACC,aAAa,EAAE;IAChC,OAAOpH,SAAS,CAACsC,IAAI,CAAC,qCAAqC,EAAE8E,aAAa,CAAC;EAC7E,CAAC;EAEDC,cAAc,WAAdA,cAAcA,CAACC,SAAS,EAAE;IACxB,OAAOtH,SAAS,CAACsC,IAAI,CAAC,iCAAiC,EAAEgF,SAAS,CAAC;EACrE,CAAC;EAEDC,aAAa,WAAbA,aAAaA,CAACL,cAAc,EAAE;IAC5B,OAAOvF,cAAc,CAACW,IAAI,CAAC,oBAAoB,EAAE4E,cAAc,CAAC;EAClE;AACF,CAAC;;AAED;AACA,IAAMM,UAAU,GAAG;EACjBC,QAAQ,EAAE5F,eAAe;EACzB6F,EAAE,EAAEnF,SAAS;EACboF,EAAE,EAAE/E,SAAS;EACbgF,KAAK,EAAEjE,YAAY;EACnBkE,KAAK,EAAEzD,YAAY;EACnB0D,UAAU,EAAEnD,iBAAiB;EAC7BoD,IAAI,EAAE3C,WAAW;EACjB4C,KAAK,EAAExC,YAAY;EACnByC,KAAK,EAAE9B;AACT,CAAC;AAED,eAAeqB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}