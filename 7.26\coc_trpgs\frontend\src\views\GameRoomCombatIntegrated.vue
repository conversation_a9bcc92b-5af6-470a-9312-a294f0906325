<template>
  <div class="game-room" :class="{ 'combat-mode': combatActive }">
    <!-- 房间头部信息 -->
    <div class="room-header">
      <div class="room-info">
        <div class="room-title">
          <i class="fas fa-door-open"></i>
          <h1>{{ roomData.name || '游戏房间' }}</h1>
          <div class="room-status" :class="roomData.status">
            <span class="status-dot"></span>
            <span>{{ getStatusText() }}</span>
          </div>
          <!-- 战斗状态指示器 -->
          <div v-if="combatActive" class="combat-indicator">
            <i class="fas fa-sword"></i>
            <span>战斗进行中</span>
          </div>
        </div>
        <div class="room-meta">
          <span class="room-creator">
            <i class="fas fa-crown"></i>
            KP: {{ roomData.creator?.username || '未知' }}
          </span>
          <span class="room-players">
            <i class="fas fa-users"></i>
            玩家: {{ currentPlayers }}/{{ roomData.maxPlayers || 6 }}
          </span>
          <span v-if="combatActive" class="combat-round">
            <i class="fas fa-clock"></i>
            第{{ combatData?.currentRound || 1 }}轮
          </span>
        </div>
      </div>
      
      <div class="room-controls">
        <!-- KP战斗控制按钮 -->
        <button 
          v-if="isKeeper && !combatActive" 
          @click="startCombat" 
          class="control-btn combat-btn"
          title="开始战斗"
        >
          <i class="fas fa-sword"></i>
        </button>
        <button 
          v-if="isKeeper && combatActive" 
          @click="endCombat" 
          class="control-btn combat-btn active"
          title="结束战斗"
        >
          <i class="fas fa-stop"></i>
        </button>
        <button @click="toggleFullscreen" class="control-btn" title="全屏模式">
          <i class="fas fa-expand"></i>
        </button>
        <button @click="toggleSettings" class="control-btn" title="房间设置">
          <i class="fas fa-cog"></i>
        </button>
        <button @click="leaveRoom" class="control-btn leave-btn" title="离开房间">
          <i class="fas fa-sign-out-alt"></i>
        </button>
      </div>
    </div>

    <!-- 游戏布局 -->
    <div class="game-layout" :class="{ 'combat-layout': combatActive }">
      <!-- 左侧面板：角色信息 + 先攻追踪器 -->
      <div class="left-panel" :class="{ 'collapsed': leftPanelCollapsed }">
        <div class="panel-header">
          <div class="panel-title">
            <i :class="combatActive ? 'fas fa-users-cog' : 'fas fa-users'"></i>
            <span>{{ combatActive ? '战斗状态' : '角色信息' }}</span>
          </div>
          <button @click="toggleLeftPanel" class="panel-toggle">
            <i :class="leftPanelCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'"></i>
          </button>
        </div>
        
        <div class="panel-content" v-show="!leftPanelCollapsed">
          <!-- 非战斗时：角色列表 -->
          <div v-if="!combatActive" class="character-section">
            <div class="character-list">
              <div v-for="player in players" :key="player.id" class="character-card">
                <div class="character-avatar">
                  <img :src="player.avatar || '/images/default-avatar.png'" :alt="player.username" />
                  <div class="character-status" :class="player.status"></div>
                </div>
                <div class="character-info">
                  <div class="character-name">{{ player.characterName || player.username }}</div>
                  <div class="character-role" :class="{ 'kp': player.isKP }">
                    {{ player.isKP ? 'KP' : 'PL' }}
                  </div>
                  <div class="character-stats" v-if="player.stats">
                    <div class="stat-item">
                      <span class="stat-label">HP:</span>
                      <div class="stat-bar">
                        <div class="stat-fill hp" :style="{ width: getStatPercentage(player.stats.hp, player.stats.maxHp) + '%' }"></div>
                      </div>
                      <span class="stat-value">{{ player.stats.hp }}/{{ player.stats.maxHp }}</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-label">SAN:</span>
                      <div class="stat-bar">
                        <div class="stat-fill san" :style="{ width: getStatPercentage(player.stats.san, player.stats.maxSan) + '%' }"></div>
                      </div>
                      <span class="stat-value">{{ player.stats.san }}/{{ player.stats.maxSan }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="quick-actions">
              <button @click="openCharacterSheet" class="action-btn">
                <i class="fas fa-id-card"></i>
                <span>角色卡</span>
              </button>
              <button @click="openDiceRoller" class="action-btn">
                <i class="fas fa-dice"></i>
                <span>投骰</span>
              </button>
              <button @click="openInventory" class="action-btn">
                <i class="fas fa-backpack"></i>
                <span>背包</span>
              </button>
            </div>
          </div>
          
          <!-- 战斗时：先攻追踪器 -->
          <div v-if="combatActive" class="combat-section">
            <InitiativeTracker
              :initiative-order="combatData?.initiativeOrder || []"
              :current-round="combatData?.currentRound || 1"
              :current-turn="combatData?.currentTurn || 0"
              @participant-selected="handleParticipantSelect"
            />
          </div>
        </div>
      </div>    
  <!-- 中央面板：地图/战场 -->
      <div class="center-panel">
        <!-- 非战斗时：场景标签页 -->
        <div v-if="!combatActive" class="scene-tabs">
          <button 
            v-for="tab in sceneTabs" 
            :key="tab.id"
            @click="activeSceneTab = tab.id"
            class="scene-tab"
            :class="{ 'active': activeSceneTab === tab.id }"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.name }}</span>
            <span v-if="tab.badge" class="tab-badge">{{ tab.badge }}</span>
          </button>
        </div>
        
        <div class="scene-content">
          <!-- 非战斗时：地图场景 -->
          <div v-if="!combatActive && activeSceneTab === 'map'" class="scene-panel map-scene">
            <div class="map-container">
              <div class="map-placeholder">
                <i class="fas fa-map"></i>
                <h3>地图系统</h3>
                <p>地图和战斗场景将在这里显示</p>
                <button v-if="isKeeper" @click="uploadMap" class="upload-map-btn">
                  <i class="fas fa-upload"></i>
                  <span>上传地图</span>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 战斗时：2D战场 -->
          <div v-if="combatActive" class="combat-scene">
            <BattlefieldGrid
              :characters="combatData?.participants?.filter(p => p.isPlayer) || []"
              :monsters="combatData?.participants?.filter(p => !p.isPlayer) || []"
              :obstacles="combatData?.obstacles || []"
              :area-effects="combatData?.areaEffects || []"
              :current-round="combatData?.currentRound || 1"
              :current-turn-character="getCurrentTurnCharacter()"
              :is-keeper="isKeeper"
              :battlefield-width="combatData?.battlefieldSize?.width || 20"
              :battlefield-height="combatData?.battlefieldSize?.height || 15"
              @character-selected="handleCharacterSelected"
              @monster-selected="handleMonsterSelected"
              @character-moved="handleCharacterMoved"
              @monster-moved="handleMonsterMoved"
              @character-action="handleCharacterAction"
              @monster-action="handleMonsterAction"
              @inspect-character="handleInspectCharacter"
              @inspect-monster="handleInspectMonster"
              @add-monster="handleAddMonster"
            />
          </div>
          
          <!-- 非战斗时：其他场景 -->
          <div v-if="!combatActive && activeSceneTab === 'clues'" class="scene-panel clues-scene">
            <div class="clues-header">
              <h3>线索墙</h3>
              <button v-if="isKeeper" @click="addClue" class="add-btn">
                <i class="fas fa-plus"></i>
                <span>添加线索</span>
              </button>
            </div>
            <ClueBoard 
              :clues="clues"
              :can-edit="isKeeper"
              @add-clue="addClue"
              @update-clue="updateClue"
              @delete-clue="deleteClue"
            />
          </div>
          
          <div v-if="!combatActive && activeSceneTab === 'notes'" class="scene-panel notes-scene">
            <div class="notes-header">
              <h3>笔记本</h3>
              <div class="notes-controls">
                <button @click="addNote" class="add-btn">
                  <i class="fas fa-plus"></i>
                  <span>新建笔记</span>
                </button>
                <select v-model="noteFilter" class="note-filter">
                  <option value="all">全部笔记</option>
                  <option value="personal">个人笔记</option>
                  <option value="shared">共享笔记</option>
                </select>
              </div>
            </div>
            <div class="notes-list">
              <div v-for="note in filteredNotes" :key="note.id" class="note-card">
                <div class="note-header">
                  <h4 class="note-title">{{ note.title }}</h4>
                  <div class="note-meta">
                    <span class="note-author">{{ note.author }}</span>
                    <span class="note-time">{{ formatTime(note.updatedAt) }}</span>
                  </div>
                </div>
                <div class="note-content">{{ note.content }}</div>
                <div class="note-actions">
                  <button @click="editNote(note)" class="note-action-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button @click="shareNote(note)" class="note-action-btn">
                    <i class="fas fa-share"></i>
                  </button>
                  <button @click="deleteNote(note)" class="note-action-btn delete">
                    <i class="fas fa-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- KP战斗控制面板 (仅KP可见且非战斗时) -->
        <div v-if="isKeeper && !combatActive" class="kp-combat-controls">
          <KeeperCombatPanel
            :is-keeper="isKeeper"
            :room-data="roomData"
            :players="players"
            @combat-started="handleCombatStart"
            @combat-ended="handleCombatEnd"
            @participant-added="handleParticipantAdded"
          />
        </div>
      </div>

      <!-- 右侧面板：聊天 + 战斗日志 -->
      <div class="right-panel" :class="{ 'collapsed': rightPanelCollapsed }">
        <div class="panel-header">
          <div class="panel-title">
            <i :class="combatActive ? 'fas fa-scroll' : 'fas fa-comments'"></i>
            <span>{{ combatActive ? '战斗日志' : '聊天' }}</span>
          </div>
          <div v-if="!combatActive" class="chat-controls">
            <button 
              @click="chatMode = 'ic'" 
              class="chat-mode-btn" 
              :class="{ 'active': chatMode === 'ic' }"
            >
              IC
            </button>
            <button 
              @click="chatMode = 'ooc'" 
              class="chat-mode-btn" 
              :class="{ 'active': chatMode === 'ooc' }"
            >
              OOC
            </button>
          </div>
          <button @click="toggleRightPanel" class="panel-toggle">
            <i :class="rightPanelCollapsed ? 'fas fa-chevron-left' : 'fas fa-chevron-right'"></i>
          </button>
        </div>
        
        <div class="panel-content" v-show="!rightPanelCollapsed">
          <!-- 非战斗时：聊天系统 -->
          <div v-if="!combatActive" class="chat-section">
            <ChatBox 
              :messages="messages"
              :current-user="currentUser"
              :chat-mode="chatMode"
              @send-message="sendMessage"
              @roll-dice="handleDiceRoll"
            />
          </div>
          
          <!-- 战斗时：战斗日志 -->
          <div v-if="combatActive" class="combat-log-section">
            <CombatLog 
              :combat-logs="combatLogs"
              :current-round="combatData?.currentRound || 1"
              @log-action="handleLogAction"
            />
          </div>
        </div>
      </div>
    </div>  
  <!-- 底部工具栏 -->
    <div class="bottom-toolbar" :class="{ 'combat-toolbar': combatActive }">
      <div class="toolbar-left">
        <div class="audio-controls">
          <button @click="toggleMute" class="audio-btn" :class="{ 'muted': isMuted }">
            <i :class="isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'"></i>
          </button>
          <button @click="toggleDeafen" class="audio-btn" :class="{ 'deafened': isDeafened }">
            <i :class="isDeafened ? 'fas fa-volume-mute' : 'fas fa-volume-up'"></i>
          </button>
          <div class="volume-control">
            <input type="range" v-model="volume" min="0" max="100" class="volume-slider" />
          </div>
        </div>
      </div>
      
      <div class="toolbar-center">
        <!-- 非战斗时：常规工具 -->
        <template v-if="!combatActive">
          <button @click="quickRoll" class="toolbar-btn primary">
            <i class="fas fa-dice-d20"></i>
            <span>快速投骰</span>
          </button>
          <button @click="openDiceRoller" class="toolbar-btn">
            <i class="fas fa-dice"></i>
            <span>骰子面板</span>
          </button>
        </template>
        
        <!-- 战斗时：战斗工具 -->
        <template v-if="combatActive">
          <button @click="nextTurn" class="toolbar-btn primary" :disabled="!isKeeper">
            <i class="fas fa-step-forward"></i>
            <span>下一回合</span>
          </button>
          <button @click="openCombatActions" class="toolbar-btn">
            <i class="fas fa-fist-raised"></i>
            <span>行动菜单</span>
          </button>
          <button @click="openDiceRoller" class="toolbar-btn">
            <i class="fas fa-dice"></i>
            <span>投骰</span>
          </button>
        </template>
      </div>
      
      <div class="toolbar-right">
        <div class="connection-status">
          <div class="status-indicator" :class="connectionStatus">
            <i :class="getConnectionIcon()"></i>
          </div>
          <span class="status-text">{{ getConnectionText() }}</span>
        </div>
      </div>
    </div>

    <!-- 浮动组件 -->
    <!-- 骰子面板 -->
    <DiceRoller 
      v-if="showDiceRoller" 
      @close="showDiceRoller = false"
      @roll="handleDiceRoll"
      @skill-check="handleSkillCheck"
    />
    
    <!-- 强制战斗模式覆盖层 (玩家) -->
    <ForcedCombatMode
      v-if="combatActive && !isKeeper && shouldShowForcedMode"
      :is-active="combatActive"
      :is-keeper="isKeeper"
      :combat-data="combatData"
      :character="currentPlayerCharacter"
      @action-confirmed="handlePlayerCombatAction"
      @combat-mode-entered="onCombatModeEntered"
      @combat-mode-exited="onCombatModeExited"
    />
    
    <!-- KP战斗控制面板 (战斗时) -->
    <div v-if="combatActive && isKeeper" class="keeper-combat-overlay">
      <KeeperCombatPanel
        :is-keeper="isKeeper"
        :combat-data="combatData"
        :players="players"
        @combat-action="handleKeeperCombatAction"
        @end-combat="handleCombatEnd"
        @next-turn="handleNextTurn"
        @add-participant="handleAddParticipant"
        @remove-participant="handleRemoveParticipant"
      />
    </div>
    
    <!-- 角色详情浮动面板 -->
    <FloatingCharacterSheet 
      v-if="showCharacterSheet"
      :character="selectedCharacterForSheet"
      @close="showCharacterSheet = false"
      @update="updateCharacter"
    />
    
    <!-- 怪物详情浮动面板 (暂时使用角色卡组件) -->
    <FloatingCharacterSheet 
      v-if="showMonsterSheet"
      :character="selectedMonsterForSheet"
      @close="showMonsterSheet = false"
      @update="updateMonster"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import ChatBox from '@/components/ChatBox.vue'
import DiceRoller from '@/components/DiceRoller.vue'
import CombatLog from '@/components/combat/CombatLog.vue'
import BattlefieldGrid from '@/components/combat/BattlefieldGrid.vue'
import InitiativeTracker from '@/components/combat/InitiativeTracker.vue'
import KeeperCombatPanel from '@/components/combat/KeeperCombatPanel.vue'
import ForcedCombatMode from '@/components/combat/ForcedCombatMode.vue'
import FloatingCharacterSheet from '@/components/FloatingCharacterSheet.vue'
import ClueBoard from '@/components/ClueBoard.vue'
import { storageMixin } from '@/mixins/storageMixin'

export default {
  name: 'GameRoomCombatIntegrated',
  mixins: [storageMixin],
  components: {
    ChatBox,
    DiceRoller,
    CombatLog,
    BattlefieldGrid,
    InitiativeTracker,
    KeeperCombatPanel,
    ForcedCombatMode,
    FloatingCharacterSheet,
    ClueBoard
  },
  props: {
    roomId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // 房间数据
      roomData: {
        name: '神秘的古宅',
        status: 'active',
        creator: { username: 'KP_Master' },
        maxPlayers: 6
      },
      
      // 战斗状态
      combatActive: false,
      combatData: null,
      combatLogs: [],
      combatWebSocket: null,
      shouldShowForcedMode: false,
      
      // 面板状态
      leftPanelCollapsed: false,
      rightPanelCollapsed: false,
      activeSceneTab: 'map',
      
      // 聊天相关
      chatMode: 'ic',
      messages: [],
      
      // 玩家数据
      players: [],
      
      // 线索数据
      clues: [
        {
          id: 1,
          title: '神秘的日记',
          content: '在废弃的房屋中发现了一本日记，记录着奇怪的仪式...',
          type: 'important',
          discoverer: 'Player1',
          createdAt: new Date()
        }
      ],
      
      // 笔记数据
      notes: [
        {
          id: 1,
          title: '调查笔记',
          content: '今天的调查发现了一些线索...',
          author: 'Player1',
          shared: false,
          updatedAt: new Date()
        }
      ],
      
      noteFilter: 'all',
      
      // 场景数据
      sceneTabs: [
        { id: 'map', name: '地图', icon: 'fas fa-map', badge: null },
        { id: 'clues', name: '线索', icon: 'fas fa-search', badge: 3 },
        { id: 'notes', name: '笔记', icon: 'fas fa-sticky-note', badge: null }
      ],
      
      // 音频控制
      isMuted: false,
      isDeafened: false,
      volume: 50,
      
      // 连接状态
      connectionStatus: 'connected',
      
      // 组件状态
      showDiceRoller: false,
      showCharacterSheet: false,
      showMonsterSheet: false,
      selectedCharacterForSheet: null,
      selectedMonsterForSheet: null,
      
      // 选中状态
      selectedParticipant: null,
      currentPlayerCharacter: null
    }
  },
  computed: {
    ...mapGetters(['currentUser']),
    
    currentPlayers() {
      return this.players.length
    },
    
    isKeeper() {
      return this.currentUser?.isKP || false
    },
    
    filteredNotes() {
      if (this.noteFilter === 'all') return this.notes
      if (this.noteFilter === 'personal') return this.notes.filter(note => note.author === this.currentUser?.username)
      if (this.noteFilter === 'shared') return this.notes.filter(note => note.shared)
      return this.notes
    }
  },
  
  async mounted() {
    await this.initializeRoom()
    await this.initializeCombatWebSocket()
    this.restorePanelStates()
  },
  
  beforeUnmount() {
    this.savePanelStates()
    this.cleanupWebSocket()
  },
  
  methods: {
    /**
     * 初始化房间
     */
    async initializeRoom() {
      // 加载房间数据
      this.loadRoomData()
      this.loadPlayers()
      this.loadMessages()
    },
    
    /**
     * 初始化战斗WebSocket
     */
    async initializeCombatWebSocket() {
      try {
        // 确保有必要的数据才初始化WebSocket
        if (!this.roomId || !this.currentUser?.id) {
          console.warn('缺少必要数据，跳过WebSocket初始化')
          return
        }
        
        const { combatWebSocketManager } = await import('@/services/combatWebSocket.js')
        
        this.combatWebSocket = combatWebSocketManager.getConnection(
          this.roomId,
          this.currentUser.id,
          {
            url: process.env.VUE_APP_COMBAT_WS_URL || 'ws://localhost:8765',
            token: this.$store.getters['auth/token']
          }
        )
        
        // 监听战斗事件
        this.combatWebSocket.on('combatStateSync', this.handleCombatStateSync)
        this.combatWebSocket.on('actionBroadcast', this.handleActionBroadcast)
        this.combatWebSocket.on('forceCombatMode', this.handleForceCombatMode)
        this.combatWebSocket.on('combatStarted', this.handleCombatStarted)
        this.combatWebSocket.on('combatEnded', this.handleCombatEnded)
        this.combatWebSocket.on('turnChanged', this.handleTurnChanged)
        
        await this.combatWebSocket.connect()
      } catch (error) {
        console.error('初始化战斗WebSocket失败:', error)
      }
    },
    
    /**
     * 清理WebSocket连接
     */
    cleanupWebSocket() {
      if (this.combatWebSocket) {
        this.combatWebSocket.disconnect()
        this.combatWebSocket = null
      }
    },
    
    /**
     * 战斗相关方法
     */
    async startCombat() {
      try {
        if (this.combatWebSocket) {
          await this.combatWebSocket.send('startCombat', {
            roomId: this.roomId,
            participants: this.players.map(p => ({
              id: p.id,
              name: p.characterName || p.username,
              isPlayer: !p.isKP,
              position: { x: Math.floor(Math.random() * 10), y: Math.floor(Math.random() * 10) },
              stats: p.stats || {}
            }))
          })
        }
      } catch (error) {
        console.error('开始战斗失败:', error)
      }
    },
    
    async endCombat() {
      try {
        if (this.combatWebSocket) {
          await this.combatWebSocket.send('endCombat', {
            roomId: this.roomId
          })
        }
      } catch (error) {
        console.error('结束战斗失败:', error)
      }
    },
    
    async nextTurn() {
      try {
        if (this.combatWebSocket && this.isKeeper) {
          await this.combatWebSocket.send('nextTurn', {
            roomId: this.roomId
          })
        }
      } catch (error) {
        console.error('下一回合失败:', error)
      }
    },
    
    /**
     * 战斗事件处理
     */
    handleCombatStateSync(data) {
      this.combatData = data.combatState
      this.combatActive = data.combatState.active
      
      if (this.combatActive) {
        this.activeSceneTab = 'combat'
        this.updateCurrentPlayerCharacter()
      }
    },
    
    handleActionBroadcast(data) {
      this.combatLogs.push({
        id: Date.now(),
        type: 'action',
        content: data.message,
        timestamp: new Date(),
        participant: data.participant
      })
    },
    
    handleForceCombatMode(data) {
      this.shouldShowForcedMode = data.forced && !this.isKeeper
    },
    
    handleCombatStarted(data) {
      this.combatActive = true
      this.combatData = data.combatState
      this.combatLogs = []
      this.updateCurrentPlayerCharacter()
      
      this.combatLogs.push({
        id: Date.now(),
        type: 'system',
        content: '战斗开始！',
        timestamp: new Date()
      })
    },
    
    handleCombatEnded(data) {
      this.combatActive = false
      this.combatData = null
      this.shouldShowForcedMode = false
      this.selectedParticipant = null
      
      this.combatLogs.push({
        id: Date.now(),
        type: 'system',
        content: '战斗结束！',
        timestamp: new Date()
      })
    },
    
    handleTurnChanged(data) {
      if (this.combatData) {
        this.combatData.currentTurn = data.currentTurn
        this.combatData.currentRound = data.currentRound
        
        const currentParticipant = this.getCurrentTurnCharacter()
        if (currentParticipant) {
          this.combatLogs.push({
            id: Date.now(),
            type: 'turn',
            content: `轮到 ${currentParticipant.name} 行动`,
            timestamp: new Date(),
            participant: currentParticipant
          })
        }
      }
    },
    
    /**
     * 战场交互处理
     */
    handleCharacterSelected(character) {
      this.selectedParticipant = character
    },
    
    handleMonsterSelected(monster) {
      this.selectedParticipant = monster
    },
    
    handleCharacterMoved(data) {
      if (this.combatWebSocket) {
        this.combatWebSocket.send('participantMoved', {
          roomId: this.roomId,
          participantId: data.character.id,
          oldPosition: data.oldPosition,
          newPosition: data.newPosition
        })
      }
    },
    
    handleMonsterMoved(data) {
      if (this.combatWebSocket && this.isKeeper) {
        this.combatWebSocket.send('participantMoved', {
          roomId: this.roomId,
          participantId: data.monster.id,
          oldPosition: data.oldPosition,
          newPosition: data.newPosition
        })
      }
    },
    
    handleCharacterAction(data) {
      if (this.combatWebSocket) {
        this.combatWebSocket.send('participantAction', {
          roomId: this.roomId,
          participantId: data.character.id,
          action: data.action
        })
      }
    },
    
    handleMonsterAction(data) {
      if (this.combatWebSocket && this.isKeeper) {
        this.combatWebSocket.send('participantAction', {
          roomId: this.roomId,
          participantId: data.monster.id,
          action: data.action
        })
      }
    },
    
    /**
     * 玩家战斗行动处理
     */
    handlePlayerCombatAction(actionData) {
      if (this.combatWebSocket) {
        this.combatWebSocket.send('playerAction', {
          roomId: this.roomId,
          playerId: this.currentUser.id,
          action: actionData
        })
      }
    },
    
    handleKeeperCombatAction(actionData) {
      if (this.combatWebSocket && this.isKeeper) {
        this.combatWebSocket.send('keeperAction', {
          roomId: this.roomId,
          action: actionData
        })
      }
    },
    
    /**
     * 工具函数
     */
    getCurrentTurnCharacter() {
      if (!this.combatData || !this.combatData.initiativeOrder) return null
      
      const currentIndex = this.combatData.currentTurn || 0
      return this.combatData.initiativeOrder[currentIndex] || null
    },
    
    updateCurrentPlayerCharacter() {
      if (!this.combatData) return
      
      const playerCharacter = this.combatData.participants?.find(p => 
        p.isPlayer && p.playerId === this.currentUser.id
      )
      
      this.currentPlayerCharacter = playerCharacter || null
    },
    
    /**
     * 数据加载方法
     */
    loadRoomData() {
      // 模拟加载房间数据
      this.roomData = {
        name: '神秘的古宅',
        status: 'active',
        creator: { username: 'KP_Master' },
        maxPlayers: 6
      }
    },
    
    loadPlayers() {
      // 模拟加载玩家数据
      this.players = [
        {
          id: 1,
          username: 'KP_Master',
          characterName: 'KP',
          isKP: true,
          status: 'online',
          avatar: '/images/default-avatar.png'
        },
        {
          id: 2,
          username: 'Player1',
          characterName: '侦探约翰',
          isKP: false,
          status: 'online',
          avatar: '/images/default-avatar.png',
          stats: {
            hp: 85,
            maxHp: 100,
            san: 72,
            maxSan: 80
          }
        },
        {
          id: 3,
          username: 'Player2',
          characterName: '记者玛丽',
          isKP: false,
          status: 'online',
          avatar: '/images/default-avatar.png',
          stats: {
            hp: 78,
            maxHp: 90,
            san: 65,
            maxSan: 75
          }
        }
      ]
    },
    
    loadMessages() {
      // 模拟加载消息
      this.messages = [
        {
          id: 1,
          username: 'KP_Master',
          content: '欢迎来到游戏房间！',
          type: 'system',
          timestamp: new Date(),
          avatar: '/images/default-avatar.png'
        }
      ]
      
      // 模拟加载战斗日志
      this.combatLogs = [
        {
          id: 1,
          type: 'system',
          message: '战斗开始！',
          timestamp: new Date(),
          round: 1
        },
        {
          id: 2,
          type: 'attack',
          message: '侦探约翰对邪教徒发起攻击',
          timestamp: new Date(),
          round: 1,
          participant: {
            name: '侦探约翰',
            avatar: '/images/default-avatar.png'
          },
          diceRoll: {
            formula: '1d100',
            rolls: [45],
            total: 45
          },
          successLevel: 'regular'
        },
        {
          id: 3,
          type: 'damage',
          message: '攻击命中，造成8点伤害',
          timestamp: new Date(),
          round: 1,
          severity: 'critical'
        }
      ]
    },
    
    /**
     * 界面控制方法
     */
    toggleLeftPanel() {
      this.leftPanelCollapsed = !this.leftPanelCollapsed
    },
    
    toggleRightPanel() {
      this.rightPanelCollapsed = !this.rightPanelCollapsed
    },
    
    toggleFullscreen() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen()
      } else {
        document.exitFullscreen()
      }
    },
    
    toggleSettings() {
      console.log('打开设置')
    },
    
    leaveRoom() {
      if (confirm('确定要离开房间吗？')) {
        this.$router.push('/')
      }
    },
    
    /**
     * 音频控制
     */
    toggleMute() {
      this.isMuted = !this.isMuted
    },
    
    toggleDeafen() {
      this.isDeafened = !this.isDeafened
    },
    
    /**
     * 工具方法
     */
    openDiceRoller() {
      this.showDiceRoller = true
    },
    
    openCombatActions() {
      // 打开战斗行动菜单
      console.log('打开战斗行动菜单')
    },
    
    quickRoll() {
      const result = Math.floor(Math.random() * 100) + 1
      this.handleDiceRoll({
        total: result,
        results: [result],
        description: '1D100',
        timestamp: Date.now()
      })
    },
    
    /**
     * 聊天和骰子处理
     */
    sendMessage(message) {
      const newMessage = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名用户',
        content: message,
        type: this.chatMode,
        timestamp: new Date(),
        avatar: this.currentUser?.avatar
      }
      
      this.messages.push(newMessage)
    },
    
    handleDiceRoll(rollResult) {
      const message = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名用户',
        content: `投掷 ${rollResult.description}: ${rollResult.total}`,
        type: 'dice',
        timestamp: new Date(),
        avatar: this.currentUser?.avatar,
        rollData: rollResult
      }
      
      if (this.combatActive) {
        this.combatLogs.push(message)
      } else {
        this.messages.push(message)
      }
    },
    
    handleSkillCheck(checkResult) {
      const message = {
        id: Date.now(),
        username: this.currentUser?.username || '匿名用户',
        content: `技能检定: ${checkResult.diceResult}/${checkResult.effectiveSkill} - ${checkResult.level}`,
        type: 'skill-check',
        timestamp: new Date(),
        avatar: this.currentUser?.avatar,
        checkData: checkResult
      }
      
      if (this.combatActive) {
        this.combatLogs.push(message)
      } else {
        this.messages.push(message)
      }
    },
    
    /**
     * 工具函数
     */
    getStatusText() {
      const statusMap = {
        'active': '进行中',
        'waiting': '等待中',
        'paused': '已暂停',
        'ended': '已结束'
      }
      return statusMap[this.roomData.status] || '未知'
    },
    
    getConnectionIcon() {
      const iconMap = {
        'connected': 'fas fa-wifi',
        'connecting': 'fas fa-spinner fa-spin',
        'disconnected': 'fas fa-wifi-slash'
      }
      return iconMap[this.connectionStatus] || 'fas fa-question'
    },
    
    getConnectionText() {
      const textMap = {
        'connected': '已连接',
        'connecting': '连接中',
        'disconnected': '已断开'
      }
      return textMap[this.connectionStatus] || '未知'
    },
    
    /**
     * 状态保存和恢复
     */
    savePanelStates() {
      const states = {
        leftPanelCollapsed: this.leftPanelCollapsed,
        rightPanelCollapsed: this.rightPanelCollapsed,
        activeSceneTab: this.activeSceneTab,
        chatMode: this.chatMode
      }
      this.safeSetJSON(`gameroom_${this.roomId}_panels`, states)
    },
    
    restorePanelStates() {
      const states = this.safeGetJSON(`gameroom_${this.roomId}_panels`)
      if (states) {
        try {
          this.leftPanelCollapsed = states.leftPanelCollapsed || false
          this.rightPanelCollapsed = states.rightPanelCollapsed || false
          this.activeSceneTab = states.activeSceneTab || 'map'
          this.chatMode = states.chatMode || 'ic'
        } catch (error) {
          console.warn('恢复面板状态失败:', error)
        }
      }
    },
    
    /**
     * 缺失的方法实现
     */
    openInventory() {
      console.log('打开背包')
    },
    
    openCharacterSheet() {
      this.selectedCharacterForSheet = this.currentPlayerCharacter
      this.showCharacterSheet = true
    },
    
    /**
     * 线索和笔记管理
     */
    addClue() {
      console.log('添加线索')
    },
    
    updateClue(clue) {
      console.log('更新线索:', clue)
    },
    
    deleteClue(clue) {
      console.log('删除线索:', clue)
    },
    
    addNote() {
      console.log('添加笔记')
    },
    
    editNote(note) {
      console.log('编辑笔记:', note)
    },
    
    shareNote(note) {
      console.log('分享笔记:', note)
    },
    
    deleteNote(note) {
      console.log('删除笔记:', note)
    },
    
    updateCharacter(character) {
      console.log('更新角色:', character)
    },
    
    updateMonster(monster) {
      console.log('更新怪物:', monster)
    },
    
    /**
     * 战场交互处理方法
     */
    handleParticipantSelect(participant) {
      this.selectedParticipant = participant
    },
    
    handleInspectCharacter(character) {
      this.selectedCharacterForSheet = character
      this.showCharacterSheet = true
    },
    
    handleInspectMonster(monster) {
      this.selectedMonsterForSheet = monster
      this.showMonsterSheet = true
    },
    
    handleAddMonster() {
      console.log('添加怪物')
    },
    
    handleCombatStart(data) {
      this.combatActive = true
      this.combatData = data
    },
    
    handleCombatEnd() {
      this.combatActive = false
      this.combatData = null
    },
    
    handleParticipantAdded(participant) {
      console.log('添加参与者:', participant)
    },
    
    handleNextTurn() {
      this.nextTurn()
    },
    
    handleAddParticipant(participant) {
      console.log('添加参与者:', participant)
    },
    
    handleRemoveParticipant(participant) {
      console.log('移除参与者:', participant)
    },
    
    handleLogAction(action) {
      console.log('记录行动:', action)
    },
    
    onCombatModeEntered() {
      console.log('进入战斗模式')
    },
    
    onCombatModeExited() {
      console.log('退出战斗模式')
    },
    
    uploadMap() {
      console.log('上传地图')
    },
    
    /**
     * 工具函数
     */
    getStatPercentage(current, max) {
      return max > 0 ? Math.round((current / max) * 100) : 0
    },
    
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }
}
</script>

<style scoped>
/* ===== 游戏房间基础样式 ===== */
.game-room {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.game-room.combat-mode {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* ===== 房间头部 ===== */
.room-header {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid #15803d;
  position: relative;
  z-index: 10;
}

.game-room.combat-mode .room-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  border-bottom-color: #991b1b;
}

.room-info {
  flex: 1;
}

.room-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.room-title h1 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.room-status {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.combat-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  animation: combat-pulse 2s ease-in-out infinite;
}

@keyframes combat-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4ade80;
  animation: status-pulse 2s ease-in-out infinite;
}

@keyframes status-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.room-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  opacity: 0.9;
}

.room-creator,
.room-players,
.combat-round {
  display: flex;
  align-items: center;
  gap: 6px;
}

.room-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.control-btn.combat-btn {
  background: rgba(220, 38, 38, 0.8);
  border-color: rgba(220, 38, 38, 0.6);
}

.control-btn.combat-btn.active {
  background: rgba(239, 68, 68, 0.9);
  animation: combat-active 1.5s ease-in-out infinite;
}

@keyframes combat-active {
  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0); }
}

.control-btn.leave-btn:hover {
  background: rgba(239, 68, 68, 0.8);
}

/* ===== 游戏布局 ===== */
.game-layout {
  flex: 1;
  display: flex;
  overflow: hidden;
  transition: all 0.3s ease;
}

.game-layout.combat-layout {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

/* ===== 面板样式 ===== */
.left-panel,
.right-panel {
  background: white;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.game-room.combat-mode .left-panel,
.game-room.combat-mode .right-panel {
  background: rgba(31, 41, 55, 0.95);
  border-color: #4b5563;
}

.left-panel {
  width: 300px;
  border-right: 2px solid #e5e7eb;
}

.right-panel {
  width: 350px;
  border-left: 2px solid #e5e7eb;
}

.game-room.combat-mode .left-panel {
  border-right-color: #4b5563;
}

.game-room.combat-mode .right-panel {
  border-left-color: #4b5563;
}

.left-panel.collapsed {
  width: 60px;
}

.right-panel.collapsed {
  width: 60px;
}

.panel-header {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-room.combat-mode .panel-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.panel-toggle {
  width: 28px;
  height: 28px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #f9fafb;
}

.game-room.combat-mode .panel-content {
  background: rgba(17, 24, 39, 0.8);
  color: #f3f4f6;
}

/* ===== 中央面板 ===== */
.center-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  position: relative;
}

.game-room.combat-mode .center-panel {
  background: rgba(17, 24, 39, 0.9);
}

.scene-tabs {
  display: flex;
  background: #f3f4f6;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 16px;
}

.scene-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
  position: relative;
}

.scene-tab:hover {
  color: #374151;
  background: rgba(34, 197, 94, 0.1);
}

.scene-tab.active {
  color: #22c55e;
  border-bottom-color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.tab-badge {
  background: #ef4444;
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.scene-content {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.scene-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 24px;
  overflow-y: auto;
}

.combat-scene {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1f2937;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  text-align: center;
}

.map-placeholder i {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.upload-map-btn {
  margin-top: 16px;
  padding: 12px 24px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.upload-map-btn:hover {
  background: #16a34a;
  transform: translateY(-2px);
}

/* ===== KP战斗控制面板 ===== */
.kp-combat-controls {
  position: absolute;
  bottom: 16px;
  right: 16px;
  z-index: 5;
}

.keeper-combat-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

/* ===== 底部工具栏 ===== */
.bottom-toolbar {
  background: white;
  border-top: 1px solid #e5e7eb;
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.bottom-toolbar.combat-toolbar {
  background: rgba(31, 41, 55, 0.95);
  border-top-color: #4b5563;
  color: #f3f4f6;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  display: flex;
  gap: 12px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.audio-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.audio-btn.muted,
.audio-btn.deafened {
  background: #fef2f2;
  border-color: #fca5a5;
  color: #dc2626;
}

.volume-control {
  width: 80px;
}

.volume-slider {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: #d1d5db;
  outline: none;
  cursor: pointer;
}

.toolbar-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #f9fafb;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.toolbar-btn.primary {
  background: #22c55e;
  border-color: #16a34a;
  color: white;
}

.toolbar-btn.primary:hover {
  background: #16a34a;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-indicator {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.status-indicator.connected {
  background: #dcfce7;
  color: #16a34a;
}

.status-indicator.connecting {
  background: #fef3c7;
  color: #d97706;
}

.status-indicator.disconnected {
  background: #fef2f2;
  color: #dc2626;
}

/* ===== 聊天控制 ===== */
.chat-controls {
  display: flex;
  gap: 4px;
}

.chat-mode-btn {
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.chat-mode-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chat-mode-btn.active {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
  .left-panel {
    width: 250px;
  }
  
  .right-panel {
    width: 300px;
  }
}

@media (max-width: 768px) {
  .game-layout {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .left-panel.collapsed,
  .right-panel.collapsed {
    height: 60px;
  }
  
  .room-title h1 {
    font-size: 18px;
  }
  
  .room-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .toolbar-center {
    flex-direction: column;
    gap: 8px;
  }
}
</style>/
* ===== 角色列表样式 ===== */
.character-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.character-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 12px;
  display: flex;
  gap: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.game-room.combat-mode .character-card {
  background: rgba(55, 65, 81, 0.9);
  border-color: #6b7280;
  color: #f3f4f6;
}

.character-card:hover {
  border-color: #22c55e;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
  transform: translateY(-2px);
}

.character-avatar {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #22c55e;
  flex-shrink: 0;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-status {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
}

.character-status.online {
  background: #22c55e;
}

.character-status.away {
  background: #f59e0b;
}

.character-status.offline {
  background: #ef4444;
}

.character-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.character-name {
  font-weight: bold;
  color: #1f2937;
  font-size: 14px;
}

.game-room.combat-mode .character-name {
  color: #f3f4f6;
}

.character-role {
  font-size: 12px;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 6px;
  background: #f3f4f6;
  align-self: flex-start;
}

.character-role.kp {
  background: #fef3c7;
  color: #d97706;
}

.game-room.combat-mode .character-role {
  background: rgba(75, 85, 99, 0.8);
  color: #d1d5db;
}

.character-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 4px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.stat-label {
  min-width: 30px;
  font-weight: 600;
  color: #6b7280;
}

.game-room.combat-mode .stat-label {
  color: #9ca3af;
}

.stat-bar {
  flex: 1;
  height: 6px;
  background: #f3f4f6;
  border-radius: 3px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.game-room.combat-mode .stat-bar {
  background: rgba(75, 85, 99, 0.5);
  border-color: #6b7280;
}

.stat-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.stat-fill.hp {
  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
}

.stat-fill.san {
  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
}

.stat-value {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.game-room.combat-mode .stat-value {
  color: #9ca3af;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  text-align: center;
}

.game-room.combat-mode .action-btn {
  background: rgba(55, 65, 81, 0.8);
  border-color: #6b7280;
  color: #d1d5db;
}

.action-btn:hover {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-color: #22c55e;
  color: #22c55e;
  transform: translateY(-2px);
}

.game-room.combat-mode .action-btn:hover {
  background: rgba(75, 85, 99, 0.9);
  border-color: #22c55e;
  color: #22c55e;
}

/* ===== 线索和笔记样式 ===== */
.clues-header,
.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.clues-header h3,
.notes-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 18px;
  font-weight: bold;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.add-btn:hover {
  background: #16a34a;
  transform: translateY(-1px);
}

.notes-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.note-filter {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #374151;
  font-size: 14px;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.note-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.note-card:hover {
  border-color: #22c55e;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.note-title {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #1f2937;
}

.note-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.note-content {
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12px;
}

.note-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.note-action-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.note-action-btn:hover {
  border-color: #22c55e;
  color: #22c55e;
}

.note-action-btn.delete:hover {
  border-color: #ef4444;
  color: #ef4444;
}
/* 
===== 场景标签页样式 ===== */
.scene-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e7eb;
  padding: 0 16px;
}

.scene-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: transparent;
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.scene-tab:hover {
  color: #374151;
  background: rgba(0, 0, 0, 0.05);
}

.scene-tab.active {
  color: #22c55e;
  border-bottom-color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.tab-badge {
  background: #ef4444;
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* ===== 面板折叠样式 ===== */
.left-panel.collapsed,
.right-panel.collapsed {
  width: 60px;
  min-width: 60px;
}

.left-panel.collapsed .panel-content,
.right-panel.collapsed .panel-content {
  display: none;
}

.panel-toggle {
  width: 24px;
  height: 24px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.panel-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* ===== 聊天控制样式 ===== */
.chat-controls {
  display: flex;
  gap: 4px;
}

.chat-mode-btn {
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.chat-mode-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.chat-mode-btn.active {
  background: rgba(255, 255, 255, 0.3);
  font-weight: bold;
}

/* ===== 线索系统样式 ===== */
.clues-header,
.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.clues-header h3,
.notes-header h3 {
  margin: 0;
  color: #374151;
  font-size: 18px;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #16a34a;
  transform: translateY(-1px);
}

/* ===== 笔记系统样式 ===== */
.notes-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.note-filter {
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
  font-size: 14px;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.note-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.note-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.note-title {
  margin: 0;
  font-size: 16px;
  color: #374151;
}

.note-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
  font-size: 12px;
  color: #6b7280;
}

.note-content {
  color: #4b5563;
  line-height: 1.5;
  margin-bottom: 12px;
}

.note-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.note-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.note-action-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.note-action-btn.delete:hover {
  background: #fef2f2;
  color: #ef4444;
}

/* ===== 底部工具栏样式 ===== */
.bottom-toolbar {
  height: 60px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.bottom-toolbar.combat-toolbar {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  border-top-color: #4b5563;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.audio-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.audio-btn.muted,
.audio-btn.deafened {
  background: #fef2f2;
  color: #ef4444;
  border-color: #fecaca;
}

.volume-control {
  width: 80px;
}

.volume-slider {
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.toolbar-btn:hover {
  background: #f3f4f6;
  transform: translateY(-1px);
}

.toolbar-btn.primary {
  background: #22c55e;
  color: white;
  border-color: #16a34a;
}

.toolbar-btn.primary:hover {
  background: #16a34a;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator.connected {
  background: #22c55e;
  color: white;
}

.status-indicator.connecting {
  background: #f59e0b;
  color: white;
}

.status-indicator.disconnected {
  background: #ef4444;
  color: white;
}

.status-text {
  font-size: 12px;
  color: #6b7280;
}

/* ===== KP战斗控制覆盖层 ===== */
.keeper-combat-overlay {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== 地图上传按钮样式 ===== */
.upload-map-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.upload-map-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1200px) {
  .left-panel,
  .right-panel {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .game-layout {
    flex-direction: column;
  }
  
  .left-panel,
  .right-panel {
    width: 100%;
    height: 200px;
  }
  
  .center-panel {
    flex: 1;
    min-height: 400px;
  }
  
  .bottom-toolbar {
    padding: 0 12px;
  }
  
  .toolbar-center {
    gap: 4px;
  }
  
  .toolbar-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
  
  .toolbar-btn span {
    display: none;
  }
}

/* ===== 动画效果 ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.note-card,
.character-card {
  animation: fadeIn 0.3s ease-out;
}

/* ===== 滚动条样式 ===== */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}