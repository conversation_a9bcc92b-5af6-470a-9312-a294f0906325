/**
 * COC 7版射击系统测试
 * 验证射击系统的完整功能
 */

import CombatRules from './combatRules.js'
import { diceRoller } from './diceRoller.js'

console.log('=== COC 7版射击系统测试 ===\n')

// 创建测试角色
const marksman = {
  id: 'marksman1',
  name: '神枪手',
  faction: 'heroes',
  
  // 基础属性
  strength: 65,
  dexterity: 80,
  constitution: 70,
  size: 65,
  
  // 技能
  firearms_handgun: 75,
  firearms_rifle: 80,
  firearms_shotgun: 60,
  mechanical_repair: 50,
  
  // 伤害加值
  damageBonus: CombatRules.calculateDamageBonus(65, 65),
  
  // 弹药库存
  inventory: {
    pistol_ammo: 50,
    rifle_ammo: 30,
    shotgun_shells: 20
  }
}

const target = {
  id: 'target1',
  name: '目标',
  faction: 'enemies',
  size: 'normal'
}

// 测试武器
const pistol = {
  name: '.45手枪',
  damage: '1d10+2',
  skill: 'firearms_handgun',
  range: { base: 15, long: 30, extreme: 60 },
  ammo: 7,
  currentAmmo: 7,
  malfunction: 100,
  reloadTime: 1,
  ammoType: 'pistol_ammo'
}

const rifle = {
  name: '步枪',
  damage: '2d6+4',
  skill: 'firearms_rifle',
  range: { base: 90, long: 180, extreme: 360 },
  ammo: 5,
  currentAmmo: 5,
  malfunction: 100,
  reloadTime: 3,
  ammoType: 'rifle_ammo'
}

const shotgun = {
  name: '霰弹枪',
  damage: '4d6',
  skill: 'firearms_shotgun',
  range: { base: 10, long: 20, extreme: 50 },
  ammo: 2,
  currentAmmo: 2,
  malfunction: 100,
  reloadTime: 2,
  ammoType: 'shotgun_shells'
}

const machinegun = {
  name: '机枪',
  damage: '2d6+4',
  skill: 'firearms_machinegun',
  range: { base: 150, long: 300, extreme: 600 },
  ammo: 100,
  currentAmmo: 100,
  malfunction: 96,
  reloadTime: 5,
  ammoType: 'machinegun_ammo',
  fullAuto: true
}

// 测试1: 基础射击 (不同距离)
console.log('=== 测试1: 基础射击 (不同距离) ===')

const distances = [10, 30, 60, 120]
distances.forEach(distance => {
  console.log(`\n距离 ${distance} 码:`)
  
  const result = CombatRules.performShootingAttack(marksman, target, pistol, {
    distance: distance
  })
  
  console.log(`  射击难度: ${result.difficulty}`)
  console.log(`  目标技能值: ${result.targetSkill}`)
  console.log(`  投掷结果: ${result.roll} (${result.successLevel})`)
  console.log(`  ${result.description}`)
  
  if (result.hitResult.hit) {
    console.log(`  💥 伤害: ${result.hitResult.damage.totalDamage} 点`)
  }
})

// 测试2: 瞄准射击
console.log('\n\n=== 测试2: 瞄准射击 ===')

for (let aimingRounds = 1; aimingRounds <= 3; aimingRounds++) {
  console.log(`\n瞄准 ${aimingRounds} 轮:`)
  
  const result = CombatRules.performShootingAttack(marksman, target, rifle, {
    distance: 100,
    aiming: true,
    aimingRounds: aimingRounds,
    targetLocation: 'head'
  })
  
  console.log(`  奖励骰: +${result.bonusDice}`)
  console.log(`  投掷结果: ${result.roll} (${result.successLevel})`)
  console.log(`  ${result.description}`)
  
  if (result.hitResult.hit && result.hitResult.location) {
    console.log(`  🎯 命中部位: ${result.hitResult.location.location}`)
    console.log(`  💥 伤害: ${result.hitResult.damage.totalDamage} 点`)
  }
}

// 测试3: 环境因素影响
console.log('\n\n=== 测试3: 环境因素影响 ===')

const environmentalTests = [
  { name: '正常条件', options: {} },
  { name: '昏暗光线', options: { lighting: 'dim' } },
  { name: '黑暗环境', options: { lighting: 'dark' } },
  { name: '轻掩体', options: { cover: 'light' } },
  { name: '重掩体', options: { cover: 'heavy' } },
  { name: '下雨', options: { weather: 'rain' } },
  { name: '移动射击', options: { moving: true } },
  { name: '目标移动', options: { targetMoving: true } },
  { name: '小目标', options: { targetSize: 'small' } },
  { name: '大目标', options: { targetSize: 'large' } }
]

environmentalTests.forEach(test => {
  const bonusDice = CombatRules.calculateShootingBonusDice(marksman, target, pistol, {
    distance: 20,
    ...test.options
  })
  
  console.log(`${test.name}: ${bonusDice >= 0 ? '+' : ''}${bonusDice} 骰子修正`)
})

// 测试4: 连发射击
console.log('\n\n=== 测试4: 连发射击 ===')

const burstResult = CombatRules.performShootingAttack(marksman, target, machinegun, {
  distance: 50,
  burstFire: true,
  burstSize: 3
})

console.log(`连发射击结果:`)
console.log(`  射击子弹数: ${burstResult.shots}`)
console.log(`  命中子弹数: ${burstResult.hits}`)
console.log(`  总伤害: ${burstResult.totalDamage}`)
console.log(`  ${burstResult.description}`)

console.log('\n详细结果:')
burstResult.results.forEach((result, index) => {
  if (result.hit) {
    console.log(`  第${index + 1}发: 命中，伤害 ${result.damage.totalDamage}`)
  } else {
    console.log(`  第${index + 1}发: 未命中 (投掷 ${result.roll})`)
  }
})

// 测试5: 全自动射击
console.log('\n\n=== 测试5: 全自动射击 ===')

const fullAutoResult = CombatRules.performShootingAttack(marksman, target, machinegun, {
  distance: 30,
  fullAuto: true,
  fullAutoRounds: 10
})

console.log(`全自动射击结果:`)
console.log(`  射击子弹数: ${fullAutoResult.shots}`)
console.log(`  命中子弹数: ${fullAutoResult.hits}`)
console.log(`  总伤害: ${fullAutoResult.totalDamage}`)
console.log(`  ${fullAutoResult.description}`)

// 测试6: 弹幕射击
console.log('\n\n=== 测试6: 弹幕射击 ===')

const barrageTargets = [
  { name: '目标1', size: 'normal', cover: 'none' },
  { name: '目标2', size: 'large', cover: 'light' },
  { name: '目标3', size: 'small', cover: 'heavy' }
]

const barrageResult = CombatRules.performBarrageAttack(marksman, barrageTargets, machinegun, {
  area: 'large',
  rounds: 20
})

console.log(`弹幕射击结果:`)
console.log(`  ${barrageResult.description}`)
console.log(`  使用弹药: ${barrageResult.rounds} 发`)

console.log('\n各目标结果:')
barrageResult.results.forEach(result => {
  if (result.hit) {
    console.log(`  ${result.target}: 命中，伤害 ${result.damage.totalDamage}`)
  } else {
    console.log(`  ${result.target}: 未命中 (投掷 ${result.roll})`)
  }
})

// 测试7: 武器故障
console.log('\n\n=== 测试7: 武器故障 ===')

const faultyWeapon = {
  ...pistol,
  malfunction: 95 // 容易故障的武器
}

// 模拟多次射击直到故障
let shotsFired = 0
let malfunctionOccurred = false

while (!malfunctionOccurred && shotsFired < 10) {
  shotsFired++
  
  const result = CombatRules.performShootingAttack(marksman, target, faultyWeapon, {
    distance: 15
  })
  
  console.log(`第${shotsFired}发: 投掷 ${result.roll}`)
  
  if (result.hitResult.malfunction) {
    console.log(`  ⚠️ 武器故障！`)
    malfunctionOccurred = true
    
    // 尝试修理
    const repairResult = CombatRules.repairWeapon(marksman, faultyWeapon)
    console.log(`  修理尝试: 投掷 ${repairResult.roll} vs 技能 ${repairResult.skill}`)
    console.log(`  ${repairResult.description}`)
  } else {
    console.log(`  ${result.description}`)
  }
}

// 测试8: 装填弹药
console.log('\n\n=== 测试8: 装填弹药 ===')

// 消耗弹药
const testPistol = { ...pistol, currentAmmo: 2 }

console.log(`装填前: ${testPistol.currentAmmo}/${testPistol.ammo}`)
console.log(`库存弹药: ${marksman.inventory.pistol_ammo}`)

const reloadResult = CombatRules.reloadWeapon(marksman, testPistol)

if (reloadResult.success) {
  console.log(`装填成功: ${reloadResult.description}`)
  console.log(`装填后: ${testPistol.currentAmmo}/${testPistol.ammo}`)
  console.log(`剩余库存: ${marksman.inventory.pistol_ammo}`)
} else {
  console.log(`装填失败: ${reloadResult.reason}`)
}

// 测试9: 命中部位
console.log('\n\n=== 测试9: 命中部位 ===')

for (let i = 0; i < 5; i++) {
  const location = CombatRules.determineHitLocation()
  console.log(`随机命中部位 ${i + 1}: ${location.location} (伤害修正 x${location.modifier})`)
}

// 测试指定部位
const headshot = CombatRules.determineHitLocation('head')
console.log(`指定头部射击: ${headshot.location} (伤害修正 x${headshot.modifier})`)

// 测试10: 不同武器类型
console.log('\n\n=== 测试10: 不同武器类型 ===')

const weapons = [
  { weapon: pistol, name: '手枪' },
  { weapon: rifle, name: '步枪' },
  { weapon: shotgun, name: '霰弹枪' }
]

weapons.forEach(({ weapon, name }) => {
  console.log(`\n${name} 射击测试:`)
  
  const result = CombatRules.performShootingAttack(marksman, target, weapon, {
    distance: 25
  })
  
  console.log(`  技能值: ${CombatRules.getFirearmSkill(marksman, weapon)}`)
  console.log(`  射击难度: ${result.difficulty}`)
  console.log(`  ${result.description}`)
  
  if (result.hitResult.hit) {
    console.log(`  💥 伤害: ${result.hitResult.damage.totalDamage} 点`)
  }
})

console.log('\n=== 射击系统测试完成 ===')
console.log('所有射击系统功能已验证，符合COC 7版规则书要求！')