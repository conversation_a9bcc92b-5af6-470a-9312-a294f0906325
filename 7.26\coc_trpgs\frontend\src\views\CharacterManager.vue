<template>
  <div class="character-manager">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="fas fa-user-friends"></i>
            <span>角色管理</span>
          </h1>
          <p class="page-subtitle">管理您的COC角色，创建新的冒险者</p>
        </div>
        
        <div class="header-actions">
          <div class="view-controls">
            <button 
              @click="viewMode = 'grid'" 
              class="view-btn"
              :class="{ 'active': viewMode === 'grid' }"
              title="网格视图"
            >
              <i class="fas fa-th"></i>
            </button>
            <button 
              @click="viewMode = 'list'" 
              class="view-btn"
              :class="{ 'active': viewMode === 'list' }"
              title="列表视图"
            >
              <i class="fas fa-list"></i>
            </button>
          </div>
          
          <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input 
              v-model="searchQuery" 
              type="text" 
              placeholder="搜索角色..." 
              class="search-input"
            />
          </div>
          
          <button @click="createCharacter" class="create-btn primary">
            <i class="fas fa-plus"></i>
            <span>创建角色</span>
          </button>
        </div>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-section" v-if="characters.length > 0">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ characters.length }}</div>
            <div class="stat-label">总角色数</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-heart"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ aliveCharacters }}</div>
            <div class="stat-label">存活角色</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-star"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ favoriteCharacters }}</div>
            <div class="stat-label">收藏角色</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ recentCharacters }}</div>
            <div class="stat-label">最近使用</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 筛选和排序 -->
    <div class="filter-section" v-if="characters.length > 0">
      <div class="filter-controls">
        <div class="filter-group">
          <label class="filter-label">职业筛选:</label>
          <select v-model="selectedOccupation" class="filter-select">
            <option value="">全部职业</option>
            <option v-for="occupation in occupations" :key="occupation" :value="occupation">
              {{ occupation }}
            </option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">状态筛选:</label>
          <select v-model="selectedStatus" class="filter-select">
            <option value="">全部状态</option>
            <option value="alive">存活</option>
            <option value="dead">死亡</option>
            <option value="insane">疯狂</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">排序方式:</label>
          <select v-model="sortBy" class="filter-select">
            <option value="name">按名称</option>
            <option value="created">按创建时间</option>
            <option value="updated">按更新时间</option>
            <option value="level">按等级</option>
          </select>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载角色数据...</p>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="characters.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-user-plus"></i>
      </div>
      <h3 class="empty-title">还没有角色</h3>
      <p class="empty-description">创建您的第一个COC角色，开始您的克苏鲁冒险之旅</p>
      <button @click="createCharacter" class="create-btn primary large">
        <i class="fas fa-plus"></i>
        <span>创建第一个角色</span>
      </button>
    </div>
    
    <!-- 角色列表 -->
    <div v-else class="characters-section">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="character-grid">
        <div 
          v-for="character in filteredCharacters" 
          :key="character.id" 
          class="character-card"
          :class="{ 'favorite': character.is_favorite }"
          @click="selectCharacter(character)"
        >
          <!-- 角色头像和状态 -->
          <div class="character-avatar-section">
            <div class="character-avatar">
              <img :src="getCharacterAvatar(character)" :alt="character.name" />
              <div class="character-status-indicator" :class="character.status"></div>
            </div>
            <button 
              @click.stop="toggleFavorite(character)" 
              class="favorite-btn"
              :class="{ 'active': character.is_favorite }"
            >
              <i class="fas fa-star"></i>
            </button>
          </div>
          
          <!-- 角色基本信息 -->
          <div class="character-basic-info">
            <h3 class="character-name">{{ character.name }}</h3>
            <div class="character-meta">
              <span class="character-occupation">{{ character.occupation || '未知职业' }}</span>
              <span class="character-age">{{ character.age || '?' }}岁</span>
            </div>
          </div>
          
          <!-- 角色属性预览 -->
          <div class="character-attributes" v-if="character.attributes">
            <div class="attribute-row">
              <div class="attribute-item">
                <span class="attr-label">力量</span>
                <span class="attr-value">{{ character.attributes.STR || 0 }}</span>
              </div>
              <div class="attribute-item">
                <span class="attr-label">体质</span>
                <span class="attr-value">{{ character.attributes.CON || 0 }}</span>
              </div>
              <div class="attribute-item">
                <span class="attr-label">敏捷</span>
                <span class="attr-value">{{ character.attributes.DEX || 0 }}</span>
              </div>
            </div>
            <div class="attribute-row">
              <div class="attribute-item">
                <span class="attr-label">智力</span>
                <span class="attr-value">{{ character.attributes.INT || 0 }}</span>
              </div>
              <div class="attribute-item">
                <span class="attr-label">意志</span>
                <span class="attr-value">{{ character.attributes.POW || 0 }}</span>
              </div>
              <div class="attribute-item">
                <span class="attr-label">魅力</span>
                <span class="attr-value">{{ character.attributes.APP || 0 }}</span>
              </div>
            </div>
          </div>
          
          <!-- 生命值和理智值 -->
          <div class="character-vitals">
            <div class="vital-item">
              <div class="vital-label">
                <i class="fas fa-heart"></i>
                <span>HP</span>
              </div>
              <div class="vital-bar">
                <div 
                  class="vital-fill hp" 
                  :style="{ width: `${getHPPercentage(character)}%` }"
                ></div>
                <span class="vital-text">{{ character.current_hp || 0 }}/{{ character.max_hp || 0 }}</span>
              </div>
            </div>
            <div class="vital-item">
              <div class="vital-label">
                <i class="fas fa-brain"></i>
                <span>SAN</span>
              </div>
              <div class="vital-bar">
                <div 
                  class="vital-fill san" 
                  :style="{ width: `${getSANPercentage(character)}%` }"
                ></div>
                <span class="vital-text">{{ character.current_san || 0 }}/{{ character.max_san || 0 }}</span>
              </div>
            </div>
          </div>
          
          <!-- 角色操作按钮 -->
          <div class="character-actions">
            <button @click.stop="viewCharacter(character)" class="action-btn view">
              <i class="fas fa-eye"></i>
              <span>查看</span>
            </button>
            <button @click.stop="editCharacter(character)" class="action-btn edit">
              <i class="fas fa-edit"></i>
              <span>编辑</span>
            </button>
            <button @click.stop="duplicateCharacter(character)" class="action-btn duplicate">
              <i class="fas fa-copy"></i>
              <span>复制</span>
            </button>
            <button @click.stop="deleteCharacter(character)" class="action-btn delete">
              <i class="fas fa-trash"></i>
              <span>删除</span>
            </button>
          </div>
        </div>
      </div>
      
      <!-- 列表视图 -->
      <div v-else class="character-list">
        <div class="list-header">
          <div class="list-col name">角色名称</div>
          <div class="list-col occupation">职业</div>
          <div class="list-col age">年龄</div>
          <div class="list-col vitals">生命/理智</div>
          <div class="list-col status">状态</div>
          <div class="list-col actions">操作</div>
        </div>
        
        <div 
          v-for="character in filteredCharacters" 
          :key="character.id" 
          class="list-item"
          @click="selectCharacter(character)"
        >
          <div class="list-col name">
            <div class="character-name-cell">
              <img :src="getCharacterAvatar(character)" :alt="character.name" class="list-avatar" />
              <div>
                <div class="character-name">{{ character.name }}</div>
                <div class="character-id">ID: {{ character.id }}</div>
              </div>
            </div>
          </div>
          <div class="list-col occupation">{{ character.occupation || '未知' }}</div>
          <div class="list-col age">{{ character.age || '?' }}岁</div>
          <div class="list-col vitals">
            <div class="vitals-cell">
              <div class="vital-mini">
                <span class="vital-label">HP:</span>
                <span class="vital-value">{{ character.current_hp || 0 }}/{{ character.max_hp || 0 }}</span>
              </div>
              <div class="vital-mini">
                <span class="vital-label">SAN:</span>
                <span class="vital-value">{{ character.current_san || 0 }}/{{ character.max_san || 0 }}</span>
              </div>
            </div>
          </div>
          <div class="list-col status">
            <span class="status-badge" :class="character.status">
              {{ getStatusText(character.status) }}
            </span>
          </div>
          <div class="list-col actions">
            <div class="list-actions">
              <button @click.stop="viewCharacter(character)" class="list-action-btn" title="查看">
                <i class="fas fa-eye"></i>
              </button>
              <button @click.stop="editCharacter(character)" class="list-action-btn" title="编辑">
                <i class="fas fa-edit"></i>
              </button>
              <button @click.stop="duplicateCharacter(character)" class="list-action-btn" title="复制">
                <i class="fas fa-copy"></i>
              </button>
              <button @click.stop="deleteCharacter(character)" class="list-action-btn delete" title="删除">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 编辑模态框 -->
    <div v-if="showEditForm" class="character-edit-modal-overlay" @click="closeEditForm">
      <div class="character-edit-modal" @click.stop>
        <div class="character-edit-modal-header">
          <h3 class="modal-title">编辑角色</h3>
          <button @click="closeEditForm" class="modal-close">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="character-edit-modal-body">
          <character-form
            :is-edit="true"
            :character="selectedCharacter"
            @submit="updateCharacter"
            @cancel="closeEditForm"
          />
        </div>
      </div>
    </div>
    
    <div v-if="showCharacterView" class="character-view-modal-overlay" @click="closeCharacterView">
      <div class="character-view-modal" @click.stop>
        <div class="character-view-modal-header">
          <div class="modal-title-section">
            <div class="character-avatar-small">
              <img :src="getCharacterAvatar(selectedCharacter)" :alt="selectedCharacter?.name" />
            </div>
            <div class="character-title-info">
              <h3 class="modal-title">{{ selectedCharacter?.name }}</h3>
              <p class="character-subtitle">{{ selectedCharacter?.occupation }} • ID: {{ selectedCharacter?.id }}</p>
            </div>
          </div>
          <div class="modal-actions">
            <button @click="editCharacterFromView" class="modal-action-btn edit" title="编辑角色">
              <i class="fas fa-edit"></i>
            </button>
            <button @click="duplicateCharacterFromView" class="modal-action-btn duplicate" title="复制角色">
              <i class="fas fa-copy"></i>
            </button>
            <button @click="deleteCharacterFromView" class="modal-action-btn delete" title="删除角色">
              <i class="fas fa-trash"></i>
            </button>
            <button @click="closeCharacterView" class="modal-close">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="character-view-modal-body">
          <character-detail-view :character="selectedCharacter" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import CharacterForm from '@/components/CharacterForm.vue'
import CharacterDetailView from '@/components/CharacterDetailView.vue'
import { storageMixin } from '@/mixins/storageMixin'

export default {
  name: 'CharacterManager',
  mixins: [storageMixin],
  components: {
    CharacterForm,
    CharacterDetailView
  },
  data() {
    return {
      loading: true,
      characters: [],
      showEditForm: false,
      showCharacterView: false,
      selectedCharacter: null,
      viewMode: 'grid', // 'grid' or 'list'
      searchQuery: '',
      selectedOccupation: '',
      selectedStatus: '',
      sortBy: 'name',
      defaultAvatar: '/images/default-character.png'
    }
  },
  computed: {
    ...mapGetters(['userCharacters']),
    
    filteredCharacters() {
      let filtered = [...this.characters]
      
      // 搜索过滤
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(char => 
          char.name.toLowerCase().includes(query) ||
          (char.occupation && char.occupation.toLowerCase().includes(query))
        )
      }
      
      // 职业过滤
      if (this.selectedOccupation) {
        filtered = filtered.filter(char => char.occupation === this.selectedOccupation)
      }
      
      // 状态过滤
      if (this.selectedStatus) {
        filtered = filtered.filter(char => char.status === this.selectedStatus)
      }
      
      // 排序
      filtered.sort((a, b) => {
        switch (this.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name)
          case 'created':
            return new Date(b.created_at) - new Date(a.created_at)
          case 'updated':
            return new Date(b.updated_at) - new Date(a.updated_at)
          case 'level':
            return (b.level || 0) - (a.level || 0)
          default:
            return 0
        }
      })
      
      return filtered
    },
    
    occupations() {
      const occupations = [...new Set(this.characters.map(char => char.occupation).filter(Boolean))]
      return occupations.sort()
    },
    
    aliveCharacters() {
      return this.characters.filter(char => char.status === 'alive' || !char.status).length
    },
    
    favoriteCharacters() {
      return this.characters.filter(char => char.is_favorite).length
    },
    
    recentCharacters() {
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      return this.characters.filter(char => 
        new Date(char.updated_at || char.created_at) > oneWeekAgo
      ).length
    }
  },
  async created() {
    await this.fetchCharacters()
    this.restoreViewSettings()
  },
  beforeUnmount() {
    this.saveViewSettings()
  },
  methods: {
    async fetchCharacters() {
      try {
        this.loading = true
        await this.$store.dispatch('fetchCharacters')
        this.characters = this.userCharacters || []
      } catch (error) {
        console.error('加载角色失败:', error)
        this.$message.error('加载角色失败')
      } finally {
        this.loading = false
      }
    },
    
    createCharacter() {
      this.$router.push({ name: 'CharacterCreator' })
    },
    
    selectCharacter(character) {
      // 可以添加选中角色的逻辑
      console.log('选中角色:', character)
    },
    
    viewCharacter(character) {
      console.log('🔍 查看角色:', character)
      this.selectedCharacter = character
      this.showCharacterView = true
      console.log('🔍 showCharacterView:', this.showCharacterView)
      console.log('🔍 selectedCharacter:', this.selectedCharacter)
    },
    
    editCharacter(character) {
      this.selectedCharacter = { ...character }
      this.showEditForm = true
    },
    
    async updateCharacter(characterData) {
      try {
        // 先更新角色基本信息
        await this.$store.dispatch('updateCharacter', {
          id: this.selectedCharacter.id,
          ...characterData
        })

        // 如果有头像文件，单独上传头像
        if (characterData.avatarFile) {
          await this.uploadCharacterAvatar(this.selectedCharacter.id, characterData.avatarFile)
        }

        this.showEditForm = false
        await this.fetchCharacters()
        this.$message.success('角色更新成功')
      } catch (error) {
        console.error('更新角色失败:', error)
        this.$message.error('更新角色失败')
      }
    },
    
    async duplicateCharacter(character) {
      try {
        // 生成唯一的复制名称
        const newName = this.generateDuplicateName(character.name)

        const duplicatedData = {
          ...character,
          name: newName,
          id: undefined,
          created_at: undefined,
          updated_at: undefined,
          // 清除头像数据，让复制的角色使用默认头像
          avatar_data: null,
          avatar_filename: null,
          avatar_content_type: null
        }

        await this.$store.dispatch('createCharacter', duplicatedData)
        await this.fetchCharacters()
        this.$message.success(`角色复制成功，新角色名称：${newName}`)
      } catch (error) {
        console.error('复制角色失败:', error)
        this.$message.error('复制角色失败: ' + (error.response?.data?.detail || error.message))
      }
    },

    generateDuplicateName(originalName) {
      const existingNames = this.characters.map(c => c.name)
      let newName = `${originalName} (副本)`
      let counter = 1

      // 如果名称已存在，添加数字后缀
      while (existingNames.includes(newName)) {
        counter++
        newName = `${originalName} (副本${counter})`
      }

      return newName
    },
    
    async deleteCharacter(character) {
      // 使用更友好的确认对话框
      const confirmed = await this.showDeleteConfirmDialog(character)
      if (!confirmed) {
        return
      }

      try {
        await this.$store.dispatch('deleteCharacter', character.id)
        await this.fetchCharacters()
        this.$message.success(`角色 "${character.name}" 删除成功`)
      } catch (error) {
        console.error('删除角色失败:', error)
        this.$message.error('删除角色失败: ' + (error.response?.data?.detail || error.message))
      }
    },

    showDeleteConfirmDialog(character) {
      return new Promise((resolve) => {
        const confirmed = confirm(
          `⚠️ 确定要删除角色 "${character.name}" 吗？\n\n` +
          `角色信息：\n` +
          `• 职业：${character.occupation || '未知'}\n` +
          `• 等级：${character.level || '未知'}\n` +
          `• 创建时间：${character.created_at ? new Date(character.created_at).toLocaleDateString() : '未知'}\n\n` +
          `⚠️ 此操作不可撤销，角色的所有数据将被永久删除！`
        )
        resolve(confirmed)
      })
    },
    
    async toggleFavorite(character) {
      try {
        await this.$store.dispatch('updateCharacter', {
          id: character.id,
          is_favorite: !character.is_favorite
        })
        await this.fetchCharacters()
      } catch (error) {
        console.error('更新收藏状态失败:', error)
      }
    },
    
    closeEditForm() {
      this.showEditForm = false
      this.selectedCharacter = null
    },
    
    closeCharacterView() {
      this.showCharacterView = false
      this.selectedCharacter = null
    },

    // 从查看模态框中进行操作
    editCharacterFromView() {
      this.showCharacterView = false
      this.editCharacter(this.selectedCharacter)
    },

    async duplicateCharacterFromView() {
      this.showCharacterView = false
      await this.duplicateCharacter(this.selectedCharacter)
    },

    async deleteCharacterFromView() {
      const character = this.selectedCharacter
      this.showCharacterView = false
      await this.deleteCharacter(character)
    },

    // 头像上传方法
    async uploadCharacterAvatar(characterId, avatarFile) {
      try {
        const formData = new FormData()
        formData.append('file', avatarFile)

        const response = await this.$http.post(`/api/characters/${characterId}/avatar`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.data.success) {
          console.log('✅ 头像上传成功')
          return true
        } else {
          console.error('❌ 头像上传失败:', response.data.message)
          throw new Error(response.data.message || '头像上传失败')
        }
      } catch (error) {
        console.error('❌ 头像上传错误:', error)
        throw error
      }
    },

    // 获取角色头像URL
    getCharacterAvatar(character) {
      if (character && character.id) {
        // 尝试从API获取头像，如果失败则使用默认头像
        return `http://localhost:8000/api/characters/${character.id}/avatar`
      }
      return this.defaultAvatar
    },

    // 获取属性中文名称
    getAttributeName(key) {
      const attributeNames = {
        'STR': '力量',
        'CON': '体质',
        'SIZ': '体型',
        'DEX': '敏捷',
        'APP': '外貌',
        'INT': '智力',
        'POW': '意志',
        'EDU': '教育'
      }
      return attributeNames[key] || key
    },
    
    getHPPercentage(character) {
      if (!character.max_hp || character.max_hp === 0) return 0
      return Math.max(0, Math.min(100, (character.current_hp || 0) / character.max_hp * 100))
    },
    
    getSANPercentage(character) {
      if (!character.max_san || character.max_san === 0) return 0
      return Math.max(0, Math.min(100, (character.current_san || 0) / character.max_san * 100))
    },
    
    getStatusText(status) {
      const statusMap = {
        'alive': '存活',
        'dead': '死亡',
        'insane': '疯狂',
        'unconscious': '昏迷'
      }
      return statusMap[status] || '正常'
    },
    
    saveViewSettings() {
      const settings = {
        viewMode: this.viewMode,
        sortBy: this.sortBy
      }
      this.safeSetJSON('character_manager_settings', settings)
    },
    
    restoreViewSettings() {
      const settings = this.safeGetJSON('character_manager_settings')
      if (settings) {
        try {
          this.viewMode = settings.viewMode || 'grid'
          this.sortBy = settings.sortBy || 'name'
        } catch (error) {
          console.warn('恢复视图设置失败:', error)
        }
      }
    }
  }
}
</script>

<style scoped>
.character-manager {
  min-height: 100vh;
  background: var(--bg-primary);
  padding: var(--spacing-4);
}

/* ===== 页面头部 ===== */
.page-header {
  margin-bottom: var(--spacing-6);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.header-left {
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin: 0 0 var(--spacing-2);
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.page-subtitle {
  margin: 0;
  color: var(--text-muted);
  font-size: var(--font-size-base);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.view-controls {
  display: flex;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.view-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: none;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.view-btn:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.view-btn.active {
  background: var(--primary-600);
  color: var(--text-inverse);
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--spacing-3);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.search-input {
  padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-8);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  width: 250px;
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.create-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-md);
  background: var(--primary-600);
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.create-btn:hover {
  background: var(--primary-700);
  transform: translateY(-1px);
}

.create-btn.large {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
}

/* ===== 统计信息 ===== */
.stats-section {
  margin-bottom: var(--spacing-6);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: var(--font-size-lg);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* ===== 筛选区域 ===== */
.filter-section {
  margin-bottom: var(--spacing-6);
}

.filter-controls {
  display: flex;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  white-space: nowrap;
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  min-width: 120px;
}

/* ===== 加载和空状态 ===== */
.loading-state, .empty-state {
  text-align: center;
  padding: var(--spacing-8);
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--primary-200);
  border-top: 3px solid var(--primary-600);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: var(--font-size-4xl);
  color: var(--text-muted);
  margin-bottom: var(--spacing-4);
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-2);
}

.empty-description {
  color: var(--text-muted);
  margin-bottom: var(--spacing-6);
}

/* ===== 角色网格 ===== */
.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-4);
}

.character-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.character-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-300);
}

.character-card.favorite {
  border-color: var(--warning-400);
  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--warning-50) 100%);
}

.character-avatar-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
}

.character-avatar {
  position: relative;
  width: 64px;
  height: 64px;
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 3px solid var(--primary-200);
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-elevated);
  background: var(--success-500);
}

.character-status-indicator.dead {
  background: var(--error-500);
}

.character-status-indicator.insane {
  background: var(--warning-500);
}

.favorite-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--bg-secondary);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-btn:hover {
  background: var(--warning-100);
  color: var(--warning-600);
}

.favorite-btn.active {
  background: var(--warning-500);
  color: var(--text-inverse);
}

.character-basic-info {
  margin-bottom: var(--spacing-3);
}

.character-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-1);
}

.character-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.character-occupation {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.character-age {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.character-attributes {
  margin-bottom: var(--spacing-3);
}

.attribute-row {
  display: flex;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.attribute-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-2);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
}

.attr-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-bottom: var(--spacing-1);
}

.attr-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.character-vitals {
  margin-bottom: var(--spacing-4);
}

.vital-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.vital-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  min-width: 50px;
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.vital-bar {
  flex: 1;
  height: 20px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vital-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  transition: width var(--transition-fast);
}

.vital-fill.hp {
  background: var(--error-500);
}

.vital-fill.san {
  background: var(--primary-500);
}

.vital-text {
  position: relative;
  z-index: 1;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.character-actions {
  display: flex;
  gap: var(--spacing-2);
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-xs);
}

.action-btn:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.action-btn.view:hover {
  background: var(--primary-50);
  color: var(--primary-600);
  border-color: var(--primary-300);
}

.action-btn.edit:hover {
  background: var(--warning-50);
  color: var(--warning-600);
  border-color: var(--warning-300);
}

.action-btn.duplicate:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
}

.action-btn.delete:hover {
  background: var(--error-50);
  color: var(--error-600);
  border-color: var(--error-300);
}

/* ===== 列表视图 ===== */
.character-list {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.list-header {
  display: flex;
  background: var(--bg-tertiary);
  border-bottom: 1px solid var(--border-primary);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.list-item {
  display: flex;
  border-bottom: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.list-item:hover {
  background: var(--hover-bg);
}

.list-col {
  padding: var(--spacing-3);
  display: flex;
  align-items: center;
}

.list-col.name {
  flex: 2;
}

.list-col.occupation {
  flex: 1;
}

.list-col.age {
  flex: 0.8;
}

.list-col.vitals {
  flex: 1.2;
}

.list-col.status {
  flex: 0.8;
}

.list-col.actions {
  flex: 1;
}

.character-name-cell {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.list-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 2px solid var(--primary-200);
}

.character-id {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.vitals-cell {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.vital-mini {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
}

.vital-label {
  color: var(--text-muted);
}

.vital-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.status-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.status-badge.alive {
  background: var(--success-100);
  color: var(--success-700);
}

.status-badge.dead {
  background: var(--error-100);
  color: var(--error-700);
}

.status-badge.insane {
  background: var(--warning-100);
  color: var(--warning-700);
}

.list-actions {
  display: flex;
  gap: var(--spacing-1);
}

.list-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-action-btn:hover {
  background: var(--primary-100);
  color: var(--primary-600);
}

.list-action-btn.delete:hover {
  background: var(--error-100);
  color: var(--error-600);
}

/* ===== 模态框 ===== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  animation: modal-appear 0.2s ease-out;
  width: 600px;
}

.modal.large {
  width: 95vw;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.modal-title-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.character-avatar-small {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  overflow: hidden;
  border: 2px solid var(--primary-200);
}

.character-avatar-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-title-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.character-subtitle {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.modal-action-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-action-btn:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.modal-action-btn.edit:hover {
  background: var(--warning-50);
  color: var(--warning-600);
  border-color: var(--warning-300);
}

.modal-action-btn.duplicate:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
}

.modal-action-btn.delete:hover {
  background: var(--error-50);
  color: var(--error-600);
  border-color: var(--error-300);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: none;
  color: var(--text-muted);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  background: var(--hover-bg);
  color: var(--text-secondary);
}

.modal-body {
  padding: var(--spacing-6);
  overflow-y: auto;
  max-height: 70vh;
}

.modal-body.character-detail-body {
  padding: 0;
  max-height: none;
  overflow: visible;
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
  .character-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
  
  .filter-controls {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .character-manager {
    padding: var(--spacing-3);
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .search-input {
    width: 100%;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .character-grid {
    grid-template-columns: 1fr;
  }
  
  .character-list {
    overflow-x: auto;
  }
  
  .list-header, .list-item {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .modal {
    width: 95vw;
    margin: var(--spacing-2);
  }
  
  .modal-header, .modal-body {
    padding: var(--spacing-3) var(--spacing-4);
  }
}

/* ===== 角色查看模态框专用样式 ===== */
.character-view-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.character-view-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 95vw;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  animation: modal-appear 0.2s ease-out;
  display: flex;
  flex-direction: column;
}

.character-view-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.character-view-modal-body {
  flex: 1;
  overflow-y: auto;
  background: white;
}

@media (max-width: 768px) {
  .character-view-modal {
    width: 95vw;
    margin: 8px;
  }

  .character-view-modal-header {
    padding: 12px 16px;
  }
}

/* ===== 角色编辑模态框专用样式 ===== */
.character-edit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.character-edit-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: modal-appear 0.2s ease-out;
  display: flex;
  flex-direction: column;
}

.character-edit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.character-edit-modal-body {
  flex: 1;
  overflow-y: auto;
  background: white;
}

@media (max-width: 768px) {
  .character-edit-modal {
    width: 95vw;
    margin: 8px;
  }

  .character-edit-modal-header {
    padding: 12px 16px;
  }
}
</style>