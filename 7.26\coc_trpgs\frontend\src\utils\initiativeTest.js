/**
 * COC 7版先攻和回合系统测试
 * 验证先攻计算和回合管理功能
 */

import { combatSystem } from './combatSystem.js'
import CombatRules from './combatRules.js'

console.log('=== COC 7版先攻和回合系统测试 ===\n')

// 创建测试角色
const characters = [
  {
    id: 'gunslinger',
    name: '枪手',
    faction: 'heroes',
    dexterity: 80,
    constitution: 70,
    hitPoints: 14,
    currentWeapon: {
      name: '左轮手枪',
      type: 'firearm',
      damage: '1d10+2'
    },
    damageBonus: CombatRules.calculateDamageBonus(70, 65),
    conditions: []
  },
  {
    id: 'swordsman',
    name: '剑客',
    faction: 'heroes',
    dexterity: 85,
    constitution: 75,
    hitPoints: 15,
    currentWeapon: {
      name: '长剑',
      type: 'melee',
      damage: '1d8+1'
    },
    damageBonus: CombatRules.calculateDamageBonus(75, 70),
    conditions: []
  },
  {
    id: 'thief',
    name: '盗贼',
    faction: 'villains',
    dexterity: 90,
    constitution: 60,
    hitPoints: 12,
    currentWeapon: {
      name: '匕首',
      type: 'melee',
      damage: '1d4+2'
    },
    damageBonus: CombatRules.calculateDamageBonus(60, 60),
    conditions: []
  },
  {
    id: 'archer',
    name: '弓箭手',
    faction: 'villains',
    dexterity: 75,
    constitution: 65,
    hitPoints: 13,
    currentWeapon: {
      name: '弓',
      type: 'ranged',
      damage: '1d6'
    },
    damageBonus: CombatRules.calculateDamageBonus(65, 65),
    conditions: []
  }
]

// 测试1: 基础先攻计算
console.log('=== 测试1: 基础先攻计算 ===')

characters.forEach(char => {
  const initiative = combatSystem.calculateInitiative(char)
  console.log(`${char.name}:`)
  console.log(`  敏捷值: ${char.dexterity}`)
  console.log(`  武器类型: ${char.currentWeapon.type}`)
  console.log(`  先攻值: ${initiative}`)
  
  if (char.currentWeapon.type === 'firearm') {
    console.log(`  (包含射击武器+50加值)`)
  }
  console.log()
})

// 测试2: 开始战斗和先攻排序
console.log('=== 测试2: 开始战斗和先攻排序 ===')

const combat = combatSystem.startCombat(characters, {
  location: '废弃工厂',
  lighting: 'dim'
})

console.log(`战斗开始: ${combat.id}`)
console.log(`环境: ${combat.environment.location} (${combat.environment.lighting})`)
console.log()

console.log('先攻顺序:')
combatSystem.participants.forEach((participant, index) => {
  console.log(`${index + 1}. ${participant.name} (先攻: ${participant.initiative})`)
})
console.log()

// 测试3: 回合管理
console.log('=== 测试3: 回合管理 ===')

for (let round = 1; round <= 3; round++) {
  console.log(`--- 第${round}轮 ---`)
  
  // 每个参与者的回合
  for (let turn = 0; turn < combatSystem.participants.length; turn++) {
    const currentParticipant = combatSystem.getCurrentParticipant()
    
    if (!currentParticipant || currentParticipant.status !== 'active') {
      combatSystem.nextTurn()
      continue
    }
    
    console.log(`\n${currentParticipant.name} 的回合:`)
    
    // 模拟不同的行动
    const actionType = Math.random()
    
    if (actionType < 0.3) {
      // 延迟行动
      console.log(`  选择延迟行动`)
      combatSystem.delayAction(currentParticipant.id)
    } else if (actionType < 0.5) {
      // 全防御
      console.log(`  选择全防御 (+20防御加值)`)
      combatSystem.fullDefense(currentParticipant.id)
      currentParticipant.hasActed = true
    } else if (actionType < 0.7) {
      // 准备行动
      console.log(`  准备行动 (等待触发条件)`)
      combatSystem.readyAction(currentParticipant.id)
      currentParticipant.hasActed = true
    } else {
      // 正常攻击
      console.log(`  进行攻击`)
      currentParticipant.hasActed = true
    }
    
    combatSystem.nextTurn()
  }
  
  // 检查回合结束
  if (combatSystem.checkRoundEnd()) {
    console.log(`\n第${round}轮结束`)
    if (round < 3) {
      combatSystem.startNewRound()
    }
  }
}

// 测试4: 状态效果对先攻的影响
console.log('\n\n=== 测试4: 状态效果对先攻的影响 ===')

const injuredCharacter = {
  id: 'injured',
  name: '受伤角色',
  dexterity: 70,
  conditions: ['injured'],
  currentWeapon: { type: 'melee' }
}

const majorWoundedCharacter = {
  id: 'wounded',
  name: '重伤角色',
  dexterity: 70,
  conditions: ['major_wound'],
  currentWeapon: { type: 'melee' }
}

const stunnedCharacter = {
  id: 'stunned',
  name: '眩晕角色',
  dexterity: 70,
  conditions: ['stunned'],
  currentWeapon: { type: 'melee' }
}

const testCharacters = [injuredCharacter, majorWoundedCharacter, stunnedCharacter]

testCharacters.forEach(char => {
  const initiative = combatSystem.calculateInitiative(char)
  console.log(`${char.name}:`)
  console.log(`  基础敏捷: ${char.dexterity}`)
  console.log(`  状态效果: ${char.conditions.join(', ')}`)
  console.log(`  最终先攻: ${initiative}`)
  console.log()
})

// 测试5: 持续效果处理
console.log('=== 测试5: 持续效果处理 ===')

const affectedCharacter = {
  id: 'affected',
  name: '中毒流血角色',
  dexterity: 70,
  constitution: 60,
  currentHP: 10,
  conditions: ['bleeding', 'poisoned'],
  currentWeapon: { type: 'melee' }
}

console.log(`${affectedCharacter.name} 回合开始前:`)
console.log(`  生命值: ${affectedCharacter.currentHP}`)
console.log(`  状态: ${affectedCharacter.conditions.join(', ')}`)

// 模拟处理持续效果
combatSystem.processContinuousEffects(affectedCharacter)

console.log(`\n处理持续效果后:`)
console.log(`  生命值: ${affectedCharacter.currentHP}`)
console.log(`  状态: ${affectedCharacter.conditions.join(', ')}`)

// 测试6: 准备行动触发
console.log('\n\n=== 测试6: 准备行动触发 ===')

const readyCharacter = {
  id: 'ready',
  name: '准备角色',
  isReady: false
}

console.log(`${readyCharacter.name} 准备行动...`)
combatSystem.readyAction(readyCharacter.id)
console.log(`  准备状态: ${readyCharacter.isReady}`)

// 触发准备的行动
const triggerAction = { type: 'attack', target: 'enemy' }
combatSystem.triggerReadyAction(readyCharacter.id, triggerAction)
console.log(`触发准备行动: ${triggerAction.type}`)
console.log(`  准备状态: ${readyCharacter.isReady}`)
console.log(`  行动内容: ${JSON.stringify(readyCharacter.readyAction)}`)

// 测试7: 战斗日志
console.log('\n\n=== 测试7: 战斗日志 ===')

const status = combatSystem.getCombatStatus()
console.log(`战斗日志条目数: ${status.log.length}`)
console.log('\n最近的日志条目:')

status.log.slice(-10).forEach((entry, index) => {
  const time = new Date(entry.timestamp).toLocaleTimeString()
  console.log(`${index + 1}. [${time}] ${entry.type}: ${JSON.stringify(entry.data)}`)
})

// 测试8: 回合状态重置
console.log('\n\n=== 测试8: 回合状态重置 ===')

// 设置一些回合状态
const testParticipant = combatSystem.participants[0]
testParticipant.hasActed = true
testParticipant.isFullDefense = true
testParticipant.defenseBonus = 20
testParticipant.hasDelayed = true

console.log(`${testParticipant.name} 回合结束状态:`)
console.log(`  已行动: ${testParticipant.hasActed}`)
console.log(`  全防御: ${testParticipant.isFullDefense}`)
console.log(`  防御加值: ${testParticipant.defenseBonus}`)
console.log(`  已延迟: ${testParticipant.hasDelayed}`)

// 开始新回合
combatSystem.startNewRound()

console.log(`\n新回合开始后:`)
console.log(`  已行动: ${testParticipant.hasActed}`)
console.log(`  全防御: ${testParticipant.isFullDefense}`)
console.log(`  防御加值: ${testParticipant.defenseBonus}`)
console.log(`  已延迟: ${testParticipant.hasDelayed}`)

console.log('\n=== 先攻和回合系统测试完成 ===')
console.log('所有先攻和回合管理功能已验证，符合COC 7版规则书要求！')