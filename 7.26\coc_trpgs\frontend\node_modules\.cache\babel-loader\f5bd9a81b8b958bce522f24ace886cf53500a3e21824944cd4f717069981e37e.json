{"ast": null, "code": "import _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, resolveComponent as _resolveComponent, createVNode as _createVNode, vShow as _vShow, withDirectives as _withDirectives, vModelSelect as _vModelSelect, vModelText as _vModelText, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"room-header\"\n};\nvar _hoisted_2 = {\n  \"class\": \"room-info\"\n};\nvar _hoisted_3 = {\n  \"class\": \"room-title\"\n};\nvar _hoisted_4 = {\n  key: 0,\n  \"class\": \"combat-indicator\"\n};\nvar _hoisted_5 = {\n  \"class\": \"room-meta\"\n};\nvar _hoisted_6 = {\n  \"class\": \"room-creator\"\n};\nvar _hoisted_7 = {\n  \"class\": \"room-players\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  \"class\": \"combat-round\"\n};\nvar _hoisted_9 = {\n  \"class\": \"room-controls\"\n};\nvar _hoisted_10 = {\n  \"class\": \"panel-header\"\n};\nvar _hoisted_11 = {\n  \"class\": \"panel-title\"\n};\nvar _hoisted_12 = {\n  \"class\": \"panel-content\"\n};\nvar _hoisted_13 = {\n  key: 0,\n  \"class\": \"character-section\"\n};\nvar _hoisted_14 = {\n  \"class\": \"character-list\"\n};\nvar _hoisted_15 = {\n  \"class\": \"character-avatar\"\n};\nvar _hoisted_16 = [\"src\", \"alt\"];\nvar _hoisted_17 = {\n  \"class\": \"character-info\"\n};\nvar _hoisted_18 = {\n  \"class\": \"character-name\"\n};\nvar _hoisted_19 = {\n  key: 0,\n  \"class\": \"character-stats\"\n};\nvar _hoisted_20 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_21 = {\n  \"class\": \"stat-bar\"\n};\nvar _hoisted_22 = {\n  \"class\": \"stat-value\"\n};\nvar _hoisted_23 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_24 = {\n  \"class\": \"stat-bar\"\n};\nvar _hoisted_25 = {\n  \"class\": \"stat-value\"\n};\nvar _hoisted_26 = {\n  \"class\": \"quick-actions\"\n};\nvar _hoisted_27 = {\n  key: 1,\n  \"class\": \"combat-section\"\n};\nvar _hoisted_28 = {\n  \"class\": \"center-panel\"\n};\nvar _hoisted_29 = {\n  key: 0,\n  \"class\": \"scene-tabs\"\n};\nvar _hoisted_30 = [\"onClick\"];\nvar _hoisted_31 = {\n  key: 0,\n  \"class\": \"tab-badge\"\n};\nvar _hoisted_32 = {\n  \"class\": \"scene-content\"\n};\nvar _hoisted_33 = {\n  key: 0,\n  \"class\": \"scene-panel map-scene\"\n};\nvar _hoisted_34 = {\n  \"class\": \"map-container\"\n};\nvar _hoisted_35 = {\n  \"class\": \"map-placeholder\"\n};\nvar _hoisted_36 = {\n  key: 1,\n  \"class\": \"combat-scene\"\n};\nvar _hoisted_37 = {\n  key: 2,\n  \"class\": \"scene-panel clues-scene\"\n};\nvar _hoisted_38 = {\n  \"class\": \"clues-header\"\n};\nvar _hoisted_39 = {\n  key: 3,\n  \"class\": \"scene-panel notes-scene\"\n};\nvar _hoisted_40 = {\n  \"class\": \"notes-header\"\n};\nvar _hoisted_41 = {\n  \"class\": \"notes-controls\"\n};\nvar _hoisted_42 = {\n  \"class\": \"notes-list\"\n};\nvar _hoisted_43 = {\n  \"class\": \"note-header\"\n};\nvar _hoisted_44 = {\n  \"class\": \"note-title\"\n};\nvar _hoisted_45 = {\n  \"class\": \"note-meta\"\n};\nvar _hoisted_46 = {\n  \"class\": \"note-author\"\n};\nvar _hoisted_47 = {\n  \"class\": \"note-time\"\n};\nvar _hoisted_48 = {\n  \"class\": \"note-content\"\n};\nvar _hoisted_49 = {\n  \"class\": \"note-actions\"\n};\nvar _hoisted_50 = [\"onClick\"];\nvar _hoisted_51 = [\"onClick\"];\nvar _hoisted_52 = [\"onClick\"];\nvar _hoisted_53 = {\n  key: 1,\n  \"class\": \"kp-combat-controls\"\n};\nvar _hoisted_54 = {\n  \"class\": \"panel-header\"\n};\nvar _hoisted_55 = {\n  \"class\": \"panel-title\"\n};\nvar _hoisted_56 = {\n  key: 0,\n  \"class\": \"chat-controls\"\n};\nvar _hoisted_57 = {\n  \"class\": \"panel-content\"\n};\nvar _hoisted_58 = {\n  key: 0,\n  \"class\": \"chat-section\"\n};\nvar _hoisted_59 = {\n  key: 1,\n  \"class\": \"combat-log-section\"\n};\nvar _hoisted_60 = {\n  \"class\": \"toolbar-left\"\n};\nvar _hoisted_61 = {\n  \"class\": \"audio-controls\"\n};\nvar _hoisted_62 = {\n  \"class\": \"volume-control\"\n};\nvar _hoisted_63 = {\n  \"class\": \"toolbar-center\"\n};\nvar _hoisted_64 = [\"disabled\"];\nvar _hoisted_65 = {\n  \"class\": \"toolbar-right\"\n};\nvar _hoisted_66 = {\n  \"class\": \"connection-status\"\n};\nvar _hoisted_67 = {\n  \"class\": \"status-text\"\n};\nvar _hoisted_68 = {\n  key: 2,\n  \"class\": \"keeper-combat-overlay\"\n};\nexport function render(_ctx, _cache) {\n  var _ctx$roomData$creator, _ctx$combatData, _ctx$combatData2, _ctx$combatData3, _ctx$combatData4, _ctx$combatData5, _ctx$combatData6, _ctx$combatData7, _ctx$combatData8, _ctx$combatData9, _ctx$combatData0, _ctx$combatData1, _ctx$combatData10;\n  var _component_InitiativeTracker = _resolveComponent(\"InitiativeTracker\");\n  var _component_BattlefieldGrid = _resolveComponent(\"BattlefieldGrid\");\n  var _component_ClueBoard = _resolveComponent(\"ClueBoard\");\n  var _component_KeeperCombatPanel = _resolveComponent(\"KeeperCombatPanel\");\n  var _component_ChatBox = _resolveComponent(\"ChatBox\");\n  var _component_CombatLog = _resolveComponent(\"CombatLog\");\n  var _component_DiceRoller = _resolveComponent(\"DiceRoller\");\n  var _component_ForcedCombatMode = _resolveComponent(\"ForcedCombatMode\");\n  var _component_FloatingCharacterSheet = _resolveComponent(\"FloatingCharacterSheet\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    \"class\": _normalizeClass([\"game-room\", {\n      'combat-mode': _ctx.combatActive\n    }])\n  }, [_createCommentVNode(\" 房间头部信息 \"), _createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[29] || (_cache[29] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-door-open\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"h1\", null, _toDisplayString(_ctx.roomData.name || '游戏房间'), 1 /* TEXT */), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"room-status\", _ctx.roomData.status])\n  }, [_cache[27] || (_cache[27] = _createElementVNode(\"span\", {\n    \"class\": \"status-dot\"\n  }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(_ctx.getStatusText()), 1 /* TEXT */)], 2 /* CLASS */), _createCommentVNode(\" 战斗状态指示器 \"), _ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"战斗进行中\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", _hoisted_6, [_cache[30] || (_cache[30] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-crown\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" KP: \" + _toDisplayString(((_ctx$roomData$creator = _ctx.roomData.creator) === null || _ctx$roomData$creator === void 0 ? void 0 : _ctx$roomData$creator.username) || '未知'), 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_7, [_cache[31] || (_cache[31] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-users\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" 玩家: \" + _toDisplayString(_ctx.currentPlayers) + \"/\" + _toDisplayString(_ctx.roomData.maxPlayers || 6), 1 /* TEXT */)]), _ctx.combatActive ? (_openBlock(), _createElementBlock(\"span\", _hoisted_8, [_cache[32] || (_cache[32] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-clock\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" 第\" + _toDisplayString(((_ctx$combatData = _ctx.combatData) === null || _ctx$combatData === void 0 ? void 0 : _ctx$combatData.currentRound) || 1) + \"轮 \", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_9, [_createCommentVNode(\" KP战斗控制按钮 \"), _ctx.isKeeper && !_ctx.combatActive ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[0] || (_cache[0] = function () {\n      return _ctx.startCombat && _ctx.startCombat.apply(_ctx, arguments);\n    }),\n    \"class\": \"control-btn combat-btn\",\n    title: \"开始战斗\"\n  }, _cache[33] || (_cache[33] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sword\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), _ctx.isKeeper && _ctx.combatActive ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    onClick: _cache[1] || (_cache[1] = function () {\n      return _ctx.endCombat && _ctx.endCombat.apply(_ctx, arguments);\n    }),\n    \"class\": \"control-btn combat-btn active\",\n    title: \"结束战斗\"\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-stop\"\n  }, null, -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = function () {\n      return _ctx.toggleFullscreen && _ctx.toggleFullscreen.apply(_ctx, arguments);\n    }),\n    \"class\": \"control-btn\",\n    title: \"全屏模式\"\n  }, _cache[35] || (_cache[35] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-expand\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = function () {\n      return _ctx.toggleSettings && _ctx.toggleSettings.apply(_ctx, arguments);\n    }),\n    \"class\": \"control-btn\",\n    title: \"房间设置\"\n  }, _cache[36] || (_cache[36] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-cog\"\n  }, null, -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function () {\n      return _ctx.leaveRoom && _ctx.leaveRoom.apply(_ctx, arguments);\n    }),\n    \"class\": \"control-btn leave-btn\",\n    title: \"离开房间\"\n  }, _cache[37] || (_cache[37] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sign-out-alt\"\n  }, null, -1 /* CACHED */)]))])]), _createCommentVNode(\" 游戏布局 \"), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"game-layout\", {\n      'combat-layout': _ctx.combatActive\n    }])\n  }, [_createCommentVNode(\" 左侧面板：角色信息 + 先攻追踪器 \"), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"left-panel\", {\n      'collapsed': _ctx.leftPanelCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.combatActive ? 'fas fa-users-cog' : 'fas fa-users')\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(_ctx.combatActive ? '战斗状态' : '角色信息'), 1 /* TEXT */)]), _createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = function () {\n      return _ctx.toggleLeftPanel && _ctx.toggleLeftPanel.apply(_ctx, arguments);\n    }),\n    \"class\": \"panel-toggle\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.leftPanelCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left')\n  }, null, 2 /* CLASS */)])]), _withDirectives(_createElementVNode(\"div\", _hoisted_12, [_createCommentVNode(\" 非战斗时：角色列表 \"), !_ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.players, function (player) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: player.id,\n      \"class\": \"character-card\"\n    }, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"img\", {\n      src: player.avatar || '/images/default-avatar.png',\n      alt: player.username\n    }, null, 8 /* PROPS */, _hoisted_16), _createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"character-status\", player.status])\n    }, null, 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString(player.characterName || player.username), 1 /* TEXT */), _createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"character-role\", {\n        'kp': player.isKP\n      }])\n    }, _toDisplayString(player.isKP ? 'KP' : 'PL'), 3 /* TEXT, CLASS */), player.stats ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[38] || (_cache[38] = _createElementVNode(\"span\", {\n      \"class\": \"stat-label\"\n    }, \"HP:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", {\n      \"class\": \"stat-fill hp\",\n      style: _normalizeStyle({\n        width: _ctx.getStatPercentage(player.stats.hp, player.stats.maxHp) + '%'\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", _hoisted_22, _toDisplayString(player.stats.hp) + \"/\" + _toDisplayString(player.stats.maxHp), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_23, [_cache[39] || (_cache[39] = _createElementVNode(\"span\", {\n      \"class\": \"stat-label\"\n    }, \"SAN:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", {\n      \"class\": \"stat-fill san\",\n      style: _normalizeStyle({\n        width: _ctx.getStatPercentage(player.stats.san, player.stats.maxSan) + '%'\n      })\n    }, null, 4 /* STYLE */)]), _createElementVNode(\"span\", _hoisted_25, _toDisplayString(player.stats.san) + \"/\" + _toDisplayString(player.stats.maxSan), 1 /* TEXT */)])])) : _createCommentVNode(\"v-if\", true)])]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 快速操作 \"), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"button\", {\n    onClick: _cache[6] || (_cache[6] = function () {\n      return _ctx.openCharacterSheet && _ctx.openCharacterSheet.apply(_ctx, arguments);\n    }),\n    \"class\": \"action-btn\"\n  }, _cache[40] || (_cache[40] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-id-card\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"角色卡\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[7] || (_cache[7] = function () {\n      return _ctx.openDiceRoller && _ctx.openDiceRoller.apply(_ctx, arguments);\n    }),\n    \"class\": \"action-btn\"\n  }, _cache[41] || (_cache[41] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-dice\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"投骰\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[8] || (_cache[8] = function () {\n      return _ctx.openInventory && _ctx.openInventory.apply(_ctx, arguments);\n    }),\n    \"class\": \"action-btn\"\n  }, _cache[42] || (_cache[42] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-backpack\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"背包\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：先攻追踪器 \"), _ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_createVNode(_component_InitiativeTracker, {\n    \"initiative-order\": ((_ctx$combatData2 = _ctx.combatData) === null || _ctx$combatData2 === void 0 ? void 0 : _ctx$combatData2.initiativeOrder) || [],\n    \"current-round\": ((_ctx$combatData3 = _ctx.combatData) === null || _ctx$combatData3 === void 0 ? void 0 : _ctx$combatData3.currentRound) || 1,\n    \"current-turn\": ((_ctx$combatData4 = _ctx.combatData) === null || _ctx$combatData4 === void 0 ? void 0 : _ctx$combatData4.currentTurn) || 0,\n    onParticipantSelected: _ctx.handleParticipantSelect\n  }, null, 8 /* PROPS */, [\"initiative-order\", \"current-round\", \"current-turn\", \"onParticipantSelected\"])])) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, !_ctx.leftPanelCollapsed]])], 2 /* CLASS */), _createCommentVNode(\" 中央面板：地图/战场 \"), _createElementVNode(\"div\", _hoisted_28, [_createCommentVNode(\" 非战斗时：场景标签页 \"), !_ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.sceneTabs, function (tab) {\n    return _openBlock(), _createElementBlock(\"button\", {\n      key: tab.id,\n      onClick: function onClick($event) {\n        return _ctx.activeSceneTab = tab.id;\n      },\n      \"class\": _normalizeClass([\"scene-tab\", {\n        'active': _ctx.activeSceneTab === tab.id\n      }])\n    }, [_createElementVNode(\"i\", {\n      \"class\": _normalizeClass(tab.icon)\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(tab.name), 1 /* TEXT */), tab.badge ? (_openBlock(), _createElementBlock(\"span\", _hoisted_31, _toDisplayString(tab.badge), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_30);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_32, [_createCommentVNode(\" 非战斗时：地图场景 \"), !_ctx.combatActive && _ctx.activeSceneTab === 'map' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_33, [_createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [_cache[44] || (_cache[44] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-map\"\n  }, null, -1 /* CACHED */)), _cache[45] || (_cache[45] = _createElementVNode(\"h3\", null, \"地图系统\", -1 /* CACHED */)), _cache[46] || (_cache[46] = _createElementVNode(\"p\", null, \"地图和战斗场景将在这里显示\", -1 /* CACHED */)), _ctx.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[9] || (_cache[9] = function () {\n      return _ctx.uploadMap && _ctx.uploadMap.apply(_ctx, arguments);\n    }),\n    \"class\": \"upload-map-btn\"\n  }, _cache[43] || (_cache[43] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-upload\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"上传地图\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)])])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：2D战场 \"), _ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_36, [_createVNode(_component_BattlefieldGrid, {\n    characters: ((_ctx$combatData5 = _ctx.combatData) === null || _ctx$combatData5 === void 0 || (_ctx$combatData5 = _ctx$combatData5.participants) === null || _ctx$combatData5 === void 0 ? void 0 : _ctx$combatData5.filter(function (p) {\n      return p.isPlayer;\n    })) || [],\n    monsters: ((_ctx$combatData6 = _ctx.combatData) === null || _ctx$combatData6 === void 0 || (_ctx$combatData6 = _ctx$combatData6.participants) === null || _ctx$combatData6 === void 0 ? void 0 : _ctx$combatData6.filter(function (p) {\n      return !p.isPlayer;\n    })) || [],\n    obstacles: ((_ctx$combatData7 = _ctx.combatData) === null || _ctx$combatData7 === void 0 ? void 0 : _ctx$combatData7.obstacles) || [],\n    \"area-effects\": ((_ctx$combatData8 = _ctx.combatData) === null || _ctx$combatData8 === void 0 ? void 0 : _ctx$combatData8.areaEffects) || [],\n    \"current-round\": ((_ctx$combatData9 = _ctx.combatData) === null || _ctx$combatData9 === void 0 ? void 0 : _ctx$combatData9.currentRound) || 1,\n    \"current-turn-character\": _ctx.getCurrentTurnCharacter(),\n    \"is-keeper\": _ctx.isKeeper,\n    \"battlefield-width\": ((_ctx$combatData0 = _ctx.combatData) === null || _ctx$combatData0 === void 0 || (_ctx$combatData0 = _ctx$combatData0.battlefieldSize) === null || _ctx$combatData0 === void 0 ? void 0 : _ctx$combatData0.width) || 20,\n    \"battlefield-height\": ((_ctx$combatData1 = _ctx.combatData) === null || _ctx$combatData1 === void 0 || (_ctx$combatData1 = _ctx$combatData1.battlefieldSize) === null || _ctx$combatData1 === void 0 ? void 0 : _ctx$combatData1.height) || 15,\n    onCharacterSelected: _ctx.handleCharacterSelected,\n    onMonsterSelected: _ctx.handleMonsterSelected,\n    onCharacterMoved: _ctx.handleCharacterMoved,\n    onMonsterMoved: _ctx.handleMonsterMoved,\n    onCharacterAction: _ctx.handleCharacterAction,\n    onMonsterAction: _ctx.handleMonsterAction,\n    onInspectCharacter: _ctx.handleInspectCharacter,\n    onInspectMonster: _ctx.handleInspectMonster,\n    onAddMonster: _ctx.handleAddMonster\n  }, null, 8 /* PROPS */, [\"characters\", \"monsters\", \"obstacles\", \"area-effects\", \"current-round\", \"current-turn-character\", \"is-keeper\", \"battlefield-width\", \"battlefield-height\", \"onCharacterSelected\", \"onMonsterSelected\", \"onCharacterMoved\", \"onMonsterMoved\", \"onCharacterAction\", \"onMonsterAction\", \"onInspectCharacter\", \"onInspectMonster\", \"onAddMonster\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 非战斗时：其他场景 \"), !_ctx.combatActive && _ctx.activeSceneTab === 'clues' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_cache[48] || (_cache[48] = _createElementVNode(\"h3\", null, \"线索墙\", -1 /* CACHED */)), _ctx.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[10] || (_cache[10] = function () {\n      return _ctx.addClue && _ctx.addClue.apply(_ctx, arguments);\n    }),\n    \"class\": \"add-btn\"\n  }, _cache[47] || (_cache[47] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"添加线索\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true)]), _createVNode(_component_ClueBoard, {\n    clues: _ctx.clues,\n    \"can-edit\": _ctx.isKeeper,\n    onAddClue: _ctx.addClue,\n    onUpdateClue: _ctx.updateClue,\n    onDeleteClue: _ctx.deleteClue\n  }, null, 8 /* PROPS */, [\"clues\", \"can-edit\", \"onAddClue\", \"onUpdateClue\", \"onDeleteClue\"])])) : _createCommentVNode(\"v-if\", true), !_ctx.combatActive && _ctx.activeSceneTab === 'notes' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_cache[51] || (_cache[51] = _createElementVNode(\"h3\", null, \"笔记本\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"button\", {\n    onClick: _cache[11] || (_cache[11] = function () {\n      return _ctx.addNote && _ctx.addNote.apply(_ctx, arguments);\n    }),\n    \"class\": \"add-btn\"\n  }, _cache[49] || (_cache[49] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"新建笔记\", -1 /* CACHED */)])), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = function ($event) {\n      return _ctx.noteFilter = $event;\n    }),\n    \"class\": \"note-filter\"\n  }, _cache[50] || (_cache[50] = [_createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"全部笔记\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"personal\"\n  }, \"个人笔记\", -1 /* CACHED */), _createElementVNode(\"option\", {\n    value: \"shared\"\n  }, \"共享笔记\", -1 /* CACHED */)]), 512 /* NEED_PATCH */), [[_vModelSelect, _ctx.noteFilter]])])]), _createElementVNode(\"div\", _hoisted_42, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.filteredNotes, function (note) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: note.id,\n      \"class\": \"note-card\"\n    }, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"h4\", _hoisted_44, _toDisplayString(note.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"span\", _hoisted_46, _toDisplayString(note.author), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_47, _toDisplayString(_ctx.formatTime(note.updatedAt)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_48, _toDisplayString(note.content), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"button\", {\n      onClick: function onClick($event) {\n        return _ctx.editNote(note);\n      },\n      \"class\": \"note-action-btn\"\n    }, _toConsumableArray(_cache[52] || (_cache[52] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-edit\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_50), _createElementVNode(\"button\", {\n      onClick: function onClick($event) {\n        return _ctx.shareNote(note);\n      },\n      \"class\": \"note-action-btn\"\n    }, _toConsumableArray(_cache[53] || (_cache[53] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-share\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_51), _createElementVNode(\"button\", {\n      onClick: function onClick($event) {\n        return _ctx.deleteNote(note);\n      },\n      \"class\": \"note-action-btn delete\"\n    }, _toConsumableArray(_cache[54] || (_cache[54] = [_createElementVNode(\"i\", {\n      \"class\": \"fas fa-trash\"\n    }, null, -1 /* CACHED */)])), 8 /* PROPS */, _hoisted_52)])]);\n  }), 128 /* KEYED_FRAGMENT */))])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" KP战斗控制面板 (仅KP可见且非战斗时) \"), _ctx.isKeeper && !_ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_53, [_createVNode(_component_KeeperCombatPanel, {\n    \"is-keeper\": _ctx.isKeeper,\n    \"room-data\": _ctx.roomData,\n    players: _ctx.players,\n    onCombatStarted: _ctx.handleCombatStart,\n    onCombatEnded: _ctx.handleCombatEnd,\n    onParticipantAdded: _ctx.handleParticipantAdded\n  }, null, 8 /* PROPS */, [\"is-keeper\", \"room-data\", \"players\", \"onCombatStarted\", \"onCombatEnded\", \"onParticipantAdded\"])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 右侧面板：聊天 + 战斗日志 \"), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"right-panel\", {\n      'collapsed': _ctx.rightPanelCollapsed\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.combatActive ? 'fas fa-scroll' : 'fas fa-comments')\n  }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString(_ctx.combatActive ? '战斗日志' : '聊天'), 1 /* TEXT */)]), !_ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_56, [_createElementVNode(\"button\", {\n    onClick: _cache[13] || (_cache[13] = function ($event) {\n      return _ctx.chatMode = 'ic';\n    }),\n    \"class\": _normalizeClass([\"chat-mode-btn\", {\n      'active': _ctx.chatMode === 'ic'\n    }])\n  }, \" IC \", 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[14] || (_cache[14] = function ($event) {\n      return _ctx.chatMode = 'ooc';\n    }),\n    \"class\": _normalizeClass([\"chat-mode-btn\", {\n      'active': _ctx.chatMode === 'ooc'\n    }])\n  }, \" OOC \", 2 /* CLASS */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[15] || (_cache[15] = function () {\n      return _ctx.toggleRightPanel && _ctx.toggleRightPanel.apply(_ctx, arguments);\n    }),\n    \"class\": \"panel-toggle\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.rightPanelCollapsed ? 'fas fa-chevron-left' : 'fas fa-chevron-right')\n  }, null, 2 /* CLASS */)])]), _withDirectives(_createElementVNode(\"div\", _hoisted_57, [_createCommentVNode(\" 非战斗时：聊天系统 \"), !_ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_58, [_createVNode(_component_ChatBox, {\n    messages: _ctx.messages,\n    \"current-user\": _ctx.currentUser,\n    \"chat-mode\": _ctx.chatMode,\n    onSendMessage: _ctx.sendMessage,\n    onRollDice: _ctx.handleDiceRoll\n  }, null, 8 /* PROPS */, [\"messages\", \"current-user\", \"chat-mode\", \"onSendMessage\", \"onRollDice\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：战斗日志 \"), _ctx.combatActive ? (_openBlock(), _createElementBlock(\"div\", _hoisted_59, [_createVNode(_component_CombatLog, {\n    \"combat-logs\": _ctx.combatLogs,\n    \"current-round\": ((_ctx$combatData10 = _ctx.combatData) === null || _ctx$combatData10 === void 0 ? void 0 : _ctx$combatData10.currentRound) || 1,\n    onLogAction: _ctx.handleLogAction\n  }, null, 8 /* PROPS */, [\"combat-logs\", \"current-round\", \"onLogAction\"])])) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), [[_vShow, !_ctx.rightPanelCollapsed]])], 2 /* CLASS */)], 2 /* CLASS */), _createCommentVNode(\" 底部工具栏 \"), _createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"bottom-toolbar\", {\n      'combat-toolbar': _ctx.combatActive\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_60, [_createElementVNode(\"div\", _hoisted_61, [_createElementVNode(\"button\", {\n    onClick: _cache[16] || (_cache[16] = function () {\n      return _ctx.toggleMute && _ctx.toggleMute.apply(_ctx, arguments);\n    }),\n    \"class\": _normalizeClass([\"audio-btn\", {\n      'muted': _ctx.isMuted\n    }])\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone')\n  }, null, 2 /* CLASS */)], 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[17] || (_cache[17] = function () {\n      return _ctx.toggleDeafen && _ctx.toggleDeafen.apply(_ctx, arguments);\n    }),\n    \"class\": _normalizeClass([\"audio-btn\", {\n      'deafened': _ctx.isDeafened\n    }])\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.isDeafened ? 'fas fa-volume-mute' : 'fas fa-volume-up')\n  }, null, 2 /* CLASS */)], 2 /* CLASS */), _createElementVNode(\"div\", _hoisted_62, [_withDirectives(_createElementVNode(\"input\", {\n    type: \"range\",\n    \"onUpdate:modelValue\": _cache[18] || (_cache[18] = function ($event) {\n      return _ctx.volume = $event;\n    }),\n    min: \"0\",\n    max: \"100\",\n    \"class\": \"volume-slider\"\n  }, null, 512 /* NEED_PATCH */), [[_vModelText, _ctx.volume]])])])]), _createElementVNode(\"div\", _hoisted_63, [_createCommentVNode(\" 非战斗时：常规工具 \"), !_ctx.combatActive ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createElementVNode(\"button\", {\n    onClick: _cache[19] || (_cache[19] = function () {\n      return _ctx.quickRoll && _ctx.quickRoll.apply(_ctx, arguments);\n    }),\n    \"class\": \"toolbar-btn primary\"\n  }, _cache[55] || (_cache[55] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-dice-d20\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"快速投骰\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[20] || (_cache[20] = function () {\n      return _ctx.openDiceRoller && _ctx.openDiceRoller.apply(_ctx, arguments);\n    }),\n    \"class\": \"toolbar-btn\"\n  }, _cache[56] || (_cache[56] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-dice\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"骰子面板\", -1 /* CACHED */)]))], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 战斗时：战斗工具 \"), _ctx.combatActive ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createElementVNode(\"button\", {\n    onClick: _cache[21] || (_cache[21] = function () {\n      return _ctx.nextTurn && _ctx.nextTurn.apply(_ctx, arguments);\n    }),\n    \"class\": \"toolbar-btn primary\",\n    disabled: !_ctx.isKeeper\n  }, _cache[57] || (_cache[57] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-step-forward\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"下一回合\", -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_64), _createElementVNode(\"button\", {\n    onClick: _cache[22] || (_cache[22] = function () {\n      return _ctx.openCombatActions && _ctx.openCombatActions.apply(_ctx, arguments);\n    }),\n    \"class\": \"toolbar-btn\"\n  }, _cache[58] || (_cache[58] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-fist-raised\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"行动菜单\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    onClick: _cache[23] || (_cache[23] = function () {\n      return _ctx.openDiceRoller && _ctx.openDiceRoller.apply(_ctx, arguments);\n    }),\n    \"class\": \"toolbar-btn\"\n  }, _cache[59] || (_cache[59] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-dice\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"投骰\", -1 /* CACHED */)]))], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_65, [_createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"div\", {\n    \"class\": _normalizeClass([\"status-indicator\", _ctx.connectionStatus])\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass(_ctx.getConnectionIcon())\n  }, null, 2 /* CLASS */)], 2 /* CLASS */), _createElementVNode(\"span\", _hoisted_67, _toDisplayString(_ctx.getConnectionText()), 1 /* TEXT */)])])], 2 /* CLASS */), _createCommentVNode(\" 浮动组件 \"), _createCommentVNode(\" 骰子面板 \"), _ctx.showDiceRoller ? (_openBlock(), _createBlock(_component_DiceRoller, {\n    key: 0,\n    onClose: _cache[24] || (_cache[24] = function ($event) {\n      return _ctx.showDiceRoller = false;\n    }),\n    onRoll: _ctx.handleDiceRoll,\n    onSkillCheck: _ctx.handleSkillCheck\n  }, null, 8 /* PROPS */, [\"onRoll\", \"onSkillCheck\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 强制战斗模式覆盖层 (玩家) \"), _ctx.combatActive && !_ctx.isKeeper && _ctx.shouldShowForcedMode ? (_openBlock(), _createBlock(_component_ForcedCombatMode, {\n    key: 1,\n    \"is-active\": _ctx.combatActive,\n    \"is-keeper\": _ctx.isKeeper,\n    \"combat-data\": _ctx.combatData,\n    character: _ctx.currentPlayerCharacter,\n    onActionConfirmed: _ctx.handlePlayerCombatAction,\n    onCombatModeEntered: _ctx.onCombatModeEntered,\n    onCombatModeExited: _ctx.onCombatModeExited\n  }, null, 8 /* PROPS */, [\"is-active\", \"is-keeper\", \"combat-data\", \"character\", \"onActionConfirmed\", \"onCombatModeEntered\", \"onCombatModeExited\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" KP战斗控制面板 (战斗时) \"), _ctx.combatActive && _ctx.isKeeper ? (_openBlock(), _createElementBlock(\"div\", _hoisted_68, [_createVNode(_component_KeeperCombatPanel, {\n    \"is-keeper\": _ctx.isKeeper,\n    \"combat-data\": _ctx.combatData,\n    players: _ctx.players,\n    onCombatAction: _ctx.handleKeeperCombatAction,\n    onEndCombat: _ctx.handleCombatEnd,\n    onNextTurn: _ctx.handleNextTurn,\n    onAddParticipant: _ctx.handleAddParticipant,\n    onRemoveParticipant: _ctx.handleRemoveParticipant\n  }, null, 8 /* PROPS */, [\"is-keeper\", \"combat-data\", \"players\", \"onCombatAction\", \"onEndCombat\", \"onNextTurn\", \"onAddParticipant\", \"onRemoveParticipant\"])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 角色详情浮动面板 \"), _ctx.showCharacterSheet ? (_openBlock(), _createBlock(_component_FloatingCharacterSheet, {\n    key: 3,\n    character: _ctx.selectedCharacterForSheet,\n    onClose: _cache[25] || (_cache[25] = function ($event) {\n      return _ctx.showCharacterSheet = false;\n    }),\n    onUpdate: _ctx.updateCharacter\n  }, null, 8 /* PROPS */, [\"character\", \"onUpdate\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 怪物详情浮动面板 (暂时使用角色卡组件) \"), _ctx.showMonsterSheet ? (_openBlock(), _createBlock(_component_FloatingCharacterSheet, {\n    key: 4,\n    character: _ctx.selectedMonsterForSheet,\n    onClose: _cache[26] || (_cache[26] = function ($event) {\n      return _ctx.showMonsterSheet = false;\n    }),\n    onUpdate: _ctx.updateMonster\n  }, null, 8 /* PROPS */, [\"character\", \"onUpdate\"])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "_normalizeClass", "_ctx", "combatActive", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_toDisplayString", "roomData", "name", "status", "getStatusText", "_hoisted_4", "_cache", "_hoisted_5", "_hoisted_6", "_ctx$roomData$creator", "creator", "username", "_hoisted_7", "currentPlayers", "maxPlayers", "_hoisted_8", "_ctx$combatData", "combatData", "currentRound", "_hoisted_9", "<PERSON><PERSON><PERSON><PERSON>", "onClick", "startCombat", "apply", "arguments", "title", "endCombat", "toggleFullscreen", "toggleSettings", "leaveRoom", "leftPanelCollapsed", "_hoisted_10", "_hoisted_11", "toggleLeftPanel", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_Fragment", "_renderList", "players", "player", "key", "id", "_hoisted_15", "src", "avatar", "alt", "_hoisted_17", "_hoisted_18", "<PERSON><PERSON><PERSON>", "isKP", "stats", "_hoisted_19", "_hoisted_20", "_hoisted_21", "style", "_normalizeStyle", "width", "getStatPercentage", "hp", "maxHp", "_hoisted_22", "_hoisted_23", "_hoisted_24", "san", "maxSan", "_hoisted_25", "_hoisted_26", "openCharacterSheet", "openDiceRoller", "openInventory", "_hoisted_27", "_createVNode", "_component_InitiativeTracker", "_ctx$combatData2", "initiativeOrder", "_ctx$combatData3", "_ctx$combatData4", "currentTurn", "onParticipantSelected", "handleParticipantSelect", "_hoisted_28", "_hoisted_29", "sceneTabs", "tab", "$event", "activeSceneTab", "icon", "badge", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_hoisted_35", "uploadMap", "_hoisted_36", "_component_BattlefieldGrid", "characters", "_ctx$combatData5", "participants", "filter", "p", "isPlayer", "monsters", "_ctx$combatData6", "obstacles", "_ctx$combatData7", "_ctx$combatData8", "areaEffects", "_ctx$combatData9", "getCurrentTurnCharacter", "_ctx$combatData0", "battlefieldSize", "_ctx$combatData1", "height", "onCharacterSelected", "handleCharacterSelected", "onMonsterSelected", "handleMonsterSelected", "onCharacterMoved", "handleCharacterMoved", "onMonsterMoved", "handleMonsterMoved", "onCharacterAction", "handleCharacterAction", "onMonsterAction", "handleMonsterAction", "onInspectCharacter", "handleInspectCharacter", "onInspectMonster", "handleInspectMonster", "onAddMonster", "handleAddMonster", "_hoisted_37", "_hoisted_38", "addClue", "_component_ClueBoard", "clues", "onAddClue", "onUpdateClue", "updateClue", "onDeleteClue", "deleteClue", "_hoisted_39", "_hoisted_40", "_hoisted_41", "addNote", "noteFilter", "value", "_hoisted_42", "filteredNotes", "note", "_hoisted_43", "_hoisted_44", "_hoisted_45", "_hoisted_46", "author", "_hoisted_47", "formatTime", "updatedAt", "_hoisted_48", "content", "_hoisted_49", "editNote", "shareNote", "deleteNote", "_hoisted_53", "_component_KeeperCombatPanel", "onCombatStarted", "handleCombatStart", "onCombatEnded", "handleCombatEnd", "onParticipantAdded", "handleParticipantAdded", "rightPanelCollapsed", "_hoisted_54", "_hoisted_55", "_hoisted_56", "chatMode", "toggleRightPanel", "_hoisted_57", "_hoisted_58", "_component_ChatBox", "messages", "currentUser", "onSendMessage", "sendMessage", "onRollDice", "handleDiceRoll", "_hoisted_59", "_component_CombatLog", "combatLogs", "_ctx$combatData10", "onLogAction", "handleLogAction", "_hoisted_60", "_hoisted_61", "toggleMute", "isMuted", "toggleDeafen", "isDeafened", "_hoisted_62", "type", "volume", "min", "max", "_hoisted_63", "quickRoll", "nextTurn", "disabled", "openCombatActions", "_hoisted_65", "_hoisted_66", "connectionStatus", "getConnectionIcon", "_hoisted_67", "getConnectionText", "showDiceRoller", "_createBlock", "_component_<PERSON><PERSON><PERSON><PERSON><PERSON>", "onClose", "onRoll", "onSkillCheck", "handleSkillCheck", "shouldShowForcedMode", "_component_ForcedCombatMode", "character", "currentPlayerCharacter", "onActionConfirmed", "handlePlayerCombatAction", "onCombatModeEntered", "onCombatModeExited", "_hoisted_68", "onCombatAction", "handleKeeperCombatAction", "onEndCombat", "onNextTurn", "handleNextTurn", "onAddParticipant", "handleAddParticipant", "onRemoveParticipant", "handleRemoveParticipant", "showCharacterSheet", "_component_FloatingCharacterSheet", "selectedCharacterForSheet", "onUpdate", "updateCharacter", "showMonsterSheet", "selectedMonsterForSheet", "updateMonster"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\GameRoomCombatIntegrated.vue"], "sourcesContent": ["<template>\r\n  <div class=\"game-room\" :class=\"{ 'combat-mode': combatActive }\">\r\n    <!-- 房间头部信息 -->\r\n    <div class=\"room-header\">\r\n      <div class=\"room-info\">\r\n        <div class=\"room-title\">\r\n          <i class=\"fas fa-door-open\"></i>\r\n          <h1>{{ roomData.name || '游戏房间' }}</h1>\r\n          <div class=\"room-status\" :class=\"roomData.status\">\r\n            <span class=\"status-dot\"></span>\r\n            <span>{{ getStatusText() }}</span>\r\n          </div>\r\n          <!-- 战斗状态指示器 -->\r\n          <div v-if=\"combatActive\" class=\"combat-indicator\">\r\n            <i class=\"fas fa-sword\"></i>\r\n            <span>战斗进行中</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"room-meta\">\r\n          <span class=\"room-creator\">\r\n            <i class=\"fas fa-crown\"></i>\r\n            KP: {{ roomData.creator?.username || '未知' }}\r\n          </span>\r\n          <span class=\"room-players\">\r\n            <i class=\"fas fa-users\"></i>\r\n            玩家: {{ currentPlayers }}/{{ roomData.maxPlayers || 6 }}\r\n          </span>\r\n          <span v-if=\"combatActive\" class=\"combat-round\">\r\n            <i class=\"fas fa-clock\"></i>\r\n            第{{ combatData?.currentRound || 1 }}轮\r\n          </span>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"room-controls\">\r\n        <!-- KP战斗控制按钮 -->\r\n        <button \r\n          v-if=\"isKeeper && !combatActive\" \r\n          @click=\"startCombat\" \r\n          class=\"control-btn combat-btn\"\r\n          title=\"开始战斗\"\r\n        >\r\n          <i class=\"fas fa-sword\"></i>\r\n        </button>\r\n        <button \r\n          v-if=\"isKeeper && combatActive\" \r\n          @click=\"endCombat\" \r\n          class=\"control-btn combat-btn active\"\r\n          title=\"结束战斗\"\r\n        >\r\n          <i class=\"fas fa-stop\"></i>\r\n        </button>\r\n        <button @click=\"toggleFullscreen\" class=\"control-btn\" title=\"全屏模式\">\r\n          <i class=\"fas fa-expand\"></i>\r\n        </button>\r\n        <button @click=\"toggleSettings\" class=\"control-btn\" title=\"房间设置\">\r\n          <i class=\"fas fa-cog\"></i>\r\n        </button>\r\n        <button @click=\"leaveRoom\" class=\"control-btn leave-btn\" title=\"离开房间\">\r\n          <i class=\"fas fa-sign-out-alt\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 游戏布局 -->\r\n    <div class=\"game-layout\" :class=\"{ 'combat-layout': combatActive }\">\r\n      <!-- 左侧面板：角色信息 + 先攻追踪器 -->\r\n      <div class=\"left-panel\" :class=\"{ 'collapsed': leftPanelCollapsed }\">\r\n        <div class=\"panel-header\">\r\n          <div class=\"panel-title\">\r\n            <i :class=\"combatActive ? 'fas fa-users-cog' : 'fas fa-users'\"></i>\r\n            <span>{{ combatActive ? '战斗状态' : '角色信息' }}</span>\r\n          </div>\r\n          <button @click=\"toggleLeftPanel\" class=\"panel-toggle\">\r\n            <i :class=\"leftPanelCollapsed ? 'fas fa-chevron-right' : 'fas fa-chevron-left'\"></i>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"panel-content\" v-show=\"!leftPanelCollapsed\">\r\n          <!-- 非战斗时：角色列表 -->\r\n          <div v-if=\"!combatActive\" class=\"character-section\">\r\n            <div class=\"character-list\">\r\n              <div v-for=\"player in players\" :key=\"player.id\" class=\"character-card\">\r\n                <div class=\"character-avatar\">\r\n                  <img :src=\"player.avatar || '/images/default-avatar.png'\" :alt=\"player.username\" />\r\n                  <div class=\"character-status\" :class=\"player.status\"></div>\r\n                </div>\r\n                <div class=\"character-info\">\r\n                  <div class=\"character-name\">{{ player.characterName || player.username }}</div>\r\n                  <div class=\"character-role\" :class=\"{ 'kp': player.isKP }\">\r\n                    {{ player.isKP ? 'KP' : 'PL' }}\r\n                  </div>\r\n                  <div class=\"character-stats\" v-if=\"player.stats\">\r\n                    <div class=\"stat-item\">\r\n                      <span class=\"stat-label\">HP:</span>\r\n                      <div class=\"stat-bar\">\r\n                        <div class=\"stat-fill hp\" :style=\"{ width: getStatPercentage(player.stats.hp, player.stats.maxHp) + '%' }\"></div>\r\n                      </div>\r\n                      <span class=\"stat-value\">{{ player.stats.hp }}/{{ player.stats.maxHp }}</span>\r\n                    </div>\r\n                    <div class=\"stat-item\">\r\n                      <span class=\"stat-label\">SAN:</span>\r\n                      <div class=\"stat-bar\">\r\n                        <div class=\"stat-fill san\" :style=\"{ width: getStatPercentage(player.stats.san, player.stats.maxSan) + '%' }\"></div>\r\n                      </div>\r\n                      <span class=\"stat-value\">{{ player.stats.san }}/{{ player.stats.maxSan }}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 快速操作 -->\r\n            <div class=\"quick-actions\">\r\n              <button @click=\"openCharacterSheet\" class=\"action-btn\">\r\n                <i class=\"fas fa-id-card\"></i>\r\n                <span>角色卡</span>\r\n              </button>\r\n              <button @click=\"openDiceRoller\" class=\"action-btn\">\r\n                <i class=\"fas fa-dice\"></i>\r\n                <span>投骰</span>\r\n              </button>\r\n              <button @click=\"openInventory\" class=\"action-btn\">\r\n                <i class=\"fas fa-backpack\"></i>\r\n                <span>背包</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 战斗时：先攻追踪器 -->\r\n          <div v-if=\"combatActive\" class=\"combat-section\">\r\n            <InitiativeTracker\r\n              :initiative-order=\"combatData?.initiativeOrder || []\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              :current-turn=\"combatData?.currentTurn || 0\"\r\n              @participant-selected=\"handleParticipantSelect\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>    \r\n  <!-- 中央面板：地图/战场 -->\r\n      <div class=\"center-panel\">\r\n        <!-- 非战斗时：场景标签页 -->\r\n        <div v-if=\"!combatActive\" class=\"scene-tabs\">\r\n          <button \r\n            v-for=\"tab in sceneTabs\" \r\n            :key=\"tab.id\"\r\n            @click=\"activeSceneTab = tab.id\"\r\n            class=\"scene-tab\"\r\n            :class=\"{ 'active': activeSceneTab === tab.id }\"\r\n          >\r\n            <i :class=\"tab.icon\"></i>\r\n            <span>{{ tab.name }}</span>\r\n            <span v-if=\"tab.badge\" class=\"tab-badge\">{{ tab.badge }}</span>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"scene-content\">\r\n          <!-- 非战斗时：地图场景 -->\r\n          <div v-if=\"!combatActive && activeSceneTab === 'map'\" class=\"scene-panel map-scene\">\r\n            <div class=\"map-container\">\r\n              <div class=\"map-placeholder\">\r\n                <i class=\"fas fa-map\"></i>\r\n                <h3>地图系统</h3>\r\n                <p>地图和战斗场景将在这里显示</p>\r\n                <button v-if=\"isKeeper\" @click=\"uploadMap\" class=\"upload-map-btn\">\r\n                  <i class=\"fas fa-upload\"></i>\r\n                  <span>上传地图</span>\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 战斗时：2D战场 -->\r\n          <div v-if=\"combatActive\" class=\"combat-scene\">\r\n            <BattlefieldGrid\r\n              :characters=\"combatData?.participants?.filter(p => p.isPlayer) || []\"\r\n              :monsters=\"combatData?.participants?.filter(p => !p.isPlayer) || []\"\r\n              :obstacles=\"combatData?.obstacles || []\"\r\n              :area-effects=\"combatData?.areaEffects || []\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              :current-turn-character=\"getCurrentTurnCharacter()\"\r\n              :is-keeper=\"isKeeper\"\r\n              :battlefield-width=\"combatData?.battlefieldSize?.width || 20\"\r\n              :battlefield-height=\"combatData?.battlefieldSize?.height || 15\"\r\n              @character-selected=\"handleCharacterSelected\"\r\n              @monster-selected=\"handleMonsterSelected\"\r\n              @character-moved=\"handleCharacterMoved\"\r\n              @monster-moved=\"handleMonsterMoved\"\r\n              @character-action=\"handleCharacterAction\"\r\n              @monster-action=\"handleMonsterAction\"\r\n              @inspect-character=\"handleInspectCharacter\"\r\n              @inspect-monster=\"handleInspectMonster\"\r\n              @add-monster=\"handleAddMonster\"\r\n            />\r\n          </div>\r\n          \r\n          <!-- 非战斗时：其他场景 -->\r\n          <div v-if=\"!combatActive && activeSceneTab === 'clues'\" class=\"scene-panel clues-scene\">\r\n            <div class=\"clues-header\">\r\n              <h3>线索墙</h3>\r\n              <button v-if=\"isKeeper\" @click=\"addClue\" class=\"add-btn\">\r\n                <i class=\"fas fa-plus\"></i>\r\n                <span>添加线索</span>\r\n              </button>\r\n            </div>\r\n            <ClueBoard \r\n              :clues=\"clues\"\r\n              :can-edit=\"isKeeper\"\r\n              @add-clue=\"addClue\"\r\n              @update-clue=\"updateClue\"\r\n              @delete-clue=\"deleteClue\"\r\n            />\r\n          </div>\r\n          \r\n          <div v-if=\"!combatActive && activeSceneTab === 'notes'\" class=\"scene-panel notes-scene\">\r\n            <div class=\"notes-header\">\r\n              <h3>笔记本</h3>\r\n              <div class=\"notes-controls\">\r\n                <button @click=\"addNote\" class=\"add-btn\">\r\n                  <i class=\"fas fa-plus\"></i>\r\n                  <span>新建笔记</span>\r\n                </button>\r\n                <select v-model=\"noteFilter\" class=\"note-filter\">\r\n                  <option value=\"all\">全部笔记</option>\r\n                  <option value=\"personal\">个人笔记</option>\r\n                  <option value=\"shared\">共享笔记</option>\r\n                </select>\r\n              </div>\r\n            </div>\r\n            <div class=\"notes-list\">\r\n              <div v-for=\"note in filteredNotes\" :key=\"note.id\" class=\"note-card\">\r\n                <div class=\"note-header\">\r\n                  <h4 class=\"note-title\">{{ note.title }}</h4>\r\n                  <div class=\"note-meta\">\r\n                    <span class=\"note-author\">{{ note.author }}</span>\r\n                    <span class=\"note-time\">{{ formatTime(note.updatedAt) }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"note-content\">{{ note.content }}</div>\r\n                <div class=\"note-actions\">\r\n                  <button @click=\"editNote(note)\" class=\"note-action-btn\">\r\n                    <i class=\"fas fa-edit\"></i>\r\n                  </button>\r\n                  <button @click=\"shareNote(note)\" class=\"note-action-btn\">\r\n                    <i class=\"fas fa-share\"></i>\r\n                  </button>\r\n                  <button @click=\"deleteNote(note)\" class=\"note-action-btn delete\">\r\n                    <i class=\"fas fa-trash\"></i>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- KP战斗控制面板 (仅KP可见且非战斗时) -->\r\n        <div v-if=\"isKeeper && !combatActive\" class=\"kp-combat-controls\">\r\n          <KeeperCombatPanel\r\n            :is-keeper=\"isKeeper\"\r\n            :room-data=\"roomData\"\r\n            :players=\"players\"\r\n            @combat-started=\"handleCombatStart\"\r\n            @combat-ended=\"handleCombatEnd\"\r\n            @participant-added=\"handleParticipantAdded\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 右侧面板：聊天 + 战斗日志 -->\r\n      <div class=\"right-panel\" :class=\"{ 'collapsed': rightPanelCollapsed }\">\r\n        <div class=\"panel-header\">\r\n          <div class=\"panel-title\">\r\n            <i :class=\"combatActive ? 'fas fa-scroll' : 'fas fa-comments'\"></i>\r\n            <span>{{ combatActive ? '战斗日志' : '聊天' }}</span>\r\n          </div>\r\n          <div v-if=\"!combatActive\" class=\"chat-controls\">\r\n            <button \r\n              @click=\"chatMode = 'ic'\" \r\n              class=\"chat-mode-btn\" \r\n              :class=\"{ 'active': chatMode === 'ic' }\"\r\n            >\r\n              IC\r\n            </button>\r\n            <button \r\n              @click=\"chatMode = 'ooc'\" \r\n              class=\"chat-mode-btn\" \r\n              :class=\"{ 'active': chatMode === 'ooc' }\"\r\n            >\r\n              OOC\r\n            </button>\r\n          </div>\r\n          <button @click=\"toggleRightPanel\" class=\"panel-toggle\">\r\n            <i :class=\"rightPanelCollapsed ? 'fas fa-chevron-left' : 'fas fa-chevron-right'\"></i>\r\n          </button>\r\n        </div>\r\n        \r\n        <div class=\"panel-content\" v-show=\"!rightPanelCollapsed\">\r\n          <!-- 非战斗时：聊天系统 -->\r\n          <div v-if=\"!combatActive\" class=\"chat-section\">\r\n            <ChatBox \r\n              :messages=\"messages\"\r\n              :current-user=\"currentUser\"\r\n              :chat-mode=\"chatMode\"\r\n              @send-message=\"sendMessage\"\r\n              @roll-dice=\"handleDiceRoll\"\r\n            />\r\n          </div>\r\n          \r\n          <!-- 战斗时：战斗日志 -->\r\n          <div v-if=\"combatActive\" class=\"combat-log-section\">\r\n            <CombatLog \r\n              :combat-logs=\"combatLogs\"\r\n              :current-round=\"combatData?.currentRound || 1\"\r\n              @log-action=\"handleLogAction\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>  \r\n  <!-- 底部工具栏 -->\r\n    <div class=\"bottom-toolbar\" :class=\"{ 'combat-toolbar': combatActive }\">\r\n      <div class=\"toolbar-left\">\r\n        <div class=\"audio-controls\">\r\n          <button @click=\"toggleMute\" class=\"audio-btn\" :class=\"{ 'muted': isMuted }\">\r\n            <i :class=\"isMuted ? 'fas fa-microphone-slash' : 'fas fa-microphone'\"></i>\r\n          </button>\r\n          <button @click=\"toggleDeafen\" class=\"audio-btn\" :class=\"{ 'deafened': isDeafened }\">\r\n            <i :class=\"isDeafened ? 'fas fa-volume-mute' : 'fas fa-volume-up'\"></i>\r\n          </button>\r\n          <div class=\"volume-control\">\r\n            <input type=\"range\" v-model=\"volume\" min=\"0\" max=\"100\" class=\"volume-slider\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <div class=\"toolbar-center\">\r\n        <!-- 非战斗时：常规工具 -->\r\n        <template v-if=\"!combatActive\">\r\n          <button @click=\"quickRoll\" class=\"toolbar-btn primary\">\r\n            <i class=\"fas fa-dice-d20\"></i>\r\n            <span>快速投骰</span>\r\n          </button>\r\n          <button @click=\"openDiceRoller\" class=\"toolbar-btn\">\r\n            <i class=\"fas fa-dice\"></i>\r\n            <span>骰子面板</span>\r\n          </button>\r\n        </template>\r\n        \r\n        <!-- 战斗时：战斗工具 -->\r\n        <template v-if=\"combatActive\">\r\n          <button @click=\"nextTurn\" class=\"toolbar-btn primary\" :disabled=\"!isKeeper\">\r\n            <i class=\"fas fa-step-forward\"></i>\r\n            <span>下一回合</span>\r\n          </button>\r\n          <button @click=\"openCombatActions\" class=\"toolbar-btn\">\r\n            <i class=\"fas fa-fist-raised\"></i>\r\n            <span>行动菜单</span>\r\n          </button>\r\n          <button @click=\"openDiceRoller\" class=\"toolbar-btn\">\r\n            <i class=\"fas fa-dice\"></i>\r\n            <span>投骰</span>\r\n          </button>\r\n        </template>\r\n      </div>\r\n      \r\n      <div class=\"toolbar-right\">\r\n        <div class=\"connection-status\">\r\n          <div class=\"status-indicator\" :class=\"connectionStatus\">\r\n            <i :class=\"getConnectionIcon()\"></i>\r\n          </div>\r\n          <span class=\"status-text\">{{ getConnectionText() }}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 浮动组件 -->\r\n    <!-- 骰子面板 -->\r\n    <DiceRoller \r\n      v-if=\"showDiceRoller\" \r\n      @close=\"showDiceRoller = false\"\r\n      @roll=\"handleDiceRoll\"\r\n      @skill-check=\"handleSkillCheck\"\r\n    />\r\n    \r\n    <!-- 强制战斗模式覆盖层 (玩家) -->\r\n    <ForcedCombatMode\r\n      v-if=\"combatActive && !isKeeper && shouldShowForcedMode\"\r\n      :is-active=\"combatActive\"\r\n      :is-keeper=\"isKeeper\"\r\n      :combat-data=\"combatData\"\r\n      :character=\"currentPlayerCharacter\"\r\n      @action-confirmed=\"handlePlayerCombatAction\"\r\n      @combat-mode-entered=\"onCombatModeEntered\"\r\n      @combat-mode-exited=\"onCombatModeExited\"\r\n    />\r\n    \r\n    <!-- KP战斗控制面板 (战斗时) -->\r\n    <div v-if=\"combatActive && isKeeper\" class=\"keeper-combat-overlay\">\r\n      <KeeperCombatPanel\r\n        :is-keeper=\"isKeeper\"\r\n        :combat-data=\"combatData\"\r\n        :players=\"players\"\r\n        @combat-action=\"handleKeeperCombatAction\"\r\n        @end-combat=\"handleCombatEnd\"\r\n        @next-turn=\"handleNextTurn\"\r\n        @add-participant=\"handleAddParticipant\"\r\n        @remove-participant=\"handleRemoveParticipant\"\r\n      />\r\n    </div>\r\n    \r\n    <!-- 角色详情浮动面板 -->\r\n    <FloatingCharacterSheet \r\n      v-if=\"showCharacterSheet\"\r\n      :character=\"selectedCharacterForSheet\"\r\n      @close=\"showCharacterSheet = false\"\r\n      @update=\"updateCharacter\"\r\n    />\r\n    \r\n    <!-- 怪物详情浮动面板 (暂时使用角色卡组件) -->\r\n    <FloatingCharacterSheet \r\n      v-if=\"showMonsterSheet\"\r\n      :character=\"selectedMonsterForSheet\"\r\n      @close=\"showMonsterSheet = false\"\r\n      @update=\"updateMonster\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport ChatBox from '@/components/ChatBox.vue'\r\nimport DiceRoller from '@/components/DiceRoller.vue'\r\nimport CombatLog from '@/components/combat/CombatLog.vue'\r\nimport BattlefieldGrid from '@/components/combat/BattlefieldGrid.vue'\r\nimport InitiativeTracker from '@/components/combat/InitiativeTracker.vue'\r\nimport KeeperCombatPanel from '@/components/combat/KeeperCombatPanel.vue'\r\nimport ForcedCombatMode from '@/components/combat/ForcedCombatMode.vue'\r\nimport FloatingCharacterSheet from '@/components/FloatingCharacterSheet.vue'\r\nimport ClueBoard from '@/components/ClueBoard.vue'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'GameRoomCombatIntegrated',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    ChatBox,\r\n    DiceRoller,\r\n    CombatLog,\r\n    BattlefieldGrid,\r\n    InitiativeTracker,\r\n    KeeperCombatPanel,\r\n    ForcedCombatMode,\r\n    FloatingCharacterSheet,\r\n    ClueBoard\r\n  },\r\n  props: {\r\n    roomId: {\r\n      type: String,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 房间数据\r\n      roomData: {\r\n        name: '神秘的古宅',\r\n        status: 'active',\r\n        creator: { username: 'KP_Master' },\r\n        maxPlayers: 6\r\n      },\r\n      \r\n      // 战斗状态\r\n      combatActive: false,\r\n      combatData: null,\r\n      combatLogs: [],\r\n      combatWebSocket: null,\r\n      shouldShowForcedMode: false,\r\n      \r\n      // 面板状态\r\n      leftPanelCollapsed: false,\r\n      rightPanelCollapsed: false,\r\n      activeSceneTab: 'map',\r\n      \r\n      // 聊天相关\r\n      chatMode: 'ic',\r\n      messages: [],\r\n      \r\n      // 玩家数据\r\n      players: [],\r\n      \r\n      // 线索数据\r\n      clues: [\r\n        {\r\n          id: 1,\r\n          title: '神秘的日记',\r\n          content: '在废弃的房屋中发现了一本日记，记录着奇怪的仪式...',\r\n          type: 'important',\r\n          discoverer: 'Player1',\r\n          createdAt: new Date()\r\n        }\r\n      ],\r\n      \r\n      // 笔记数据\r\n      notes: [\r\n        {\r\n          id: 1,\r\n          title: '调查笔记',\r\n          content: '今天的调查发现了一些线索...',\r\n          author: 'Player1',\r\n          shared: false,\r\n          updatedAt: new Date()\r\n        }\r\n      ],\r\n      \r\n      noteFilter: 'all',\r\n      \r\n      // 场景数据\r\n      sceneTabs: [\r\n        { id: 'map', name: '地图', icon: 'fas fa-map', badge: null },\r\n        { id: 'clues', name: '线索', icon: 'fas fa-search', badge: 3 },\r\n        { id: 'notes', name: '笔记', icon: 'fas fa-sticky-note', badge: null }\r\n      ],\r\n      \r\n      // 音频控制\r\n      isMuted: false,\r\n      isDeafened: false,\r\n      volume: 50,\r\n      \r\n      // 连接状态\r\n      connectionStatus: 'connected',\r\n      \r\n      // 组件状态\r\n      showDiceRoller: false,\r\n      showCharacterSheet: false,\r\n      showMonsterSheet: false,\r\n      selectedCharacterForSheet: null,\r\n      selectedMonsterForSheet: null,\r\n      \r\n      // 选中状态\r\n      selectedParticipant: null,\r\n      currentPlayerCharacter: null\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['currentUser']),\r\n    \r\n    currentPlayers() {\r\n      return this.players.length\r\n    },\r\n    \r\n    isKeeper() {\r\n      return this.currentUser?.isKP || false\r\n    },\r\n    \r\n    filteredNotes() {\r\n      if (this.noteFilter === 'all') return this.notes\r\n      if (this.noteFilter === 'personal') return this.notes.filter(note => note.author === this.currentUser?.username)\r\n      if (this.noteFilter === 'shared') return this.notes.filter(note => note.shared)\r\n      return this.notes\r\n    }\r\n  },\r\n  \r\n  async mounted() {\r\n    await this.initializeRoom()\r\n    await this.initializeCombatWebSocket()\r\n    this.restorePanelStates()\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    this.savePanelStates()\r\n    this.cleanupWebSocket()\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 初始化房间\r\n     */\r\n    async initializeRoom() {\r\n      // 加载房间数据\r\n      this.loadRoomData()\r\n      this.loadPlayers()\r\n      this.loadMessages()\r\n    },\r\n    \r\n    /**\r\n     * 初始化战斗WebSocket\r\n     */\r\n    async initializeCombatWebSocket() {\r\n      try {\r\n        // 确保有必要的数据才初始化WebSocket\r\n        if (!this.roomId || !this.currentUser?.id) {\r\n          console.warn('缺少必要数据，跳过WebSocket初始化')\r\n          return\r\n        }\r\n        \r\n        const { combatWebSocketManager } = await import('@/services/combatWebSocket.js')\r\n        \r\n        this.combatWebSocket = combatWebSocketManager.getConnection(\r\n          this.roomId,\r\n          this.currentUser.id,\r\n          {\r\n            url: process.env.VUE_APP_COMBAT_WS_URL || 'ws://localhost:8765',\r\n            token: this.$store.getters['auth/token']\r\n          }\r\n        )\r\n        \r\n        // 监听战斗事件\r\n        this.combatWebSocket.on('combatStateSync', this.handleCombatStateSync)\r\n        this.combatWebSocket.on('actionBroadcast', this.handleActionBroadcast)\r\n        this.combatWebSocket.on('forceCombatMode', this.handleForceCombatMode)\r\n        this.combatWebSocket.on('combatStarted', this.handleCombatStarted)\r\n        this.combatWebSocket.on('combatEnded', this.handleCombatEnded)\r\n        this.combatWebSocket.on('turnChanged', this.handleTurnChanged)\r\n        \r\n        await this.combatWebSocket.connect()\r\n      } catch (error) {\r\n        console.error('初始化战斗WebSocket失败:', error)\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 清理WebSocket连接\r\n     */\r\n    cleanupWebSocket() {\r\n      if (this.combatWebSocket) {\r\n        this.combatWebSocket.disconnect()\r\n        this.combatWebSocket = null\r\n      }\r\n    } \r\n   /**\r\n     * 战斗相关方法\r\n     */\r\n    async startCombat() {\r\n      try {\r\n        if (this.combatWebSocket) {\r\n          await this.combatWebSocket.send('startCombat', {\r\n            roomId: this.roomId,\r\n            participants: this.players.map(p => ({\r\n              id: p.id,\r\n              name: p.characterName || p.username,\r\n              isPlayer: !p.isKP,\r\n              position: { x: Math.floor(Math.random() * 10), y: Math.floor(Math.random() * 10) },\r\n              stats: p.stats || {}\r\n            }))\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('开始战斗失败:', error)\r\n      }\r\n    },\r\n    \r\n    async endCombat() {\r\n      try {\r\n        if (this.combatWebSocket) {\r\n          await this.combatWebSocket.send('endCombat', {\r\n            roomId: this.roomId\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('结束战斗失败:', error)\r\n      }\r\n    },\r\n    \r\n    async nextTurn() {\r\n      try {\r\n        if (this.combatWebSocket && this.isKeeper) {\r\n          await this.combatWebSocket.send('nextTurn', {\r\n            roomId: this.roomId\r\n          })\r\n        }\r\n      } catch (error) {\r\n        console.error('下一回合失败:', error)\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 战斗事件处理\r\n     */\r\n    handleCombatStateSync(data) {\r\n      this.combatData = data.combatState\r\n      this.combatActive = data.combatState.active\r\n      \r\n      if (this.combatActive) {\r\n        this.activeSceneTab = 'combat'\r\n        this.updateCurrentPlayerCharacter()\r\n      }\r\n    },\r\n    \r\n    handleActionBroadcast(data) {\r\n      this.combatLogs.push({\r\n        id: Date.now(),\r\n        type: 'action',\r\n        content: data.message,\r\n        timestamp: new Date(),\r\n        participant: data.participant\r\n      })\r\n    },\r\n    \r\n    handleForceCombatMode(data) {\r\n      this.shouldShowForcedMode = data.forced && !this.isKeeper\r\n    },\r\n    \r\n    handleCombatStarted(data) {\r\n      this.combatActive = true\r\n      this.combatData = data.combatState\r\n      this.combatLogs = []\r\n      this.updateCurrentPlayerCharacter()\r\n      \r\n      this.combatLogs.push({\r\n        id: Date.now(),\r\n        type: 'system',\r\n        content: '战斗开始！',\r\n        timestamp: new Date()\r\n      })\r\n    },\r\n    \r\n    handleCombatEnded(data) {\r\n      this.combatActive = false\r\n      this.combatData = null\r\n      this.shouldShowForcedMode = false\r\n      this.selectedParticipant = null\r\n      \r\n      this.combatLogs.push({\r\n        id: Date.now(),\r\n        type: 'system',\r\n        content: '战斗结束！',\r\n        timestamp: new Date()\r\n      })\r\n    },\r\n    \r\n    handleTurnChanged(data) {\r\n      if (this.combatData) {\r\n        this.combatData.currentTurn = data.currentTurn\r\n        this.combatData.currentRound = data.currentRound\r\n        \r\n        const currentParticipant = this.getCurrentTurnCharacter()\r\n        if (currentParticipant) {\r\n          this.combatLogs.push({\r\n            id: Date.now(),\r\n            type: 'turn',\r\n            content: `轮到 ${currentParticipant.name} 行动`,\r\n            timestamp: new Date(),\r\n            participant: currentParticipant\r\n          })\r\n        }\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 战场交互处理\r\n     */\r\n    handleCharacterSelected(character) {\r\n      this.selectedParticipant = character\r\n    },\r\n    \r\n    handleMonsterSelected(monster) {\r\n      this.selectedParticipant = monster\r\n    },\r\n    \r\n    handleCharacterMoved(data) {\r\n      if (this.combatWebSocket) {\r\n        this.combatWebSocket.send('participantMoved', {\r\n          roomId: this.roomId,\r\n          participantId: data.character.id,\r\n          oldPosition: data.oldPosition,\r\n          newPosition: data.newPosition\r\n        })\r\n      }\r\n    },\r\n    \r\n    handleMonsterMoved(data) {\r\n      if (this.combatWebSocket && this.isKeeper) {\r\n        this.combatWebSocket.send('participantMoved', {\r\n          roomId: this.roomId,\r\n          participantId: data.monster.id,\r\n          oldPosition: data.oldPosition,\r\n          newPosition: data.newPosition\r\n        })\r\n      }\r\n    },\r\n    \r\n    handleCharacterAction(data) {\r\n      if (this.combatWebSocket) {\r\n        this.combatWebSocket.send('participantAction', {\r\n          roomId: this.roomId,\r\n          participantId: data.character.id,\r\n          action: data.action\r\n        })\r\n      }\r\n    },\r\n    \r\n    handleMonsterAction(data) {\r\n      if (this.combatWebSocket && this.isKeeper) {\r\n        this.combatWebSocket.send('participantAction', {\r\n          roomId: this.roomId,\r\n          participantId: data.monster.id,\r\n          action: data.action\r\n        })\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 玩家战斗行动处理\r\n     */\r\n    handlePlayerCombatAction(actionData) {\r\n      if (this.combatWebSocket) {\r\n        this.combatWebSocket.send('playerAction', {\r\n          roomId: this.roomId,\r\n          playerId: this.currentUser.id,\r\n          action: actionData\r\n        })\r\n      }\r\n    },\r\n    \r\n    handleKeeperCombatAction(actionData) {\r\n      if (this.combatWebSocket && this.isKeeper) {\r\n        this.combatWebSocket.send('keeperAction', {\r\n          roomId: this.roomId,\r\n          action: actionData\r\n        })\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 工具函数\r\n     */\r\n    getCurrentTurnCharacter() {\r\n      if (!this.combatData || !this.combatData.initiativeOrder) return null\r\n      \r\n      const currentIndex = this.combatData.currentTurn || 0\r\n      return this.combatData.initiativeOrder[currentIndex] || null\r\n    },\r\n    \r\n    updateCurrentPlayerCharacter() {\r\n      if (!this.combatData) return\r\n      \r\n      const playerCharacter = this.combatData.participants?.find(p => \r\n        p.isPlayer && p.playerId === this.currentUser.id\r\n      )\r\n      \r\n      this.currentPlayerCharacter = playerCharacter || null\r\n    },\r\n    \r\n    /**\r\n     * 数据加载方法\r\n     */\r\n    loadRoomData() {\r\n      // 模拟加载房间数据\r\n      this.roomData = {\r\n        name: '神秘的古宅',\r\n        status: 'active',\r\n        creator: { username: 'KP_Master' },\r\n        maxPlayers: 6\r\n      }\r\n    },\r\n    \r\n    loadPlayers() {\r\n      // 模拟加载玩家数据\r\n      this.players = [\r\n        {\r\n          id: 1,\r\n          username: 'KP_Master',\r\n          characterName: 'KP',\r\n          isKP: true,\r\n          status: 'online',\r\n          avatar: '/images/default-avatar.png'\r\n        },\r\n        {\r\n          id: 2,\r\n          username: 'Player1',\r\n          characterName: '侦探约翰',\r\n          isKP: false,\r\n          status: 'online',\r\n          avatar: '/images/default-avatar.png',\r\n          stats: {\r\n            hp: 85,\r\n            maxHp: 100,\r\n            san: 72,\r\n            maxSan: 80\r\n          }\r\n        },\r\n        {\r\n          id: 3,\r\n          username: 'Player2',\r\n          characterName: '记者玛丽',\r\n          isKP: false,\r\n          status: 'online',\r\n          avatar: '/images/default-avatar.png',\r\n          stats: {\r\n            hp: 78,\r\n            maxHp: 90,\r\n            san: 65,\r\n            maxSan: 75\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    \r\n    loadMessages() {\r\n      // 模拟加载消息\r\n      this.messages = [\r\n        {\r\n          id: 1,\r\n          username: 'KP_Master',\r\n          content: '欢迎来到游戏房间！',\r\n          type: 'system',\r\n          timestamp: new Date(),\r\n          avatar: '/images/default-avatar.png'\r\n        }\r\n      ]\r\n      \r\n      // 模拟加载战斗日志\r\n      this.combatLogs = [\r\n        {\r\n          id: 1,\r\n          type: 'system',\r\n          message: '战斗开始！',\r\n          timestamp: new Date(),\r\n          round: 1\r\n        },\r\n        {\r\n          id: 2,\r\n          type: 'attack',\r\n          message: '侦探约翰对邪教徒发起攻击',\r\n          timestamp: new Date(),\r\n          round: 1,\r\n          participant: {\r\n            name: '侦探约翰',\r\n            avatar: '/images/default-avatar.png'\r\n          },\r\n          diceRoll: {\r\n            formula: '1d100',\r\n            rolls: [45],\r\n            total: 45\r\n          },\r\n          successLevel: 'regular'\r\n        },\r\n        {\r\n          id: 3,\r\n          type: 'damage',\r\n          message: '攻击命中，造成8点伤害',\r\n          timestamp: new Date(),\r\n          round: 1,\r\n          severity: 'critical'\r\n        }\r\n      ]\r\n    }   \r\n /**\r\n     * 界面控制方法\r\n     */\r\n    toggleLeftPanel() {\r\n      this.leftPanelCollapsed = !this.leftPanelCollapsed\r\n    },\r\n    \r\n    toggleRightPanel() {\r\n      this.rightPanelCollapsed = !this.rightPanelCollapsed\r\n    },\r\n    \r\n    toggleFullscreen() {\r\n      if (!document.fullscreenElement) {\r\n        document.documentElement.requestFullscreen()\r\n      } else {\r\n        document.exitFullscreen()\r\n      }\r\n    },\r\n    \r\n    toggleSettings() {\r\n      console.log('打开设置')\r\n    },\r\n    \r\n    leaveRoom() {\r\n      if (confirm('确定要离开房间吗？')) {\r\n        this.$router.push('/')\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 音频控制\r\n     */\r\n    toggleMute() {\r\n      this.isMuted = !this.isMuted\r\n    },\r\n    \r\n    toggleDeafen() {\r\n      this.isDeafened = !this.isDeafened\r\n    },\r\n    \r\n    /**\r\n     * 工具方法\r\n     */\r\n    openDiceRoller() {\r\n      this.showDiceRoller = true\r\n    },\r\n    \r\n    openCombatActions() {\r\n      // 打开战斗行动菜单\r\n      console.log('打开战斗行动菜单')\r\n    },\r\n    \r\n    quickRoll() {\r\n      const result = Math.floor(Math.random() * 100) + 1\r\n      this.handleDiceRoll({\r\n        total: result,\r\n        results: [result],\r\n        description: '1D100',\r\n        timestamp: Date.now()\r\n      })\r\n    },\r\n    \r\n    /**\r\n     * 聊天和骰子处理\r\n     */\r\n    sendMessage(message) {\r\n      const newMessage = {\r\n        id: Date.now(),\r\n        username: this.currentUser?.username || '匿名用户',\r\n        content: message,\r\n        type: this.chatMode,\r\n        timestamp: new Date(),\r\n        avatar: this.currentUser?.avatar\r\n      }\r\n      \r\n      this.messages.push(newMessage)\r\n    },\r\n    \r\n    handleDiceRoll(rollResult) {\r\n      const message = {\r\n        id: Date.now(),\r\n        username: this.currentUser?.username || '匿名用户',\r\n        content: `投掷 ${rollResult.description}: ${rollResult.total}`,\r\n        type: 'dice',\r\n        timestamp: new Date(),\r\n        avatar: this.currentUser?.avatar,\r\n        rollData: rollResult\r\n      }\r\n      \r\n      if (this.combatActive) {\r\n        this.combatLogs.push(message)\r\n      } else {\r\n        this.messages.push(message)\r\n      }\r\n    },\r\n    \r\n    handleSkillCheck(checkResult) {\r\n      const message = {\r\n        id: Date.now(),\r\n        username: this.currentUser?.username || '匿名用户',\r\n        content: `技能检定: ${checkResult.diceResult}/${checkResult.effectiveSkill} - ${checkResult.level}`,\r\n        type: 'skill-check',\r\n        timestamp: new Date(),\r\n        avatar: this.currentUser?.avatar,\r\n        checkData: checkResult\r\n      }\r\n      \r\n      if (this.combatActive) {\r\n        this.combatLogs.push(message)\r\n      } else {\r\n        this.messages.push(message)\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 工具函数\r\n     */\r\n    getStatusText() {\r\n      const statusMap = {\r\n        'active': '进行中',\r\n        'waiting': '等待中',\r\n        'paused': '已暂停',\r\n        'ended': '已结束'\r\n      }\r\n      return statusMap[this.roomData.status] || '未知'\r\n    },\r\n    \r\n    getConnectionIcon() {\r\n      const iconMap = {\r\n        'connected': 'fas fa-wifi',\r\n        'connecting': 'fas fa-spinner fa-spin',\r\n        'disconnected': 'fas fa-wifi-slash'\r\n      }\r\n      return iconMap[this.connectionStatus] || 'fas fa-question'\r\n    },\r\n    \r\n    getConnectionText() {\r\n      const textMap = {\r\n        'connected': '已连接',\r\n        'connecting': '连接中',\r\n        'disconnected': '已断开'\r\n      }\r\n      return textMap[this.connectionStatus] || '未知'\r\n    },\r\n    \r\n    /**\r\n     * 状态保存和恢复\r\n     */\r\n    savePanelStates() {\r\n      const states = {\r\n        leftPanelCollapsed: this.leftPanelCollapsed,\r\n        rightPanelCollapsed: this.rightPanelCollapsed,\r\n        activeSceneTab: this.activeSceneTab,\r\n        chatMode: this.chatMode\r\n      }\r\n      this.safeSetJSON(`gameroom_${this.roomId}_panels`, states)\r\n    },\r\n    \r\n    restorePanelStates() {\r\n      const states = this.safeGetJSON(`gameroom_${this.roomId}_panels`)\r\n      if (states) {\r\n        try {\r\n          this.leftPanelCollapsed = states.leftPanelCollapsed || false\r\n          this.rightPanelCollapsed = states.rightPanelCollapsed || false\r\n          this.activeSceneTab = states.activeSceneTab || 'map'\r\n          this.chatMode = states.chatMode || 'ic'\r\n        } catch (error) {\r\n          console.warn('恢复面板状态失败:', error)\r\n        }\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 缺失的方法实现\r\n     */\r\n    openInventory() {\r\n      console.log('打开背包')\r\n    },\r\n    \r\n    openCharacterSheet() {\r\n      this.selectedCharacterForSheet = this.currentPlayerCharacter\r\n      this.showCharacterSheet = true\r\n    },\r\n    \r\n    /**\r\n     * 线索和笔记管理\r\n     */\r\n    addClue() {\r\n      console.log('添加线索')\r\n    },\r\n    \r\n    updateClue(clue) {\r\n      console.log('更新线索:', clue)\r\n    },\r\n    \r\n    deleteClue(clue) {\r\n      console.log('删除线索:', clue)\r\n    },\r\n    \r\n    addNote() {\r\n      console.log('添加笔记')\r\n    },\r\n    \r\n    editNote(note) {\r\n      console.log('编辑笔记:', note)\r\n    },\r\n    \r\n    shareNote(note) {\r\n      console.log('分享笔记:', note)\r\n    },\r\n    \r\n    deleteNote(note) {\r\n      console.log('删除笔记:', note)\r\n    },\r\n    \r\n    updateCharacter(character) {\r\n      console.log('更新角色:', character)\r\n    },\r\n    \r\n    updateMonster(monster) {\r\n      console.log('更新怪物:', monster)\r\n    },\r\n    \r\n    /**\r\n     * 战场交互处理方法\r\n     */\r\n    handleParticipantSelect(participant) {\r\n      this.selectedParticipant = participant\r\n    },\r\n    \r\n    handleInspectCharacter(character) {\r\n      this.selectedCharacterForSheet = character\r\n      this.showCharacterSheet = true\r\n    },\r\n    \r\n    handleInspectMonster(monster) {\r\n      this.selectedMonsterForSheet = monster\r\n      this.showMonsterSheet = true\r\n    },\r\n    \r\n    handleAddMonster() {\r\n      console.log('添加怪物')\r\n    },\r\n    \r\n    handleCombatStart(data) {\r\n      this.combatActive = true\r\n      this.combatData = data\r\n    },\r\n    \r\n    handleCombatEnd() {\r\n      this.combatActive = false\r\n      this.combatData = null\r\n    },\r\n    \r\n    handleParticipantAdded(participant) {\r\n      console.log('添加参与者:', participant)\r\n    },\r\n    \r\n    handleNextTurn() {\r\n      this.nextTurn()\r\n    },\r\n    \r\n    handleAddParticipant(participant) {\r\n      console.log('添加参与者:', participant)\r\n    },\r\n    \r\n    handleRemoveParticipant(participant) {\r\n      console.log('移除参与者:', participant)\r\n    },\r\n    \r\n    handleLogAction(action) {\r\n      console.log('记录行动:', action)\r\n    },\r\n    \r\n    onCombatModeEntered() {\r\n      console.log('进入战斗模式')\r\n    },\r\n    \r\n    onCombatModeExited() {\r\n      console.log('退出战斗模式')\r\n    },\r\n    \r\n    uploadMap() {\r\n      console.log('上传地图')\r\n    },\r\n    \r\n    /**\r\n     * 工具函数\r\n     */\r\n    getStatPercentage(current, max) {\r\n      return max > 0 ? Math.round((current / max) * 100) : 0\r\n    },\r\n    \r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      const date = new Date(time)\r\n      return date.toLocaleTimeString('zh-CN', { \r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* ===== 游戏房间基础样式 ===== */\r\n.game-room {\r\n  height: 100vh;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-room.combat-mode {\r\n  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\r\n}\r\n\r\n/* ===== 房间头部 ===== */\r\n.room-header {\r\n  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\r\n  color: white;\r\n  padding: 16px 24px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n  border-bottom: 2px solid #15803d;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.game-room.combat-mode .room-header {\r\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\r\n  border-bottom-color: #991b1b;\r\n}\r\n\r\n.room-info {\r\n  flex: 1;\r\n}\r\n\r\n.room-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.room-title h1 {\r\n  margin: 0;\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.room-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.15);\r\n  padding: 4px 12px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.combat-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  padding: 6px 12px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  font-weight: bold;\r\n  animation: combat-pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes combat-pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.7; }\r\n}\r\n\r\n.status-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  background: #4ade80;\r\n  animation: status-pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes status-pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.6; }\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  gap: 16px;\r\n  font-size: 14px;\r\n  opacity: 0.9;\r\n}\r\n\r\n.room-creator,\r\n.room-players,\r\n.combat-round {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.room-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.control-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.control-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.control-btn.combat-btn {\r\n  background: rgba(220, 38, 38, 0.8);\r\n  border-color: rgba(220, 38, 38, 0.6);\r\n}\r\n\r\n.control-btn.combat-btn.active {\r\n  background: rgba(239, 68, 68, 0.9);\r\n  animation: combat-active 1.5s ease-in-out infinite;\r\n}\r\n\r\n@keyframes combat-active {\r\n  0%, 100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }\r\n  50% { box-shadow: 0 0 0 8px rgba(239, 68, 68, 0); }\r\n}\r\n\r\n.control-btn.leave-btn:hover {\r\n  background: rgba(239, 68, 68, 0.8);\r\n}\r\n\r\n/* ===== 游戏布局 ===== */\r\n.game-layout {\r\n  flex: 1;\r\n  display: flex;\r\n  overflow: hidden;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.game-layout.combat-layout {\r\n  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);\r\n}\r\n\r\n/* ===== 面板样式 ===== */\r\n.left-panel,\r\n.right-panel {\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  display: flex;\r\n  flex-direction: column;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.game-room.combat-mode .left-panel,\r\n.game-room.combat-mode .right-panel {\r\n  background: rgba(31, 41, 55, 0.95);\r\n  border-color: #4b5563;\r\n}\r\n\r\n.left-panel {\r\n  width: 300px;\r\n  border-right: 2px solid #e5e7eb;\r\n}\r\n\r\n.right-panel {\r\n  width: 350px;\r\n  border-left: 2px solid #e5e7eb;\r\n}\r\n\r\n.game-room.combat-mode .left-panel {\r\n  border-right-color: #4b5563;\r\n}\r\n\r\n.game-room.combat-mode .right-panel {\r\n  border-left-color: #4b5563;\r\n}\r\n\r\n.left-panel.collapsed {\r\n  width: 60px;\r\n}\r\n\r\n.right-panel.collapsed {\r\n  width: 60px;\r\n}\r\n\r\n.panel-header {\r\n  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);\r\n  color: white;\r\n  padding: 16px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.game-room.combat-mode .panel-header {\r\n  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\r\n}\r\n\r\n.panel-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-weight: bold;\r\n}\r\n\r\n.panel-toggle {\r\n  width: 28px;\r\n  height: 28px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 6px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.panel-toggle:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.panel-content {\r\n  flex: 1;\r\n  padding: 16px;\r\n  overflow-y: auto;\r\n  background: #f9fafb;\r\n}\r\n\r\n.game-room.combat-mode .panel-content {\r\n  background: rgba(17, 24, 39, 0.8);\r\n  color: #f3f4f6;\r\n}\r\n\r\n/* ===== 中央面板 ===== */\r\n.center-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  background: white;\r\n  position: relative;\r\n}\r\n\r\n.game-room.combat-mode .center-panel {\r\n  background: rgba(17, 24, 39, 0.9);\r\n}\r\n\r\n.scene-tabs {\r\n  display: flex;\r\n  background: #f3f4f6;\r\n  border-bottom: 1px solid #e5e7eb;\r\n  padding: 0 16px;\r\n}\r\n\r\n.scene-tab {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px 16px;\r\n  border: none;\r\n  background: none;\r\n  color: #6b7280;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-bottom: 2px solid transparent;\r\n  position: relative;\r\n}\r\n\r\n.scene-tab:hover {\r\n  color: #374151;\r\n  background: rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.scene-tab.active {\r\n  color: #22c55e;\r\n  border-bottom-color: #22c55e;\r\n  background: rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.tab-badge {\r\n  background: #ef4444;\r\n  color: white;\r\n  font-size: 12px;\r\n  padding: 2px 6px;\r\n  border-radius: 10px;\r\n  min-width: 18px;\r\n  text-align: center;\r\n}\r\n\r\n.scene-content {\r\n  flex: 1;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.scene-panel {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  padding: 24px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.combat-scene {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: #1f2937;\r\n}\r\n\r\n.map-placeholder {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  color: #6b7280;\r\n  text-align: center;\r\n}\r\n\r\n.map-placeholder i {\r\n  font-size: 64px;\r\n  margin-bottom: 16px;\r\n  opacity: 0.5;\r\n}\r\n\r\n.upload-map-btn {\r\n  margin-top: 16px;\r\n  padding: 12px 24px;\r\n  background: #22c55e;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.upload-map-btn:hover {\r\n  background: #16a34a;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* ===== KP战斗控制面板 ===== */\r\n.kp-combat-controls {\r\n  position: absolute;\r\n  bottom: 16px;\r\n  right: 16px;\r\n  z-index: 5;\r\n}\r\n\r\n.keeper-combat-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n/* ===== 底部工具栏 ===== */\r\n.bottom-toolbar {\r\n  background: white;\r\n  border-top: 1px solid #e5e7eb;\r\n  padding: 12px 24px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.bottom-toolbar.combat-toolbar {\r\n  background: rgba(31, 41, 55, 0.95);\r\n  border-top-color: #4b5563;\r\n  color: #f3f4f6;\r\n}\r\n\r\n.toolbar-left,\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.toolbar-center {\r\n  display: flex;\r\n  gap: 12px;\r\n}\r\n\r\n.audio-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.audio-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background: #f9fafb;\r\n  color: #6b7280;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.audio-btn:hover {\r\n  background: #f3f4f6;\r\n  border-color: #9ca3af;\r\n}\r\n\r\n.audio-btn.muted,\r\n.audio-btn.deafened {\r\n  background: #fef2f2;\r\n  border-color: #fca5a5;\r\n  color: #dc2626;\r\n}\r\n\r\n.volume-control {\r\n  width: 80px;\r\n}\r\n\r\n.volume-slider {\r\n  width: 100%;\r\n  height: 4px;\r\n  border-radius: 2px;\r\n  background: #d1d5db;\r\n  outline: none;\r\n  cursor: pointer;\r\n}\r\n\r\n.toolbar-btn {\r\n  padding: 8px 16px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background: #f9fafb;\r\n  color: #374151;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.toolbar-btn:hover {\r\n  background: #f3f4f6;\r\n  border-color: #9ca3af;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.toolbar-btn.primary {\r\n  background: #22c55e;\r\n  border-color: #16a34a;\r\n  color: white;\r\n}\r\n\r\n.toolbar-btn.primary:hover {\r\n  background: #16a34a;\r\n}\r\n\r\n.toolbar-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.connection-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.status-indicator {\r\n  width: 20px;\r\n  height: 20px;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 12px;\r\n}\r\n\r\n.status-indicator.connected {\r\n  background: #dcfce7;\r\n  color: #16a34a;\r\n}\r\n\r\n.status-indicator.connecting {\r\n  background: #fef3c7;\r\n  color: #d97706;\r\n}\r\n\r\n.status-indicator.disconnected {\r\n  background: #fef2f2;\r\n  color: #dc2626;\r\n}\r\n\r\n/* ===== 聊天控制 ===== */\r\n.chat-controls {\r\n  display: flex;\r\n  gap: 4px;\r\n}\r\n\r\n.chat-mode-btn {\r\n  padding: 4px 8px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 4px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.8);\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n}\r\n\r\n.chat-mode-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.chat-mode-btn.active {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n@media (max-width: 1024px) {\r\n  .left-panel {\r\n    width: 250px;\r\n  }\r\n  \r\n  .right-panel {\r\n    width: 300px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .game-layout {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .left-panel,\r\n  .right-panel {\r\n    width: 100%;\r\n    height: 200px;\r\n  }\r\n  \r\n  .left-panel.collapsed,\r\n  .right-panel.collapsed {\r\n    height: 60px;\r\n  }\r\n  \r\n  .room-title h1 {\r\n    font-size: 18px;\r\n  }\r\n  \r\n  .room-meta {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n  \r\n  .toolbar-center {\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n}\r\n</style>/\r\n* ===== 角色列表样式 ===== */\r\n.character-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.character-card {\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 12px;\r\n  padding: 12px;\r\n  display: flex;\r\n  gap: 12px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.game-room.combat-mode .character-card {\r\n  background: rgba(55, 65, 81, 0.9);\r\n  border-color: #6b7280;\r\n  color: #f3f4f6;\r\n}\r\n\r\n.character-card:hover {\r\n  border-color: #22c55e;\r\n  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.character-avatar {\r\n  position: relative;\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 50%;\r\n  overflow: hidden;\r\n  border: 2px solid #22c55e;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.character-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.character-status {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: 50%;\r\n  border: 2px solid white;\r\n}\r\n\r\n.character-status.online {\r\n  background: #22c55e;\r\n}\r\n\r\n.character-status.away {\r\n  background: #f59e0b;\r\n}\r\n\r\n.character-status.offline {\r\n  background: #ef4444;\r\n}\r\n\r\n.character-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.character-name {\r\n  font-weight: bold;\r\n  color: #1f2937;\r\n  font-size: 14px;\r\n}\r\n\r\n.game-room.combat-mode .character-name {\r\n  color: #f3f4f6;\r\n}\r\n\r\n.character-role {\r\n  font-size: 12px;\r\n  color: #6b7280;\r\n  padding: 2px 8px;\r\n  border-radius: 6px;\r\n  background: #f3f4f6;\r\n  align-self: flex-start;\r\n}\r\n\r\n.character-role.kp {\r\n  background: #fef3c7;\r\n  color: #d97706;\r\n}\r\n\r\n.game-room.combat-mode .character-role {\r\n  background: rgba(75, 85, 99, 0.8);\r\n  color: #d1d5db;\r\n}\r\n\r\n.character-stats {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  margin-top: 4px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n}\r\n\r\n.stat-label {\r\n  min-width: 30px;\r\n  font-weight: 600;\r\n  color: #6b7280;\r\n}\r\n\r\n.game-room.combat-mode .stat-label {\r\n  color: #9ca3af;\r\n}\r\n\r\n.stat-bar {\r\n  flex: 1;\r\n  height: 6px;\r\n  background: #f3f4f6;\r\n  border-radius: 3px;\r\n  overflow: hidden;\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.game-room.combat-mode .stat-bar {\r\n  background: rgba(75, 85, 99, 0.5);\r\n  border-color: #6b7280;\r\n}\r\n\r\n.stat-fill {\r\n  height: 100%;\r\n  border-radius: 3px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n.stat-fill.hp {\r\n  background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);\r\n}\r\n\r\n.stat-fill.san {\r\n  background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);\r\n}\r\n\r\n.stat-value {\r\n  font-size: 11px;\r\n  color: #6b7280;\r\n  font-weight: 500;\r\n  min-width: 40px;\r\n  text-align: right;\r\n}\r\n\r\n.game-room.combat-mode .stat-value {\r\n  color: #9ca3af;\r\n}\r\n\r\n.quick-actions {\r\n  display: grid;\r\n  grid-template-columns: repeat(3, 1fr);\r\n  gap: 8px;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n  padding: 12px;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\r\n  color: #6b7280;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 12px;\r\n  text-align: center;\r\n}\r\n\r\n.game-room.combat-mode .action-btn {\r\n  background: rgba(55, 65, 81, 0.8);\r\n  border-color: #6b7280;\r\n  color: #d1d5db;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);\r\n  border-color: #22c55e;\r\n  color: #22c55e;\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.game-room.combat-mode .action-btn:hover {\r\n  background: rgba(75, 85, 99, 0.9);\r\n  border-color: #22c55e;\r\n  color: #22c55e;\r\n}\r\n\r\n/* ===== 线索和笔记样式 ===== */\r\n.clues-header,\r\n.notes-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid #e5e7eb;\r\n}\r\n\r\n.clues-header h3,\r\n.notes-header h3 {\r\n  margin: 0;\r\n  color: #1f2937;\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.add-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 8px 16px;\r\n  background: #22c55e;\r\n  color: white;\r\n  border: none;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 14px;\r\n}\r\n\r\n.add-btn:hover {\r\n  background: #16a34a;\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.notes-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.note-filter {\r\n  padding: 6px 12px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background: white;\r\n  color: #374151;\r\n  font-size: 14px;\r\n}\r\n\r\n.notes-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.note-card {\r\n  background: white;\r\n  border: 1px solid #e5e7eb;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.note-card:hover {\r\n  border-color: #22c55e;\r\n  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.note-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.note-title {\r\n  margin: 0;\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #1f2937;\r\n}\r\n\r\n.note-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-end;\r\n  gap: 4px;\r\n  font-size: 12px;\r\n  color: #6b7280;\r\n}\r\n\r\n.note-content {\r\n  color: #374151;\r\n  line-height: 1.5;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.note-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.note-action-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background: white;\r\n  color: #6b7280;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.note-action-btn:hover {\r\n  border-color: #22c55e;\r\n  color: #22c55e;\r\n}\r\n\r\n.note-action-btn.delete:hover {\r\n  border-color: #ef4444;\r\n  color: #ef4444;\r\n}"], "mappings": ";;;;;;;;EAGS,SAAM;AAAa;;EACjB,SAAM;AAAW;;EACf,SAAM;AAAY;;;EAQI,SAAM;;;EAK5B,SAAM;AAAW;;EACd,SAAM;AAAc;;EAIpB,SAAM;AAAc;;;EAIA,SAAM;;;EAO/B,SAAM;AAAe;;EAkCnB,SAAM;AAAc;;EAClB,SAAM;AAAa;;EASrB,SAAM;AAAe;;;EAEE,SAAM;;;EACzB,SAAM;AAAgB;;EAElB,SAAM;AAAkB;;;EAIxB,SAAM;AAAgB;;EACpB,SAAM;AAAgB;;;EAItB,SAAM;;;EACJ,SAAM;AAAW;;EAEf,SAAM;AAAU;;EAGf,SAAM;AAAY;;EAErB,SAAM;AAAW;;EAEf,SAAM;AAAU;;EAGf,SAAM;AAAY;;EAQ7B,SAAM;AAAe;;;EAiBH,SAAM;;;EAW9B,SAAM;AAAc;;;EAEG,SAAM;;;;;EAUL,SAAM;;;EAI5B,SAAM;AAAe;;;EAE8B,SAAM;;;EACrD,SAAM;AAAe;;EACnB,SAAM;AAAiB;;;EAaP,SAAM;;;;EAwByB,SAAM;;;EACvD,SAAM;AAAc;;;EAgB6B,SAAM;;;EACvD,SAAM;AAAc;;EAElB,SAAM;AAAgB;;EAYxB,SAAM;AAAY;;EAEd,SAAM;AAAa;;EAClB,SAAM;AAAY;;EACjB,SAAM;AAAW;;EACd,SAAM;AAAa;;EACnB,SAAM;AAAW;;EAGtB,SAAM;AAAc;;EACpB,SAAM;AAAc;;;;;;EAiBK,SAAM;;;EAcvC,SAAM;AAAc;;EAClB,SAAM;AAAa;;;EAIE,SAAM;;;EAqB7B,SAAM;AAAe;;;EAEE,SAAM;;;;EAWP,SAAM;;;EAY9B,SAAM;AAAc;;EAClB,SAAM;AAAgB;;EAOpB,SAAM;AAAgB;;EAM1B,SAAM;AAAgB;;;EA8BtB,SAAM;AAAe;;EACnB,SAAM;AAAmB;;EAItB,SAAM;AAAa;;;EA2BM,SAAM;;;;;;;;;;;;;uBA7Y7CA,mBAAA,CAyaM;IAzaD,SAAKC,eAAA,EAAC,WAAW;MAAA,eAA0BC,IAAA,CAAAC;IAAY;MAC1DC,mBAAA,YAAe,EACfC,mBAAA,CA2DM,OA3DNC,UA2DM,GA1DJD,mBAAA,CA4BM,OA5BNE,UA4BM,GA3BJF,mBAAA,CAYM,OAZNG,UAYM,G,4BAXJH,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,4BAC3BA,mBAAA,CAAsC,YAAAI,gBAAA,CAA/BP,IAAA,CAAAQ,QAAQ,CAACC,IAAI,4BACpBN,mBAAA,CAGM;IAHD,SAAKJ,eAAA,EAAC,aAAa,EAASC,IAAA,CAAAQ,QAAQ,CAACE,MAAM;kCAC9CP,mBAAA,CAAgC;IAA1B,SAAM;EAAY,4BACxBA,mBAAA,CAAkC,cAAAI,gBAAA,CAAzBP,IAAA,CAAAW,aAAa,mB,kBAExBT,mBAAA,aAAgB,EACLF,IAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAGM,OAHNc,UAGM,EAAAC,MAAA,SAAAA,MAAA,QAFJV,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2BACvBA,mBAAA,CAAkB,cAAZ,OAAK,mB,2CAGfA,mBAAA,CAaM,OAbNW,UAaM,GAZJX,mBAAA,CAGO,QAHPY,UAGO,G,4BAFLZ,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,OACxB,GAAAI,gBAAA,CAAG,EAAAS,qBAAA,GAAAhB,IAAA,CAAAQ,QAAQ,CAACS,OAAO,cAAAD,qBAAA,uBAAhBA,qBAAA,CAAkBE,QAAQ,0B,GAEnCf,mBAAA,CAGO,QAHPgB,UAGO,G,4BAFLhB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,OACxB,GAAAI,gBAAA,CAAGP,IAAA,CAAAoB,cAAc,IAAG,GAAC,GAAAb,gBAAA,CAAGP,IAAA,CAAAQ,QAAQ,CAACa,UAAU,sB,GAErCrB,IAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAGO,QAHPwB,UAGO,G,4BAFLnB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,IAC3B,GAAAI,gBAAA,CAAG,EAAAgB,eAAA,GAAAvB,IAAA,CAAAwB,UAAU,cAAAD,eAAA,uBAAVA,eAAA,CAAYE,YAAY,UAAQ,IACtC,gB,4CAIJtB,mBAAA,CA2BM,OA3BNuB,UA2BM,GA1BJxB,mBAAA,cAAiB,EAETF,IAAA,CAAA2B,QAAQ,KAAK3B,IAAA,CAAAC,YAAY,I,cADjCH,mBAAA,CAOS;;IALN8B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA6B,WAAA,IAAA7B,IAAA,CAAA6B,WAAA,CAAAC,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAW;IAAA;IACnB,SAAM,wBAAwB;IAC9BC,KAAK,EAAC;kCAEN7B,mBAAA,CAA4B;IAAzB,SAAM;EAAc,0B,yCAGjBH,IAAA,CAAA2B,QAAQ,IAAI3B,IAAA,CAAAC,YAAY,I,cADhCH,mBAAA,CAOS;;IALN8B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAiC,SAAA,IAAAjC,IAAA,CAAAiC,SAAA,CAAAH,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAS;IAAA;IACjB,SAAM,+BAA+B;IACrCC,KAAK,EAAC;kCAEN7B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,yCAExBA,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAkC,gBAAA,IAAAlC,IAAA,CAAAkC,gBAAA,CAAAJ,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAgB;IAAA;IAAE,SAAM,aAAa;IAACC,KAAK,EAAC;kCAC1D7B,mBAAA,CAA6B;IAA1B,SAAM;EAAe,0B,IAE1BA,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAmC,cAAA,IAAAnC,IAAA,CAAAmC,cAAA,CAAAL,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAc;IAAA;IAAE,SAAM,aAAa;IAACC,KAAK,EAAC;kCACxD7B,mBAAA,CAA0B;IAAvB,SAAM;EAAY,0B,IAEvBA,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAoC,SAAA,IAAApC,IAAA,CAAAoC,SAAA,CAAAN,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAS;IAAA;IAAE,SAAM,uBAAuB;IAACC,KAAK,EAAC;kCAC7D7B,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,0B,QAKpCD,mBAAA,UAAa,EACbC,mBAAA,CA8PM;IA9PD,SAAKJ,eAAA,EAAC,aAAa;MAAA,iBAA4BC,IAAA,CAAAC;IAAY;MAC9DC,mBAAA,uBAA0B,EAC1BC,mBAAA,CAwEM;IAxED,SAAKJ,eAAA,EAAC,YAAY;MAAA,aAAwBC,IAAA,CAAAqC;IAAkB;MAC/DlC,mBAAA,CAQM,OARNmC,WAQM,GAPJnC,mBAAA,CAGM,OAHNoC,WAGM,GAFJpC,mBAAA,CAAmE;IAA/D,SAAKJ,eAAA,CAAEC,IAAA,CAAAC,YAAY;2BACvBE,mBAAA,CAAiD,cAAAI,gBAAA,CAAxCP,IAAA,CAAAC,YAAY,mC,GAEvBE,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAwC,eAAA,IAAAxC,IAAA,CAAAwC,eAAA,CAAAV,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAe;IAAA;IAAE,SAAM;MACrC5B,mBAAA,CAAoF;IAAhF,SAAKJ,eAAA,CAAEC,IAAA,CAAAqC,kBAAkB;+CAIjClC,mBAAA,CA4DM,OA5DNsC,WA4DM,GA3DJvC,mBAAA,eAAkB,E,CACNF,IAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CA+CM,OA/CN4C,WA+CM,GA9CJvC,mBAAA,CA6BM,OA7BNwC,WA6BM,I,kBA5BJ7C,mBAAA,CA2BM8C,SAAA,QAAAC,WAAA,CA3BgB7C,IAAA,CAAA8C,OAAO,YAAjBC,MAAM;yBAAlBjD,mBAAA,CA2BM;MA3B0BkD,GAAG,EAAED,MAAM,CAACE,EAAE;MAAE,SAAM;QACpD9C,mBAAA,CAGM,OAHN+C,WAGM,GAFJ/C,mBAAA,CAAmF;MAA7EgD,GAAG,EAAEJ,MAAM,CAACK,MAAM;MAAmCC,GAAG,EAAEN,MAAM,CAAC7B;0CACvEf,mBAAA,CAA2D;MAAtD,SAAKJ,eAAA,EAAC,kBAAkB,EAASgD,MAAM,CAACrC,MAAM;+BAErDP,mBAAA,CAqBM,OArBNmD,WAqBM,GApBJnD,mBAAA,CAA+E,OAA/EoD,WAA+E,EAAAhD,gBAAA,CAAhDwC,MAAM,CAACS,aAAa,IAAIT,MAAM,CAAC7B,QAAQ,kBACtEf,mBAAA,CAEM;MAFD,SAAKJ,eAAA,EAAC,gBAAgB;QAAA,MAAiBgD,MAAM,CAACU;MAAI;wBAClDV,MAAM,CAACU,IAAI,uCAEmBV,MAAM,CAACW,KAAK,I,cAA/C5D,mBAAA,CAeM,OAfN6D,WAeM,GAdJxD,mBAAA,CAMM,OANNyD,WAMM,G,4BALJzD,mBAAA,CAAmC;MAA7B,SAAM;IAAY,GAAC,KAAG,qBAC5BA,mBAAA,CAEM,OAFN0D,WAEM,GADJ1D,mBAAA,CAAiH;MAA5G,SAAM,cAAc;MAAE2D,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWhE,IAAA,CAAAiE,iBAAiB,CAAClB,MAAM,CAACW,KAAK,CAACQ,EAAE,EAAEnB,MAAM,CAACW,KAAK,CAACS,KAAK;MAAA;+BAElGhE,mBAAA,CAA8E,QAA9EiE,WAA8E,EAAA7D,gBAAA,CAAlDwC,MAAM,CAACW,KAAK,CAACQ,EAAE,IAAG,GAAC,GAAA3D,gBAAA,CAAGwC,MAAM,CAACW,KAAK,CAACS,KAAK,iB,GAEtEhE,mBAAA,CAMM,OANNkE,WAMM,G,4BALJlE,mBAAA,CAAoC;MAA9B,SAAM;IAAY,GAAC,MAAI,qBAC7BA,mBAAA,CAEM,OAFNmE,WAEM,GADJnE,mBAAA,CAAoH;MAA/G,SAAM,eAAe;MAAE2D,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAWhE,IAAA,CAAAiE,iBAAiB,CAAClB,MAAM,CAACW,KAAK,CAACa,GAAG,EAAExB,MAAM,CAACW,KAAK,CAACc,MAAM;MAAA;+BAErGrE,mBAAA,CAAgF,QAAhFsE,WAAgF,EAAAlE,gBAAA,CAApDwC,MAAM,CAACW,KAAK,CAACa,GAAG,IAAG,GAAC,GAAAhE,gBAAA,CAAGwC,MAAM,CAACW,KAAK,CAACc,MAAM,iB;oCAOhFtE,mBAAA,UAAa,EACbC,mBAAA,CAaM,OAbNuE,WAaM,GAZJvE,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA2E,kBAAA,IAAA3E,IAAA,CAAA2E,kBAAA,CAAA7C,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAkB;IAAA;IAAE,SAAM;kCACxC5B,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2BACzBA,mBAAA,CAAgB,cAAV,KAAG,mB,IAEXA,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA4E,cAAA,IAAA5E,IAAA,CAAA4E,cAAA,CAAA9C,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAc;IAAA;IAAE,SAAM;kCACpC5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAe,cAAT,IAAE,mB,IAEVA,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA6E,aAAA,IAAA7E,IAAA,CAAA6E,aAAA,CAAA/C,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAa;IAAA;IAAE,SAAM;kCACnC5B,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAAe,cAAT,IAAE,mB,6CAKdD,mBAAA,eAAkB,EACPF,IAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAOM,OAPNgF,WAOM,GANJC,YAAA,CAKEC,4BAAA;IAJC,kBAAgB,EAAE,EAAAC,gBAAA,GAAAjF,IAAA,CAAAwB,UAAU,cAAAyD,gBAAA,uBAAVA,gBAAA,CAAYC,eAAe;IAC7C,eAAa,EAAE,EAAAC,gBAAA,GAAAnF,IAAA,CAAAwB,UAAU,cAAA2D,gBAAA,uBAAVA,gBAAA,CAAY1D,YAAY;IACvC,cAAY,EAAE,EAAA2D,gBAAA,GAAApF,IAAA,CAAAwB,UAAU,cAAA4D,gBAAA,uBAAVA,gBAAA,CAAYC,WAAW;IACrCC,qBAAoB,EAAEtF,IAAA,CAAAuF;qLAzDOvF,IAAA,CAAAqC,kBAAkB,E,oBA8D5DnC,mBAAA,gBAAmB,EACfC,mBAAA,CA8HM,OA9HNqF,WA8HM,GA7HJtF,mBAAA,gBAAmB,E,CACPF,IAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAYM,OAZN2F,WAYM,I,kBAXJ3F,mBAAA,CAUS8C,SAAA,QAAAC,WAAA,CATO7C,IAAA,CAAA0F,SAAS,YAAhBC,GAAG;yBADZ7F,mBAAA,CAUS;MARNkD,GAAG,EAAE2C,GAAG,CAAC1C,EAAE;MACXrB,OAAK,WAALA,OAAKA,CAAAgE,MAAA;QAAA,OAAE5F,IAAA,CAAA6F,cAAc,GAAGF,GAAG,CAAC1C,EAAE;MAAA;MAC/B,SAAKlD,eAAA,EAAC,WAAW;QAAA,UACGC,IAAA,CAAA6F,cAAc,KAAKF,GAAG,CAAC1C;MAAE;QAE7C9C,mBAAA,CAAyB;MAArB,SAAKJ,eAAA,CAAE4F,GAAG,CAACG,IAAI;6BACnB3F,mBAAA,CAA2B,cAAAI,gBAAA,CAAlBoF,GAAG,CAAClF,IAAI,kBACLkF,GAAG,CAACI,KAAK,I,cAArBjG,mBAAA,CAA+D,QAA/DkG,WAA+D,EAAAzF,gBAAA,CAAnBoF,GAAG,CAACI,KAAK,oB;yEAIzD5F,mBAAA,CAiGM,OAjGN8F,WAiGM,GAhGJ/F,mBAAA,eAAkB,E,CACNF,IAAA,CAAAC,YAAY,IAAID,IAAA,CAAA6F,cAAc,c,cAA1C/F,mBAAA,CAYM,OAZNoG,WAYM,GAXJ/F,mBAAA,CAUM,OAVNgG,WAUM,GATJhG,mBAAA,CAQM,OARNiG,WAQM,G,4BAPJjG,mBAAA,CAA0B;IAAvB,SAAM;EAAY,4B,4BACrBA,mBAAA,CAAa,YAAT,MAAI,qB,4BACRA,mBAAA,CAAoB,WAAjB,eAAa,qBACFH,IAAA,CAAA2B,QAAQ,I,cAAtB7B,mBAAA,CAGS;;IAHgB8B,OAAK,EAAAf,MAAA,QAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAqG,SAAA,IAAArG,IAAA,CAAAqG,SAAA,CAAAvE,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAS;IAAA;IAAE,SAAM;kCAC/C5B,mBAAA,CAA6B;IAA1B,SAAM;EAAe,2BACxBA,mBAAA,CAAiB,cAAX,MAAI,mB,oFAMlBD,mBAAA,cAAiB,EACNF,IAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAqBM,OArBNwG,WAqBM,GApBJvB,YAAA,CAmBEwB,0BAAA;IAlBCC,UAAU,EAAE,EAAAC,gBAAA,GAAAzG,IAAA,CAAAwB,UAAU,cAAAiF,gBAAA,gBAAAA,gBAAA,GAAVA,gBAAA,CAAYC,YAAY,cAAAD,gBAAA,uBAAxBA,gBAAA,CAA0BE,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACC,QAAQ;IAAA;IAC5DC,QAAQ,EAAE,EAAAC,gBAAA,GAAA/G,IAAA,CAAAwB,UAAU,cAAAuF,gBAAA,gBAAAA,gBAAA,GAAVA,gBAAA,CAAYL,YAAY,cAAAK,gBAAA,uBAAxBA,gBAAA,CAA0BJ,MAAM,CAAC,UAAAC,CAAC;MAAA,QAAKA,CAAC,CAACC,QAAQ;IAAA;IAC3DG,SAAS,EAAE,EAAAC,gBAAA,GAAAjH,IAAA,CAAAwB,UAAU,cAAAyF,gBAAA,uBAAVA,gBAAA,CAAYD,SAAS;IAChC,cAAY,EAAE,EAAAE,gBAAA,GAAAlH,IAAA,CAAAwB,UAAU,cAAA0F,gBAAA,uBAAVA,gBAAA,CAAYC,WAAW;IACrC,eAAa,EAAE,EAAAC,gBAAA,GAAApH,IAAA,CAAAwB,UAAU,cAAA4F,gBAAA,uBAAVA,gBAAA,CAAY3F,YAAY;IACvC,wBAAsB,EAAEzB,IAAA,CAAAqH,uBAAuB;IAC/C,WAAS,EAAErH,IAAA,CAAA2B,QAAQ;IACnB,mBAAiB,EAAE,EAAA2F,gBAAA,GAAAtH,IAAA,CAAAwB,UAAU,cAAA8F,gBAAA,gBAAAA,gBAAA,GAAVA,gBAAA,CAAYC,eAAe,cAAAD,gBAAA,uBAA3BA,gBAAA,CAA6BtD,KAAK;IACrD,oBAAkB,EAAE,EAAAwD,gBAAA,GAAAxH,IAAA,CAAAwB,UAAU,cAAAgG,gBAAA,gBAAAA,gBAAA,GAAVA,gBAAA,CAAYD,eAAe,cAAAC,gBAAA,uBAA3BA,gBAAA,CAA6BC,MAAM;IACvDC,mBAAkB,EAAE1H,IAAA,CAAA2H,uBAAuB;IAC3CC,iBAAgB,EAAE5H,IAAA,CAAA6H,qBAAqB;IACvCC,gBAAe,EAAE9H,IAAA,CAAA+H,oBAAoB;IACrCC,cAAa,EAAEhI,IAAA,CAAAiI,kBAAkB;IACjCC,iBAAgB,EAAElI,IAAA,CAAAmI,qBAAqB;IACvCC,eAAc,EAAEpI,IAAA,CAAAqI,mBAAmB;IACnCC,kBAAiB,EAAEtI,IAAA,CAAAuI,sBAAsB;IACzCC,gBAAe,EAAExI,IAAA,CAAAyI,oBAAoB;IACrCC,YAAW,EAAE1I,IAAA,CAAA2I;kZAIlBzI,mBAAA,eAAkB,E,CACNF,IAAA,CAAAC,YAAY,IAAID,IAAA,CAAA6F,cAAc,gB,cAA1C/F,mBAAA,CAeM,OAfN8I,WAeM,GAdJzI,mBAAA,CAMM,OANN0I,WAMM,G,4BALJ1I,mBAAA,CAAY,YAAR,KAAG,qBACOH,IAAA,CAAA2B,QAAQ,I,cAAtB7B,mBAAA,CAGS;;IAHgB8B,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA8I,OAAA,IAAA9I,IAAA,CAAA8I,OAAA,CAAAhH,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAO;IAAA;IAAE,SAAM;kCAC7C5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,2CAGd4E,YAAA,CAMEgE,oBAAA;IALCC,KAAK,EAAEhJ,IAAA,CAAAgJ,KAAK;IACZ,UAAQ,EAAEhJ,IAAA,CAAA2B,QAAQ;IAClBsH,SAAQ,EAAEjJ,IAAA,CAAA8I,OAAO;IACjBI,YAAW,EAAElJ,IAAA,CAAAmJ,UAAU;IACvBC,YAAW,EAAEpJ,IAAA,CAAAqJ;uIAINrJ,IAAA,CAAAC,YAAY,IAAID,IAAA,CAAA6F,cAAc,gB,cAA1C/F,mBAAA,CAsCM,OAtCNwJ,WAsCM,GArCJnJ,mBAAA,CAaM,OAbNoJ,WAaM,G,4BAZJpJ,mBAAA,CAAY,YAAR,KAAG,qBACPA,mBAAA,CAUM,OAVNqJ,WAUM,GATJrJ,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAyJ,OAAA,IAAAzJ,IAAA,CAAAyJ,OAAA,CAAA3H,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAO;IAAA;IAAE,SAAM;kCAC7B5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,oBAEZA,mBAAA,CAIS;;aAJQH,IAAA,CAAA0J,UAAU,GAAA9D,MAAA;IAAA;IAAE,SAAM;kCACjCzF,mBAAA,CAAiC;IAAzBwJ,KAAK,EAAC;EAAK,GAAC,MAAI,oBACxBxJ,mBAAA,CAAsC;IAA9BwJ,KAAK,EAAC;EAAU,GAAC,MAAI,oBAC7BxJ,mBAAA,CAAoC;IAA5BwJ,KAAK,EAAC;EAAQ,GAAC,MAAI,mB,2CAHZ3J,IAAA,CAAA0J,UAAU,E,OAO/BvJ,mBAAA,CAsBM,OAtBNyJ,WAsBM,I,kBArBJ9J,mBAAA,CAoBM8C,SAAA,QAAAC,WAAA,CApBc7C,IAAA,CAAA6J,aAAa,YAArBC,IAAI;yBAAhBhK,mBAAA,CAoBM;MApB8BkD,GAAG,EAAE8G,IAAI,CAAC7G,EAAE;MAAE,SAAM;QACtD9C,mBAAA,CAMM,OANN4J,WAMM,GALJ5J,mBAAA,CAA4C,MAA5C6J,WAA4C,EAAAzJ,gBAAA,CAAlBuJ,IAAI,CAAC9H,KAAK,kBACpC7B,mBAAA,CAGM,OAHN8J,WAGM,GAFJ9J,mBAAA,CAAkD,QAAlD+J,WAAkD,EAAA3J,gBAAA,CAArBuJ,IAAI,CAACK,MAAM,kBACxChK,mBAAA,CAA+D,QAA/DiK,WAA+D,EAAA7J,gBAAA,CAApCP,IAAA,CAAAqK,UAAU,CAACP,IAAI,CAACQ,SAAS,kB,KAGxDnK,mBAAA,CAAkD,OAAlDoK,WAAkD,EAAAhK,gBAAA,CAArBuJ,IAAI,CAACU,OAAO,kBACzCrK,mBAAA,CAUM,OAVNsK,WAUM,GATJtK,mBAAA,CAES;MAFAyB,OAAK,WAALA,OAAKA,CAAAgE,MAAA;QAAA,OAAE5F,IAAA,CAAA0K,QAAQ,CAACZ,IAAI;MAAA;MAAG,SAAM;uDACpC3J,mBAAA,CAA2B;MAAxB,SAAM;IAAa,0B,iCAExBA,mBAAA,CAES;MAFAyB,OAAK,WAALA,OAAKA,CAAAgE,MAAA;QAAA,OAAE5F,IAAA,CAAA2K,SAAS,CAACb,IAAI;MAAA;MAAG,SAAM;uDACrC3J,mBAAA,CAA4B;MAAzB,SAAM;IAAc,0B,iCAEzBA,mBAAA,CAES;MAFAyB,OAAK,WAALA,OAAKA,CAAAgE,MAAA;QAAA,OAAE5F,IAAA,CAAA4K,UAAU,CAACd,IAAI;MAAA;MAAG,SAAM;uDACtC3J,mBAAA,CAA4B;MAAzB,SAAM;IAAc,0B;6EAQnCD,mBAAA,2BAA8B,EACnBF,IAAA,CAAA2B,QAAQ,KAAK3B,IAAA,CAAAC,YAAY,I,cAApCH,mBAAA,CASM,OATN+K,WASM,GARJ9F,YAAA,CAOE+F,4BAAA;IANC,WAAS,EAAE9K,IAAA,CAAA2B,QAAQ;IACnB,WAAS,EAAE3B,IAAA,CAAAQ,QAAQ;IACnBsC,OAAO,EAAE9C,IAAA,CAAA8C,OAAO;IAChBiI,eAAc,EAAE/K,IAAA,CAAAgL,iBAAiB;IACjCC,aAAY,EAAEjL,IAAA,CAAAkL,eAAe;IAC7BC,kBAAiB,EAAEnL,IAAA,CAAAoL;qKAK1BlL,mBAAA,oBAAuB,EACvBC,mBAAA,CAgDM;IAhDD,SAAKJ,eAAA,EAAC,aAAa;MAAA,aAAwBC,IAAA,CAAAqL;IAAmB;MACjElL,mBAAA,CAwBM,OAxBNmL,WAwBM,GAvBJnL,mBAAA,CAGM,OAHNoL,WAGM,GAFJpL,mBAAA,CAAmE;IAA/D,SAAKJ,eAAA,CAAEC,IAAA,CAAAC,YAAY;2BACvBE,mBAAA,CAA+C,cAAAI,gBAAA,CAAtCP,IAAA,CAAAC,YAAY,iC,IAEXD,IAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAeM,OAfN0L,WAeM,GAdJrL,mBAAA,CAMS;IALNyB,OAAK,EAAAf,MAAA,SAAAA,MAAA,iBAAA+E,MAAA;MAAA,OAAE5F,IAAA,CAAAyL,QAAQ;IAAA;IAChB,SAAK1L,eAAA,EAAC,eAAe;MAAA,UACDC,IAAA,CAAAyL,QAAQ;IAAA;KAC7B,MAED,kBACAtL,mBAAA,CAMS;IALNyB,OAAK,EAAAf,MAAA,SAAAA,MAAA,iBAAA+E,MAAA;MAAA,OAAE5F,IAAA,CAAAyL,QAAQ;IAAA;IAChB,SAAK1L,eAAA,EAAC,eAAe;MAAA,UACDC,IAAA,CAAAyL,QAAQ;IAAA;KAC7B,OAED,iB,wCAEFtL,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA0L,gBAAA,IAAA1L,IAAA,CAAA0L,gBAAA,CAAA5J,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAgB;IAAA;IAAE,SAAM;MACtC5B,mBAAA,CAAqF;IAAjF,SAAKJ,eAAA,CAAEC,IAAA,CAAAqL,mBAAmB;+CAIlClL,mBAAA,CAoBM,OApBNwL,WAoBM,GAnBJzL,mBAAA,eAAkB,E,CACNF,IAAA,CAAAC,YAAY,I,cAAxBH,mBAAA,CAQM,OARN8L,WAQM,GAPJ7G,YAAA,CAME8G,kBAAA;IALCC,QAAQ,EAAE9L,IAAA,CAAA8L,QAAQ;IAClB,cAAY,EAAE9L,IAAA,CAAA+L,WAAW;IACzB,WAAS,EAAE/L,IAAA,CAAAyL,QAAQ;IACnBO,aAAY,EAAEhM,IAAA,CAAAiM,WAAW;IACzBC,UAAS,EAAElM,IAAA,CAAAmM;4IAIhBjM,mBAAA,cAAiB,EACNF,IAAA,CAAAC,YAAY,I,cAAvBH,mBAAA,CAMM,OANNsM,WAMM,GALJrH,YAAA,CAIEsH,oBAAA;IAHC,aAAW,EAAErM,IAAA,CAAAsM,UAAU;IACvB,eAAa,EAAE,EAAAC,iBAAA,GAAAvM,IAAA,CAAAwB,UAAU,cAAA+K,iBAAA,uBAAVA,iBAAA,CAAY9K,YAAY;IACvC+K,WAAU,EAAExM,IAAA,CAAAyM;sJAjBiBzM,IAAA,CAAAqL,mBAAmB,E,qCAuB7DnL,mBAAA,WAAc,EACZC,mBAAA,CAqDM;IArDD,SAAKJ,eAAA,EAAC,gBAAgB;MAAA,kBAA6BC,IAAA,CAAAC;IAAY;MAClEE,mBAAA,CAYM,OAZNuM,WAYM,GAXJvM,mBAAA,CAUM,OAVNwM,WAUM,GATJxM,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA4M,UAAA,IAAA5M,IAAA,CAAA4M,UAAA,CAAA9K,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAU;IAAA;IAAE,SAAKhC,eAAA,EAAC,WAAW;MAAA,SAAoBC,IAAA,CAAA6M;IAAO;MACtE1M,mBAAA,CAA0E;IAAtE,SAAKJ,eAAA,CAAEC,IAAA,CAAA6M,OAAO;4CAEpB1M,mBAAA,CAES;IAFAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA8M,YAAA,IAAA9M,IAAA,CAAA8M,YAAA,CAAAhL,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAY;IAAA;IAAE,SAAKhC,eAAA,EAAC,WAAW;MAAA,YAAuBC,IAAA,CAAA+M;IAAU;MAC9E5M,mBAAA,CAAuE;IAAnE,SAAKJ,eAAA,CAAEC,IAAA,CAAA+M,UAAU;4CAEvB5M,mBAAA,CAEM,OAFN6M,WAEM,G,gBADJ7M,mBAAA,CAA+E;IAAxE8M,IAAI,EAAC,OAAO;;aAAUjN,IAAA,CAAAkN,MAAM,GAAAtH,MAAA;IAAA;IAAEuH,GAAG,EAAC,GAAG;IAACC,GAAG,EAAC,KAAK;IAAC,SAAM;iDAAhCpN,IAAA,CAAAkN,MAAM,E,SAKzC/M,mBAAA,CA4BM,OA5BNkN,WA4BM,GA3BJnN,mBAAA,eAAkB,E,CACDF,IAAA,CAAAC,YAAY,I,cAA7BH,mBAAA,CASW8C,SAAA;IAAAI,GAAA;EAAA,IART7C,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAsN,SAAA,IAAAtN,IAAA,CAAAsN,SAAA,CAAAxL,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAS;IAAA;IAAE,SAAM;kCAC/B5B,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAAiB,cAAX,MAAI,mB,IAEZA,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA4E,cAAA,IAAA5E,IAAA,CAAA4E,cAAA,CAAA9C,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAc;IAAA;IAAE,SAAM;kCACpC5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,qEAIdD,mBAAA,cAAiB,EACDF,IAAA,CAAAC,YAAY,I,cAA5BH,mBAAA,CAaW8C,SAAA;IAAAI,GAAA;EAAA,IAZT7C,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAuN,QAAA,IAAAvN,IAAA,CAAAuN,QAAA,CAAAzL,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAQ;IAAA;IAAE,SAAM,qBAAqB;IAAEyL,QAAQ,GAAGxN,IAAA,CAAA2B;kCAChExB,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,2BAC9BA,mBAAA,CAAiB,cAAX,MAAI,mB,gCAEZA,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAAyN,iBAAA,IAAAzN,IAAA,CAAAyN,iBAAA,CAAA3L,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAiB;IAAA;IAAE,SAAM;kCACvC5B,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,2BAC7BA,mBAAA,CAAiB,cAAX,MAAI,mB,IAEZA,mBAAA,CAGS;IAHAyB,OAAK,EAAAf,MAAA,SAAAA,MAAA;MAAA,OAAEb,IAAA,CAAA4E,cAAA,IAAA5E,IAAA,CAAA4E,cAAA,CAAA9C,KAAA,CAAA9B,IAAA,EAAA+B,SAAA,CAAc;IAAA;IAAE,SAAM;kCACpC5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAe,cAAT,IAAE,mB,uEAKdA,mBAAA,CAOM,OAPNuN,WAOM,GANJvN,mBAAA,CAKM,OALNwN,WAKM,GAJJxN,mBAAA,CAEM;IAFD,SAAKJ,eAAA,EAAC,kBAAkB,EAASC,IAAA,CAAA4N,gBAAgB;MACpDzN,mBAAA,CAAoC;IAAhC,SAAKJ,eAAA,CAAEC,IAAA,CAAA6N,iBAAiB;4CAE9B1N,mBAAA,CAA0D,QAA1D2N,WAA0D,EAAAvN,gBAAA,CAA7BP,IAAA,CAAA+N,iBAAiB,mB,sBAKpD7N,mBAAA,UAAa,EACbA,mBAAA,UAAa,EAELF,IAAA,CAAAgO,cAAc,I,cADtBC,YAAA,CAKEC,qBAAA;;IAHCC,OAAK,EAAAtN,MAAA,SAAAA,MAAA,iBAAA+E,MAAA;MAAA,OAAE5F,IAAA,CAAAgO,cAAc;IAAA;IACrBI,MAAI,EAAEpO,IAAA,CAAAmM,cAAc;IACpBkC,YAAW,EAAErO,IAAA,CAAAsO;4FAGhBpO,mBAAA,oBAAuB,EAEfF,IAAA,CAAAC,YAAY,KAAKD,IAAA,CAAA2B,QAAQ,IAAI3B,IAAA,CAAAuO,oBAAoB,I,cADzDN,YAAA,CASEO,2BAAA;;IAPC,WAAS,EAAExO,IAAA,CAAAC,YAAY;IACvB,WAAS,EAAED,IAAA,CAAA2B,QAAQ;IACnB,aAAW,EAAE3B,IAAA,CAAAwB,UAAU;IACvBiN,SAAS,EAAEzO,IAAA,CAAA0O,sBAAsB;IACjCC,iBAAgB,EAAE3O,IAAA,CAAA4O,wBAAwB;IAC1CC,mBAAmB,EAAE7O,IAAA,CAAA6O,mBAAmB;IACxCC,kBAAkB,EAAE9O,IAAA,CAAA8O;0LAGvB5O,mBAAA,oBAAuB,EACZF,IAAA,CAAAC,YAAY,IAAID,IAAA,CAAA2B,QAAQ,I,cAAnC7B,mBAAA,CAWM,OAXNiP,WAWM,GAVJhK,YAAA,CASE+F,4BAAA;IARC,WAAS,EAAE9K,IAAA,CAAA2B,QAAQ;IACnB,aAAW,EAAE3B,IAAA,CAAAwB,UAAU;IACvBsB,OAAO,EAAE9C,IAAA,CAAA8C,OAAO;IAChBkM,cAAa,EAAEhP,IAAA,CAAAiP,wBAAwB;IACvCC,WAAU,EAAElP,IAAA,CAAAkL,eAAe;IAC3BiE,UAAS,EAAEnP,IAAA,CAAAoP,cAAc;IACzBC,gBAAe,EAAErP,IAAA,CAAAsP,oBAAoB;IACrCC,mBAAkB,EAAEvP,IAAA,CAAAwP;qMAIzBtP,mBAAA,cAAiB,EAETF,IAAA,CAAAyP,kBAAkB,I,cAD1BxB,YAAA,CAKEyB,iCAAA;;IAHCjB,SAAS,EAAEzO,IAAA,CAAA2P,yBAAyB;IACpCxB,OAAK,EAAAtN,MAAA,SAAAA,MAAA,iBAAA+E,MAAA;MAAA,OAAE5F,IAAA,CAAAyP,kBAAkB;IAAA;IACzBG,QAAM,EAAE5P,IAAA,CAAA6P;2FAGX3P,mBAAA,0BAA6B,EAErBF,IAAA,CAAA8P,gBAAgB,I,cADxB7B,YAAA,CAKEyB,iCAAA;;IAHCjB,SAAS,EAAEzO,IAAA,CAAA+P,uBAAuB;IAClC5B,OAAK,EAAAtN,MAAA,SAAAA,MAAA,iBAAA+E,MAAA;MAAA,OAAE5F,IAAA,CAAA8P,gBAAgB;IAAA;IACvBF,QAAM,EAAE5P,IAAA,CAAAgQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}