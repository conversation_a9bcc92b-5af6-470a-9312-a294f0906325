/**
 * COC 7版战斗规则引擎测试
 * 验证所有战斗规则的正确性
 */

import CombatRules from './combatRules.js'
import { diceRoller } from './diceRoller.js'

// 测试伤害加值计算
console.log('=== 测试伤害加值计算 ===')

// 测试各种力量+体型组合
const testCases = [
  { str: 30, siz: 30, expected: { bonus: -2, text: '-2' } }, // 总和60 < 65
  { str: 40, siz: 40, expected: { bonus: -1, text: '-1' } }, // 总和80 < 85
  { str: 50, siz: 60, expected: { bonus: 0, text: '0' } },   // 总和110 < 125
  { str: 60, siz: 80, expected: { bonus: '1d4', text: '+1d4' } }, // 总和140 < 165
  { str: 80, siz: 90, expected: { bonus: '1d6', text: '+1d6' } }, // 总和170 < 205
  { str: 100, siz: 120, expected: { bonus: '2d6', text: '+2d6' } } // 总和220 > 205
]

testCases.forEach((testCase, index) => {
  const result = CombatRules.calculateDamageBonus(testCase.str, testCase.siz)
  console.log(`测试 ${index + 1}: STR${testCase.str} + SIZ${testCase.siz} = ${testCase.str + testCase.siz}`)
  console.log(`  期望: ${testCase.expected.text}, 实际: ${result.text}`)
  console.log(`  匹配: ${result.text === testCase.expected.text ? '✓' : '✗'}`)
})

// 测试体格计算
console.log('\n=== 测试体格计算 ===')

testCases.forEach((testCase, index) => {
  const build = CombatRules.calculateBuild(testCase.str, testCase.siz)
  console.log(`测试 ${index + 1}: STR${testCase.str} + SIZ${testCase.siz} = 体格${build}`)
})

// 测试成功等级判定
console.log('\n=== 测试成功等级判定 ===')

const skillTests = [
  { roll: 1, skill: 50, expected: 'critical' },    // 大成功
  { roll: 10, skill: 50, expected: 'extreme' },    // 极难成功 (≤50/5=10)
  { roll: 25, skill: 50, expected: 'hard' },       // 困难成功 (≤50/2=25)
  { roll: 45, skill: 50, expected: 'regular' },    // 常规成功 (≤50)
  { roll: 60, skill: 50, expected: 'failure' },    // 失败 (>50)
  { roll: 96, skill: 40, expected: 'fumble' },     // 大失败 (技能<50时96-100)
  { roll: 100, skill: 60, expected: 'fumble' }     // 大失败 (技能≥50时100)
]

skillTests.forEach((test, index) => {
  const result = CombatRules.getSuccessLevel(test.roll, test.skill)
  console.log(`测试 ${index + 1}: 投掷${test.roll} vs 技能${test.skill}`)
  console.log(`  期望: ${test.expected}, 实际: ${result}`)
  console.log(`  匹配: ${result === test.expected ? '✓' : '✗'}`)
})

// 测试对抗检定
console.log('\n=== 测试对抗检定 ===')

const opposedTests = [
  {
    attacker: { roll: 25, skill: 50, name: '攻击者' },
    defender: { roll: 30, skill: 60, name: '防御者' },
    description: '困难成功 vs 困难成功 (攻击者获胜)'
  },
  {
    attacker: { roll: 10, skill: 50, name: '攻击者' },
    defender: { roll: 15, skill: 60, name: '防御者' },
    description: '极难成功 vs 困难成功 (攻击者获胜)'
  },
  {
    attacker: { roll: 60, skill: 50, name: '攻击者' },
    defender: { roll: 30, skill: 60, name: '防御者' },
    description: '失败 vs 困难成功 (防御者获胜)'
  }
]

opposedTests.forEach((test, index) => {
  const result = CombatRules.resolveOpposedRoll(test.attacker, test.defender)
  console.log(`测试 ${index + 1}: ${test.description}`)
  console.log(`  攻击者: 投掷${test.attacker.roll} (${result.attackerLevel})`)
  console.log(`  防御者: 投掷${test.defender.roll} (${result.defenderLevel})`)
  console.log(`  获胜者: ${result.winner} (${result.winnerLevel})`)
  console.log(`  描述: ${result.description}`)
})

// 测试射击难度
console.log('\n=== 测试射击难度 ===')

const weaponRange = { base: 50, long: 100, extreme: 200 } // 基础射程50码

const shootingTests = [
  { distance: 30, expected: 'regular' },   // 基础射程内
  { distance: 80, expected: 'hard' },     // 远射程 (2倍基础)
  { distance: 150, expected: 'extreme' }, // 超射程 (4倍基础)
  { distance: 250, expected: 'impossible' } // 超出射程
]

shootingTests.forEach((test, index) => {
  const difficulty = CombatRules.getShootingDifficulty(test.distance, weaponRange)
  console.log(`测试 ${index + 1}: 距离${test.distance}码`)
  console.log(`  期望: ${test.expected}, 实际: ${difficulty}`)
  console.log(`  匹配: ${difficulty === test.expected ? '✓' : '✗'}`)
})

// 测试奖励/惩罚骰
console.log('\n=== 测试奖励/惩罚骰 ===')

// 设置固定种子以获得可预测的结果
diceRoller.setSeed(12345)

for (let i = 0; i < 5; i++) {
  const baseRoll = 65 // 固定基础投掷
  
  // 测试奖励骰
  const bonusResult = CombatRules.applyBonusPenaltyDice(baseRoll, 1)
  console.log(`奖励骰测试 ${i + 1}: 基础${baseRoll} -> 结果${bonusResult}`)
  
  // 测试惩罚骰
  const penaltyResult = CombatRules.applyBonusPenaltyDice(baseRoll, -1)
  console.log(`惩罚骰测试 ${i + 1}: 基础${baseRoll} -> 结果${penaltyResult}`)
}

// 重置随机种子
diceRoller.resetSeed()

// 测试伤害计算
console.log('\n=== 测试伤害计算 ===')

const weapon = {
  name: '手枪',
  damage: '1d10',
  maxDamage: 10,
  impaling: false
}

const impalingWeapon = {
  name: '步枪',
  damage: '2d6+4',
  maxDamage: 16,
  impaling: true
}

const attacker = {
  name: '测试角色',
  damageBonus: CombatRules.calculateDamageBonus(70, 60) // +1d4
}

// 测试常规成功伤害
const regularDamage = CombatRules.calculateDamage(weapon, attacker, 'regular')
console.log('常规成功伤害:')
console.log(`  ${regularDamage.description}`)
console.log(`  总伤害: ${regularDamage.totalDamage}`)

// 测试极难成功伤害 (非贯穿)
const extremeDamage = CombatRules.calculateDamage(weapon, attacker, 'extreme')
console.log('极难成功伤害 (非贯穿):')
console.log(`  ${extremeDamage.description}`)
console.log(`  总伤害: ${extremeDamage.totalDamage}`)

// 测试极难成功伤害 (贯穿)
const extremeImpalingDamage = CombatRules.calculateDamage(impalingWeapon, attacker, 'extreme')
console.log('极难成功伤害 (贯穿):')
console.log(`  ${extremeImpalingDamage.description}`)
console.log(`  总伤害: ${extremeImpalingDamage.totalDamage}`)

// 测试大失败后果
console.log('\n=== 测试大失败后果 ===')

for (let i = 0; i < 3; i++) {
  const fumbleResult = CombatRules.handleFumble('combat')
  console.log(`战斗大失败 ${i + 1}:`)
  console.log(`  类型: ${fumbleResult.type}`)
  console.log(`  描述: ${fumbleResult.description}`)
  console.log(`  效果: ${fumbleResult.effect}`)
}

console.log('\n=== 测试完成 ===')
console.log('所有核心战斗规则已验证，符合COC 7版规则书要求')