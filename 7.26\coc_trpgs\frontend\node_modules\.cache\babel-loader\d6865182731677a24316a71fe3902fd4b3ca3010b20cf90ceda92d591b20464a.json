{"ast": null, "code": "import \"core-js/modules/es.symbol.js\";\nimport \"core-js/modules/es.symbol.description.js\";\nimport \"core-js/modules/es.array.slice.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, vModelSelect as _vModelSelect, withDirectives as _withDirectives, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, normalizeStyle as _normalizeStyle, withModifiers as _withModifiers } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"home-page\"\n};\nvar _hoisted_2 = {\n  \"class\": \"welcome-section\"\n};\nvar _hoisted_3 = {\n  \"class\": \"welcome-content\"\n};\nvar _hoisted_4 = {\n  \"class\": \"welcome-text\"\n};\nvar _hoisted_5 = {\n  \"class\": \"welcome-title\"\n};\nvar _hoisted_6 = {\n  key: 0\n};\nvar _hoisted_7 = {\n  key: 1\n};\nvar _hoisted_8 = {\n  \"class\": \"welcome-subtitle\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  \"class\": \"welcome-actions\"\n};\nvar _hoisted_10 = {\n  \"class\": \"main-content\"\n};\nvar _hoisted_11 = {\n  key: 0,\n  \"class\": \"quick-actions-section\"\n};\nvar _hoisted_12 = {\n  \"class\": \"quick-actions-grid\"\n};\nvar _hoisted_13 = [\"disabled\"];\nvar _hoisted_14 = {\n  \"class\": \"rooms-section\"\n};\nvar _hoisted_15 = {\n  \"class\": \"section-header\"\n};\nvar _hoisted_16 = {\n  \"class\": \"header-controls\"\n};\nvar _hoisted_17 = {\n  \"class\": \"filter-group\"\n};\nvar _hoisted_18 = {\n  key: 0,\n  value: \"my\"\n};\nvar _hoisted_19 = {\n  \"class\": \"view-controls\"\n};\nvar _hoisted_20 = [\"disabled\"];\nvar _hoisted_21 = {\n  key: 0,\n  \"class\": \"rooms-stats\"\n};\nvar _hoisted_22 = {\n  \"class\": \"stats-item\"\n};\nvar _hoisted_23 = {\n  \"class\": \"stats-number\"\n};\nvar _hoisted_24 = {\n  \"class\": \"stats-item\"\n};\nvar _hoisted_25 = {\n  \"class\": \"stats-number\"\n};\nvar _hoisted_26 = {\n  \"class\": \"stats-item\"\n};\nvar _hoisted_27 = {\n  \"class\": \"stats-number\"\n};\nvar _hoisted_28 = {\n  \"class\": \"rooms-container\"\n};\nvar _hoisted_29 = {\n  key: 0,\n  \"class\": \"room-grid\"\n};\nvar _hoisted_30 = [\"onClick\"];\nvar _hoisted_31 = {\n  \"class\": \"room-header\"\n};\nvar _hoisted_32 = {\n  \"class\": \"room-info\"\n};\nvar _hoisted_33 = {\n  \"class\": \"room-title\"\n};\nvar _hoisted_34 = {\n  \"class\": \"room-meta\"\n};\nvar _hoisted_35 = {\n  \"class\": \"room-creator\"\n};\nvar _hoisted_36 = {\n  \"class\": \"room-time\"\n};\nvar _hoisted_37 = {\n  \"class\": \"room-description\"\n};\nvar _hoisted_38 = {\n  key: 0,\n  \"class\": \"room-tags\"\n};\nvar _hoisted_39 = {\n  \"class\": \"room-footer\"\n};\nvar _hoisted_40 = {\n  \"class\": \"room-players\"\n};\nvar _hoisted_41 = {\n  \"class\": \"players-info\"\n};\nvar _hoisted_42 = {\n  \"class\": \"players-bar\"\n};\nvar _hoisted_43 = {\n  \"class\": \"room-actions\"\n};\nvar _hoisted_44 = [\"disabled\", \"onClick\"];\nvar _hoisted_45 = {\n  \"class\": \"room-list\"\n};\nvar _hoisted_46 = [\"onClick\"];\nvar _hoisted_47 = {\n  \"class\": \"room-list-info\"\n};\nvar _hoisted_48 = {\n  \"class\": \"room-list-header\"\n};\nvar _hoisted_49 = {\n  \"class\": \"room-list-title\"\n};\nvar _hoisted_50 = {\n  \"class\": \"room-list-description\"\n};\nvar _hoisted_51 = {\n  \"class\": \"room-list-meta\"\n};\nvar _hoisted_52 = {\n  \"class\": \"meta-item\"\n};\nvar _hoisted_53 = {\n  \"class\": \"meta-item\"\n};\nvar _hoisted_54 = {\n  \"class\": \"meta-item\"\n};\nvar _hoisted_55 = {\n  \"class\": \"room-list-actions\"\n};\nvar _hoisted_56 = [\"disabled\", \"onClick\"];\nvar _hoisted_57 = {\n  key: 2,\n  \"class\": \"empty-state\"\n};\nvar _hoisted_58 = {\n  \"class\": \"empty-title\"\n};\nvar _hoisted_59 = {\n  \"class\": \"empty-desc\"\n};\nvar _hoisted_60 = {\n  \"class\": \"empty-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _ctx$currentUser;\n  var _component_router_link = _resolveComponent(\"router-link\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 简化的欢迎区域 \"), _createElementVNode(\"section\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h1\", _hoisted_5, [_ctx.isAuthenticated ? (_openBlock(), _createElementBlock(\"span\", _hoisted_6, _toDisplayString($options.getGreeting()) + \"，\" + _toDisplayString(((_ctx$currentUser = _ctx.currentUser) === null || _ctx$currentUser === void 0 ? void 0 : _ctx$currentUser.username) || '探索者'), 1 /* TEXT */)) : (_openBlock(), _createElementBlock(\"span\", _hoisted_7, \"欢迎来到COC跑团\"))]), _createElementVNode(\"p\", _hoisted_8, _toDisplayString($options.getRandomSubtitle()), 1 /* TEXT */)]), _createCommentVNode(\" 未登录时显示登录按钮 \"), !_ctx.isAuthenticated ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_router_link, {\n    to: \"/login\",\n    \"class\": \"welcome-btn primary\"\n  }, {\n    \"default\": _withCtx(function () {\n      return _cache[9] || (_cache[9] = [_createElementVNode(\"i\", {\n        \"class\": \"fas fa-sign-in-alt\"\n      }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"立即登录\", -1 /* CACHED */)]);\n    }),\n    _: 1 /* STABLE */,\n    __: [9]\n  }), _createVNode(_component_router_link, {\n    to: \"/register\",\n    \"class\": \"welcome-btn secondary\"\n  }, {\n    \"default\": _withCtx(function () {\n      return _cache[10] || (_cache[10] = [_createElementVNode(\"i\", {\n        \"class\": \"fas fa-user-plus\"\n      }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"注册账号\", -1 /* CACHED */)]);\n    }),\n    _: 1 /* STABLE */,\n    __: [10]\n  })])) : _createCommentVNode(\"v-if\", true)])]), _createCommentVNode(\" 主要内容区域 \"), _createElementVNode(\"main\", _hoisted_10, [_createCommentVNode(\" 快速操作区域 \"), _ctx.isAuthenticated ? (_openBlock(), _createElementBlock(\"section\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"button\", {\n    \"class\": \"action-btn primary\",\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.createRoom && $options.createRoom.apply($options, arguments);\n    })\n  }, _cache[11] || (_cache[11] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"创建房间\", -1 /* CACHED */)])), _createElementVNode(\"button\", {\n    \"class\": \"action-btn\",\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.joinRandomRoom && $options.joinRandomRoom.apply($options, arguments);\n    }),\n    disabled: $options.availableRoomsCount === 0\n  }, _cache[12] || (_cache[12] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-random\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"随机加入\", -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_13), _createElementVNode(\"button\", {\n    \"class\": \"action-btn\",\n    onClick: _cache[2] || (_cache[2] = function () {\n      return $options.openCharacterManager && $options.openCharacterManager.apply($options, arguments);\n    })\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-user-friends\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"角色管理\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 房间列表区域 \"), _createElementVNode(\"section\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n    \"class\": \"header-left\"\n  }, [_createElementVNode(\"h2\", {\n    \"class\": \"section-title\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-door-open\"\n  }), _createElementVNode(\"span\", null, \"房间大厅\")]), _createElementVNode(\"p\", {\n    \"class\": \"section-subtitle\"\n  }, \"发现正在进行的游戏\")], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[18] || (_cache[18] = _createElementVNode(\"label\", {\n    \"class\": \"filter-label\"\n  }, \"筛选：\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"select\", {\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $data.roomFilter = $event;\n    }),\n    \"class\": \"filter-select\"\n  }, [_cache[14] || (_cache[14] = _createElementVNode(\"option\", {\n    value: \"all\"\n  }, \"全部房间\", -1 /* CACHED */)), _cache[15] || (_cache[15] = _createElementVNode(\"option\", {\n    value: \"public\"\n  }, \"公开房间\", -1 /* CACHED */)), _cache[16] || (_cache[16] = _createElementVNode(\"option\", {\n    value: \"private\"\n  }, \"私人房间\", -1 /* CACHED */)), _ctx.isAuthenticated ? (_openBlock(), _createElementBlock(\"option\", _hoisted_18, \"我的房间\")) : _createCommentVNode(\"v-if\", true), _cache[17] || (_cache[17] = _createElementVNode(\"option\", {\n    value: \"available\"\n  }, \"可加入\", -1 /* CACHED */))], 512 /* NEED_PATCH */), [[_vModelSelect, $data.roomFilter]])]), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function ($event) {\n      return $data.viewMode = 'grid';\n    }),\n    \"class\": _normalizeClass([\"view-btn\", {\n      'active': $data.viewMode === 'grid'\n    }]),\n    title: \"网格视图\"\n  }, _cache[19] || (_cache[19] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-th\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = function ($event) {\n      return $data.viewMode = 'list';\n    }),\n    \"class\": _normalizeClass([\"view-btn\", {\n      'active': $data.viewMode === 'list'\n    }]),\n    title: \"列表视图\"\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-list\"\n  }, null, -1 /* CACHED */)]), 2 /* CLASS */)]), _createElementVNode(\"button\", {\n    onClick: _cache[6] || (_cache[6] = function () {\n      return $options.refreshRooms && $options.refreshRooms.apply($options, arguments);\n    }),\n    \"class\": \"refresh-btn\",\n    disabled: $data.loading,\n    title: \"刷新房间列表\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass([\"fas fa-sync-alt\", {\n      'fa-spin': $data.loading\n    }])\n  }, null, 2 /* CLASS */)], 8 /* PROPS */, _hoisted_20)])]), _createCommentVNode(\" 房间统计 \"), $options.filteredRooms.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"span\", _hoisted_23, _toDisplayString($options.filteredRooms.length), 1 /* TEXT */), _cache[22] || (_cache[22] = _createElementVNode(\"span\", {\n    \"class\": \"stats-label\"\n  }, \"个房间\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"span\", _hoisted_25, _toDisplayString($options.availableRoomsCount), 1 /* TEXT */), _cache[23] || (_cache[23] = _createElementVNode(\"span\", {\n    \"class\": \"stats-label\"\n  }, \"可加入\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_26, [_createElementVNode(\"span\", _hoisted_27, _toDisplayString($options.activeRoomsCount), 1 /* TEXT */), _cache[24] || (_cache[24] = _createElementVNode(\"span\", {\n    \"class\": \"stats-label\"\n  }, \"进行中\", -1 /* CACHED */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 房间列表 \"), _createElementVNode(\"div\", _hoisted_28, [_createCommentVNode(\" 网格视图 \"), $data.viewMode === 'grid' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredRooms, function (room) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: room.id,\n      \"class\": _normalizeClass([\"room-card\", {\n        'full': room.current_players >= room.max_players\n      }]),\n      onClick: function onClick($event) {\n        return $options.joinRoom(room);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, [_createElementVNode(\"h3\", _hoisted_33, _toDisplayString(room.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"span\", _hoisted_35, \"by \" + _toDisplayString(room.creator_name || '未知'), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_36, _toDisplayString($options.formatTime(room.created_at)), 1 /* TEXT */)])]), _createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"room-status-badge\", room.status])\n    }, [_createElementVNode(\"i\", {\n      \"class\": _normalizeClass($options.getStatusIcon(room.status))\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString($options.getStatusText(room.status)), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"p\", _hoisted_37, _toDisplayString(room.description || '暂无描述'), 1 /* TEXT */), room.tags && room.tags.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_38, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(room.tags.slice(0, 3), function (tag) {\n      return _openBlock(), _createElementBlock(\"span\", {\n        key: tag,\n        \"class\": \"room-tag\"\n      }, _toDisplayString(tag), 1 /* TEXT */);\n    }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_cache[25] || (_cache[25] = _createElementVNode(\"i\", {\n      \"class\": \"fas fa-users\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(room.current_players) + \"/\" + _toDisplayString(room.max_players), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", {\n      \"class\": \"players-fill\",\n      style: _normalizeStyle({\n        width: room.current_players / room.max_players * 100 + '%'\n      })\n    }, null, 4 /* STYLE */)])]), _createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"button\", {\n      \"class\": \"join-btn\",\n      disabled: room.current_players >= room.max_players || !_ctx.isAuthenticated,\n      onClick: _withModifiers(function ($event) {\n        return $options.joinRoom(room);\n      }, [\"stop\"])\n    }, _toDisplayString(room.current_players >= room.max_players ? '已满' : '加入'), 9 /* TEXT, PROPS */, _hoisted_44)])])], 10 /* CLASS, PROPS */, _hoisted_30);\n  }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createCommentVNode(\" 列表视图 \"), _createElementVNode(\"div\", _hoisted_45, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($options.filteredRooms, function (room) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: room.id,\n      \"class\": _normalizeClass([\"room-list-item\", {\n        'full': room.current_players >= room.max_players\n      }]),\n      onClick: function onClick($event) {\n        return $options.joinRoom(room);\n      }\n    }, [_createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"h3\", _hoisted_49, _toDisplayString(room.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n      \"class\": _normalizeClass([\"room-list-status\", room.status])\n    }, [_createElementVNode(\"i\", {\n      \"class\": _normalizeClass($options.getStatusIcon(room.status))\n    }, null, 2 /* CLASS */), _createElementVNode(\"span\", null, _toDisplayString($options.getStatusText(room.status)), 1 /* TEXT */)], 2 /* CLASS */)]), _createElementVNode(\"p\", _hoisted_50, _toDisplayString(room.description || '暂无描述'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_51, [_createElementVNode(\"span\", _hoisted_52, [_cache[26] || (_cache[26] = _createElementVNode(\"i\", {\n      \"class\": \"fas fa-user\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(room.creator_name || '未知'), 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_53, [_cache[27] || (_cache[27] = _createElementVNode(\"i\", {\n      \"class\": \"fas fa-clock\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString($options.formatTime(room.created_at)), 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_54, [_cache[28] || (_cache[28] = _createElementVNode(\"i\", {\n      \"class\": \"fas fa-users\"\n    }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(room.current_players) + \"/\" + _toDisplayString(room.max_players), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_55, [_createElementVNode(\"button\", {\n      \"class\": \"join-btn\",\n      disabled: room.current_players >= room.max_players || !_ctx.isAuthenticated,\n      onClick: _withModifiers(function ($event) {\n        return $options.joinRoom(room);\n      }, [\"stop\"])\n    }, _toDisplayString(room.current_players >= room.max_players ? '已满' : '加入'), 9 /* TEXT, PROPS */, _hoisted_56)])], 10 /* CLASS, PROPS */, _hoisted_46);\n  }), 128 /* KEYED_FRAGMENT */))])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 空状态 \"), $options.filteredRooms.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_57, [_cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n    \"class\": \"empty-icon\"\n  }, [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-door-open\"\n  })], -1 /* CACHED */)), _createElementVNode(\"h3\", _hoisted_58, _toDisplayString($options.getEmptyStateTitle()), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_59, _toDisplayString($options.getEmptyStateDesc()), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_60, [_ctx.isAuthenticated ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[7] || (_cache[7] = function () {\n      return $options.createRoom && $options.createRoom.apply($options, arguments);\n    }),\n    \"class\": \"empty-action-btn primary\"\n  }, _cache[29] || (_cache[29] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"创建房间\", -1 /* CACHED */)]))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[8] || (_cache[8] = function () {\n      return $options.refreshRooms && $options.refreshRooms.apply($options, arguments);\n    }),\n    \"class\": \"empty-action-btn secondary\"\n  }, _cache[30] || (_cache[30] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-sync-alt\"\n  }, null, -1 /* CACHED */), _createElementVNode(\"span\", null, \"刷新列表\", -1 /* CACHED */)]))])])) : _createCommentVNode(\"v-if\", true)])])])]);\n}", "map": {"version": 3, "names": ["value", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_ctx", "isAuthenticated", "_hoisted_6", "_toDisplayString", "$options", "getGreeting", "_ctx$currentUser", "currentUser", "username", "_hoisted_7", "_hoisted_8", "getRandomSubtitle", "_hoisted_9", "_createVNode", "_component_router_link", "to", "_cache", "_hoisted_10", "_hoisted_11", "_hoisted_12", "onClick", "createRoom", "apply", "arguments", "joinRandomRoom", "disabled", "availableRoomsCount", "openCharacterManager", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "$data", "roomFilter", "$event", "_hoisted_18", "_hoisted_19", "viewMode", "_normalizeClass", "title", "refreshRooms", "loading", "filteredRooms", "length", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "activeRoomsCount", "_hoisted_28", "_hoisted_29", "_Fragment", "_renderList", "room", "key", "id", "current_players", "max_players", "joinRoom", "_hoisted_31", "_hoisted_32", "_hoisted_33", "name", "_hoisted_34", "_hoisted_35", "creator_name", "_hoisted_36", "formatTime", "created_at", "status", "getStatusIcon", "getStatusText", "_hoisted_37", "description", "tags", "_hoisted_38", "slice", "tag", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "style", "_normalizeStyle", "width", "_hoisted_43", "_withModifiers", "_hoisted_44", "_hoisted_45", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "_hoisted_51", "_hoisted_52", "_hoisted_53", "_hoisted_54", "_hoisted_55", "_hoisted_56", "_hoisted_57", "_hoisted_58", "getEmptyStateTitle", "_hoisted_59", "getEmptyStateDesc", "_hoisted_60"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-page\">\r\n    <!-- 简化的欢迎区域 -->\r\n    <section class=\"welcome-section\">\r\n      <div class=\"welcome-content\">\r\n        <div class=\"welcome-text\">\r\n          <h1 class=\"welcome-title\">\r\n            <span v-if=\"isAuthenticated\">{{ getGreeting() }}，{{ currentUser?.username || '探索者' }}</span>\r\n            <span v-else>欢迎来到COC跑团</span>\r\n          </h1>\r\n          <p class=\"welcome-subtitle\">{{ getRandomSubtitle() }}</p>\r\n        </div>\r\n        \r\n        <!-- 未登录时显示登录按钮 -->\r\n        <div class=\"welcome-actions\" v-if=\"!isAuthenticated\">\r\n          <router-link to=\"/login\" class=\"welcome-btn primary\">\r\n            <i class=\"fas fa-sign-in-alt\"></i>\r\n            <span>立即登录</span>\r\n          </router-link>\r\n          <router-link to=\"/register\" class=\"welcome-btn secondary\">\r\n            <i class=\"fas fa-user-plus\"></i>\r\n            <span>注册账号</span>\r\n          </router-link>\r\n        </div>\r\n      </div>\r\n    </section>\r\n\r\n    <!-- 主要内容区域 -->\r\n    <main class=\"main-content\">\r\n      <!-- 快速操作区域 -->\r\n      <section class=\"quick-actions-section\" v-if=\"isAuthenticated\">\r\n        <div class=\"quick-actions-grid\">\r\n          <button class=\"action-btn primary\" @click=\"createRoom\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>创建房间</span>\r\n          </button>\r\n          \r\n          <button class=\"action-btn\" @click=\"joinRandomRoom\" :disabled=\"availableRoomsCount === 0\">\r\n            <i class=\"fas fa-random\"></i>\r\n            <span>随机加入</span>\r\n          </button>\r\n          \r\n          <button class=\"action-btn\" @click=\"openCharacterManager\">\r\n            <i class=\"fas fa-user-friends\"></i>\r\n            <span>角色管理</span>\r\n          </button>\r\n        </div>\r\n      </section>\r\n\r\n      <!-- 房间列表区域 -->\r\n      <section class=\"rooms-section\">\r\n        <div class=\"section-header\">\r\n          <div class=\"header-left\">\r\n            <h2 class=\"section-title\">\r\n              <i class=\"fas fa-door-open\"></i>\r\n              <span>房间大厅</span>\r\n            </h2>\r\n            <p class=\"section-subtitle\">发现正在进行的游戏</p>\r\n          </div>\r\n          \r\n          <div class=\"header-controls\">\r\n            <div class=\"filter-group\">\r\n              <label class=\"filter-label\">筛选：</label>\r\n              <select v-model=\"roomFilter\" class=\"filter-select\">\r\n                <option value=\"all\">全部房间</option>\r\n                <option value=\"public\">公开房间</option>\r\n                <option value=\"private\">私人房间</option>\r\n                <option value=\"my\" v-if=\"isAuthenticated\">我的房间</option>\r\n                <option value=\"available\">可加入</option>\r\n              </select>\r\n            </div>\r\n            \r\n            <div class=\"view-controls\">\r\n              <button \r\n                @click=\"viewMode = 'grid'\" \r\n                class=\"view-btn\" \r\n                :class=\"{ 'active': viewMode === 'grid' }\"\r\n                title=\"网格视图\"\r\n              >\r\n                <i class=\"fas fa-th\"></i>\r\n              </button>\r\n              <button \r\n                @click=\"viewMode = 'list'\" \r\n                class=\"view-btn\" \r\n                :class=\"{ 'active': viewMode === 'list' }\"\r\n                title=\"列表视图\"\r\n              >\r\n                <i class=\"fas fa-list\"></i>\r\n              </button>\r\n            </div>\r\n            \r\n            <button @click=\"refreshRooms\" class=\"refresh-btn\" :disabled=\"loading\" title=\"刷新房间列表\">\r\n              <i class=\"fas fa-sync-alt\" :class=\"{ 'fa-spin': loading }\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 房间统计 -->\r\n        <div class=\"rooms-stats\" v-if=\"filteredRooms.length > 0\">\r\n          <div class=\"stats-item\">\r\n            <span class=\"stats-number\">{{ filteredRooms.length }}</span>\r\n            <span class=\"stats-label\">个房间</span>\r\n          </div>\r\n          <div class=\"stats-item\">\r\n            <span class=\"stats-number\">{{ availableRoomsCount }}</span>\r\n            <span class=\"stats-label\">可加入</span>\r\n          </div>\r\n          <div class=\"stats-item\">\r\n            <span class=\"stats-number\">{{ activeRoomsCount }}</span>\r\n            <span class=\"stats-label\">进行中</span>\r\n          </div>\r\n        </div>\r\n        \r\n        <!-- 房间列表 -->\r\n        <div class=\"rooms-container\">\r\n          <!-- 网格视图 -->\r\n          <div v-if=\"viewMode === 'grid'\" class=\"room-grid\">\r\n            <div \r\n              v-for=\"room in filteredRooms\" \r\n              :key=\"room.id\" \r\n              class=\"room-card\" \r\n              @click=\"joinRoom(room)\"\r\n              :class=\"{ 'full': room.current_players >= room.max_players }\"\r\n            >\r\n              <div class=\"room-header\">\r\n                <div class=\"room-info\">\r\n                  <h3 class=\"room-title\">{{ room.name }}</h3>\r\n                  <div class=\"room-meta\">\r\n                    <span class=\"room-creator\">by {{ room.creator_name || '未知' }}</span>\r\n                    <span class=\"room-time\">{{ formatTime(room.created_at) }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"room-status-badge\" :class=\"room.status\">\r\n                  <i :class=\"getStatusIcon(room.status)\"></i>\r\n                  <span>{{ getStatusText(room.status) }}</span>\r\n                </div>\r\n              </div>\r\n              \r\n              <p class=\"room-description\">{{ room.description || '暂无描述' }}</p>\r\n              \r\n              <div class=\"room-tags\" v-if=\"room.tags && room.tags.length > 0\">\r\n                <span v-for=\"tag in room.tags.slice(0, 3)\" :key=\"tag\" class=\"room-tag\">{{ tag }}</span>\r\n              </div>\r\n              \r\n              <div class=\"room-footer\">\r\n                <div class=\"room-players\">\r\n                  <div class=\"players-info\">\r\n                    <i class=\"fas fa-users\"></i>\r\n                    <span>{{ room.current_players }}/{{ room.max_players }}</span>\r\n                  </div>\r\n                  <div class=\"players-bar\">\r\n                    <div \r\n                      class=\"players-fill\" \r\n                      :style=\"{ width: (room.current_players / room.max_players * 100) + '%' }\"\r\n                    ></div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"room-actions\">\r\n                  <button \r\n                    class=\"join-btn\" \r\n                    :disabled=\"room.current_players >= room.max_players || !isAuthenticated\"\r\n                    @click.stop=\"joinRoom(room)\"\r\n                  >\r\n                    {{ room.current_players >= room.max_players ? '已满' : '加入' }}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 列表视图 -->\r\n          <div v-else class=\"room-list\">\r\n            <div \r\n              v-for=\"room in filteredRooms\" \r\n              :key=\"room.id\" \r\n              class=\"room-list-item\" \r\n              @click=\"joinRoom(room)\"\r\n              :class=\"{ 'full': room.current_players >= room.max_players }\"\r\n            >\r\n              <div class=\"room-list-info\">\r\n                <div class=\"room-list-header\">\r\n                  <h3 class=\"room-list-title\">{{ room.name }}</h3>\r\n                  <div class=\"room-list-status\" :class=\"room.status\">\r\n                    <i :class=\"getStatusIcon(room.status)\"></i>\r\n                    <span>{{ getStatusText(room.status) }}</span>\r\n                  </div>\r\n                </div>\r\n                <p class=\"room-list-description\">{{ room.description || '暂无描述' }}</p>\r\n                <div class=\"room-list-meta\">\r\n                  <span class=\"meta-item\">\r\n                    <i class=\"fas fa-user\"></i>\r\n                    <span>{{ room.creator_name || '未知' }}</span>\r\n                  </span>\r\n                  <span class=\"meta-item\">\r\n                    <i class=\"fas fa-clock\"></i>\r\n                    <span>{{ formatTime(room.created_at) }}</span>\r\n                  </span>\r\n                  <span class=\"meta-item\">\r\n                    <i class=\"fas fa-users\"></i>\r\n                    <span>{{ room.current_players }}/{{ room.max_players }}</span>\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              \r\n              <div class=\"room-list-actions\">\r\n                <button \r\n                  class=\"join-btn\" \r\n                  :disabled=\"room.current_players >= room.max_players || !isAuthenticated\"\r\n                  @click.stop=\"joinRoom(room)\"\r\n                >\r\n                  {{ room.current_players >= room.max_players ? '已满' : '加入' }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 空状态 -->\r\n          <div v-if=\"filteredRooms.length === 0\" class=\"empty-state\">\r\n            <div class=\"empty-icon\">\r\n              <i class=\"fas fa-door-open\"></i>\r\n            </div>\r\n            <h3 class=\"empty-title\">{{ getEmptyStateTitle() }}</h3>\r\n            <p class=\"empty-desc\">{{ getEmptyStateDesc() }}</p>\r\n            <div class=\"empty-actions\">\r\n              <button @click=\"createRoom\" class=\"empty-action-btn primary\" v-if=\"isAuthenticated\">\r\n                <i class=\"fas fa-plus\"></i>\r\n                <span>创建房间</span>\r\n              </button>\r\n              <button @click=\"refreshRooms\" class=\"empty-action-btn secondary\">\r\n                <i class=\"fas fa-sync-alt\"></i>\r\n                <span>刷新列表</span>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n\r\n    </main>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'Home',\r\n  mixins: [storageMixin],\r\n  data() {\r\n    return {\r\n      roomFilter: 'all',\r\n      viewMode: 'grid',\r\n      loading: false,\r\n      \r\n      // 副标题列表\r\n      subtitles: [\r\n        '准备开始新的冒险了吗？',\r\n        '黑暗中有什么在等待着你...',\r\n        '真相往往比想象更加恐怖',\r\n        '在未知的世界中寻找答案',\r\n        '每一次投骰都可能改变命运',\r\n        '克苏鲁的呼唤正在响起...'\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters('auth', ['currentUser', 'isAuthenticated']),\r\n    \r\n    filteredRooms() {\r\n      const rooms = this.$store.getters['rooms/rooms'] || []\r\n      switch (this.roomFilter) {\r\n        case 'public':\r\n          return rooms.filter(room => room.is_public)\r\n        case 'private':\r\n          return rooms.filter(room => !room.is_public)\r\n        case 'my':\r\n          return rooms.filter(room => room.creator_id === this.currentUser?.id)\r\n        case 'available':\r\n          return rooms.filter(room => \r\n            room.current_players < room.max_players && room.status === 'waiting'\r\n          )\r\n        default:\r\n          return rooms\r\n      }\r\n    },\r\n    \r\n    myCharacters() {\r\n      return this.$store.getters.userCharacters?.length || 0\r\n    },\r\n    \r\n    availableRoomsCount() {\r\n      return this.$store.getters['rooms/availableRooms'].length\r\n    },\r\n    \r\n    activeRoomsCount() {\r\n      const rooms = this.$store.getters['rooms/rooms'] || []\r\n      return rooms.filter(room => room.status === 'active').length\r\n    }\r\n  },\r\n  methods: {\r\n    // 问候语相关\r\n    getGreeting() {\r\n      const hour = new Date().getHours()\r\n      if (hour < 6) return '深夜好'\r\n      if (hour < 12) return '早上好'\r\n      if (hour < 18) return '下午好'\r\n      return '晚上好'\r\n    },\r\n    \r\n    getRandomSubtitle() {\r\n      return this.subtitles[Math.floor(Math.random() * this.subtitles.length)]\r\n    },\r\n    \r\n    // 导航方法\r\n    openCharacterManager() {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      this.$router.push('/characters')\r\n    },\r\n    \r\n\r\n    \r\n    // 房间操作\r\n    async createRoom() {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      this.$router.push('/create-room')\r\n    },\r\n    \r\n    async joinRandomRoom() {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      \r\n      const availableRooms = this.filteredRooms.filter(room => \r\n        room.current_players < room.max_players && room.status === 'waiting'\r\n      )\r\n      \r\n      if (availableRooms.length > 0) {\r\n        const randomRoom = availableRooms[Math.floor(Math.random() * availableRooms.length)]\r\n        this.joinRoom(randomRoom)\r\n      } else {\r\n        this.$message.info('暂无可加入的房间')\r\n      }\r\n    },\r\n    \r\n    async joinRoom(room) {\r\n      if (!this.isAuthenticated) {\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n      \r\n      if (room.current_players >= room.max_players) {\r\n        this.$message.warning('房间已满')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        // 这里可以添加加入房间的逻辑，比如检查权限等\r\n        // await this.$store.dispatch('joinRoom', room.id)\r\n        this.$router.push(`/room/${room.id}`)\r\n      } catch (error) {\r\n        console.error('加入房间失败:', error)\r\n        this.$message.error('加入房间失败')\r\n      }\r\n    },\r\n    \r\n    async refreshRooms() {\r\n      try {\r\n        this.loading = true\r\n        await this.$store.dispatch('rooms/loadRooms')\r\n        this.$message.success('房间列表已刷新')\r\n      } catch (error) {\r\n        console.error('刷新房间列表失败:', error)\r\n        this.$message.error('刷新失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 状态相关\r\n    getStatusIcon(status) {\r\n      const iconMap = {\r\n        active: 'fas fa-play',\r\n        waiting: 'fas fa-clock',\r\n        finished: 'fas fa-check',\r\n        paused: 'fas fa-pause'\r\n      }\r\n      return iconMap[status] || 'fas fa-question'\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const textMap = {\r\n        active: '进行中',\r\n        waiting: '等待中',\r\n        finished: '已结束',\r\n        paused: '已暂停'\r\n      }\r\n      return textMap[status] || '未知'\r\n    },\r\n    \r\n\r\n    \r\n    // 空状态\r\n    getEmptyStateTitle() {\r\n      switch (this.roomFilter) {\r\n        case 'public':\r\n          return '暂无公开房间'\r\n        case 'private':\r\n          return '暂无私人房间'\r\n        case 'my':\r\n          return '你还没有创建房间'\r\n        case 'available':\r\n          return '暂无可加入的房间'\r\n        default:\r\n          return '暂无房间'\r\n      }\r\n    },\r\n    \r\n    getEmptyStateDesc() {\r\n      switch (this.roomFilter) {\r\n        case 'public':\r\n          return '当前没有公开的游戏房间'\r\n        case 'private':\r\n          return '当前没有私人游戏房间'\r\n        case 'my':\r\n          return '创建一个新房间开始你的冒险吧！'\r\n        case 'available':\r\n          return '所有房间都已满员或正在进行中'\r\n        default:\r\n          return '创建一个新房间开始你的冒险吧！'\r\n      }\r\n    },\r\n    \r\n    // 工具方法\r\n    formatTime(time) {\r\n      if (!time) return '未知时间'\r\n      \r\n      const now = new Date()\r\n      const timeDate = new Date(time)\r\n      const diffMs = now - timeDate\r\n      const diffMins = Math.floor(diffMs / 60000)\r\n      const diffHours = Math.floor(diffMs / 3600000)\r\n      const diffDays = Math.floor(diffMs / 86400000)\r\n      \r\n      if (diffMins < 1) return '刚刚'\r\n      if (diffMins < 60) return `${diffMins}分钟前`\r\n      if (diffHours < 24) return `${diffHours}小时前`\r\n      if (diffDays < 7) return `${diffDays}天前`\r\n      \r\n      return timeDate.toLocaleDateString('zh-CN')\r\n    },\r\n    \r\n\r\n  },\r\n  \r\n  mounted() {\r\n    // 加载房间列表\r\n    this.refreshRooms()\r\n    \r\n    // 加载用户数据\r\n    if (this.isAuthenticated) {\r\n      this.$store.dispatch('loadUserCharacters')\r\n    }\r\n    \r\n    // 从本地存储恢复视图模式\r\n    const savedViewMode = this.safeGetItem('home-view-mode')\r\n    if (savedViewMode) {\r\n      this.viewMode = savedViewMode\r\n    }\r\n  },\r\n  \r\n  watch: {\r\n    viewMode(newMode) {\r\n      this.safeSetItem('home-view-mode', newMode)\r\n    },\r\n    \r\n    isAuthenticated(newVal) {\r\n      if (newVal) {\r\n        this.$store.dispatch('loadUserCharacters')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script><style\r\n scoped>\r\n/* ===== 页面基础样式 ===== */\r\n.home-page {\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  padding-bottom: var(--spacing-8);\r\n}\r\n\r\n/* ===== 简化的欢迎区域 ===== */\r\n.welcome-section {\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 50%, var(--success-400) 100%);\r\n  color: var(--text-inverse);\r\n  padding: var(--spacing-6) 0;\r\n  margin-bottom: var(--spacing-6);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.welcome-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>') repeat;\r\n  opacity: 0.3;\r\n}\r\n\r\n.welcome-content {\r\n  position: relative;\r\n  z-index: 1;\r\n  text-align: center;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--spacing-4);\r\n}\r\n\r\n.welcome-text {\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.welcome-title {\r\n  font-size: var(--font-size-3xl);\r\n  font-weight: var(--font-weight-bold);\r\n  margin-bottom: var(--spacing-2);\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.welcome-subtitle {\r\n  font-size: var(--font-size-lg);\r\n  opacity: 0.9;\r\n  margin: 0;\r\n  font-style: italic;\r\n}\r\n\r\n.welcome-actions {\r\n  display: flex;\r\n  gap: var(--spacing-3);\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.welcome-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-5);\r\n  border-radius: var(--radius-xl);\r\n  text-decoration: none;\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  border: 2px solid transparent;\r\n  min-width: 140px;\r\n  justify-content: center;\r\n}\r\n\r\n.welcome-btn.primary {\r\n  background: var(--bg-primary);\r\n  color: var(--success-600);\r\n  border-color: var(--bg-primary);\r\n}\r\n\r\n.welcome-btn.primary:hover {\r\n  background: var(--success-50);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-lg);\r\n  text-decoration: none;\r\n  color: var(--success-700);\r\n}\r\n\r\n.welcome-btn.secondary {\r\n  background: transparent;\r\n  color: var(--text-inverse);\r\n  border-color: rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.welcome-btn.secondary:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-2px);\r\n  text-decoration: none;\r\n  color: var(--text-inverse);\r\n}\r\n\r\n/* ===== 主要内容区域 ===== */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--spacing-4);\r\n}\r\n\r\n/* ===== 快速操作区域 ===== */\r\n.quick-actions-section {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.quick-actions-grid {\r\n  display: flex;\r\n  gap: var(--spacing-3);\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-4);\r\n  background: var(--success-50);\r\n  color: var(--success-700);\r\n  border: 2px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  transition: all var(--transition-fast);\r\n  cursor: pointer;\r\n  min-width: 140px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn:hover:not(:disabled) {\r\n  background: var(--success-100);\r\n  border-color: var(--success-300);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.action-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.action-btn.primary {\r\n  background: var(--success-600);\r\n  color: var(--text-inverse);\r\n  border-color: var(--success-600);\r\n}\r\n\r\n.action-btn.primary:hover:not(:disabled) {\r\n  background: var(--success-700);\r\n  border-color: var(--success-700);\r\n  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.5);\r\n}\r\n\r\n.welcome-btn.secondary:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.8);\r\n  transform: translateY(-2px);\r\n  text-decoration: none;\r\n  color: var(--text-inverse);\r\n}\r\n\r\n/* ===== 主要内容区域 ===== */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: 0 var(--spacing-4);\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-8);\r\n}\r\n\r\n/* ===== 房间列表区域 ===== */\r\n.rooms-section {\r\n  background: var(--bg-elevated);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--spacing-6);\r\n  box-shadow: var(--shadow-sm);\r\n  border: 1px solid var(--success-200);\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-6);\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-2);\r\n}\r\n\r\n.section-title i {\r\n  color: var(--success-500);\r\n}\r\n\r\n.section-subtitle {\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-base);\r\n  margin: 0;\r\n}\r\n\r\n\r\n\r\n.header-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.filter-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.filter-select {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  transition: all var(--transition-fast);\r\n  min-width: 120px;\r\n}\r\n\r\n.filter-select {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-primary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  transition: all var(--transition-fast);\r\n  min-width: 120px;\r\n}\r\n\r\n.filter-select:focus {\r\n  outline: none;\r\n  border-color: var(--success-400);\r\n  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.view-controls {\r\n  display: flex;\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-md);\r\n  overflow: hidden;\r\n}\r\n\r\n.view-btn {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: none;\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  border-right: 1px solid var(--success-200);\r\n}\r\n\r\n.view-btn:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.view-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n}\r\n\r\n.view-btn.active {\r\n  background: var(--success-500);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.refresh-btn {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  min-width: 44px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.refresh-btn:hover:not(:disabled) {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-400);\r\n}\r\n\r\n.refresh-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.refresh-btn .fa-spin {\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  from { transform: rotate(0deg); }\r\n  to { transform: rotate(360deg); }\r\n}\r\n\r\n/* ===== 房间统计 ===== */\r\n.rooms-stats {\r\n  display: flex;\r\n  gap: var(--spacing-6);\r\n  margin-bottom: var(--spacing-4);\r\n  padding: var(--spacing-4);\r\n  background: var(--success-50);\r\n  border-radius: var(--radius-lg);\r\n  border: 1px solid var(--success-200);\r\n}\r\n\r\n.stats-item {\r\n  display: flex;\r\n  align-items: baseline;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.stats-number {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--success-700);\r\n}\r\n\r\n.stats-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--success-600);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n/* ===== 房间网格视图 ===== */\r\n.room-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.room-card {\r\n  background: var(--bg-primary);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--spacing-5);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  box-shadow: var(--shadow-sm);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.05), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.room-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n  border-color: var(--success-400);\r\n}\r\n\r\n.room-card:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.room-card.full {\r\n  opacity: 0.7;\r\n}\r\n\r\n.room-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-3);\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.room-title {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-1);\r\n  line-height: 1.3;\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.room-creator {\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.room-status-badge {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  white-space: nowrap;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  flex-shrink: 0;\r\n}\r\n\r\n.room-status-badge.active {\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n}\r\n\r\n.room-status-badge.waiting {\r\n  background: var(--warning-100);\r\n  color: var(--warning-700);\r\n}\r\n\r\n.room-status-badge.finished {\r\n  background: var(--gray-100);\r\n  color: var(--gray-700);\r\n}\r\n\r\n.room-status-badge.paused {\r\n  background: var(--error-100);\r\n  color: var(--error-700);\r\n}\r\n\r\n.room-description {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin-bottom: var(--spacing-3);\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-tags {\r\n  display: flex;\r\n  gap: var(--spacing-1);\r\n  margin-bottom: var(--spacing-3);\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.room-tag {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n  border-radius: var(--radius-md);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.room-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-players {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.players-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--spacing-1);\r\n}\r\n\r\n.players-bar {\r\n  height: 4px;\r\n  background: var(--gray-200);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n}\r\n\r\n.players-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, var(--success-400), var(--success-500));\r\n  border-radius: var(--radius-full);\r\n  transition: width var(--transition-fast);\r\n}\r\n\r\n.room-actions {\r\n  flex-shrink: 0;\r\n}\r\n\r\n.join-btn {\r\n  padding: var(--spacing-2) var(--spacing-4);\r\n  border: 1px solid var(--success-400);\r\n  border-radius: var(--radius-md);\r\n  background: var(--success-500);\r\n  color: var(--text-inverse);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.join-btn:hover:not(:disabled) {\r\n  background: var(--success-600);\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.join-btn:disabled {\r\n  background: var(--gray-400);\r\n  border-color: var(--gray-400);\r\n  cursor: not-allowed;\r\n  opacity: 0.6;\r\n}\r\n\r\n/* ===== 房间列表视图 ===== */\r\n.room-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-list-item {\r\n  background: var(--bg-primary);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  padding: var(--spacing-4);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.room-list-item:hover {\r\n  border-color: var(--success-400);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.room-list-item.full {\r\n  opacity: 0.7;\r\n}\r\n\r\n.room-list-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.room-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-2);\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.room-list-title {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin: 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.room-list-status {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  white-space: nowrap;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.room-list-status.active {\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n}\r\n\r\n.room-list-status.waiting {\r\n  background: var(--warning-100);\r\n  color: var(--warning-700);\r\n}\r\n\r\n.room-list-description {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  margin-bottom: var(--spacing-2);\r\n  line-height: 1.5;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-list-meta {\r\n  display: flex;\r\n  gap: var(--spacing-4);\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.meta-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.room-list-actions {\r\n  flex-shrink: 0;\r\n}\r\n\r\n/* ===== 空状态 ===== */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: var(--spacing-8) var(--spacing-4);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.empty-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: var(--success-100);\r\n  border-radius: var(--radius-full);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--font-size-3xl);\r\n  margin: 0 auto var(--spacing-4);\r\n  color: var(--success-400);\r\n}\r\n\r\n.empty-title {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-secondary);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.empty-desc {\r\n  font-size: var(--font-size-base);\r\n  margin-bottom: var(--spacing-6);\r\n  max-width: 400px;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n}\r\n\r\n.empty-actions {\r\n  display: flex;\r\n  gap: var(--spacing-3);\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.empty-action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-5);\r\n  border-radius: var(--radius-lg);\r\n  font-weight: var(--font-weight-medium);\r\n  transition: all var(--transition-fast);\r\n  cursor: pointer;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.empty-action-btn.primary {\r\n  background: var(--success-500);\r\n  color: var(--text-inverse);\r\n  border-color: var(--success-500);\r\n}\r\n\r\n.empty-action-btn.primary:hover {\r\n  background: var(--success-600);\r\n  border-color: var(--success-600);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.empty-action-btn.secondary {\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  border-color: var(--success-200);\r\n}\r\n\r\n.empty-action-btn.secondary:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-400);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n\r\n@media (max-width: 768px) {\r\n  .welcome-title {\r\n    font-size: var(--font-size-2xl);\r\n  }\r\n  \r\n  .welcome-subtitle {\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .quick-actions-grid {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .action-btn {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n  \r\n  .header-controls {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .rooms-stats {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .welcome-actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .welcome-btn {\r\n    width: 100%;\r\n    max-width: 250px;\r\n  }\r\n  \r\n  .section-header {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-card {\r\n    padding: var(--spacing-4);\r\n  }\r\n  \r\n  .room-list-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-list-actions {\r\n    align-self: flex-end;\r\n  }\r\n}\r\n\r\n\r\n</style>\r\n\r\n<style scoped>\r\n/* ===== 首页基础样式 ===== */\r\n.home-page {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--success-50) 100%);\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.home-page::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"home-pattern\" width=\"60\" height=\"60\" patternUnits=\"userSpaceOnUse\"><circle cx=\"30\" cy=\"30\" r=\"3\" fill=\"%2322c55e\" opacity=\"0.03\"/><circle cx=\"15\" cy=\"15\" r=\"1.5\" fill=\"%2322c55e\" opacity=\"0.02\"/><circle cx=\"45\" cy=\"45\" r=\"1.5\" fill=\"%2322c55e\" opacity=\"0.02\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23home-pattern)\"/></svg>') repeat;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n\r\n.home-page > * {\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n/* ===== 英雄区域 ===== */\r\n.hero-section {\r\n  min-height: 60vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.hero-background {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"hero-pattern\" width=\"80\" height=\"80\" patternUnits=\"userSpaceOnUse\"><circle cx=\"40\" cy=\"40\" r=\"4\" fill=\"%23ffffff\" opacity=\"0.05\"/><circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"%23ffffff\" opacity=\"0.03\"/><circle cx=\"60\" cy=\"60\" r=\"2\" fill=\"%23ffffff\" opacity=\"0.03\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23hero-pattern)\"/></svg>') repeat;\r\n  pointer-events: none;\r\n  z-index: 1;\r\n}\r\n\r\n.hero-content {\r\n  text-align: center;\r\n  max-width: 800px;\r\n  padding: 0 2rem;\r\n}\r\n\r\n/* ===== 英雄区域 ===== */\r\n.hero-section {\r\n  min-height: 60vh;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  overflow: hidden;\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n\r\n\r\n.hero-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);\r\n}\r\n\r\n.hero-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: var(--spacing-8) var(--spacing-6);\r\n  text-align: center;\r\n  position: relative;\r\n  z-index: 3;\r\n}\r\n\r\n.hero-text {\r\n  margin-bottom: var(--spacing-8);\r\n}\r\n\r\n.hero-title {\r\n  font-size: var(--font-size-4xl);\r\n  font-weight: var(--font-weight-bold);\r\n  margin: 0 0 var(--spacing-4) 0;\r\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);\r\n  animation: hero-title-appear 1s ease-out;\r\n}\r\n\r\n@keyframes hero-title-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(30px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.hero-subtitle {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-medium);\r\n  margin: 0 0 var(--spacing-6) 0;\r\n  opacity: 0.9;\r\n  animation: hero-subtitle-appear 1s ease-out 0.3s both;\r\n}\r\n\r\n@keyframes hero-subtitle-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 0.9;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.hero-stats {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--spacing-8);\r\n  margin-top: var(--spacing-6);\r\n  animation: hero-stats-appear 1s ease-out 0.6s both;\r\n}\r\n\r\n@keyframes hero-stats-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-4);\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: var(--radius-xl);\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.stat-item:hover {\r\n  background: rgba(255, 255, 255, 0.15);\r\n  transform: translateY(-4px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.stat-item i {\r\n  font-size: var(--font-size-2xl);\r\n  color: var(--success-200);\r\n}\r\n\r\n.stat-value {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.stat-label {\r\n  font-size: var(--font-size-sm);\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.hero-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: var(--spacing-4);\r\n  animation: hero-actions-appear 1s ease-out 0.9s both;\r\n}\r\n\r\n@keyframes hero-actions-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.hero-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  padding: var(--spacing-4) var(--spacing-6);\r\n  border: none;\r\n  border-radius: var(--radius-xl);\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-bold);\r\n  text-decoration: none;\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.hero-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.hero-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.hero-btn.primary {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: var(--text-inverse);\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.hero-btn.primary:hover {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  border-color: rgba(255, 255, 255, 0.5);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.hero-btn.secondary {\r\n  background: transparent;\r\n  color: var(--text-inverse);\r\n  border: 2px solid rgba(255, 255, 255, 0.5);\r\n}\r\n\r\n.hero-btn.secondary:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-color: rgba(255, 255, 255, 0.7);\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* ===== 主要内容区域 ===== */\r\n.main-content {\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n  padding: var(--spacing-8) var(--spacing-6);\r\n}\r\n\r\n/* ===== 区域头部通用样式 ===== */\r\n.section-header {\r\n  text-align: center;\r\n  margin-bottom: var(--spacing-8);\r\n}\r\n\r\n.section-title {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: var(--spacing-3);\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-3) 0;\r\n}\r\n\r\n.section-title i {\r\n  color: var(--success-600);\r\n  font-size: var(--font-size-xl);\r\n}\r\n\r\n.section-desc {\r\n  font-size: var(--font-size-base);\r\n  color: var(--text-secondary);\r\n  margin: 0;\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.section-controls {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: var(--spacing-4);\r\n}\r\n\r\n/* ===== 快速操作区域 ===== */\r\n.quick-actions-section {\r\n  margin-bottom: var(--spacing-12);\r\n}\r\n\r\n.quick-actions-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\r\n  gap: var(--spacing-6);\r\n  margin-top: var(--spacing-6);\r\n}\r\n\r\n.action-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-4);\r\n  padding: var(--spacing-6);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.95) 100%);\r\n  border: 2px solid var(--success-200);\r\n  border-radius: var(--radius-2xl);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  text-align: left;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.action-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 50%, var(--success-400) 100%);\r\n  transform: scaleX(0);\r\n  transition: transform var(--transition-fast);\r\n}\r\n\r\n.action-card:hover::before {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.action-card:hover {\r\n  border-color: var(--success-300);\r\n  box-shadow: var(--shadow-xl);\r\n  transform: translateY(-6px);\r\n}\r\n\r\n.action-card:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.action-card.primary {\r\n  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);\r\n  border-color: var(--success-300);\r\n}\r\n\r\n.action-card.primary:hover {\r\n  background: linear-gradient(135deg, var(--success-200) 0%, var(--success-300) 100%);\r\n  border-color: var(--success-400);\r\n}\r\n\r\n.action-icon {\r\n  width: 60px;\r\n  height: 60px;\r\n  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);\r\n  border-radius: var(--radius-xl);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--font-size-xl);\r\n  color: var(--text-inverse);\r\n  flex-shrink: 0;\r\n  box-shadow: var(--shadow-md);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-icon::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);\r\n  transform: translateX(-100%);\r\n  transition: transform 0.6s ease;\r\n}\r\n\r\n.action-card:hover .action-icon::before {\r\n  transform: translateX(100%);\r\n}\r\n\r\n.action-content {\r\n  flex: 1;\r\n}\r\n\r\n.action-title {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-2) 0;\r\n}\r\n\r\n.action-desc {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  margin: 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* ===== 房间过滤器 ===== */\r\n.room-filters {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.9) 100%);\r\n  padding: var(--spacing-2);\r\n  border-radius: var(--radius-xl);\r\n  box-shadow: var(--shadow-sm);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.filter-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-3) var(--spacing-4);\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.filter-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.filter-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.filter-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.filter-btn.active {\r\n  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);\r\n  color: var(--text-inverse);\r\n  border-color: var(--success-500);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.filter-count {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: var(--text-inverse);\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-bold);\r\n  min-width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.filter-btn:not(.active) .filter-count {\r\n  background: var(--success-200);\r\n  color: var(--success-700);\r\n}\r\n\r\n.refresh-btn {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 1px solid var(--success-200);\r\n  border-radius: var(--radius-lg);\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: var(--font-size-base);\r\n}\r\n\r\n.refresh-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n  transform: scale(1.1);\r\n  box-shadow: var(--shadow-sm);\r\n}/* =\r\n==== 房间网格 ===== */\r\n.rooms-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\r\n  gap: var(--spacing-6);\r\n  margin-top: var(--spacing-6);\r\n}\r\n\r\n.room-card {\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.95) 100%);\r\n  border: 2px solid var(--success-200);\r\n  border-radius: var(--radius-2xl);\r\n  padding: var(--spacing-6);\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  box-shadow: var(--shadow-md);\r\n  animation: room-card-appear 0.5s ease-out;\r\n}\r\n\r\n@keyframes room-card-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px) scale(0.95);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n.room-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 4px;\r\n  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 50%, var(--success-400) 100%);\r\n  transform: scaleX(0);\r\n  transition: transform var(--transition-fast);\r\n}\r\n\r\n.room-card:hover {\r\n  border-color: var(--success-300);\r\n  box-shadow: var(--shadow-xl);\r\n  transform: translateY(-6px);\r\n}\r\n\r\n.room-card:hover::before {\r\n  transform: scaleX(1);\r\n}\r\n\r\n.room-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.room-info {\r\n  flex: 1;\r\n}\r\n\r\n.room-name {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-2) 0;\r\n  line-height: 1.3;\r\n}\r\n\r\n.room-meta {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.room-creator,\r\n.room-players {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.room-creator i {\r\n  color: var(--warning-500);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.room-players i {\r\n  color: var(--success-500);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.room-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-bold);\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.room-status.active {\r\n  background: rgba(34, 197, 94, 0.1);\r\n  color: var(--success-700);\r\n  border: 1px solid var(--success-300);\r\n}\r\n\r\n.room-status.waiting {\r\n  background: rgba(251, 191, 36, 0.1);\r\n  color: var(--warning-700);\r\n  border: 1px solid var(--warning-300);\r\n}\r\n\r\n.room-status.recruiting {\r\n  background: rgba(59, 130, 246, 0.1);\r\n  color: var(--info-700);\r\n  border: 1px solid var(--info-300);\r\n}\r\n\r\n.room-status.private {\r\n  background: rgba(107, 114, 128, 0.1);\r\n  color: var(--gray-700);\r\n  border: 1px solid var(--gray-300);\r\n}\r\n\r\n.status-dot {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: var(--radius-full);\r\n  animation: status-pulse 2s ease-in-out infinite;\r\n}\r\n\r\n.room-status.active .status-dot {\r\n  background: var(--success-500);\r\n}\r\n\r\n.room-status.waiting .status-dot {\r\n  background: var(--warning-500);\r\n}\r\n\r\n.room-status.recruiting .status-dot {\r\n  background: var(--info-500);\r\n}\r\n\r\n.room-status.private .status-dot {\r\n  background: var(--gray-500);\r\n}\r\n\r\n@keyframes status-pulse {\r\n  0%, 100% { opacity: 1; }\r\n  50% { opacity: 0.5; }\r\n}\r\n\r\n.room-content {\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.room-description {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n  line-height: 1.6;\r\n  margin: 0 0 var(--spacing-3) 0;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 3;\r\n  -webkit-box-orient: vertical;\r\n  overflow: hidden;\r\n}\r\n\r\n.room-tags {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.room-tag {\r\n  padding: var(--spacing-1) var(--spacing-3);\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  border: 1px solid var(--success-200);\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.room-tag:hover {\r\n  background: var(--success-200);\r\n  border-color: var(--success-300);\r\n  transform: scale(1.05);\r\n}\r\n\r\n.room-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding-top: var(--spacing-4);\r\n  border-top: 1px solid var(--success-200);\r\n}\r\n\r\n.room-time {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.room-time i {\r\n  color: var(--success-400);\r\n}\r\n\r\n.room-actions {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.join-btn,\r\n.view-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-2) var(--spacing-4);\r\n  border: none;\r\n  border-radius: var(--radius-lg);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-semibold);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.join-btn::before,\r\n.view-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left var(--transition-fast);\r\n}\r\n\r\n.join-btn:hover::before,\r\n.view-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.join-btn {\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.join-btn:hover:not(:disabled) {\r\n  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.join-btn:disabled {\r\n  background: var(--disabled-bg);\r\n  color: var(--disabled-text);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.view-btn {\r\n  background: var(--bg-primary);\r\n  color: var(--text-secondary);\r\n  border: 1px solid var(--success-200);\r\n}\r\n\r\n.view-btn:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n/* ===== 空状态 ===== */\r\n.empty-state {\r\n  text-align: center;\r\n  padding: var(--spacing-12) var(--spacing-6);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.9) 100%);\r\n  border: 2px dashed var(--success-300);\r\n  border-radius: var(--radius-2xl);\r\n  margin-top: var(--spacing-6);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.empty-icon {\r\n  width: 80px;\r\n  height: 80px;\r\n  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);\r\n  border-radius: var(--radius-full);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin: 0 auto var(--spacing-4);\r\n  font-size: var(--font-size-2xl);\r\n  color: var(--success-600);\r\n  animation: empty-icon-float 3s ease-in-out infinite;\r\n}\r\n\r\n@keyframes empty-icon-float {\r\n  0%, 100% { transform: translateY(0); }\r\n  50% { transform: translateY(-10px); }\r\n}\r\n\r\n.empty-title {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-3) 0;\r\n}\r\n\r\n.empty-desc {\r\n  font-size: var(--font-size-base);\r\n  color: var(--text-secondary);\r\n  margin: 0 0 var(--spacing-6) 0;\r\n  line-height: 1.5;\r\n}\r\n\r\n.empty-action-btn {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  padding: var(--spacing-4) var(--spacing-6);\r\n  border: none;\r\n  border-radius: var(--radius-xl);\r\n  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);\r\n  color: var(--text-inverse);\r\n  font-size: var(--font-size-base);\r\n  font-weight: var(--font-weight-bold);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.empty-action-btn:hover {\r\n  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);\r\n  transform: translateY(-3px);\r\n  box-shadow: var(--shadow-lg);\r\n}/* ====\r\n= 响应式设计 ===== */\r\n@media (max-width: 1024px) {\r\n  .hero-content {\r\n    padding: var(--spacing-6) var(--spacing-4);\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: var(--font-size-3xl);\r\n  }\r\n  \r\n  .hero-subtitle {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n  \r\n  .hero-stats {\r\n    gap: var(--spacing-6);\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-6) var(--spacing-4);\r\n  }\r\n  \r\n  .quick-actions-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n    gap: var(--spacing-4);\r\n  }\r\n  \r\n  .rooms-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n    gap: var(--spacing-4);\r\n  }\r\n  \r\n  .section-controls {\r\n    flex-direction: column;\r\n    gap: var(--spacing-4);\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .room-filters {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .hero-section {\r\n    min-height: 50vh;\r\n  }\r\n  \r\n  .hero-content {\r\n    padding: var(--spacing-4) var(--spacing-3);\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: var(--font-size-2xl);\r\n  }\r\n  \r\n  .hero-subtitle {\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .hero-stats {\r\n    flex-direction: column;\r\n    gap: var(--spacing-4);\r\n    align-items: center;\r\n  }\r\n  \r\n  .stat-item {\r\n    flex-direction: row;\r\n    padding: var(--spacing-3);\r\n    width: 100%;\r\n    max-width: 200px;\r\n  }\r\n  \r\n  .hero-actions {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n  \r\n  .hero-btn {\r\n    width: 100%;\r\n    max-width: 250px;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-4) var(--spacing-3);\r\n  }\r\n  \r\n  .section-title {\r\n    font-size: var(--font-size-xl);\r\n  }\r\n  \r\n  .quick-actions-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .action-card {\r\n    padding: var(--spacing-4);\r\n  }\r\n  \r\n  .action-icon {\r\n    width: 50px;\r\n    height: 50px;\r\n    font-size: var(--font-size-lg);\r\n  }\r\n  \r\n  .rooms-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .room-card {\r\n    padding: var(--spacing-4);\r\n  }\r\n  \r\n  .room-header {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .room-footer {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .room-actions {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .join-btn,\r\n  .view-btn {\r\n    flex: 1;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .hero-content {\r\n    padding: var(--spacing-3) var(--spacing-2);\r\n  }\r\n  \r\n  .hero-title {\r\n    font-size: var(--font-size-xl);\r\n  }\r\n  \r\n  .hero-subtitle {\r\n    font-size: var(--font-size-sm);\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-3) var(--spacing-2);\r\n  }\r\n  \r\n  .section-header {\r\n    margin-bottom: var(--spacing-6);\r\n  }\r\n  \r\n  .section-title {\r\n    font-size: var(--font-size-lg);\r\n    flex-direction: column;\r\n    gap: var(--spacing-2);\r\n  }\r\n  \r\n  .action-card {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    padding: var(--spacing-3);\r\n  }\r\n  \r\n  .action-icon {\r\n    width: 40px;\r\n    height: 40px;\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .room-card {\r\n    padding: var(--spacing-3);\r\n  }\r\n  \r\n  .room-name {\r\n    font-size: var(--font-size-base);\r\n  }\r\n  \r\n  .room-meta {\r\n    gap: var(--spacing-2);\r\n  }\r\n  \r\n  .room-creator,\r\n  .room-players {\r\n    font-size: var(--font-size-xs);\r\n  }\r\n  \r\n  .room-tags {\r\n    gap: var(--spacing-1);\r\n  }\r\n  \r\n  .room-tag {\r\n    font-size: var(--font-size-xs);\r\n    padding: var(--spacing-1) var(--spacing-2);\r\n  }\r\n  \r\n  .filter-btn {\r\n    padding: var(--spacing-2) var(--spacing-3);\r\n    font-size: var(--font-size-xs);\r\n  }\r\n  \r\n  .filter-btn span:not(.filter-count) {\r\n    display: none;\r\n  }\r\n  \r\n  .empty-state {\r\n    padding: var(--spacing-8) var(--spacing-4);\r\n  }\r\n  \r\n  .empty-icon {\r\n    width: 60px;\r\n    height: 60px;\r\n    font-size: var(--font-size-xl);\r\n  }\r\n  \r\n  .empty-title {\r\n    font-size: var(--font-size-lg);\r\n  }\r\n  \r\n  .empty-desc {\r\n    font-size: var(--font-size-sm);\r\n  }\r\n}\r\n\r\n/* ===== 可访问性增强 ===== */\r\n.home-page button:focus,\r\n.home-page a:focus {\r\n  outline: 2px solid var(--success-500);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* ===== 高对比度模式 ===== */\r\n@media (prefers-contrast: high) {\r\n  .hero-section {\r\n    border-bottom: 3px solid var(--success-400);\r\n  }\r\n  \r\n  .action-card,\r\n  .room-card {\r\n    border-width: 3px;\r\n  }\r\n  \r\n  .hero-btn,\r\n  .join-btn,\r\n  .view-btn,\r\n  .filter-btn {\r\n    border-width: 2px;\r\n  }\r\n  \r\n  .empty-state {\r\n    border-width: 3px;\r\n  }\r\n}\r\n\r\n/* ===== 减少动画模式 ===== */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .home-page *,\r\n  .home-page *::before,\r\n  .home-page *::after {\r\n    animation: none !important;\r\n    transition: none !important;\r\n  }\r\n  \r\n  .action-card:hover,\r\n  .room-card:hover,\r\n  .hero-btn:hover,\r\n  .join-btn:hover,\r\n  .view-btn:hover {\r\n    transform: none !important;\r\n  }\r\n}\r\n\r\n/* ===== 打印样式 ===== */\r\n@media print {\r\n  .home-page {\r\n    background: white !important;\r\n  }\r\n  \r\n  .hero-section {\r\n    background: var(--gray-100) !important;\r\n    color: var(--text-primary) !important;\r\n    page-break-after: avoid;\r\n  }\r\n  \r\n  .hero-actions,\r\n  .room-actions,\r\n  .section-controls {\r\n    display: none !important;\r\n  }\r\n  \r\n  .main-content {\r\n    padding: var(--spacing-4) !important;\r\n  }\r\n  \r\n  .action-card,\r\n  .room-card {\r\n    page-break-inside: avoid;\r\n    margin-bottom: var(--spacing-4);\r\n    box-shadow: none !important;\r\n    border: 1px solid var(--gray-300) !important;\r\n  }\r\n  \r\n  .quick-actions-grid,\r\n  .rooms-grid {\r\n    grid-template-columns: 1fr !important;\r\n    gap: var(--spacing-3) !important;\r\n  }\r\n}\r\n\r\n/* ===== 自定义滚动条 ===== */\r\n.home-page::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.home-page::-webkit-scrollbar-track {\r\n  background: var(--success-100);\r\n  border-radius: var(--radius-full);\r\n}\r\n\r\n.home-page::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(180deg, var(--success-400) 0%, var(--success-500) 100%);\r\n  border-radius: var(--radius-full);\r\n  border: 1px solid var(--success-300);\r\n}\r\n\r\n.home-page::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(180deg, var(--success-500) 0%, var(--success-600) 100%);\r\n}\r\n\r\n/* ===== 加载状态 ===== */\r\n.loading-state {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: var(--spacing-8);\r\n  color: var(--success-600);\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 4px solid var(--success-200);\r\n  border-top: 4px solid var(--success-600);\r\n  border-radius: var(--radius-full);\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n</style>"], "mappings": ";;;;;;EACO,SAAM;AAAW;;EAEX,SAAM;AAAiB;;EACzB,SAAM;AAAiB;;EACrB,SAAM;AAAc;;EACnB,SAAM;AAAe;;;;;;;;EAItB,SAAM;AAAkB;;;EAIxB,SAAM;;;EAcT,SAAM;AAAc;;;EAEf,SAAM;;;EACR,SAAM;AAAoB;;;EAmBxB,SAAM;AAAe;;EACvB,SAAM;AAAgB;;EASpB,SAAM;AAAiB;;EACrB,SAAM;AAAc;;;EAMbA,KAAK,EAAC;;;EAKb,SAAM;AAAe;;;;EA0BzB,SAAM;;;EACJ,SAAM;AAAY;;EACf,SAAM;AAAc;;EAGvB,SAAM;AAAY;;EACf,SAAM;AAAc;;EAGvB,SAAM;AAAY;;EACf,SAAM;AAAc;;EAMzB,SAAM;AAAiB;;;EAEM,SAAM;;;;EAQ7B,SAAM;AAAa;;EACjB,SAAM;AAAW;;EAChB,SAAM;AAAY;;EACjB,SAAM;AAAW;;EACd,SAAM;AAAc;;EACpB,SAAM;AAAW;;EAS1B,SAAM;AAAkB;;;EAEtB,SAAM;;;EAIN,SAAM;AAAa;;EACjB,SAAM;AAAc;;EAClB,SAAM;AAAc;;EAIpB,SAAM;AAAa;;EAQrB,SAAM;AAAc;;;EAcnB,SAAM;AAAW;;;EAQpB,SAAM;AAAgB;;EACpB,SAAM;AAAkB;;EACvB,SAAM;AAAiB;;EAM1B,SAAM;AAAuB;;EAC3B,SAAM;AAAgB;;EACnB,SAAM;AAAW;;EAIjB,SAAM;AAAW;;EAIjB,SAAM;AAAW;;EAOtB,SAAM;AAAmB;;;;EAaK,SAAM;;;EAIvC,SAAM;AAAa;;EACpB,SAAM;AAAY;;EAChB,SAAM;AAAe;;;;uBA/NpCC,mBAAA,CA+OM,OA/ONC,UA+OM,GA9OJC,mBAAA,aAAgB,EAChBC,mBAAA,CAsBU,WAtBVC,UAsBU,GArBRD,mBAAA,CAoBM,OApBNE,UAoBM,GAnBJF,mBAAA,CAMM,OANNG,UAMM,GALJH,mBAAA,CAGK,MAHLI,UAGK,GAFSC,IAAA,CAAAC,eAAe,I,cAA3BT,mBAAA,CAA4F,QAAAU,UAAA,EAAAC,gBAAA,CAA5DC,QAAA,CAAAC,WAAW,MAAK,GAAC,GAAAF,gBAAA,CAAG,EAAAG,gBAAA,GAAAN,IAAA,CAAAO,WAAW,cAAAD,gBAAA,uBAAXA,gBAAA,CAAaE,QAAQ,+B,cACzEhB,mBAAA,CAA6B,QAAAiB,UAAA,EAAhB,WAAS,G,GAExBd,mBAAA,CAAyD,KAAzDe,UAAyD,EAAAP,gBAAA,CAA1BC,QAAA,CAAAO,iBAAiB,mB,GAGlDjB,mBAAA,gBAAmB,E,CACiBM,IAAA,CAAAC,eAAe,I,cAAnDT,mBAAA,CASM,OATNoB,UASM,GARJC,YAAA,CAGcC,sBAAA;IAHDC,EAAE,EAAC,QAAQ;IAAC,SAAM;;wBAC7B;MAAA,OAAkCC,MAAA,QAAAA,MAAA,OAAlCrB,mBAAA,CAAkC;QAA/B,SAAM;MAAoB,2BAC7BA,mBAAA,CAAiB,cAAX,MAAI,mB;;;;MAEZkB,YAAA,CAGcC,sBAAA;IAHDC,EAAE,EAAC,WAAW;IAAC,SAAM;;wBAChC;MAAA,OAAgCC,MAAA,SAAAA,MAAA,QAAhCrB,mBAAA,CAAgC;QAA7B,SAAM;MAAkB,2BAC3BA,mBAAA,CAAiB,cAAX,MAAI,mB;;;;iDAMlBD,mBAAA,YAAe,EACfC,mBAAA,CAmNO,QAnNPsB,WAmNO,GAlNLvB,mBAAA,YAAe,EAC8BM,IAAA,CAAAC,eAAe,I,cAA5DT,mBAAA,CAiBU,WAjBV0B,WAiBU,GAhBRvB,mBAAA,CAeM,OAfNwB,WAeM,GAdJxB,mBAAA,CAGS;IAHD,SAAM,oBAAoB;IAAEyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA;MAAA,OAAEZ,QAAA,CAAAiB,UAAA,IAAAjB,QAAA,CAAAiB,UAAA,CAAAC,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAU;IAAA;kCACnD5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,IAGZA,mBAAA,CAGS;IAHD,SAAM,YAAY;IAAEyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA;MAAA,OAAEZ,QAAA,CAAAoB,cAAA,IAAApB,QAAA,CAAAoB,cAAA,CAAAF,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAc;IAAA;IAAGE,QAAQ,EAAErB,QAAA,CAAAsB,mBAAmB;kCAC/E/B,mBAAA,CAA6B;IAA1B,SAAM;EAAe,2BACxBA,mBAAA,CAAiB,cAAX,MAAI,mB,gCAGZA,mBAAA,CAGS;IAHD,SAAM,YAAY;IAAEyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA;MAAA,OAAEZ,QAAA,CAAAuB,oBAAA,IAAAvB,QAAA,CAAAuB,oBAAA,CAAAL,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAoB;IAAA;kCACrD5B,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,2BAC9BA,mBAAA,CAAiB,cAAX,MAAI,mB,6CAKhBD,mBAAA,YAAe,EACfC,mBAAA,CA0LU,WA1LViC,WA0LU,GAzLRjC,mBAAA,CA4CM,OA5CNkC,WA4CM,G,4BA3CJlC,mBAAA,CAMM;IAND,SAAM;EAAa,IACtBA,mBAAA,CAGK;IAHD,SAAM;EAAe,IACvBA,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,IAC3BA,mBAAA,CAAiB,cAAX,MAAI,E,GAEZA,mBAAA,CAAyC;IAAtC,SAAM;EAAkB,GAAC,WAAS,E,qBAGvCA,mBAAA,CAkCM,OAlCNmC,WAkCM,GAjCJnC,mBAAA,CASM,OATNoC,WASM,G,4BARJpC,mBAAA,CAAuC;IAAhC,SAAM;EAAc,GAAC,KAAG,qB,gBAC/BA,mBAAA,CAMS;;aANQqC,KAAA,CAAAC,UAAU,GAAAC,MAAA;IAAA;IAAE,SAAM;kCACjCvC,mBAAA,CAAiC;IAAzBJ,KAAK,EAAC;EAAK,GAAC,MAAI,qB,4BACxBI,mBAAA,CAAoC;IAA5BJ,KAAK,EAAC;EAAQ,GAAC,MAAI,qB,4BAC3BI,mBAAA,CAAqC;IAA7BJ,KAAK,EAAC;EAAS,GAAC,MAAI,qBACHS,IAAA,CAAAC,eAAe,I,cAAxCT,mBAAA,CAAuD,UAAvD2C,WAAuD,EAAb,MAAI,K,+DAC9CxC,mBAAA,CAAsC;IAA9BJ,KAAK,EAAC;EAAW,GAAC,KAAG,oB,0CALdyC,KAAA,CAAAC,UAAU,E,KAS7BtC,mBAAA,CAiBM,OAjBNyC,WAiBM,GAhBJzC,mBAAA,CAOS;IANNyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;MAAA,OAAEF,KAAA,CAAAK,QAAQ;IAAA;IAChB,SAAKC,eAAA,EAAC,UAAU;MAAA,UACIN,KAAA,CAAAK,QAAQ;IAAA;IAC5BE,KAAK,EAAC;kCAEN5C,mBAAA,CAAyB;IAAtB,SAAM;EAAW,0B,mBAEtBA,mBAAA,CAOS;IANNyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA,gBAAAkB,MAAA;MAAA,OAAEF,KAAA,CAAAK,QAAQ;IAAA;IAChB,SAAKC,eAAA,EAAC,UAAU;MAAA,UACIN,KAAA,CAAAK,QAAQ;IAAA;IAC5BE,KAAK,EAAC;kCAEN5C,mBAAA,CAA2B;IAAxB,SAAM;EAAa,0B,qBAI1BA,mBAAA,CAES;IAFAyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA;MAAA,OAAEZ,QAAA,CAAAoC,YAAA,IAAApC,QAAA,CAAAoC,YAAA,CAAAlB,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAY;IAAA;IAAE,SAAM,aAAa;IAAEE,QAAQ,EAAEO,KAAA,CAAAS,OAAO;IAAEF,KAAK,EAAC;MAC1E5C,mBAAA,CAA+D;IAA5D,SAAK2C,eAAA,EAAC,iBAAiB;MAAA,WAAsBN,KAAA,CAAAS;IAAO;6DAK7D/C,mBAAA,UAAa,EACkBU,QAAA,CAAAsC,aAAa,CAACC,MAAM,Q,cAAnDnD,mBAAA,CAaM,OAbNoD,WAaM,GAZJjD,mBAAA,CAGM,OAHNkD,WAGM,GAFJlD,mBAAA,CAA4D,QAA5DmD,WAA4D,EAAA3C,gBAAA,CAA9BC,QAAA,CAAAsC,aAAa,CAACC,MAAM,kB,4BAClDhD,mBAAA,CAAoC;IAA9B,SAAM;EAAa,GAAC,KAAG,oB,GAE/BA,mBAAA,CAGM,OAHNoD,WAGM,GAFJpD,mBAAA,CAA2D,QAA3DqD,WAA2D,EAAA7C,gBAAA,CAA7BC,QAAA,CAAAsB,mBAAmB,kB,4BACjD/B,mBAAA,CAAoC;IAA9B,SAAM;EAAa,GAAC,KAAG,oB,GAE/BA,mBAAA,CAGM,OAHNsD,WAGM,GAFJtD,mBAAA,CAAwD,QAAxDuD,WAAwD,EAAA/C,gBAAA,CAA1BC,QAAA,CAAA+C,gBAAgB,kB,4BAC9CxD,mBAAA,CAAoC;IAA9B,SAAM;EAAa,GAAC,KAAG,oB,0CAIjCD,mBAAA,UAAa,EACbC,mBAAA,CAyHM,OAzHNyD,WAyHM,GAxHJ1D,mBAAA,UAAa,EACFsC,KAAA,CAAAK,QAAQ,e,cAAnB7C,mBAAA,CAqDM,OArDN6D,WAqDM,I,kBApDJ7D,mBAAA,CAmDM8D,SAAA,QAAAC,WAAA,CAlDWnD,QAAA,CAAAsC,aAAa,YAArBc,IAAI;yBADbhE,mBAAA,CAmDM;MAjDHiE,GAAG,EAAED,IAAI,CAACE,EAAE;MACb,SAAKpB,eAAA,EAAC,WAAW;QAAA,QAECkB,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACI;MAAW;MADzDxC,OAAK,WAALA,OAAKA,CAAAc,MAAA;QAAA,OAAE9B,QAAA,CAAAyD,QAAQ,CAACL,IAAI;MAAA;QAGrB7D,mBAAA,CAYM,OAZNmE,WAYM,GAXJnE,mBAAA,CAMM,OANNoE,WAMM,GALJpE,mBAAA,CAA2C,MAA3CqE,WAA2C,EAAA7D,gBAAA,CAAjBqD,IAAI,CAACS,IAAI,kBACnCtE,mBAAA,CAGM,OAHNuE,WAGM,GAFJvE,mBAAA,CAAoE,QAApEwE,WAAoE,EAAzC,KAAG,GAAAhE,gBAAA,CAAGqD,IAAI,CAACY,YAAY,0BAClDzE,mBAAA,CAAgE,QAAhE0E,WAAgE,EAAAlE,gBAAA,CAArCC,QAAA,CAAAkE,UAAU,CAACd,IAAI,CAACe,UAAU,kB,KAGzD5E,mBAAA,CAGM;MAHD,SAAK2C,eAAA,EAAC,mBAAmB,EAASkB,IAAI,CAACgB,MAAM;QAChD7E,mBAAA,CAA2C;MAAvC,SAAK2C,eAAA,CAAElC,QAAA,CAAAqE,aAAa,CAACjB,IAAI,CAACgB,MAAM;6BACpC7E,mBAAA,CAA6C,cAAAQ,gBAAA,CAApCC,QAAA,CAAAsE,aAAa,CAAClB,IAAI,CAACgB,MAAM,kB,oBAItC7E,mBAAA,CAAgE,KAAhEgF,WAAgE,EAAAxE,gBAAA,CAAjCqD,IAAI,CAACoB,WAAW,4BAElBpB,IAAI,CAACqB,IAAI,IAAIrB,IAAI,CAACqB,IAAI,CAAClC,MAAM,Q,cAA1DnD,mBAAA,CAEM,OAFNsF,WAEM,I,kBADJtF,mBAAA,CAAuF8D,SAAA,QAAAC,WAAA,CAAnEC,IAAI,CAACqB,IAAI,CAACE,KAAK,kBAAtBC,GAAG;2BAAhBxF,mBAAA,CAAuF;QAA3CiE,GAAG,EAAEuB,GAAG;QAAE,SAAM;0BAAcA,GAAG;2EAG/ErF,mBAAA,CAuBM,OAvBNsF,WAuBM,GAtBJtF,mBAAA,CAWM,OAXNuF,WAWM,GAVJvF,mBAAA,CAGM,OAHNwF,WAGM,G,4BAFJxF,mBAAA,CAA4B;MAAzB,SAAM;IAAc,4BACvBA,mBAAA,CAA8D,cAAAQ,gBAAA,CAArDqD,IAAI,CAACG,eAAe,IAAG,GAAC,GAAAxD,gBAAA,CAAGqD,IAAI,CAACI,WAAW,iB,GAEtDjE,mBAAA,CAKM,OALNyF,WAKM,GAJJzF,mBAAA,CAGO;MAFL,SAAM,cAAc;MACnB0F,KAAK,EAAAC,eAAA;QAAAC,KAAA,EAAY/B,IAAI,CAACG,eAAe,GAAGH,IAAI,CAACI,WAAW;MAAA;iCAK/DjE,mBAAA,CAQM,OARN6F,WAQM,GAPJ7F,mBAAA,CAMS;MALP,SAAM,UAAU;MACf8B,QAAQ,EAAE+B,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACI,WAAW,KAAK5D,IAAA,CAAAC,eAAe;MACtEmB,OAAK,EAAAqE,cAAA,WAAAvD,MAAA;QAAA,OAAO9B,QAAA,CAAAyD,QAAQ,CAACL,IAAI;MAAA;wBAEvBA,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACI,WAAW,sCAAA8B,WAAA,E;qDAQrDlG,mBAAA,CA2CM8D,SAAA;IAAAG,GAAA;EAAA,IA5CN/D,mBAAA,UAAa,EACbC,mBAAA,CA2CM,OA3CNgG,WA2CM,I,kBA1CJnG,mBAAA,CAyCM8D,SAAA,QAAAC,WAAA,CAxCWnD,QAAA,CAAAsC,aAAa,YAArBc,IAAI;yBADbhE,mBAAA,CAyCM;MAvCHiE,GAAG,EAAED,IAAI,CAACE,EAAE;MACb,SAAKpB,eAAA,EAAC,gBAAgB;QAAA,QAEJkB,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACI;MAAW;MADzDxC,OAAK,WAALA,OAAKA,CAAAc,MAAA;QAAA,OAAE9B,QAAA,CAAAyD,QAAQ,CAACL,IAAI;MAAA;QAGrB7D,mBAAA,CAuBM,OAvBNiG,WAuBM,GAtBJjG,mBAAA,CAMM,OANNkG,WAMM,GALJlG,mBAAA,CAAgD,MAAhDmG,WAAgD,EAAA3F,gBAAA,CAAjBqD,IAAI,CAACS,IAAI,kBACxCtE,mBAAA,CAGM;MAHD,SAAK2C,eAAA,EAAC,kBAAkB,EAASkB,IAAI,CAACgB,MAAM;QAC/C7E,mBAAA,CAA2C;MAAvC,SAAK2C,eAAA,CAAElC,QAAA,CAAAqE,aAAa,CAACjB,IAAI,CAACgB,MAAM;6BACpC7E,mBAAA,CAA6C,cAAAQ,gBAAA,CAApCC,QAAA,CAAAsE,aAAa,CAAClB,IAAI,CAACgB,MAAM,kB,oBAGtC7E,mBAAA,CAAqE,KAArEoG,WAAqE,EAAA5F,gBAAA,CAAjCqD,IAAI,CAACoB,WAAW,4BACpDjF,mBAAA,CAaM,OAbNqG,WAaM,GAZJrG,mBAAA,CAGO,QAHPsG,WAGO,G,4BAFLtG,mBAAA,CAA2B;MAAxB,SAAM;IAAa,4BACtBA,mBAAA,CAA4C,cAAAQ,gBAAA,CAAnCqD,IAAI,CAACY,YAAY,yB,GAE5BzE,mBAAA,CAGO,QAHPuG,WAGO,G,4BAFLvG,mBAAA,CAA4B;MAAzB,SAAM;IAAc,4BACvBA,mBAAA,CAA8C,cAAAQ,gBAAA,CAArCC,QAAA,CAAAkE,UAAU,CAACd,IAAI,CAACe,UAAU,kB,GAErC5E,mBAAA,CAGO,QAHPwG,WAGO,G,4BAFLxG,mBAAA,CAA4B;MAAzB,SAAM;IAAc,4BACvBA,mBAAA,CAA8D,cAAAQ,gBAAA,CAArDqD,IAAI,CAACG,eAAe,IAAG,GAAC,GAAAxD,gBAAA,CAAGqD,IAAI,CAACI,WAAW,iB,OAK1DjE,mBAAA,CAQM,OARNyG,WAQM,GAPJzG,mBAAA,CAMS;MALP,SAAM,UAAU;MACf8B,QAAQ,EAAE+B,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACI,WAAW,KAAK5D,IAAA,CAAAC,eAAe;MACtEmB,OAAK,EAAAqE,cAAA,WAAAvD,MAAA;QAAA,OAAO9B,QAAA,CAAAyD,QAAQ,CAACL,IAAI;MAAA;wBAEvBA,IAAI,CAACG,eAAe,IAAIH,IAAI,CAACI,WAAW,sCAAAyC,WAAA,E;sFAMnD3G,mBAAA,SAAY,EACDU,QAAA,CAAAsC,aAAa,CAACC,MAAM,U,cAA/BnD,mBAAA,CAgBM,OAhBN8G,WAgBM,G,4BAfJ3G,mBAAA,CAEM;IAFD,SAAM;EAAY,IACrBA,mBAAA,CAAgC;IAA7B,SAAM;EAAkB,G,qBAE7BA,mBAAA,CAAuD,MAAvD4G,WAAuD,EAAApG,gBAAA,CAA5BC,QAAA,CAAAoG,kBAAkB,oBAC7C7G,mBAAA,CAAmD,KAAnD8G,WAAmD,EAAAtG,gBAAA,CAA1BC,QAAA,CAAAsG,iBAAiB,oBAC1C/G,mBAAA,CASM,OATNgH,WASM,GAR+D3G,IAAA,CAAAC,eAAe,I,cAAlFT,mBAAA,CAGS;;IAHA4B,OAAK,EAAAJ,MAAA,QAAAA,MAAA;MAAA,OAAEZ,QAAA,CAAAiB,UAAA,IAAAjB,QAAA,CAAAiB,UAAA,CAAAC,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAU;IAAA;IAAE,SAAM;kCAChC5B,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2BACtBA,mBAAA,CAAiB,cAAX,MAAI,mB,yCAEZA,mBAAA,CAGS;IAHAyB,OAAK,EAAAJ,MAAA,QAAAA,MAAA;MAAA,OAAEZ,QAAA,CAAAoC,YAAA,IAAApC,QAAA,CAAAoC,YAAA,CAAAlB,KAAA,CAAAlB,QAAA,EAAAmB,SAAA,CAAY;IAAA;IAAE,SAAM;kCAClC5B,mBAAA,CAA+B;IAA5B,SAAM;EAAiB,2BAC1BA,mBAAA,CAAiB,cAAX,MAAI,mB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}