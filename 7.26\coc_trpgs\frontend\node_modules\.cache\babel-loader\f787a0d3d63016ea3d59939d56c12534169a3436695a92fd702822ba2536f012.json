{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.find-index.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.splice.js\";\nimport \"core-js/modules/es.date.now.js\";\nimport \"core-js/modules/es.number.constructor.js\";\nimport CharacterToken from './CharacterToken.vue';\nimport MonsterToken from './MonsterToken.vue';\nimport CombatAnimation from './CombatAnimation.vue';\nexport default {\n  name: 'BattlefieldGrid',\n  components: {\n    CharacterToken: CharacterToken,\n    MonsterToken: MonsterToken,\n    CombatAnimation: CombatAnimation\n  },\n  props: {\n    characters: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    monsters: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    obstacles: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    areaEffects: {\n      type: Array,\n      \"default\": function _default() {\n        return [];\n      }\n    },\n    currentRound: {\n      type: Number,\n      \"default\": 1\n    },\n    currentTurnCharacter: {\n      type: Object,\n      \"default\": null\n    },\n    isKeeper: {\n      type: Boolean,\n      \"default\": false\n    },\n    battlefieldWidth: {\n      type: Number,\n      \"default\": 20\n    },\n    battlefieldHeight: {\n      type: Number,\n      \"default\": 15\n    },\n    gridSize: {\n      type: Number,\n      \"default\": 40\n    }\n  },\n  data: function data() {\n    return {\n      // 视图控制\n      zoom: 1,\n      viewBox: '0 0 800 600',\n      svgWidth: 800,\n      svgHeight: 600,\n      // 显示控制\n      showGrid: true,\n      showRuler: false,\n      showAttackRange: false,\n      // 选择状态\n      selectedCharacter: null,\n      selectedMonster: null,\n      // 交互状态\n      isDragging: false,\n      dragStart: null,\n      movementPreview: null,\n      // 测距工具\n      rulerStart: null,\n      rulerEnd: null,\n      // 右键菜单\n      contextMenu: {\n        show: false,\n        type: null,\n        target: null,\n        x: 0,\n        y: 0\n      },\n      // 动画\n      activeAnimations: []\n    };\n  },\n  computed: {\n    // 计算SVG视图框\n    calculatedViewBox: function calculatedViewBox() {\n      var width = this.battlefieldWidth * this.gridSize;\n      var height = this.battlefieldHeight * this.gridSize;\n      return \"0 0 \".concat(width, \" \").concat(height);\n    }\n  },\n  mounted: function mounted() {\n    this.initializeBattlefield();\n    this.setupEventListeners();\n  },\n  beforeUnmount: function beforeUnmount() {\n    this.removeEventListeners();\n  },\n  methods: {\n    /**\r\n     * 初始化战场\r\n     */\n    initializeBattlefield: function initializeBattlefield() {\n      this.updateViewBox();\n      this.centerView();\n    },\n    /**\r\n     * 设置事件监听器\r\n     */\n    setupEventListeners: function setupEventListeners() {\n      document.addEventListener('click', this.handleDocumentClick);\n      document.addEventListener('keydown', this.handleKeyDown);\n      window.addEventListener('resize', this.handleResize);\n    },\n    /**\r\n     * 移除事件监听器\r\n     */\n    removeEventListeners: function removeEventListeners() {\n      document.removeEventListener('click', this.handleDocumentClick);\n      document.removeEventListener('keydown', this.handleKeyDown);\n      window.removeEventListener('resize', this.handleResize);\n    },\n    /**\r\n     * 更新视图框\r\n     */\n    updateViewBox: function updateViewBox() {\n      var width = this.battlefieldWidth * this.gridSize;\n      var height = this.battlefieldHeight * this.gridSize;\n      this.viewBox = \"0 0 \".concat(width, \" \").concat(height);\n      this.svgWidth = width * this.zoom;\n      this.svgHeight = height * this.zoom;\n    },\n    /**\r\n     * 缩放控制\r\n     */\n    zoomIn: function zoomIn() {\n      this.zoom = Math.min(2, this.zoom + 0.1);\n      this.updateViewBox();\n    },\n    zoomOut: function zoomOut() {\n      this.zoom = Math.max(0.5, this.zoom - 0.1);\n      this.updateViewBox();\n    },\n    /**\r\n     * 视图控制\r\n     */\n    centerView: function centerView() {\n      var container = this.$refs.battlefieldContainer;\n      if (container) {\n        container.scrollLeft = (this.svgWidth - container.clientWidth) / 2;\n        container.scrollTop = (this.svgHeight - container.clientHeight) / 2;\n      }\n    },\n    fitToScreen: function fitToScreen() {\n      var container = this.$refs.battlefieldContainer;\n      if (container) {\n        var scaleX = container.clientWidth / (this.battlefieldWidth * this.gridSize);\n        var scaleY = container.clientHeight / (this.battlefieldHeight * this.gridSize);\n        this.zoom = Math.min(scaleX, scaleY, 2);\n        this.updateViewBox();\n        this.centerView();\n      }\n    },\n    resetView: function resetView() {\n      this.zoom = 1;\n      this.updateViewBox();\n      this.centerView();\n    },\n    /**\r\n     * 网格和工具切换\r\n     */\n    toggleGrid: function toggleGrid() {\n      this.showGrid = !this.showGrid;\n    },\n    toggleRuler: function toggleRuler() {\n      this.showRuler = !this.showRuler;\n      if (!this.showRuler) {\n        this.rulerStart = null;\n        this.rulerEnd = null;\n      }\n    },\n    /**\r\n     * 鼠标事件处理\r\n     */\n    handleMouseDown: function handleMouseDown(event) {\n      if (this.showRuler) {\n        var rect = this.$refs.battlefieldSvg.getBoundingClientRect();\n        var x = (event.clientX - rect.left) / this.zoom;\n        var y = (event.clientY - rect.top) / this.zoom;\n        if (!this.rulerStart) {\n          this.rulerStart = {\n            x: x,\n            y: y\n          };\n        } else {\n          this.rulerEnd = {\n            x: x,\n            y: y\n          };\n        }\n      }\n    },\n    handleMouseMove: function handleMouseMove(event) {\n      if (this.showRuler && this.rulerStart && !this.rulerEnd) {\n        var rect = this.$refs.battlefieldSvg.getBoundingClientRect();\n        var x = (event.clientX - rect.left) / this.zoom;\n        var y = (event.clientY - rect.top) / this.zoom;\n        this.rulerEnd = {\n          x: x,\n          y: y\n        };\n      }\n    },\n    handleMouseUp: function handleMouseUp(event) {\n      // 处理鼠标释放\n    },\n    handleWheel: function handleWheel(event) {\n      event.preventDefault();\n      if (event.deltaY < 0) {\n        this.zoomIn();\n      } else {\n        this.zoomOut();\n      }\n    },\n    handleDocumentClick: function handleDocumentClick(event) {\n      if (!event.target.closest('.context-menu')) {\n        this.contextMenu.show = false;\n      }\n    },\n    handleKeyDown: function handleKeyDown(event) {\n      switch (event.key) {\n        case 'Escape':\n          this.clearSelection();\n          this.contextMenu.show = false;\n          break;\n        case 'g':\n        case 'G':\n          if (event.ctrlKey) {\n            event.preventDefault();\n            this.toggleGrid();\n          }\n          break;\n        case 'r':\n        case 'R':\n          if (event.ctrlKey) {\n            event.preventDefault();\n            this.toggleRuler();\n          }\n          break;\n      }\n    },\n    handleResize: function handleResize() {\n      var _this = this;\n      this.$nextTick(function () {\n        _this.updateViewBox();\n      });\n    },\n    /**\r\n     * 角色选择和操作\r\n     */\n    selectCharacter: function selectCharacter(character) {\n      this.selectedCharacter = character;\n      this.selectedMonster = null;\n      this.showAttackRange = true;\n      this.$emit('character-selected', character);\n    },\n    selectMonster: function selectMonster(monster) {\n      this.selectedMonster = monster;\n      this.selectedCharacter = null;\n      this.showAttackRange = false;\n      this.$emit('monster-selected', monster);\n    },\n    clearSelection: function clearSelection() {\n      this.selectedCharacter = null;\n      this.selectedMonster = null;\n      this.showAttackRange = false;\n      this.movementPreview = null;\n    },\n    /**\r\n     * 移动相关\r\n     */\n    moveCharacter: function moveCharacter(character, newPosition) {\n      this.$emit('character-moved', {\n        character: character,\n        oldPosition: character.position,\n        newPosition: newPosition\n      });\n    },\n    moveMonster: function moveMonster(monster, newPosition) {\n      this.$emit('monster-moved', {\n        monster: monster,\n        oldPosition: monster.position,\n        newPosition: newPosition\n      });\n    },\n    startMovement: function startMovement(target) {\n      this.movementPreview = {\n        target: target,\n        start: _objectSpread({}, target.position),\n        end: _objectSpread({}, target.position)\n      };\n      this.contextMenu.show = false;\n    },\n    /**\r\n     * 行动相关\r\n     */\n    characterAction: function characterAction(character, action) {\n      this.$emit('character-action', {\n        character: character,\n        action: action\n      });\n    },\n    monsterAction: function monsterAction(monster, action) {\n      this.$emit('monster-action', {\n        monster: monster,\n        action: action\n      });\n    },\n    /**\r\n     * 右键菜单\r\n     */\n    showCharacterMenu: function showCharacterMenu(character, event) {\n      this.contextMenu = {\n        show: true,\n        type: 'character',\n        target: character,\n        x: event.clientX,\n        y: event.clientY\n      };\n    },\n    showMonsterMenu: function showMonsterMenu(monster, event) {\n      this.contextMenu = {\n        show: true,\n        type: 'monster',\n        target: monster,\n        x: event.clientX,\n        y: event.clientY\n      };\n    },\n    /**\r\n     * 菜单操作\r\n     */\n    inspectCharacter: function inspectCharacter(character) {\n      this.$emit('inspect-character', character);\n      this.contextMenu.show = false;\n    },\n    inspectMonster: function inspectMonster(monster) {\n      this.$emit('inspect-monster', monster);\n      this.contextMenu.show = false;\n    },\n    editCharacter: function editCharacter(character) {\n      this.$emit('edit-character', character);\n      this.contextMenu.show = false;\n    },\n    editMonster: function editMonster(monster) {\n      this.$emit('edit-monster', monster);\n      this.contextMenu.show = false;\n    },\n    controlMonster: function controlMonster(monster) {\n      this.$emit('control-monster', monster);\n      this.contextMenu.show = false;\n    },\n    removeMonster: function removeMonster(monster) {\n      this.$emit('remove-monster', monster);\n      this.contextMenu.show = false;\n    },\n    addMonster: function addMonster() {\n      this.$emit('add-monster');\n    },\n    showActionMenu: function showActionMenu(character) {\n      this.$emit('show-action-menu', character);\n      this.contextMenu.show = false;\n    },\n    /**\r\n     * 工具函数\r\n     */\n    canMoveCharacter: function canMoveCharacter(character) {\n      var _this$currentTurnChar;\n      return ((_this$currentTurnChar = this.currentTurnCharacter) === null || _this$currentTurnChar === void 0 ? void 0 : _this$currentTurnChar.id) === character.id && !character.hasActed;\n    },\n    canActCharacter: function canActCharacter(character) {\n      var _this$currentTurnChar2;\n      return ((_this$currentTurnChar2 = this.currentTurnCharacter) === null || _this$currentTurnChar2 === void 0 ? void 0 : _this$currentTurnChar2.id) === character.id && !character.hasActed;\n    },\n    getAttackRange: function getAttackRange(character) {\n      var weapon = character.equippedWeapon;\n      if (!weapon) return 1.5;\n      if (weapon.type === 'melee') {\n        return weapon.reach || 1.5;\n      } else if (weapon.type === 'ranged') {\n        var _weapon$range;\n        return ((_weapon$range = weapon.range) === null || _weapon$range === void 0 ? void 0 : _weapon$range.base) || 10;\n      }\n      return 1.5;\n    },\n    getRulerDistance: function getRulerDistance() {\n      if (!this.rulerStart || !this.rulerEnd) return 0;\n      var dx = this.rulerEnd.x - this.rulerStart.x;\n      var dy = this.rulerEnd.y - this.rulerStart.y;\n      var pixelDistance = Math.sqrt(dx * dx + dy * dy);\n      var meterDistance = pixelDistance / this.gridSize * 1.5;\n      return Math.round(meterDistance * 10) / 10;\n    },\n    getMovementPath: function getMovementPath() {\n      if (!this.movementPreview) return '';\n      var start = this.movementPreview.start;\n      var end = this.movementPreview.end;\n      return \"M \".concat(start.x * this.gridSize * this.zoom, \" \").concat(start.y * this.gridSize * this.zoom, \" L \").concat(end.x * this.gridSize * this.zoom, \" \").concat(end.y * this.gridSize * this.zoom);\n    },\n    getMonsterIcon: function getMonsterIcon(monster) {\n      var iconMap = {\n        'human': 'fas fa-user',\n        'animal': 'fas fa-paw',\n        'mythos': 'fas fa-eye',\n        'undead': 'fas fa-skull',\n        'demon': 'fas fa-fire',\n        'construct': 'fas fa-robot'\n      };\n      return iconMap[monster.type] || 'fas fa-question';\n    },\n    /**\r\n     * 动画管理\r\n     */\n    addAnimation: function addAnimation(animation) {\n      this.activeAnimations.push(_objectSpread(_objectSpread({}, animation), {}, {\n        id: Date.now() + Math.random()\n      }));\n    },\n    removeAnimation: function removeAnimation(animationId) {\n      var index = this.activeAnimations.findIndex(function (a) {\n        return a.id === animationId;\n      });\n      if (index !== -1) {\n        this.activeAnimations.splice(index, 1);\n      }\n    },\n    /**\r\n     * 播放攻击动画\r\n     */\n    playAttackAnimation: function playAttackAnimation(attacker, target, result) {\n      this.addAnimation({\n        type: 'attack',\n        from: attacker.position,\n        to: target.position,\n        result: result,\n        duration: 1000\n      });\n    },\n    /**\r\n     * 播放移动动画\r\n     */\n    playMovementAnimation: function playMovementAnimation(character, from, to) {\n      this.addAnimation({\n        type: 'movement',\n        character: character,\n        from: from,\n        to: to,\n        duration: 800\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["CharacterToken", "MonsterToken", "CombatAnimation", "name", "components", "props", "characters", "type", "Array", "default", "monsters", "obstacles", "areaEffects", "currentRound", "Number", "currentTurnCharacter", "Object", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "battlefieldWidth", "battlefieldHeight", "gridSize", "data", "zoom", "viewBox", "svgWidth", "svgHeight", "showGrid", "showRuler", "showAttackRange", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "isDragging", "dragStart", "movementPreview", "rulerS<PERSON>t", "rulerEnd", "contextMenu", "show", "target", "x", "y", "activeAnimations", "computed", "calculatedViewBox", "width", "height", "concat", "mounted", "initializeBattlefield", "setupEventListeners", "beforeUnmount", "removeEventListeners", "methods", "updateViewBox", "centerView", "document", "addEventListener", "handleDocumentClick", "handleKeyDown", "window", "handleResize", "removeEventListener", "zoomIn", "Math", "min", "zoomOut", "max", "container", "$refs", "battlefieldContainer", "scrollLeft", "clientWidth", "scrollTop", "clientHeight", "fitToScreen", "scaleX", "scaleY", "resetView", "to<PERSON><PERSON><PERSON>", "toggleRuler", "handleMouseDown", "event", "rect", "battlefieldSvg", "getBoundingClientRect", "clientX", "left", "clientY", "top", "handleMouseMove", "handleMouseUp", "handleWheel", "preventDefault", "deltaY", "closest", "key", "clearSelection", "ctrl<PERSON>ey", "_this", "$nextTick", "selectCharacter", "character", "$emit", "selectMonster", "monster", "moveCharacter", "newPosition", "oldPosition", "position", "moveMonster", "startMovement", "start", "_objectSpread", "end", "characterAction", "action", "monsterAction", "showCharacterMenu", "showMonsterMenu", "inspectCharacter", "inspectMonster", "edit<PERSON><PERSON><PERSON>", "editMonster", "controlMonster", "removeMonster", "addMonster", "showActionMenu", "canMoveCharacter", "_this$currentTurnChar", "id", "hasActed", "canActCharacter", "_this$currentTurnChar2", "getAttackRange", "weapon", "equippedWeapon", "reach", "_weapon$range", "range", "base", "getRulerDistance", "dx", "dy", "pixelDistance", "sqrt", "meterDistance", "round", "getMovementPath", "getMonsterIcon", "iconMap", "addAnimation", "animation", "push", "Date", "now", "random", "removeAnimation", "animationId", "index", "findIndex", "a", "splice", "playAttackAnimation", "attacker", "result", "from", "to", "duration", "playMovementAnimation"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\BattlefieldGrid.vue"], "sourcesContent": ["<template>\r\n  <!-- 2D战场网格系统 -->\r\n  <div class=\"battlefield-container\">\r\n    <!-- 战场工具栏 -->\r\n    <div class=\"battlefield-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <div class=\"zoom-controls\">\r\n          <button @click=\"zoomOut\" class=\"zoom-btn\" :disabled=\"zoom <= 0.5\">\r\n            <i class=\"fas fa-search-minus\"></i>\r\n          </button>\r\n          <span class=\"zoom-display\">{{ Math.round(zoom * 100) }}%</span>\r\n          <button @click=\"zoomIn\" class=\"zoom-btn\" :disabled=\"zoom >= 2\">\r\n            <i class=\"fas fa-search-plus\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"grid-controls\">\r\n          <button @click=\"toggleGrid\" class=\"grid-btn\" :class=\"{ active: showGrid }\">\r\n            <i class=\"fas fa-th\"></i>\r\n            网格\r\n          </button>\r\n          <button @click=\"toggleRuler\" class=\"ruler-btn\" :class=\"{ active: showRuler }\">\r\n            <i class=\"fas fa-ruler\"></i>\r\n            测距\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"toolbar-center\">\r\n        <div class=\"battlefield-info\">\r\n          <span class=\"round-info\">第{{ currentRound }}轮</span>\r\n          <span class=\"turn-info\">{{ currentTurnCharacter?.name || '等待开始' }}的回合</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"toolbar-right\">\r\n        <div class=\"view-controls\">\r\n          <button @click=\"centerView\" class=\"center-btn\">\r\n            <i class=\"fas fa-crosshairs\"></i>\r\n            居中\r\n          </button>\r\n          <button @click=\"fitToScreen\" class=\"fit-btn\">\r\n            <i class=\"fas fa-expand-arrows-alt\"></i>\r\n            适应屏幕\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战场主体 -->\r\n    <div class=\"battlefield-main\" ref=\"battlefieldContainer\">\r\n      <!-- SVG战场 -->\r\n      <svg \r\n        ref=\"battlefieldSvg\"\r\n        class=\"battlefield-svg\"\r\n        :width=\"svgWidth\"\r\n        :height=\"svgHeight\"\r\n        :viewBox=\"viewBox\"\r\n        @mousedown=\"handleMouseDown\"\r\n        @mousemove=\"handleMouseMove\"\r\n        @mouseup=\"handleMouseUp\"\r\n        @wheel=\"handleWheel\"\r\n      >\r\n        <!-- 定义 -->\r\n        <defs>\r\n          <!-- 网格图案 -->\r\n          <pattern id=\"grid\" :width=\"gridSize * zoom\" :height=\"gridSize * zoom\" patternUnits=\"userSpaceOnUse\">\r\n            <path \r\n              :d=\"`M ${gridSize * zoom} 0 L 0 0 0 ${gridSize * zoom}`\" \r\n              fill=\"none\" \r\n              stroke=\"#e0e0e0\" \r\n              stroke-width=\"1\"\r\n              opacity=\"0.5\"\r\n            />\r\n          </pattern>\r\n          <!-- 角色阴影滤镜 -->\r\n          <filter id=\"character-shadow\">\r\n            <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"3\" flood-opacity=\"0.3\"/>\r\n          </filter>\r\n          <!-- 选中效果滤镜 -->\r\n          <filter id=\"selection-glow\">\r\n            <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\r\n            <feMerge> \r\n              <feMergeNode in=\"coloredBlur\"/>\r\n              <feMergeNode in=\"SourceGraphic\"/>\r\n            </feMerge>\r\n          </filter>\r\n        </defs>\r\n\r\n        <!-- 背景 -->\r\n        <rect \r\n          width=\"100%\" \r\n          height=\"100%\" \r\n          :fill=\"showGrid ? 'url(#grid)' : '#f8f9fa'\"\r\n        />\r\n\r\n        <!-- 地形和障碍物 -->\r\n        <g class=\"terrain-layer\">\r\n          <rect \r\n            v-for=\"obstacle in obstacles\" \r\n            :key=\"obstacle.id\"\r\n            :x=\"obstacle.x * gridSize * zoom\"\r\n            :y=\"obstacle.y * gridSize * zoom\"\r\n            :width=\"obstacle.width * gridSize * zoom\"\r\n            :height=\"obstacle.height * gridSize * zoom\"\r\n            :fill=\"obstacle.color || '#8b4513'\"\r\n            :opacity=\"obstacle.opacity || 0.7\"\r\n            stroke=\"#654321\"\r\n            stroke-width=\"2\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 测距线 -->\r\n        <g class=\"ruler-layer\" v-if=\"showRuler && rulerStart && rulerEnd\">\r\n          <line \r\n            :x1=\"rulerStart.x * zoom\"\r\n            :y1=\"rulerStart.y * zoom\"\r\n            :x2=\"rulerEnd.x * zoom\"\r\n            :y2=\"rulerEnd.y * zoom\"\r\n            stroke=\"#ff6b6b\"\r\n            stroke-width=\"2\"\r\n            stroke-dasharray=\"5,5\"\r\n          />\r\n          <text \r\n            :x=\"(rulerStart.x + rulerEnd.x) / 2 * zoom\"\r\n            :y=\"(rulerStart.y + rulerEnd.y) / 2 * zoom - 10\"\r\n            text-anchor=\"middle\"\r\n            fill=\"#ff6b6b\"\r\n            font-size=\"14\"\r\n            font-weight=\"bold\"\r\n          >\r\n            {{ getRulerDistance() }}米\r\n          </text>\r\n        </g>\r\n\r\n        <!-- 移动路径预览 -->\r\n        <g class=\"movement-preview\" v-if=\"movementPreview\">\r\n          <path \r\n            :d=\"getMovementPath()\"\r\n            fill=\"none\"\r\n            stroke=\"#4ecdc4\"\r\n            stroke-width=\"3\"\r\n            stroke-dasharray=\"8,4\"\r\n            opacity=\"0.8\"\r\n          />\r\n          <circle \r\n            :cx=\"movementPreview.end.x * gridSize * zoom\"\r\n            :cy=\"movementPreview.end.y * gridSize * zoom\"\r\n            :r=\"8 * zoom\"\r\n            fill=\"#4ecdc4\"\r\n            opacity=\"0.6\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 攻击范围显示 -->\r\n        <g class=\"attack-range\" v-if=\"selectedCharacter && showAttackRange\">\r\n          <circle \r\n            :cx=\"selectedCharacter.position.x * gridSize * zoom\"\r\n            :cy=\"selectedCharacter.position.y * gridSize * zoom\"\r\n            :r=\"getAttackRange(selectedCharacter) * gridSize * zoom\"\r\n            fill=\"rgba(255, 107, 107, 0.2)\"\r\n            stroke=\"#ff6b6b\"\r\n            stroke-width=\"2\"\r\n            stroke-dasharray=\"5,5\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 角色层 -->\r\n        <g class=\"characters-layer\">\r\n          <CharacterToken\r\n            v-for=\"character in characters\"\r\n            :key=\"character.id\"\r\n            :character=\"character\"\r\n            :grid-size=\"gridSize\"\r\n            :zoom=\"zoom\"\r\n            :selected=\"selectedCharacter?.id === character.id\"\r\n            :current-turn=\"currentTurnCharacter?.id === character.id\"\r\n            :can-move=\"canMoveCharacter(character)\"\r\n            :can-act=\"canActCharacter(character)\"\r\n            @select=\"selectCharacter\"\r\n            @move=\"moveCharacter\"\r\n            @action=\"characterAction\"\r\n            @context-menu=\"showCharacterMenu\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 怪物层 -->\r\n        <g class=\"monsters-layer\">\r\n          <MonsterToken\r\n            v-for=\"monster in monsters\"\r\n            :key=\"monster.id\"\r\n            :monster=\"monster\"\r\n            :grid-size=\"gridSize\"\r\n            :zoom=\"zoom\"\r\n            :selected=\"selectedMonster?.id === monster.id\"\r\n            :can-control=\"isKeeper\"\r\n            @select=\"selectMonster\"\r\n            @move=\"moveMonster\"\r\n            @action=\"monsterAction\"\r\n            @context-menu=\"showMonsterMenu\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 效果层 -->\r\n        <g class=\"effects-layer\">\r\n          <!-- 法术效果区域 -->\r\n          <circle \r\n            v-for=\"effect in areaEffects\" \r\n            :key=\"effect.id\"\r\n            :cx=\"effect.position.x * gridSize * zoom\"\r\n            :cy=\"effect.position.y * gridSize * zoom\"\r\n            :r=\"effect.radius * gridSize * zoom\"\r\n            :fill=\"effect.color\"\r\n            :opacity=\"effect.opacity || 0.3\"\r\n            :stroke=\"effect.borderColor || effect.color\"\r\n            stroke-width=\"2\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 动画层 -->\r\n        <g class=\"animations-layer\">\r\n          <CombatAnimation\r\n            v-for=\"animation in activeAnimations\"\r\n            :key=\"animation.id\"\r\n            :animation=\"animation\"\r\n            :grid-size=\"gridSize\"\r\n            :zoom=\"zoom\"\r\n            @complete=\"removeAnimation\"\r\n          />\r\n        </g>\r\n      </svg>\r\n\r\n      <!-- 角色右键菜单 -->\r\n      <div \r\n        v-if=\"contextMenu.show && contextMenu.type === 'character'\"\r\n        class=\"context-menu\"\r\n        :style=\"{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }\"\r\n      >\r\n        <div class=\"menu-header\">\r\n          <img :src=\"contextMenu.target.avatar\" :alt=\"contextMenu.target.name\">\r\n          <span>{{ contextMenu.target.name }}</span>\r\n        </div>\r\n        <div class=\"menu-items\">\r\n          <button @click=\"inspectCharacter(contextMenu.target)\" class=\"menu-item\">\r\n            <i class=\"fas fa-search\"></i>\r\n            查看详情\r\n          </button>\r\n          <button \r\n            v-if=\"canMoveCharacter(contextMenu.target)\"\r\n            @click=\"startMovement(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-walking\"></i>\r\n            移动\r\n          </button>\r\n          <button \r\n            v-if=\"canActCharacter(contextMenu.target)\"\r\n            @click=\"showActionMenu(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-fist-raised\"></i>\r\n            行动\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"editCharacter(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-edit\"></i>\r\n            编辑\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 怪物右键菜单 -->\r\n      <div \r\n        v-if=\"contextMenu.show && contextMenu.type === 'monster'\"\r\n        class=\"context-menu\"\r\n        :style=\"{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }\"\r\n      >\r\n        <div class=\"menu-header\">\r\n          <div class=\"monster-icon\">\r\n            <i :class=\"getMonsterIcon(contextMenu.target)\"></i>\r\n          </div>\r\n          <span>{{ contextMenu.target.name }}</span>\r\n        </div>\r\n        <div class=\"menu-items\">\r\n          <button @click=\"inspectMonster(contextMenu.target)\" class=\"menu-item\">\r\n            <i class=\"fas fa-search\"></i>\r\n            查看详情\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"controlMonster(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-gamepad\"></i>\r\n            控制\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"editMonster(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-edit\"></i>\r\n            编辑\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"removeMonster(contextMenu.target)\" \r\n            class=\"menu-item danger\"\r\n          >\r\n            <i class=\"fas fa-trash\"></i>\r\n            移除\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战场状态栏 -->\r\n    <div class=\"battlefield-status\">\r\n      <div class=\"status-left\">\r\n        <div class=\"selected-info\" v-if=\"selectedCharacter || selectedMonster\">\r\n          <div class=\"selected-avatar\">\r\n            <img \r\n              v-if=\"selectedCharacter\" \r\n              :src=\"selectedCharacter.avatar\" \r\n              :alt=\"selectedCharacter.name\"\r\n            >\r\n            <div v-else-if=\"selectedMonster\" class=\"monster-avatar\">\r\n              <i :class=\"getMonsterIcon(selectedMonster)\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"selected-details\">\r\n            <div class=\"selected-name\">\r\n              {{ (selectedCharacter || selectedMonster)?.name }}\r\n            </div>\r\n            <div class=\"selected-stats\">\r\n              <span class=\"hp\">\r\n                HP: {{ (selectedCharacter || selectedMonster)?.currentHP }}/{{ (selectedCharacter || selectedMonster)?.maxHP }}\r\n              </span>\r\n              <span class=\"position\">\r\n                位置: ({{ (selectedCharacter || selectedMonster)?.position.x }}, {{ (selectedCharacter || selectedMonster)?.position.y }})\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"status-center\">\r\n        <div class=\"battlefield-stats\">\r\n          <span class=\"stat-item\">\r\n            <i class=\"fas fa-users\"></i>\r\n            角色: {{ characters.length }}\r\n          </span>\r\n          <span class=\"stat-item\">\r\n            <i class=\"fas fa-dragon\"></i>\r\n            怪物: {{ monsters.length }}\r\n          </span>\r\n          <span class=\"stat-item\">\r\n            <i class=\"fas fa-map\"></i>\r\n            {{ battlefieldWidth }}×{{ battlefieldHeight }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div class=\"status-right\">\r\n        <div class=\"action-buttons\">\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"addMonster\" \r\n            class=\"action-btn\"\r\n          >\r\n            <i class=\"fas fa-plus\"></i>\r\n            添加怪物\r\n          </button>\r\n          <button \r\n            @click=\"resetView\" \r\n            class=\"action-btn\"\r\n          >\r\n            <i class=\"fas fa-home\"></i>\r\n            重置视图\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CharacterToken from './CharacterToken.vue'\r\nimport MonsterToken from './MonsterToken.vue'\r\nimport CombatAnimation from './CombatAnimation.vue'\r\n\r\nexport default {\r\n  name: 'BattlefieldGrid',\r\n  components: {\r\n    CharacterToken,\r\n    MonsterToken,\r\n    CombatAnimation\r\n  },\r\n  props: {\r\n    characters: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    monsters: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    obstacles: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    areaEffects: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    currentRound: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    currentTurnCharacter: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    isKeeper: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    battlefieldWidth: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    battlefieldHeight: {\r\n      type: Number,\r\n      default: 15\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      // 视图控制\r\n      zoom: 1,\r\n      viewBox: '0 0 800 600',\r\n      svgWidth: 800,\r\n      svgHeight: 600,\r\n      // 显示控制\r\n      showGrid: true,\r\n      showRuler: false,\r\n      showAttackRange: false,\r\n      // 选择状态\r\n      selectedCharacter: null,\r\n      selectedMonster: null,\r\n      // 交互状态\r\n      isDragging: false,\r\n      dragStart: null,\r\n      movementPreview: null,\r\n      // 测距工具\r\n      rulerStart: null,\r\n      rulerEnd: null,\r\n      // 右键菜单\r\n      contextMenu: {\r\n        show: false,\r\n        type: null,\r\n        target: null,\r\n        x: 0,\r\n        y: 0\r\n      },\r\n      // 动画\r\n      activeAnimations: []\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 计算SVG视图框\r\n    calculatedViewBox() {\r\n      const width = this.battlefieldWidth * this.gridSize\r\n      const height = this.battlefieldHeight * this.gridSize\r\n      return `0 0 ${width} ${height}`\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.initializeBattlefield()\r\n    this.setupEventListeners()\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    this.removeEventListeners()\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 初始化战场\r\n     */\r\n    initializeBattlefield() {\r\n      this.updateViewBox()\r\n      this.centerView()\r\n    },\r\n    \r\n    /**\r\n     * 设置事件监听器\r\n     */\r\n    setupEventListeners() {\r\n      document.addEventListener('click', this.handleDocumentClick)\r\n      document.addEventListener('keydown', this.handleKeyDown)\r\n      window.addEventListener('resize', this.handleResize)\r\n    },\r\n    \r\n    /**\r\n     * 移除事件监听器\r\n     */\r\n    removeEventListeners() {\r\n      document.removeEventListener('click', this.handleDocumentClick)\r\n      document.removeEventListener('keydown', this.handleKeyDown)\r\n      window.removeEventListener('resize', this.handleResize)\r\n    },\r\n    \r\n    /**\r\n     * 更新视图框\r\n     */\r\n    updateViewBox() {\r\n      const width = this.battlefieldWidth * this.gridSize\r\n      const height = this.battlefieldHeight * this.gridSize\r\n      this.viewBox = `0 0 ${width} ${height}`\r\n      this.svgWidth = width * this.zoom\r\n      this.svgHeight = height * this.zoom\r\n    },\r\n    \r\n    /**\r\n     * 缩放控制\r\n     */\r\n    zoomIn() {\r\n      this.zoom = Math.min(2, this.zoom + 0.1)\r\n      this.updateViewBox()\r\n    },\r\n    \r\n    zoomOut() {\r\n      this.zoom = Math.max(0.5, this.zoom - 0.1)\r\n      this.updateViewBox()\r\n    },\r\n    \r\n    /**\r\n     * 视图控制\r\n     */\r\n    centerView() {\r\n      const container = this.$refs.battlefieldContainer\r\n      if (container) {\r\n        container.scrollLeft = (this.svgWidth - container.clientWidth) / 2\r\n        container.scrollTop = (this.svgHeight - container.clientHeight) / 2\r\n      }\r\n    },\r\n    \r\n    fitToScreen() {\r\n      const container = this.$refs.battlefieldContainer\r\n      if (container) {\r\n        const scaleX = container.clientWidth / (this.battlefieldWidth * this.gridSize)\r\n        const scaleY = container.clientHeight / (this.battlefieldHeight * this.gridSize)\r\n        this.zoom = Math.min(scaleX, scaleY, 2)\r\n        this.updateViewBox()\r\n        this.centerView()\r\n      }\r\n    },\r\n    \r\n    resetView() {\r\n      this.zoom = 1\r\n      this.updateViewBox()\r\n      this.centerView()\r\n    },\r\n    \r\n    /**\r\n     * 网格和工具切换\r\n     */\r\n    toggleGrid() {\r\n      this.showGrid = !this.showGrid\r\n    },\r\n    \r\n    toggleRuler() {\r\n      this.showRuler = !this.showRuler\r\n      if (!this.showRuler) {\r\n        this.rulerStart = null\r\n        this.rulerEnd = null\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 鼠标事件处理\r\n     */\r\n    handleMouseDown(event) {\r\n      if (this.showRuler) {\r\n        const rect = this.$refs.battlefieldSvg.getBoundingClientRect()\r\n        const x = (event.clientX - rect.left) / this.zoom\r\n        const y = (event.clientY - rect.top) / this.zoom\r\n        \r\n        if (!this.rulerStart) {\r\n          this.rulerStart = { x, y }\r\n        } else {\r\n          this.rulerEnd = { x, y }\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleMouseMove(event) {\r\n      if (this.showRuler && this.rulerStart && !this.rulerEnd) {\r\n        const rect = this.$refs.battlefieldSvg.getBoundingClientRect()\r\n        const x = (event.clientX - rect.left) / this.zoom\r\n        const y = (event.clientY - rect.top) / this.zoom\r\n        this.rulerEnd = { x, y }\r\n      }\r\n    },\r\n    \r\n    handleMouseUp(event) {\r\n      // 处理鼠标释放\r\n    },\r\n    \r\n    handleWheel(event) {\r\n      event.preventDefault()\r\n      if (event.deltaY < 0) {\r\n        this.zoomIn()\r\n      } else {\r\n        this.zoomOut()\r\n      }\r\n    },\r\n    \r\n    handleDocumentClick(event) {\r\n      if (!event.target.closest('.context-menu')) {\r\n        this.contextMenu.show = false\r\n      }\r\n    },\r\n    \r\n    handleKeyDown(event) {\r\n      switch (event.key) {\r\n        case 'Escape':\r\n          this.clearSelection()\r\n          this.contextMenu.show = false\r\n          break\r\n        case 'g':\r\n        case 'G':\r\n          if (event.ctrlKey) {\r\n            event.preventDefault()\r\n            this.toggleGrid()\r\n          }\r\n          break\r\n        case 'r':\r\n        case 'R':\r\n          if (event.ctrlKey) {\r\n            event.preventDefault()\r\n            this.toggleRuler()\r\n          }\r\n          break\r\n      }\r\n    },\r\n    \r\n    handleResize() {\r\n      this.$nextTick(() => {\r\n        this.updateViewBox()\r\n      })\r\n    },\r\n    \r\n    /**\r\n     * 角色选择和操作\r\n     */\r\n    selectCharacter(character) {\r\n      this.selectedCharacter = character\r\n      this.selectedMonster = null\r\n      this.showAttackRange = true\r\n      this.$emit('character-selected', character)\r\n    },\r\n    \r\n    selectMonster(monster) {\r\n      this.selectedMonster = monster\r\n      this.selectedCharacter = null\r\n      this.showAttackRange = false\r\n      this.$emit('monster-selected', monster)\r\n    },\r\n    \r\n    clearSelection() {\r\n      this.selectedCharacter = null\r\n      this.selectedMonster = null\r\n      this.showAttackRange = false\r\n      this.movementPreview = null\r\n    },\r\n    \r\n    /**\r\n     * 移动相关\r\n     */\r\n    moveCharacter(character, newPosition) {\r\n      this.$emit('character-moved', {\r\n        character,\r\n        oldPosition: character.position,\r\n        newPosition\r\n      })\r\n    },\r\n    \r\n    moveMonster(monster, newPosition) {\r\n      this.$emit('monster-moved', {\r\n        monster,\r\n        oldPosition: monster.position,\r\n        newPosition\r\n      })\r\n    },\r\n    \r\n    startMovement(target) {\r\n      this.movementPreview = {\r\n        target,\r\n        start: { ...target.position },\r\n        end: { ...target.position }\r\n      }\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    /**\r\n     * 行动相关\r\n     */\r\n    characterAction(character, action) {\r\n      this.$emit('character-action', { character, action })\r\n    },\r\n    \r\n    monsterAction(monster, action) {\r\n      this.$emit('monster-action', { monster, action })\r\n    },\r\n    \r\n    /**\r\n     * 右键菜单\r\n     */\r\n    showCharacterMenu(character, event) {\r\n      this.contextMenu = {\r\n        show: true,\r\n        type: 'character',\r\n        target: character,\r\n        x: event.clientX,\r\n        y: event.clientY\r\n      }\r\n    },\r\n    \r\n    showMonsterMenu(monster, event) {\r\n      this.contextMenu = {\r\n        show: true,\r\n        type: 'monster',\r\n        target: monster,\r\n        x: event.clientX,\r\n        y: event.clientY\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 菜单操作\r\n     */\r\n    inspectCharacter(character) {\r\n      this.$emit('inspect-character', character)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    inspectMonster(monster) {\r\n      this.$emit('inspect-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    editCharacter(character) {\r\n      this.$emit('edit-character', character)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    editMonster(monster) {\r\n      this.$emit('edit-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    controlMonster(monster) {\r\n      this.$emit('control-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    removeMonster(monster) {\r\n      this.$emit('remove-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    addMonster() {\r\n      this.$emit('add-monster')\r\n    },\r\n    \r\n    showActionMenu(character) {\r\n      this.$emit('show-action-menu', character)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    /**\r\n     * 工具函数\r\n     */\r\n    canMoveCharacter(character) {\r\n      return this.currentTurnCharacter?.id === character.id && !character.hasActed\r\n    },\r\n    \r\n    canActCharacter(character) {\r\n      return this.currentTurnCharacter?.id === character.id && !character.hasActed\r\n    },\r\n    \r\n    getAttackRange(character) {\r\n      const weapon = character.equippedWeapon\r\n      if (!weapon) return 1.5\r\n      \r\n      if (weapon.type === 'melee') {\r\n        return weapon.reach || 1.5\r\n      } else if (weapon.type === 'ranged') {\r\n        return weapon.range?.base || 10\r\n      }\r\n      \r\n      return 1.5\r\n    },\r\n    \r\n    getRulerDistance() {\r\n      if (!this.rulerStart || !this.rulerEnd) return 0\r\n      \r\n      const dx = this.rulerEnd.x - this.rulerStart.x\r\n      const dy = this.rulerEnd.y - this.rulerStart.y\r\n      const pixelDistance = Math.sqrt(dx * dx + dy * dy)\r\n      const meterDistance = (pixelDistance / this.gridSize) * 1.5\r\n      \r\n      return Math.round(meterDistance * 10) / 10\r\n    },\r\n    \r\n    getMovementPath() {\r\n      if (!this.movementPreview) return ''\r\n      \r\n      const start = this.movementPreview.start\r\n      const end = this.movementPreview.end\r\n      \r\n      return `M ${start.x * this.gridSize * this.zoom} ${start.y * this.gridSize * this.zoom} L ${end.x * this.gridSize * this.zoom} ${end.y * this.gridSize * this.zoom}`\r\n    },\r\n    \r\n    getMonsterIcon(monster) {\r\n      const iconMap = {\r\n        'human': 'fas fa-user',\r\n        'animal': 'fas fa-paw',\r\n        'mythos': 'fas fa-eye',\r\n        'undead': 'fas fa-skull',\r\n        'demon': 'fas fa-fire',\r\n        'construct': 'fas fa-robot'\r\n      }\r\n      \r\n      return iconMap[monster.type] || 'fas fa-question'\r\n    },\r\n    \r\n    /**\r\n     * 动画管理\r\n     */\r\n    addAnimation(animation) {\r\n      this.activeAnimations.push({\r\n        ...animation,\r\n        id: Date.now() + Math.random()\r\n      })\r\n    },\r\n    \r\n    removeAnimation(animationId) {\r\n      const index = this.activeAnimations.findIndex(a => a.id === animationId)\r\n      if (index !== -1) {\r\n        this.activeAnimations.splice(index, 1)\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAttackAnimation(attacker, target, result) {\r\n      this.addAnimation({\r\n        type: 'attack',\r\n        from: attacker.position,\r\n        to: target.position,\r\n        result,\r\n        duration: 1000\r\n      })\r\n    },\r\n    \r\n    /**\r\n     * 播放移动动画\r\n     */\r\n    playMovementAnimation(character, from, to) {\r\n      this.addAnimation({\r\n        type: 'movement',\r\n        character,\r\n        from,\r\n        to,\r\n        duration: 800\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.battlefield-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.battlefield-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: linear-gradient(135deg, #2c3e50, #34495e);\r\n  color: white;\r\n  border-bottom: 2px solid #e74c3c;\r\n}\r\n\r\n.toolbar-left,\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.toolbar-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.zoom-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n}\r\n\r\n.zoom-btn {\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.zoom-btn:hover:not(:disabled) {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.zoom-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.zoom-display {\r\n  font-weight: bold;\r\n  min-width: 50px;\r\n  text-align: center;\r\n}\r\n\r\n.grid-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.grid-btn,\r\n.ruler-btn {\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.grid-btn:hover,\r\n.ruler-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.grid-btn.active,\r\n.ruler-btn.active {\r\n  background: #e74c3c;\r\n  border-color: #e74c3c;\r\n}\r\n\r\n.battlefield-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.round-info {\r\n  color: #e74c3c;\r\n  font-size: 1.1em;\r\n}\r\n\r\n.turn-info {\r\n  color: #3498db;\r\n}\r\n\r\n.view-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.center-btn,\r\n.fit-btn {\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.center-btn:hover,\r\n.fit-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.battlefield-main {\r\n  flex: 1;\r\n  overflow: auto;\r\n  position: relative;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.battlefield-svg {\r\n  display: block;\r\n  cursor: crosshair;\r\n}\r\n\r\n.context-menu {\r\n  position: fixed;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);\r\n  z-index: 1000;\r\n  min-width: 200px;\r\n  overflow: hidden;\r\n}\r\n\r\n.menu-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.menu-header img {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.monster-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #6c757d;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 16px;\r\n}\r\n\r\n.menu-items {\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-item {\r\n  width: 100%;\r\n  padding: 10px 16px;\r\n  border: none;\r\n  background: none;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  transition: background 0.2s ease;\r\n}\r\n\r\n.menu-item:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.menu-item.danger {\r\n  color: #dc3545;\r\n}\r\n\r\n.menu-item.danger:hover {\r\n  background: #f8d7da;\r\n}\r\n\r\n.battlefield-status {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: white;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n.selected-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.selected-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.monster-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #6c757d;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 18px;\r\n}\r\n\r\n.selected-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.selected-name {\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.selected-stats {\r\n  display: flex;\r\n  gap: 16px;\r\n  font-size: 0.9em;\r\n  color: #6c757d;\r\n}\r\n\r\n.battlefield-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #6c757d;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.action-btn {\r\n  background: #007bff;\r\n  color: white;\r\n  border: none;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: #0056b3;\r\n}\r\n</style>"], "mappings": ";;;;;;;AAiYA,OAAOA,cAAa,MAAO,sBAAqB;AAChD,OAAOC,YAAW,MAAO,oBAAmB;AAC5C,OAAOC,eAAc,MAAO,uBAAsB;AAElD,eAAe;EACbC,IAAI,EAAE,iBAAiB;EACvBC,UAAU,EAAE;IACVJ,cAAc,EAAdA,cAAc;IACdC,YAAY,EAAZA,YAAY;IACZC,eAAc,EAAdA;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,UAAU,EAAE;MACVC,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDC,QAAQ,EAAE;MACRH,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDE,SAAS,EAAE;MACTJ,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDG,WAAW,EAAE;MACXL,IAAI,EAAEC,KAAK;MACX,WAAS,SAATC,QAAOA,CAAA;QAAA,OAAQ,EAAC;MAAA;IAClB,CAAC;IACDI,YAAY,EAAE;MACZN,IAAI,EAAEO,MAAM;MACZ,WAAS;IACX,CAAC;IACDC,oBAAoB,EAAE;MACpBR,IAAI,EAAES,MAAM;MACZ,WAAS;IACX,CAAC;IACDC,QAAQ,EAAE;MACRV,IAAI,EAAEW,OAAO;MACb,WAAS;IACX,CAAC;IACDC,gBAAgB,EAAE;MAChBZ,IAAI,EAAEO,MAAM;MACZ,WAAS;IACX,CAAC;IACDM,iBAAiB,EAAE;MACjBb,IAAI,EAAEO,MAAM;MACZ,WAAS;IACX,CAAC;IACDO,QAAQ,EAAE;MACRd,IAAI,EAAEO,MAAM;MACZ,WAAS;IACX;EACF,CAAC;EAEDQ,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACL;MACAC,IAAI,EAAE,CAAC;MACPC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,GAAG;MACbC,SAAS,EAAE,GAAG;MACd;MACAC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,KAAK;MAChBC,eAAe,EAAE,KAAK;MACtB;MACAC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrB;MACAC,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,IAAI;MACfC,eAAe,EAAE,IAAI;MACrB;MACAC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,IAAI;MACd;MACAC,WAAW,EAAE;QACXC,IAAI,EAAE,KAAK;QACX/B,IAAI,EAAE,IAAI;QACVgC,MAAM,EAAE,IAAI;QACZC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE;MACL,CAAC;MACD;MACAC,gBAAgB,EAAE;IACpB;EACF,CAAC;EAEDC,QAAQ,EAAE;IACR;IACAC,iBAAiB,WAAjBA,iBAAiBA,CAAA,EAAG;MAClB,IAAMC,KAAI,GAAI,IAAI,CAAC1B,gBAAe,GAAI,IAAI,CAACE,QAAO;MAClD,IAAMyB,MAAK,GAAI,IAAI,CAAC1B,iBAAgB,GAAI,IAAI,CAACC,QAAO;MACpD,cAAA0B,MAAA,CAAcF,KAAK,OAAAE,MAAA,CAAID,MAAM;IAC/B;EACF,CAAC;EAEDE,OAAO,WAAPA,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,qBAAqB,CAAC;IAC3B,IAAI,CAACC,mBAAmB,CAAC;EAC3B,CAAC;EAEDC,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,oBAAoB,CAAC;EAC5B,CAAC;EAEDC,OAAO,EAAE;IACP;;;IAGAJ,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAACK,aAAa,CAAC;MACnB,IAAI,CAACC,UAAU,CAAC;IAClB,CAAC;IAED;;;IAGAL,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpBM,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,mBAAmB;MAC3DF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACE,aAAa;MACvDC,MAAM,CAACH,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACI,YAAY;IACrD,CAAC;IAED;;;IAGAT,oBAAoB,WAApBA,oBAAoBA,CAAA,EAAG;MACrBI,QAAQ,CAACM,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACJ,mBAAmB;MAC9DF,QAAQ,CAACM,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACH,aAAa;MAC1DC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACD,YAAY;IACxD,CAAC;IAED;;;IAGAP,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAMT,KAAI,GAAI,IAAI,CAAC1B,gBAAe,GAAI,IAAI,CAACE,QAAO;MAClD,IAAMyB,MAAK,GAAI,IAAI,CAAC1B,iBAAgB,GAAI,IAAI,CAACC,QAAO;MACpD,IAAI,CAACG,OAAM,UAAAuB,MAAA,CAAWF,KAAK,OAAAE,MAAA,CAAID,MAAM,CAAC;MACtC,IAAI,CAACrB,QAAO,GAAIoB,KAAI,GAAI,IAAI,CAACtB,IAAG;MAChC,IAAI,CAACG,SAAQ,GAAIoB,MAAK,GAAI,IAAI,CAACvB,IAAG;IACpC,CAAC;IAED;;;IAGAwC,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAI,CAACxC,IAAG,GAAIyC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC1C,IAAG,GAAI,GAAG;MACvC,IAAI,CAAC+B,aAAa,CAAC;IACrB,CAAC;IAEDY,OAAO,WAAPA,OAAOA,CAAA,EAAG;MACR,IAAI,CAAC3C,IAAG,GAAIyC,IAAI,CAACG,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC5C,IAAG,GAAI,GAAG;MACzC,IAAI,CAAC+B,aAAa,CAAC;IACrB,CAAC;IAED;;;IAGAC,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAMa,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,oBAAmB;MAChD,IAAIF,SAAS,EAAE;QACbA,SAAS,CAACG,UAAS,GAAI,CAAC,IAAI,CAAC9C,QAAO,GAAI2C,SAAS,CAACI,WAAW,IAAI;QACjEJ,SAAS,CAACK,SAAQ,GAAI,CAAC,IAAI,CAAC/C,SAAQ,GAAI0C,SAAS,CAACM,YAAY,IAAI;MACpE;IACF,CAAC;IAEDC,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAMP,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,oBAAmB;MAChD,IAAIF,SAAS,EAAE;QACb,IAAMQ,MAAK,GAAIR,SAAS,CAACI,WAAU,IAAK,IAAI,CAACrD,gBAAe,GAAI,IAAI,CAACE,QAAQ;QAC7E,IAAMwD,MAAK,GAAIT,SAAS,CAACM,YAAW,IAAK,IAAI,CAACtD,iBAAgB,GAAI,IAAI,CAACC,QAAQ;QAC/E,IAAI,CAACE,IAAG,GAAIyC,IAAI,CAACC,GAAG,CAACW,MAAM,EAAEC,MAAM,EAAE,CAAC;QACtC,IAAI,CAACvB,aAAa,CAAC;QACnB,IAAI,CAACC,UAAU,CAAC;MAClB;IACF,CAAC;IAEDuB,SAAS,WAATA,SAASA,CAAA,EAAG;MACV,IAAI,CAACvD,IAAG,GAAI;MACZ,IAAI,CAAC+B,aAAa,CAAC;MACnB,IAAI,CAACC,UAAU,CAAC;IAClB,CAAC;IAED;;;IAGAwB,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAACpD,QAAO,GAAI,CAAC,IAAI,CAACA,QAAO;IAC/B,CAAC;IAEDqD,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACpD,SAAQ,GAAI,CAAC,IAAI,CAACA,SAAQ;MAC/B,IAAI,CAAC,IAAI,CAACA,SAAS,EAAE;QACnB,IAAI,CAACO,UAAS,GAAI,IAAG;QACrB,IAAI,CAACC,QAAO,GAAI,IAAG;MACrB;IACF,CAAC;IAED;;;IAGA6C,eAAe,WAAfA,eAAeA,CAACC,KAAK,EAAE;MACrB,IAAI,IAAI,CAACtD,SAAS,EAAE;QAClB,IAAMuD,IAAG,GAAI,IAAI,CAACd,KAAK,CAACe,cAAc,CAACC,qBAAqB,CAAC;QAC7D,IAAM7C,CAAA,GAAI,CAAC0C,KAAK,CAACI,OAAM,GAAIH,IAAI,CAACI,IAAI,IAAI,IAAI,CAAChE,IAAG;QAChD,IAAMkB,CAAA,GAAI,CAACyC,KAAK,CAACM,OAAM,GAAIL,IAAI,CAACM,GAAG,IAAI,IAAI,CAAClE,IAAG;QAE/C,IAAI,CAAC,IAAI,CAACY,UAAU,EAAE;UACpB,IAAI,CAACA,UAAS,GAAI;YAAEK,CAAC,EAADA,CAAC;YAAEC,CAAA,EAAAA;UAAE;QAC3B,OAAO;UACL,IAAI,CAACL,QAAO,GAAI;YAAEI,CAAC,EAADA,CAAC;YAAEC,CAAA,EAAAA;UAAE;QACzB;MACF;IACF,CAAC;IAEDiD,eAAe,WAAfA,eAAeA,CAACR,KAAK,EAAE;MACrB,IAAI,IAAI,CAACtD,SAAQ,IAAK,IAAI,CAACO,UAAS,IAAK,CAAC,IAAI,CAACC,QAAQ,EAAE;QACvD,IAAM+C,IAAG,GAAI,IAAI,CAACd,KAAK,CAACe,cAAc,CAACC,qBAAqB,CAAC;QAC7D,IAAM7C,CAAA,GAAI,CAAC0C,KAAK,CAACI,OAAM,GAAIH,IAAI,CAACI,IAAI,IAAI,IAAI,CAAChE,IAAG;QAChD,IAAMkB,CAAA,GAAI,CAACyC,KAAK,CAACM,OAAM,GAAIL,IAAI,CAACM,GAAG,IAAI,IAAI,CAAClE,IAAG;QAC/C,IAAI,CAACa,QAAO,GAAI;UAAEI,CAAC,EAADA,CAAC;UAAEC,CAAA,EAAAA;QAAE;MACzB;IACF,CAAC;IAEDkD,aAAa,WAAbA,aAAaA,CAACT,KAAK,EAAE;MACnB;IAAA,CACD;IAEDU,WAAW,WAAXA,WAAWA,CAACV,KAAK,EAAE;MACjBA,KAAK,CAACW,cAAc,CAAC;MACrB,IAAIX,KAAK,CAACY,MAAK,GAAI,CAAC,EAAE;QACpB,IAAI,CAAC/B,MAAM,CAAC;MACd,OAAO;QACL,IAAI,CAACG,OAAO,CAAC;MACf;IACF,CAAC;IAEDR,mBAAmB,WAAnBA,mBAAmBA,CAACwB,KAAK,EAAE;MACzB,IAAI,CAACA,KAAK,CAAC3C,MAAM,CAACwD,OAAO,CAAC,eAAe,CAAC,EAAE;QAC1C,IAAI,CAAC1D,WAAW,CAACC,IAAG,GAAI,KAAI;MAC9B;IACF,CAAC;IAEDqB,aAAa,WAAbA,aAAaA,CAACuB,KAAK,EAAE;MACnB,QAAQA,KAAK,CAACc,GAAG;QACf,KAAK,QAAQ;UACX,IAAI,CAACC,cAAc,CAAC;UACpB,IAAI,CAAC5D,WAAW,CAACC,IAAG,GAAI,KAAI;UAC5B;QACF,KAAK,GAAG;QACR,KAAK,GAAG;UACN,IAAI4C,KAAK,CAACgB,OAAO,EAAE;YACjBhB,KAAK,CAACW,cAAc,CAAC;YACrB,IAAI,CAACd,UAAU,CAAC;UAClB;UACA;QACF,KAAK,GAAG;QACR,KAAK,GAAG;UACN,IAAIG,KAAK,CAACgB,OAAO,EAAE;YACjBhB,KAAK,CAACW,cAAc,CAAC;YACrB,IAAI,CAACb,WAAW,CAAC;UACnB;UACA;MACJ;IACF,CAAC;IAEDnB,YAAY,WAAZA,YAAYA,CAAA,EAAG;MAAA,IAAAsC,KAAA;MACb,IAAI,CAACC,SAAS,CAAC,YAAM;QACnBD,KAAI,CAAC7C,aAAa,CAAC;MACrB,CAAC;IACH,CAAC;IAED;;;IAGA+C,eAAe,WAAfA,eAAeA,CAACC,SAAS,EAAE;MACzB,IAAI,CAACxE,iBAAgB,GAAIwE,SAAQ;MACjC,IAAI,CAACvE,eAAc,GAAI,IAAG;MAC1B,IAAI,CAACF,eAAc,GAAI,IAAG;MAC1B,IAAI,CAAC0E,KAAK,CAAC,oBAAoB,EAAED,SAAS;IAC5C,CAAC;IAEDE,aAAa,WAAbA,aAAaA,CAACC,OAAO,EAAE;MACrB,IAAI,CAAC1E,eAAc,GAAI0E,OAAM;MAC7B,IAAI,CAAC3E,iBAAgB,GAAI,IAAG;MAC5B,IAAI,CAACD,eAAc,GAAI,KAAI;MAC3B,IAAI,CAAC0E,KAAK,CAAC,kBAAkB,EAAEE,OAAO;IACxC,CAAC;IAEDR,cAAc,WAAdA,cAAcA,CAAA,EAAG;MACf,IAAI,CAACnE,iBAAgB,GAAI,IAAG;MAC5B,IAAI,CAACC,eAAc,GAAI,IAAG;MAC1B,IAAI,CAACF,eAAc,GAAI,KAAI;MAC3B,IAAI,CAACK,eAAc,GAAI,IAAG;IAC5B,CAAC;IAED;;;IAGAwE,aAAa,WAAbA,aAAaA,CAACJ,SAAS,EAAEK,WAAW,EAAE;MACpC,IAAI,CAACJ,KAAK,CAAC,iBAAiB,EAAE;QAC5BD,SAAS,EAATA,SAAS;QACTM,WAAW,EAAEN,SAAS,CAACO,QAAQ;QAC/BF,WAAU,EAAVA;MACF,CAAC;IACH,CAAC;IAEDG,WAAW,WAAXA,WAAWA,CAACL,OAAO,EAAEE,WAAW,EAAE;MAChC,IAAI,CAACJ,KAAK,CAAC,eAAe,EAAE;QAC1BE,OAAO,EAAPA,OAAO;QACPG,WAAW,EAAEH,OAAO,CAACI,QAAQ;QAC7BF,WAAU,EAAVA;MACF,CAAC;IACH,CAAC;IAEDI,aAAa,WAAbA,aAAaA,CAACxE,MAAM,EAAE;MACpB,IAAI,CAACL,eAAc,GAAI;QACrBK,MAAM,EAANA,MAAM;QACNyE,KAAK,EAAAC,aAAA,KAAO1E,MAAM,CAACsE,QAAO,CAAG;QAC7BK,GAAG,EAAAD,aAAA,KAAO1E,MAAM,CAACsE,QAAO;MAC1B;MACA,IAAI,CAACxE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAED;;;IAGA6E,eAAe,WAAfA,eAAeA,CAACb,SAAS,EAAEc,MAAM,EAAE;MACjC,IAAI,CAACb,KAAK,CAAC,kBAAkB,EAAE;QAAED,SAAS,EAATA,SAAS;QAAEc,MAAK,EAALA;MAAO,CAAC;IACtD,CAAC;IAEDC,aAAa,WAAbA,aAAaA,CAACZ,OAAO,EAAEW,MAAM,EAAE;MAC7B,IAAI,CAACb,KAAK,CAAC,gBAAgB,EAAE;QAAEE,OAAO,EAAPA,OAAO;QAAEW,MAAK,EAALA;MAAO,CAAC;IAClD,CAAC;IAED;;;IAGAE,iBAAiB,WAAjBA,iBAAiBA,CAAChB,SAAS,EAAEpB,KAAK,EAAE;MAClC,IAAI,CAAC7C,WAAU,GAAI;QACjBC,IAAI,EAAE,IAAI;QACV/B,IAAI,EAAE,WAAW;QACjBgC,MAAM,EAAE+D,SAAS;QACjB9D,CAAC,EAAE0C,KAAK,CAACI,OAAO;QAChB7C,CAAC,EAAEyC,KAAK,CAACM;MACX;IACF,CAAC;IAED+B,eAAe,WAAfA,eAAeA,CAACd,OAAO,EAAEvB,KAAK,EAAE;MAC9B,IAAI,CAAC7C,WAAU,GAAI;QACjBC,IAAI,EAAE,IAAI;QACV/B,IAAI,EAAE,SAAS;QACfgC,MAAM,EAAEkE,OAAO;QACfjE,CAAC,EAAE0C,KAAK,CAACI,OAAO;QAChB7C,CAAC,EAAEyC,KAAK,CAACM;MACX;IACF,CAAC;IAED;;;IAGAgC,gBAAgB,WAAhBA,gBAAgBA,CAAClB,SAAS,EAAE;MAC1B,IAAI,CAACC,KAAK,CAAC,mBAAmB,EAAED,SAAS;MACzC,IAAI,CAACjE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAEDmF,cAAc,WAAdA,cAAcA,CAAChB,OAAO,EAAE;MACtB,IAAI,CAACF,KAAK,CAAC,iBAAiB,EAAEE,OAAO;MACrC,IAAI,CAACpE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAEDoF,aAAa,WAAbA,aAAaA,CAACpB,SAAS,EAAE;MACvB,IAAI,CAACC,KAAK,CAAC,gBAAgB,EAAED,SAAS;MACtC,IAAI,CAACjE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAEDqF,WAAW,WAAXA,WAAWA,CAAClB,OAAO,EAAE;MACnB,IAAI,CAACF,KAAK,CAAC,cAAc,EAAEE,OAAO;MAClC,IAAI,CAACpE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAEDsF,cAAc,WAAdA,cAAcA,CAACnB,OAAO,EAAE;MACtB,IAAI,CAACF,KAAK,CAAC,iBAAiB,EAAEE,OAAO;MACrC,IAAI,CAACpE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAEDuF,aAAa,WAAbA,aAAaA,CAACpB,OAAO,EAAE;MACrB,IAAI,CAACF,KAAK,CAAC,gBAAgB,EAAEE,OAAO;MACpC,IAAI,CAACpE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAEDwF,UAAU,WAAVA,UAAUA,CAAA,EAAG;MACX,IAAI,CAACvB,KAAK,CAAC,aAAa;IAC1B,CAAC;IAEDwB,cAAc,WAAdA,cAAcA,CAACzB,SAAS,EAAE;MACxB,IAAI,CAACC,KAAK,CAAC,kBAAkB,EAAED,SAAS;MACxC,IAAI,CAACjE,WAAW,CAACC,IAAG,GAAI,KAAI;IAC9B,CAAC;IAED;;;IAGA0F,gBAAgB,WAAhBA,gBAAgBA,CAAC1B,SAAS,EAAE;MAAA,IAAA2B,qBAAA;MAC1B,OAAO,EAAAA,qBAAA,OAAI,CAAClH,oBAAoB,cAAAkH,qBAAA,uBAAzBA,qBAAA,CAA2BC,EAAC,MAAM5B,SAAS,CAAC4B,EAAC,IAAK,CAAC5B,SAAS,CAAC6B,QAAO;IAC7E,CAAC;IAEDC,eAAe,WAAfA,eAAeA,CAAC9B,SAAS,EAAE;MAAA,IAAA+B,sBAAA;MACzB,OAAO,EAAAA,sBAAA,OAAI,CAACtH,oBAAoB,cAAAsH,sBAAA,uBAAzBA,sBAAA,CAA2BH,EAAC,MAAM5B,SAAS,CAAC4B,EAAC,IAAK,CAAC5B,SAAS,CAAC6B,QAAO;IAC7E,CAAC;IAEDG,cAAc,WAAdA,cAAcA,CAAChC,SAAS,EAAE;MACxB,IAAMiC,MAAK,GAAIjC,SAAS,CAACkC,cAAa;MACtC,IAAI,CAACD,MAAM,EAAE,OAAO,GAAE;MAEtB,IAAIA,MAAM,CAAChI,IAAG,KAAM,OAAO,EAAE;QAC3B,OAAOgI,MAAM,CAACE,KAAI,IAAK,GAAE;MAC3B,OAAO,IAAIF,MAAM,CAAChI,IAAG,KAAM,QAAQ,EAAE;QAAA,IAAAmI,aAAA;QACnC,OAAO,EAAAA,aAAA,GAAAH,MAAM,CAACI,KAAK,cAAAD,aAAA,uBAAZA,aAAA,CAAcE,IAAG,KAAK,EAAC;MAChC;MAEA,OAAO,GAAE;IACX,CAAC;IAEDC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAAC1G,UAAS,IAAK,CAAC,IAAI,CAACC,QAAQ,EAAE,OAAO;MAE/C,IAAM0G,EAAC,GAAI,IAAI,CAAC1G,QAAQ,CAACI,CAAA,GAAI,IAAI,CAACL,UAAU,CAACK,CAAA;MAC7C,IAAMuG,EAAC,GAAI,IAAI,CAAC3G,QAAQ,CAACK,CAAA,GAAI,IAAI,CAACN,UAAU,CAACM,CAAA;MAC7C,IAAMuG,aAAY,GAAIhF,IAAI,CAACiF,IAAI,CAACH,EAAC,GAAIA,EAAC,GAAIC,EAAC,GAAIA,EAAE;MACjD,IAAMG,aAAY,GAAKF,aAAY,GAAI,IAAI,CAAC3H,QAAQ,GAAI,GAAE;MAE1D,OAAO2C,IAAI,CAACmF,KAAK,CAACD,aAAY,GAAI,EAAE,IAAI,EAAC;IAC3C,CAAC;IAEDE,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAAClH,eAAe,EAAE,OAAO,EAAC;MAEnC,IAAM8E,KAAI,GAAI,IAAI,CAAC9E,eAAe,CAAC8E,KAAI;MACvC,IAAME,GAAE,GAAI,IAAI,CAAChF,eAAe,CAACgF,GAAE;MAEnC,YAAAnE,MAAA,CAAYiE,KAAK,CAACxE,CAAA,GAAI,IAAI,CAACnB,QAAO,GAAI,IAAI,CAACE,IAAI,OAAAwB,MAAA,CAAIiE,KAAK,CAACvE,CAAA,GAAI,IAAI,CAACpB,QAAO,GAAI,IAAI,CAACE,IAAI,SAAAwB,MAAA,CAAMmE,GAAG,CAAC1E,CAAA,GAAI,IAAI,CAACnB,QAAO,GAAI,IAAI,CAACE,IAAI,OAAAwB,MAAA,CAAImE,GAAG,CAACzE,CAAA,GAAI,IAAI,CAACpB,QAAO,GAAI,IAAI,CAACE,IAAI;IACpK,CAAC;IAED8H,cAAc,WAAdA,cAAcA,CAAC5C,OAAO,EAAE;MACtB,IAAM6C,OAAM,GAAI;QACd,OAAO,EAAE,aAAa;QACtB,QAAQ,EAAE,YAAY;QACtB,QAAQ,EAAE,YAAY;QACtB,QAAQ,EAAE,cAAc;QACxB,OAAO,EAAE,aAAa;QACtB,WAAW,EAAE;MACf;MAEA,OAAOA,OAAO,CAAC7C,OAAO,CAAClG,IAAI,KAAK,iBAAgB;IAClD,CAAC;IAED;;;IAGAgJ,YAAY,WAAZA,YAAYA,CAACC,SAAS,EAAE;MACtB,IAAI,CAAC9G,gBAAgB,CAAC+G,IAAI,CAAAxC,aAAA,CAAAA,aAAA,KACrBuC,SAAS;QACZtB,EAAE,EAAEwB,IAAI,CAACC,GAAG,CAAC,IAAI3F,IAAI,CAAC4F,MAAM,CAAC;MAAA,EAC9B;IACH,CAAC;IAEDC,eAAe,WAAfA,eAAeA,CAACC,WAAW,EAAE;MAC3B,IAAMC,KAAI,GAAI,IAAI,CAACrH,gBAAgB,CAACsH,SAAS,CAAC,UAAAC,CAAA;QAAA,OAAKA,CAAC,CAAC/B,EAAC,KAAM4B,WAAW;MAAA;MACvE,IAAIC,KAAI,KAAM,CAAC,CAAC,EAAE;QAChB,IAAI,CAACrH,gBAAgB,CAACwH,MAAM,CAACH,KAAK,EAAE,CAAC;MACvC;IACF,CAAC;IAED;;;IAGAI,mBAAmB,WAAnBA,mBAAmBA,CAACC,QAAQ,EAAE7H,MAAM,EAAE8H,MAAM,EAAE;MAC5C,IAAI,CAACd,YAAY,CAAC;QAChBhJ,IAAI,EAAE,QAAQ;QACd+J,IAAI,EAAEF,QAAQ,CAACvD,QAAQ;QACvB0D,EAAE,EAAEhI,MAAM,CAACsE,QAAQ;QACnBwD,MAAM,EAANA,MAAM;QACNG,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;IAED;;;IAGAC,qBAAqB,WAArBA,qBAAqBA,CAACnE,SAAS,EAAEgE,IAAI,EAAEC,EAAE,EAAE;MACzC,IAAI,CAAChB,YAAY,CAAC;QAChBhJ,IAAI,EAAE,UAAU;QAChB+F,SAAS,EAATA,SAAS;QACTgE,IAAI,EAAJA,IAAI;QACJC,EAAE,EAAFA,EAAE;QACFC,QAAQ,EAAE;MACZ,CAAC;IACH;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}