<template>
  <div class="home-page">
    <!-- 简化的欢迎区域 -->
    <section class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            <span v-if="isAuthenticated">{{ getGreeting() }}，{{ currentUser?.username || '探索者' }}</span>
            <span v-else>欢迎来到COC跑团</span>
          </h1>
          <p class="welcome-subtitle">{{ getRandomSubtitle() }}</p>
        </div>
        
        <!-- 未登录时显示登录按钮 -->
        <div class="welcome-actions" v-if="!isAuthenticated">
          <router-link to="/login" class="welcome-btn primary">
            <i class="fas fa-sign-in-alt"></i>
            <span>立即登录</span>
          </router-link>
          <router-link to="/register" class="welcome-btn secondary">
            <i class="fas fa-user-plus"></i>
            <span>注册账号</span>
          </router-link>
        </div>
      </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- 快速操作区域 -->
      <section class="quick-actions-section" v-if="isAuthenticated">
        <div class="quick-actions-grid">
          <button class="action-btn primary" @click="createRoom">
            <i class="fas fa-plus"></i>
            <span>创建房间</span>
          </button>
          
          <button class="action-btn" @click="joinRandomRoom" :disabled="availableRoomsCount === 0">
            <i class="fas fa-random"></i>
            <span>随机加入</span>
          </button>
          
          <button class="action-btn" @click="openCharacterManager">
            <i class="fas fa-user-friends"></i>
            <span>角色管理</span>
          </button>
        </div>
      </section>

      <!-- 房间列表区域 -->
      <section class="rooms-section">
        <div class="section-header">
          <div class="header-left">
            <h2 class="section-title">
              <i class="fas fa-door-open"></i>
              <span>房间大厅</span>
            </h2>
            <p class="section-subtitle">发现正在进行的游戏</p>
          </div>
          
          <div class="header-controls">
            <div class="filter-group">
              <label class="filter-label">筛选：</label>
              <select v-model="roomFilter" class="filter-select">
                <option value="all">全部房间</option>
                <option value="public">公开房间</option>
                <option value="private">私人房间</option>
                <option value="my" v-if="isAuthenticated">我的房间</option>
                <option value="available">可加入</option>
              </select>
            </div>
            
            <div class="view-controls">
              <button 
                @click="viewMode = 'grid'" 
                class="view-btn" 
                :class="{ 'active': viewMode === 'grid' }"
                title="网格视图"
              >
                <i class="fas fa-th"></i>
              </button>
              <button 
                @click="viewMode = 'list'" 
                class="view-btn" 
                :class="{ 'active': viewMode === 'list' }"
                title="列表视图"
              >
                <i class="fas fa-list"></i>
              </button>
            </div>
            
            <button @click="refreshRooms" class="refresh-btn" :disabled="loading" title="刷新房间列表">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loading }"></i>
            </button>
          </div>
        </div>
        
        <!-- 房间统计 -->
        <div class="rooms-stats" v-if="filteredRooms.length > 0">
          <div class="stats-item">
            <span class="stats-number">{{ filteredRooms.length }}</span>
            <span class="stats-label">个房间</span>
          </div>
          <div class="stats-item">
            <span class="stats-number">{{ availableRoomsCount }}</span>
            <span class="stats-label">可加入</span>
          </div>
          <div class="stats-item">
            <span class="stats-number">{{ activeRoomsCount }}</span>
            <span class="stats-label">进行中</span>
          </div>
        </div>
        
        <!-- 房间列表 -->
        <div class="rooms-container">
          <!-- 网格视图 -->
          <div v-if="viewMode === 'grid'" class="room-grid">
            <div 
              v-for="room in filteredRooms" 
              :key="room.id" 
              class="room-card" 
              @click="joinRoom(room)"
              :class="{ 'full': room.current_players >= room.max_players }"
            >
              <div class="room-header">
                <div class="room-info">
                  <h3 class="room-title">{{ room.name }}</h3>
                  <div class="room-meta">
                    <span class="room-creator">by {{ room.creator_name || '未知' }}</span>
                    <span class="room-time">{{ formatTime(room.created_at) }}</span>
                  </div>
                </div>
                <div class="room-status-badge" :class="room.status">
                  <i :class="getStatusIcon(room.status)"></i>
                  <span>{{ getStatusText(room.status) }}</span>
                </div>
              </div>
              
              <p class="room-description">{{ room.description || '暂无描述' }}</p>
              
              <div class="room-tags" v-if="room.tags && room.tags.length > 0">
                <span v-for="tag in room.tags.slice(0, 3)" :key="tag" class="room-tag">{{ tag }}</span>
              </div>
              
              <div class="room-footer">
                <div class="room-players">
                  <div class="players-info">
                    <i class="fas fa-users"></i>
                    <span>{{ room.current_players }}/{{ room.max_players }}</span>
                  </div>
                  <div class="players-bar">
                    <div 
                      class="players-fill" 
                      :style="{ width: (room.current_players / room.max_players * 100) + '%' }"
                    ></div>
                  </div>
                </div>
                
                <div class="room-actions">
                  <button 
                    class="join-btn" 
                    :disabled="room.current_players >= room.max_players || !isAuthenticated"
                    @click.stop="joinRoom(room)"
                  >
                    {{ room.current_players >= room.max_players ? '已满' : '加入' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 列表视图 -->
          <div v-else class="room-list">
            <div 
              v-for="room in filteredRooms" 
              :key="room.id" 
              class="room-list-item" 
              @click="joinRoom(room)"
              :class="{ 'full': room.current_players >= room.max_players }"
            >
              <div class="room-list-info">
                <div class="room-list-header">
                  <h3 class="room-list-title">{{ room.name }}</h3>
                  <div class="room-list-status" :class="room.status">
                    <i :class="getStatusIcon(room.status)"></i>
                    <span>{{ getStatusText(room.status) }}</span>
                  </div>
                </div>
                <p class="room-list-description">{{ room.description || '暂无描述' }}</p>
                <div class="room-list-meta">
                  <span class="meta-item">
                    <i class="fas fa-user"></i>
                    <span>{{ room.creator_name || '未知' }}</span>
                  </span>
                  <span class="meta-item">
                    <i class="fas fa-clock"></i>
                    <span>{{ formatTime(room.created_at) }}</span>
                  </span>
                  <span class="meta-item">
                    <i class="fas fa-users"></i>
                    <span>{{ room.current_players }}/{{ room.max_players }}</span>
                  </span>
                </div>
              </div>
              
              <div class="room-list-actions">
                <button 
                  class="join-btn" 
                  :disabled="room.current_players >= room.max_players || !isAuthenticated"
                  @click.stop="joinRoom(room)"
                >
                  {{ room.current_players >= room.max_players ? '已满' : '加入' }}
                </button>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="filteredRooms.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-door-open"></i>
            </div>
            <h3 class="empty-title">{{ getEmptyStateTitle() }}</h3>
            <p class="empty-desc">{{ getEmptyStateDesc() }}</p>
            <div class="empty-actions">
              <button @click="createRoom" class="empty-action-btn primary" v-if="isAuthenticated">
                <i class="fas fa-plus"></i>
                <span>创建房间</span>
              </button>
              <button @click="refreshRooms" class="empty-action-btn secondary">
                <i class="fas fa-sync-alt"></i>
                <span>刷新列表</span>
              </button>
            </div>
          </div>
        </div>
      </section>


    </main>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { storageMixin } from '@/mixins/storageMixin'

export default {
  name: 'Home',
  mixins: [storageMixin],
  data() {
    return {
      roomFilter: 'all',
      viewMode: 'grid',
      loading: false,
      
      // 副标题列表
      subtitles: [
        '准备开始新的冒险了吗？',
        '黑暗中有什么在等待着你...',
        '真相往往比想象更加恐怖',
        '在未知的世界中寻找答案',
        '每一次投骰都可能改变命运',
        '克苏鲁的呼唤正在响起...'
      ]
    }
  },
  computed: {
    ...mapGetters('auth', ['currentUser', 'isAuthenticated']),
    
    filteredRooms() {
      const rooms = this.$store.getters['rooms/rooms'] || []
      switch (this.roomFilter) {
        case 'public':
          return rooms.filter(room => room.is_public)
        case 'private':
          return rooms.filter(room => !room.is_public)
        case 'my':
          return rooms.filter(room => room.creator_id === this.currentUser?.id)
        case 'available':
          return rooms.filter(room => 
            room.current_players < room.max_players && room.status === 'waiting'
          )
        default:
          return rooms
      }
    },
    
    myCharacters() {
      return this.$store.getters.userCharacters?.length || 0
    },
    
    availableRoomsCount() {
      return this.$store.getters['rooms/availableRooms'].length
    },
    
    activeRoomsCount() {
      const rooms = this.$store.getters['rooms/rooms'] || []
      return rooms.filter(room => room.status === 'active').length
    }
  },
  methods: {
    // 问候语相关
    getGreeting() {
      const hour = new Date().getHours()
      if (hour < 6) return '深夜好'
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    },
    
    getRandomSubtitle() {
      return this.subtitles[Math.floor(Math.random() * this.subtitles.length)]
    },
    
    // 导航方法
    openCharacterManager() {
      if (!this.isAuthenticated) {
        this.$router.push('/login')
        return
      }
      this.$router.push('/characters')
    },
    

    
    // 房间操作
    async createRoom() {
      if (!this.isAuthenticated) {
        this.$router.push('/login')
        return
      }
      this.$router.push('/create-room')
    },
    
    async joinRandomRoom() {
      if (!this.isAuthenticated) {
        this.$router.push('/login')
        return
      }
      
      const availableRooms = this.filteredRooms.filter(room => 
        room.current_players < room.max_players && room.status === 'waiting'
      )
      
      if (availableRooms.length > 0) {
        const randomRoom = availableRooms[Math.floor(Math.random() * availableRooms.length)]
        this.joinRoom(randomRoom)
      } else {
        this.$message.info('暂无可加入的房间')
      }
    },
    
    async joinRoom(room) {
      if (!this.isAuthenticated) {
        this.$router.push('/login')
        return
      }
      
      if (room.current_players >= room.max_players) {
        this.$message.warning('房间已满')
        return
      }
      
      try {
        // 这里可以添加加入房间的逻辑，比如检查权限等
        // await this.$store.dispatch('joinRoom', room.id)
        this.$router.push(`/room/${room.id}`)
      } catch (error) {
        console.error('加入房间失败:', error)
        this.$message.error('加入房间失败')
      }
    },
    
    async refreshRooms() {
      try {
        this.loading = true
        await this.$store.dispatch('rooms/loadRooms')
        this.$message.success('房间列表已刷新')
      } catch (error) {
        console.error('刷新房间列表失败:', error)
        this.$message.error('刷新失败')
      } finally {
        this.loading = false
      }
    },
    
    // 状态相关
    getStatusIcon(status) {
      const iconMap = {
        active: 'fas fa-play',
        waiting: 'fas fa-clock',
        finished: 'fas fa-check',
        paused: 'fas fa-pause'
      }
      return iconMap[status] || 'fas fa-question'
    },
    
    getStatusText(status) {
      const textMap = {
        active: '进行中',
        waiting: '等待中',
        finished: '已结束',
        paused: '已暂停'
      }
      return textMap[status] || '未知'
    },
    

    
    // 空状态
    getEmptyStateTitle() {
      switch (this.roomFilter) {
        case 'public':
          return '暂无公开房间'
        case 'private':
          return '暂无私人房间'
        case 'my':
          return '你还没有创建房间'
        case 'available':
          return '暂无可加入的房间'
        default:
          return '暂无房间'
      }
    },
    
    getEmptyStateDesc() {
      switch (this.roomFilter) {
        case 'public':
          return '当前没有公开的游戏房间'
        case 'private':
          return '当前没有私人游戏房间'
        case 'my':
          return '创建一个新房间开始你的冒险吧！'
        case 'available':
          return '所有房间都已满员或正在进行中'
        default:
          return '创建一个新房间开始你的冒险吧！'
      }
    },
    
    // 工具方法
    formatTime(time) {
      if (!time) return '未知时间'
      
      const now = new Date()
      const timeDate = new Date(time)
      const diffMs = now - timeDate
      const diffMins = Math.floor(diffMs / 60000)
      const diffHours = Math.floor(diffMs / 3600000)
      const diffDays = Math.floor(diffMs / 86400000)
      
      if (diffMins < 1) return '刚刚'
      if (diffMins < 60) return `${diffMins}分钟前`
      if (diffHours < 24) return `${diffHours}小时前`
      if (diffDays < 7) return `${diffDays}天前`
      
      return timeDate.toLocaleDateString('zh-CN')
    },
    

  },
  
  mounted() {
    // 加载房间列表
    this.refreshRooms()
    
    // 加载用户数据
    if (this.isAuthenticated) {
      this.$store.dispatch('loadUserCharacters')
    }
    
    // 从本地存储恢复视图模式
    const savedViewMode = this.safeGetItem('home-view-mode')
    if (savedViewMode) {
      this.viewMode = savedViewMode
    }
  },
  
  watch: {
    viewMode(newMode) {
      this.safeSetItem('home-view-mode', newMode)
    },
    
    isAuthenticated(newVal) {
      if (newVal) {
        this.$store.dispatch('loadUserCharacters')
      }
    }
  }
}
</script><style
 scoped>
/* ===== 页面基础样式 ===== */
.home-page {
  min-height: 100vh;
  background: var(--bg-primary);
  padding-bottom: var(--spacing-8);
}

/* ===== 简化的欢迎区域 ===== */
.welcome-section {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 50%, var(--success-400) 100%);
  color: var(--text-inverse);
  padding: var(--spacing-6) 0;
  margin-bottom: var(--spacing-6);
  position: relative;
  overflow: hidden;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  opacity: 0.3;
}

.welcome-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.welcome-text {
  margin-bottom: var(--spacing-4);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-2);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.welcome-subtitle {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  margin: 0;
  font-style: italic;
}

.welcome-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
  flex-wrap: wrap;
}

.welcome-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border-radius: var(--radius-xl);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-fast);
  border: 2px solid transparent;
  min-width: 140px;
  justify-content: center;
}

.welcome-btn.primary {
  background: var(--bg-primary);
  color: var(--success-600);
  border-color: var(--bg-primary);
}

.welcome-btn.primary:hover {
  background: var(--success-50);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
  color: var(--success-700);
}

.welcome-btn.secondary {
  background: transparent;
  color: var(--text-inverse);
  border-color: rgba(255, 255, 255, 0.3);
}

.welcome-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  text-decoration: none;
  color: var(--text-inverse);
}

/* ===== 主要内容区域 ===== */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* ===== 快速操作区域 ===== */
.quick-actions-section {
  margin-bottom: var(--spacing-6);
}

.quick-actions-grid {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--success-50);
  color: var(--success-700);
  border: 2px solid var(--success-200);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-fast);
  cursor: pointer;
  min-width: 140px;
  justify-content: center;
}

.action-btn:hover:not(:disabled) {
  background: var(--success-100);
  border-color: var(--success-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.primary {
  background: var(--success-600);
  color: var(--text-inverse);
  border-color: var(--success-600);
}

.action-btn.primary:hover:not(:disabled) {
  background: var(--success-700);
  border-color: var(--success-700);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.5);
}

.welcome-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.8);
  transform: translateY(-2px);
  text-decoration: none;
  color: var(--text-inverse);
}

/* ===== 主要内容区域 ===== */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

/* ===== 房间列表区域 ===== */
.rooms-section {
  background: var(--bg-elevated);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--success-200);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-6);
  gap: var(--spacing-4);
}

.header-left {
  flex: 1;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2);
}

.section-title i {
  color: var(--success-500);
}

.section-subtitle {
  color: var(--text-muted);
  font-size: var(--font-size-base);
  margin: 0;
}



.header-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.filter-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  min-width: 120px;
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--success-400);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.view-controls {
  display: flex;
  border: 1px solid var(--success-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.view-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: none;
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  border-right: 1px solid var(--success-200);
}

.view-btn:last-child {
  border-right: none;
}

.view-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
}

.view-btn.active {
  background: var(--success-500);
  color: var(--text-inverse);
}

.refresh-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-400);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-btn .fa-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== 房间统计 ===== */
.rooms-stats {
  display: flex;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--success-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--success-200);
}

.stats-item {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-1);
}

.stats-number {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--success-700);
}

.stats-label {
  font-size: var(--font-size-sm);
  color: var(--success-600);
  font-weight: var(--font-weight-medium);
}

/* ===== 房间网格视图 ===== */
.room-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-4);
}

.room-card {
  background: var(--bg-primary);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-5);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.room-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.05), transparent);
  transition: left var(--transition-fast);
}

.room-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--success-400);
}

.room-card:hover::before {
  left: 100%;
}

.room-card.full {
  opacity: 0.7;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-3);
  gap: var(--spacing-3);
}

.room-info {
  flex: 1;
  min-width: 0;
}

.room-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-1);
  line-height: 1.3;
}

.room-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.room-creator {
  font-weight: var(--font-weight-medium);
}

.room-status-badge {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  flex-shrink: 0;
}

.room-status-badge.active {
  background: var(--success-100);
  color: var(--success-700);
}

.room-status-badge.waiting {
  background: var(--warning-100);
  color: var(--warning-700);
}

.room-status-badge.finished {
  background: var(--gray-100);
  color: var(--gray-700);
}

.room-status-badge.paused {
  background: var(--error-100);
  color: var(--error-700);
}

.room-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-3);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.room-tags {
  display: flex;
  gap: var(--spacing-1);
  margin-bottom: var(--spacing-3);
  flex-wrap: wrap;
}

.room-tag {
  padding: var(--spacing-1) var(--spacing-2);
  background: var(--success-100);
  color: var(--success-700);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.room-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-3);
}

.room-players {
  flex: 1;
  min-width: 0;
}

.players-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-1);
}

.players-bar {
  height: 4px;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.players-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-400), var(--success-500));
  border-radius: var(--radius-full);
  transition: width var(--transition-fast);
}

.room-actions {
  flex-shrink: 0;
}

.join-btn {
  padding: var(--spacing-2) var(--spacing-4);
  border: 1px solid var(--success-400);
  border-radius: var(--radius-md);
  background: var(--success-500);
  color: var(--text-inverse);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.join-btn:hover:not(:disabled) {
  background: var(--success-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.join-btn:disabled {
  background: var(--gray-400);
  border-color: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.6;
}

/* ===== 房间列表视图 ===== */
.room-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.room-list-item {
  background: var(--bg-primary);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-4);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.room-list-item:hover {
  border-color: var(--success-400);
  box-shadow: var(--shadow-sm);
}

.room-list-item.full {
  opacity: 0.7;
}

.room-list-info {
  flex: 1;
  min-width: 0;
}

.room-list-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2);
  gap: var(--spacing-3);
}

.room-list-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.room-list-status {
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.room-list-status.active {
  background: var(--success-100);
  color: var(--success-700);
}

.room-list-status.waiting {
  background: var(--warning-100);
  color: var(--warning-700);
}

.room-list-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.room-list-meta {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.room-list-actions {
  flex-shrink: 0;
}

/* ===== 空状态 ===== */
.empty-state {
  text-align: center;
  padding: var(--spacing-8) var(--spacing-4);
  color: var(--text-muted);
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: var(--success-100);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  margin: 0 auto var(--spacing-4);
  color: var(--success-400);
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2);
}

.empty-desc {
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-6);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.empty-actions {
  display: flex;
  gap: var(--spacing-3);
  justify-content: center;
  flex-wrap: wrap;
}

.empty-action-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  cursor: pointer;
  border: 2px solid transparent;
}

.empty-action-btn.primary {
  background: var(--success-500);
  color: var(--text-inverse);
  border-color: var(--success-500);
}

.empty-action-btn.primary:hover {
  background: var(--success-600);
  border-color: var(--success-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.empty-action-btn.secondary {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-color: var(--success-200);
}

.empty-action-btn.secondary:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-400);
  transform: translateY(-2px);
}

/* ===== 响应式设计 ===== */

@media (max-width: 768px) {
  .welcome-title {
    font-size: var(--font-size-2xl);
  }
  
  .welcome-subtitle {
    font-size: var(--font-size-base);
  }
  
  .quick-actions-grid {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 300px;
  }
  
  .header-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .room-grid {
    grid-template-columns: 1fr;
  }
  
  .rooms-stats {
    flex-direction: column;
    gap: var(--spacing-3);
  }
}

@media (max-width: 480px) {
  .welcome-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .welcome-btn {
    width: 100%;
    max-width: 250px;
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .room-card {
    padding: var(--spacing-4);
  }
  
  .room-list-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .room-list-actions {
    align-self: flex-end;
  }
}


</style>

<style scoped>
/* ===== 首页基础样式 ===== */
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--success-50) 100%);
  position: relative;
  overflow-x: hidden;
}

.home-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="home-pattern" width="60" height="60" patternUnits="userSpaceOnUse"><circle cx="30" cy="30" r="3" fill="%2322c55e" opacity="0.03"/><circle cx="15" cy="15" r="1.5" fill="%2322c55e" opacity="0.02"/><circle cx="45" cy="45" r="1.5" fill="%2322c55e" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23home-pattern)"/></svg>') repeat;
  pointer-events: none;
  z-index: 1;
}

.home-page > * {
  position: relative;
  z-index: 2;
}

/* ===== 英雄区域 ===== */
.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="80" height="80" patternUnits="userSpaceOnUse"><circle cx="40" cy="40" r="4" fill="%23ffffff" opacity="0.05"/><circle cx="20" cy="20" r="2" fill="%23ffffff" opacity="0.03"/><circle cx="60" cy="60" r="2" fill="%23ffffff" opacity="0.03"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>') repeat;
  pointer-events: none;
  z-index: 1;
}

.hero-content {
  text-align: center;
  max-width: 800px;
  padding: 0 2rem;
}

/* ===== 英雄区域 ===== */
.hero-section {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
}



.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-8) var(--spacing-6);
  text-align: center;
  position: relative;
  z-index: 3;
}

.hero-text {
  margin-bottom: var(--spacing-8);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-4) 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: hero-title-appear 1s ease-out;
}

@keyframes hero-title-appear {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin: 0 0 var(--spacing-6) 0;
  opacity: 0.9;
  animation: hero-subtitle-appear 1s ease-out 0.3s both;
}

@keyframes hero-subtitle-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 0.9;
    transform: translateY(0);
  }
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-8);
  margin-top: var(--spacing-6);
  animation: hero-stats-appear 1s ease-out 0.6s both;
}

@keyframes hero-stats-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-fast);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.stat-item i {
  font-size: var(--font-size-2xl);
  color: var(--success-200);
}

.stat-value {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-inverse);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: rgba(255, 255, 255, 0.8);
  font-weight: var(--font-weight-medium);
}

.hero-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-4);
  animation: hero-actions-appear 1s ease-out 0.9s both;
}

@keyframes hero-actions-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4) var(--spacing-6);
  border: none;
  border-radius: var(--radius-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  text-decoration: none;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.hero-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-fast);
}

.hero-btn:hover::before {
  left: 100%;
}

.hero-btn.primary {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.hero-btn.primary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.hero-btn.secondary {
  background: transparent;
  color: var(--text-inverse);
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.hero-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.7);
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

/* ===== 主要内容区域 ===== */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-8) var(--spacing-6);
}

/* ===== 区域头部通用样式 ===== */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.section-title i {
  color: var(--success-600);
  font-size: var(--font-size-xl);
}

.section-desc {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0;
  font-weight: var(--font-weight-medium);
}

.section-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-4);
}

/* ===== 快速操作区域 ===== */
.quick-actions-section {
  margin-bottom: var(--spacing-12);
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

.action-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6);
  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.95) 100%);
  border: 2px solid var(--success-200);
  border-radius: var(--radius-2xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: left;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 50%, var(--success-400) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-fast);
}

.action-card:hover::before {
  transform: scaleX(1);
}

.action-card:hover {
  border-color: var(--success-300);
  box-shadow: var(--shadow-xl);
  transform: translateY(-6px);
}

.action-card:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-card.primary {
  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);
  border-color: var(--success-300);
}

.action-card.primary:hover {
  background: linear-gradient(135deg, var(--success-200) 0%, var(--success-300) 100%);
  border-color: var(--success-400);
}

.action-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--text-inverse);
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.action-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.action-card:hover .action-icon::before {
  transform: translateX(100%);
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2) 0;
}

.action-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* ===== 房间过滤器 ===== */
.room-filters {
  display: flex;
  gap: var(--spacing-2);
  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.9) 100%);
  padding: var(--spacing-2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
  transition: left var(--transition-fast);
}

.filter-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.filter-btn:hover::before {
  left: 100%;
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
  color: var(--text-inverse);
  border-color: var(--success-500);
  box-shadow: var(--shadow-md);
}

.filter-count {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-inverse);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  min-width: 20px;
  text-align: center;
}

.filter-btn:not(.active) .filter-count {
  background: var(--success-200);
  color: var(--success-700);
}

.refresh-btn {
  width: 40px;
  height: 40px;
  border: 1px solid var(--success-200);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
}

.refresh-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
  transform: scale(1.1);
  box-shadow: var(--shadow-sm);
}/* =
==== 房间网格 ===== */
.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-6);
  margin-top: var(--spacing-6);
}

.room-card {
  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.95) 100%);
  border: 2px solid var(--success-200);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  animation: room-card-appear 0.5s ease-out;
}

@keyframes room-card-appear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.room-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--success-400) 0%, var(--success-500) 50%, var(--success-400) 100%);
  transform: scaleX(0);
  transition: transform var(--transition-fast);
}

.room-card:hover {
  border-color: var(--success-300);
  box-shadow: var(--shadow-xl);
  transform: translateY(-6px);
}

.room-card:hover::before {
  transform: scaleX(1);
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-4);
}

.room-info {
  flex: 1;
}

.room-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-2) 0;
  line-height: 1.3;
}

.room-meta {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.room-creator,
.room-players {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.room-creator i {
  color: var(--warning-500);
  font-size: var(--font-size-xs);
}

.room-players i {
  color: var(--success-500);
  font-size: var(--font-size-xs);
}

.room-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  backdrop-filter: blur(5px);
}

.room-status.active {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-700);
  border: 1px solid var(--success-300);
}

.room-status.waiting {
  background: rgba(251, 191, 36, 0.1);
  color: var(--warning-700);
  border: 1px solid var(--warning-300);
}

.room-status.recruiting {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-700);
  border: 1px solid var(--info-300);
}

.room-status.private {
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  animation: status-pulse 2s ease-in-out infinite;
}

.room-status.active .status-dot {
  background: var(--success-500);
}

.room-status.waiting .status-dot {
  background: var(--warning-500);
}

.room-status.recruiting .status-dot {
  background: var(--info-500);
}

.room-status.private .status-dot {
  background: var(--gray-500);
}

@keyframes status-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.room-content {
  margin-bottom: var(--spacing-4);
}

.room-description {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: 1.6;
  margin: 0 0 var(--spacing-3) 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.room-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-2);
}

.room-tag {
  padding: var(--spacing-1) var(--spacing-3);
  background: var(--success-100);
  color: var(--success-700);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--success-200);
  transition: all var(--transition-fast);
}

.room-tag:hover {
  background: var(--success-200);
  border-color: var(--success-300);
  transform: scale(1.05);
}

.room-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-4);
  border-top: 1px solid var(--success-200);
}

.room-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.room-time i {
  color: var(--success-400);
}

.room-actions {
  display: flex;
  gap: var(--spacing-2);
}

.join-btn,
.view-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.join-btn::before,
.view-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left var(--transition-fast);
}

.join-btn:hover::before,
.view-btn:hover::before {
  left: 100%;
}

.join-btn {
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-sm);
}

.join-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.join-btn:disabled {
  background: var(--disabled-bg);
  color: var(--disabled-text);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.view-btn {
  background: var(--bg-primary);
  color: var(--text-secondary);
  border: 1px solid var(--success-200);
}

.view-btn:hover {
  background: var(--success-50);
  color: var(--success-600);
  border-color: var(--success-300);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* ===== 空状态 ===== */
.empty-state {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  background: linear-gradient(135deg, var(--bg-elevated) 0%, rgba(255, 255, 255, 0.9) 100%);
  border: 2px dashed var(--success-300);
  border-radius: var(--radius-2xl);
  margin-top: var(--spacing-6);
  backdrop-filter: blur(10px);
}

.empty-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--success-100) 0%, var(--success-200) 100%);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
  font-size: var(--font-size-2xl);
  color: var(--success-600);
  animation: empty-icon-float 3s ease-in-out infinite;
}

@keyframes empty-icon-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.empty-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-3) 0;
}

.empty-desc {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-6) 0;
  line-height: 1.5;
}

.empty-action-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4) var(--spacing-6);
  border: none;
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--success-600) 0%, var(--success-500) 100%);
  color: var(--text-inverse);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-bold);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.empty-action-btn:hover {
  background: linear-gradient(135deg, var(--success-700) 0%, var(--success-600) 100%);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}/* ====
= 响应式设计 ===== */
@media (max-width: 1024px) {
  .hero-content {
    padding: var(--spacing-6) var(--spacing-4);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .hero-stats {
    gap: var(--spacing-6);
  }
  
  .main-content {
    padding: var(--spacing-6) var(--spacing-4);
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
  }
  
  .rooms-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-4);
  }
  
  .section-controls {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
  
  .room-filters {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 50vh;
  }
  
  .hero-content {
    padding: var(--spacing-4) var(--spacing-3);
  }
  
  .hero-title {
    font-size: var(--font-size-2xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-base);
  }
  
  .hero-stats {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: center;
  }
  
  .stat-item {
    flex-direction: row;
    padding: var(--spacing-3);
    width: 100%;
    max-width: 200px;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }
  
  .main-content {
    padding: var(--spacing-4) var(--spacing-3);
  }
  
  .section-title {
    font-size: var(--font-size-xl);
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .action-card {
    padding: var(--spacing-4);
  }
  
  .action-icon {
    width: 50px;
    height: 50px;
    font-size: var(--font-size-lg);
  }
  
  .rooms-grid {
    grid-template-columns: 1fr;
  }
  
  .room-card {
    padding: var(--spacing-4);
  }
  
  .room-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .room-footer {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: flex-start;
  }
  
  .room-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .join-btn,
  .view-btn {
    flex: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: var(--spacing-3) var(--spacing-2);
  }
  
  .hero-title {
    font-size: var(--font-size-xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-sm);
  }
  
  .main-content {
    padding: var(--spacing-3) var(--spacing-2);
  }
  
  .section-header {
    margin-bottom: var(--spacing-6);
  }
  
  .section-title {
    font-size: var(--font-size-lg);
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .action-card {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-3);
  }
  
  .action-icon {
    width: 40px;
    height: 40px;
    font-size: var(--font-size-base);
  }
  
  .room-card {
    padding: var(--spacing-3);
  }
  
  .room-name {
    font-size: var(--font-size-base);
  }
  
  .room-meta {
    gap: var(--spacing-2);
  }
  
  .room-creator,
  .room-players {
    font-size: var(--font-size-xs);
  }
  
  .room-tags {
    gap: var(--spacing-1);
  }
  
  .room-tag {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
  }
  
  .filter-btn {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
  }
  
  .filter-btn span:not(.filter-count) {
    display: none;
  }
  
  .empty-state {
    padding: var(--spacing-8) var(--spacing-4);
  }
  
  .empty-icon {
    width: 60px;
    height: 60px;
    font-size: var(--font-size-xl);
  }
  
  .empty-title {
    font-size: var(--font-size-lg);
  }
  
  .empty-desc {
    font-size: var(--font-size-sm);
  }
}

/* ===== 可访问性增强 ===== */
.home-page button:focus,
.home-page a:focus {
  outline: 2px solid var(--success-500);
  outline-offset: 2px;
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
  .hero-section {
    border-bottom: 3px solid var(--success-400);
  }
  
  .action-card,
  .room-card {
    border-width: 3px;
  }
  
  .hero-btn,
  .join-btn,
  .view-btn,
  .filter-btn {
    border-width: 2px;
  }
  
  .empty-state {
    border-width: 3px;
  }
}

/* ===== 减少动画模式 ===== */
@media (prefers-reduced-motion: reduce) {
  .home-page *,
  .home-page *::before,
  .home-page *::after {
    animation: none !important;
    transition: none !important;
  }
  
  .action-card:hover,
  .room-card:hover,
  .hero-btn:hover,
  .join-btn:hover,
  .view-btn:hover {
    transform: none !important;
  }
}

/* ===== 打印样式 ===== */
@media print {
  .home-page {
    background: white !important;
  }
  
  .hero-section {
    background: var(--gray-100) !important;
    color: var(--text-primary) !important;
    page-break-after: avoid;
  }
  
  .hero-actions,
  .room-actions,
  .section-controls {
    display: none !important;
  }
  
  .main-content {
    padding: var(--spacing-4) !important;
  }
  
  .action-card,
  .room-card {
    page-break-inside: avoid;
    margin-bottom: var(--spacing-4);
    box-shadow: none !important;
    border: 1px solid var(--gray-300) !important;
  }
  
  .quick-actions-grid,
  .rooms-grid {
    grid-template-columns: 1fr !important;
    gap: var(--spacing-3) !important;
  }
}

/* ===== 自定义滚动条 ===== */
.home-page::-webkit-scrollbar {
  width: 8px;
}

.home-page::-webkit-scrollbar-track {
  background: var(--success-100);
  border-radius: var(--radius-full);
}

.home-page::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--success-400) 0%, var(--success-500) 100%);
  border-radius: var(--radius-full);
  border: 1px solid var(--success-300);
}

.home-page::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--success-500) 0%, var(--success-600) 100%);
}

/* ===== 加载状态 ===== */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--success-600);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--success-200);
  border-top: 4px solid var(--success-600);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>