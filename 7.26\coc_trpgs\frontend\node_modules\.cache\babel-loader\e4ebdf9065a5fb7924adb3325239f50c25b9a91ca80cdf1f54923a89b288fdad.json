{"ast": null, "code": "import _regenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/regenerator.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _toConsumableArray from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _objectSpread from \"C:/Users/<USER>/Desktop/\\u6700\\u65B0\\u7684 - \\u526F\\u672C/7.26/coc_trpgs/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport \"core-js/modules/es.error.cause.js\";\nimport \"core-js/modules/es.error.to-string.js\";\nimport \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.array.filter.js\";\nimport \"core-js/modules/es.array.includes.js\";\nimport \"core-js/modules/es.array.map.js\";\nimport \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.array.sort.js\";\nimport \"core-js/modules/es.date.to-string.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.object.to-string.js\";\nimport \"core-js/modules/es.set.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport \"core-js/modules/es.string.includes.js\";\nimport \"core-js/modules/es.string.iterator.js\";\nimport \"core-js/modules/web.dom-collections.iterator.js\";\nimport { mapGetters } from 'vuex';\nimport CharacterForm from '@/components/CharacterForm.vue';\nimport CharacterDetailView from '@/components/CharacterDetailView.vue';\nimport { storageMixin } from '@/mixins/storageMixin';\nexport default {\n  name: 'CharacterManager',\n  mixins: [storageMixin],\n  components: {\n    CharacterForm: CharacterForm,\n    CharacterDetailView: CharacterDetailView\n  },\n  data: function data() {\n    return {\n      loading: true,\n      characters: [],\n      showEditForm: false,\n      showCharacterView: false,\n      selectedCharacter: null,\n      viewMode: 'grid',\n      // 'grid' or 'list'\n      searchQuery: '',\n      selectedOccupation: '',\n      selectedStatus: '',\n      sortBy: 'name',\n      defaultAvatar: '/images/default-character.png'\n    };\n  },\n  computed: _objectSpread(_objectSpread({}, mapGetters(['userCharacters'])), {}, {\n    filteredCharacters: function filteredCharacters() {\n      var _this = this;\n      var filtered = _toConsumableArray(this.characters);\n\n      // 搜索过滤\n      if (this.searchQuery) {\n        var query = this.searchQuery.toLowerCase();\n        filtered = filtered.filter(function (_char) {\n          return _char.name.toLowerCase().includes(query) || _char.occupation && _char.occupation.toLowerCase().includes(query);\n        });\n      }\n\n      // 职业过滤\n      if (this.selectedOccupation) {\n        filtered = filtered.filter(function (_char2) {\n          return _char2.occupation === _this.selectedOccupation;\n        });\n      }\n\n      // 状态过滤\n      if (this.selectedStatus) {\n        filtered = filtered.filter(function (_char3) {\n          return _char3.status === _this.selectedStatus;\n        });\n      }\n\n      // 排序\n      filtered.sort(function (a, b) {\n        switch (_this.sortBy) {\n          case 'name':\n            return a.name.localeCompare(b.name);\n          case 'created':\n            return new Date(b.created_at) - new Date(a.created_at);\n          case 'updated':\n            return new Date(b.updated_at) - new Date(a.updated_at);\n          case 'level':\n            return (b.level || 0) - (a.level || 0);\n          default:\n            return 0;\n        }\n      });\n      return filtered;\n    },\n    occupations: function occupations() {\n      var occupations = _toConsumableArray(new Set(this.characters.map(function (_char4) {\n        return _char4.occupation;\n      }).filter(Boolean)));\n      return occupations.sort();\n    },\n    aliveCharacters: function aliveCharacters() {\n      return this.characters.filter(function (_char5) {\n        return _char5.status === 'alive' || !_char5.status;\n      }).length;\n    },\n    favoriteCharacters: function favoriteCharacters() {\n      return this.characters.filter(function (_char6) {\n        return _char6.is_favorite;\n      }).length;\n    },\n    recentCharacters: function recentCharacters() {\n      var oneWeekAgo = new Date();\n      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);\n      return this.characters.filter(function (_char7) {\n        return new Date(_char7.updated_at || _char7.created_at) > oneWeekAgo;\n      }).length;\n    }\n  }),\n  created: function created() {\n    var _this2 = this;\n    return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {\n      return _regenerator().w(function (_context) {\n        while (1) switch (_context.n) {\n          case 0:\n            _context.n = 1;\n            return _this2.fetchCharacters();\n          case 1:\n            _this2.restoreViewSettings();\n          case 2:\n            return _context.a(2);\n        }\n      }, _callee);\n    }))();\n  },\n  beforeUnmount: function beforeUnmount() {\n    this.saveViewSettings();\n  },\n  methods: {\n    fetchCharacters: function fetchCharacters() {\n      var _this3 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee2() {\n        var _t;\n        return _regenerator().w(function (_context2) {\n          while (1) switch (_context2.n) {\n            case 0:\n              _context2.p = 0;\n              _this3.loading = true;\n              _context2.n = 1;\n              return _this3.$store.dispatch('fetchCharacters');\n            case 1:\n              _this3.characters = _this3.userCharacters || [];\n              _context2.n = 3;\n              break;\n            case 2:\n              _context2.p = 2;\n              _t = _context2.v;\n              console.error('加载角色失败:', _t);\n              _this3.$message.error('加载角色失败');\n            case 3:\n              _context2.p = 3;\n              _this3.loading = false;\n              return _context2.f(3);\n            case 4:\n              return _context2.a(2);\n          }\n        }, _callee2, null, [[0, 2, 3, 4]]);\n      }))();\n    },\n    createCharacter: function createCharacter() {\n      this.$router.push({\n        name: 'CharacterCreator'\n      });\n    },\n    selectCharacter: function selectCharacter(character) {\n      // 可以添加选中角色的逻辑\n      console.log('选中角色:', character);\n    },\n    viewCharacter: function viewCharacter(character) {\n      console.log('🔍 查看角色:', character);\n      this.selectedCharacter = character;\n      this.showCharacterView = true;\n      console.log('🔍 showCharacterView:', this.showCharacterView);\n      console.log('🔍 selectedCharacter:', this.selectedCharacter);\n    },\n    editCharacter: function editCharacter(character) {\n      this.selectedCharacter = _objectSpread({}, character);\n      this.showEditForm = true;\n    },\n    updateCharacter: function updateCharacter(characterData) {\n      var _this4 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee3() {\n        var _t2;\n        return _regenerator().w(function (_context3) {\n          while (1) switch (_context3.n) {\n            case 0:\n              _context3.p = 0;\n              _context3.n = 1;\n              return _this4.$store.dispatch('updateCharacter', _objectSpread({\n                id: _this4.selectedCharacter.id\n              }, characterData));\n            case 1:\n              if (!characterData.avatarFile) {\n                _context3.n = 2;\n                break;\n              }\n              _context3.n = 2;\n              return _this4.uploadCharacterAvatar(_this4.selectedCharacter.id, characterData.avatarFile);\n            case 2:\n              _this4.showEditForm = false;\n              _context3.n = 3;\n              return _this4.fetchCharacters();\n            case 3:\n              _this4.$message.success('角色更新成功');\n              _context3.n = 5;\n              break;\n            case 4:\n              _context3.p = 4;\n              _t2 = _context3.v;\n              console.error('更新角色失败:', _t2);\n              _this4.$message.error('更新角色失败');\n            case 5:\n              return _context3.a(2);\n          }\n        }, _callee3, null, [[0, 4]]);\n      }))();\n    },\n    duplicateCharacter: function duplicateCharacter(character) {\n      var _this5 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee4() {\n        var newName, duplicatedData, _error$response, _t3;\n        return _regenerator().w(function (_context4) {\n          while (1) switch (_context4.n) {\n            case 0:\n              _context4.p = 0;\n              // 生成唯一的复制名称\n              newName = _this5.generateDuplicateName(character.name);\n              duplicatedData = _objectSpread(_objectSpread({}, character), {}, {\n                name: newName,\n                id: undefined,\n                created_at: undefined,\n                updated_at: undefined,\n                // 清除头像数据，让复制的角色使用默认头像\n                avatar_data: null,\n                avatar_filename: null,\n                avatar_content_type: null\n              });\n              _context4.n = 1;\n              return _this5.$store.dispatch('createCharacter', duplicatedData);\n            case 1:\n              _context4.n = 2;\n              return _this5.fetchCharacters();\n            case 2:\n              _this5.$message.success(\"\\u89D2\\u8272\\u590D\\u5236\\u6210\\u529F\\uFF0C\\u65B0\\u89D2\\u8272\\u540D\\u79F0\\uFF1A\".concat(newName));\n              _context4.n = 4;\n              break;\n            case 3:\n              _context4.p = 3;\n              _t3 = _context4.v;\n              console.error('复制角色失败:', _t3);\n              _this5.$message.error('复制角色失败: ' + (((_error$response = _t3.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.detail) || _t3.message));\n            case 4:\n              return _context4.a(2);\n          }\n        }, _callee4, null, [[0, 3]]);\n      }))();\n    },\n    generateDuplicateName: function generateDuplicateName(originalName) {\n      var existingNames = this.characters.map(function (c) {\n        return c.name;\n      });\n      var newName = \"\".concat(originalName, \" (\\u526F\\u672C)\");\n      var counter = 1;\n\n      // 如果名称已存在，添加数字后缀\n      while (existingNames.includes(newName)) {\n        counter++;\n        newName = \"\".concat(originalName, \" (\\u526F\\u672C\").concat(counter, \")\");\n      }\n      return newName;\n    },\n    deleteCharacter: function deleteCharacter(character) {\n      var _this6 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee5() {\n        var confirmed, _error$response2, _t4;\n        return _regenerator().w(function (_context5) {\n          while (1) switch (_context5.n) {\n            case 0:\n              _context5.n = 1;\n              return _this6.showDeleteConfirmDialog(character);\n            case 1:\n              confirmed = _context5.v;\n              if (confirmed) {\n                _context5.n = 2;\n                break;\n              }\n              return _context5.a(2);\n            case 2:\n              _context5.p = 2;\n              _context5.n = 3;\n              return _this6.$store.dispatch('deleteCharacter', character.id);\n            case 3:\n              _context5.n = 4;\n              return _this6.fetchCharacters();\n            case 4:\n              _this6.$message.success(\"\\u89D2\\u8272 \\\"\".concat(character.name, \"\\\" \\u5220\\u9664\\u6210\\u529F\"));\n              _context5.n = 6;\n              break;\n            case 5:\n              _context5.p = 5;\n              _t4 = _context5.v;\n              console.error('删除角色失败:', _t4);\n              _this6.$message.error('删除角色失败: ' + (((_error$response2 = _t4.response) === null || _error$response2 === void 0 || (_error$response2 = _error$response2.data) === null || _error$response2 === void 0 ? void 0 : _error$response2.detail) || _t4.message));\n            case 6:\n              return _context5.a(2);\n          }\n        }, _callee5, null, [[2, 5]]);\n      }))();\n    },\n    showDeleteConfirmDialog: function showDeleteConfirmDialog(character) {\n      return new Promise(function (resolve) {\n        var confirmed = confirm(\"\\u26A0\\uFE0F \\u786E\\u5B9A\\u8981\\u5220\\u9664\\u89D2\\u8272 \\\"\".concat(character.name, \"\\\" \\u5417\\uFF1F\\n\\n\") + \"\\u89D2\\u8272\\u4FE1\\u606F\\uFF1A\\n\" + \"\\u2022 \\u804C\\u4E1A\\uFF1A\".concat(character.occupation || '未知', \"\\n\") + \"\\u2022 \\u7B49\\u7EA7\\uFF1A\".concat(character.level || '未知', \"\\n\") + \"\\u2022 \\u521B\\u5EFA\\u65F6\\u95F4\\uFF1A\".concat(character.created_at ? new Date(character.created_at).toLocaleDateString() : '未知', \"\\n\\n\") + \"\\u26A0\\uFE0F \\u6B64\\u64CD\\u4F5C\\u4E0D\\u53EF\\u64A4\\u9500\\uFF0C\\u89D2\\u8272\\u7684\\u6240\\u6709\\u6570\\u636E\\u5C06\\u88AB\\u6C38\\u4E45\\u5220\\u9664\\uFF01\");\n        resolve(confirmed);\n      });\n    },\n    toggleFavorite: function toggleFavorite(character) {\n      var _this7 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee6() {\n        var _t5;\n        return _regenerator().w(function (_context6) {\n          while (1) switch (_context6.n) {\n            case 0:\n              _context6.p = 0;\n              _context6.n = 1;\n              return _this7.$store.dispatch('updateCharacter', {\n                id: character.id,\n                is_favorite: !character.is_favorite\n              });\n            case 1:\n              _context6.n = 2;\n              return _this7.fetchCharacters();\n            case 2:\n              _context6.n = 4;\n              break;\n            case 3:\n              _context6.p = 3;\n              _t5 = _context6.v;\n              console.error('更新收藏状态失败:', _t5);\n            case 4:\n              return _context6.a(2);\n          }\n        }, _callee6, null, [[0, 3]]);\n      }))();\n    },\n    closeEditForm: function closeEditForm() {\n      this.showEditForm = false;\n      this.selectedCharacter = null;\n    },\n    closeCharacterView: function closeCharacterView() {\n      this.showCharacterView = false;\n      this.selectedCharacter = null;\n    },\n    // 从查看模态框中进行操作\n    editCharacterFromView: function editCharacterFromView() {\n      this.showCharacterView = false;\n      this.editCharacter(this.selectedCharacter);\n    },\n    duplicateCharacterFromView: function duplicateCharacterFromView() {\n      var _this8 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee7() {\n        return _regenerator().w(function (_context7) {\n          while (1) switch (_context7.n) {\n            case 0:\n              _this8.showCharacterView = false;\n              _context7.n = 1;\n              return _this8.duplicateCharacter(_this8.selectedCharacter);\n            case 1:\n              return _context7.a(2);\n          }\n        }, _callee7);\n      }))();\n    },\n    deleteCharacterFromView: function deleteCharacterFromView() {\n      var _this9 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee8() {\n        var character;\n        return _regenerator().w(function (_context8) {\n          while (1) switch (_context8.n) {\n            case 0:\n              character = _this9.selectedCharacter;\n              _this9.showCharacterView = false;\n              _context8.n = 1;\n              return _this9.deleteCharacter(character);\n            case 1:\n              return _context8.a(2);\n          }\n        }, _callee8);\n      }))();\n    },\n    // 头像上传方法\n    uploadCharacterAvatar: function uploadCharacterAvatar(characterId, avatarFile) {\n      var _this0 = this;\n      return _asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee9() {\n        var formData, response, _t6;\n        return _regenerator().w(function (_context9) {\n          while (1) switch (_context9.n) {\n            case 0:\n              _context9.p = 0;\n              formData = new FormData();\n              formData.append('file', avatarFile);\n              _context9.n = 1;\n              return _this0.$http.post(\"/api/characters/\".concat(characterId, \"/avatar\"), formData, {\n                headers: {\n                  'Content-Type': 'multipart/form-data'\n                }\n              });\n            case 1:\n              response = _context9.v;\n              if (!response.data.success) {\n                _context9.n = 2;\n                break;\n              }\n              console.log('✅ 头像上传成功');\n              return _context9.a(2, true);\n            case 2:\n              console.error('❌ 头像上传失败:', response.data.message);\n              throw new Error(response.data.message || '头像上传失败');\n            case 3:\n              _context9.n = 5;\n              break;\n            case 4:\n              _context9.p = 4;\n              _t6 = _context9.v;\n              console.error('❌ 头像上传错误:', _t6);\n              throw _t6;\n            case 5:\n              return _context9.a(2);\n          }\n        }, _callee9, null, [[0, 4]]);\n      }))();\n    },\n    // 获取角色头像URL\n    getCharacterAvatar: function getCharacterAvatar(character) {\n      if (character && character.id) {\n        // 尝试从API获取头像，如果失败则使用默认头像\n        return \"http://localhost:8000/api/characters/\".concat(character.id, \"/avatar\");\n      }\n      return this.defaultAvatar;\n    },\n    // 获取属性中文名称\n    getAttributeName: function getAttributeName(key) {\n      var attributeNames = {\n        'STR': '力量',\n        'CON': '体质',\n        'SIZ': '体型',\n        'DEX': '敏捷',\n        'APP': '外貌',\n        'INT': '智力',\n        'POW': '意志',\n        'EDU': '教育'\n      };\n      return attributeNames[key] || key;\n    },\n    getHPPercentage: function getHPPercentage(character) {\n      if (!character.max_hp || character.max_hp === 0) return 0;\n      return Math.max(0, Math.min(100, (character.current_hp || 0) / character.max_hp * 100));\n    },\n    getSANPercentage: function getSANPercentage(character) {\n      if (!character.max_san || character.max_san === 0) return 0;\n      return Math.max(0, Math.min(100, (character.current_san || 0) / character.max_san * 100));\n    },\n    getStatusText: function getStatusText(status) {\n      var statusMap = {\n        'alive': '存活',\n        'dead': '死亡',\n        'insane': '疯狂',\n        'unconscious': '昏迷'\n      };\n      return statusMap[status] || '正常';\n    },\n    saveViewSettings: function saveViewSettings() {\n      var settings = {\n        viewMode: this.viewMode,\n        sortBy: this.sortBy\n      };\n      this.safeSetJSON('character_manager_settings', settings);\n    },\n    restoreViewSettings: function restoreViewSettings() {\n      var settings = this.safeGetJSON('character_manager_settings');\n      if (settings) {\n        try {\n          this.viewMode = settings.viewMode || 'grid';\n          this.sortBy = settings.sortBy || 'name';\n        } catch (error) {\n          console.warn('恢复视图设置失败:', error);\n        }\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "CharacterForm", "CharacterDetailView", "storageMixin", "name", "mixins", "components", "data", "loading", "characters", "showEditForm", "showCharacterView", "<PERSON><PERSON><PERSON><PERSON>", "viewMode", "searchQuery", "selectedOccupation", "selectedStatus", "sortBy", "defaultAvatar", "computed", "_objectSpread", "filteredCharacters", "_this", "filtered", "_toConsumableArray", "query", "toLowerCase", "filter", "char", "includes", "occupation", "status", "sort", "a", "b", "localeCompare", "Date", "created_at", "updated_at", "level", "occupations", "Set", "map", "Boolean", "aliveCharacters", "length", "favoriteCharacters", "is_favorite", "recentCharacters", "oneWeekAgo", "setDate", "getDate", "created", "_this2", "_asyncToGenerator", "_regenerator", "m", "_callee", "w", "_context", "n", "fetchCharacters", "restoreViewSettings", "beforeUnmount", "saveViewSettings", "methods", "_this3", "_callee2", "_t", "_context2", "p", "$store", "dispatch", "userCharacters", "v", "console", "error", "$message", "f", "createCharacter", "$router", "push", "selectCharacter", "character", "log", "viewCharacter", "edit<PERSON><PERSON><PERSON>", "updateCharacter", "characterData", "_this4", "_callee3", "_t2", "_context3", "id", "avatar<PERSON>ile", "uploadCharacterAvatar", "success", "duplicateCharacter", "_this5", "_callee4", "newName", "duplicatedData", "_error$response", "_t3", "_context4", "generateDuplicateName", "undefined", "avatar_data", "avatar_filename", "avatar_content_type", "concat", "response", "detail", "message", "originalName", "existingNames", "c", "counter", "deleteCharacter", "_this6", "_callee5", "confirmed", "_error$response2", "_t4", "_context5", "showDeleteConfirmDialog", "Promise", "resolve", "confirm", "toLocaleDateString", "toggleFavorite", "_this7", "_callee6", "_t5", "_context6", "closeEditForm", "closeCharacterView", "editCharacterFromView", "duplicateCharacterFromView", "_this8", "_callee7", "_context7", "deleteCharacterFromView", "_this9", "_callee8", "_context8", "characterId", "_this0", "_callee9", "formData", "_t6", "_context9", "FormData", "append", "$http", "post", "headers", "Error", "getCharacterAvatar", "getAttributeName", "key", "attributeNames", "getHPPercentage", "max_hp", "Math", "max", "min", "current_hp", "getSANPercentage", "max_san", "current_san", "getStatusText", "statusMap", "settings", "safeSetJSON", "safeGetJSON", "warn"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\views\\CharacterManager.vue"], "sourcesContent": ["<template>\r\n  <div class=\"character-manager\">\r\n    <!-- 页面头部 -->\r\n    <div class=\"page-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"header-left\">\r\n          <h1 class=\"page-title\">\r\n            <i class=\"fas fa-user-friends\"></i>\r\n            <span>角色管理</span>\r\n          </h1>\r\n          <p class=\"page-subtitle\">管理您的COC角色，创建新的冒险者</p>\r\n        </div>\r\n        \r\n        <div class=\"header-actions\">\r\n          <div class=\"view-controls\">\r\n            <button \r\n              @click=\"viewMode = 'grid'\" \r\n              class=\"view-btn\"\r\n              :class=\"{ 'active': viewMode === 'grid' }\"\r\n              title=\"网格视图\"\r\n            >\r\n              <i class=\"fas fa-th\"></i>\r\n            </button>\r\n            <button \r\n              @click=\"viewMode = 'list'\" \r\n              class=\"view-btn\"\r\n              :class=\"{ 'active': viewMode === 'list' }\"\r\n              title=\"列表视图\"\r\n            >\r\n              <i class=\"fas fa-list\"></i>\r\n            </button>\r\n          </div>\r\n          \r\n          <div class=\"search-box\">\r\n            <i class=\"fas fa-search search-icon\"></i>\r\n            <input \r\n              v-model=\"searchQuery\" \r\n              type=\"text\" \r\n              placeholder=\"搜索角色...\" \r\n              class=\"search-input\"\r\n            />\r\n          </div>\r\n          \r\n          <button @click=\"createCharacter\" class=\"create-btn primary\">\r\n            <i class=\"fas fa-plus\"></i>\r\n            <span>创建角色</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 统计信息 -->\r\n    <div class=\"stats-section\" v-if=\"characters.length > 0\">\r\n      <div class=\"stats-grid\">\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-users\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ characters.length }}</div>\r\n            <div class=\"stat-label\">总角色数</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-heart\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ aliveCharacters }}</div>\r\n            <div class=\"stat-label\">存活角色</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-star\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ favoriteCharacters }}</div>\r\n            <div class=\"stat-label\">收藏角色</div>\r\n          </div>\r\n        </div>\r\n        <div class=\"stat-card\">\r\n          <div class=\"stat-icon\">\r\n            <i class=\"fas fa-clock\"></i>\r\n          </div>\r\n          <div class=\"stat-content\">\r\n            <div class=\"stat-number\">{{ recentCharacters }}</div>\r\n            <div class=\"stat-label\">最近使用</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 筛选和排序 -->\r\n    <div class=\"filter-section\" v-if=\"characters.length > 0\">\r\n      <div class=\"filter-controls\">\r\n        <div class=\"filter-group\">\r\n          <label class=\"filter-label\">职业筛选:</label>\r\n          <select v-model=\"selectedOccupation\" class=\"filter-select\">\r\n            <option value=\"\">全部职业</option>\r\n            <option v-for=\"occupation in occupations\" :key=\"occupation\" :value=\"occupation\">\r\n              {{ occupation }}\r\n            </option>\r\n          </select>\r\n        </div>\r\n        \r\n        <div class=\"filter-group\">\r\n          <label class=\"filter-label\">状态筛选:</label>\r\n          <select v-model=\"selectedStatus\" class=\"filter-select\">\r\n            <option value=\"\">全部状态</option>\r\n            <option value=\"alive\">存活</option>\r\n            <option value=\"dead\">死亡</option>\r\n            <option value=\"insane\">疯狂</option>\r\n          </select>\r\n        </div>\r\n        \r\n        <div class=\"filter-group\">\r\n          <label class=\"filter-label\">排序方式:</label>\r\n          <select v-model=\"sortBy\" class=\"filter-select\">\r\n            <option value=\"name\">按名称</option>\r\n            <option value=\"created\">按创建时间</option>\r\n            <option value=\"updated\">按更新时间</option>\r\n            <option value=\"level\">按等级</option>\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-state\">\r\n      <div class=\"loading-spinner\"></div>\r\n      <p>正在加载角色数据...</p>\r\n    </div>\r\n    \r\n    <!-- 空状态 -->\r\n    <div v-else-if=\"characters.length === 0\" class=\"empty-state\">\r\n      <div class=\"empty-icon\">\r\n        <i class=\"fas fa-user-plus\"></i>\r\n      </div>\r\n      <h3 class=\"empty-title\">还没有角色</h3>\r\n      <p class=\"empty-description\">创建您的第一个COC角色，开始您的克苏鲁冒险之旅</p>\r\n      <button @click=\"createCharacter\" class=\"create-btn primary large\">\r\n        <i class=\"fas fa-plus\"></i>\r\n        <span>创建第一个角色</span>\r\n      </button>\r\n    </div>\r\n    \r\n    <!-- 角色列表 -->\r\n    <div v-else class=\"characters-section\">\r\n      <!-- 网格视图 -->\r\n      <div v-if=\"viewMode === 'grid'\" class=\"character-grid\">\r\n        <div \r\n          v-for=\"character in filteredCharacters\" \r\n          :key=\"character.id\" \r\n          class=\"character-card\"\r\n          :class=\"{ 'favorite': character.is_favorite }\"\r\n          @click=\"selectCharacter(character)\"\r\n        >\r\n          <!-- 角色头像和状态 -->\r\n          <div class=\"character-avatar-section\">\r\n            <div class=\"character-avatar\">\r\n              <img :src=\"getCharacterAvatar(character)\" :alt=\"character.name\" />\r\n              <div class=\"character-status-indicator\" :class=\"character.status\"></div>\r\n            </div>\r\n            <button \r\n              @click.stop=\"toggleFavorite(character)\" \r\n              class=\"favorite-btn\"\r\n              :class=\"{ 'active': character.is_favorite }\"\r\n            >\r\n              <i class=\"fas fa-star\"></i>\r\n            </button>\r\n          </div>\r\n          \r\n          <!-- 角色基本信息 -->\r\n          <div class=\"character-basic-info\">\r\n            <h3 class=\"character-name\">{{ character.name }}</h3>\r\n            <div class=\"character-meta\">\r\n              <span class=\"character-occupation\">{{ character.occupation || '未知职业' }}</span>\r\n              <span class=\"character-age\">{{ character.age || '?' }}岁</span>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 角色属性预览 -->\r\n          <div class=\"character-attributes\" v-if=\"character.attributes\">\r\n            <div class=\"attribute-row\">\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">力量</span>\r\n                <span class=\"attr-value\">{{ character.attributes.STR || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">体质</span>\r\n                <span class=\"attr-value\">{{ character.attributes.CON || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">敏捷</span>\r\n                <span class=\"attr-value\">{{ character.attributes.DEX || 0 }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"attribute-row\">\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">智力</span>\r\n                <span class=\"attr-value\">{{ character.attributes.INT || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">意志</span>\r\n                <span class=\"attr-value\">{{ character.attributes.POW || 0 }}</span>\r\n              </div>\r\n              <div class=\"attribute-item\">\r\n                <span class=\"attr-label\">魅力</span>\r\n                <span class=\"attr-value\">{{ character.attributes.APP || 0 }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 生命值和理智值 -->\r\n          <div class=\"character-vitals\">\r\n            <div class=\"vital-item\">\r\n              <div class=\"vital-label\">\r\n                <i class=\"fas fa-heart\"></i>\r\n                <span>HP</span>\r\n              </div>\r\n              <div class=\"vital-bar\">\r\n                <div \r\n                  class=\"vital-fill hp\" \r\n                  :style=\"{ width: `${getHPPercentage(character)}%` }\"\r\n                ></div>\r\n                <span class=\"vital-text\">{{ character.current_hp || 0 }}/{{ character.max_hp || 0 }}</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"vital-item\">\r\n              <div class=\"vital-label\">\r\n                <i class=\"fas fa-brain\"></i>\r\n                <span>SAN</span>\r\n              </div>\r\n              <div class=\"vital-bar\">\r\n                <div \r\n                  class=\"vital-fill san\" \r\n                  :style=\"{ width: `${getSANPercentage(character)}%` }\"\r\n                ></div>\r\n                <span class=\"vital-text\">{{ character.current_san || 0 }}/{{ character.max_san || 0 }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <!-- 角色操作按钮 -->\r\n          <div class=\"character-actions\">\r\n            <button @click.stop=\"viewCharacter(character)\" class=\"action-btn view\">\r\n              <i class=\"fas fa-eye\"></i>\r\n              <span>查看</span>\r\n            </button>\r\n            <button @click.stop=\"editCharacter(character)\" class=\"action-btn edit\">\r\n              <i class=\"fas fa-edit\"></i>\r\n              <span>编辑</span>\r\n            </button>\r\n            <button @click.stop=\"duplicateCharacter(character)\" class=\"action-btn duplicate\">\r\n              <i class=\"fas fa-copy\"></i>\r\n              <span>复制</span>\r\n            </button>\r\n            <button @click.stop=\"deleteCharacter(character)\" class=\"action-btn delete\">\r\n              <i class=\"fas fa-trash\"></i>\r\n              <span>删除</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 列表视图 -->\r\n      <div v-else class=\"character-list\">\r\n        <div class=\"list-header\">\r\n          <div class=\"list-col name\">角色名称</div>\r\n          <div class=\"list-col occupation\">职业</div>\r\n          <div class=\"list-col age\">年龄</div>\r\n          <div class=\"list-col vitals\">生命/理智</div>\r\n          <div class=\"list-col status\">状态</div>\r\n          <div class=\"list-col actions\">操作</div>\r\n        </div>\r\n        \r\n        <div \r\n          v-for=\"character in filteredCharacters\" \r\n          :key=\"character.id\" \r\n          class=\"list-item\"\r\n          @click=\"selectCharacter(character)\"\r\n        >\r\n          <div class=\"list-col name\">\r\n            <div class=\"character-name-cell\">\r\n              <img :src=\"getCharacterAvatar(character)\" :alt=\"character.name\" class=\"list-avatar\" />\r\n              <div>\r\n                <div class=\"character-name\">{{ character.name }}</div>\r\n                <div class=\"character-id\">ID: {{ character.id }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"list-col occupation\">{{ character.occupation || '未知' }}</div>\r\n          <div class=\"list-col age\">{{ character.age || '?' }}岁</div>\r\n          <div class=\"list-col vitals\">\r\n            <div class=\"vitals-cell\">\r\n              <div class=\"vital-mini\">\r\n                <span class=\"vital-label\">HP:</span>\r\n                <span class=\"vital-value\">{{ character.current_hp || 0 }}/{{ character.max_hp || 0 }}</span>\r\n              </div>\r\n              <div class=\"vital-mini\">\r\n                <span class=\"vital-label\">SAN:</span>\r\n                <span class=\"vital-value\">{{ character.current_san || 0 }}/{{ character.max_san || 0 }}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"list-col status\">\r\n            <span class=\"status-badge\" :class=\"character.status\">\r\n              {{ getStatusText(character.status) }}\r\n            </span>\r\n          </div>\r\n          <div class=\"list-col actions\">\r\n            <div class=\"list-actions\">\r\n              <button @click.stop=\"viewCharacter(character)\" class=\"list-action-btn\" title=\"查看\">\r\n                <i class=\"fas fa-eye\"></i>\r\n              </button>\r\n              <button @click.stop=\"editCharacter(character)\" class=\"list-action-btn\" title=\"编辑\">\r\n                <i class=\"fas fa-edit\"></i>\r\n              </button>\r\n              <button @click.stop=\"duplicateCharacter(character)\" class=\"list-action-btn\" title=\"复制\">\r\n                <i class=\"fas fa-copy\"></i>\r\n              </button>\r\n              <button @click.stop=\"deleteCharacter(character)\" class=\"list-action-btn delete\" title=\"删除\">\r\n                <i class=\"fas fa-trash\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <!-- 编辑模态框 -->\r\n    <div v-if=\"showEditForm\" class=\"character-edit-modal-overlay\" @click=\"closeEditForm\">\r\n      <div class=\"character-edit-modal\" @click.stop>\r\n        <div class=\"character-edit-modal-header\">\r\n          <h3 class=\"modal-title\">编辑角色</h3>\r\n          <button @click=\"closeEditForm\" class=\"modal-close\">\r\n            <i class=\"fas fa-times\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"character-edit-modal-body\">\r\n          <character-form\r\n            :is-edit=\"true\"\r\n            :character=\"selectedCharacter\"\r\n            @submit=\"updateCharacter\"\r\n            @cancel=\"closeEditForm\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <div v-if=\"showCharacterView\" class=\"character-view-modal-overlay\" @click=\"closeCharacterView\">\r\n      <div class=\"character-view-modal\" @click.stop>\r\n        <div class=\"character-view-modal-header\">\r\n          <div class=\"modal-title-section\">\r\n            <div class=\"character-avatar-small\">\r\n              <img :src=\"getCharacterAvatar(selectedCharacter)\" :alt=\"selectedCharacter?.name\" />\r\n            </div>\r\n            <div class=\"character-title-info\">\r\n              <h3 class=\"modal-title\">{{ selectedCharacter?.name }}</h3>\r\n              <p class=\"character-subtitle\">{{ selectedCharacter?.occupation }} • ID: {{ selectedCharacter?.id }}</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"modal-actions\">\r\n            <button @click=\"editCharacterFromView\" class=\"modal-action-btn edit\" title=\"编辑角色\">\r\n              <i class=\"fas fa-edit\"></i>\r\n            </button>\r\n            <button @click=\"duplicateCharacterFromView\" class=\"modal-action-btn duplicate\" title=\"复制角色\">\r\n              <i class=\"fas fa-copy\"></i>\r\n            </button>\r\n            <button @click=\"deleteCharacterFromView\" class=\"modal-action-btn delete\" title=\"删除角色\">\r\n              <i class=\"fas fa-trash\"></i>\r\n            </button>\r\n            <button @click=\"closeCharacterView\" class=\"modal-close\">\r\n              <i class=\"fas fa-times\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n        <div class=\"character-view-modal-body\">\r\n          <character-detail-view :character=\"selectedCharacter\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from 'vuex'\r\nimport CharacterForm from '@/components/CharacterForm.vue'\r\nimport CharacterDetailView from '@/components/CharacterDetailView.vue'\r\nimport { storageMixin } from '@/mixins/storageMixin'\r\n\r\nexport default {\r\n  name: 'CharacterManager',\r\n  mixins: [storageMixin],\r\n  components: {\r\n    CharacterForm,\r\n    CharacterDetailView\r\n  },\r\n  data() {\r\n    return {\r\n      loading: true,\r\n      characters: [],\r\n      showEditForm: false,\r\n      showCharacterView: false,\r\n      selectedCharacter: null,\r\n      viewMode: 'grid', // 'grid' or 'list'\r\n      searchQuery: '',\r\n      selectedOccupation: '',\r\n      selectedStatus: '',\r\n      sortBy: 'name',\r\n      defaultAvatar: '/images/default-character.png'\r\n    }\r\n  },\r\n  computed: {\r\n    ...mapGetters(['userCharacters']),\r\n    \r\n    filteredCharacters() {\r\n      let filtered = [...this.characters]\r\n      \r\n      // 搜索过滤\r\n      if (this.searchQuery) {\r\n        const query = this.searchQuery.toLowerCase()\r\n        filtered = filtered.filter(char => \r\n          char.name.toLowerCase().includes(query) ||\r\n          (char.occupation && char.occupation.toLowerCase().includes(query))\r\n        )\r\n      }\r\n      \r\n      // 职业过滤\r\n      if (this.selectedOccupation) {\r\n        filtered = filtered.filter(char => char.occupation === this.selectedOccupation)\r\n      }\r\n      \r\n      // 状态过滤\r\n      if (this.selectedStatus) {\r\n        filtered = filtered.filter(char => char.status === this.selectedStatus)\r\n      }\r\n      \r\n      // 排序\r\n      filtered.sort((a, b) => {\r\n        switch (this.sortBy) {\r\n          case 'name':\r\n            return a.name.localeCompare(b.name)\r\n          case 'created':\r\n            return new Date(b.created_at) - new Date(a.created_at)\r\n          case 'updated':\r\n            return new Date(b.updated_at) - new Date(a.updated_at)\r\n          case 'level':\r\n            return (b.level || 0) - (a.level || 0)\r\n          default:\r\n            return 0\r\n        }\r\n      })\r\n      \r\n      return filtered\r\n    },\r\n    \r\n    occupations() {\r\n      const occupations = [...new Set(this.characters.map(char => char.occupation).filter(Boolean))]\r\n      return occupations.sort()\r\n    },\r\n    \r\n    aliveCharacters() {\r\n      return this.characters.filter(char => char.status === 'alive' || !char.status).length\r\n    },\r\n    \r\n    favoriteCharacters() {\r\n      return this.characters.filter(char => char.is_favorite).length\r\n    },\r\n    \r\n    recentCharacters() {\r\n      const oneWeekAgo = new Date()\r\n      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)\r\n      return this.characters.filter(char => \r\n        new Date(char.updated_at || char.created_at) > oneWeekAgo\r\n      ).length\r\n    }\r\n  },\r\n  async created() {\r\n    await this.fetchCharacters()\r\n    this.restoreViewSettings()\r\n  },\r\n  beforeUnmount() {\r\n    this.saveViewSettings()\r\n  },\r\n  methods: {\r\n    async fetchCharacters() {\r\n      try {\r\n        this.loading = true\r\n        await this.$store.dispatch('fetchCharacters')\r\n        this.characters = this.userCharacters || []\r\n      } catch (error) {\r\n        console.error('加载角色失败:', error)\r\n        this.$message.error('加载角色失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    createCharacter() {\r\n      this.$router.push({ name: 'CharacterCreator' })\r\n    },\r\n    \r\n    selectCharacter(character) {\r\n      // 可以添加选中角色的逻辑\r\n      console.log('选中角色:', character)\r\n    },\r\n    \r\n    viewCharacter(character) {\r\n      console.log('🔍 查看角色:', character)\r\n      this.selectedCharacter = character\r\n      this.showCharacterView = true\r\n      console.log('🔍 showCharacterView:', this.showCharacterView)\r\n      console.log('🔍 selectedCharacter:', this.selectedCharacter)\r\n    },\r\n    \r\n    editCharacter(character) {\r\n      this.selectedCharacter = { ...character }\r\n      this.showEditForm = true\r\n    },\r\n    \r\n    async updateCharacter(characterData) {\r\n      try {\r\n        // 先更新角色基本信息\r\n        await this.$store.dispatch('updateCharacter', {\r\n          id: this.selectedCharacter.id,\r\n          ...characterData\r\n        })\r\n\r\n        // 如果有头像文件，单独上传头像\r\n        if (characterData.avatarFile) {\r\n          await this.uploadCharacterAvatar(this.selectedCharacter.id, characterData.avatarFile)\r\n        }\r\n\r\n        this.showEditForm = false\r\n        await this.fetchCharacters()\r\n        this.$message.success('角色更新成功')\r\n      } catch (error) {\r\n        console.error('更新角色失败:', error)\r\n        this.$message.error('更新角色失败')\r\n      }\r\n    },\r\n    \r\n    async duplicateCharacter(character) {\r\n      try {\r\n        // 生成唯一的复制名称\r\n        const newName = this.generateDuplicateName(character.name)\r\n\r\n        const duplicatedData = {\r\n          ...character,\r\n          name: newName,\r\n          id: undefined,\r\n          created_at: undefined,\r\n          updated_at: undefined,\r\n          // 清除头像数据，让复制的角色使用默认头像\r\n          avatar_data: null,\r\n          avatar_filename: null,\r\n          avatar_content_type: null\r\n        }\r\n\r\n        await this.$store.dispatch('createCharacter', duplicatedData)\r\n        await this.fetchCharacters()\r\n        this.$message.success(`角色复制成功，新角色名称：${newName}`)\r\n      } catch (error) {\r\n        console.error('复制角色失败:', error)\r\n        this.$message.error('复制角色失败: ' + (error.response?.data?.detail || error.message))\r\n      }\r\n    },\r\n\r\n    generateDuplicateName(originalName) {\r\n      const existingNames = this.characters.map(c => c.name)\r\n      let newName = `${originalName} (副本)`\r\n      let counter = 1\r\n\r\n      // 如果名称已存在，添加数字后缀\r\n      while (existingNames.includes(newName)) {\r\n        counter++\r\n        newName = `${originalName} (副本${counter})`\r\n      }\r\n\r\n      return newName\r\n    },\r\n    \r\n    async deleteCharacter(character) {\r\n      // 使用更友好的确认对话框\r\n      const confirmed = await this.showDeleteConfirmDialog(character)\r\n      if (!confirmed) {\r\n        return\r\n      }\r\n\r\n      try {\r\n        await this.$store.dispatch('deleteCharacter', character.id)\r\n        await this.fetchCharacters()\r\n        this.$message.success(`角色 \"${character.name}\" 删除成功`)\r\n      } catch (error) {\r\n        console.error('删除角色失败:', error)\r\n        this.$message.error('删除角色失败: ' + (error.response?.data?.detail || error.message))\r\n      }\r\n    },\r\n\r\n    showDeleteConfirmDialog(character) {\r\n      return new Promise((resolve) => {\r\n        const confirmed = confirm(\r\n          `⚠️ 确定要删除角色 \"${character.name}\" 吗？\\n\\n` +\r\n          `角色信息：\\n` +\r\n          `• 职业：${character.occupation || '未知'}\\n` +\r\n          `• 等级：${character.level || '未知'}\\n` +\r\n          `• 创建时间：${character.created_at ? new Date(character.created_at).toLocaleDateString() : '未知'}\\n\\n` +\r\n          `⚠️ 此操作不可撤销，角色的所有数据将被永久删除！`\r\n        )\r\n        resolve(confirmed)\r\n      })\r\n    },\r\n    \r\n    async toggleFavorite(character) {\r\n      try {\r\n        await this.$store.dispatch('updateCharacter', {\r\n          id: character.id,\r\n          is_favorite: !character.is_favorite\r\n        })\r\n        await this.fetchCharacters()\r\n      } catch (error) {\r\n        console.error('更新收藏状态失败:', error)\r\n      }\r\n    },\r\n    \r\n    closeEditForm() {\r\n      this.showEditForm = false\r\n      this.selectedCharacter = null\r\n    },\r\n    \r\n    closeCharacterView() {\r\n      this.showCharacterView = false\r\n      this.selectedCharacter = null\r\n    },\r\n\r\n    // 从查看模态框中进行操作\r\n    editCharacterFromView() {\r\n      this.showCharacterView = false\r\n      this.editCharacter(this.selectedCharacter)\r\n    },\r\n\r\n    async duplicateCharacterFromView() {\r\n      this.showCharacterView = false\r\n      await this.duplicateCharacter(this.selectedCharacter)\r\n    },\r\n\r\n    async deleteCharacterFromView() {\r\n      const character = this.selectedCharacter\r\n      this.showCharacterView = false\r\n      await this.deleteCharacter(character)\r\n    },\r\n\r\n    // 头像上传方法\r\n    async uploadCharacterAvatar(characterId, avatarFile) {\r\n      try {\r\n        const formData = new FormData()\r\n        formData.append('file', avatarFile)\r\n\r\n        const response = await this.$http.post(`/api/characters/${characterId}/avatar`, formData, {\r\n          headers: {\r\n            'Content-Type': 'multipart/form-data'\r\n          }\r\n        })\r\n\r\n        if (response.data.success) {\r\n          console.log('✅ 头像上传成功')\r\n          return true\r\n        } else {\r\n          console.error('❌ 头像上传失败:', response.data.message)\r\n          throw new Error(response.data.message || '头像上传失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ 头像上传错误:', error)\r\n        throw error\r\n      }\r\n    },\r\n\r\n    // 获取角色头像URL\r\n    getCharacterAvatar(character) {\r\n      if (character && character.id) {\r\n        // 尝试从API获取头像，如果失败则使用默认头像\r\n        return `http://localhost:8000/api/characters/${character.id}/avatar`\r\n      }\r\n      return this.defaultAvatar\r\n    },\r\n\r\n    // 获取属性中文名称\r\n    getAttributeName(key) {\r\n      const attributeNames = {\r\n        'STR': '力量',\r\n        'CON': '体质',\r\n        'SIZ': '体型',\r\n        'DEX': '敏捷',\r\n        'APP': '外貌',\r\n        'INT': '智力',\r\n        'POW': '意志',\r\n        'EDU': '教育'\r\n      }\r\n      return attributeNames[key] || key\r\n    },\r\n    \r\n    getHPPercentage(character) {\r\n      if (!character.max_hp || character.max_hp === 0) return 0\r\n      return Math.max(0, Math.min(100, (character.current_hp || 0) / character.max_hp * 100))\r\n    },\r\n    \r\n    getSANPercentage(character) {\r\n      if (!character.max_san || character.max_san === 0) return 0\r\n      return Math.max(0, Math.min(100, (character.current_san || 0) / character.max_san * 100))\r\n    },\r\n    \r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        'alive': '存活',\r\n        'dead': '死亡',\r\n        'insane': '疯狂',\r\n        'unconscious': '昏迷'\r\n      }\r\n      return statusMap[status] || '正常'\r\n    },\r\n    \r\n    saveViewSettings() {\r\n      const settings = {\r\n        viewMode: this.viewMode,\r\n        sortBy: this.sortBy\r\n      }\r\n      this.safeSetJSON('character_manager_settings', settings)\r\n    },\r\n    \r\n    restoreViewSettings() {\r\n      const settings = this.safeGetJSON('character_manager_settings')\r\n      if (settings) {\r\n        try {\r\n          this.viewMode = settings.viewMode || 'grid'\r\n          this.sortBy = settings.sortBy || 'name'\r\n        } catch (error) {\r\n          console.warn('恢复视图设置失败:', error)\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.character-manager {\r\n  min-height: 100vh;\r\n  background: var(--bg-primary);\r\n  padding: var(--spacing-4);\r\n}\r\n\r\n/* ===== 页面头部 ===== */\r\n.page-header {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.header-left {\r\n  flex: 1;\r\n}\r\n\r\n.page-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  margin: 0 0 var(--spacing-2);\r\n  font-size: var(--font-size-3xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.page-subtitle {\r\n  margin: 0;\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-base);\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.view-controls {\r\n  display: flex;\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  overflow: hidden;\r\n}\r\n\r\n.view-btn {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: none;\r\n  background: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.view-btn:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.view-btn.active {\r\n  background: var(--primary-600);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: var(--spacing-3);\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.search-input {\r\n  padding: var(--spacing-2) var(--spacing-3) var(--spacing-2) var(--spacing-8);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  width: 250px;\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: var(--primary-400);\r\n  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);\r\n}\r\n\r\n.create-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  padding: var(--spacing-2) var(--spacing-4);\r\n  border: none;\r\n  border-radius: var(--radius-md);\r\n  background: var(--primary-600);\r\n  color: var(--text-inverse);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.create-btn:hover {\r\n  background: var(--primary-700);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.create-btn.large {\r\n  padding: var(--spacing-3) var(--spacing-6);\r\n  font-size: var(--font-size-base);\r\n}\r\n\r\n/* ===== 统计信息 ===== */\r\n.stats-section {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.stats-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.stat-card {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n  padding: var(--spacing-4);\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-lg);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.stat-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);\r\n  border-radius: var(--radius-lg);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: var(--text-inverse);\r\n  font-size: var(--font-size-lg);\r\n}\r\n\r\n.stat-content {\r\n  flex: 1;\r\n}\r\n\r\n.stat-number {\r\n  font-size: var(--font-size-2xl);\r\n  font-weight: var(--font-weight-bold);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--spacing-1);\r\n}\r\n\r\n.stat-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-muted);\r\n}\r\n\r\n/* ===== 筛选区域 ===== */\r\n.filter-section {\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  gap: var(--spacing-4);\r\n  padding: var(--spacing-4);\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-lg);\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.filter-label {\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n  white-space: nowrap;\r\n}\r\n\r\n.filter-select {\r\n  padding: var(--spacing-2) var(--spacing-3);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-primary);\r\n  font-size: var(--font-size-sm);\r\n  min-width: 120px;\r\n}\r\n\r\n/* ===== 加载和空状态 ===== */\r\n.loading-state, .empty-state {\r\n  text-align: center;\r\n  padding: var(--spacing-8);\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-xl);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid var(--primary-200);\r\n  border-top: 3px solid var(--primary-600);\r\n  border-radius: var(--radius-full);\r\n  animation: spin 1s linear infinite;\r\n  margin: 0 auto var(--spacing-4);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n.empty-icon {\r\n  font-size: var(--font-size-4xl);\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.empty-title {\r\n  font-size: var(--font-size-xl);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.empty-description {\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-6);\r\n}\r\n\r\n/* ===== 角色网格 ===== */\r\n.character-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\r\n  gap: var(--spacing-4);\r\n}\r\n\r\n.character-card {\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-xl);\r\n  padding: var(--spacing-4);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.character-card:hover {\r\n  transform: translateY(-4px);\r\n  box-shadow: var(--shadow-lg);\r\n  border-color: var(--primary-300);\r\n}\r\n\r\n.character-card.favorite {\r\n  border-color: var(--warning-400);\r\n  background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--warning-50) 100%);\r\n}\r\n\r\n.character-avatar-section {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: flex-start;\r\n  margin-bottom: var(--spacing-3);\r\n}\r\n\r\n.character-avatar {\r\n  position: relative;\r\n  width: 64px;\r\n  height: 64px;\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  border: 3px solid var(--primary-200);\r\n}\r\n\r\n.character-avatar img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.character-status-indicator {\r\n  position: absolute;\r\n  bottom: -2px;\r\n  right: -2px;\r\n  width: 16px;\r\n  height: 16px;\r\n  border-radius: var(--radius-full);\r\n  border: 2px solid var(--bg-elevated);\r\n  background: var(--success-500);\r\n}\r\n\r\n.character-status-indicator.dead {\r\n  background: var(--error-500);\r\n}\r\n\r\n.character-status-indicator.insane {\r\n  background: var(--warning-500);\r\n}\r\n\r\n.favorite-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--radius-full);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.favorite-btn:hover {\r\n  background: var(--warning-100);\r\n  color: var(--warning-600);\r\n}\r\n\r\n.favorite-btn.active {\r\n  background: var(--warning-500);\r\n  color: var(--text-inverse);\r\n}\r\n\r\n.character-basic-info {\r\n  margin-bottom: var(--spacing-3);\r\n}\r\n\r\n.character-name {\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n  margin: 0 0 var(--spacing-1);\r\n}\r\n\r\n.character-meta {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.character-occupation {\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.character-age {\r\n  color: var(--text-muted);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.character-attributes {\r\n  margin-bottom: var(--spacing-3);\r\n}\r\n\r\n.attribute-row {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.attribute-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: var(--spacing-2);\r\n  background: var(--bg-tertiary);\r\n  border-radius: var(--radius-md);\r\n}\r\n\r\n.attr-label {\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n  margin-bottom: var(--spacing-1);\r\n}\r\n\r\n.attr-value {\r\n  font-size: var(--font-size-sm);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.character-vitals {\r\n  margin-bottom: var(--spacing-4);\r\n}\r\n\r\n.vital-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n  margin-bottom: var(--spacing-2);\r\n}\r\n\r\n.vital-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  min-width: 50px;\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.vital-bar {\r\n  flex: 1;\r\n  height: 20px;\r\n  background: var(--gray-200);\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.vital-fill {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  transition: width var(--transition-fast);\r\n}\r\n\r\n.vital-fill.hp {\r\n  background: var(--error-500);\r\n}\r\n\r\n.vital-fill.san {\r\n  background: var(--primary-500);\r\n}\r\n\r\n.vital-text {\r\n  position: relative;\r\n  z-index: 1;\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n  color: var(--text-primary);\r\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.character-actions {\r\n  display: flex;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.action-btn {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  padding: var(--spacing-2);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.action-btn:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.action-btn.view:hover {\r\n  background: var(--primary-50);\r\n  color: var(--primary-600);\r\n  border-color: var(--primary-300);\r\n}\r\n\r\n.action-btn.edit:hover {\r\n  background: var(--warning-50);\r\n  color: var(--warning-600);\r\n  border-color: var(--warning-300);\r\n}\r\n\r\n.action-btn.duplicate:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n}\r\n\r\n.action-btn.delete:hover {\r\n  background: var(--error-50);\r\n  color: var(--error-600);\r\n  border-color: var(--error-300);\r\n}\r\n\r\n/* ===== 列表视图 ===== */\r\n.character-list {\r\n  background: var(--bg-elevated);\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-lg);\r\n  overflow: hidden;\r\n}\r\n\r\n.list-header {\r\n  display: flex;\r\n  background: var(--bg-tertiary);\r\n  border-bottom: 1px solid var(--border-primary);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-secondary);\r\n  font-size: var(--font-size-sm);\r\n}\r\n\r\n.list-item {\r\n  display: flex;\r\n  border-bottom: 1px solid var(--border-primary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n}\r\n\r\n.list-item:hover {\r\n  background: var(--hover-bg);\r\n}\r\n\r\n.list-col {\r\n  padding: var(--spacing-3);\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.list-col.name {\r\n  flex: 2;\r\n}\r\n\r\n.list-col.occupation {\r\n  flex: 1;\r\n}\r\n\r\n.list-col.age {\r\n  flex: 0.8;\r\n}\r\n\r\n.list-col.vitals {\r\n  flex: 1.2;\r\n}\r\n\r\n.list-col.status {\r\n  flex: 0.8;\r\n}\r\n\r\n.list-col.actions {\r\n  flex: 1;\r\n}\r\n\r\n.character-name-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.list-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: var(--radius-full);\r\n  object-fit: cover;\r\n  border: 2px solid var(--primary-200);\r\n}\r\n\r\n.character-id {\r\n  font-size: var(--font-size-xs);\r\n  color: var(--text-muted);\r\n}\r\n\r\n.vitals-cell {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.vital-mini {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-1);\r\n  font-size: var(--font-size-xs);\r\n}\r\n\r\n.vital-label {\r\n  color: var(--text-muted);\r\n}\r\n\r\n.vital-value {\r\n  color: var(--text-primary);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.status-badge {\r\n  padding: var(--spacing-1) var(--spacing-2);\r\n  border-radius: var(--radius-full);\r\n  font-size: var(--font-size-xs);\r\n  font-weight: var(--font-weight-medium);\r\n}\r\n\r\n.status-badge.alive {\r\n  background: var(--success-100);\r\n  color: var(--success-700);\r\n}\r\n\r\n.status-badge.dead {\r\n  background: var(--error-100);\r\n  color: var(--error-700);\r\n}\r\n\r\n.status-badge.insane {\r\n  background: var(--warning-100);\r\n  color: var(--warning-700);\r\n}\r\n\r\n.list-actions {\r\n  display: flex;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.list-action-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.list-action-btn:hover {\r\n  background: var(--primary-100);\r\n  color: var(--primary-600);\r\n}\r\n\r\n.list-action-btn.delete:hover {\r\n  background: var(--error-100);\r\n  color: var(--error-600);\r\n}\r\n\r\n/* ===== 模态框 ===== */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n}\r\n\r\n.modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n  max-width: 90vw;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  animation: modal-appear 0.2s ease-out;\r\n  width: 600px;\r\n}\r\n\r\n.modal.large {\r\n  width: 95vw;\r\n  max-width: 1200px;\r\n  max-height: 90vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n@keyframes modal-appear {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.9) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1) translateY(0);\r\n  }\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.modal-title-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-3);\r\n}\r\n\r\n.character-avatar-small {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: var(--radius-full);\r\n  overflow: hidden;\r\n  border: 2px solid var(--primary-200);\r\n}\r\n\r\n.character-avatar-small img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: cover;\r\n}\r\n\r\n.character-title-info {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: var(--spacing-1);\r\n}\r\n\r\n.character-subtitle {\r\n  margin: 0;\r\n  font-size: var(--font-size-sm);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: var(--spacing-2);\r\n}\r\n\r\n.modal-action-btn {\r\n  width: 36px;\r\n  height: 36px;\r\n  border: 1px solid var(--border-primary);\r\n  border-radius: var(--radius-md);\r\n  background: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-action-btn:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.modal-action-btn.edit:hover {\r\n  background: var(--warning-50);\r\n  color: var(--warning-600);\r\n  border-color: var(--warning-300);\r\n}\r\n\r\n.modal-action-btn.duplicate:hover {\r\n  background: var(--success-50);\r\n  color: var(--success-600);\r\n  border-color: var(--success-300);\r\n}\r\n\r\n.modal-action-btn.delete:hover {\r\n  background: var(--error-50);\r\n  color: var(--error-600);\r\n  border-color: var(--error-300);\r\n}\r\n\r\n.modal-title {\r\n  margin: 0;\r\n  font-size: var(--font-size-lg);\r\n  font-weight: var(--font-weight-semibold);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.modal-close {\r\n  width: 32px;\r\n  height: 32px;\r\n  border: none;\r\n  border-radius: var(--radius-md);\r\n  background: none;\r\n  color: var(--text-muted);\r\n  cursor: pointer;\r\n  transition: all var(--transition-fast);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.modal-close:hover {\r\n  background: var(--hover-bg);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.modal-body {\r\n  padding: var(--spacing-6);\r\n  overflow-y: auto;\r\n  max-height: 70vh;\r\n}\r\n\r\n.modal-body.character-detail-body {\r\n  padding: 0;\r\n  max-height: none;\r\n  overflow: visible;\r\n}\r\n\r\n/* ===== 响应式设计 ===== */\r\n@media (max-width: 1024px) {\r\n  .character-grid {\r\n    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\r\n  }\r\n  \r\n  .filter-controls {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n  }\r\n  \r\n  .filter-group {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .character-manager {\r\n    padding: var(--spacing-3);\r\n  }\r\n  \r\n  .header-content {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .header-actions {\r\n    flex-direction: column;\r\n    gap: var(--spacing-3);\r\n  }\r\n  \r\n  .search-input {\r\n    width: 100%;\r\n  }\r\n  \r\n  .stats-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n  \r\n  .character-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .character-list {\r\n    overflow-x: auto;\r\n  }\r\n  \r\n  .list-header, .list-item {\r\n    min-width: 600px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .stats-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .modal {\r\n    width: 95vw;\r\n    margin: var(--spacing-2);\r\n  }\r\n  \r\n  .modal-header, .modal-body {\r\n    padding: var(--spacing-3) var(--spacing-4);\r\n  }\r\n}\r\n\r\n/* ===== 角色查看模态框专用样式 ===== */\r\n.character-view-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n}\r\n\r\n.character-view-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n  width: 95vw;\r\n  max-width: 1200px;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  animation: modal-appear 0.2s ease-out;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.character-view-modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.character-view-modal-body {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  background: white;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .character-view-modal {\r\n    width: 95vw;\r\n    margin: 8px;\r\n  }\r\n\r\n  .character-view-modal-header {\r\n    padding: 12px 16px;\r\n  }\r\n}\r\n\r\n/* ===== 角色编辑模态框专用样式 ===== */\r\n.character-edit-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 16px;\r\n}\r\n\r\n.character-edit-modal {\r\n  background: white;\r\n  border-radius: 12px;\r\n  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\r\n  width: 90vw;\r\n  max-width: 800px;\r\n  max-height: 90vh;\r\n  overflow: hidden;\r\n  animation: modal-appear 0.2s ease-out;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.character-edit-modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 24px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.character-edit-modal-body {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  background: white;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .character-edit-modal {\r\n    width: 95vw;\r\n    margin: 8px;\r\n  }\r\n\r\n  .character-edit-modal-header {\r\n    padding: 12px 16px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmYA,SAASA,UAAS,QAAS,MAAK;AAChC,OAAOC,aAAY,MAAO,gCAA+B;AACzD,OAAOC,mBAAkB,MAAO,sCAAqC;AACrE,SAASC,YAAW,QAAS,uBAAsB;AAEnD,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,MAAM,EAAE,CAACF,YAAY,CAAC;EACtBG,UAAU,EAAE;IACVL,aAAa,EAAbA,aAAa;IACbC,mBAAkB,EAAlBA;EACF,CAAC;EACDK,IAAI,WAAJA,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE,EAAE;MACdC,YAAY,EAAE,KAAK;MACnBC,iBAAiB,EAAE,KAAK;MACxBC,iBAAiB,EAAE,IAAI;MACvBC,QAAQ,EAAE,MAAM;MAAE;MAClBC,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE,EAAE;MACtBC,cAAc,EAAE,EAAE;MAClBC,MAAM,EAAE,MAAM;MACdC,aAAa,EAAE;IACjB;EACF,CAAC;EACDC,QAAQ,EAAAC,aAAA,CAAAA,aAAA,KACHpB,UAAU,CAAC,CAAC,gBAAgB,CAAC,CAAC;IAEjCqB,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACnB,IAAIC,QAAO,GAAAC,kBAAA,CAAQ,IAAI,CAACf,UAAU;;MAElC;MACA,IAAI,IAAI,CAACK,WAAW,EAAE;QACpB,IAAMW,KAAI,GAAI,IAAI,CAACX,WAAW,CAACY,WAAW,CAAC;QAC3CH,QAAO,GAAIA,QAAQ,CAACI,MAAM,CAAC,UAAAC,KAAG;UAAA,OAC5BA,KAAI,CAACxB,IAAI,CAACsB,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,KACrCG,KAAI,CAACE,UAAS,IAAKF,KAAI,CAACE,UAAU,CAACJ,WAAW,CAAC,CAAC,CAACG,QAAQ,CAACJ,KAAK,CAAC;QAAA,CACnE;MACF;;MAEA;MACA,IAAI,IAAI,CAACV,kBAAkB,EAAE;QAC3BQ,QAAO,GAAIA,QAAQ,CAACI,MAAM,CAAC,UAAAC,MAAG;UAAA,OAAKA,MAAI,CAACE,UAAS,KAAMR,KAAI,CAACP,kBAAkB;QAAA;MAChF;;MAEA;MACA,IAAI,IAAI,CAACC,cAAc,EAAE;QACvBO,QAAO,GAAIA,QAAQ,CAACI,MAAM,CAAC,UAAAC,MAAG;UAAA,OAAKA,MAAI,CAACG,MAAK,KAAMT,KAAI,CAACN,cAAc;QAAA;MACxE;;MAEA;MACAO,QAAQ,CAACS,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;QACtB,QAAQZ,KAAI,CAACL,MAAM;UACjB,KAAK,MAAM;YACT,OAAOgB,CAAC,CAAC7B,IAAI,CAAC+B,aAAa,CAACD,CAAC,CAAC9B,IAAI;UACpC,KAAK,SAAS;YACZ,OAAO,IAAIgC,IAAI,CAACF,CAAC,CAACG,UAAU,IAAI,IAAID,IAAI,CAACH,CAAC,CAACI,UAAU;UACvD,KAAK,SAAS;YACZ,OAAO,IAAID,IAAI,CAACF,CAAC,CAACI,UAAU,IAAI,IAAIF,IAAI,CAACH,CAAC,CAACK,UAAU;UACvD,KAAK,OAAO;YACV,OAAO,CAACJ,CAAC,CAACK,KAAI,IAAK,CAAC,KAAKN,CAAC,CAACM,KAAI,IAAK,CAAC;UACvC;YACE,OAAO;QACX;MACF,CAAC;MAED,OAAOhB,QAAO;IAChB,CAAC;IAEDiB,WAAW,WAAXA,WAAWA,CAAA,EAAG;MACZ,IAAMA,WAAU,GAAAhB,kBAAA,CAAQ,IAAIiB,GAAG,CAAC,IAAI,CAAChC,UAAU,CAACiC,GAAG,CAAC,UAAAd,MAAG;QAAA,OAAKA,MAAI,CAACE,UAAU;MAAA,EAAC,CAACH,MAAM,CAACgB,OAAO,CAAC,CAAC;MAC7F,OAAOH,WAAW,CAACR,IAAI,CAAC;IAC1B,CAAC;IAEDY,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,OAAO,IAAI,CAACnC,UAAU,CAACkB,MAAM,CAAC,UAAAC,MAAG;QAAA,OAAKA,MAAI,CAACG,MAAK,KAAM,OAAM,IAAK,CAACH,MAAI,CAACG,MAAM;MAAA,EAAC,CAACc,MAAK;IACtF,CAAC;IAEDC,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,OAAO,IAAI,CAACrC,UAAU,CAACkB,MAAM,CAAC,UAAAC,MAAG;QAAA,OAAKA,MAAI,CAACmB,WAAW;MAAA,EAAC,CAACF,MAAK;IAC/D,CAAC;IAEDG,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAMC,UAAS,GAAI,IAAIb,IAAI,CAAC;MAC5Ba,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,IAAI,CAAC;MAC3C,OAAO,IAAI,CAAC1C,UAAU,CAACkB,MAAM,CAAC,UAAAC,MAAG;QAAA,OAC/B,IAAIQ,IAAI,CAACR,MAAI,CAACU,UAAS,IAAKV,MAAI,CAACS,UAAU,IAAIY,UAAS;MAAA,CAC1D,CAAC,CAACJ,MAAK;IACT;EAAA,EACD;EACKO,OAAO,WAAPA,OAAOA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAC,QAAA;MAAA,OAAAF,YAAA,GAAAG,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OACRP,MAAI,CAACQ,eAAe,CAAC;UAAA;YAC3BR,MAAI,CAACS,mBAAmB,CAAC;UAAA;YAAA,OAAAH,QAAA,CAAA1B,CAAA;QAAA;MAAA,GAAAwB,OAAA;IAAA;EAC3B,CAAC;EACDM,aAAa,WAAbA,aAAaA,CAAA,EAAG;IACd,IAAI,CAACC,gBAAgB,CAAC;EACxB,CAAC;EACDC,OAAO,EAAE;IACDJ,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAAA,IAAAK,MAAA;MAAA,OAAAZ,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAW,SAAA;QAAA,IAAAC,EAAA;QAAA,OAAAb,YAAA,GAAAG,CAAA,WAAAW,SAAA;UAAA,kBAAAA,SAAA,CAAAT,CAAA;YAAA;cAAAS,SAAA,CAAAC,CAAA;cAEpBJ,MAAI,CAAC1D,OAAM,GAAI,IAAG;cAAA6D,SAAA,CAAAT,CAAA;cAAA,OACZM,MAAI,CAACK,MAAM,CAACC,QAAQ,CAAC,iBAAiB;YAAA;cAC5CN,MAAI,CAACzD,UAAS,GAAIyD,MAAI,CAACO,cAAa,IAAK,EAAC;cAAAJ,SAAA,CAAAT,CAAA;cAAA;YAAA;cAAAS,SAAA,CAAAC,CAAA;cAAAF,EAAA,GAAAC,SAAA,CAAAK,CAAA;cAE1CC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAAR,EAAO;cAC9BF,MAAI,CAACW,QAAQ,CAACD,KAAK,CAAC,QAAQ;YAAA;cAAAP,SAAA,CAAAC,CAAA;cAE5BJ,MAAI,CAAC1D,OAAM,GAAI,KAAI;cAAA,OAAA6D,SAAA,CAAAS,CAAA;YAAA;cAAA,OAAAT,SAAA,CAAApC,CAAA;UAAA;QAAA,GAAAkC,QAAA;MAAA;IAEvB,CAAC;IAEDY,eAAe,WAAfA,eAAeA,CAAA,EAAG;MAChB,IAAI,CAACC,OAAO,CAACC,IAAI,CAAC;QAAE7E,IAAI,EAAE;MAAmB,CAAC;IAChD,CAAC;IAED8E,eAAe,WAAfA,eAAeA,CAACC,SAAS,EAAE;MACzB;MACAR,OAAO,CAACS,GAAG,CAAC,OAAO,EAAED,SAAS;IAChC,CAAC;IAEDE,aAAa,WAAbA,aAAaA,CAACF,SAAS,EAAE;MACvBR,OAAO,CAACS,GAAG,CAAC,UAAU,EAAED,SAAS;MACjC,IAAI,CAACvE,iBAAgB,GAAIuE,SAAQ;MACjC,IAAI,CAACxE,iBAAgB,GAAI,IAAG;MAC5BgE,OAAO,CAACS,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACzE,iBAAiB;MAC3DgE,OAAO,CAACS,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxE,iBAAiB;IAC7D,CAAC;IAED0E,aAAa,WAAbA,aAAaA,CAACH,SAAS,EAAE;MACvB,IAAI,CAACvE,iBAAgB,GAAAQ,aAAA,KAAS+D,SAAQ,CAAE;MACxC,IAAI,CAACzE,YAAW,GAAI,IAAG;IACzB,CAAC;IAEK6E,eAAe,WAAfA,eAAeA,CAACC,aAAa,EAAE;MAAA,IAAAC,MAAA;MAAA,OAAAnC,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAkC,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAApC,YAAA,GAAAG,CAAA,WAAAkC,SAAA;UAAA,kBAAAA,SAAA,CAAAhC,CAAA;YAAA;cAAAgC,SAAA,CAAAtB,CAAA;cAAAsB,SAAA,CAAAhC,CAAA;cAAA,OAG3B6B,MAAI,CAAClB,MAAM,CAACC,QAAQ,CAAC,iBAAiB,EAAApD,aAAA;gBAC1CyE,EAAE,EAAEJ,MAAI,CAAC7E,iBAAiB,CAACiF;cAAE,GAC1BL,aAAY,CAChB;YAAA;cAAA,KAGGA,aAAa,CAACM,UAAU;gBAAAF,SAAA,CAAAhC,CAAA;gBAAA;cAAA;cAAAgC,SAAA,CAAAhC,CAAA;cAAA,OACpB6B,MAAI,CAACM,qBAAqB,CAACN,MAAI,CAAC7E,iBAAiB,CAACiF,EAAE,EAAEL,aAAa,CAACM,UAAU;YAAA;cAGtFL,MAAI,CAAC/E,YAAW,GAAI,KAAI;cAAAkF,SAAA,CAAAhC,CAAA;cAAA,OAClB6B,MAAI,CAAC5B,eAAe,CAAC;YAAA;cAC3B4B,MAAI,CAACZ,QAAQ,CAACmB,OAAO,CAAC,QAAQ;cAAAJ,SAAA,CAAAhC,CAAA;cAAA;YAAA;cAAAgC,SAAA,CAAAtB,CAAA;cAAAqB,GAAA,GAAAC,SAAA,CAAAlB,CAAA;cAE9BC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAAe,GAAO;cAC9BF,MAAI,CAACZ,QAAQ,CAACD,KAAK,CAAC,QAAQ;YAAA;cAAA,OAAAgB,SAAA,CAAA3D,CAAA;UAAA;QAAA,GAAAyD,QAAA;MAAA;IAEhC,CAAC;IAEKO,kBAAkB,WAAlBA,kBAAkBA,CAACd,SAAS,EAAE;MAAA,IAAAe,MAAA;MAAA,OAAA5C,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA2C,SAAA;QAAA,IAAAC,OAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,GAAA;QAAA,OAAAhD,YAAA,GAAAG,CAAA,WAAA8C,SAAA;UAAA,kBAAAA,SAAA,CAAA5C,CAAA;YAAA;cAAA4C,SAAA,CAAAlC,CAAA;cAEhC;cACM8B,OAAM,GAAIF,MAAI,CAACO,qBAAqB,CAACtB,SAAS,CAAC/E,IAAI;cAEnDiG,cAAa,GAAAjF,aAAA,CAAAA,aAAA,KACd+D,SAAS;gBACZ/E,IAAI,EAAEgG,OAAO;gBACbP,EAAE,EAAEa,SAAS;gBACbrE,UAAU,EAAEqE,SAAS;gBACrBpE,UAAU,EAAEoE,SAAS;gBACrB;gBACAC,WAAW,EAAE,IAAI;gBACjBC,eAAe,EAAE,IAAI;gBACrBC,mBAAmB,EAAE;cAAG;cAAAL,SAAA,CAAA5C,CAAA;cAAA,OAGpBsC,MAAI,CAAC3B,MAAM,CAACC,QAAQ,CAAC,iBAAiB,EAAE6B,cAAc;YAAA;cAAAG,SAAA,CAAA5C,CAAA;cAAA,OACtDsC,MAAI,CAACrC,eAAe,CAAC;YAAA;cAC3BqC,MAAI,CAACrB,QAAQ,CAACmB,OAAO,kFAAAc,MAAA,CAAiBV,OAAO,CAAE;cAAAI,SAAA,CAAA5C,CAAA;cAAA;YAAA;cAAA4C,SAAA,CAAAlC,CAAA;cAAAiC,GAAA,GAAAC,SAAA,CAAA9B,CAAA;cAE/CC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAA2B,GAAO;cAC9BL,MAAI,CAACrB,QAAQ,CAACD,KAAK,CAAC,UAAS,IAAK,EAAA0B,eAAA,GAAAC,GAAA,CAAMQ,QAAQ,cAAAT,eAAA,gBAAAA,eAAA,GAAdA,eAAA,CAAgB/F,IAAI,cAAA+F,eAAA,uBAApBA,eAAA,CAAsBU,MAAK,KAAKT,GAAA,CAAMU,OAAO,CAAC;YAAA;cAAA,OAAAT,SAAA,CAAAvE,CAAA;UAAA;QAAA,GAAAkE,QAAA;MAAA;IAEpF,CAAC;IAEDM,qBAAqB,WAArBA,qBAAqBA,CAACS,YAAY,EAAE;MAClC,IAAMC,aAAY,GAAI,IAAI,CAAC1G,UAAU,CAACiC,GAAG,CAAC,UAAA0E,CAAA;QAAA,OAAKA,CAAC,CAAChH,IAAI;MAAA;MACrD,IAAIgG,OAAM,MAAAU,MAAA,CAAOI,YAAY,oBAAM;MACnC,IAAIG,OAAM,GAAI;;MAEd;MACA,OAAOF,aAAa,CAACtF,QAAQ,CAACuE,OAAO,CAAC,EAAE;QACtCiB,OAAO,EAAC;QACRjB,OAAM,MAAAU,MAAA,CAAOI,YAAY,oBAAAJ,MAAA,CAAOO,OAAO,MAAE;MAC3C;MAEA,OAAOjB,OAAM;IACf,CAAC;IAEKkB,eAAe,WAAfA,eAAeA,CAACnC,SAAS,EAAE;MAAA,IAAAoC,MAAA;MAAA,OAAAjE,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAgE,SAAA;QAAA,IAAAC,SAAA,EAAAC,gBAAA,EAAAC,GAAA;QAAA,OAAApE,YAAA,GAAAG,CAAA,WAAAkE,SAAA;UAAA,kBAAAA,SAAA,CAAAhE,CAAA;YAAA;cAAAgE,SAAA,CAAAhE,CAAA;cAAA,OAEP2D,MAAI,CAACM,uBAAuB,CAAC1C,SAAS;YAAA;cAAxDsC,SAAQ,GAAAG,SAAA,CAAAlD,CAAA;cAAA,IACT+C,SAAS;gBAAAG,SAAA,CAAAhE,CAAA;gBAAA;cAAA;cAAA,OAAAgE,SAAA,CAAA3F,CAAA;YAAA;cAAA2F,SAAA,CAAAtD,CAAA;cAAAsD,SAAA,CAAAhE,CAAA;cAAA,OAKN2D,MAAI,CAAChD,MAAM,CAACC,QAAQ,CAAC,iBAAiB,EAAEW,SAAS,CAACU,EAAE;YAAA;cAAA+B,SAAA,CAAAhE,CAAA;cAAA,OACpD2D,MAAI,CAAC1D,eAAe,CAAC;YAAA;cAC3B0D,MAAI,CAAC1C,QAAQ,CAACmB,OAAO,mBAAAc,MAAA,CAAQ3B,SAAS,CAAC/E,IAAI,gCAAQ;cAAAwH,SAAA,CAAAhE,CAAA;cAAA;YAAA;cAAAgE,SAAA,CAAAtD,CAAA;cAAAqD,GAAA,GAAAC,SAAA,CAAAlD,CAAA;cAEnDC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAA+C,GAAO;cAC9BJ,MAAI,CAAC1C,QAAQ,CAACD,KAAK,CAAC,UAAS,IAAK,EAAA8C,gBAAA,GAAAC,GAAA,CAAMZ,QAAQ,cAAAW,gBAAA,gBAAAA,gBAAA,GAAdA,gBAAA,CAAgBnH,IAAI,cAAAmH,gBAAA,uBAApBA,gBAAA,CAAsBV,MAAK,KAAKW,GAAA,CAAMV,OAAO,CAAC;YAAA;cAAA,OAAAW,SAAA,CAAA3F,CAAA;UAAA;QAAA,GAAAuF,QAAA;MAAA;IAEpF,CAAC;IAEDK,uBAAuB,WAAvBA,uBAAuBA,CAAC1C,SAAS,EAAE;MACjC,OAAO,IAAI2C,OAAO,CAAC,UAACC,OAAO,EAAK;QAC9B,IAAMN,SAAQ,GAAIO,OAAO,CACvB,6DAAAlB,MAAA,CAAe3B,SAAS,CAAC/E,IAAI,6DACrB,+BAAA0G,MAAA,CACA3B,SAAS,CAACrD,UAAS,IAAK,IAAI,OAAG,+BAAAgF,MAAA,CAC/B3B,SAAS,CAAC5C,KAAI,IAAK,IAAI,OAAG,2CAAAuE,MAAA,CACxB3B,SAAS,CAAC9C,UAAS,GAAI,IAAID,IAAI,CAAC+C,SAAS,CAAC9C,UAAU,CAAC,CAAC4F,kBAAkB,CAAC,IAAI,IAAI,SAAK,sJAElG;QACAF,OAAO,CAACN,SAAS;MACnB,CAAC;IACH,CAAC;IAEKS,cAAc,WAAdA,cAAcA,CAAC/C,SAAS,EAAE;MAAA,IAAAgD,MAAA;MAAA,OAAA7E,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA4E,SAAA;QAAA,IAAAC,GAAA;QAAA,OAAA9E,YAAA,GAAAG,CAAA,WAAA4E,SAAA;UAAA,kBAAAA,SAAA,CAAA1E,CAAA;YAAA;cAAA0E,SAAA,CAAAhE,CAAA;cAAAgE,SAAA,CAAA1E,CAAA;cAAA,OAEtBuE,MAAI,CAAC5D,MAAM,CAACC,QAAQ,CAAC,iBAAiB,EAAE;gBAC5CqB,EAAE,EAAEV,SAAS,CAACU,EAAE;gBAChB9C,WAAW,EAAE,CAACoC,SAAS,CAACpC;cAC1B,CAAC;YAAA;cAAAuF,SAAA,CAAA1E,CAAA;cAAA,OACKuE,MAAI,CAACtE,eAAe,CAAC;YAAA;cAAAyE,SAAA,CAAA1E,CAAA;cAAA;YAAA;cAAA0E,SAAA,CAAAhE,CAAA;cAAA+D,GAAA,GAAAC,SAAA,CAAA5D,CAAA;cAE3BC,OAAO,CAACC,KAAK,CAAC,WAAW,EAAAyD,GAAO;YAAA;cAAA,OAAAC,SAAA,CAAArG,CAAA;UAAA;QAAA,GAAAmG,QAAA;MAAA;IAEpC,CAAC;IAEDG,aAAa,WAAbA,aAAaA,CAAA,EAAG;MACd,IAAI,CAAC7H,YAAW,GAAI,KAAI;MACxB,IAAI,CAACE,iBAAgB,GAAI,IAAG;IAC9B,CAAC;IAED4H,kBAAkB,WAAlBA,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAC7H,iBAAgB,GAAI,KAAI;MAC7B,IAAI,CAACC,iBAAgB,GAAI,IAAG;IAC9B,CAAC;IAED;IACA6H,qBAAqB,WAArBA,qBAAqBA,CAAA,EAAG;MACtB,IAAI,CAAC9H,iBAAgB,GAAI,KAAI;MAC7B,IAAI,CAAC2E,aAAa,CAAC,IAAI,CAAC1E,iBAAiB;IAC3C,CAAC;IAEK8H,0BAA0B,WAA1BA,0BAA0BA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAArF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAoF,SAAA;QAAA,OAAArF,YAAA,GAAAG,CAAA,WAAAmF,SAAA;UAAA,kBAAAA,SAAA,CAAAjF,CAAA;YAAA;cACjC+E,MAAI,CAAChI,iBAAgB,GAAI,KAAI;cAAAkI,SAAA,CAAAjF,CAAA;cAAA,OACvB+E,MAAI,CAAC1C,kBAAkB,CAAC0C,MAAI,CAAC/H,iBAAiB;YAAA;cAAA,OAAAiI,SAAA,CAAA5G,CAAA;UAAA;QAAA,GAAA2G,QAAA;MAAA;IACtD,CAAC;IAEKE,uBAAuB,WAAvBA,uBAAuBA,CAAA,EAAG;MAAA,IAAAC,MAAA;MAAA,OAAAzF,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAAwF,SAAA;QAAA,IAAA7D,SAAA;QAAA,OAAA5B,YAAA,GAAAG,CAAA,WAAAuF,SAAA;UAAA,kBAAAA,SAAA,CAAArF,CAAA;YAAA;cACxBuB,SAAQ,GAAI4D,MAAI,CAACnI,iBAAgB;cACvCmI,MAAI,CAACpI,iBAAgB,GAAI,KAAI;cAAAsI,SAAA,CAAArF,CAAA;cAAA,OACvBmF,MAAI,CAACzB,eAAe,CAACnC,SAAS;YAAA;cAAA,OAAA8D,SAAA,CAAAhH,CAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA;IACtC,CAAC;IAED;IACMjD,qBAAqB,WAArBA,qBAAqBA,CAACmD,WAAW,EAAEpD,UAAU,EAAE;MAAA,IAAAqD,MAAA;MAAA,OAAA7F,iBAAA,cAAAC,YAAA,GAAAC,CAAA,UAAA4F,SAAA;QAAA,IAAAC,QAAA,EAAAtC,QAAA,EAAAuC,GAAA;QAAA,OAAA/F,YAAA,GAAAG,CAAA,WAAA6F,SAAA;UAAA,kBAAAA,SAAA,CAAA3F,CAAA;YAAA;cAAA2F,SAAA,CAAAjF,CAAA;cAE3C+E,QAAO,GAAI,IAAIG,QAAQ,CAAC;cAC9BH,QAAQ,CAACI,MAAM,CAAC,MAAM,EAAE3D,UAAU;cAAAyD,SAAA,CAAA3F,CAAA;cAAA,OAEXuF,MAAI,CAACO,KAAK,CAACC,IAAI,oBAAA7C,MAAA,CAAoBoC,WAAW,cAAWG,QAAQ,EAAE;gBACxFO,OAAO,EAAE;kBACP,cAAc,EAAE;gBAClB;cACF,CAAC;YAAA;cAJK7C,QAAO,GAAAwC,SAAA,CAAA7E,CAAA;cAAA,KAMTqC,QAAQ,CAACxG,IAAI,CAACyF,OAAO;gBAAAuD,SAAA,CAAA3F,CAAA;gBAAA;cAAA;cACvBe,OAAO,CAACS,GAAG,CAAC,UAAU;cAAA,OAAAmE,SAAA,CAAAtH,CAAA,IACf,IAAG;YAAA;cAEV0C,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEmC,QAAQ,CAACxG,IAAI,CAAC0G,OAAO;cAAA,MAC1C,IAAI4C,KAAK,CAAC9C,QAAQ,CAACxG,IAAI,CAAC0G,OAAM,IAAK,QAAQ;YAAA;cAAAsC,SAAA,CAAA3F,CAAA;cAAA;YAAA;cAAA2F,SAAA,CAAAjF,CAAA;cAAAgF,GAAA,GAAAC,SAAA,CAAA7E,CAAA;cAGnDC,OAAO,CAACC,KAAK,CAAC,WAAW,EAAA0E,GAAO;cAAA,MAAAA,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAtH,CAAA;UAAA;QAAA,GAAAmH,QAAA;MAAA;IAGpC,CAAC;IAED;IACAU,kBAAkB,WAAlBA,kBAAkBA,CAAC3E,SAAS,EAAE;MAC5B,IAAIA,SAAQ,IAAKA,SAAS,CAACU,EAAE,EAAE;QAC7B;QACA,+CAAAiB,MAAA,CAA+C3B,SAAS,CAACU,EAAE;MAC7D;MACA,OAAO,IAAI,CAAC3E,aAAY;IAC1B,CAAC;IAED;IACA6I,gBAAgB,WAAhBA,gBAAgBA,CAACC,GAAG,EAAE;MACpB,IAAMC,cAAa,GAAI;QACrB,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,KAAK,EAAE;MACT;MACA,OAAOA,cAAc,CAACD,GAAG,KAAKA,GAAE;IAClC,CAAC;IAEDE,eAAe,WAAfA,eAAeA,CAAC/E,SAAS,EAAE;MACzB,IAAI,CAACA,SAAS,CAACgF,MAAK,IAAKhF,SAAS,CAACgF,MAAK,KAAM,CAAC,EAAE,OAAO;MACxD,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAE,CAACnF,SAAS,CAACoF,UAAS,IAAK,CAAC,IAAIpF,SAAS,CAACgF,MAAK,GAAI,GAAG,CAAC;IACxF,CAAC;IAEDK,gBAAgB,WAAhBA,gBAAgBA,CAACrF,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,CAACsF,OAAM,IAAKtF,SAAS,CAACsF,OAAM,KAAM,CAAC,EAAE,OAAO;MAC1D,OAAOL,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAE,CAACnF,SAAS,CAACuF,WAAU,IAAK,CAAC,IAAIvF,SAAS,CAACsF,OAAM,GAAI,GAAG,CAAC;IAC1F,CAAC;IAEDE,aAAa,WAAbA,aAAaA,CAAC5I,MAAM,EAAE;MACpB,IAAM6I,SAAQ,GAAI;QAChB,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;QACZ,QAAQ,EAAE,IAAI;QACd,aAAa,EAAE;MACjB;MACA,OAAOA,SAAS,CAAC7I,MAAM,KAAK,IAAG;IACjC,CAAC;IAEDiC,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;MACjB,IAAM6G,QAAO,GAAI;QACfhK,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBI,MAAM,EAAE,IAAI,CAACA;MACf;MACA,IAAI,CAAC6J,WAAW,CAAC,4BAA4B,EAAED,QAAQ;IACzD,CAAC;IAED/G,mBAAmB,WAAnBA,mBAAmBA,CAAA,EAAG;MACpB,IAAM+G,QAAO,GAAI,IAAI,CAACE,WAAW,CAAC,4BAA4B;MAC9D,IAAIF,QAAQ,EAAE;QACZ,IAAI;UACF,IAAI,CAAChK,QAAO,GAAIgK,QAAQ,CAAChK,QAAO,IAAK,MAAK;UAC1C,IAAI,CAACI,MAAK,GAAI4J,QAAQ,CAAC5J,MAAK,IAAK,MAAK;QACxC,EAAE,OAAO2D,KAAK,EAAE;UACdD,OAAO,CAACqG,IAAI,CAAC,WAAW,EAAEpG,KAAK;QACjC;MACF;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}