<template>
  <!-- 2D战场上的怪物令牌 -->
  <g 
    class="monster-token"
    :class="{ 
      selected, 
      'can-control': canControl,
      'boss-monster': monster.isBoss,
      'minion-monster': monster.isMinion
    }"
    :transform="`translate(${x}, ${y})`"
    @click="$emit('select', monster)"
    @contextmenu.prevent="$emit('context-menu', monster, $event)"
  >
    <!-- 选中光环 -->
    <circle 
      v-if="selected"
      :r="tokenRadius + 8"
      fill="none"
      stroke="#ff6b6b"
      stroke-width="3"
      opacity="0.8"
    >
      <animate 
        attributeName="stroke-opacity" 
        values="0.8;0.3;0.8" 
        dur="2s" 
        repeatCount="indefinite"
      />
    </circle>
    
    <!-- Boss怪物特殊光环 -->
    <circle 
      v-if="monster.isBoss"
      :r="tokenRadius + 15"
      fill="none"
      stroke="#9c27b0"
      stroke-width="2"
      stroke-dasharray="8,4"
      opacity="0.7"
    >
      <animateTransform
        attributeName="transform"
        type="rotate"
        values="0;360"
        dur="4s"
        repeatCount="indefinite"
      />
    </circle>
    
    <!-- 威胁范围指示 -->
    <circle 
      v-if="showThreatRange"
      :r="threatRange * gridSize"
      fill="rgba(244, 67, 54, 0.1)"
      stroke="#f44336"
      stroke-width="2"
      stroke-dasharray="3,3"
      opacity="0.4"
    />
    
    <!-- 怪物主体 -->
    <g class="monster-body">
      <!-- 背景形状 (根据怪物类型变化) -->
      <g v-if="monster.size === 'large'">
        <!-- 大型怪物 - 2x2格子 -->
        <rect 
          :x="-tokenRadius * 1.5"
          :y="-tokenRadius * 1.5"
          :width="tokenRadius * 3"
          :height="tokenRadius * 3"
          :fill="getMonsterColor()"
          stroke="#fff"
          stroke-width="2"
          rx="4"
          opacity="0.9"
        />
      </g>
      <g v-else-if="monster.size === 'huge'">
        <!-- 巨型怪物 - 3x3格子 -->
        <rect 
          :x="-tokenRadius * 2"
          :y="-tokenRadius * 2"
          :width="tokenRadius * 4"
          :height="tokenRadius * 4"
          :fill="getMonsterColor()"
          stroke="#fff"
          stroke-width="3"
          rx="6"
          opacity="0.9"
        />
      </g>
      <g v-else>
        <!-- 普通怪物 - 圆形 -->
        <circle 
          :r="tokenRadius"
          :fill="getMonsterColor()"
          stroke="#fff"
          stroke-width="2"
          opacity="0.9"
        />
      </g>
      
      <!-- 怪物图标 -->
      <text 
        text-anchor="middle"
        dy="6"
        :font-size="getIconSize()"
        fill="white"
        class="monster-icon"
      >
        {{ getMonsterIcon() }}
      </text>
      
      <!-- 生命值条 -->
      <rect 
        :x="-tokenRadius + 4"
        :y="tokenRadius - 8"
        :width="(tokenRadius - 4) * 2"
        height="4"
        fill="#333"
        opacity="0.7"
        rx="2"
      />
      <rect 
        :x="-tokenRadius + 4"
        :y="tokenRadius - 8"
        :width="getHealthBarWidth()"
        height="4"
        :fill="getHealthColor()"
        opacity="0.9"
        rx="2"
      />
    </g>
    
    <!-- 怪物名称和等级 -->
    <text 
      :y="getNameYPosition()"
      text-anchor="middle"
      font-size="10"
      font-weight="bold"
      fill="#f44336"
      class="monster-name-text"
    >
      {{ monster.name }}
    </text>
    
    <!-- 挑战等级显示 -->
    <g class="challenge-rating" :transform="`translate(${tokenRadius - 12}, ${-tokenRadius + 12})`">
      <circle r="10" fill="#ff5722" stroke="#fff" stroke-width="1"/>
      <text 
        text-anchor="middle"
        dy="3"
        font-size="8"
        fill="white"
        font-weight="bold"
      >
        {{ monster.challengeRating || '?' }}
      </text>
    </g>
    
    <!-- 状态效果图标 -->
    <g class="status-effects" :transform="`translate(${-tokenRadius}, ${-getNameYPosition() - 10})`">
      <g 
        v-for="(condition, index) in monster.conditions" 
        :key="condition"
        :transform="`translate(${index * 14}, 0)`"
      >
        <circle r="7" fill="#333" opacity="0.8"/>
        <text 
          text-anchor="middle" 
          dy="3" 
          font-size="8" 
          fill="white"
        >
          {{ getConditionIcon(condition) }}
        </text>
      </g>
    </g>
    
    <!-- 特殊能力指示器 -->
    <g class="special-abilities" :transform="`translate(${-tokenRadius + 8}, ${tokenRadius - 8})`">
      <!-- 法术能力 -->
      <circle 
        v-if="monster.hasSpells"
        r="4"
        fill="#9c27b0"
        stroke="#fff"
        stroke-width="1"
      />
      <text 
        v-if="monster.hasSpells"
        text-anchor="middle"
        dy="2"
        font-size="6"
        fill="white"
      >
        ✨
      </text>
      
      <!-- 传奇行动 -->
      <circle 
        v-if="monster.hasLegendaryActions"
        :transform="`translate(12, 0)`"
        r="4"
        fill="#ff9800"
        stroke="#fff"
        stroke-width="1"
      />
      <text 
        v-if="monster.hasLegendaryActions"
        :transform="`translate(12, 0)`"
        text-anchor="middle"
        dy="2"
        font-size="6"
        fill="white"
      >
        ⚡
      </text>
    </g>
    
    <!-- 伤害数字动画 -->
    <g v-if="damageAnimation" class="damage-animation">
      <text 
        :y="-getNameYPosition() - 20"
        text-anchor="middle"
        font-size="14"
        font-weight="bold"
        :fill="damageAnimation.color"
        opacity="0"
      >
        {{ damageAnimation.text }}
        <animate 
          attributeName="y" 
          :values="`${-getNameYPosition() - 20};${-getNameYPosition() - 50}`"
          dur="1.5s"
        />
        <animate 
          attributeName="opacity" 
          values="0;1;1;0"
          dur="1.5s"
        />
      </text>
    </g>
    
    <!-- 攻击动画效果 -->
    <g v-if="attackAnimation" class="attack-animation">
      <circle 
        :r="tokenRadius"
        fill="none"
        stroke="#ff4444"
        stroke-width="4"
        opacity="0"
      >
        <animate 
          attributeName="r" 
          :values="`${tokenRadius};${tokenRadius + 20}`"
          dur="0.5s"
        />
        <animate 
          attributeName="opacity" 
          values="0.8;0"
          dur="0.5s"
        />
      </circle>
    </g>
    
    <!-- KP控制手柄 -->
    <circle 
      v-if="canControl && selected"
      :r="tokenRadius + 5"
      fill="transparent"
      stroke="none"
      style="cursor: move"
      @mousedown="startDrag"
    />
  </g>
</template>

<script>
export default {
  name: 'MonsterToken',
  props: {
    monster: {
      type: Object,
      required: true
    },
    gridSize: {
      type: Number,
      default: 40
    },
    zoom: {
      type: Number,
      default: 1
    },
    selected: {
      type: Boolean,
      default: false
    },
    canControl: {
      type: Boolean,
      default: false
    },
    showThreatRange: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      tokenRadius: 20,
      isDragging: false,
      dragStart: null,
      damageAnimation: null,
      attackAnimation: false
    }
  },
  
  computed: {
    x() {
      return (this.monster.position?.x || 0) * this.gridSize * this.zoom
    },
    
    y() {
      return (this.monster.position?.y || 0) * this.gridSize * this.zoom
    },
    
    threatRange() {
      // 根据怪物武器和能力计算威胁范围
      return this.monster.reach || 1.5
    }
  },
  
  methods: {
    /**
     * 获取怪物颜色
     */
    getMonsterColor() {
      const colorMap = {
        'beast': '#8bc34a',      // 野兽 - 绿色
        'humanoid': '#ff9800',   // 人形 - 橙色
        'undead': '#9c27b0',     // 不死 - 紫色
        'fiend': '#f44336',      // 恶魔 - 红色
        'celestial': '#ffeb3b',  // 天界 - 黄色
        'dragon': '#e91e63',     // 龙类 - 粉红
        'aberration': '#3f51b5', // 异怪 - 蓝色
        'construct': '#607d8b',  // 构装 - 灰色
        'elemental': '#00bcd4',  // 元素 - 青色
        'fey': '#4caf50',        // 精类 - 浅绿
        'giant': '#795548',      // 巨人 - 棕色
        'monstrosity': '#ff5722', // 怪物 - 深橙
        'ooze': '#9e9e9e',       // 软泥 - 灰色
        'plant': '#689f38'       // 植物 - 深绿
      }
      
      return colorMap[this.monster.type] || '#666666'
    },
    
    /**
     * 获取怪物图标
     */
    getMonsterIcon() {
      const iconMap = {
        'beast': '🐺',
        'humanoid': '👤',
        'undead': '💀',
        'fiend': '👹',
        'celestial': '👼',
        'dragon': '🐉',
        'aberration': '👁️',
        'construct': '🤖',
        'elemental': '🌪️',
        'fey': '🧚',
        'giant': '🗿',
        'monstrosity': '👾',
        'ooze': '🟢',
        'plant': '🌿'
      }
      
      return iconMap[this.monster.type] || '❓'
    },
    
    /**
     * 获取图标大小
     */
    getIconSize() {
      if (this.monster.size === 'huge') return 32
      if (this.monster.size === 'large') return 24
      return 16
    },
    
    /**
     * 获取名称Y位置
     */
    getNameYPosition() {
      if (this.monster.size === 'huge') return this.tokenRadius * 2 + 15
      if (this.monster.size === 'large') return this.tokenRadius * 1.5 + 12
      return this.tokenRadius + 15
    },
    
    /**
     * 获取生命值颜色
     */
    getHealthColor() {
      const healthPercent = this.getHealthPercentage()
      
      if (healthPercent > 75) return '#4caf50'
      if (healthPercent > 50) return '#ff9800'
      if (healthPercent > 25) return '#f44336'
      return '#9c27b0'
    },
    
    /**
     * 获取生命值百分比
     */
    getHealthPercentage() {
      const current = this.monster.currentHP || this.monster.hitPoints
      const max = this.monster.maxHP || this.monster.hitPoints
      return max > 0 ? (current / max) * 100 : 0
    },
    
    /**
     * 获取生命值条宽度
     */
    getHealthBarWidth() {
      const maxWidth = (this.tokenRadius - 4) * 2
      const healthPercent = this.getHealthPercentage() / 100
      return maxWidth * healthPercent
    },
    
    /**
     * 获取状态效果图标
     */
    getConditionIcon(condition) {
      const iconMap = {
        'unconscious': '💤',
        'dying': '💀',
        'prone': '⬇️',
        'stunned': '😵',
        'restrained': '🔒',
        'frightened': '😨',
        'poisoned': '☠️',
        'bleeding': '🩸',
        'burning': '🔥',
        'frozen': '❄️',
        'paralyzed': '⚡',
        'blinded': '👁️',
        'deafened': '👂',
        'charmed': '💖',
        'confused': '❓',
        'enraged': '😡',
        'invisible': '👻'
      }
      
      return iconMap[condition] || '?'
    },
    
    /**
     * 开始拖拽 (仅KP可用)
     */
    startDrag(event) {
      if (!this.canControl) return
      
      this.isDragging = true
      this.dragStart = {
        x: event.clientX,
        y: event.clientY,
        monsterX: this.monster.position.x,
        monsterY: this.monster.position.y
      }
      
      document.addEventListener('mousemove', this.handleDrag)
      document.addEventListener('mouseup', this.endDrag)
      
      event.stopPropagation()
    },
    
    /**
     * 处理拖拽
     */
    handleDrag(event) {
      if (!this.isDragging || !this.dragStart) return
      
      const deltaX = event.clientX - this.dragStart.x
      const deltaY = event.clientY - this.dragStart.y
      
      const gridDeltaX = Math.round(deltaX / (this.gridSize * this.zoom))
      const gridDeltaY = Math.round(deltaY / (this.gridSize * this.zoom))
      
      const newX = this.dragStart.monsterX + gridDeltaX
      const newY = this.dragStart.monsterY + gridDeltaY
      
      this.$emit('move', this.monster, { x: newX, y: newY })
    },
    
    /**
     * 结束拖拽
     */
    endDrag() {
      this.isDragging = false
      this.dragStart = null
      
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.endDrag)
    },
    
    /**
     * 播放伤害动画
     */
    playDamageAnimation(damage, type = 'normal') {
      const colors = {
        normal: '#f44336',
        critical: '#e91e63',
        healing: '#4caf50',
        miss: '#9e9e9e'
      }
      
      const texts = {
        normal: `-${damage}`,
        critical: `CRIT! -${damage}`,
        healing: `+${damage}`,
        miss: 'MISS'
      }
      
      this.damageAnimation = {
        text: texts[type] || `-${damage}`,
        color: colors[type] || '#f44336'
      }
      
      setTimeout(() => {
        this.damageAnimation = null
      }, 1500)
    },
    
    /**
     * 播放攻击动画
     */
    playAttackAnimation() {
      this.attackAnimation = true
      
      setTimeout(() => {
        this.attackAnimation = false
      }, 500)
    },
    
    /**
     * 播放死亡动画
     */
    playDeathAnimation() {
      // 添加死亡动画效果
      this.$el.style.transition = 'all 1s ease-out'
      this.$el.style.opacity = '0.3'
      this.$el.style.transform += ' scale(0.8)'
    }
  }
}
</script>

<style scoped>
.monster-token {
  cursor: pointer;
  transition: all 0.3s ease;
}

.monster-token:hover {
  filter: brightness(1.1);
}

.monster-token.selected {
  filter: drop-shadow(0 0 8px rgba(255, 107, 107, 0.8));
}

.monster-token.boss-monster {
  filter: drop-shadow(0 0 12px rgba(156, 39, 176, 0.9));
}

.monster-token.can-control {
  cursor: move;
}

.monster-name-text {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  font-family: 'Arial', sans-serif;
}

.monster-icon {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.challenge-rating circle {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.status-effects circle {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.special-abilities circle {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.damage-animation text {
  font-family: 'Arial Black', sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* 大型怪物样式 */
.monster-token.large-monster .monster-body rect {
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
}

/* 巨型怪物样式 */
.monster-token.huge-monster .monster-body rect {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.4));
}

/* Boss怪物特殊效果 */
.monster-token.boss-monster .monster-body {
  animation: bossGlow 3s ease-in-out infinite alternate;
}

@keyframes bossGlow {
  from { filter: brightness(1); }
  to { filter: brightness(1.2); }
}

/* 小怪特殊效果 */
.monster-token.minion-monster {
  opacity: 0.8;
  transform: scale(0.9);
}
</style>