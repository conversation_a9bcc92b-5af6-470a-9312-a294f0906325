{"ast": null, "code": "import \"core-js/modules/es.array.concat.js\";\nimport \"core-js/modules/es.function.name.js\";\nimport { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, normalizeStyle as _normalizeStyle } from \"vue\";\nvar _hoisted_1 = {\n  \"class\": \"battlefield-container\"\n};\nvar _hoisted_2 = {\n  \"class\": \"battlefield-toolbar\"\n};\nvar _hoisted_3 = {\n  \"class\": \"toolbar-left\"\n};\nvar _hoisted_4 = {\n  \"class\": \"zoom-controls\"\n};\nvar _hoisted_5 = [\"disabled\"];\nvar _hoisted_6 = {\n  \"class\": \"zoom-display\"\n};\nvar _hoisted_7 = [\"disabled\"];\nvar _hoisted_8 = {\n  \"class\": \"grid-controls\"\n};\nvar _hoisted_9 = {\n  \"class\": \"toolbar-center\"\n};\nvar _hoisted_10 = {\n  \"class\": \"battlefield-info\"\n};\nvar _hoisted_11 = {\n  \"class\": \"round-info\"\n};\nvar _hoisted_12 = {\n  \"class\": \"turn-info\"\n};\nvar _hoisted_13 = {\n  \"class\": \"toolbar-right\"\n};\nvar _hoisted_14 = {\n  \"class\": \"view-controls\"\n};\nvar _hoisted_15 = {\n  \"class\": \"battlefield-main\",\n  ref: \"battlefieldContainer\"\n};\nvar _hoisted_16 = [\"width\", \"height\", \"viewBox\"];\nvar _hoisted_17 = [\"width\", \"height\"];\nvar _hoisted_18 = [\"d\"];\nvar _hoisted_19 = [\"fill\"];\nvar _hoisted_20 = {\n  \"class\": \"terrain-layer\"\n};\nvar _hoisted_21 = [\"x\", \"y\", \"width\", \"height\", \"fill\", \"opacity\"];\nvar _hoisted_22 = {\n  key: 0,\n  \"class\": \"ruler-layer\"\n};\nvar _hoisted_23 = [\"x1\", \"y1\", \"x2\", \"y2\"];\nvar _hoisted_24 = [\"x\", \"y\"];\nvar _hoisted_25 = {\n  key: 1,\n  \"class\": \"movement-preview\"\n};\nvar _hoisted_26 = [\"d\"];\nvar _hoisted_27 = [\"cx\", \"cy\", \"r\"];\nvar _hoisted_28 = {\n  key: 2,\n  \"class\": \"attack-range\"\n};\nvar _hoisted_29 = [\"cx\", \"cy\", \"r\"];\nvar _hoisted_30 = {\n  \"class\": \"characters-layer\"\n};\nvar _hoisted_31 = {\n  \"class\": \"monsters-layer\"\n};\nvar _hoisted_32 = {\n  \"class\": \"effects-layer\"\n};\nvar _hoisted_33 = [\"cx\", \"cy\", \"r\", \"fill\", \"opacity\", \"stroke\"];\nvar _hoisted_34 = {\n  \"class\": \"animations-layer\"\n};\nvar _hoisted_35 = {\n  \"class\": \"menu-header\"\n};\nvar _hoisted_36 = [\"src\", \"alt\"];\nvar _hoisted_37 = {\n  \"class\": \"menu-items\"\n};\nvar _hoisted_38 = {\n  \"class\": \"menu-header\"\n};\nvar _hoisted_39 = {\n  \"class\": \"monster-icon\"\n};\nvar _hoisted_40 = {\n  \"class\": \"menu-items\"\n};\nvar _hoisted_41 = {\n  \"class\": \"battlefield-status\"\n};\nvar _hoisted_42 = {\n  \"class\": \"status-left\"\n};\nvar _hoisted_43 = {\n  key: 0,\n  \"class\": \"selected-info\"\n};\nvar _hoisted_44 = {\n  \"class\": \"selected-avatar\"\n};\nvar _hoisted_45 = [\"src\", \"alt\"];\nvar _hoisted_46 = {\n  key: 1,\n  \"class\": \"monster-avatar\"\n};\nvar _hoisted_47 = {\n  \"class\": \"selected-details\"\n};\nvar _hoisted_48 = {\n  \"class\": \"selected-name\"\n};\nvar _hoisted_49 = {\n  \"class\": \"selected-stats\"\n};\nvar _hoisted_50 = {\n  \"class\": \"hp\"\n};\nvar _hoisted_51 = {\n  \"class\": \"position\"\n};\nvar _hoisted_52 = {\n  \"class\": \"status-center\"\n};\nvar _hoisted_53 = {\n  \"class\": \"battlefield-stats\"\n};\nvar _hoisted_54 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_55 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_56 = {\n  \"class\": \"stat-item\"\n};\nvar _hoisted_57 = {\n  \"class\": \"status-right\"\n};\nvar _hoisted_58 = {\n  \"class\": \"action-buttons\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$props$currentTurnCh, _ref, _ref2, _ref3, _ref4, _ref5;\n  var _component_CharacterToken = _resolveComponent(\"CharacterToken\");\n  var _component_MonsterToken = _resolveComponent(\"MonsterToken\");\n  var _component_CombatAnimation = _resolveComponent(\"CombatAnimation\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createCommentVNode(\" 2D战场网格系统 \"), _createElementVNode(\"div\", _hoisted_1, [_createCommentVNode(\" 战场工具栏 \"), _createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"button\", {\n    onClick: _cache[0] || (_cache[0] = function () {\n      return $options.zoomOut && $options.zoomOut.apply($options, arguments);\n    }),\n    \"class\": \"zoom-btn\",\n    disabled: $data.zoom <= 0.5\n  }, _cache[20] || (_cache[20] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-search-minus\"\n  }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_5), _createElementVNode(\"span\", _hoisted_6, _toDisplayString(Math.round($data.zoom * 100)) + \"%\", 1 /* TEXT */), _createElementVNode(\"button\", {\n    onClick: _cache[1] || (_cache[1] = function () {\n      return $options.zoomIn && $options.zoomIn.apply($options, arguments);\n    }),\n    \"class\": \"zoom-btn\",\n    disabled: $data.zoom >= 2\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-search-plus\"\n  }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_7)]), _createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"button\", {\n    onClick: _cache[2] || (_cache[2] = function () {\n      return $options.toggleGrid && $options.toggleGrid.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"grid-btn\", {\n      active: $data.showGrid\n    }])\n  }, _cache[22] || (_cache[22] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-th\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 网格 \")]), 2 /* CLASS */), _createElementVNode(\"button\", {\n    onClick: _cache[3] || (_cache[3] = function () {\n      return $options.toggleRuler && $options.toggleRuler.apply($options, arguments);\n    }),\n    \"class\": _normalizeClass([\"ruler-btn\", {\n      active: $data.showRuler\n    }])\n  }, _cache[23] || (_cache[23] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-ruler\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 测距 \")]), 2 /* CLASS */)])]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"span\", _hoisted_11, \"第\" + _toDisplayString($props.currentRound) + \"轮\", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_12, _toDisplayString(((_$props$currentTurnCh = $props.currentTurnCharacter) === null || _$props$currentTurnCh === void 0 ? void 0 : _$props$currentTurnCh.name) || '等待开始') + \"的回合\", 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"button\", {\n    onClick: _cache[4] || (_cache[4] = function () {\n      return $options.centerView && $options.centerView.apply($options, arguments);\n    }),\n    \"class\": \"center-btn\"\n  }, _cache[24] || (_cache[24] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-crosshairs\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 居中 \")])), _createElementVNode(\"button\", {\n    onClick: _cache[5] || (_cache[5] = function () {\n      return $options.fitToScreen && $options.fitToScreen.apply($options, arguments);\n    }),\n    \"class\": \"fit-btn\"\n  }, _cache[25] || (_cache[25] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-expand-arrows-alt\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 适应屏幕 \")]))])])]), _createCommentVNode(\" 战场主体 \"), _createElementVNode(\"div\", _hoisted_15, [_createCommentVNode(\" SVG战场 \"), (_openBlock(), _createElementBlock(\"svg\", {\n    ref: \"battlefieldSvg\",\n    \"class\": \"battlefield-svg\",\n    width: $data.svgWidth,\n    height: $data.svgHeight,\n    viewBox: $data.viewBox,\n    onMousedown: _cache[6] || (_cache[6] = function () {\n      return $options.handleMouseDown && $options.handleMouseDown.apply($options, arguments);\n    }),\n    onMousemove: _cache[7] || (_cache[7] = function () {\n      return $options.handleMouseMove && $options.handleMouseMove.apply($options, arguments);\n    }),\n    onMouseup: _cache[8] || (_cache[8] = function () {\n      return $options.handleMouseUp && $options.handleMouseUp.apply($options, arguments);\n    }),\n    onWheel: _cache[9] || (_cache[9] = function () {\n      return $options.handleWheel && $options.handleWheel.apply($options, arguments);\n    })\n  }, [_createCommentVNode(\" 定义 \"), _createElementVNode(\"defs\", null, [_createCommentVNode(\" 网格图案 \"), _createElementVNode(\"pattern\", {\n    id: \"grid\",\n    width: $props.gridSize * $data.zoom,\n    height: $props.gridSize * $data.zoom,\n    patternUnits: \"userSpaceOnUse\"\n  }, [_createElementVNode(\"path\", {\n    d: \"M \".concat($props.gridSize * $data.zoom, \" 0 L 0 0 0 \").concat($props.gridSize * $data.zoom),\n    fill: \"none\",\n    stroke: \"#e0e0e0\",\n    \"stroke-width\": \"1\",\n    opacity: \"0.5\"\n  }, null, 8 /* PROPS */, _hoisted_18)], 8 /* PROPS */, _hoisted_17), _createCommentVNode(\" 角色阴影滤镜 \"), _cache[26] || (_cache[26] = _createElementVNode(\"filter\", {\n    id: \"character-shadow\"\n  }, [_createElementVNode(\"feDropShadow\", {\n    dx: \"2\",\n    dy: \"2\",\n    stdDeviation: \"3\",\n    \"flood-opacity\": \"0.3\"\n  })], -1 /* CACHED */)), _createCommentVNode(\" 选中效果滤镜 \"), _cache[27] || (_cache[27] = _createElementVNode(\"filter\", {\n    id: \"selection-glow\"\n  }, [_createElementVNode(\"feGaussianBlur\", {\n    stdDeviation: \"3\",\n    result: \"coloredBlur\"\n  }), _createElementVNode(\"feMerge\", null, [_createElementVNode(\"feMergeNode\", {\n    \"in\": \"coloredBlur\"\n  }), _createElementVNode(\"feMergeNode\", {\n    \"in\": \"SourceGraphic\"\n  })])], -1 /* CACHED */))]), _createCommentVNode(\" 背景 \"), _createElementVNode(\"rect\", {\n    width: \"100%\",\n    height: \"100%\",\n    fill: $data.showGrid ? 'url(#grid)' : '#f8f9fa'\n  }, null, 8 /* PROPS */, _hoisted_19), _createCommentVNode(\" 地形和障碍物 \"), _createElementVNode(\"g\", _hoisted_20, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.obstacles, function (obstacle) {\n    return _openBlock(), _createElementBlock(\"rect\", {\n      key: obstacle.id,\n      x: obstacle.x * $props.gridSize * $data.zoom,\n      y: obstacle.y * $props.gridSize * $data.zoom,\n      width: obstacle.width * $props.gridSize * $data.zoom,\n      height: obstacle.height * $props.gridSize * $data.zoom,\n      fill: obstacle.color || '#8b4513',\n      opacity: obstacle.opacity || 0.7,\n      stroke: \"#654321\",\n      \"stroke-width\": \"2\"\n    }, null, 8 /* PROPS */, _hoisted_21);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 测距线 \"), $data.showRuler && $data.rulerStart && $data.rulerEnd ? (_openBlock(), _createElementBlock(\"g\", _hoisted_22, [_createElementVNode(\"line\", {\n    x1: $data.rulerStart.x * $data.zoom,\n    y1: $data.rulerStart.y * $data.zoom,\n    x2: $data.rulerEnd.x * $data.zoom,\n    y2: $data.rulerEnd.y * $data.zoom,\n    stroke: \"#ff6b6b\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"5,5\"\n  }, null, 8 /* PROPS */, _hoisted_23), _createElementVNode(\"text\", {\n    x: ($data.rulerStart.x + $data.rulerEnd.x) / 2 * $data.zoom,\n    y: ($data.rulerStart.y + $data.rulerEnd.y) / 2 * $data.zoom - 10,\n    \"text-anchor\": \"middle\",\n    fill: \"#ff6b6b\",\n    \"font-size\": \"14\",\n    \"font-weight\": \"bold\"\n  }, _toDisplayString($options.getRulerDistance()) + \"米 \", 9 /* TEXT, PROPS */, _hoisted_24)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 移动路径预览 \"), $data.movementPreview ? (_openBlock(), _createElementBlock(\"g\", _hoisted_25, [_createElementVNode(\"path\", {\n    d: $options.getMovementPath(),\n    fill: \"none\",\n    stroke: \"#4ecdc4\",\n    \"stroke-width\": \"3\",\n    \"stroke-dasharray\": \"8,4\",\n    opacity: \"0.8\"\n  }, null, 8 /* PROPS */, _hoisted_26), _createElementVNode(\"circle\", {\n    cx: $data.movementPreview.end.x * $props.gridSize * $data.zoom,\n    cy: $data.movementPreview.end.y * $props.gridSize * $data.zoom,\n    r: 8 * $data.zoom,\n    fill: \"#4ecdc4\",\n    opacity: \"0.6\"\n  }, null, 8 /* PROPS */, _hoisted_27)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 攻击范围显示 \"), $data.selectedCharacter && $data.showAttackRange ? (_openBlock(), _createElementBlock(\"g\", _hoisted_28, [_createElementVNode(\"circle\", {\n    cx: $data.selectedCharacter.position.x * $props.gridSize * $data.zoom,\n    cy: $data.selectedCharacter.position.y * $props.gridSize * $data.zoom,\n    r: $options.getAttackRange($data.selectedCharacter) * $props.gridSize * $data.zoom,\n    fill: \"rgba(255, 107, 107, 0.2)\",\n    stroke: \"#ff6b6b\",\n    \"stroke-width\": \"2\",\n    \"stroke-dasharray\": \"5,5\"\n  }, null, 8 /* PROPS */, _hoisted_29)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 角色层 \"), _createElementVNode(\"g\", _hoisted_30, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.characters, function (character) {\n    var _$data$selectedCharac, _$props$currentTurnCh2;\n    return _openBlock(), _createBlock(_component_CharacterToken, {\n      key: character.id,\n      character: character,\n      \"grid-size\": $props.gridSize,\n      zoom: $data.zoom,\n      selected: ((_$data$selectedCharac = $data.selectedCharacter) === null || _$data$selectedCharac === void 0 ? void 0 : _$data$selectedCharac.id) === character.id,\n      \"current-turn\": ((_$props$currentTurnCh2 = $props.currentTurnCharacter) === null || _$props$currentTurnCh2 === void 0 ? void 0 : _$props$currentTurnCh2.id) === character.id,\n      \"can-move\": $options.canMoveCharacter(character),\n      \"can-act\": $options.canActCharacter(character),\n      onSelect: $options.selectCharacter,\n      onMove: $options.moveCharacter,\n      onAction: $options.characterAction,\n      onContextMenu: $options.showCharacterMenu\n    }, null, 8 /* PROPS */, [\"character\", \"grid-size\", \"zoom\", \"selected\", \"current-turn\", \"can-move\", \"can-act\", \"onSelect\", \"onMove\", \"onAction\", \"onContextMenu\"]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 怪物层 \"), _createElementVNode(\"g\", _hoisted_31, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.monsters, function (monster) {\n    var _$data$selectedMonste;\n    return _openBlock(), _createBlock(_component_MonsterToken, {\n      key: monster.id,\n      monster: monster,\n      \"grid-size\": $props.gridSize,\n      zoom: $data.zoom,\n      selected: ((_$data$selectedMonste = $data.selectedMonster) === null || _$data$selectedMonste === void 0 ? void 0 : _$data$selectedMonste.id) === monster.id,\n      \"can-control\": $props.isKeeper,\n      onSelect: $options.selectMonster,\n      onMove: $options.moveMonster,\n      onAction: $options.monsterAction,\n      onContextMenu: $options.showMonsterMenu\n    }, null, 8 /* PROPS */, [\"monster\", \"grid-size\", \"zoom\", \"selected\", \"can-control\", \"onSelect\", \"onMove\", \"onAction\", \"onContextMenu\"]);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 效果层 \"), _createElementVNode(\"g\", _hoisted_32, [_createCommentVNode(\" 法术效果区域 \"), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.areaEffects, function (effect) {\n    return _openBlock(), _createElementBlock(\"circle\", {\n      key: effect.id,\n      cx: effect.position.x * $props.gridSize * $data.zoom,\n      cy: effect.position.y * $props.gridSize * $data.zoom,\n      r: effect.radius * $props.gridSize * $data.zoom,\n      fill: effect.color,\n      opacity: effect.opacity || 0.3,\n      stroke: effect.borderColor || effect.color,\n      \"stroke-width\": \"2\"\n    }, null, 8 /* PROPS */, _hoisted_33);\n  }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 动画层 \"), _createElementVNode(\"g\", _hoisted_34, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.activeAnimations, function (animation) {\n    return _openBlock(), _createBlock(_component_CombatAnimation, {\n      key: animation.id,\n      animation: animation,\n      \"grid-size\": $props.gridSize,\n      zoom: $data.zoom,\n      onComplete: $options.removeAnimation\n    }, null, 8 /* PROPS */, [\"animation\", \"grid-size\", \"zoom\", \"onComplete\"]);\n  }), 128 /* KEYED_FRAGMENT */))])], 40 /* PROPS, NEED_HYDRATION */, _hoisted_16)), _createCommentVNode(\" 角色右键菜单 \"), $data.contextMenu.show && $data.contextMenu.type === 'character' ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    \"class\": \"context-menu\",\n    style: _normalizeStyle({\n      left: $data.contextMenu.x + 'px',\n      top: $data.contextMenu.y + 'px'\n    })\n  }, [_createElementVNode(\"div\", _hoisted_35, [_createElementVNode(\"img\", {\n    src: $data.contextMenu.target.avatar,\n    alt: $data.contextMenu.target.name\n  }, null, 8 /* PROPS */, _hoisted_36), _createElementVNode(\"span\", null, _toDisplayString($data.contextMenu.target.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_37, [_createElementVNode(\"button\", {\n    onClick: _cache[10] || (_cache[10] = function ($event) {\n      return $options.inspectCharacter($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[28] || (_cache[28] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-search\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 查看详情 \")])), $options.canMoveCharacter($data.contextMenu.target) ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[11] || (_cache[11] = function ($event) {\n      return $options.startMovement($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[29] || (_cache[29] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-walking\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 移动 \")]))) : _createCommentVNode(\"v-if\", true), $options.canActCharacter($data.contextMenu.target) ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    onClick: _cache[12] || (_cache[12] = function ($event) {\n      return $options.showActionMenu($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[30] || (_cache[30] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-fist-raised\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 行动 \")]))) : _createCommentVNode(\"v-if\", true), $props.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 2,\n    onClick: _cache[13] || (_cache[13] = function ($event) {\n      return $options.editCharacter($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[31] || (_cache[31] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-edit\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 编辑 \")]))) : _createCommentVNode(\"v-if\", true)])], 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 怪物右键菜单 \"), $data.contextMenu.show && $data.contextMenu.type === 'monster' ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    \"class\": \"context-menu\",\n    style: _normalizeStyle({\n      left: $data.contextMenu.x + 'px',\n      top: $data.contextMenu.y + 'px'\n    })\n  }, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"div\", _hoisted_39, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass($options.getMonsterIcon($data.contextMenu.target))\n  }, null, 2 /* CLASS */)]), _createElementVNode(\"span\", null, _toDisplayString($data.contextMenu.target.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_40, [_createElementVNode(\"button\", {\n    onClick: _cache[14] || (_cache[14] = function ($event) {\n      return $options.inspectMonster($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[32] || (_cache[32] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-search\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 查看详情 \")])), $props.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[15] || (_cache[15] = function ($event) {\n      return $options.controlMonster($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[33] || (_cache[33] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-gamepad\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 控制 \")]))) : _createCommentVNode(\"v-if\", true), $props.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 1,\n    onClick: _cache[16] || (_cache[16] = function ($event) {\n      return $options.editMonster($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item\"\n  }, _cache[34] || (_cache[34] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-edit\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 编辑 \")]))) : _createCommentVNode(\"v-if\", true), $props.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 2,\n    onClick: _cache[17] || (_cache[17] = function ($event) {\n      return $options.removeMonster($data.contextMenu.target);\n    }),\n    \"class\": \"menu-item danger\"\n  }, _cache[35] || (_cache[35] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-trash\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 移除 \")]))) : _createCommentVNode(\"v-if\", true)])], 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true)], 512 /* NEED_PATCH */), _createCommentVNode(\" 战场状态栏 \"), _createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [$data.selectedCharacter || $data.selectedMonster ? (_openBlock(), _createElementBlock(\"div\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [$data.selectedCharacter ? (_openBlock(), _createElementBlock(\"img\", {\n    key: 0,\n    src: $data.selectedCharacter.avatar,\n    alt: $data.selectedCharacter.name\n  }, null, 8 /* PROPS */, _hoisted_45)) : $data.selectedMonster ? (_openBlock(), _createElementBlock(\"div\", _hoisted_46, [_createElementVNode(\"i\", {\n    \"class\": _normalizeClass($options.getMonsterIcon($data.selectedMonster))\n  }, null, 2 /* CLASS */)])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, _toDisplayString((_ref = $data.selectedCharacter || $data.selectedMonster) === null || _ref === void 0 ? void 0 : _ref.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_49, [_createElementVNode(\"span\", _hoisted_50, \" HP: \" + _toDisplayString((_ref2 = $data.selectedCharacter || $data.selectedMonster) === null || _ref2 === void 0 ? void 0 : _ref2.currentHP) + \"/\" + _toDisplayString((_ref3 = $data.selectedCharacter || $data.selectedMonster) === null || _ref3 === void 0 ? void 0 : _ref3.maxHP), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_51, \" 位置: (\" + _toDisplayString((_ref4 = $data.selectedCharacter || $data.selectedMonster) === null || _ref4 === void 0 ? void 0 : _ref4.position.x) + \", \" + _toDisplayString((_ref5 = $data.selectedCharacter || $data.selectedMonster) === null || _ref5 === void 0 ? void 0 : _ref5.position.y) + \") \", 1 /* TEXT */)])])])) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"div\", _hoisted_53, [_createElementVNode(\"span\", _hoisted_54, [_cache[36] || (_cache[36] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-users\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" 角色: \" + _toDisplayString($props.characters.length), 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_55, [_cache[37] || (_cache[37] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-dragon\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" 怪物: \" + _toDisplayString($props.monsters.length), 1 /* TEXT */)]), _createElementVNode(\"span\", _hoisted_56, [_cache[38] || (_cache[38] = _createElementVNode(\"i\", {\n    \"class\": \"fas fa-map\"\n  }, null, -1 /* CACHED */)), _createTextVNode(\" \" + _toDisplayString($props.battlefieldWidth) + \"×\" + _toDisplayString($props.battlefieldHeight), 1 /* TEXT */)])])]), _createElementVNode(\"div\", _hoisted_57, [_createElementVNode(\"div\", _hoisted_58, [$props.isKeeper ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[18] || (_cache[18] = function () {\n      return $options.addMonster && $options.addMonster.apply($options, arguments);\n    }),\n    \"class\": \"action-btn\"\n  }, _cache[39] || (_cache[39] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-plus\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 添加怪物 \")]))) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[19] || (_cache[19] = function () {\n      return $options.resetView && $options.resetView.apply($options, arguments);\n    }),\n    \"class\": \"action-btn\"\n  }, _cache[40] || (_cache[40] = [_createElementVNode(\"i\", {\n    \"class\": \"fas fa-home\"\n  }, null, -1 /* CACHED */), _createTextVNode(\" 重置视图 \")]))])])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */);\n}", "map": {"version": 3, "names": ["ref", "_createCommentVNode", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_hoisted_3", "_hoisted_4", "onClick", "_cache", "$options", "zoomOut", "apply", "arguments", "disabled", "$data", "zoom", "_hoisted_6", "_toDisplayString", "Math", "round", "zoomIn", "_hoisted_8", "to<PERSON><PERSON><PERSON>", "_normalizeClass", "active", "showGrid", "toggleRuler", "showRuler", "_hoisted_9", "_hoisted_10", "_hoisted_11", "$props", "currentRound", "_hoisted_12", "_$props$currentTurnCh", "currentTurnCharacter", "name", "_hoisted_13", "_hoisted_14", "centerView", "fitToScreen", "_hoisted_15", "_createElementBlock", "width", "svgWidth", "height", "svgHeight", "viewBox", "onMousedown", "handleMouseDown", "onMousemove", "handleMouseMove", "onMouseup", "handleMouseUp", "onWheel", "handleWheel", "id", "gridSize", "patternUnits", "d", "concat", "fill", "stroke", "opacity", "dx", "dy", "stdDeviation", "result", "_hoisted_20", "_Fragment", "_renderList", "obstacles", "obstacle", "key", "x", "y", "color", "rulerS<PERSON>t", "rulerEnd", "_hoisted_22", "x1", "y1", "x2", "y2", "getRulerDistance", "_hoisted_24", "movementPreview", "_hoisted_25", "getMovementPath", "cx", "end", "cy", "r", "<PERSON><PERSON><PERSON><PERSON>", "showAttackRange", "_hoisted_28", "position", "getAttackRange", "_hoisted_30", "characters", "character", "_$data$selectedCharac", "_$props$currentTurnCh2", "_createBlock", "_component_CharacterToken", "selected", "canMoveCharacter", "canActCharacter", "onSelect", "selectCharacter", "onMove", "moveCharacter", "onAction", "characterAction", "onContextMenu", "showCharacterMenu", "_hoisted_31", "monsters", "monster", "_$data$selectedMonste", "_component_MonsterToken", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "selectMonster", "moveMonster", "monsterAction", "showMonsterMenu", "_hoisted_32", "areaEffects", "effect", "radius", "borderColor", "_hoisted_34", "activeAnimations", "animation", "_component_CombatAnimation", "onComplete", "removeAnimation", "contextMenu", "show", "type", "style", "_normalizeStyle", "left", "top", "_hoisted_35", "src", "target", "avatar", "alt", "_hoisted_37", "$event", "inspectCharacter", "startMovement", "showActionMenu", "edit<PERSON><PERSON><PERSON>", "_hoisted_38", "_hoisted_39", "getMonsterIcon", "_hoisted_40", "inspectMonster", "controlMonster", "editMonster", "removeMonster", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_ref", "_hoisted_49", "_hoisted_50", "_ref2", "currentHP", "_ref3", "maxHP", "_hoisted_51", "_ref4", "_ref5", "_hoisted_52", "_hoisted_53", "_hoisted_54", "length", "_hoisted_55", "_hoisted_56", "battlefieldWidth", "battlefieldHeight", "_hoisted_57", "_hoisted_58", "addMonster", "resetView"], "sources": ["C:\\Users\\<USER>\\Desktop\\最新的 - 副本\\7.26\\coc_trpgs\\frontend\\src\\components\\combat\\BattlefieldGrid.vue"], "sourcesContent": ["<template>\r\n  <!-- 2D战场网格系统 -->\r\n  <div class=\"battlefield-container\">\r\n    <!-- 战场工具栏 -->\r\n    <div class=\"battlefield-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <div class=\"zoom-controls\">\r\n          <button @click=\"zoomOut\" class=\"zoom-btn\" :disabled=\"zoom <= 0.5\">\r\n            <i class=\"fas fa-search-minus\"></i>\r\n          </button>\r\n          <span class=\"zoom-display\">{{ Math.round(zoom * 100) }}%</span>\r\n          <button @click=\"zoomIn\" class=\"zoom-btn\" :disabled=\"zoom >= 2\">\r\n            <i class=\"fas fa-search-plus\"></i>\r\n          </button>\r\n        </div>\r\n        <div class=\"grid-controls\">\r\n          <button @click=\"toggleGrid\" class=\"grid-btn\" :class=\"{ active: showGrid }\">\r\n            <i class=\"fas fa-th\"></i>\r\n            网格\r\n          </button>\r\n          <button @click=\"toggleRuler\" class=\"ruler-btn\" :class=\"{ active: showRuler }\">\r\n            <i class=\"fas fa-ruler\"></i>\r\n            测距\r\n          </button>\r\n        </div>\r\n      </div>\r\n      <div class=\"toolbar-center\">\r\n        <div class=\"battlefield-info\">\r\n          <span class=\"round-info\">第{{ currentRound }}轮</span>\r\n          <span class=\"turn-info\">{{ currentTurnCharacter?.name || '等待开始' }}的回合</span>\r\n        </div>\r\n      </div>\r\n      <div class=\"toolbar-right\">\r\n        <div class=\"view-controls\">\r\n          <button @click=\"centerView\" class=\"center-btn\">\r\n            <i class=\"fas fa-crosshairs\"></i>\r\n            居中\r\n          </button>\r\n          <button @click=\"fitToScreen\" class=\"fit-btn\">\r\n            <i class=\"fas fa-expand-arrows-alt\"></i>\r\n            适应屏幕\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战场主体 -->\r\n    <div class=\"battlefield-main\" ref=\"battlefieldContainer\">\r\n      <!-- SVG战场 -->\r\n      <svg \r\n        ref=\"battlefieldSvg\"\r\n        class=\"battlefield-svg\"\r\n        :width=\"svgWidth\"\r\n        :height=\"svgHeight\"\r\n        :viewBox=\"viewBox\"\r\n        @mousedown=\"handleMouseDown\"\r\n        @mousemove=\"handleMouseMove\"\r\n        @mouseup=\"handleMouseUp\"\r\n        @wheel=\"handleWheel\"\r\n      >\r\n        <!-- 定义 -->\r\n        <defs>\r\n          <!-- 网格图案 -->\r\n          <pattern id=\"grid\" :width=\"gridSize * zoom\" :height=\"gridSize * zoom\" patternUnits=\"userSpaceOnUse\">\r\n            <path \r\n              :d=\"`M ${gridSize * zoom} 0 L 0 0 0 ${gridSize * zoom}`\" \r\n              fill=\"none\" \r\n              stroke=\"#e0e0e0\" \r\n              stroke-width=\"1\"\r\n              opacity=\"0.5\"\r\n            />\r\n          </pattern>\r\n          <!-- 角色阴影滤镜 -->\r\n          <filter id=\"character-shadow\">\r\n            <feDropShadow dx=\"2\" dy=\"2\" stdDeviation=\"3\" flood-opacity=\"0.3\"/>\r\n          </filter>\r\n          <!-- 选中效果滤镜 -->\r\n          <filter id=\"selection-glow\">\r\n            <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\r\n            <feMerge> \r\n              <feMergeNode in=\"coloredBlur\"/>\r\n              <feMergeNode in=\"SourceGraphic\"/>\r\n            </feMerge>\r\n          </filter>\r\n        </defs>\r\n\r\n        <!-- 背景 -->\r\n        <rect \r\n          width=\"100%\" \r\n          height=\"100%\" \r\n          :fill=\"showGrid ? 'url(#grid)' : '#f8f9fa'\"\r\n        />\r\n\r\n        <!-- 地形和障碍物 -->\r\n        <g class=\"terrain-layer\">\r\n          <rect \r\n            v-for=\"obstacle in obstacles\" \r\n            :key=\"obstacle.id\"\r\n            :x=\"obstacle.x * gridSize * zoom\"\r\n            :y=\"obstacle.y * gridSize * zoom\"\r\n            :width=\"obstacle.width * gridSize * zoom\"\r\n            :height=\"obstacle.height * gridSize * zoom\"\r\n            :fill=\"obstacle.color || '#8b4513'\"\r\n            :opacity=\"obstacle.opacity || 0.7\"\r\n            stroke=\"#654321\"\r\n            stroke-width=\"2\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 测距线 -->\r\n        <g class=\"ruler-layer\" v-if=\"showRuler && rulerStart && rulerEnd\">\r\n          <line \r\n            :x1=\"rulerStart.x * zoom\"\r\n            :y1=\"rulerStart.y * zoom\"\r\n            :x2=\"rulerEnd.x * zoom\"\r\n            :y2=\"rulerEnd.y * zoom\"\r\n            stroke=\"#ff6b6b\"\r\n            stroke-width=\"2\"\r\n            stroke-dasharray=\"5,5\"\r\n          />\r\n          <text \r\n            :x=\"(rulerStart.x + rulerEnd.x) / 2 * zoom\"\r\n            :y=\"(rulerStart.y + rulerEnd.y) / 2 * zoom - 10\"\r\n            text-anchor=\"middle\"\r\n            fill=\"#ff6b6b\"\r\n            font-size=\"14\"\r\n            font-weight=\"bold\"\r\n          >\r\n            {{ getRulerDistance() }}米\r\n          </text>\r\n        </g>\r\n\r\n        <!-- 移动路径预览 -->\r\n        <g class=\"movement-preview\" v-if=\"movementPreview\">\r\n          <path \r\n            :d=\"getMovementPath()\"\r\n            fill=\"none\"\r\n            stroke=\"#4ecdc4\"\r\n            stroke-width=\"3\"\r\n            stroke-dasharray=\"8,4\"\r\n            opacity=\"0.8\"\r\n          />\r\n          <circle \r\n            :cx=\"movementPreview.end.x * gridSize * zoom\"\r\n            :cy=\"movementPreview.end.y * gridSize * zoom\"\r\n            :r=\"8 * zoom\"\r\n            fill=\"#4ecdc4\"\r\n            opacity=\"0.6\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 攻击范围显示 -->\r\n        <g class=\"attack-range\" v-if=\"selectedCharacter && showAttackRange\">\r\n          <circle \r\n            :cx=\"selectedCharacter.position.x * gridSize * zoom\"\r\n            :cy=\"selectedCharacter.position.y * gridSize * zoom\"\r\n            :r=\"getAttackRange(selectedCharacter) * gridSize * zoom\"\r\n            fill=\"rgba(255, 107, 107, 0.2)\"\r\n            stroke=\"#ff6b6b\"\r\n            stroke-width=\"2\"\r\n            stroke-dasharray=\"5,5\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 角色层 -->\r\n        <g class=\"characters-layer\">\r\n          <CharacterToken\r\n            v-for=\"character in characters\"\r\n            :key=\"character.id\"\r\n            :character=\"character\"\r\n            :grid-size=\"gridSize\"\r\n            :zoom=\"zoom\"\r\n            :selected=\"selectedCharacter?.id === character.id\"\r\n            :current-turn=\"currentTurnCharacter?.id === character.id\"\r\n            :can-move=\"canMoveCharacter(character)\"\r\n            :can-act=\"canActCharacter(character)\"\r\n            @select=\"selectCharacter\"\r\n            @move=\"moveCharacter\"\r\n            @action=\"characterAction\"\r\n            @context-menu=\"showCharacterMenu\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 怪物层 -->\r\n        <g class=\"monsters-layer\">\r\n          <MonsterToken\r\n            v-for=\"monster in monsters\"\r\n            :key=\"monster.id\"\r\n            :monster=\"monster\"\r\n            :grid-size=\"gridSize\"\r\n            :zoom=\"zoom\"\r\n            :selected=\"selectedMonster?.id === monster.id\"\r\n            :can-control=\"isKeeper\"\r\n            @select=\"selectMonster\"\r\n            @move=\"moveMonster\"\r\n            @action=\"monsterAction\"\r\n            @context-menu=\"showMonsterMenu\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 效果层 -->\r\n        <g class=\"effects-layer\">\r\n          <!-- 法术效果区域 -->\r\n          <circle \r\n            v-for=\"effect in areaEffects\" \r\n            :key=\"effect.id\"\r\n            :cx=\"effect.position.x * gridSize * zoom\"\r\n            :cy=\"effect.position.y * gridSize * zoom\"\r\n            :r=\"effect.radius * gridSize * zoom\"\r\n            :fill=\"effect.color\"\r\n            :opacity=\"effect.opacity || 0.3\"\r\n            :stroke=\"effect.borderColor || effect.color\"\r\n            stroke-width=\"2\"\r\n          />\r\n        </g>\r\n\r\n        <!-- 动画层 -->\r\n        <g class=\"animations-layer\">\r\n          <CombatAnimation\r\n            v-for=\"animation in activeAnimations\"\r\n            :key=\"animation.id\"\r\n            :animation=\"animation\"\r\n            :grid-size=\"gridSize\"\r\n            :zoom=\"zoom\"\r\n            @complete=\"removeAnimation\"\r\n          />\r\n        </g>\r\n      </svg>\r\n\r\n      <!-- 角色右键菜单 -->\r\n      <div \r\n        v-if=\"contextMenu.show && contextMenu.type === 'character'\"\r\n        class=\"context-menu\"\r\n        :style=\"{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }\"\r\n      >\r\n        <div class=\"menu-header\">\r\n          <img :src=\"contextMenu.target.avatar\" :alt=\"contextMenu.target.name\">\r\n          <span>{{ contextMenu.target.name }}</span>\r\n        </div>\r\n        <div class=\"menu-items\">\r\n          <button @click=\"inspectCharacter(contextMenu.target)\" class=\"menu-item\">\r\n            <i class=\"fas fa-search\"></i>\r\n            查看详情\r\n          </button>\r\n          <button \r\n            v-if=\"canMoveCharacter(contextMenu.target)\"\r\n            @click=\"startMovement(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-walking\"></i>\r\n            移动\r\n          </button>\r\n          <button \r\n            v-if=\"canActCharacter(contextMenu.target)\"\r\n            @click=\"showActionMenu(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-fist-raised\"></i>\r\n            行动\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"editCharacter(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-edit\"></i>\r\n            编辑\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 怪物右键菜单 -->\r\n      <div \r\n        v-if=\"contextMenu.show && contextMenu.type === 'monster'\"\r\n        class=\"context-menu\"\r\n        :style=\"{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }\"\r\n      >\r\n        <div class=\"menu-header\">\r\n          <div class=\"monster-icon\">\r\n            <i :class=\"getMonsterIcon(contextMenu.target)\"></i>\r\n          </div>\r\n          <span>{{ contextMenu.target.name }}</span>\r\n        </div>\r\n        <div class=\"menu-items\">\r\n          <button @click=\"inspectMonster(contextMenu.target)\" class=\"menu-item\">\r\n            <i class=\"fas fa-search\"></i>\r\n            查看详情\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"controlMonster(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-gamepad\"></i>\r\n            控制\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"editMonster(contextMenu.target)\" \r\n            class=\"menu-item\"\r\n          >\r\n            <i class=\"fas fa-edit\"></i>\r\n            编辑\r\n          </button>\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"removeMonster(contextMenu.target)\" \r\n            class=\"menu-item danger\"\r\n          >\r\n            <i class=\"fas fa-trash\"></i>\r\n            移除\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 战场状态栏 -->\r\n    <div class=\"battlefield-status\">\r\n      <div class=\"status-left\">\r\n        <div class=\"selected-info\" v-if=\"selectedCharacter || selectedMonster\">\r\n          <div class=\"selected-avatar\">\r\n            <img \r\n              v-if=\"selectedCharacter\" \r\n              :src=\"selectedCharacter.avatar\" \r\n              :alt=\"selectedCharacter.name\"\r\n            >\r\n            <div v-else-if=\"selectedMonster\" class=\"monster-avatar\">\r\n              <i :class=\"getMonsterIcon(selectedMonster)\"></i>\r\n            </div>\r\n          </div>\r\n          <div class=\"selected-details\">\r\n            <div class=\"selected-name\">\r\n              {{ (selectedCharacter || selectedMonster)?.name }}\r\n            </div>\r\n            <div class=\"selected-stats\">\r\n              <span class=\"hp\">\r\n                HP: {{ (selectedCharacter || selectedMonster)?.currentHP }}/{{ (selectedCharacter || selectedMonster)?.maxHP }}\r\n              </span>\r\n              <span class=\"position\">\r\n                位置: ({{ (selectedCharacter || selectedMonster)?.position.x }}, {{ (selectedCharacter || selectedMonster)?.position.y }})\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"status-center\">\r\n        <div class=\"battlefield-stats\">\r\n          <span class=\"stat-item\">\r\n            <i class=\"fas fa-users\"></i>\r\n            角色: {{ characters.length }}\r\n          </span>\r\n          <span class=\"stat-item\">\r\n            <i class=\"fas fa-dragon\"></i>\r\n            怪物: {{ monsters.length }}\r\n          </span>\r\n          <span class=\"stat-item\">\r\n            <i class=\"fas fa-map\"></i>\r\n            {{ battlefieldWidth }}×{{ battlefieldHeight }}\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <div class=\"status-right\">\r\n        <div class=\"action-buttons\">\r\n          <button \r\n            v-if=\"isKeeper\"\r\n            @click=\"addMonster\" \r\n            class=\"action-btn\"\r\n          >\r\n            <i class=\"fas fa-plus\"></i>\r\n            添加怪物\r\n          </button>\r\n          <button \r\n            @click=\"resetView\" \r\n            class=\"action-btn\"\r\n          >\r\n            <i class=\"fas fa-home\"></i>\r\n            重置视图\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CharacterToken from './CharacterToken.vue'\r\nimport MonsterToken from './MonsterToken.vue'\r\nimport CombatAnimation from './CombatAnimation.vue'\r\n\r\nexport default {\r\n  name: 'BattlefieldGrid',\r\n  components: {\r\n    CharacterToken,\r\n    MonsterToken,\r\n    CombatAnimation\r\n  },\r\n  props: {\r\n    characters: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    monsters: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    obstacles: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    areaEffects: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    currentRound: {\r\n      type: Number,\r\n      default: 1\r\n    },\r\n    currentTurnCharacter: {\r\n      type: Object,\r\n      default: null\r\n    },\r\n    isKeeper: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    battlefieldWidth: {\r\n      type: Number,\r\n      default: 20\r\n    },\r\n    battlefieldHeight: {\r\n      type: Number,\r\n      default: 15\r\n    },\r\n    gridSize: {\r\n      type: Number,\r\n      default: 40\r\n    }\r\n  },\r\n  \r\n  data() {\r\n    return {\r\n      // 视图控制\r\n      zoom: 1,\r\n      viewBox: '0 0 800 600',\r\n      svgWidth: 800,\r\n      svgHeight: 600,\r\n      // 显示控制\r\n      showGrid: true,\r\n      showRuler: false,\r\n      showAttackRange: false,\r\n      // 选择状态\r\n      selectedCharacter: null,\r\n      selectedMonster: null,\r\n      // 交互状态\r\n      isDragging: false,\r\n      dragStart: null,\r\n      movementPreview: null,\r\n      // 测距工具\r\n      rulerStart: null,\r\n      rulerEnd: null,\r\n      // 右键菜单\r\n      contextMenu: {\r\n        show: false,\r\n        type: null,\r\n        target: null,\r\n        x: 0,\r\n        y: 0\r\n      },\r\n      // 动画\r\n      activeAnimations: []\r\n    }\r\n  },\r\n  \r\n  computed: {\r\n    // 计算SVG视图框\r\n    calculatedViewBox() {\r\n      const width = this.battlefieldWidth * this.gridSize\r\n      const height = this.battlefieldHeight * this.gridSize\r\n      return `0 0 ${width} ${height}`\r\n    }\r\n  },\r\n  \r\n  mounted() {\r\n    this.initializeBattlefield()\r\n    this.setupEventListeners()\r\n  },\r\n  \r\n  beforeUnmount() {\r\n    this.removeEventListeners()\r\n  },\r\n  \r\n  methods: {\r\n    /**\r\n     * 初始化战场\r\n     */\r\n    initializeBattlefield() {\r\n      this.updateViewBox()\r\n      this.centerView()\r\n    },\r\n    \r\n    /**\r\n     * 设置事件监听器\r\n     */\r\n    setupEventListeners() {\r\n      document.addEventListener('click', this.handleDocumentClick)\r\n      document.addEventListener('keydown', this.handleKeyDown)\r\n      window.addEventListener('resize', this.handleResize)\r\n    },\r\n    \r\n    /**\r\n     * 移除事件监听器\r\n     */\r\n    removeEventListeners() {\r\n      document.removeEventListener('click', this.handleDocumentClick)\r\n      document.removeEventListener('keydown', this.handleKeyDown)\r\n      window.removeEventListener('resize', this.handleResize)\r\n    },\r\n    \r\n    /**\r\n     * 更新视图框\r\n     */\r\n    updateViewBox() {\r\n      const width = this.battlefieldWidth * this.gridSize\r\n      const height = this.battlefieldHeight * this.gridSize\r\n      this.viewBox = `0 0 ${width} ${height}`\r\n      this.svgWidth = width * this.zoom\r\n      this.svgHeight = height * this.zoom\r\n    },\r\n    \r\n    /**\r\n     * 缩放控制\r\n     */\r\n    zoomIn() {\r\n      this.zoom = Math.min(2, this.zoom + 0.1)\r\n      this.updateViewBox()\r\n    },\r\n    \r\n    zoomOut() {\r\n      this.zoom = Math.max(0.5, this.zoom - 0.1)\r\n      this.updateViewBox()\r\n    },\r\n    \r\n    /**\r\n     * 视图控制\r\n     */\r\n    centerView() {\r\n      const container = this.$refs.battlefieldContainer\r\n      if (container) {\r\n        container.scrollLeft = (this.svgWidth - container.clientWidth) / 2\r\n        container.scrollTop = (this.svgHeight - container.clientHeight) / 2\r\n      }\r\n    },\r\n    \r\n    fitToScreen() {\r\n      const container = this.$refs.battlefieldContainer\r\n      if (container) {\r\n        const scaleX = container.clientWidth / (this.battlefieldWidth * this.gridSize)\r\n        const scaleY = container.clientHeight / (this.battlefieldHeight * this.gridSize)\r\n        this.zoom = Math.min(scaleX, scaleY, 2)\r\n        this.updateViewBox()\r\n        this.centerView()\r\n      }\r\n    },\r\n    \r\n    resetView() {\r\n      this.zoom = 1\r\n      this.updateViewBox()\r\n      this.centerView()\r\n    },\r\n    \r\n    /**\r\n     * 网格和工具切换\r\n     */\r\n    toggleGrid() {\r\n      this.showGrid = !this.showGrid\r\n    },\r\n    \r\n    toggleRuler() {\r\n      this.showRuler = !this.showRuler\r\n      if (!this.showRuler) {\r\n        this.rulerStart = null\r\n        this.rulerEnd = null\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 鼠标事件处理\r\n     */\r\n    handleMouseDown(event) {\r\n      if (this.showRuler) {\r\n        const rect = this.$refs.battlefieldSvg.getBoundingClientRect()\r\n        const x = (event.clientX - rect.left) / this.zoom\r\n        const y = (event.clientY - rect.top) / this.zoom\r\n        \r\n        if (!this.rulerStart) {\r\n          this.rulerStart = { x, y }\r\n        } else {\r\n          this.rulerEnd = { x, y }\r\n        }\r\n      }\r\n    },\r\n    \r\n    handleMouseMove(event) {\r\n      if (this.showRuler && this.rulerStart && !this.rulerEnd) {\r\n        const rect = this.$refs.battlefieldSvg.getBoundingClientRect()\r\n        const x = (event.clientX - rect.left) / this.zoom\r\n        const y = (event.clientY - rect.top) / this.zoom\r\n        this.rulerEnd = { x, y }\r\n      }\r\n    },\r\n    \r\n    handleMouseUp(event) {\r\n      // 处理鼠标释放\r\n    },\r\n    \r\n    handleWheel(event) {\r\n      event.preventDefault()\r\n      if (event.deltaY < 0) {\r\n        this.zoomIn()\r\n      } else {\r\n        this.zoomOut()\r\n      }\r\n    },\r\n    \r\n    handleDocumentClick(event) {\r\n      if (!event.target.closest('.context-menu')) {\r\n        this.contextMenu.show = false\r\n      }\r\n    },\r\n    \r\n    handleKeyDown(event) {\r\n      switch (event.key) {\r\n        case 'Escape':\r\n          this.clearSelection()\r\n          this.contextMenu.show = false\r\n          break\r\n        case 'g':\r\n        case 'G':\r\n          if (event.ctrlKey) {\r\n            event.preventDefault()\r\n            this.toggleGrid()\r\n          }\r\n          break\r\n        case 'r':\r\n        case 'R':\r\n          if (event.ctrlKey) {\r\n            event.preventDefault()\r\n            this.toggleRuler()\r\n          }\r\n          break\r\n      }\r\n    },\r\n    \r\n    handleResize() {\r\n      this.$nextTick(() => {\r\n        this.updateViewBox()\r\n      })\r\n    },\r\n    \r\n    /**\r\n     * 角色选择和操作\r\n     */\r\n    selectCharacter(character) {\r\n      this.selectedCharacter = character\r\n      this.selectedMonster = null\r\n      this.showAttackRange = true\r\n      this.$emit('character-selected', character)\r\n    },\r\n    \r\n    selectMonster(monster) {\r\n      this.selectedMonster = monster\r\n      this.selectedCharacter = null\r\n      this.showAttackRange = false\r\n      this.$emit('monster-selected', monster)\r\n    },\r\n    \r\n    clearSelection() {\r\n      this.selectedCharacter = null\r\n      this.selectedMonster = null\r\n      this.showAttackRange = false\r\n      this.movementPreview = null\r\n    },\r\n    \r\n    /**\r\n     * 移动相关\r\n     */\r\n    moveCharacter(character, newPosition) {\r\n      this.$emit('character-moved', {\r\n        character,\r\n        oldPosition: character.position,\r\n        newPosition\r\n      })\r\n    },\r\n    \r\n    moveMonster(monster, newPosition) {\r\n      this.$emit('monster-moved', {\r\n        monster,\r\n        oldPosition: monster.position,\r\n        newPosition\r\n      })\r\n    },\r\n    \r\n    startMovement(target) {\r\n      this.movementPreview = {\r\n        target,\r\n        start: { ...target.position },\r\n        end: { ...target.position }\r\n      }\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    /**\r\n     * 行动相关\r\n     */\r\n    characterAction(character, action) {\r\n      this.$emit('character-action', { character, action })\r\n    },\r\n    \r\n    monsterAction(monster, action) {\r\n      this.$emit('monster-action', { monster, action })\r\n    },\r\n    \r\n    /**\r\n     * 右键菜单\r\n     */\r\n    showCharacterMenu(character, event) {\r\n      this.contextMenu = {\r\n        show: true,\r\n        type: 'character',\r\n        target: character,\r\n        x: event.clientX,\r\n        y: event.clientY\r\n      }\r\n    },\r\n    \r\n    showMonsterMenu(monster, event) {\r\n      this.contextMenu = {\r\n        show: true,\r\n        type: 'monster',\r\n        target: monster,\r\n        x: event.clientX,\r\n        y: event.clientY\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 菜单操作\r\n     */\r\n    inspectCharacter(character) {\r\n      this.$emit('inspect-character', character)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    inspectMonster(monster) {\r\n      this.$emit('inspect-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    editCharacter(character) {\r\n      this.$emit('edit-character', character)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    editMonster(monster) {\r\n      this.$emit('edit-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    controlMonster(monster) {\r\n      this.$emit('control-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    removeMonster(monster) {\r\n      this.$emit('remove-monster', monster)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    addMonster() {\r\n      this.$emit('add-monster')\r\n    },\r\n    \r\n    showActionMenu(character) {\r\n      this.$emit('show-action-menu', character)\r\n      this.contextMenu.show = false\r\n    },\r\n    \r\n    /**\r\n     * 工具函数\r\n     */\r\n    canMoveCharacter(character) {\r\n      return this.currentTurnCharacter?.id === character.id && !character.hasActed\r\n    },\r\n    \r\n    canActCharacter(character) {\r\n      return this.currentTurnCharacter?.id === character.id && !character.hasActed\r\n    },\r\n    \r\n    getAttackRange(character) {\r\n      const weapon = character.equippedWeapon\r\n      if (!weapon) return 1.5\r\n      \r\n      if (weapon.type === 'melee') {\r\n        return weapon.reach || 1.5\r\n      } else if (weapon.type === 'ranged') {\r\n        return weapon.range?.base || 10\r\n      }\r\n      \r\n      return 1.5\r\n    },\r\n    \r\n    getRulerDistance() {\r\n      if (!this.rulerStart || !this.rulerEnd) return 0\r\n      \r\n      const dx = this.rulerEnd.x - this.rulerStart.x\r\n      const dy = this.rulerEnd.y - this.rulerStart.y\r\n      const pixelDistance = Math.sqrt(dx * dx + dy * dy)\r\n      const meterDistance = (pixelDistance / this.gridSize) * 1.5\r\n      \r\n      return Math.round(meterDistance * 10) / 10\r\n    },\r\n    \r\n    getMovementPath() {\r\n      if (!this.movementPreview) return ''\r\n      \r\n      const start = this.movementPreview.start\r\n      const end = this.movementPreview.end\r\n      \r\n      return `M ${start.x * this.gridSize * this.zoom} ${start.y * this.gridSize * this.zoom} L ${end.x * this.gridSize * this.zoom} ${end.y * this.gridSize * this.zoom}`\r\n    },\r\n    \r\n    getMonsterIcon(monster) {\r\n      const iconMap = {\r\n        'human': 'fas fa-user',\r\n        'animal': 'fas fa-paw',\r\n        'mythos': 'fas fa-eye',\r\n        'undead': 'fas fa-skull',\r\n        'demon': 'fas fa-fire',\r\n        'construct': 'fas fa-robot'\r\n      }\r\n      \r\n      return iconMap[monster.type] || 'fas fa-question'\r\n    },\r\n    \r\n    /**\r\n     * 动画管理\r\n     */\r\n    addAnimation(animation) {\r\n      this.activeAnimations.push({\r\n        ...animation,\r\n        id: Date.now() + Math.random()\r\n      })\r\n    },\r\n    \r\n    removeAnimation(animationId) {\r\n      const index = this.activeAnimations.findIndex(a => a.id === animationId)\r\n      if (index !== -1) {\r\n        this.activeAnimations.splice(index, 1)\r\n      }\r\n    },\r\n    \r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAttackAnimation(attacker, target, result) {\r\n      this.addAnimation({\r\n        type: 'attack',\r\n        from: attacker.position,\r\n        to: target.position,\r\n        result,\r\n        duration: 1000\r\n      })\r\n    },\r\n    \r\n    /**\r\n     * 播放移动动画\r\n     */\r\n    playMovementAnimation(character, from, to) {\r\n      this.addAnimation({\r\n        type: 'movement',\r\n        character,\r\n        from,\r\n        to,\r\n        duration: 800\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.battlefield-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.battlefield-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: linear-gradient(135deg, #2c3e50, #34495e);\r\n  color: white;\r\n  border-bottom: 2px solid #e74c3c;\r\n}\r\n\r\n.toolbar-left,\r\n.toolbar-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.toolbar-center {\r\n  flex: 1;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.zoom-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n}\r\n\r\n.zoom-btn {\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 4px 8px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.zoom-btn:hover:not(:disabled) {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.zoom-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.zoom-display {\r\n  font-weight: bold;\r\n  min-width: 50px;\r\n  text-align: center;\r\n}\r\n\r\n.grid-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.grid-btn,\r\n.ruler-btn {\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.grid-btn:hover,\r\n.ruler-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.grid-btn.active,\r\n.ruler-btn.active {\r\n  background: #e74c3c;\r\n  border-color: #e74c3c;\r\n}\r\n\r\n.battlefield-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  font-weight: bold;\r\n}\r\n\r\n.round-info {\r\n  color: #e74c3c;\r\n  font-size: 1.1em;\r\n}\r\n\r\n.turn-info {\r\n  color: #3498db;\r\n}\r\n\r\n.view-controls {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.center-btn,\r\n.fit-btn {\r\n  background: none;\r\n  border: 1px solid rgba(255, 255, 255, 0.3);\r\n  color: white;\r\n  padding: 6px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n}\r\n\r\n.center-btn:hover,\r\n.fit-btn:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.battlefield-main {\r\n  flex: 1;\r\n  overflow: auto;\r\n  position: relative;\r\n  background: #f8f9fa;\r\n}\r\n\r\n.battlefield-svg {\r\n  display: block;\r\n  cursor: crosshair;\r\n}\r\n\r\n.context-menu {\r\n  position: fixed;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);\r\n  z-index: 1000;\r\n  min-width: 200px;\r\n  overflow: hidden;\r\n}\r\n\r\n.menu-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px 16px;\r\n  background: #f8f9fa;\r\n  border-bottom: 1px solid #e9ecef;\r\n}\r\n\r\n.menu-header img {\r\n  width: 32px;\r\n  height: 32px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.monster-icon {\r\n  width: 32px;\r\n  height: 32px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #6c757d;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 16px;\r\n}\r\n\r\n.menu-items {\r\n  padding: 8px 0;\r\n}\r\n\r\n.menu-item {\r\n  width: 100%;\r\n  padding: 10px 16px;\r\n  border: none;\r\n  background: none;\r\n  text-align: left;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  transition: background 0.2s ease;\r\n}\r\n\r\n.menu-item:hover {\r\n  background: #f8f9fa;\r\n}\r\n\r\n.menu-item.danger {\r\n  color: #dc3545;\r\n}\r\n\r\n.menu-item.danger:hover {\r\n  background: #f8d7da;\r\n}\r\n\r\n.battlefield-status {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  background: white;\r\n  border-top: 1px solid #e9ecef;\r\n}\r\n\r\n.selected-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.selected-avatar img {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.monster-avatar {\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #6c757d;\r\n  color: white;\r\n  border-radius: 50%;\r\n  font-size: 18px;\r\n}\r\n\r\n.selected-details {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.selected-name {\r\n  font-weight: bold;\r\n  color: #2c3e50;\r\n}\r\n\r\n.selected-stats {\r\n  display: flex;\r\n  gap: 16px;\r\n  font-size: 0.9em;\r\n  color: #6c757d;\r\n}\r\n\r\n.battlefield-stats {\r\n  display: flex;\r\n  gap: 20px;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: #6c757d;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.action-btn {\r\n  background: #007bff;\r\n  color: white;\r\n  border: none;\r\n  padding: 8px 12px;\r\n  border-radius: 4px;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  transition: background 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: #0056b3;\r\n}\r\n</style>"], "mappings": ";;;;EAEO,SAAM;AAAuB;;EAE3B,SAAM;AAAqB;;EACzB,SAAM;AAAc;;EAClB,SAAM;AAAe;;;EAIlB,SAAM;AAAc;;;EAKvB,SAAM;AAAe;;EAWvB,SAAM;AAAgB;;EACpB,SAAM;AAAkB;;EACrB,SAAM;AAAY;;EAClB,SAAM;AAAW;;EAGtB,SAAM;AAAe;;EACnB,SAAM;AAAe;;EAczB,SAAM,kBAAkB;EAACA,GAAG,EAAC;;;;;;;EA+C3B,SAAM;AAAe;;;;EAgBrB,SAAM;;;;;;EAuBN,SAAM;;;;;;EAmBN,SAAM;;;;EAaN,SAAM;AAAkB;;EAmBxB,SAAM;AAAgB;;EAiBtB,SAAM;AAAe;;;EAgBrB,SAAM;AAAkB;;EAkBtB,SAAM;AAAa;;;EAInB,SAAM;AAAY;;EAsClB,SAAM;AAAa;;EACjB,SAAM;AAAc;;EAKtB,SAAM;AAAY;;EAkCtB,SAAM;AAAoB;;EACxB,SAAM;AAAa;;;EACjB,SAAM;;;EACJ,SAAM;AAAiB;;;;EAMO,SAAM;;;EAIpC,SAAM;AAAkB;;EACtB,SAAM;AAAe;;EAGrB,SAAM;AAAgB;;EACnB,SAAM;AAAI;;EAGV,SAAM;AAAU;;EAOzB,SAAM;AAAe;;EACnB,SAAM;AAAmB;;EACtB,SAAM;AAAW;;EAIjB,SAAM;AAAW;;EAIjB,SAAM;AAAW;;EAMtB,SAAM;AAAc;;EAClB,SAAM;AAAgB;;;;;;6DAzWjCC,mBAAA,cAAiB,EACjBC,mBAAA,CA2XM,OA3XNC,UA2XM,GA1XJF,mBAAA,WAAc,EACdC,mBAAA,CAwCM,OAxCNE,UAwCM,GAvCJF,mBAAA,CAoBM,OApBNG,UAoBM,GAnBJH,mBAAA,CAQM,OARNI,UAQM,GAPJJ,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAC,OAAA,IAAAD,QAAA,CAAAC,OAAA,CAAAC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAO;IAAA;IAAE,SAAM,UAAU;IAAEC,QAAQ,EAAEC,KAAA,CAAAC,IAAI;kCACvDb,mBAAA,CAAmC;IAAhC,SAAM;EAAqB,0B,+BAEhCA,mBAAA,CAA+D,QAA/Dc,UAA+D,EAAAC,gBAAA,CAAjCC,IAAI,CAACC,KAAK,CAACL,KAAA,CAAAC,IAAI,WAAU,GAAC,iBACxDb,mBAAA,CAES;IAFAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAW,MAAA,IAAAX,QAAA,CAAAW,MAAA,CAAAT,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAM;IAAA;IAAE,SAAM,UAAU;IAAEC,QAAQ,EAAEC,KAAA,CAAAC,IAAI;kCACtDb,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,0B,iCAGjCA,mBAAA,CASM,OATNmB,UASM,GARJnB,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAa,UAAA,IAAAb,QAAA,CAAAa,UAAA,CAAAX,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAU;IAAA;IAAE,SAAKW,eAAA,EAAC,UAAU;MAAAC,MAAA,EAAmBV,KAAA,CAAAW;IAAQ;kCACrEvB,mBAAA,CAAyB;IAAtB,SAAM;EAAW,2B,iBAAK,MAE3B,E,mBACAA,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAiB,WAAA,IAAAjB,QAAA,CAAAiB,WAAA,CAAAf,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAW;IAAA;IAAE,SAAKW,eAAA,EAAC,WAAW;MAAAC,MAAA,EAAmBV,KAAA,CAAAa;IAAS;kCACxEzB,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2B,iBAAK,MAE9B,E,uBAGJA,mBAAA,CAKM,OALN0B,UAKM,GAJJ1B,mBAAA,CAGM,OAHN2B,WAGM,GAFJ3B,mBAAA,CAAoD,QAApD4B,WAAoD,EAA3B,GAAC,GAAAb,gBAAA,CAAGc,MAAA,CAAAC,YAAY,IAAG,GAAC,iBAC7C9B,mBAAA,CAA4E,QAA5E+B,WAA4E,EAAAhB,gBAAA,CAAjD,EAAAiB,qBAAA,GAAAH,MAAA,CAAAI,oBAAoB,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBE,IAAI,eAAa,KAAG,gB,KAGzElC,mBAAA,CAWM,OAXNmC,WAWM,GAVJnC,mBAAA,CASM,OATNoC,WASM,GARJpC,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA8B,UAAA,IAAA9B,QAAA,CAAA8B,UAAA,CAAA5B,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAU;IAAA;IAAE,SAAM;kCAChCV,mBAAA,CAAiC;IAA9B,SAAM;EAAmB,2B,iBAAK,MAEnC,E,IACAA,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA+B,WAAA,IAAA/B,QAAA,CAAA+B,WAAA,CAAA7B,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAW;IAAA;IAAE,SAAM;kCACjCV,mBAAA,CAAwC;IAArC,SAAM;EAA0B,2B,iBAAK,QAE1C,E,UAKND,mBAAA,UAAa,EACbC,mBAAA,CA2QM,OA3QNuC,WA2QM,GA1QJxC,mBAAA,WAAc,G,cACdyC,mBAAA,CAkLM;IAjLJ1C,GAAG,EAAC,gBAAgB;IACpB,SAAM,iBAAiB;IACtB2C,KAAK,EAAE7B,KAAA,CAAA8B,QAAQ;IACfC,MAAM,EAAE/B,KAAA,CAAAgC,SAAS;IACjBC,OAAO,EAAEjC,KAAA,CAAAiC,OAAO;IAChBC,WAAS,EAAAxC,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAwC,eAAA,IAAAxC,QAAA,CAAAwC,eAAA,CAAAtC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAC1BsC,WAAS,EAAA1C,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA0C,eAAA,IAAA1C,QAAA,CAAA0C,eAAA,CAAAxC,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAe;IAAA;IAC1BwC,SAAO,EAAA5C,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA4C,aAAA,IAAA5C,QAAA,CAAA4C,aAAA,CAAA1C,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAa;IAAA;IACtB0C,OAAK,EAAA9C,MAAA,QAAAA,MAAA;MAAA,OAAEC,QAAA,CAAA8C,WAAA,IAAA9C,QAAA,CAAA8C,WAAA,CAAA5C,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAW;IAAA;MAEnBX,mBAAA,QAAW,EACXC,mBAAA,CAuBO,eAtBLD,mBAAA,UAAa,EACbC,mBAAA,CAQU;IARDsD,EAAE,EAAC,MAAM;IAAEb,KAAK,EAAEZ,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IAAG8B,MAAM,EAAEd,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IAAE2C,YAAY,EAAC;MACjFxD,mBAAA,CAME;IALCyD,CAAC,OAAAC,MAAA,CAAO7B,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI,iBAAA6C,MAAA,CAAc7B,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IACrD8C,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChBC,OAAO,EAAC;sEAGZ9D,mBAAA,YAAe,E,4BACfC,mBAAA,CAES;IAFDsD,EAAE,EAAC;EAAkB,IAC3BtD,mBAAA,CAAkE;IAApD8D,EAAE,EAAC,GAAG;IAACC,EAAE,EAAC,GAAG;IAACC,YAAY,EAAC,GAAG;IAAC,eAAa,EAAC;0BAE7DjE,mBAAA,YAAe,E,4BACfC,mBAAA,CAMS;IANDsD,EAAE,EAAC;EAAgB,IACzBtD,mBAAA,CAAuD;IAAvCgE,YAAY,EAAC,GAAG;IAACC,MAAM,EAAC;MACxCjE,mBAAA,CAGU,kBAFRA,mBAAA,CAA+B;IAAlB,MAAG;EAAa,IAC7BA,mBAAA,CAAiC;IAApB,MAAG;EAAe,G,yBAKrCD,mBAAA,QAAW,EACXC,mBAAA,CAIE;IAHAyC,KAAK,EAAC,MAAM;IACZE,MAAM,EAAC,MAAM;IACZgB,IAAI,EAAE/C,KAAA,CAAAW,QAAQ;wCAGjBxB,mBAAA,YAAe,EACfC,mBAAA,CAaI,KAbJkE,WAaI,I,kBAZF1B,mBAAA,CAWE2B,SAAA,QAAAC,WAAA,CAVmBvC,MAAA,CAAAwC,SAAS,YAArBC,QAAQ;yBADjB9B,mBAAA,CAWE;MATC+B,GAAG,EAAED,QAAQ,CAAChB,EAAE;MAChBkB,CAAC,EAAEF,QAAQ,CAACE,CAAC,GAAG3C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MAC/B4D,CAAC,EAAEH,QAAQ,CAACG,CAAC,GAAG5C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MAC/B4B,KAAK,EAAE6B,QAAQ,CAAC7B,KAAK,GAAGZ,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MACvC8B,MAAM,EAAE2B,QAAQ,CAAC3B,MAAM,GAAGd,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MACzC8C,IAAI,EAAEW,QAAQ,CAACI,KAAK;MACpBb,OAAO,EAAES,QAAQ,CAACT,OAAO;MAC1BD,MAAM,EAAC,SAAS;MAChB,cAAY,EAAC;;oCAIjB7D,mBAAA,SAAY,EACiBa,KAAA,CAAAa,SAAS,IAAIb,KAAA,CAAA+D,UAAU,IAAI/D,KAAA,CAAAgE,QAAQ,I,cAAhEpC,mBAAA,CAoBI,KApBJqC,WAoBI,GAnBF7E,mBAAA,CAQE;IAPC8E,EAAE,EAAElE,KAAA,CAAA+D,UAAU,CAACH,CAAC,GAAG5D,KAAA,CAAAC,IAAI;IACvBkE,EAAE,EAAEnE,KAAA,CAAA+D,UAAU,CAACF,CAAC,GAAG7D,KAAA,CAAAC,IAAI;IACvBmE,EAAE,EAAEpE,KAAA,CAAAgE,QAAQ,CAACJ,CAAC,GAAG5D,KAAA,CAAAC,IAAI;IACrBoE,EAAE,EAAErE,KAAA,CAAAgE,QAAQ,CAACH,CAAC,GAAG7D,KAAA,CAAAC,IAAI;IACtB+C,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC;wCAEnB5D,mBAAA,CASO;IARJwE,CAAC,GAAG5D,KAAA,CAAA+D,UAAU,CAACH,CAAC,GAAG5D,KAAA,CAAAgE,QAAQ,CAACJ,CAAC,QAAQ5D,KAAA,CAAAC,IAAI;IACzC4D,CAAC,GAAG7D,KAAA,CAAA+D,UAAU,CAACF,CAAC,GAAG7D,KAAA,CAAAgE,QAAQ,CAACH,CAAC,QAAQ7D,KAAA,CAAAC,IAAI;IAC1C,aAAW,EAAC,QAAQ;IACpB8C,IAAI,EAAC,SAAS;IACd,WAAS,EAAC,IAAI;IACd,aAAW,EAAC;sBAETpD,QAAA,CAAA2E,gBAAgB,MAAK,IAC1B,uBAAAC,WAAA,E,wCAGFpF,mBAAA,YAAe,EACmBa,KAAA,CAAAwE,eAAe,I,cAAjD5C,mBAAA,CAgBI,KAhBJ6C,WAgBI,GAfFrF,mBAAA,CAOE;IANCyD,CAAC,EAAElD,QAAA,CAAA+E,eAAe;IACnB3B,IAAI,EAAC,MAAM;IACXC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC,KAAK;IACtBC,OAAO,EAAC;wCAEV7D,mBAAA,CAME;IALCuF,EAAE,EAAE3E,KAAA,CAAAwE,eAAe,CAACI,GAAG,CAAChB,CAAC,GAAG3C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IAC3C4E,EAAE,EAAE7E,KAAA,CAAAwE,eAAe,CAACI,GAAG,CAACf,CAAC,GAAG5C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IAC3C6E,CAAC,MAAM9E,KAAA,CAAAC,IAAI;IACZ8C,IAAI,EAAC,SAAS;IACdE,OAAO,EAAC;+EAIZ9D,mBAAA,YAAe,EACea,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAAgF,eAAe,I,cAAlEpD,mBAAA,CAUI,KAVJqD,WAUI,GATF7F,mBAAA,CAQE;IAPCuF,EAAE,EAAE3E,KAAA,CAAA+E,iBAAiB,CAACG,QAAQ,CAACtB,CAAC,GAAG3C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IAClD4E,EAAE,EAAE7E,KAAA,CAAA+E,iBAAiB,CAACG,QAAQ,CAACrB,CAAC,GAAG5C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IAClD6E,CAAC,EAAEnF,QAAA,CAAAwF,cAAc,CAACnF,KAAA,CAAA+E,iBAAiB,IAAI9D,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;IACvD8C,IAAI,EAAC,0BAA0B;IAC/BC,MAAM,EAAC,SAAS;IAChB,cAAY,EAAC,GAAG;IAChB,kBAAgB,EAAC;+EAIrB7D,mBAAA,SAAY,EACZC,mBAAA,CAgBI,KAhBJgG,WAgBI,I,kBAfFxD,mBAAA,CAcE2B,SAAA,QAAAC,WAAA,CAboBvC,MAAA,CAAAoE,UAAU,YAAvBC,SAAS;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;yBADlBC,YAAA,CAcEC,yBAAA;MAZC/B,GAAG,EAAE2B,SAAS,CAAC5C,EAAE;MACjB4C,SAAS,EAAEA,SAAS;MACpB,WAAS,EAAErE,MAAA,CAAA0B,QAAQ;MACnB1C,IAAI,EAAED,KAAA,CAAAC,IAAI;MACV0F,QAAQ,EAAE,EAAAJ,qBAAA,GAAAvF,KAAA,CAAA+E,iBAAiB,cAAAQ,qBAAA,uBAAjBA,qBAAA,CAAmB7C,EAAE,MAAK4C,SAAS,CAAC5C,EAAE;MAChD,cAAY,EAAE,EAAA8C,sBAAA,GAAAvE,MAAA,CAAAI,oBAAoB,cAAAmE,sBAAA,uBAApBA,sBAAA,CAAsB9C,EAAE,MAAK4C,SAAS,CAAC5C,EAAE;MACvD,UAAQ,EAAE/C,QAAA,CAAAiG,gBAAgB,CAACN,SAAS;MACpC,SAAO,EAAE3F,QAAA,CAAAkG,eAAe,CAACP,SAAS;MAClCQ,QAAM,EAAEnG,QAAA,CAAAoG,eAAe;MACvBC,MAAI,EAAErG,QAAA,CAAAsG,aAAa;MACnBC,QAAM,EAAEvG,QAAA,CAAAwG,eAAe;MACvBC,aAAY,EAAEzG,QAAA,CAAA0G;;oCAInBlH,mBAAA,SAAY,EACZC,mBAAA,CAcI,KAdJkH,WAcI,I,kBAbF1E,mBAAA,CAYE2B,SAAA,QAAAC,WAAA,CAXkBvC,MAAA,CAAAsF,QAAQ,YAAnBC,OAAO;IAAA,IAAAC,qBAAA;yBADhBhB,YAAA,CAYEiB,uBAAA;MAVC/C,GAAG,EAAE6C,OAAO,CAAC9D,EAAE;MACf8D,OAAO,EAAEA,OAAO;MAChB,WAAS,EAAEvF,MAAA,CAAA0B,QAAQ;MACnB1C,IAAI,EAAED,KAAA,CAAAC,IAAI;MACV0F,QAAQ,EAAE,EAAAc,qBAAA,GAAAzG,KAAA,CAAA2G,eAAe,cAAAF,qBAAA,uBAAfA,qBAAA,CAAiB/D,EAAE,MAAK8D,OAAO,CAAC9D,EAAE;MAC5C,aAAW,EAAEzB,MAAA,CAAA2F,QAAQ;MACrBd,QAAM,EAAEnG,QAAA,CAAAkH,aAAa;MACrBb,MAAI,EAAErG,QAAA,CAAAmH,WAAW;MACjBZ,QAAM,EAAEvG,QAAA,CAAAoH,aAAa;MACrBX,aAAY,EAAEzG,QAAA,CAAAqH;;oCAInB7H,mBAAA,SAAY,EACZC,mBAAA,CAaI,KAbJ6H,WAaI,GAZF9H,mBAAA,YAAe,G,kBACfyC,mBAAA,CAUE2B,SAAA,QAAAC,WAAA,CATiBvC,MAAA,CAAAiG,WAAW,YAArBC,MAAM;yBADfvF,mBAAA,CAUE;MARC+B,GAAG,EAAEwD,MAAM,CAACzE,EAAE;MACdiC,EAAE,EAAEwC,MAAM,CAACjC,QAAQ,CAACtB,CAAC,GAAG3C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MACvC4E,EAAE,EAAEsC,MAAM,CAACjC,QAAQ,CAACrB,CAAC,GAAG5C,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MACvC6E,CAAC,EAAEqC,MAAM,CAACC,MAAM,GAAGnG,MAAA,CAAA0B,QAAQ,GAAG3C,KAAA,CAAAC,IAAI;MAClC8C,IAAI,EAAEoE,MAAM,CAACrD,KAAK;MAClBb,OAAO,EAAEkE,MAAM,CAAClE,OAAO;MACvBD,MAAM,EAAEmE,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACrD,KAAK;MAC3C,cAAY,EAAC;;oCAIjB3E,mBAAA,SAAY,EACZC,mBAAA,CASI,KATJkI,WASI,I,kBARF1F,mBAAA,CAOE2B,SAAA,QAAAC,WAAA,CANoBxD,KAAA,CAAAuH,gBAAgB,YAA7BC,SAAS;yBADlB/B,YAAA,CAOEgC,0BAAA;MALC9D,GAAG,EAAE6D,SAAS,CAAC9E,EAAE;MACjB8E,SAAS,EAAEA,SAAS;MACpB,WAAS,EAAEvG,MAAA,CAAA0B,QAAQ;MACnB1C,IAAI,EAAED,KAAA,CAAAC,IAAI;MACVyH,UAAQ,EAAE/H,QAAA,CAAAgI;;oFAKjBxI,mBAAA,YAAe,EAEPa,KAAA,CAAA4H,WAAW,CAACC,IAAI,IAAI7H,KAAA,CAAA4H,WAAW,CAACE,IAAI,oB,cAD5ClG,mBAAA,CAuCM;;IArCJ,SAAM,cAAc;IACnBmG,KAAK,EAAAC,eAAA;MAAAC,IAAA,EAAUjI,KAAA,CAAA4H,WAAW,CAAChE,CAAC;MAAAsE,GAAA,EAAclI,KAAA,CAAA4H,WAAW,CAAC/D,CAAC;IAAA;MAExDzE,mBAAA,CAGM,OAHN+I,WAGM,GAFJ/I,mBAAA,CAAqE;IAA/DgJ,GAAG,EAAEpI,KAAA,CAAA4H,WAAW,CAACS,MAAM,CAACC,MAAM;IAAGC,GAAG,EAAEvI,KAAA,CAAA4H,WAAW,CAACS,MAAM,CAAC/G;wCAC/DlC,mBAAA,CAA0C,cAAAe,gBAAA,CAAjCH,KAAA,CAAA4H,WAAW,CAACS,MAAM,CAAC/G,IAAI,iB,GAElClC,mBAAA,CA6BM,OA7BNoJ,WA6BM,GA5BJpJ,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAA+I,gBAAgB,CAAC1I,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IAAG,SAAM;kCAC1DjJ,mBAAA,CAA6B;IAA1B,SAAM;EAAe,2B,iBAAK,QAE/B,E,IAEQO,QAAA,CAAAiG,gBAAgB,CAAC5F,KAAA,CAAA4H,WAAW,CAACS,MAAM,K,cAD3CzG,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAAgJ,aAAa,CAAC3I,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IACxC,SAAM;kCAENjJ,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2B,iBAAK,MAEhC,E,yCAEQO,QAAA,CAAAkG,eAAe,CAAC7F,KAAA,CAAA4H,WAAW,CAACS,MAAM,K,cAD1CzG,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAAiJ,cAAc,CAAC5I,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IACzC,SAAM;kCAENjJ,mBAAA,CAAkC;IAA/B,SAAM;EAAoB,2B,iBAAK,MAEpC,E,yCAEQ6B,MAAA,CAAA2F,QAAQ,I,cADhBhF,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAAkJ,aAAa,CAAC7I,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IACxC,SAAM;kCAENjJ,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2B,iBAAK,MAE7B,E,iGAIJD,mBAAA,YAAe,EAEPa,KAAA,CAAA4H,WAAW,CAACC,IAAI,IAAI7H,KAAA,CAAA4H,WAAW,CAACE,IAAI,kB,cAD5ClG,mBAAA,CAyCM;;IAvCJ,SAAM,cAAc;IACnBmG,KAAK,EAAAC,eAAA;MAAAC,IAAA,EAAUjI,KAAA,CAAA4H,WAAW,CAAChE,CAAC;MAAAsE,GAAA,EAAclI,KAAA,CAAA4H,WAAW,CAAC/D,CAAC;IAAA;MAExDzE,mBAAA,CAKM,OALN0J,WAKM,GAJJ1J,mBAAA,CAEM,OAFN2J,WAEM,GADJ3J,mBAAA,CAAmD;IAA/C,SAAKqB,eAAA,CAAEd,QAAA,CAAAqJ,cAAc,CAAChJ,KAAA,CAAA4H,WAAW,CAACS,MAAM;6BAE9CjJ,mBAAA,CAA0C,cAAAe,gBAAA,CAAjCH,KAAA,CAAA4H,WAAW,CAACS,MAAM,CAAC/G,IAAI,iB,GAElClC,mBAAA,CA6BM,OA7BN6J,WA6BM,GA5BJ7J,mBAAA,CAGS;IAHAK,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAAuJ,cAAc,CAAClJ,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IAAG,SAAM;kCACxDjJ,mBAAA,CAA6B;IAA1B,SAAM;EAAe,2B,iBAAK,QAE/B,E,IAEQ6B,MAAA,CAAA2F,QAAQ,I,cADhBhF,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAAwJ,cAAc,CAACnJ,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IACzC,SAAM;kCAENjJ,mBAAA,CAA8B;IAA3B,SAAM;EAAgB,2B,iBAAK,MAEhC,E,yCAEQ6B,MAAA,CAAA2F,QAAQ,I,cADhBhF,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAAyJ,WAAW,CAACpJ,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IACtC,SAAM;kCAENjJ,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2B,iBAAK,MAE7B,E,yCAEQ6B,MAAA,CAAA2F,QAAQ,I,cADhBhF,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA,iBAAA+I,MAAA;MAAA,OAAE9I,QAAA,CAAA0J,aAAa,CAACrJ,KAAA,CAAA4H,WAAW,CAACS,MAAM;IAAA;IACxC,SAAM;kCAENjJ,mBAAA,CAA4B;IAAzB,SAAM;EAAc,2B,iBAAK,MAE9B,E,yHAKND,mBAAA,WAAc,EACdC,mBAAA,CA+DM,OA/DNkK,WA+DM,GA9DJlK,mBAAA,CA0BM,OA1BNmK,WA0BM,GAzB6BvJ,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAA2G,eAAe,I,cAArE/E,mBAAA,CAwBM,OAxBN4H,WAwBM,GAvBJpK,mBAAA,CASM,OATNqK,WASM,GAPIzJ,KAAA,CAAA+E,iBAAiB,I,cADzBnD,mBAAA,CAIC;;IAFEwG,GAAG,EAAEpI,KAAA,CAAA+E,iBAAiB,CAACuD,MAAM;IAC7BC,GAAG,EAAEvI,KAAA,CAAA+E,iBAAiB,CAACzD;0CAEVtB,KAAA,CAAA2G,eAAe,I,cAA/B/E,mBAAA,CAEM,OAFN8H,WAEM,GADJtK,mBAAA,CAAgD;IAA5C,SAAKqB,eAAA,CAAEd,QAAA,CAAAqJ,cAAc,CAAChJ,KAAA,CAAA2G,eAAe;oEAG7CvH,mBAAA,CAYM,OAZNuK,WAYM,GAXJvK,mBAAA,CAEM,OAFNwK,WAEM,EAAAzJ,gBAAA,EAAA0J,IAAA,GADA7J,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAA2G,eAAe,cAAAkD,IAAA,uBACpCA,IAAA,CADuCvI,IAAI,kBAEjDlC,mBAAA,CAOM,OAPN0K,WAOM,GANJ1K,mBAAA,CAEO,QAFP2K,WAEO,EAFU,OACX,GAAA5J,gBAAA,EAAA6J,KAAA,GAAIhK,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAA2G,eAAe,cAAAqD,KAAA,uBAAxCA,KAAA,CAA2CC,SAAS,IAAG,GAAC,GAAA9J,gBAAA,EAAA+J,KAAA,GAAIlK,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAA2G,eAAe,cAAAuD,KAAA,uBAAxCA,KAAA,CAA2CC,KAAK,kBAE9G/K,mBAAA,CAEO,QAFPgL,WAEO,EAFgB,QAChB,GAAAjK,gBAAA,EAAAkK,KAAA,GAAIrK,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAA2G,eAAe,cAAA0D,KAAA,uBAAxCA,KAAA,CAA2CnF,QAAQ,CAACtB,CAAC,IAAG,IAAE,GAAAzD,gBAAA,EAAAmK,KAAA,GAAItK,KAAA,CAAA+E,iBAAiB,IAAI/E,KAAA,CAAA2G,eAAe,cAAA2D,KAAA,uBAAxCA,KAAA,CAA2CpF,QAAQ,CAACrB,CAAC,IAAG,IACzH,gB,8CAKRzE,mBAAA,CAeM,OAfNmL,WAeM,GAdJnL,mBAAA,CAaM,OAbNoL,WAaM,GAZJpL,mBAAA,CAGO,QAHPqL,WAGO,G,4BAFLrL,mBAAA,CAA4B;IAAzB,SAAM;EAAc,4B,iBAAK,OACxB,GAAAe,gBAAA,CAAGc,MAAA,CAAAoE,UAAU,CAACqF,MAAM,iB,GAE1BtL,mBAAA,CAGO,QAHPuL,WAGO,G,4BAFLvL,mBAAA,CAA6B;IAA1B,SAAM;EAAe,4B,iBAAK,OACzB,GAAAe,gBAAA,CAAGc,MAAA,CAAAsF,QAAQ,CAACmE,MAAM,iB,GAExBtL,mBAAA,CAGO,QAHPwL,WAGO,G,4BAFLxL,mBAAA,CAA0B;IAAvB,SAAM;EAAY,4B,iBAAK,GAC1B,GAAAe,gBAAA,CAAGc,MAAA,CAAA4J,gBAAgB,IAAG,GAAC,GAAA1K,gBAAA,CAAGc,MAAA,CAAA6J,iBAAiB,iB,OAIjD1L,mBAAA,CAkBM,OAlBN2L,WAkBM,GAjBJ3L,mBAAA,CAgBM,OAhBN4L,WAgBM,GAdI/J,MAAA,CAAA2F,QAAQ,I,cADhBhF,mBAAA,CAOS;;IALNnC,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAsL,UAAA,IAAAtL,QAAA,CAAAsL,UAAA,CAAApL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAU;IAAA;IAClB,SAAM;kCAENV,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2B,iBAAK,QAE7B,E,yCACAA,mBAAA,CAMS;IALNK,OAAK,EAAAC,MAAA,SAAAA,MAAA;MAAA,OAAEC,QAAA,CAAAuL,SAAA,IAAAvL,QAAA,CAAAuL,SAAA,CAAArL,KAAA,CAAAF,QAAA,EAAAG,SAAA,CAAS;IAAA;IACjB,SAAM;kCAENV,mBAAA,CAA2B;IAAxB,SAAM;EAAa,2B,iBAAK,QAE7B,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}