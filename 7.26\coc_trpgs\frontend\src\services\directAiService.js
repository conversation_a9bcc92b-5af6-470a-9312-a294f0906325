// DirectAiService - 直接与DeepSeek API通信，不依赖WebSocket
import store from '../store';

class DirectAiService {
  constructor() {
    this.messageHistory = [];
    this.apiKey = this.safeGetItem('deepseek_api_key') || '';
    this.aiSettings = {
      temperature: 0.7,
      top_p: 0.9,
      max_tokens: 2000,
      responseMode: this.safeGetItem('response_mode') || 'balanced' // 添加响应模式设置
    };
    this.systemPrompt = `你是一个克苏鲁神话(COC)第七版跑团游戏中的专业守秘人(KP)，能够独立主持整个游戏过程。你精通COC 7版的所有规则，包括技能检定、战斗系统、理智系统等。

作为KP，你应该：
1. 提供生动而恐怖的场景描述，营造克苏鲁神话特有的诡异氛围
2. 根据玩家行动引导故事发展，把控游戏节奏和难度
3. 管理NPC互动，为每个NPC设定独特的语气和性格
4. 在合适时机进行各类检定，并解释检定结果如何影响故事
5. 记录玩家行动对剧情的影响，确保故事连贯性
6. 适时进行理智检定，并描述恐惧对调查员的影响
7. 在保证游戏流畅的前提下严格遵循COC规则

回应时，请优先考虑游戏的可玩性和乐趣，而非完全按照预设剧情发展。`;
    
    // 添加记忆管理系统
    this.memories = {
      characters: [], // 角色记忆
      locations: [], // 地点记忆
      events: [], // 事件记忆
      clues: [], // 线索记忆
      lastUpdated: null // 最后更新时间
    };
    
    // 从本地存储中加载设置和记忆
    this.loadSettings();
    this.loadMemories();
  }

  // 安全存储访问方法
  safeGetItem(key) {
    try {
      if (window.storageManager) {
        return window.storageManager.getItem(key);
      }
      return localStorage.getItem(key);
    } catch (error) {
      console.warn(`[DirectAiService] 无法读取存储项 ${key}:`, error.message);
      return null;
    }
  }

  safeSetItem(key, value) {
    try {
      if (window.storageManager) {
        window.storageManager.setItem(key, value);
      } else {
        localStorage.setItem(key, value);
      }
    } catch (error) {
      console.warn(`[DirectAiService] 无法保存存储项 ${key}:`, error.message);
    }
  }

  safeGetJSON(key) {
    try {
      const value = this.safeGetItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn(`[DirectAiService] 无法解析JSON存储项 ${key}:`, error.message);
      return null;
    }
  }

  safeSetJSON(key, value) {
    try {
      this.safeSetItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn(`[DirectAiService] 无法序列化JSON存储项 ${key}:`, error.message);
    }
  }

  // 保存API密钥
  setApiKey(apiKey) {
    this.apiKey = apiKey;
    this.safeSetItem('deepseek_api_key', apiKey);
  }

  // 获取API密钥
  getApiKey() {
    return this.apiKey;
  }

  // 更新AI设置
  updateSettings(settings) {
    if (settings.temperature !== undefined) this.aiSettings.temperature = settings.temperature;
    if (settings.top_p !== undefined) this.aiSettings.top_p = settings.top_p;
    if (settings.max_tokens !== undefined) this.aiSettings.max_tokens = settings.max_tokens;
    if (settings.system_prompt !== undefined) this.systemPrompt = settings.system_prompt;
    if (settings.responseMode !== undefined) {
      this.aiSettings.responseMode = settings.responseMode;
      this.safeSetItem('response_mode', settings.responseMode);
    }
    
    // 更新历史中的系统提示
    this.updateSystemPromptInHistory();
    
    // 保存设置
    this.saveSettings();
  }

  // 将设置保存到本地存储
  saveSettings() {
    const settings = {
      temperature: this.aiSettings.temperature,
      top_p: this.aiSettings.top_p,
      max_tokens: this.aiSettings.max_tokens,
      system_prompt: this.systemPrompt,
      responseMode: this.aiSettings.responseMode
    };
    
    this.safeSetJSON('direct_ai_settings', settings);
  }

  // 从本地存储中加载设置
  loadSettings() {
    const settings = this.safeGetJSON('direct_ai_settings');
    if (settings) {
      try {
        if (settings.temperature !== undefined) this.aiSettings.temperature = settings.temperature;
        if (settings.top_p !== undefined) this.aiSettings.top_p = settings.top_p;
        if (settings.max_tokens !== undefined) this.aiSettings.max_tokens = settings.max_tokens;
        if (settings.system_prompt !== undefined) this.systemPrompt = settings.system_prompt;
        if (settings.responseMode !== undefined) this.aiSettings.responseMode = settings.responseMode;
      } catch (e) {
        console.error('加载AI设置失败:', e);
      }
    }
    
    // 初始化消息历史
    this.clearHistory();
  }

  // 更新历史中的系统提示
  updateSystemPromptInHistory() {
    if (this.messageHistory.length > 0 && this.messageHistory[0].role === 'system') {
      this.messageHistory[0].content = this.systemPrompt;
    } else {
      // 如果没有系统提示，添加一个
      this.messageHistory.unshift({ role: 'system', content: this.systemPrompt });
    }
  }

  // 清空聊天历史
  clearHistory() {
    this.messageHistory = [{ role: 'system', content: this.systemPrompt }];
  }

  // 向历史中添加用户消息
  addUserMessage(content) {
    this.messageHistory.push({ role: 'user', content });
  }

  // 向历史中添加AI回复
  addAssistantMessage(content) {
    this.messageHistory.push({ role: 'assistant', content });
  }

  // 获取优化后的消息历史用于API请求
  getOptimizedMessageHistory() {
    // 如果历史消息不多，直接返回完整历史
    if (this.messageHistory.length <= 12) {
      return this.messageHistory;
    }
    
    // 保留系统提示
    const systemPrompt = this.messageHistory[0];
    
    // 提取剧本内容消息（通常是较长的消息，包含关键剧情信息）
    const scriptMessages = this.messageHistory.filter(msg => {
      // 检查是否是用户消息且包含剧本标记
      return msg.role === 'user' && 
        (msg.content.includes('剧本内容如下') || 
         msg.content.includes('剧本文件') || 
         msg.content.includes('PDF文件') || 
         msg.content.includes('Word文件') || 
         msg.content.includes('JSON剧本') || 
         msg.content.includes('文本文件'));
    });
    
    // 提取最近的对话消息（最近的10条或更多）
    const recentCount = Math.max(10, Math.min(15, this.messageHistory.length / 2));
    const recentMessages = this.messageHistory.slice(-recentCount);
    
    // 构建一个摘要消息，总结较早的对话内容
    let summaryContent = '';
    if (this.messageHistory.length > recentCount + scriptMessages.length + 1) {
      const earlierMessages = this.messageHistory.slice(1, -recentCount);
      // 过滤掉已经包含在scriptMessages中的消息
      const uniqueEarlierMessages = earlierMessages.filter(msg => 
        !scriptMessages.some(script => script.content === msg.content)
      );
      
      if (uniqueEarlierMessages.length > 0) {
        summaryContent = '以下是之前对话的摘要：\n';
        uniqueEarlierMessages.forEach(msg => {
          const roleLabel = msg.role === 'user' ? '玩家' : 'AI';
          // 只添加较短的消息摘要
          if (msg.content.length > 300) {
            summaryContent += `${roleLabel}: ${msg.content.substring(0, 300)}...\n`;
          } else {
            summaryContent += `${roleLabel}: ${msg.content}\n`;
          }
        });
      }
    }
    
    // 获取记忆摘要
    const memorySummary = this.getMemorySummary();
    
    // 构建最终的消息历史
    const optimizedHistory = [systemPrompt];
    
    // 添加记忆摘要（如果有）
    if (memorySummary) {
      optimizedHistory.push({
        role: 'system',
        content: `请记住以下游戏中的关键信息，并在回复中保持一致性：\n\n${memorySummary}`
      });
    }
    
    // 添加剧本内容（如果有）
    if (scriptMessages.length > 0) {
      // 只添加最新的剧本内容，避免重复
      optimizedHistory.push(scriptMessages[scriptMessages.length - 1]);
    }
    
    // 添加对话摘要（如果有）
    if (summaryContent) {
      optimizedHistory.push({
        role: 'system',
        content: summaryContent
      });
    }
    
    // 添加最近的消息
    recentMessages.forEach(msg => {
      // 避免重复添加已经包含的剧本消息
      if (!scriptMessages.some(script => script.content === msg.content)) {
        optimizedHistory.push(msg);
      }
    });
    
    return optimizedHistory;
  }

  // 发送消息到API并获取响应
  async sendMessage(content, role = 'assistant') {
    if (!this.apiKey) {
      throw new Error('未设置API密钥');
    }
    
    // 根据角色更新系统提示词
    this.updateRolePrompt(role);
    
    // 添加用户消息到历史
    this.addUserMessage(content);
    
    // 根据响应模式调整参数
    let maxTokens = this.aiSettings.max_tokens;
    let temperature = role === 'kp' ? 0.8 : this.aiSettings.temperature;
    
    switch (this.aiSettings.responseMode) {
      case 'fast':
        // 快速模式：减少token数量，略微提高temperature以加快生成
        maxTokens = Math.min(1000, maxTokens);
        temperature = Math.min(0.9, temperature + 0.1);
        break;
      case 'quality':
        // 质量模式：增加token数量，降低temperature以提高质量
        maxTokens = Math.max(2500, maxTokens);
        temperature = Math.max(0.5, temperature - 0.1);
        break;
      // 平衡模式使用默认设置
    }
    
    // 准备请求，使用优化后的消息历史
    const payload = {
      model: "deepseek-chat",
      messages: this.getOptimizedMessageHistory(),
      temperature: temperature,
      top_p: this.aiSettings.top_p,
      max_tokens: maxTokens,
      stream: true // 启用流式响应
    };
    
    // 发送请求到DeepSeek API
    try {
      // 创建一个用于存储完整响应的变量
      let fullResponse = '';
      let responseStarted = false;
      
      // 创建一个自定义事件，用于流式更新消息
      const messageUpdateEvent = new CustomEvent('ai-message-update', {
        detail: {
          content: '',
          role: role
        }
      });
      
      // 发送API请求
      const response = await fetch(this.aiSettings.apiUrl || 'https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`API请求失败: ${response.status} ${errorData.error?.message || errorData.error || '未知错误'}`);
      }
      
      // 处理流式响应
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let streamActive = true;
      
      while (streamActive) {
        const { value, done } = await reader.read();
        
        if (done) {
          streamActive = false;
          continue;
        }
        
        const chunk = decoder.decode(value);
        
        // 处理SSE格式的响应
        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.substring(6);
            if (data === '[DONE]') {
              streamActive = false;
              continue;
            }
            
            try {
              const parsedData = JSON.parse(data);
              if (parsedData.choices && parsedData.choices.length > 0) {
                const content = parsedData.choices[0].delta?.content || '';
                if (content) {
                  fullResponse += content;
                  
                  // 更新事件详情并触发事件
                  messageUpdateEvent.detail.content = fullResponse;
                  window.dispatchEvent(messageUpdateEvent);
                  
                  // 标记响应已开始
                  responseStarted = true;
                }
              }
            } catch (e) {
              console.error('解析流式响应数据出错:', e);
            }
          }
        }
      }
      
      // 如果没有收到任何响应内容
      if (!responseStarted) {
        throw new Error('无法获取AI回复');
      }
      
      // 处理骰子检定指令
      if (fullResponse.includes('[骰子检定:')) {
        fullResponse = this.processDiceChecks(fullResponse);
      }
      
      // 添加AI回复到历史
      this.addAssistantMessage(fullResponse);
      
      // 更新记忆系统
      this.updateMemories(fullResponse, 'assistant');
      
      return fullResponse;
    } catch (error) {
      console.error('调用DeepSeek API出错:', error);
      throw error;
    }
  }

  // 处理骰子检定指令
  processDiceChecks(content) {
    if (!content || !content.includes('[骰子检定:')) {
      return content;
    }
    
    // 提取检定技能
    const regex = /\[骰子检定:(.*?)\]/g;
    let matches;
    let processedContent = content;
    
    while ((matches = regex.exec(content)) !== null) {
      const skillName = matches[1].trim();
      console.log(`检测到骰子检定指令，技能: ${skillName}`);
      
      // 获取角色技能值
      const skillValue = this.getCharacterSkillValue(skillName);
      
      // 模拟骰子检定结果
      const roll = Math.floor(Math.random() * 100) + 1;
      
      // 判断成功等级
      let result;
      if (roll <= 5) {
        result = "大成功";
      } else if (roll <= skillValue / 5) {
        result = "极难成功";
      } else if (roll <= skillValue / 2) {
        result = "困难成功";
      } else if (roll <= skillValue) {
        result = "成功";
      } else if (roll >= 96) {
        result = "大失败";
      } else {
        result = "失败";
      }
      
      // 替换骰子指令为检定结果
      const checkResult = `[${skillName}检定: ${roll}/${skillValue} - ${result}]`;
      processedContent = processedContent.replace(matches[0], checkResult);
    }
    
    return processedContent;
  }
  
  // 获取角色技能值
  getCharacterSkillValue(skillName) {
    // TODO: 从角色表中获取实际技能值
    // 这里使用默认值模拟
    const defaultSkills = {
      '侦查': 70,
      '图书馆使用': 60,
      '聆听': 50,
      '说服': 50,
      '闪避': 40,
      '格斗': 45,
      '射击': 40,
      '急救': 55,
      '心理学': 50,
      '潜行': 45,
      '领航': 40,
      '驾驶': 50,
      '考古学': 40,
      '历史': 50,
      '科学': 40,
      '机械维修': 35
    };
    
    return defaultSkills[skillName] || 50;
  }

  // 更新角色系统提示词
  updateRolePrompt(role) {
    const rolePrompts = {
      assistant: `你是一个克苏鲁神话(COC)第七版跑团游戏中的规则助手。你的职责是：

1. 规则解释：
   - 准确解释COC 7版规则，包括属性、技能、战斗、追逐、理智检定等机制
   - 解答玩家和守秘人关于规则的疑问，提供清晰的规则引用和页码
   - 说明各种检定的成功等级：常规成功(≤技能值)、困难成功(≤技能值/2)、极难成功(≤技能值/5)、大成功(01-05)、大失败(96-00)

2. 角色创建与发展：
   - 指导玩家创建调查员角色，包括属性分配、技能选择、背景故事等
   - 解释职业包和兴趣点分配方法
   - 提供角色成长和技能提升的建议

3. 战斗与伤害系统：
   - 解释战斗轮流程、先攻顺序、战斗动作等
   - 详述武器伤害、命中率、弹药等数据
   - 说明伤害类型、治疗方法和恢复时间

4. 理智系统：
   - 解释理智值损失机制、临时疯狂和不定性疯狂
   - 提供疯狂症状表和恢复方法
   - 说明神话知识与理智的关系

5. 游戏建议：
   - 为玩家提供角色扮演和调查策略的建议
   - 为守秘人提供场景设计和游戏节奏控制的建议
   - 分享提升游戏体验的小技巧

请以客观、专业的方式回答问题，避免直接参与角色扮演或故事叙述，这些是守秘人的职责。你的目标是帮助所有参与者更好地理解和享受COC跑团游戏。`,
      kp: `你是一个克苏鲁神话(COC)第七版跑团的守秘人(KP)。作为守秘人，你需要：

1. 规则掌握：
   - 熟悉COC 7版规则系统，包括技能检定、战斗、追逐、理智检定等机制
   - 使用百分比骰(D100)系统，成功等级分为：常规成功(≤技能值)、困难成功(≤技能值/2)、极难成功(≤技能值/5)、大成功(01-05)、大失败(96-00)
   - 理智检定失败时，调查员会失去相应理智值，当理智降至0时，角色将永久疯狂
   - 主动进行骰子检定，使用格式"[骰子检定:技能名称]"(如：[骰子检定:侦查])让系统自动完成掷骰

2. 游戏引导：
   - 描述场景、扮演NPC、解释调查员的发现，创造紧张、恐怖的氛围
   - 保持神秘感，逐步揭示真相，但不要过早透露全部信息
   - 根据调查员的行动调整情节走向，保持游戏的流动性
   - 完全自主主持游戏，不依赖人类KP的指导
   - 保持故事的连贯性，记住之前发生的事件和玩家的决策
   - 时刻关注玩家的状态，包括生命值、理智值和技能值的变化

3. 调查员互动：
   - 立即回应调查员的行动，告知他们看到、听到或感受到什么
   - 当调查员使用特定技能时，提供相应的信息和结果
   - 为调查员提供足够的线索，但让他们自己拼凑真相
   - 使用不同的声音和语气来区分各个NPC
   - 当玩家陷入困境时提供微妙的提示，而不是直接告诉他们该做什么

4. 克苏鲁元素：
   - 融入克苏鲁神话元素，包括远古神祇、神话生物、禁忌知识等
   - 描述不可名状的恐怖，强调人类理智在宇宙真相面前的脆弱
   - 使用暗示和间接描述，而非直接展示怪物
   - 随着调查的深入，逐渐增加神话元素的出现频率和强度

5. 氛围营造：
   - 使用细致的环境描写，创造恐怖、压抑、神秘的氛围
   - 通过感官描述(视觉、听觉、嗅觉、触觉)增强沉浸感
   - 使用适当的停顿和节奏控制，在关键时刻制造紧张感
   - 注意光线、天气和时间的变化，利用这些元素增强恐怖氛围

6. 游戏流程管理：
   - 掌控游戏节奏，避免游戏过于缓慢或过快
   - 当玩家分散或偏离主线时，引导他们回到主要情节
   - 根据玩家的反应调整难度和挑战
   - 在合适的时机安排休息点和总结时刻
   - 处理玩家之间的分歧，确保每个人都有参与的机会

7. 规则应用：
   - 自动判断何时需要进行技能检定，并明确告知玩家
   - 追踪战斗中的先攻顺序、生命值和状态效果
   - 根据玩家描述的行动自动判断应使用哪种技能
   - 在生死攸关的场景中公平应用规则，既不过于宽松也不过于严格
   - 当出现规则争议时，做出合理裁决并保持游戏流畅

请记住，作为守秘人，你的目标不是打败玩家，而是创造一个引人入胜、令人难忘的故事体验。无论玩家做什么，你都应该即时回应，推动故事发展，并让所有人沉浸在克苏鲁神话的恐怖世界中。`,
      storyteller: `你是一位擅长创造克苏鲁风格故事的讲述者，专注于创作符合COC第七版风格的恐怖、神秘故事。作为故事讲述者，你需要：

1. 叙事风格：
   - 采用洛夫克拉夫特式的宇宙恐怖风格，强调人类在浩瀚宇宙中的渺小
   - 使用第三人称有限视角，通过人物的感知和经历传递恐惧
   - 逐步构建紧张感，从微妙的不安到直面宇宙恐怖的崩溃
   - 善用伏笔和暗示，让读者自行在想象中构建恐惧

2. 场景描写：
   - 创造细致、压抑的环境描写，注重光影、声音、气味等感官细节
   - 描绘衰败、古老或异常的场所，如废弃建筑、古老图书馆、偏远村庄
   - 利用天气、时间和环境变化增强氛围(如雾气、暴雨、黄昏)
   - 将日常场景与超自然元素对比，强化违和感和恐惧

3. 角色塑造：
   - 创造有深度的人物，包括他们的背景、动机和弱点
   - 描绘角色面对未知时的心理变化和理智崩溃过程
   - 设计多样化的NPC，包括知情者、受害者、神秘学者和敌对势力
   - 展现人物在面对超自然时的不同反应(否认、好奇、恐惧、崇拜)

4. 克苏鲁元素：
   - 巧妙融入神话元素，如古老神祇、异界生物、禁忌知识
   - 使用暗示和间接描述，避免过于直接地展示怪物或神祇
   - 通过古籍、梦境、艺术品等媒介传递神话知识
   - 强调接触神话知识对人类心智的侵蚀效果

5. 故事结构：
   - 构建紧凑的情节，从看似普通的调查开始，逐步揭示超自然真相
   - 设置多重线索和谜题，引导读者/玩家探索
   - 创造有意义的转折点和启示时刻
   - 提供开放式或模糊的结局，留下思考空间

请记住，一个好的克苏鲁故事不仅仅是关于怪物和死亡，而是关于面对无法理解的宇宙真相时人类心智的脆弱性。`
    };
    
    // 获取角色对应的提示词
    const prompt = rolePrompts[role] || this.systemPrompt;
    
    // 更新系统提示词
    if (this.messageHistory[0].role === 'system') {
      this.messageHistory[0].content = prompt;
    } else {
      this.messageHistory.unshift({ role: 'system', content: prompt });
    }
  }

  // 转换为房间消息格式
  formatAiMessage(content, role = 'assistant') {
    // 根据角色设置不同的用户名
    const roleNames = {
      assistant: 'AI助手',
      kp: 'COC守秘人',
      storyteller: '故事讲述者'
    };
    
    const username = roleNames[role] || 'AI助手';
    
    return {
      type: 'chat',
      content: content,
      username: username,
      user_id: 0, // 使用特殊ID表示AI
      timestamp: new Date().toISOString(),
      ai_generated: true,
      ai_role: role // 添加角色信息
    };
  }

  // 保存记忆到本地存储
  saveMemories() {
    this.safeSetJSON('ai_memories', this.memories);
  }
  
  // 从本地存储加载记忆
  loadMemories() {
    const savedMemories = this.safeGetJSON('ai_memories');
    if (savedMemories) {
      try {
        this.memories = savedMemories;
      } catch (e) {
        console.error('加载AI记忆失败:', e);
      }
    }
  }
  
  // 更新记忆
  updateMemories(content, role) {
    // 只处理AI回复，因为它们包含了对用户输入的响应
    if (role !== 'assistant') return;
    
    // 更新最后更新时间
    this.memories.lastUpdated = new Date().toISOString();
    
    // 提取角色信息
    this.extractCharacters(content);
    
    // 提取地点信息
    this.extractLocations(content);
    
    // 提取事件信息
    this.extractEvents(content);
    
    // 提取线索信息
    this.extractClues(content);
    
    // 保存更新后的记忆
    this.saveMemories();
  }
  
  // 提取角色信息
  extractCharacters(content) {
    // 简单的启发式方法来识别角色描述
    const characterPatterns = [
      /([^.。!！?？\n]+)是([^.。!！?？\n]*)(调查员|NPC|角色|玩家|人物)([^.。!！?？\n]*)/g,
      /(调查员|NPC|角色|玩家|人物)([^.。!！?？\n]*)([^.。!！?？\n]+)是([^.。!！?？\n]*)/g,
      /名为([^.。!！?？\n]+)的(调查员|NPC|角色|玩家|人物)/g
    ];
    
    characterPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        let characterName = '';
        let characterDesc = '';
        
        // 根据不同模式提取名称和描述
        if (pattern.source.startsWith('([^')) {
          characterName = match[1].trim();
          characterDesc = match[0].trim();
        } else if (pattern.source.startsWith('(调查员|NPC')) {
          characterName = match[3].trim();
          characterDesc = match[0].trim();
        } else {
          characterName = match[1].trim();
          characterDesc = match[0].trim();
        }
        
        // 检查是否已存在该角色
        const existingCharIndex = this.memories.characters.findIndex(
          char => char.name === characterName
        );
        
        if (existingCharIndex >= 0) {
          // 更新现有角色
          this.memories.characters[existingCharIndex].description = characterDesc;
          this.memories.characters[existingCharIndex].lastMentioned = new Date().toISOString();
        } else {
          // 添加新角色
          this.memories.characters.push({
            name: characterName,
            description: characterDesc,
            firstMentioned: new Date().toISOString(),
            lastMentioned: new Date().toISOString()
          });
        }
      }
    });
  }
  
  // 提取地点信息
  extractLocations(content) {
    // 识别地点描述
    const locationPatterns = [
      /([^.。!！?？\n]+)(位于|在|是)([^.。!！?？\n]*)(地点|地方|场所|建筑|房间)([^.。!！?？\n]*)/g,
      /(地点|地方|场所|建筑|房间)([^.。!！?？\n]*)([^.。!！?？\n]+)(位于|在|是)([^.。!！?？\n]*)/g,
      /来到([^.。!！?？\n]+)(这个|这座|这处|这里|这间)?(地点|地方|场所|建筑|房间)/g
    ];
    
    locationPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        let locationName = '';
        let locationDesc = '';
        
        // 根据不同模式提取名称和描述
        if (pattern.source.startsWith('([^')) {
          locationName = match[1].trim();
          locationDesc = match[0].trim();
        } else if (pattern.source.startsWith('(地点|地方')) {
          locationName = match[3].trim();
          locationDesc = match[0].trim();
        } else {
          locationName = match[1].trim();
          locationDesc = match[0].trim();
        }
        
        // 检查是否已存在该地点
        const existingLocIndex = this.memories.locations.findIndex(
          loc => loc.name === locationName
        );
        
        if (existingLocIndex >= 0) {
          // 更新现有地点
          this.memories.locations[existingLocIndex].description = locationDesc;
          this.memories.locations[existingLocIndex].lastMentioned = new Date().toISOString();
        } else {
          // 添加新地点
          this.memories.locations.push({
            name: locationName,
            description: locationDesc,
            firstMentioned: new Date().toISOString(),
            lastMentioned: new Date().toISOString()
          });
        }
      }
    });
  }
  
  // 提取事件信息
  extractEvents(content) {
    // 识别事件描述
    const eventPatterns = [
      /([^.。!！?？\n]*)(发生了|出现了|发现了)([^.。!！?？\n]+)/g,
      /([^.。!！?？\n]+)(事件|事情|情况)([^.。!！?？\n]*)(发生|出现)([^.。!！?？\n]*)/g
    ];
    
    eventPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const eventDesc = match[0].trim();
        
        // 检查是否已存在类似事件
        const existingEventIndex = this.memories.events.findIndex(
          event => event.description.includes(eventDesc) || eventDesc.includes(event.description)
        );
        
        if (existingEventIndex >= 0) {
          // 更新现有事件
          this.memories.events[existingEventIndex].lastMentioned = new Date().toISOString();
        } else {
          // 添加新事件
          this.memories.events.push({
            description: eventDesc,
            firstMentioned: new Date().toISOString(),
            lastMentioned: new Date().toISOString()
          });
        }
      }
    });
  }
  
  // 提取线索信息
  extractClues(content) {
    // 识别线索描述
    const cluePatterns = [
      /([^.。!！?？\n]*)(线索|证据|发现|调查结果)([^.。!！?？\n]+)/g,
      /([^.。!！?？\n]+)(发现|找到|注意到)([^.。!！?？\n]+)(线索|证据)([^.。!！?？\n]*)/g
    ];
    
    cluePatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const clueDesc = match[0].trim();
        
        // 检查是否已存在类似线索
        const existingClueIndex = this.memories.clues.findIndex(
          clue => clue.description.includes(clueDesc) || clueDesc.includes(clue.description)
        );
        
        if (existingClueIndex >= 0) {
          // 更新现有线索
          this.memories.clues[existingClueIndex].lastMentioned = new Date().toISOString();
        } else {
          // 添加新线索
          this.memories.clues.push({
            description: clueDesc,
            firstMentioned: new Date().toISOString(),
            lastMentioned: new Date().toISOString()
          });
        }
      }
    });
  }
  
  // 获取记忆摘要
  getMemorySummary() {
    if (!this.memories.lastUpdated) return '';
    
    let summary = '## 游戏记忆摘要\n\n';
    
    // 添加角色摘要
    if (this.memories.characters.length > 0) {
      summary += '### 角色\n';
      this.memories.characters.forEach(char => {
        summary += `- ${char.name}: ${char.description}\n`;
      });
      summary += '\n';
    }
    
    // 添加地点摘要
    if (this.memories.locations.length > 0) {
      summary += '### 地点\n';
      this.memories.locations.forEach(loc => {
        summary += `- ${loc.name}: ${loc.description}\n`;
      });
      summary += '\n';
    }
    
    // 添加事件摘要
    if (this.memories.events.length > 0) {
      summary += '### 重要事件\n';
      this.memories.events.forEach(event => {
        summary += `- ${event.description}\n`;
      });
      summary += '\n';
    }
    
    // 添加线索摘要
    if (this.memories.clues.length > 0) {
      summary += '### 线索\n';
      this.memories.clues.forEach(clue => {
        summary += `- ${clue.description}\n`;
      });
      summary += '\n';
    }
    
    return summary;
  }

  // 清除记忆
  clearMemories() {
    this.memories = {
      characters: [],
      locations: [],
      events: [],
      clues: [],
      lastUpdated: null
    };
    this.saveMemories();
    console.log('AI记忆已清除');
  }
}

const directAiService = new DirectAiService();
export default directAiService; 